package com.upex.reconciliation.service.cex;

import com.alibaba.fastjson.JSONObject;
import com.binance.connector.client.common.ApiException;
import com.upex.common.util.SerialNoGenerator;
import com.upex.commons.support.hmac.HmacEncryptUtil;
import com.upex.reconciliation.job.ReconciliationJobApplication;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.business.cex.*;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.dao.cex.entity.*;
import com.upex.reconciliation.service.model.config.ApolloThirdCexAssetConfig;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.service.client.cex.client.binance.BinanceApiClient;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.*;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.*;
import com.upex.reconciliation.service.service.client.cex.enmus.CexAssetHistoryTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.CexTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ThirdAssetType;
import com.upex.reconciliation.job.task.ThirdCexAssetXxlJobExecutor;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@RunWith(SpringRunner.class)
//@ActiveProfiles("local")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@SpringBootTest(classes = ReconciliationJobApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CexApiServiceTest {

    @Resource
    CexApiService cexApiService;

    @Resource
    CexUserConfigBizService cexUserConfigBizService;

    @Resource
    BinanceApiClient binanceApiClient;

    @Resource
    RedisTemplate redisTemplate;
    @Test
    public void testQueryApiKey() throws Exception {
//        redisTemplate.opsForValue().set("count",3);
//        System.out.println(redisTemplate.opsForValue().get("count"));
        CommonReq commonReq=new CommonReq();
        commonReq.setCexType(CexTypeEnum.BINANCE.getType());
        commonReq.setCexUserId("10003");
        CommonRes commonRes=binanceApiClient.queryApikeyPermission(commonReq);
        System.out.println(JSONObject.toJSONString(commonRes));
//
//        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2); // 创建一个包含两个线程的调度池
//        ScheduledFuture<?> apiKeyPermissionFuture = null;
//        ScheduledFuture<?> subUserListFuture = null;
//        // 定义第一个定时任务：queryApikeyPermission
//        ScheduledFuture<?> finalApiKeyPermissionFuture = apiKeyPermissionFuture;
//        Runnable apikeyPermissionTask = () -> {
//            try {
//                CommonRes<?> result = binanceApiClient.queryApikeyPermission(commonReq);
//                System.out.println("queryApikeyPermissionSUC: ");
//                if(!result.getSuccess()){
//                    System.out.println("queryApikeyPermissionFail result: " + JSONObject.toJSONString( result));
//                    if (finalApiKeyPermissionFuture != null) {
//                        finalApiKeyPermissionFuture.cancel(false); // 停止该任务
//                    }
//                    return;
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        };
//
//        // 定义第二个定时任务：querySubUserList
//        ScheduledFuture<?> finalSubUserListFuture = subUserListFuture;
//        Runnable subUserListTask = () -> {
//            try {
//                CommonRes<?> result = binanceApiClient.querySubUserList(commonReq);
//                System.out.println("querySubUserListSUC: ");
//                if(!result.getSuccess()){
//                    System.out.println("querySubUserListFail result: " + JSONObject.toJSONString( result));
//                    if (finalSubUserListFuture != null) {
//                        finalSubUserListFuture.cancel(false); // 停止该任务
//                    }
//                    return;
//
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        };
//
//        // 调度第一个任务，初始延迟0秒，每隔5秒执行一次
//        apiKeyPermissionFuture=scheduler.scheduleAtFixedRate(apikeyPermissionTask, 0, 20, TimeUnit.SECONDS);
//        subUserListFuture=scheduler.scheduleAtFixedRate(subUserListTask, 0, 10, TimeUnit.SECONDS);


    }

    @Test
    public void testAddApiKey() throws Exception {
//        AddApiKeyReq request = JSONObject.parseObject(str, AddApiKeyReq.class);
//        cexUserConfigBizService.addConfig(request, "1000", "admin");
    }

    @Test
    public void testQuerySpotCoinAsset() {
        CommonCoinAssetReq commonReq = new CommonCoinAssetReq();
        commonReq.setCexType(CexTypeEnum.BINANCE.getType());
        cexApiService.querySpotCoinAsset(commonReq);
    }

    @Test
    public void testQueryFundingCoinAsset() {
        CommonCoinAssetReq commonReq = new CommonCoinAssetReq();
        commonReq.setCexType(CexTypeEnum.BINANCE.getType());
        cexApiService.queryFundingCoinAsset(commonReq);
    }

    @Test
    public void testBinanceApiExecuteContext() throws InvocationTargetException, IllegalAccessException {
//        binanceApiExecuteContext.setTypeToken(new TypeToken<SpotAccountInfoRes>(){});
//        binanceApiExecuteContext.setPath(BinancePathConfig.querySpotAccountInfo);
        CommonReq commonReq = null;
//        binanceApiExecuteContext.setCommonReq(commonReq);
//        binanceApiExecuteContext.setMethodType("GET");
//        binanceApiExecuteContext.execute();
    }







    @Resource
    ThirdCexUserService thirdCexUserService;

    @Resource
    CexUserBizService thirdCexUserBizService;

    @Test
    public void testSyncSubUser() {
        ThirdCexUser user = thirdCexUserService.selectByCexTypeAndUserId(CexTypeEnum.BINANCE.getType(), "*********");
        thirdCexUserBizService.syncSubUser(CexTypeEnum.BINANCE.getType(), "*********");
    }

    @Resource
    CexApiAggregateBizService cexApiAggregateBizService;

    @Test
    public void testQuerySubUserContrant() {
        UserAssetListReq commonReq = new UserAssetListReq();
        commonReq.setCexType(CexTypeEnum.BINANCE.getType());
        commonReq.setCexUserId("**********");
        commonReq.setCexEmail("<EMAIL>");
//        CommonRes commonRes = cexSubUserAssetHistoryBizService.querySubUserCoinAsset(commonReq);
//        System.out.println(JSONObject.toJSONString(commonRes));

//      cexSubUserAssetHistoryBizService.querySubUserAssetAndSync(userAssetListReq);
//        System.out.println(JSONObject.toJSONString(commonRes));

    }

    @Resource
    CexSubUserAssetHistoryBizService cexSubUserAssetHistoryBizService;

    @Resource
    CexParentUserAssetHistoryBizService cexParentUserAssetHistoryBizService;

    @Test
    public void testQueryAssetAndSync() throws Exception {
        UserAssetListReq commonReq = new UserAssetListReq();
        commonReq.setCexType(CexTypeEnum.BINANCE.getType());
        commonReq.setCexUserId("**********");
//        cexApiAggregationService.queryUserAssetAndSync(CexTypeEnum.BINANCE, "**********");
//        cexUserAssetAggregateBizService.queryUserAssetAndSync(commonReq);
//        CommonRes<CommonTotalAssetRes> commonRes = cexUserAssetHistoryBizService.queryCoinAsset(commonReq);
        //cexApiAggregationService.queryUserAssetAndSync(CexTypeEnum.BINANCE, "**********");
        CommonRes<CommonTotalAssetRes> commonRes = cexParentUserAssetHistoryBizService.queryCoinAsset(commonReq);
        System.out.println(JSONObject.toJSONString(commonRes));

    }

    @Test
    public void testQuerySubUserAssetAndSync() throws Exception {
        UserAssetListReq commonReq = new UserAssetListReq();

        commonReq.setCexType(CexTypeEnum.BINANCE.getType());
        commonReq.setCexUserId("**********");
//        commonReq.setCexEmail("<EMAIL>");
//        cexApiAggregationService.queryUserAssetAndSync(CexTypeEnum.BINANCE, "**********");

        CommonRes<CommonSubUserTotalAssetRes> commonRes = cexSubUserAssetHistoryBizService.querySubUserCoinAsset(commonReq);
        //cexApiAggregationService.queryUserAssetAndSync(CexTypeEnum.BINANCE, "**********");
        System.out.println(JSONObject.toJSONString(commonRes));

    }

    @Test
    public void testQueryUserAssetDetail() throws Exception {
        UserAssetDetailReq commonReq = new UserAssetDetailReq();
        commonReq.setCexType(CexTypeEnum.BINANCE.getType());
        commonReq.setCexUserId("**********");
        commonReq.setAssetType(ThirdAssetType.PARENT_SPOT);
//        commonReq.setCexEmail("<EMAIL>");
//        cexApiAggregationService.queryUserAssetAndSync(CexTypeEnum.BINANCE, "**********");
        //cexApiAggregationService.querySubUserAssetAndSync(commonReq);
        CommonRes<CommonTotalAssetRes> commonRes = cexApiAggregateBizService.queryCoinAssetDetail(commonReq);
        //cexApiAggregationService.queryUserAssetAndSync(CexTypeEnum.BINANCE, "**********");
        System.out.println(JSONObject.toJSONString(commonRes));

    }

    @Resource
    CexUserDepositeHistoryBizService cexUserDepositeHistoryBizService;

    @Test
    public void queryUserDepositeHistoryAndSync() {
        UserDepositeHistoryListReq userDepositeHistoryListReq = new UserDepositeHistoryListReq();
        userDepositeHistoryListReq.setCexType(CexTypeEnum.BINANCE.getType());
        userDepositeHistoryListReq.setCexUserId("**********");
        userDepositeHistoryListReq.setStartTime(new Date(1739270934000L));//2025-02-11 18:48:54
        userDepositeHistoryListReq.setEndTime(new Date(1746960534000L));//2025-05-11 18:48:54
        userDepositeHistoryListReq.setPageNum(1);
        userDepositeHistoryListReq.setPageSize(10);
        cexUserDepositeHistoryBizService.queryUserDepositeHistoryAndSync(userDepositeHistoryListReq);

        PageData<ThirdCexDepositeHistory> pageData = cexUserDepositeHistoryBizService.getUserDepositeHistory(userDepositeHistoryListReq);
        System.out.println(JSONObject.toJSONString(pageData));
    }


    @Test
    public void testUserList() {
        CexUserPageListRequest request = new CexUserPageListRequest();
        request.setCexType(CexTypeEnum.BINANCE.getType());
        request.setUserManagerEmail("hmac_CwgCEiBGODc1MDVBQTgzODY0NDEyMTVCRkI5NTREOUMzOEZBQhoQY29tbW9uRW5jcnlwdEtleQwSJRn9Jk++88QW+LAx5pBWwzV6/zygU4FC2LKlU6Yky3gZJ8ZOHIM=");
        List<ThirdCexUser> users =thirdCexUserBizService.getCexUsers(request);
        System.out.println(JSONObject.toJSONString(users));
//        request.setPageNum(1);
//        request.setPageSize(10);
//        PageData<ThirdCexUser> pageData = thirdCexUserService.getUsersByPage(request);
//        System.out.println(JSONObject.toJSONString(pageData));
    }

    @Resource
    CexUserWithdrawHistoryBizService cexUserWithdrawHistoryBizService;

    @Test
    public void queryUserWithdrawHistoryAndSync() {
        UserWithdrawHistoryListReq userWithdrawHistoryListReq = new UserWithdrawHistoryListReq();
        userWithdrawHistoryListReq.setCexType(CexTypeEnum.BINANCE.getType());
        userWithdrawHistoryListReq.setCexUserId("**********");
        userWithdrawHistoryListReq.setStartTime(new Date(1741252016000L));//2025-02-11 18:48:54
        userWithdrawHistoryListReq.setEndTime(new Date(1747472835000L));//2025-05-11 18:48:54
        userWithdrawHistoryListReq.setPageNum(1);
        userWithdrawHistoryListReq.setPageSize(10);
        cexUserWithdrawHistoryBizService.queryUserWithdrawHistoryAndSync(userWithdrawHistoryListReq);
        PageData<ThirdCexWithdrawHistory> pageData = cexUserWithdrawHistoryBizService.getUserWithdrawHistory(userWithdrawHistoryListReq);
        System.out.println(JSONObject.toJSONString(pageData));
    }

    @Resource
    ThirdCexAssetXxlJobExecutor thirdCexAssetXxlJobExecutor;

    @Resource
    CexParentSubTransferHistoryBizService cexParentSubTransferHistoryBizService;

    @Resource
    UserWithdrawAddressService userWithdrawAddressService;

    @Resource
    CexCoinConfigBizService cexCoinConfigBizService;

    @Resource
    CommonService commonService;

    @Resource
    ThirdCexUserAssetHistoryService thirdCexUserAssetHistoryService;

    @Test
    public void testCheckWithdraw() throws Exception {
        Boolean isSys = commonService.isSysUser(123L);
        System.out.println(isSys);
//        thirdCexAssetXxlJobExecutor.syncParentUniversalTransferRecord();
//        thirdCexAssetXxlJobExecutor.syncDepositeHistoryRecord();
//        thirdCexAssetXxlJobExecutor.syncWithdrawHistoryRecord();
//        thirdCexAssetXxlJobExecutor.syncPayTransferRecord();
//        thirdCexAssetXxlJobExecutor.syncParentSubTransferRecord();
//        thirdCexAssetXxlJobExecutor.syncSubUserAssets();
        //1109175970 **********
//        thirdCexAssetXxlJobExecutor.syncUserAssets();
        UserDepositeHistoryListReq userDepositeHistoryListReq=new UserDepositeHistoryListReq();
        userDepositeHistoryListReq.setCexType(CexTypeEnum.BINANCE.getType());
        userDepositeHistoryListReq.setPageSize(10);
        userDepositeHistoryListReq.setPageNum(1);
        PageData<ThirdCexDepositeHistory> thirdCexDepositeHistoryPageData=cexUserDepositeHistoryBizService.getUserDepositeHistory(userDepositeHistoryListReq);
        System.out.println(JSONObject.toJSONString(thirdCexDepositeHistoryPageData));
        //        List<ThirdCexUserAssetHistory> thirdCexUserAssetHistories = thirdCexUserAssetHistoryService.selectUserAssetByThirdAssetType("1109175970", 1, ThirdAssetType.subUserAssetTypes());
//        System.out.println(JSONObject.toJSONString(thirdCexUserAssetHistories));
//        UniversalTransferRecordReq universalTransferRecordReq = new UniversalTransferRecordReq(CexTypeEnum.BINANCE.getType(),"**********", null, null,MAIN_UMFUTURE.getName(), new Date(1745847747000L), new Date(1748439747000L));
//        cexTransferHistoryBizService.queryParentUniversalTransferRecordAndSync(universalTransferRecordReq);
//        cexUserWithdrawHistoryBizService.getUserIdByAddress("USDT","******************************************");
//        userWithdrawAddressService.insert(UserWithdrawAddress.builder().bgUid("1").address("******************************************").cexUserId("122").chaincoinid(70).coinName("USDT").network("TRN20").createTime(new Date()).updateTime(new Date()).version(1));
        CoinNameConfig coinNameConfig1 = new CoinNameConfig();
        coinNameConfig1.setCexCoinName("BTC");
        coinNameConfig1.setBgCoinName("BTC");
        coinNameConfig1.setCexType(1);
        coinNameConfig1.setCreateTime(new Date());
        coinNameConfig1.setUpdateTime(new Date());

        CoinNameConfig coinNameConfig2 = new CoinNameConfig();
        coinNameConfig2.setCexCoinName("USDT");
        coinNameConfig2.setBgCoinName("USDT");
        coinNameConfig2.setCexType(1);
        coinNameConfig2.setCreateTime(new Date());
        coinNameConfig2.setUpdateTime(new Date());
        CoinNameConfig coinNameConfig3 = new CoinNameConfig();
        coinNameConfig3.setCexCoinName("ETH");
        coinNameConfig3.setBgCoinName("ETH");
        coinNameConfig3.setCexType(1);
        coinNameConfig3.setCreateTime(new Date());
        coinNameConfig3.setUpdateTime(new Date());
        CoinNameConfig coinNameConfig4 = new CoinNameConfig();
        coinNameConfig4.setCexCoinName("USDC");
        coinNameConfig4.setBgCoinName("USDC");
        coinNameConfig4.setCexType(1);
        coinNameConfig4.setCreateTime(new Date());
        coinNameConfig4.setUpdateTime(new Date());

//        cexCoinConfigBizService.batchInsert(Arrays.asList(coinNameConfig1,coinNameConfig2,coinNameConfig3,coinNameConfig4));
//        System.out.println(cexCoinConfigBizService.selectBgCoinNameByCexTypeAndCexCoinName(1,"BTC"));
    }

    @Test
    public void syncPayTransferRecord() throws Exception {
        thirdCexAssetXxlJobExecutor.syncPayTransferRecord();
    }

    @Test
    public void  testHmac(){
        String emailencrt=HmacEncryptUtil.encryptFieldContent("<EMAIL>", "commonEncryptKey");
        System.out.println(HmacEncryptUtil.encryptFieldContent(emailencrt, "commonEncryptKey"));
    }


    @Test
    public void testwithdrawcheck() throws Exception {
        cexUserWithdrawHistoryBizService.checkWithdrawRecords();
    }

    @Resource
    CexParentUniversalTransferHistoryBizService cexParentUniversalTransferHistoryBizService;

    @Test
    public void syncAssetHistory() throws Exception {
        ThirdCexUserConfig userConfig = cexUserConfigBizService.getThirdCexUserConfigByUserId(  1,"*********");
        cexParentUniversalTransferHistoryBizService.syncAssetHistory(null,userConfig,  new Date(1745847747000L), new Date(1748439747000L), new Date(1745847747000L));
    }
    @Resource
    ThirdCexDepositeHistoryService thirdCexDepositeHistoryService;

    @Resource
    CexTransferHistoryService cexTransferHistoryService;
    @Test
    public void testDelete() throws Exception {
        thirdCexDepositeHistoryService.deleteByDepositeIds(Arrays.asList("4189869381629011712"));
        cexTransferHistoryService.deleteByTransferIds(Arrays.asList("270035294271"));
    }
    @Resource
    AlarmNotifyService alarmNotifyService;
    @Test
    public void testAlarm() throws Exception {
        ThirdCexUserConfig userConfig = cexUserConfigBizService.getThirdCexUserConfigByUserId(  1,"10003");
        alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_ASSET_SYNC_EXCEPTION, userConfig.getCexUserId(), CexTypeEnum.fromType(userConfig.getCexType()).getName(), "test", "error");
//        CommonReq commonReq = new CommonReq(CexTypeEnum.BINANCE.getType(), "**********");
//        cexApiService.queryApikeyPermission(commonReq);
        String json="{\n" +
                "    \"cexAssetSyncConfigHashMap\": {\n" +
                "        \"pay_transfer\": {\n" +
                "            \"maxQueryDay\": 7,\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1672502400000\n" +
                "        },\n" +
                "        \"parent_sub_transfer\": {\n" +
                "            \"maxQueryDay\": 7,\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1750918742000,\n" +
                "            \"initRefersh\": true,\n" +
                "            \"initSetTime\": 1750919414000\n" +
                "        },\n" +
                "        \"sub_balance\": {\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1672502400000\n" +
                "        },\n" +
                "        \"parent_balance\": {\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1672502400000\n" +
                "        },\n" +
                "        \"deposite\": {\n" +
                "            \"maxQueryDay\": 30,\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1672502400000,\n" +
                "            \"initRefersh\": true,\n" +
                "            \"initSetTime\": 1750917608000\n" +
                "        },\n" +
                "        \"parent_universial_transfer\": {\n" +
                "            \"maxQueryDay\": 7,\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1672502400000,\n" +
                "            \"initRefersh\": true,\n" +
                "            \"initSetTime\": 1738339200000\n" +
                "        },\n" +
                "        \"withdraw\": {\n" +
                "            \"maxQueryDay\": 90,\n" +
                "            \"sleep\": 1000,\n" +
                "            \"syncInterval\": 300,\n" +
                "            \"syncRecordInitTime\": 1672502400000,\n" +
                "            \"initRefersh\": true,\n" +
                "            \"initSetTime\": 1750768585000\n" +
                "        }\n" +
                "    },\n" +
                "    \"ifParallelQueryAsset\": false,\n" +
                "    \"querySecretSave\": 30,\n" +
                "    \"sqlInsertSize\": 300,\n" +
                "    \"syncRecordInitTime\": 1672502400000,\n" +
                "    \"timeInverval\": 5,\n" +
                "    \"tmpSecretSave\": 120,\n" +
                "    \"cexAlarmConfigs\": {\n" +
                "        \"THIRD_CEX_API_EXCEPTION\": {\n" +
                "            \"ifAlarm\": false,\n" +
                "            \"alarmSilenceTime\": \"*************\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"apiAlarmConfigs\": {\n" +
                "        \"10003\": [\n" +
                "            {\n" +
                "                \"apiPath\": \"/sap/subaccount5\",\n" +
                "                \"ifAlarm\": false,\n" +
                "                \"alarmSilenceTime\": \"*************\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
//        ApolloThirdCexAssetConfig apolloThirdCexAssetConfig=JSONObject.parseObject(json,ApolloThirdCexAssetConfig.class);
//        CommonReq commonReq=new CommonReq(1,"10003","apiKey","privateKey");
//        String apiPath = "/sap/subaccount";
//        if (apolloThirdCexAssetConfig.getApiAlarmConfigs() != null) {
//            if (apolloThirdCexAssetConfig.getApiAlarmConfigs().containsKey(commonReq.getCexUserId())) {
//                List<ApolloThirdCexAssetConfig.ApiAlarmConfig> apiAlarmConfigs = apolloThirdCexAssetConfig.getApiAlarmConfigs().get(commonReq.getCexUserId());
//                Optional<ApolloThirdCexAssetConfig.ApiAlarmConfig> apiAlarmConfig = apiAlarmConfigs.stream().filter(filterApiAlarmConfig -> filterApiAlarmConfig.getApiPath().equals(apiPath)).findFirst();
//                if (apiAlarmConfig.isPresent()) {
//                    if (apiAlarmConfig.get().getIfAlarm()) {
//                        //明确告警
//                        alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_API_EXCEPTION, commonReq.getCexUserId(), CexTypeEnum.fromType(commonReq.getCexType()).getName(), apiPath);
//                    } else {
//                        if (System.currentTimeMillis()>apiAlarmConfig.get().getAlarmSilenceTime()) {
//                            //到了告警静默时间也告警，否则在告警静默时间内不告警
//                            alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_API_EXCEPTION, commonReq.getCexUserId(), CexTypeEnum.fromType(commonReq.getCexType()).getName(), apiPath);
//                        }
//                    }
//                }else{
//                    alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_API_EXCEPTION, commonReq.getCexUserId(), CexTypeEnum.fromType(commonReq.getCexType()).getName(), apiPath);
//                }
//            }
//        } else {
//            alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_API_EXCEPTION, commonReq.getCexUserId(), CexTypeEnum.fromType(commonReq.getCexType()).getName(), apiPath);
//        }
//        alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_WITHDRAW_ADDRESS_ILLEGAL, "1", "BINANCE", "apiPath", "message");
    }


    @Test
    public void testAddCoinAddress(){
        UserWithdrawAddress userWithdrawAddress=new UserWithdrawAddress();
        userWithdrawAddress.setChainCoinid(1);
        userWithdrawAddress.setAddress("1233add");
        userWithdrawAddress.setCoinId(11);
        userWithdrawAddress.setCoinName("BTC");
        userWithdrawAddress.setCreateTime(new Date());
        userWithdrawAddress.setNetwork("network");
        userWithdrawAddress.setUpdateTime(new Date());
        userWithdrawAddress.setVersion(SerialNoGenerator.getMinIdByTime(new Date()));
        userWithdrawAddressService.insert(userWithdrawAddress);
        UserWithdrawAddress userWithdrawAddress1=userWithdrawAddressService.selectByAddress("1233add","BTC","network");
        System.out.println(JSONObject.toJSONString(userWithdrawAddress1));
    }
}
