package com.upex.reconciliation.service.cex;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.upex.reconciliation.job.ReconciliationJobApplication;
import com.upex.reconciliation.service.business.cex.CexUserBizService;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.service.ThirdCexUserService;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CexUserListRequest;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.ModCexUserRequest;
import com.upex.reconciliation.service.service.client.cex.enmus.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@Slf4j
@RunWith(SpringRunner.class)
//@ActiveProfiles("local")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@SpringBootTest(classes = ReconciliationJobApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ThirdCexUserServiceTest {

    @Resource
    ThirdCexUserService thirdCexUserService;

    @Resource
    CexUserBizService thirdCexUserBizService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testUseTypeEnumDeserialization() throws Exception {
        String json = "{\"useType\": 1}";
        ModCexUserRequest request = objectMapper.readValue(json, ModCexUserRequest.class);
        assertNotNull(request.getUseType());
//        assertEquals(CexUseTypeEnum.WITHDRAW, request.getUseType());
    }


    @Test
    public void addUser(){
        ModCexUserRequest req =new ModCexUserRequest();
//        req.setUserType(UserTypeEnum.PARENT_USER);
//        req.setCexType(CexTypeEnum.BINANCE);
//        req.setCexUserId("1358498");
//        req.setCexEmail("<EMAIL>");
//        req.setUserManagerEmail("<EMAIL>");
//        req.setUserManagerId("1233");
//        req.setUseType(UseTypeEnum.DEPOSIT.getType());
//        req.setTradeType(TradeTypeEnum.CLASSIC_ACCOUNT);
//        req.setUserKyb(1);
//        thirdCexUserBizService.addParentUser(new ModCexUserRequest());
    }

    @Test
    public void testQuery() {
        ThirdCexUser user = new ThirdCexUser();
        user.setCexUserId("1358498");
        user.setCexType(1);
        user.setCexEmail("<EMAIL>");
//        user.setParentUserId("1234");
        user.setUserType(1);
        user.setApiKeyStatus(1);
        user.setUserManagerEmail("<EMAIL>");
        user.setUserManagerId("1233");
        user.setUserManageStatus(UserManagerStatusEnum.DISABLE.getType());
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        int insert =thirdCexUserService.add(user);
        log.info("insert:{}", insert);
        CexUserListRequest cexUserListRequest=new CexUserListRequest();
//        cexUserListRequest.setCexType(CexTypeEnum.BINANCE);
//        cexUserListRequest.setPageSize(10);
//        cexUserListRequest.setPageNum(1);

//        thirdCexUserService.getUsersByPage(cexUserListRequest);
//        log.info("users:{}", thirdCexUserService.getUsersByPage(cexUserListRequest));
    }
}
