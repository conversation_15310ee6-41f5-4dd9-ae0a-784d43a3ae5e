<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script src='./decimal.js'></script>
    <script src="http://libs.baidu.com/jquery/2.0.0/jquery.min.js"></script>
    <script>
    /***
    高精度计算库：decimal.js
    http://docs.asprain.cn/nodejsdecimal/API.html
    ***/
        $(function(){
            $("#consolidate").click(function(){
                var content = $("#content").val();
                var rows = content.split("\n");
                var map = new Map()
                for(var i=0;i<rows.length;i++){
                    var row = rows[i];
                    var rowSplit = row.split("\t");
                    var key = rowSplit[0];
                    var value = rowSplit[1];
                    value = value === '0E-16' ? 0 : parseFloat(value);
                    var oldValue = map.get(key);
                    map.set(key, !!oldValue?oldValue+value:value);
                }
                console.log(map)
                $("#result").val(JSON.stringify(Object.fromEntries(map)));
            });

            $("#jsonCompare").click(function(){
                   var content = $("#content").val();
                   var content1 = $("#content1").val();
                   var contentObj = JSON.parse(content)
                   var content1Obj = JSON.parse(content1)
                   var map = new Map()
                   for(var key of Object.keys(contentObj)){
                        var value1 = contentObj[key]
                        var value2 = content1Obj[key]
                        if(value1 !== value2){
                            map.set(key, value1 + "---" +value2)
                        }
                   }
                   $("#result").val(JSON.stringify(Object.fromEntries(map)));
            });

            $("#sum").click(function(){
                var content = $("#content").val();
                var rows = content.split("\n");
                var result = 0;
                for(var i=0;i<rows.length;i++){
                    var row = rows[i];
                    result += parseFloat(row)
                }
                $("#result").val(result);
            });
            $("#jsonSum").click(function(){
                var content = $("#content").val();
                var contentObj = JSON.parse(content)
                var result = new Decimal(0);
                for(var key of Object.keys(contentObj)){
                     var value = contentObj[key]
                     result = new Decimal(value).add(result)
                }
                $("#result").val(result.toFixed(18));
            });
            $("#jsonGroupSum").click(function(){
                var content1 = $("#content1").val();
                var rows = content1.split("\n");
                var groupKeyMap = new Map()
                for(var i=0;i<rows.length;i++){
                    var row = rows[i];
                    var jsonRow = JSON.parse(row)
                    for (let index in jsonRow) {
                      groupKeyMap.set(jsonRow[index], row)
                    }
                }
                console.log(groupKeyMap)
                console.log(groupKeyMap.get("lever_full-6"))

                var content = $("#content").val();
                var contentObj = JSON.parse(content)
                var resultMap = new Map()
                for(var key of Object.keys(contentObj)){
                    var groupKey = !!groupKeyMap.get(key) ? groupKeyMap.get(key) : "other"
                     var value = contentObj[key]
                     var oldValue = !!resultMap.get(groupKey) ? resultMap.get(groupKey) : new Decimal(0)
                     var result = new Decimal(value).add(oldValue)
                     resultMap.set(groupKey, result)
                }
                console.log(resultMap)
                $("#result").val(JSON.stringify(Object.fromEntries(resultMap),null,2));
            });
            $("#selectSum").click(function(){
                var selectRow = window.getSelection().toString();
                var rows = selectRow.split("\n");
                var result = new Decimal(0);
                for(var i=0;i<rows.length;i++){
                    var row = rows[i];
                    result =  new Decimal(row).add(result)
                }
                $("#result").val(result);
            });
            $("#sumByOrderId").click(function(){
                var content = $("#content").val();
                var rows = content.split("\n");
                var map1 = new Map()
                for(var i=0;i<rows.length;i++){
                    var row = rows[i];
                    var rowSplit = row.split("\t");
                    var key = rowSplit[0];
                    var value = rowSplit[1] === '0E-16' ? new Decimal(0) : new Decimal(rowSplit[1]);
                    var oldValue = map1.get(key);
                    map1.set(key, !!oldValue ? oldValue.add(value) : value);
                }
                console.log(map1)
                var resultMap = new Map()
                for (let [key, value] of map1) {
                    if(value.comparedTo(0) != 0){
                        resultMap.set(key, value)
                    }
                }
                $("#result").val(JSON.stringify(Object.fromEntries(resultMap)));
            });
        });
    </script>
</head>
<body>
<textarea id="content" style="height:300px;width:49%;"></textarea>
<textarea id="content1" style="height:300px;width:49%;"></textarea>
<div> </div>
<div style="text-align:center;">
    <input type="button" value="合并计算" id="consolidate">
    <input type="button" value="JSON差异对比" id="jsonCompare">
    <input type="button" value="JSON合并计算" id="jsonSum">
    <input type="button" value="JSON分组合并计算" id="jsonGroupSum">
    <input type="button" value="选中求和" id="selectSum">
    <input type="button" value="求和" id="sum">
    <input type="button" value="订单Id聚合计算" id="sumByOrderId">
</div>
<div></div>
<textarea id="result" style="height:300px;width:99%;"></textarea>
</body>
</html>