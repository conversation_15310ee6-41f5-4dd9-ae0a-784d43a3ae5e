package com.upex.reconciliation.job.task;

import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.cex.*;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.service.CexUserConfigService;
import com.upex.reconciliation.service.service.ThirdCexUserService;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CommonReq;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.QueryApiKeyPermissionReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonApiKeyPermissionRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.ThirdCexUserInnerRes;
import com.upex.reconciliation.service.service.client.cex.enmus.ApiKeyStatusEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.CexAssetHistoryTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.CexTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ReadOnlyEnum;
import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class ThirdCexAssetXxlJobExecutor {

    @Resource
    CexApiAggregateBizService cexApiAggregateBizService;

    @Resource
    CexUserBizService cexUserBizService;

    @Resource
    CexApiService cexApiService;

    @Resource
    ThirdCexUserService thirdCexUserService;

    @Resource
    CexUserConfigService cexUserConfigService;

    @Resource
    AlarmNotifyService alarmNotifyService;

    @Resource
    CexUserWithdrawHistoryBizService cexUserWithdrawHistoryBizService;



    /**
     * 同步母用户资产
     */
    @XxlJob("syncUserAssets")
    public void syncUserAssets() {
        try {
            log.info("syncUserAssets started");
            cexApiAggregateBizService.queryCexAssetAndSync(CexAssetHistoryTypeEnum.PARENT_BALANCE);
            log.info("syncUserAssets ended");
        } catch (Exception e) {
            log.error("syncUserAssets error", e);
        }
    }

    /**
     * 同步子用户资产
     */
    @XxlJob("syncSubUserAssets")
    public void syncSubUserAssets() {
        try {
            log.info("syncSubUserAssets started");
            cexApiAggregateBizService.queryCexAssetAndSync(CexAssetHistoryTypeEnum.SUB_BALANCE);
            log.info("syncSubUserAssets ended");
        } catch (Exception e) {
            log.error("syncUserAssets error", e);
        }
    }

    /**
     * 同步充值记录
     */
    @XxlJob("syncDepositeHistoryRecord")
    public void syncDepositeHistoryRecord() {
        try {
            log.info("syncDepositeHistoryRecord execute start");
            cexApiAggregateBizService.queryCexAssetAndSync(CexAssetHistoryTypeEnum.DEPOSITE);
            log.info("syncUserDepositeHistoryRecord end");
        }catch (Exception e){
            log.error("syncUserDepositeHistoryRecord error", e);
        }
    }

    /**
     * 同步提币记录
     */
    @XxlJob("syncWithdrawHistoryRecord")
    public void syncWithdrawHistoryRecord() throws InterruptedException {
        try {
            log.info("syncUserWithdrawHistoryRecord execute start");
            cexApiAggregateBizService.queryCexAssetAndSync(CexAssetHistoryTypeEnum.WITHDRAW);
            log.info("syncUserWithdrawHistoryRecord end");
        }catch (Exception e){
            log.error("syncUserWithdrawHistoryRecord error", e);
        }

    }

    /**
     * 同步转账记录
     */
    @XxlJob("syncPayTransferRecord")
    public void syncPayTransferRecord() throws InterruptedException {
        try {
            log.info("syncPayTransferRecord execute start");
            cexApiAggregateBizService.queryCexAssetAndSync(CexAssetHistoryTypeEnum.PAY_TRANSFER);
            log.info("syncPayTransferRecord end");
        }catch (Exception e){
            log.error("syncPayTransferRecord error", e);
        }
    }

    /**
     * 同步母子划转记录
     * 如果startTime和endTime均未发送，默认只返回最近30天数据
     */
    @XxlJob("syncParentSubTransferRecord")
    public void syncParentSubTransferRecord() throws InterruptedException {
        try {
            log.info("syncParentSubTransferRecord execute start");
            cexApiAggregateBizService.queryCexAssetAndSync(CexAssetHistoryTypeEnum.PARENT_SUB_TRANSFER);
            log.info("syncParentSubTransferRecord end");
        }catch (Exception e){
            log.error("syncParentSubTransferRecord error", e);
        }
    }

    /**
     * 同步母用户万向划转
     * fromSymbol 必须要发送，当类型为 ISOLATEDMARGIN_MARGIN 和 ISOLATEDMARGIN_ISOLATEDMARGIN
     * toSymbol 必须要发送，当类型为 MARGIN_ISOLATEDMARGIN 和 ISOLATEDMARGIN_ISOLATEDMARGIN
     * 仅支持查询最近半年（6个月）数据
     * 若startTime和endTime没传，则默认返回最近7天数据
     */
    @XxlJob("syncParentUniversalTransferRecord")
    public void syncParentUniversalTransferRecord() throws InterruptedException {
        try {
            log.info("syncParentUniversalTransferRecord execute start");
            cexApiAggregateBizService.queryCexAssetAndSync(CexAssetHistoryTypeEnum.PARENT_UNIVERSIAL_TRANSFER);
            log.info("syncParentUniversalTransferRecord end");
        }catch (Exception e){
            log.error("syncParentUniversalTransferRecord error", e);
        }
    }

    /**
     * 同步子用户
     */
    @XxlJob("syncSubUser")
    public void syncSubUser() throws InterruptedException {
        try {
            log.info("syncSubUser execute start");
            List<ThirdCexUserInnerRes> users =cexUserBizService.queryParentUserList(CexTypeEnum.BINANCE.getType());
            if(CollectionUtils.isEmpty( users)){
                return;
            }
            for(ThirdCexUserInnerRes user:users) {
                cexUserBizService.syncSubUser(CexTypeEnum.BINANCE.getType(), user.getUserId());
            }
            log.info("syncSubUser end");
        }catch (Exception e){
            log.error("syncSubUser error", e);
        }
    }

    /**
     * 查询API监控状态
     */
    @XxlJob("queryApiKeyMonitor")
    public void queryApiKeyMonitor() throws InterruptedException {
        try {
            log.info("queryApiKeyMonitor execute start");
            List<ThirdCexUser> thirdCexUsers = thirdCexUserService.selectAllParentUserByCexType(CexTypeEnum.BINANCE.getType());
            if (CollectionUtils.isEmpty(thirdCexUsers)) {
                log.warn("NoCexUser is null,cexType:{}", CexTypeEnum.BINANCE.getType());
                return;
            }
            for (ThirdCexUser cexUser : thirdCexUsers) {
                ThirdCexUserConfig userConfig = cexUserConfigService.getEffectiveAssetMonitorConfigByUserId(cexUser.getCexType(), cexUser.getCexUserId());
                if (userConfig == null) {
                    log.warn("NoCexUserAssetMonitorConfig is null,cexType:{},userId:{},cexAssetHistoryTypeEnum:{}", cexUser.getCexType(), cexUser.getCexUserId());
//                alarmNotifyService.alarm(AlarmTemplateEnum.THIRD_CEX_USER_NOT_CONFIG_ASSET_API, cexUser.getCexUserId(), CexTypeEnum.fromType(cexUser.getCexType()).getName());
                    continue;
                }
                CommonReq commonReq = new CommonReq(cexUser.getCexType(),cexUser.getCexUserId(),userConfig.getApiKey(),userConfig.getApiKeyPrivate());
                CommonRes<CommonApiKeyPermissionRes> res = cexApiService.queryApikeyPermission(commonReq);
                if(!res.getSuccess()){
                    cexUserBizService.updateUserApiKeyStatus(cexUser,userConfig);
                    alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_APIKEY_INVALID, cexUser.getCexUserId(), CexTypeEnum.fromType(cexUser.getCexType()).getName(),userConfig.getApiKeyLabel(),JSONObject.toJSONString(res));
                }
            }
            log.info("queryApiKeyMonitor end");
        }catch (Exception e){
            log.error("queryApiKeyMonitor error", e);
        }
    }

    @XxlJob("checkUserWithdrawHistoryRecord")
    public void checkUserWithdrawHistoryRecord() throws InterruptedException {
        log.info("checkUserWithdrawHistoryRecord start");
        cexUserWithdrawHistoryBizService.checkWithdrawRecords();
        log.info("checkUserWithdrawHistoryRecord end");
    }

    @XxlJob("hmactest")
    public void hmactest() throws InterruptedException {
        log.info("hmactest start");
        String jobParam = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        Boolean ifEncryp = jsonObject.getBoolean("ifEncryp");
        String data = jsonObject.getString("data");
        if(ifEncryp){
            log.info("encryptdata:"+HmacUtil.encrypt(data));
        }else{
            log.info("decryptdata:"+HmacUtil.decrypt(data));
        }
        log.info("hmactest end");
    }

}
