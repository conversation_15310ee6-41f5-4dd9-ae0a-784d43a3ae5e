package com.upex.reconciliation.job.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.upex.config.facade.swap.EnabledSwapTokenIncludePriceConfiguration;
import com.upex.contract.entrance.feign2.extension.ContractAccountAssetsCalExtension;
import com.upex.mixcontract.process.facade.extension.MixAccountAssetsCalExtension;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * @Author: allen
 * @Date: 2020-05-09 17:15
 * @DES: 配置
 */
@Configuration
@EnableApolloConfig(value = "application")
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableTransactionManagement
@EnableDiscoveryClient
@EnabledSwapTokenIncludePriceConfiguration
@ComponentScan(basePackages = {
        "com.upex.reconciliation",
        "com.upex.swap.facade",
        "com.upex.spot.facade",
        "com.upex.otc.facade",
        "com.fiat.fund.facade",
        "com.upex.commons",
        "com.upex.commons.support.i18n",
        "com.upex.user.facade",
        "com.upex.ticker.facade",
        "com.upex.newwallet.facade",
        "com.upex.mixcontract.process.facade",
        "com.upex.config.facade",
        "com.upex.financial.facade",
        "com.upex.margin.facade.inner",
        "com.upex.assets.facade",
        "com.upex.data.risk.facade",
        "com.upex.fiat.payment.center.facade",
        "com.upex.margin.facade.api",
        "com.upex.unified.account.facade.service",
        "com.upex.fiat.order.center.facade",
        "com.upex.data.feature.facade.inner",
        "com.upex.kline.feign.inner",
        "com.upex.convert.facade",
        "com.upex.reconciliation.service.business",
        "com.binance.connector.client.common",
        "com.binance.connector.client.common.auth"})
@EnableFeignClients(basePackages = {
        "com.upex.contract.entrance.feign2",
        "com.upex.otc.facade",
        "com.upex.spot.facade",
        "com.fiat.fund.facade",
        "com.upex.swap.facade",
        "com.upex.user.facade",
        "com.upex.mixcontract.process.facade",
        "com.upex.ticker.facade",
        "com.upex.newwallet.facade",
        "com.upex.config.facade",
        "com.upex.financial.facade",
        "com.upex.margin.facade.inner",
        "com.upex.assets.facade",
        "com.upex.data.risk.facade.inner",
        "com.upex.fiat.payment.center.facade",
        "com.upex.fiat.order.center.facade",
        "com.upex.margin.facade.api",
        "com.upex.unified.account.facade",
        "com.upex.broker.facade",
        "com.upex.data.feature.facade.inner",
        "com.upex.kline.feign.inner",
        "com.upex.convert.facade",
        "com.upex.reconciliation.service.business",
        "com.binance.connector.client.common",
        "com.binance.connector.client.common.auth"})
public class MainConfig {

    @Bean
    public ContractAccountAssetsCalExtension contractAccountAssetsCalExtension() {
        return new ContractAccountAssetsCalExtension();
    }

    @Bean
    public MixAccountAssetsCalExtension mixAccountAssetsCalExtension() {
        return new MixAccountAssetsCalExtension();
    }


}
