<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.upex.bill</groupId>
        <artifactId>upex-reconciliation</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>upex-reconciliation-job</artifactId>
    <name>upex-reconciliation-job</name>
    <description>upex-reconciliation-job</description>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions> <!--去除之前对老版本log4j的依赖-->
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.mixcontract</groupId>
            <artifactId>mc-common-utils</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.commons</groupId>
            <artifactId>upex-datasource</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.config</groupId>
            <artifactId>upex-config-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.contract</groupId>
            <artifactId>upex-contract-entrance-feign2</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.mixcontract</groupId>
            <artifactId>mc-process-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-data-sparse-facade</artifactId>
                    <groupId>com.upex.sparse</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-rpc-common</artifactId>
                    <groupId>com.upex.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-rpc-client</artifactId>
                    <groupId>com.upex.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.mixcontract</groupId>
            <artifactId>mc-common-framework</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.spot</groupId>
            <artifactId>upex-spot-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>upex-user-dto</artifactId>
                    <groupId>com.upex.user</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-datacenter-dto</artifactId>
                    <groupId>com.upex.datacenter</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-datacenter-facade</artifactId>
                    <groupId>com.upex.datacenter</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-log</artifactId>
                    <groupId>com.upex.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-rpc-client</artifactId>
                    <groupId>com.upex.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.commons</groupId>
            <artifactId>upex-reactive-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.bill</groupId>
            <artifactId>upex-reconciliation-service</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>upex-assets-facade</artifactId>
                    <groupId>com.upex.assets</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-spot-facade</artifactId>
                    <groupId>com.upex.spot</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-jwt</artifactId>
                    <groupId>com.upex.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-datacenter-facade</artifactId>
                    <groupId>com.upex.datacenter</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-stream-dto</artifactId>
                    <groupId>com.upex.stream</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.commons</groupId>
            <artifactId>upex-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.bill</groupId>
            <artifactId>upex-reconciliation-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.assets</groupId>
            <artifactId>upex-assets-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mc-common-utils</artifactId>
                    <groupId>com.upex.mixcontract</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.commons</groupId>
                    <artifactId>upex-rpc-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.mixcontract</groupId>
            <artifactId>mc-common-domain</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <finalName>upex-reconciliation-job</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
