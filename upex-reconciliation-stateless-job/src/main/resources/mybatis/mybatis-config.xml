<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <!--        <setting name="logImpl" value="STDOUT_LOGGING"/>-->
        <setting name="cacheEnabled" value="false"/>
        <setting name="lazyLoadingEnabled" value="true"/>
        <setting name="multipleResultSetsEnabled" value="true"/>
        <setting name="useColumnLabel" value="true"/>
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <setting name="defaultExecutorType" value="REUSE"/>
        <setting name="defaultStatementTimeout" value="15000"/>
        <setting name="defaultEnumTypeHandler" value="com.upex.reconciliation.service.common.DefaultBaseEnumHandler"/>
    </settings>
    <typeAliases>
        <package name="com.upex.mixcontract.common.domain.global"/>
        <package name="com.upex.strategy.dao.entity"/>
    </typeAliases>

    <typeHandlers>
        <package name="com.upex.strategy.dao.typehandler"/>
        <package name="com.upex.mapper.enums"/>
        <package name="com.upex.mixcontract.match.service.dao.typehandler"/>
        <package name="com.upex.mixcontract.common.mapper.ths"/>
    </typeHandlers>
    <mappers>
<!--                <package name="com.upex.strategy.base.mapper"/>-->
<!--                <package name="com.upex.mixcontract.common.mapper.global.*Mapper.xml"/>-->
    </mappers>
</configuration>