<?xml version="1.0" encoding="UTF-8"?>
<configuration status="info">
    <Properties>
        <Property name="logDir">${sys:user.home}/data/logs/upex-reconciliation-stateless-job</Property>
        <!--如果需要压缩修改为log.gz-->
        <Property name="rollingSuffix">log.gz</Property>
        <Property name="CONSOLE_LOG_PATTERN">%clr{%d{yyyy-MM-dd HH:mm:ss.SSS}}{faint} %X{UPEXTID} %clr{%5p} %clr{%5.5T}{magenta} %clr{---}{faint} %clr{[%20.20t]}{faint} %clr{%-40.40c{1.}}{cyan} %clr{:}{faint} %m%n
        </Property>
        <Property name="FILE_LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} %X{UPEXTID} %5p %5.5T --- [%20.20t] %-40.40c{1.} : %m%n
        </Property>
        <Property name="FILE_LOG_PATTERN_SECURITY">%m%n</Property>
    </Properties>

    <appenders>
        <!--这个输出控制台的配置-->
        <console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${CONSOLE_LOG_PATTERN}"/>
        </console>
        <RollingRandomAccessFile name="RollingFileInfo" fileName="${logDir}/info.log"
                                 filePattern="${logDir}/logs/%d{yyyy-MM-dd}-%i-info.${rollingSuffix}">
            <Filters>
                <ThresholdFilter level="INFO"/>
            </Filters>
            <PatternLayout pattern="${FILE_LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="1024 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="40">
                <Delete basePath="${logDir}/logs/" maxDepth="1">
                    <IfFileName glob="*.${rollingSuffix}">
                        <IfAny>
                            <IfAccumulatedFileSize exceeds="50 GB"/>
                            <IfLastModified age="15d"/>
                        </IfAny>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="RollingFileWarn" fileName="${logDir}/warn.log"
                                 filePattern="${logDir}/logs/%d{yyyy-MM-dd}-%i-warn.${rollingSuffix}">
            <Filters>
                <ThresholdFilter level="WARN"/>
            </Filters>
            <PatternLayout pattern="${FILE_LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="1024 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="40"/>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="RollingFileError" fileName="${logDir}/error.log"
                                 filePattern="${logDir}/logs/%d{yyyy-MM-dd}-%i-error.${rollingSuffix}"
                                 immediateFlush="false">
            <ThresholdFilter level="ERROR"/>
            <PatternLayout pattern="${FILE_LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="1024 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="40"/>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="RollingFileAlarm" fileName="${logDir}/alarm.log"
                                 filePattern="${logDir}/logs/%d{yyyy-MM-dd}-%i-alarm.${rollingSuffix}"
                                 immediateFlush="false">
            <PatternLayout pattern="${FILE_LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="1024 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="40"/>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="RollingFileSecurity" fileName="${logDir}/security.log"
                                 filePattern="${logDir}/logs/%d{yyyy-MM-dd}-%i-security.${rollingSuffix}"
                                 immediateFlush="false">
            <PatternLayout pattern="${FILE_LOG_PATTERN_SECURITY}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="1024 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="40"/>
        </RollingRandomAccessFile>
    </appenders>

    <loggers>
        <logger name="com.upex.utils.log.SecurityUtils" level="info">
            <appender-ref ref="RollingFileSecurity"/>
        </logger>
        <logger name="com.upex.utils.log.AlarmUtils" level="error">
            <appender-ref ref="RollingFileAlarm"/>
        </logger>
        <!--过滤掉spring和mybatis的一些无用的DEBUG信息-->
        <logger name="com.upex.bill.data" level="DEBUG" additivity="false">
            <appender-ref ref="Console"/>
        </logger>

        <!--配置输出sql语句-->
        <logger name="org.apache.ibatis" level="DEBUG"/>
        <logger name="java.sql.Connection" level="DEBUG"/>
        <logger name="java.sql.Statement" level="DEBUG"/>
        <logger name="java.sql.PreparedStatement" level="DEBUG"/>
        <logger name="RocketmqClient" level="error" additivity="false"/>

        <root level="info">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileError"/>
        </root>
    </loggers>
</configuration>