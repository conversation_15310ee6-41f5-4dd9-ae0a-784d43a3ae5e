log.info("checkSystemUserAssetChange processFilterRule context:{}",context.toString());

if(!EnvUtil.isServiceInstance("upex-reconciliation-job03")){
    return false;
}

List accountTypes = [(byte)10,(byte)21,(byte)22,(byte)52,(byte)53,(byte)54];
List userIdList = [148553856L,1922139819L,519190811L];
Date currentSnapshotDate = DateUtil.getDayDateByTimeStr("08:00:00");
//Date currentSnapshotDate = DateUtil.str2date("2024-09-02 18:00:00", "yyyy-MM-dd HH:mm:ss");
Date preSnapshotDate = DateUtil.addDay(currentSnapshotDate, -1);

// 获取当前用户资产
Map newUserAssets = new HashMap();
for(Long userId : userIdList){
    Map userAssets =ruleEngineDataService.getUserSnapshotAssets(userId, accountTypes, currentSnapshotDate, null);
    newUserAssets.put(userId, userAssets);
}

// 获取上期用户资产
Map oldUserAssets =new HashMap();
for(Long userId : userIdList){
    Map userAssets =ruleEngineDataService.getUserSnapshotAssets(userId, accountTypes, preSnapshotDate, null);
    oldUserAssets.put(userId, userAssets);
}

context.put("userIdList", userIdList);
context.put("accountTypes", accountTypes);
context.put("currentSnapshotDate", currentSnapshotDate);
context.put("preSnapshotDate", preSnapshotDate);
context.put("newUserAssets", newUserAssets);
context.put("oldUserAssets", oldUserAssets);

return true;