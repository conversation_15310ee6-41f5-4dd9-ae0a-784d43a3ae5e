log.info("checkSystemUserAssetChange dataCheckerRule context:{}",context.toString());

expectedValue = extensionConfig.getBigDecimal("expectedValue");

List userIdList = context.get("userIdList");
Map newUserAssets = context.get("newUserAssets");
Map oldUserAssets = context.get("oldUserAssets");
List alarmList = new ArrayList();
for(Long userId : userIdList){
    Map newAssets = newUserAssets.get(userId);
    Map oldAssets = oldUserAssets.get(userId);
    if(newAssets == null || oldAssets == null){
        continue;
    }
    for(Integer coinId : newAssets.keySet()){
        BigDecimal newAsset = newAssets.getOrDefault(coinId, BigDecimal.ZERO);
        BigDecimal oldAsset = oldAssets.getOrDefault(coinId, BigDecimal.ZERO);
        BigDecimal changeDiff = newAsset.subtract(oldAsset).abs();
        Boolean changeResult = changeDiff.compareTo(expectedValue) > 0;
        BigDecimal changeRate = oldAsset.compareTo(BigDecimal.ZERO) > 0 ? newAsset.divide(oldAsset, 2, BigDecimal.ROUND_HALF_UP).abs() : BigDecimal.ZERO;
        if(changeResult){
            Map alarmMap = new HashMap();
            alarmMap.put("userId", userId);
            alarmMap.put("coinId", coinId);
            alarmMap.put("newAsset", newAsset);
            alarmMap.put("oldAsset", oldAsset);
            alarmMap.put("changeDiff", changeDiff);
            alarmMap.put("changeRate", changeRate);
            alarmMap.put("expectedValue", expectedValue);
            alarmMap.put("changeResult", changeResult);
            alarmList.add(alarmMap);
        }
    }
}

context.put("alarmList", alarmList);
return true;