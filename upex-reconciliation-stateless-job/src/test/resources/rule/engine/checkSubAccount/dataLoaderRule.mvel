CommonBillChangeData billFlowData = (CommonBillChangeData) context.get("billFlowData");
Long userId = billFlowData.accountId;

// 是否子账户
Boolean isSubAccount = ruleEngineDataService.isSubAccount(userId);
if (!isSubAccount) {
    return false;
}

// 是否是nd broker子账户
Boolean isBroker = ruleEngineDataService.isBroker(userId);
if (isBroker) {
    return false;
}

context.put("userId", userId);
context.put("accountType", billFlowData.accountType);
context.put("bizType", billFlowData.bizType);
context.put("bizId", billFlowData.bizId);
context.put("billParams", billFlowData.params);
return true;