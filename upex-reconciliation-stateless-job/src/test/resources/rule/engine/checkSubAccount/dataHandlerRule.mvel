Long userId = context.get("userId");
byte accountType = context.get("accountType");
String bizType = context.get("bizType");
String configParams = context.get("configParams");
String bizMsg = "";
if (accountType == 10 && (bizType.equals("9") || bizType.equals("31")) && configParams != null && configParams != "") {
    String notes = context.get("billParams");
    String[] typeArr = configParams.split(",");
    foreach(String type : typeArr) {
        if (notes.equals(type)) {
            bizMsg = type;
        }
    }
    if (bizMsg == "") {
        return false;
    }
}
// 子账户告警
String bizType = context.get("bizType");
Long bizId = context.get("bizId");
// 注册告警模板
String code = "subAccountAlarm";
String text = "子账户出金告警：❗❗❗ 用户Id：{} 业务线：({}) 业务类型：{} 业务信息：{} 消息Id：{}";
AlarmTemplateEnum.registerTemplate(code, text);
alarmNotifyService.alarm(code, userId, accountType, bizType, bizMsg, bizId);
return true;