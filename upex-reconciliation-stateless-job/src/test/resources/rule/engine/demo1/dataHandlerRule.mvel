log.info("dataHandlerRule context:{}",context.toString());
Boolean result = context.getBoolean("result");
if(result){
    log.info("dataHandlerRule: dataHandlerRule executed successfully");
}else{
    log.error("dataHandlerRule: dataHandlerRule execution failed");
}
alarmNotifyService.alarm(AlarmTemplateEnum.CONTRACT_EXPERIENCE_ACCOUNT_ERROR, context.get("systemUserId"), context.get("accountTypeName"), context.get("couponType"), context.get("balance"), context.get("alarmBalance"));
return true;