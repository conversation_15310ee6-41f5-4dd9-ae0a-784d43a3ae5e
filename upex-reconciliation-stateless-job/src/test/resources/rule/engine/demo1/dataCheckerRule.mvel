import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.utils.BillCapitalAmountUtils;

log.info("dataCheckerRule context:{}",context.toString());

CommonBillChangeData billFlowData = (CommonBillChangeData) context.get("billFlowData");

systemUserRelationshipMap = extensionConfig.systemUserRelationshipMap;
log.info("dataCheckerRule systemUserRelationshipMap:{}", JSONObject.toJSONString(systemUserRelationshipMap));


// 获取流水余额 判断校验值
BigDecimal propSum = ruleEngineDataService.getPropSumByBillChangeData(billFlowData);
BigDecimal alarmBalance = extensionConfig.getBigDecimal("alarmBalance");
log.info("打印流水余额:{}", systemUserRelationshipMap.get(billFlowData.accountId));
String diffUExpression = systemUserRelationshipMap.get(billFlowData.accountId).?diffUExpression == nil ? extensionConfig.getString("diffUExpression") : systemUserRelationshipMap.get(billFlowData.accountId).?diffUExpression;
log.info("打印diffUExpression:{}", diffUExpression);

if(BillCapitalAmountUtils.billAssetDiff(propSum, diffUExpression)){
    context.put("balance", propSum);
    context.put("alarmBalance", alarmBalance);
    context.put("systemUserId", billFlowData.accountId);
    context.put("result", true);
    context.put("accountTypeName", AccountTypeEnum.toEnum(billFlowData.accountType).getDesc());
    context.put("couponType", systemUserRelationshipMap.get(billFlowData.accountId).couponType);

    log.info("校验不通过");
    return true;
}else{
    log.info("校验通过");
    return false;
}