log.info("processFilterRule context:{}",context.toString());

if(!EnvUtil.isServiceInstance("upex-reconciliation-job03")){
    return false;
}

List coinIdList = [1,2];
String assetsType = "internal";
List coinTypeList = ["otc-5","spot-1","spot-7","spot-11","spot-28","spot-56","spot-86","spot-29","spot-32","spot-87","spot-96","spot-97","spot-139","spot-244","spot-246","spot-248","spot-308","spot-326","spot-259","otc-6","spot-2","spot-31","spot-30","spot-88","spot-85","spot-100","spot-243","spot-245","spot-247","spot-325","spot-258","spot-417"];
Date currentSnapshotDate = DateUtil.getDayDateByTimeStr("08:00:00");
//Date currentSnapshotDate = DateUtil.str2date("2024-09-02 14:00:00", "yyyy-MM-dd HH:mm:ss");
Date preSnapshotDate = DateUtil.addDay(currentSnapshotDate, -1);

// 获取当前用户资产
Map newCoinAssets =ruleEngineDataService.getCoinSnapshotAssets(assetsType, currentSnapshotDate, coinIdList);
// 获取上期用户资产
Map oldCoinAssets =ruleEngineDataService.getCoinSnapshotAssets(assetsType, preSnapshotDate, coinIdList);

// 获取当前用户资产
Map newCoinTypeAssets =ruleEngineDataService.getCoinTypeSnapshotAssets(assetsType, currentSnapshotDate, coinIdList, coinTypeList);
// 获取上期用户资产
Map oldCoinTypeAssets =ruleEngineDataService.getCoinTypeSnapshotAssets(assetsType, preSnapshotDate, coinIdList, coinTypeList);


context.put("coinIdList", coinIdList);
context.put("assetsType", assetsType);
context.put("currentSnapshotDate", currentSnapshotDate);
context.put("preSnapshotDate", preSnapshotDate);
context.put("newCoinAssets", newCoinAssets);
context.put("oldCoinAssets", oldCoinAssets);
context.put("newCoinTypeAssets", newCoinTypeAssets);
context.put("oldCoinTypeAssets", oldCoinTypeAssets);

return true;