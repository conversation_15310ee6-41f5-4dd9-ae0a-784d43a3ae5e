log.info("dataCheckerRule context:{}",context.toString());

expectedValue = extensionConfig.getBigDecimal("expectedValue");

List coinIdList = context.get("coinIdList");
Map newCoinAssets = context.get("newCoinAssets");
Map oldCoinAssets = context.get("oldCoinAssets");
Map newCoinTypeAssets = context.get("newCoinTypeAssets");
Map oldCoinTypeAssets = context.get("oldCoinTypeAssets");
List alarmList = new ArrayList();
for(Integer coinId : coinIdList){
    BigDecimal newCoinAsset = newCoinAssets.getOrDefault(coinId, BigDecimal.ZERO);
    BigDecimal oldCoinAsset = oldCoinAssets.getOrDefault(coinId, BigDecimal.ZERO);
    BigDecimal newCoinTypeAsset = newCoinTypeAssets.getOrDefault(coinId, BigDecimal.ZERO);
    BigDecimal oldCoinTypeAsset = oldCoinTypeAssets.getOrDefault(coinId, BigDecimal.ZERO);
    BigDecimal coinChangeDiff = newCoinAsset.subtract(oldCoinAsset).abs();
    BigDecimal coinTypeChangeDiff = newCoinTypeAsset.subtract(oldCoinTypeAsset).abs();
    BigDecimal changeDiff = coinChangeDiff.subtract(coinTypeChangeDiff).abs();
    Boolean changeResult = changeDiff.compareTo(expectedValue) > 0;
    BigDecimal changeRate = coinTypeChangeDiff.compareTo(BigDecimal.ZERO) > 0 ? coinChangeDiff.divide(coinTypeChangeDiff, 2, BigDecimal.ROUND_HALF_UP).abs() : BigDecimal.ZERO;
    if(changeResult){
        Map alarmMap = new HashMap();
        alarmMap.put("coinId", coinId);
        alarmMap.put("newCoinAsset", newCoinAsset);
        alarmMap.put("oldCoinAsset", oldCoinAsset);
        alarmMap.put("newCoinTypeAsset", newCoinTypeAsset);
        alarmMap.put("oldCoinTypeAsset", oldCoinTypeAsset);
        alarmMap.put("coinChangeDiff", coinChangeDiff);
        alarmMap.put("coinTypeChangeDiff", coinTypeChangeDiff);
        alarmMap.put("changeDiff", changeDiff);
        alarmMap.put("changeRate", changeRate);
        alarmMap.put("changeResult", changeResult);
        alarmMap.put("expectedValue", expectedValue);
        alarmList.add(alarmMap);
    }
}

context.put("alarmList", alarmList);
return true;