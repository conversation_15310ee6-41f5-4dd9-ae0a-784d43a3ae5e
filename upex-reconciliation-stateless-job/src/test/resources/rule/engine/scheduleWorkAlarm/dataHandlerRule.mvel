List personnelList = ["<PERSON>","<PERSON>","<PERSON>","<PERSON>"];
List larkAddress = ["sarah.liang|ou_f1d733c7c5b3d608aa456dc42d81e020","jason.jiang|ou_4de7b1a820b55bb3547da02c55aef7e2","change.wang|ou_6f4418206ad7c9c2532ed4fa693e51c5","mark.chen|ou_eeea1179ed1e7ab38895f996820c1031"];

String code = "scheduleWorkAlarmTemplate";
String text = "值班顺序：{}。\n今日值班人员：{}";
AlarmTemplateEnum.registerTemplate(code, text);

Integer dayOfYear = DateUtil.getDayOfYear();
Integer index = dayOfYear % personnelList.size();

alarmNotifyService.alarm(AlarmParam.builder()
                             .templateEnumName(code)
                             .atUserList(Lists.newArrayList(larkAddress.get(index)))
                             .arg(new Object[]{JSON.toJSONString(personnelList),personnelList.get(index)})
                             .build());
return true;