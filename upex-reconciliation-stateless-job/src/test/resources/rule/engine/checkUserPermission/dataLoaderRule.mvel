CommonBillChangeData billFlowData = (CommonBillChangeData) context.get("billFlowData");
Long userId = billFlowData.accountId;

Pair pair = ruleEngineDataService.getPermissionCodes(billFlowData);
if (pair == null || pair.left == null || pair.left.size() == 0) {
    return false;
}
Byte accountType = billFlowData.accountType;
String bizType = billFlowData.bizType;
context.put("userId", userId);
context.put("accountType", accountType);
context.put("bizType", bizType);
context.put("bizId", billFlowData.bizId);
context.put("codeList", pair.left);
context.put("bizMsg", pair.right);
return true;