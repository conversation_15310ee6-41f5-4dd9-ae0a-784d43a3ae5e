log.info("dataHandlerRule context:{}",context.toString());

List records = context.get("checkResult");

if(records == null || records.isEmpty()){
    log.info("dataHandlerRule: no records found for processing");
    return true;
}

Map map = new HashMap();
map.put("resultList", records);

log.info("dataHandlerRule 准备报警通知");
alarmNotifyService.alarm(AlarmTemplateEnum.SYSTEM_ASSETS_DISBURSE_OUT_RANGE_TEMPLATE, map);
log.info("dataHandlerRule: processing records:{}", JSONObject.toJSONString(records));
return true;