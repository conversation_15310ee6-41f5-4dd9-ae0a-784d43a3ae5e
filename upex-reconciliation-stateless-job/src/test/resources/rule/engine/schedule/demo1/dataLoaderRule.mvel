import com.upex.reconciliation.service.business.ruleengine.dto.SystemUserRelationshipDto;
import com.upex.reconciliation.service.business.ruleengine.dto.TimeWindowsFlows;
import com.upex.reconciliation.service.business.ruleengine.dto.SystemUserAssetsMonitorDto;

log.info("dataLoaderRule execute start");
List systemUserRelationships = [
    new SystemUserRelationshipDto(533913177L, "52-default", "合约体验金，合约跟单体验券，合约策略体验券(网格/马丁格尔)"),
    new SystemUserRelationshipDto(1052305578L, "52-default", "合约仓位体验券"),
    new SystemUserRelationshipDto(3250133794L, "10-default", "现货网格策略"),
    new SystemUserRelationshipDto(1852654928L, "21-default", "杠杆体验金"),
    new SystemUserRelationshipDto(1675462459L, "10-default", "手续费返还券")
];
log.info("dataLoaderRule execute 初始化");

windowIntervalTime = extensionConfig.windowIntervalTime;
serviceInstanceName = extensionConfig.serviceInstanceName;
Boolean runExecute = EnvUtil.isServiceInstance(serviceInstanceName);
if(!runExecute){
    return false;
}

List billFlows = new java.util.ArrayList();
foreach(userRelationship : systemUserRelationships){
    Date checkTime = ruleEngineDataService.getCheckTime(userRelationship);
    timeWindowsFlows = ruleEngineDataService.getTimeWindowsUserFlows(userRelationship, checkTime, windowIntervalTime);
    if(timeWindowsFlows != empty){
        billFlows.add(timeWindowsFlows);
    }
}
log.info("dataLoaderRule execute 处理完成, 共{}条数据", billFlows.size());
if(billFlows.size() == 0) {
    return billFlows.size() > 0;
}

log.info("dataLoaderRule execute 处理数据");

List userAssetsMonitorDataList = ruleEngineDataService.calculateUserAssetsMonitorData(billFlows);
log.info("dataLoaderRule execute userAssetsMonitorDataList size: " + userAssetsMonitorDataList.size());
context.put("userAssetsMonitorDataList", userAssetsMonitorDataList);
log.info("dataLoaderRule execute end");

return userAssetsMonitorDataList.size() > 0;

