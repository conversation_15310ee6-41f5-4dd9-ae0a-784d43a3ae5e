log.info("dataCheckerRule execute start");

import com.upex.reconciliation.service.business.ruleengine.dto.SystemUserAssetsMonitorDto;

List userAssetsMonitorDataList = context.get("userAssetsMonitorDataList");

windowIntervalTime = extensionConfig.windowIntervalTime;
systemUserRelationshipMap = extensionConfig.systemUserRelationshipMap;


List records =  [];
foreach(userAssetsMonitorData : userAssetsMonitorDataList) {
    JSONObject element1 = new JSONObject();
    BigDecimal payAmount = userAssetsMonitorData.payAmount;
    userId = userAssetsMonitorData.userId;
    BigDecimal alarmBalance = extensionConfig.getBigDecimal("alarmBalance");
    BigDecimal ratio = extensionConfig.getBigDecimal("ratio");
    BigDecimal realAlarmBalance = alarmBalance.multiply(ratio);
    boolean result = payAmount.abs().compareTo(realAlarmBalance) <= 0;
    element1.put("payAmount", payAmount);
    element1.put("alarmBalance", alarmBalance);
    element1.put("ratio", ratio);
    element1.put("realAlarmBalance", realAlarmBalance);
    element1.put("startTime", userAssetsMonitorData.startTime);
    element1.put("endTime", userAssetsMonitorData.endTime);
    element1.put("userId", userId);
    element1.put("windowIntervalTime", windowIntervalTime);
    element1.put("accountTypeName", userAssetsMonitorData.accountTypeName);
    element1.put("couponType", systemUserRelationshipMap.get(userId).couponType);

    if(result == false) {
        records.add(element1);
    }
}
context.put("checkResult", records);

log.info("dataCheckerRule execute end");
return records.size() > 0;