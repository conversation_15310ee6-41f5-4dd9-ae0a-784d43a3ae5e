log.info("dataHandlerRule context:{}",context.toString());

String today = context.get("today");
String yesterday = context.get("yesterday");

String code = "financeOverThresholdAlarm";
String text = "财务用户每日余额变动超出阈值：❗❗❗ \n触发阈值：{}\n详细信息：{}\n统计时间：{} ~ {}";
AlarmTemplateEnum.registerTemplate(code, text);

defaultDifferenceThreshold = extensionConfig.defaultDifferenceThreshold;
List records = context.get("checkResult");
Map transferAmountMap = context.get("transferAmountMap");

recordsStr = "";
foreach(record : records) {
    Long userId = record.get("userId");
    String userDisplay = ruleEngineDataService.getSysUserDisplayStr(userId);
    recordsStr = recordsStr + String.format("\n用户id：%s(%s)，余额差值：%s", record.get("userId"), userDisplay, record.get("differenceUsdt"));
    Map toTransferAmountMap = transferAmountMap.get(userId);
    if (toTransferAmountMap != null && toTransferAmountMap.size() > 0) {
        for (Long toUserId : toTransferAmountMap.keySet()) {
            String toUserDisplay = ruleEngineDataService.getSysUserDisplayStr(toUserId);
            recordsStr += String.format("\n\t划转至用户id：%s(%s)，折U金额：%s", toUserId, toUserDisplay, toTransferAmountMap.get(toUserId));
        }
    }
}

alarmNotifyService.alarm("financeOverThresholdAlarm", defaultDifferenceThreshold, recordsStr, yesterday, today);
log.info("dailyDifference alarm context " + text, defaultDifferenceThreshold, recordsStr, yesterday, today);
return true;