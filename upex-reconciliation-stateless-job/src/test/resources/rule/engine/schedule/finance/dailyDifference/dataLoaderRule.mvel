List accountTypes = [(byte)10,(byte)21,(byte)22,(byte)30,(byte)52,(byte)53,(byte)54];
timeStr = extensionConfig.calculateTime;
Date today = DateUtil.getDayDateByTimeStr(timeStr == null ? "08:00:00" : timeStr);
Date yesterday = DateUtil.addDay(today, -1);

userIdList = extensionConfig.userIds;
coinIdList = extensionConfig.coinIdList;
context.put("coinIdList", coinIdList);
context.put("accountTypes", accountTypes);
context.put("today", DateUtil.date2str(today));
context.put("yesterday", DateUtil.date2str(yesterday));

Map newUserAssets = new HashMap();
userAccountTypeFeeMap = extensionConfig.userAccountTypeFeeMap;
unProfitTransferMap = extensionConfig.unProfitTransferMap;
userBizTypeMap = extensionConfig.userBizTypeMap;
normalTransferUserIdList = extensionConfig.normalTransferUserIdList;
log.info("userIdList:{}, coinIdList:{}, userAccountTypeFeeMap:{}, unProfitTransferMap:{}, userBizTypeMap:{}", userIdList, coinIdList, userAccountTypeFeeMap, unProfitTransferMap, userBizTypeMap);

// 获取用户资产
def calAssetUsdt(whichDay, snapshotTime) {
    Map userUsdts = new HashMap();
    foreach(userId : userIdList) {
        Map userAssetMap = ruleEngineDataService.getUserSnapshotAssets(userId, accountTypes, whichDay, coinIdList);
        BigDecimal userAssetUsdt = ruleEngineDataService.calculateCoinSnapshotAssetsUsdt(userAssetMap, snapshotTime);
        userUsdts.put(userId, userAssetUsdt);
    }
    return userUsdts;
}

todayTotalUsdts = calAssetUsdt(today, today);
yesterdayTotalUsdts = calAssetUsdt(yesterday, today);
context.put("todayTotalUsdts", todayTotalUsdts);
context.put("yesterdayTotalUsdts", yesterdayTotalUsdts);

// 获取用户待动账资产
def calUnProfitAssetUsdt(whichDay, snapshotTime) {
    Map userUnProfitUsdts = new HashMap();
    foreach(userId : userIdList) {
        unProfitMap = unProfitTransferMap.get(userId);
        if (unProfitMap != null) {
            Map userUnProfitAssetMap = ruleEngineDataService.getUserUnProfitTransferAssets(userId, unProfitMap.toAccountTypeList, unProfitMap.transferTypeList, whichDay);
            BigDecimal userUnProfitAssetUsdt = ruleEngineDataService.calculateCoinSnapshotAssetsUsdt(userUnProfitAssetMap, snapshotTime);
            userUnProfitUsdts.put(userId, userUnProfitAssetUsdt);
        }
    }
    return userUnProfitUsdts;
}

todayUnProfitUsdts = calUnProfitAssetUsdt(today, today);
yesterdayUnProfitUsdts = calUnProfitAssetUsdt(yesterday, today);
context.put("todayUnProfitUsdts", todayUnProfitUsdts);
context.put("yesterdayUnProfitUsdts", yesterdayUnProfitUsdts);

// 获取今日业务线手续费
Map todayFeeUsdts = ruleEngineDataService.getAccountTypeFeeTotalUsdt(accountTypes, today, today, coinIdList);
context.put("todayFeeUsdts", todayFeeUsdts);
// 获取昨日业务线手续费
Map yesterdayFeeUsdts = ruleEngineDataService.getAccountTypeFeeTotalUsdt(accountTypes, yesterday, today, coinIdList);
context.put("yesterdayFeeUsdts", yesterdayFeeUsdts);
// 获取用户change资产
Map userChangePropSumUsdts = new HashMap();
// 获取划转用户金额
Map transferAmountMap = new HashMap();
Map normalTransferAmountMap = new HashMap();
foreach(userId : userIdList) {
    List bizTypes = userBizTypeMap.get(userId);
    if (bizTypes != null && bizTypes.size() > 0) {
        Map userChangePropSumUsdt = ruleEngineDataService.getUserChangePropSum(userId, accountTypes, bizTypes, coinIdList, yesterday, today);
        userChangePropSumUsdts.put(userId, userChangePropSumUsdt);
    }
    BigDecimal normalTransferUsdt = BigDecimal.ZERO;
    Map toTransferAmountMap = ruleEngineDataService.getTransferAmount(userId, yesterday, today);
    if (normalTransferUserIdList != null && normalTransferUserIdList.size() > 0 && toTransferAmountMap.size() > 0) {
        foreach(transferUserId : normalTransferUserIdList) {
            Long transferId = (Long)transferUserId;
            if (toTransferAmountMap.containsKey(transferId)) {
                normalTransferUsdt = normalTransferUsdt.add(toTransferAmountMap.getOrDefault(transferId, BigDecimal.ZERO));
                toTransferAmountMap.remove(transferId);
            }
        }
    }
    transferAmountMap.put(userId, toTransferAmountMap);
    normalTransferAmountMap.put(userId, normalTransferUsdt);
}
context.put("userChangePropSumUsdts", userChangePropSumUsdts);
context.put("transferAmountMap", transferAmountMap);
context.put("normalTransferAmountMap", normalTransferAmountMap);
log.info("todayFeeUsdts:{}, yesterdayFeeUsdts:{}, todayUnProfitUsdts:{}, yesterdayUnProfitUsdts:{}, userChangePropSumUsdts:{}, transferAmountMap:{}, normalTransferAmountMap:{}",
todayFeeUsdts, yesterdayFeeUsdts, todayUnProfitUsdts, yesterdayUnProfitUsdts, userChangePropSumUsdts, transferAmountMap, normalTransferAmountMap);

return todayTotalUsdts.size() > 0 && yesterdayTotalUsdts.size() > 0;