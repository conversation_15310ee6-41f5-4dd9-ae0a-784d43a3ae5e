log.info("dataCheckerRule execute start");

List accountTypes = context.get("accountTypes");
Map todayTotalUsdts = context.get("todayTotalUsdts");
Map yesterdayTotalUsdts = context.get("yesterdayTotalUsdts");
Map todayFeeUsdts = context.get("todayFeeUsdts");
Map yesterdayFeeUsdts = context.get("yesterdayFeeUsdts");
Map userChangePropSumUsdts = context.get("userChangePropSumUsdts");
Map normalTransferAmountMap = context.get("normalTransferAmountMap");
Map todayUnProfitUsdts = context.get("todayUnProfitUsdts");
Map yesterdayUnProfitUsdts = context.get("yesterdayUnProfitUsdts");
userAccountTypeFeeMap = extensionConfig.userAccountTypeFeeMap;
userIds = extensionConfig.userIds;
defaultDifferenceThreshold = new BigDecimal(extensionConfig.defaultDifferenceThreshold);
// log.info("dataCheckerRule start, context:{}, userIds:{}, defaultDifferenceThreshold:{}", context, userIds, defaultDifferenceThreshold);

List records = new java.util.ArrayList();
foreach(userId : userIds) {
    BigDecimal todayTotalUsdt = todayTotalUsdts.get(userId);
    BigDecimal yesterdayTotalUsdt = yesterdayTotalUsdts.get(userId);
    List accountTypeFeeList = userAccountTypeFeeMap.get(userId);

    // 今日扣减手续费总额
    BigDecimal todayFeeTotalUsdt = BigDecimal.ZERO;
    // 昨日扣减手续费总额
    BigDecimal yesterdayFeeTotalUsdt = BigDecimal.ZERO;
    if (accountTypeFeeList != null) {
        foreach(type : accountTypeFeeList) {
            // 今日扣减手续费
            todayFeeTotalUsdt = todayFeeTotalUsdt.add(todayFeeUsdts.getOrDefault(type, BigDecimal.ZERO));
            // 昨日扣减手续费
            yesterdayFeeTotalUsdt = yesterdayFeeTotalUsdt.add(yesterdayFeeUsdts.getOrDefault(type, BigDecimal.ZERO));
        }
    }
    Map userChangePropSumUsdtMap = userChangePropSumUsdts.get(userId);
    // 用户change资产总额
    BigDecimal userChangeTotalUsdt = BigDecimal.ZERO;
    if(userChangePropSumUsdtMap != null && userChangePropSumUsdtMap.size() > 0) {
        foreach(accountType : accountTypes) {
            userChangeTotalUsdt = userChangeTotalUsdt.add(userChangePropSumUsdtMap.getOrDefault(accountType, BigDecimal.ZERO));
        }
    }

    // 用户待动账资产
    BigDecimal todayUnProfitUsdt = todayUnProfitUsdts.getOrDefault(userId, BigDecimal.ZERO);
    BigDecimal yesterdayUnProfitUsdt = yesterdayUnProfitUsdts.getOrDefault(userId, BigDecimal.ZERO);

    // 用户余额差值=今日用户应收余额(今日用户余额+今日待动账余额)-昨日用户应收余额(昨日用户余额+昨日待动账余额)
    BigDecimal userDifferenceUsdt = todayTotalUsdt.add(todayUnProfitUsdt).subtract(yesterdayTotalUsdt).subtract(yesterdayUnProfitUsdt);
    // 手续费差值=今日手续费-昨日手续费
    BigDecimal feeDifferenceUsdt = todayFeeTotalUsdt.subtract(yesterdayFeeTotalUsdt);
    // 正常划转金额
    BigDecimal normalTransferUsdt = normalTransferAmountMap.getOrDefault(userId, BigDecimal.ZERO);
    // 余额差值=用户余额差值-手续费差值-change总资产+正常划转金额
    BigDecimal differenceUsdt = userDifferenceUsdt.subtract(feeDifferenceUsdt).subtract(userChangeTotalUsdt).add(normalTransferUsdt);
    log.info("userId:{}, todayTotalUsdt:{}, yesterdayTotalUsdt:{}, todayUnProfitUsdt:{}, yesterdayUnProfitUsdt:{}, userDifferenceUsdt:{}, todayFeeTotalUsdt:{}, yesterdayFeeTotalUsdt:{}, feeDifferenceUsdt:{}, userChangeTotalUsdt:{}, normalTransferUsdt:{}, differenceUsdt:{}, defaultDifferenceThreshold:{}",
        userId, todayTotalUsdt, yesterdayTotalUsdt, todayUnProfitUsdt, yesterdayUnProfitUsdt, userDifferenceUsdt, todayFeeTotalUsdt, yesterdayFeeTotalUsdt, feeDifferenceUsdt, userChangeTotalUsdt, normalTransferUsdt, differenceUsdt, defaultDifferenceThreshold);
    if(differenceUsdt.abs().compareTo(defaultDifferenceThreshold) > 0) {
        element = new JSONObject();
        element.put("userId", userId);
        element.put("differenceUsdt", differenceUsdt);
        records.add(element);
    }
}

context.put("checkResult", records);
log.info("dataCheckerRule execute end,records:{}", records);
return records.size() > 0;
