List larkAddress = ["sarah.liang|ou_f1d733c7c5b3d608aa456dc42d81e020","jason.jiang|ou_4de7b1a820b55bb3547da02c55aef7e2","change.wang|ou_6f4418206ad7c9c2532ed4fa693e51c5","mark.chen|ou_eeea1179ed1e7ab38895f996820c1031"];
List billTableList = ["bill_contract_profit_transfer"];
List mainBillSnapshotTableList = ["bill_base_coin_asset_balance_snapshot","bill_base_user_asset_balance_snapshot_eight","bill_base_user_asset_balance_snapshot_zero","bill_capital_coin_type_property","bill_capital_coin_type_sub_property","proof_big_customer_loan_snapshot","proof_wallet_assets_snapshot","user_group_assets_snapshot_coin_all0"];
List billSnapshotTableList = ["bill_base_coin_asset_balance_snapshot","bill_base_user_asset_balance_snapshot_eight","bill_base_user_asset_balance_snapshot_zero","bill_capital_coin_type_property","bill_capital_coin_type_sub_property","proof_wallet_assets_snapshot","user_group_assets_snapshot_coin_all0","user_merkel_tree_detail","user_merkel_tree_node","wallet_assets_snapshot"];

Map mainStationMap = [
    "upex_reconciliation#upex_reconciliation" : billTableList,
    "lv4db2#upex_bill_snapshot" : mainBillSnapshotTableList,
];
Map otherStationMap = [
    "stealth-bill#upex_bill,turkey-bill#upex_bill,saas2-bill#upex_bill" : billTableList,
    "stealth-bill#upex_bill_snapshot,turkey-bill#upex_bill_snapshot,saas2-bill#upex_bill_snapshot" : billSnapshotTableList,
];

String tableView = "CREATE OR REPLACE VIEW view_%s.%s AS SELECT * from %s.%s;\n";
String code = "scheduleCreateTableAlarmTemplate";
String text = "主站站点：\n";
String yearMonth = DateUtil.format2Str(DateUtil.FMT_yyyyMM, new Date());
for (String mainStation : mainStationMap.keySet()) {
    String[] mainStationArr = mainStation.split(",");
    for (int i = 0; i < mainStationArr.size(); i++) {
        String dbInstance = mainStationArr[i];
        String dbInstanceArr = dbInstance.split("#");
        text += String.format("库：%s，实例：%s\n", dbInstanceArr[0], dbInstanceArr[1]);
        text += "请创建月表视图：\n";
        List tableList = mainStationMap.get(mainStation);
        for (String table : tableList) {
            String fullTable = table + "_" + yearMonth;
            text += String.format(tableView, dbInstanceArr[1], fullTable, dbInstanceArr[1], fullTable);
        }
        text += "\n";
    }
}
AlarmTemplateEnum.registerTemplate(code, text);

alarmNotifyService.alarm(AlarmParam.builder()
     .templateEnumName(code)
     .atUserList(larkAddress)
     .build());

text = "其他站点：\n";
for (String otherStation : otherStationMap.keySet()) {
    String[] otherStationArr = otherStation.split(",");
    String db = otherStationArr[0].split("#")[1];
    for (int i = 0; i < otherStationArr.size(); i++) {
        String dbInstance = otherStationArr[i];
        String dbInstanceArr = dbInstance.split("#");
        text += String.format("库：%s，实例：%s\n", dbInstanceArr[0], dbInstanceArr[1]);
    }
    text += "请创建月表视图：\n";
    List tableList = otherStationMap.get(otherStation);
    for (String table : tableList) {
        log.info("table:{}, yearMonth:{}", table, yearMonth);
        String fullTable = table + "_" + yearMonth;
        text += String.format(tableView, db, fullTable, db, fullTable);
    }
    text += "\n";
}
AlarmTemplateEnum.registerTemplate(code, text);

alarmNotifyService.alarm(AlarmParam.builder()
     .templateEnumName(code)
     .atUserList(larkAddress)
     .build());
return true;