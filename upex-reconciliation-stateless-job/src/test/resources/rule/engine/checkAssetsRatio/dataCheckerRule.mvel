log.info("dataCheckerRule execute start");

import com.upex.reconciliation.service.business.ruleengine.dto.SystemUserAssetsMonitorDto;
import com.upex.reconciliation.service.utils.BillCapitalAmountUtils;

List systemUserAssetsRatioMonitorDtoList = context.get("systemUserAssetsRatioMonitorDtoList");
systemUserRelationshipMap = extensionConfig.systemUserRelationshipMap;

List records =  [];
foreach(systemUserAssetsRatioMonitorDto : systemUserAssetsRatioMonitorDtoList) {
    JSONObject element1 = new JSONObject();
    BigDecimal calculateRatio = systemUserAssetsRatioMonitorDto.ratio;
    userId = systemUserAssetsRatioMonitorDto.userId;
    String diffUExpression = systemUserRelationshipMap.get(userId).?diffUExpression == nil ? extensionConfig.getString("diffUExpression") : systemUserRelationshipMap.get(userId).?diffUExpression;
    BigDecimal ratio = extensionConfig.getBigDecimal("ratio");
    boolean result = BillCapitalAmountUtils.billAssetDiff(calculateRatio, diffUExpression);
    element1.put("beginAmount", systemUserAssetsRatioMonitorDto.beginAmount);
    element1.put("endAmount", systemUserAssetsRatioMonitorDto.endAmount);
    element1.put("calculateRatio", calculateRatio);
    element1.put("ratio", diffUExpression);
    element1.put("startTime", systemUserAssetsRatioMonitorDto.startTime);
    element1.put("endTime", systemUserAssetsRatioMonitorDto.endTime);
    element1.put("userId", userId);

    if(result == true) {
        records.add(element1);
    }
}
context.put("checkResult", records);

log.info("dataCheckerRule execute end");
return records.size() > 0;