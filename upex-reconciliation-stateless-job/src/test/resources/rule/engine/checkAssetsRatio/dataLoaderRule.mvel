import com.upex.reconciliation.service.business.ruleengine.dto.SystemUserAssetsRatioParam;
import com.google.common.collect.Lists;


log.info("dataLoaderRule execute start");
List systemUserAssetsRatioParamList = [
    new SystemUserAssetsRatioParam(4868943236L, Lists.newArrayList("10-default"))
];
log.info("dataLoaderRule execute 初始化");


List systemUserAssetsRatioMonitorDtoList = ruleEngineDataService.calculateUserAssetsURatioMonitor(systemUserAssetsRatioParamList);
log.info("dataLoaderRule execute 处理数据");

log.info("dataLoaderRule execute systemUserAssetsRatioMonitorDtoList size: " + systemUserAssetsRatioMonitorDtoList.size());
context.put("systemUserAssetsRatioMonitorDtoList", systemUserAssetsRatioMonitorDtoList);
log.info("dataLoaderRule execute end");

return systemUserAssetsRatioMonitorDtoList.size() > 0;

