CommonBillChangeData billFlowData = (CommonBillChangeData) context.get("billFlowData");
log.info("checkBizTypeInOut dataLoaderRule billFlowData:{}", JSONObject.toJSONString(billFlowData));
Byte accountType = billFlowData.accountType;
Integer bizType = billFlowData.bizType;
Set bizTypeInOutSet = ruleEngineDataService.getBizTypeInOutSet(accountType);
context.put("bizTypeInOutSet", bizTypeInOutSet);
if (bizTypeInOutSet == null || bizTypeInOutSet.size() == 0) {
    return false;
}
return bizTypeInOutSet.contains(bizType);