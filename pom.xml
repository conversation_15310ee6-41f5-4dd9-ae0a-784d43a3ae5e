<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.upex.commons</groupId>
        <artifactId>upex-parent</artifactId>
        <version>********</version>
    </parent>
    <groupId>com.upex.bill</groupId>
    <artifactId>upex-reconciliation</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <name>upex-reconciliation</name>
    <description>实时对账系统</description>

    <modules>
        <module>upex-reconciliation-facade</module>
        <module>upex-reconciliation-service</module>
        <module>upex-reconciliation-job</module>
        <module>upex-reconciliation-rest</module>
        <module>upex-reconciliation-stateless-job</module>
    </modules>

    <properties>
        <revision>0.0.23</revision>
        <java.version>11</java.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <log4j2.version>2.17.0</log4j2.version>
        <upex-stream-facade-version>1.0.0</upex-stream-facade-version>
        <upex-config-facade-version>********</upex-config-facade-version>
        <jaxb-version>2.3.0</jaxb-version>
        <redisson-version>3.19.1</redisson-version>
        <activation-version>1.1.1</activation-version>
        <mc-common-version>3.8.0</mc-common-version>
        <upex.spot.facade.version>2.283</upex.spot.facade.version>
        <upex.contract.entrance.feign2.version>1.7.10</upex.contract.entrance.feign2.version>
        <upex.assets.facade.version>2.0.40</upex.assets.facade.version>
        <upex.swap.facade.version>1.0</upex.swap.facade.version>
        <upex.otc.facade.version>2.6.0.8</upex.otc.facade.version>
        <upex.mixcontract.mc.process.facade.version>3.5.0</upex.mixcontract.mc.process.facade.version>
        <upex.newwallet.facade.version>0.1.0.47</upex.newwallet.facade.version>
        <upex.margin.facade.version>1.7.3</upex.margin.facade.version>
        <upex.user.facade.version>2.14.50</upex.user.facade.version>
        <upex.data.sparse.facade.version>1.7.0</upex.data.sparse.facade.version>
        <upex.fiat.payment.center.facade.version>2.0.18</upex.fiat.payment.center.facade.version>
        <upex-fiat-order-center-facade.version>4.1.2</upex-fiat-order-center-facade.version>
        <upex.fiat.payment.center.facade.version>2.0.23.20</upex.fiat.payment.center.facade.version>
        <upex.data.risk.facade.version>1.5.11</upex.data.risk.facade.version>
        <upex.broker.facade.version>1.1.30</upex.broker.facade.version>
        <metrics-core.version>4.2.19</metrics-core.version>
        <freemarker.version>2.3.31</freemarker.version>
        <upex.fund.facade.version>2.6.1.9</upex.fund.facade.version>
        <kafka-clients.version>3.4.1</kafka-clients.version>
        <flatten-maven-plugin.version>1.1.0</flatten-maven-plugin.version>
        <mysql-connector-java.version>8.0.23</mysql-connector-java.version>
        <upex.broker.facade.version>1.1.34</upex.broker.facade.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <upex.commons.parent.version>2.6.13</upex.commons.parent.version>
        <org.openjdk.core.version>0.9</org.openjdk.core.version>
        <spring-boot-starter-security.version>2.3.10.RELEASE</spring-boot-starter-security.version>
        <com.alibaba.otter.version>1.1.5</com.alibaba.otter.version>
        <gson.version>2.8.2</gson.version>
        <cglib.version>3.3.0</cglib.version>
        <javax.jms-api.version>2.0.1</javax.jms-api.version>
        <xxl-job-core.version>2.3.3</xxl-job-core.version>
        <org.apache.lucene.version>4.0.0</org.apache.lucene.version>
        <org.apache.commons.version>3.12.0</org.apache.commons.version>
        <org.apache.rocketmq.version>4.9.0</org.apache.rocketmq.version>
        <com.upex.commons.version>2.8.2</com.upex.commons.version>
        <upex-bill-facade.version>1.5.6.5</upex-bill-facade.version>
        <upex-ticker-facade.version>2.1.9</upex-ticker-facade.version>
        <com.upex.commons.redis.version>2.6.4.27</com.upex.commons.redis.version>
        <com.upex.commons.reactive.feign.version>1.4.0</com.upex.commons.reactive.feign.version>
        <com.upex.commons.datasource.version>2.6.13</com.upex.commons.datasource.version>
        <com.upex.financial.version>1.1.25</com.upex.financial.version>
        <com.upex.mixcontract.version>3.9.0.7</com.upex.mixcontract.version>
        <com.upex.datacenter.version>1.1.3</com.upex.datacenter.version>
        <org.projectlombok.version>1.18.20</org.projectlombok.version>
        <org.slf4j.version>1.7.30</org.slf4j.version>
        <upex-stream-dto.version>1.4.5</upex-stream-dto.version>
        <mvel2.version>2.5.0.Final</mvel2.version>
        <redisson.version>3.20.0</redisson.version>
        <upex.log.version>2.8.2</upex.log.version>
        <upex-unified-account-facade.version>1.0.21</upex-unified-account-facade.version>
        <upex-rpc-client.version>1.3.0</upex-rpc-client.version>
        <upex-data-feature-facade.version>1.3.0</upex-data-feature-facade.version>
        <upex-data-statistics-facade.version>2.0.83</upex-data-statistics-facade.version>
        <com.upex.convert.version>1.1.4</com.upex.convert.version>
        <com.upex.kline.version>1.1.8</com.upex.kline.version>
        <upex.base.dto.version>1.1.8</upex.base.dto.version>
        <bcprov-jdk15on.version>1.64</bcprov-jdk15on.version>
        <crypto.eddsa.version>0.3.0</crypto.eddsa.version>
        <okhttp.version>4.12.0</okhttp.version>
        <binance.common.version>1.1.0</binance.common.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bcprov-jdk15on.version}</version>
            </dependency>
            <dependency>
                <groupId>net.i2p.crypto</groupId>
                <artifactId>eddsa</artifactId>
                <version>${crypto.eddsa.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.binance</groupId>
                <artifactId>binance-common</artifactId>
                <version>${binance.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.upex.base</groupId>
                <artifactId>upex-base-dto</artifactId>
                <version>${upex.base.dto.version}</version>
            </dependency>
            <dependency>
                <groupId>com.upex.data.statistics</groupId>
                <artifactId>upex-data-statistics-facade</artifactId>
                <version>${upex-data-statistics-facade.version}</version>
            </dependency>
            <!--        三方基础包依赖-->
            <!--            <dependency>-->
            <!--                <groupId>com.upex.commons</groupId>-->
            <!--                <artifactId>upex-parent</artifactId>-->
            <!--                <version>${upex.commons.parent.version}</version>-->
            <!--                <type>pom</type>-->
            <!--                <scope>import</scope>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.upex.data.feature</groupId>
                <artifactId>upex-data-feature-facade</artifactId>
                <version>${upex-data-feature-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>org.openjdk.jol</groupId>
                <artifactId>jol-core</artifactId>
                <version>${org.openjdk.core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-security</artifactId>
                <version>${spring-boot-starter-security.version}</version>
            </dependency>
            <dependency>
                <artifactId>upex-stream-dto</artifactId>
                <groupId>com.upex.stream</groupId>
                <version>${upex-stream-dto.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.otter</groupId>
                <artifactId>canal.server</artifactId>
                <version>${com.alibaba.otter.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>netty-all</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.otter</groupId>
                <artifactId>canal.client</artifactId>
                <version>${com.alibaba.otter.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>netty-all</artifactId>
                        <groupId>io.netty</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb-version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb-version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-core</artifactId>
                <version>${jaxb-version}</version>
            </dependency>
            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>activation</artifactId>
                <version>${activation-version}</version>
            </dependency>
            <dependency>
                <groupId>javax.jms</groupId>
                <artifactId>javax.jms-api</artifactId>
                <version>${javax.jms-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-core</artifactId>
                <version>${org.apache.lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${org.apache.commons.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${org.apache.rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-common</artifactId>
                <version>${org.apache.rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-core</artifactId>
                <version>${metrics-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka-clients.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${org.projectlombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${org.slf4j.version}</version>
            </dependency>

            <!--        二方基础包依赖-->
            <dependency>
                <groupId>com.upex.user</groupId>
                <artifactId>upex-user-facade</artifactId>
                <version>${upex.user.facade.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-log4j2</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.upex.sparse</groupId>
                <artifactId>upex-data-sparse-facade</artifactId>
                <version>${upex.data.sparse.facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.upex.bill</groupId>
                <artifactId>upex-bill-facade</artifactId>
                <version>${upex-bill-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.upex.config</groupId>
                <artifactId>upex-config-facade</artifactId>
                <version>${upex-config-facade-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-common-utils</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.upex.stream</groupId>
                <artifactId>upex-stream-facade</artifactId>
                <version>${upex-stream-facade-version}</version>
            </dependency>
            <!--获取币种的汇率-->
            <dependency>
                <groupId>com.upex</groupId>
                <artifactId>upex-ticker-facade</artifactId>
                <version>${upex-ticker-facade.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>apollo-client</artifactId>
                        <groupId>com.ctrip.framework.apollo</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.projectreactor.netty</groupId>
                <artifactId>reactor-netty</artifactId>
                <version>0.9.19-upex-001.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.upex.commons</groupId>
                <artifactId>upex-reactive-feign</artifactId>
                <version>${com.upex.commons.reactive.feign.version}</version>
            </dependency>
            <!--otc业务包-->
            <dependency>
                <groupId>com.upex.otc</groupId>
                <artifactId>upex-otc-facade</artifactId>
                <version>${upex.otc.facade.version}</version>
            </dependency>
            <!--？-->
            <dependency>
                <groupId>com.upex.assets</groupId>
                <artifactId>upex-assets-facade</artifactId>
                <version>${upex.assets.facade.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-common-literal</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.commons</groupId>
                        <artifactId>upex-rpc-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--合约-->
            <dependency>
                <groupId>com.upex.contract</groupId>
                <artifactId>upex-contract-entrance-feign2</artifactId>
                <version>${upex.contract.entrance.feign2.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ctrip.framework.apollo</groupId>
                        <artifactId>apollo-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.upex.mixcontract</groupId>
                <artifactId>mc-common-repo</artifactId>
                <version>${mc-common-version}</version>
            </dependency>
            <dependency>
                <groupId>com.upex.mixcontract</groupId>
                <artifactId>mc-common-framework</artifactId>
                <version>${mc-common-version}</version>
            </dependency>
            <dependency>
                <groupId>com.upex.mixcontract</groupId>
                <artifactId>mc-common-domain</artifactId>
                <version>${com.upex.mixcontract.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mc-common-repo</artifactId>
                        <groupId>com.upex.mixcontract</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.upex.mixcontract</groupId>
                <artifactId>mc-common-literal</artifactId>
                <version>${com.upex.mixcontract.version}</version>
            </dependency>
            <dependency>
                <groupId>com.upex.mixcontract</groupId>
                <artifactId>mc-common-utils</artifactId>
                <version>${com.upex.mixcontract.version}</version>
            </dependency>
            <dependency>
                <groupId>com.upex.mixcontract</groupId>
                <artifactId>mc-process-facade</artifactId>
                <version>${upex.mixcontract.mc.process.facade.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.upex.commons</groupId>
                        <artifactId>upex-datasource</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>upex-data-statistics-facade</artifactId>
                        <groupId>com.upex.data.statistics</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>upex-websocket-facade</artifactId>
                        <groupId>com.upex.websocket</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mc-common-domain</artifactId>
                        <groupId>com.upex.mixcontract</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mc-common-framework</artifactId>
                        <groupId>com.upex.mixcontract</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mc-common-literal</artifactId>
                        <groupId>com.upex.mixcontract</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mc-common-repo</artifactId>
                        <groupId>com.upex.mixcontract</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--？-->
            <dependency>
                <groupId>com.upex.swap</groupId>
                <artifactId>upex-swap-facade</artifactId>
                <version>${upex.swap.facade.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-common-utils</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.assets</groupId>
                        <artifactId>upex-assets-facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-common-literal</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--钱包-->
            <dependency>
                <groupId>com.upex.newwallet</groupId>
                <artifactId>upex-newwallet-facade</artifactId>
                <version>${upex.newwallet.facade.version}</version>
            </dependency>
            <!--风控-->
            <dependency>
                <groupId>com.upex.data.risk</groupId>
                <artifactId>upex-data-risk-facade</artifactId>
                <version>${upex.data.risk.facade.version}</version>
            </dependency>
            <!-- 杠杆 -->
            <dependency>
                <groupId>com.upex.margin</groupId>
                <artifactId>upex-margin-facade</artifactId>
                <version>${upex.margin.facade.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.upex.assets</groupId>
                        <artifactId>upex-assets-facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-common-literal</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-common-utils</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>upex-stream-dto</artifactId>
                        <groupId>com.upex.stream</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--理财-->
            <dependency>
                <groupId>com.upex.financial</groupId>
                <artifactId>upex-financial-dto</artifactId>
                <version>${com.upex.financial.version}</version>
            </dependency>
            <dependency>
                <groupId>com.upex.financial</groupId>
                <artifactId>upex-financial-facade</artifactId>
                <version>${com.upex.financial.version}</version>
            </dependency>
            <!--数据中心-->
            <dependency>
                <groupId>com.upex.datacenter</groupId>
                <artifactId>upex-datacenter-facade</artifactId>
                <version>${com.upex.datacenter.version}</version>
            </dependency>
            <!--法币-->
            <dependency>
                <groupId>com.upex.fiat.order.center</groupId>
                <artifactId>upex-fiat-order-center-facade</artifactId>
                <version>${upex-fiat-order-center-facade.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>upex-fiat-common</artifactId>
                        <groupId>com.upex.fiat.common</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.upex.fiat.payment.center</groupId>
                <artifactId>upex-fiat-payment-center-facade</artifactId>
                <version>${upex.fiat.payment.center.facade.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>upex-fiat-common</artifactId>
                        <groupId>com.upex.fiat.common</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--代理商-->
            <dependency>
                <artifactId>upex-broker-facade</artifactId>
                <groupId>com.upex.broker</groupId>
                <version>${upex.broker.facade.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.upex.datacenter</groupId>
                        <artifactId>upex-datacenter-facade</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--现货-->
            <dependency>
                <groupId>com.upex.spot</groupId>
                <artifactId>upex-spot-facade</artifactId>
                <version>${upex.spot.facade.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.upex.commons</groupId>
                        <artifactId>upex-datasource</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-common-utils</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.commons</groupId>
                        <artifactId>upex-loadbalance</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>upex-newwallet-dto</artifactId>
                        <groupId>com.upex.newwallet</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.stream</groupId>
                        <artifactId>upex-stream-facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.assets</groupId>
                        <artifactId>upex-assets-facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-common-literal</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.stream</groupId>
                        <artifactId>upex-stream-facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>HdrHistogram</artifactId>
                        <groupId>org.hdrhistogram</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!--        一方基础包依赖-->
            <dependency>
                <groupId>com.upex.bill</groupId>
                <artifactId>upex-reconciliation-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.upex.bill</groupId>
                <artifactId>upex-reconciliation-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.upex.unified.account</groupId>
                <artifactId>upex-unified-account-facade</artifactId>
                <version>${upex-unified-account-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.upex.commons</groupId>
                <artifactId>upex-rpc-client</artifactId>
                <version>${upex-rpc-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.upex.convert</groupId>
                <artifactId>upex-convert-facade</artifactId>
                <version>${com.upex.convert.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-common-literal</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-common-utils</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-match-facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>upex-user-dto</artifactId>
                        <groupId>com.upex.user</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-log4j2</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.upex.kline</groupId>
                <artifactId>upex-kline-feign</artifactId>
                <version>${com.upex.kline.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-common-literal</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-common-utils</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.upex.mixcontract</groupId>
                        <artifactId>mc-match-facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>upex-user-dto</artifactId>
                        <groupId>com.upex.user</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-log4j2</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
