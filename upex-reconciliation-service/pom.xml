<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.upex.bill</groupId>
        <artifactId>upex-reconciliation</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>upex-reconciliation-service</artifactId>
    <name>upex-reconciliation-service</name>
    <dependencies>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>
        <dependency>
            <groupId>net.i2p.crypto</groupId>
            <artifactId>eddsa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.binance</groupId>
            <artifactId>binance-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.base</groupId>
            <artifactId>upex-base-dto</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.upex.config</groupId>
                    <artifactId>upex-config-dto</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.strategy</groupId>
                    <artifactId>upex-strategy-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.strategy</groupId>
                    <artifactId>upex-strategy-platform-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.spot</groupId>
                    <artifactId>upex-spot-dto</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.commons</groupId>
                    <artifactId>upex-wormholes</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.assets</groupId>
                    <artifactId>upex-assets-dto</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.strategy</groupId>
                    <artifactId>strategy-common-util</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.otc</groupId>
                    <artifactId>upex-c2c-dto</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.newwallet</groupId>
                    <artifactId>upex-newwallet-dto</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.data.risk</groupId>
                    <artifactId>upex-data-risk-dto</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.commons</groupId>
                    <artifactId>upex-rpc-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.user</groupId>
                    <artifactId>upex-user-dto</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.data.feature</groupId>
            <artifactId>upex-data-feature-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mvel</groupId>
            <artifactId>mvel2</artifactId>
            <version>${mvel2.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.otter</groupId>
            <artifactId>canal.server</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-core</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.otter</groupId>
            <artifactId>canal.client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-core</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.openjdk.jol</groupId>
            <artifactId>jol-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>io.dropwizard.metrics</groupId>
            <artifactId>metrics-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>${freemarker.version}</version>
        </dependency>
        <dependency>
            <groupId>com.upex.commons</groupId>
            <artifactId>upex-rpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.config</groupId>
            <artifactId>upex-config-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.mixcontract</groupId>
            <artifactId>mc-common-literal</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.commons</groupId>
            <artifactId>upex-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.spot</groupId>
            <artifactId>upex-spot-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>upex-user-dto</artifactId>
                    <groupId>com.upex.user</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-datacenter-facade</artifactId>
                    <groupId>com.upex.datacenter</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-log</artifactId>
                    <groupId>com.upex.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-rpc-client</artifactId>
                    <groupId>com.upex.commons</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.otc</groupId>
                    <artifactId>upex-c2c-dto</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.commons</groupId>
            <artifactId>upex-reactive-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.bill</groupId>
            <artifactId>upex-reconciliation-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.mixcontract</groupId>
            <artifactId>mc-common-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.contract</groupId>
            <artifactId>upex-contract-entrance-feign2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.mixcontract</groupId>
            <artifactId>mc-process-facade</artifactId>
            <version>${upex.mixcontract.mc.process.facade.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.upex.commons</groupId>
                    <artifactId>upex-datasource</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.datacenter</groupId>
                    <artifactId>upex-datacenter-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.assets</groupId>
                    <artifactId>upex-assets-facade</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-data-statistics-facade</artifactId>
                    <groupId>com.upex.data.statistics</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-websocket-facade</artifactId>
                    <groupId>com.upex.websocket</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-stream-dto</artifactId>
                    <groupId>com.upex.stream</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-data-sparse-facade</artifactId>
                    <groupId>com.upex.sparse</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-rpc-common</artifactId>
                    <groupId>com.upex.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-rpc-client</artifactId>
                    <groupId>com.upex.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.swap</groupId>
            <artifactId>upex-swap-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.config</groupId>
            <artifactId>upex-config-facade</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.upex.spot</groupId>
                    <artifactId>upex-spot-dto</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.swap</groupId>
            <artifactId>upex-swap-facade</artifactId>
        </dependency>
        <!--钱包-->
        <dependency>
            <groupId>com.upex.newwallet</groupId>
            <artifactId>upex-newwallet-facade</artifactId>
        </dependency>
        <!--风控-->
        <dependency>
            <groupId>com.upex.data.risk</groupId>
            <artifactId>upex-data-risk-facade</artifactId>
        </dependency>
        <dependency>
            <artifactId>upex-stream-dto</artifactId>
            <groupId>com.upex.stream</groupId>
        </dependency>
        <dependency>
            <groupId>com.upex.bill</groupId>
            <artifactId>upex-bill-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.mixcontract</groupId>
            <artifactId>mc-common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.user</groupId>
            <artifactId>upex-user-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>upex-rpc-common</artifactId>
                    <groupId>com.upex.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>upex-broker-facade</artifactId>
            <groupId>com.upex.broker</groupId>
            <exclusions>
                <exclusion>
                    <groupId>com.upex.datacenter</groupId>
                    <artifactId>upex-datacenter-facade</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 法币 -->
        <dependency>
            <groupId>com.upex.fiat.payment.center</groupId>
            <artifactId>upex-fiat-payment-center-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>upex-fiat-common</artifactId>
                    <groupId>com.upex.fiat.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.fiat.order.center</groupId>
            <artifactId>upex-fiat-order-center-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>upex-fiat-common</artifactId>
                    <groupId>com.upex.fiat.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-fiat-payment-center-dto</artifactId>
                    <groupId>com.upex.fiat.payment.center</groupId>
                </exclusion>
            </exclusions>

        </dependency>
        <dependency>
            <groupId>com.upex.mixcontract</groupId>
            <artifactId>mc-common-framework</artifactId>
        </dependency>
        <!-- 杠杆 -->
        <dependency>
            <groupId>com.upex.margin</groupId>
            <artifactId>upex-margin-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>upex-rpc-client</artifactId>
                    <groupId>com.upex.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.financial</groupId>
            <artifactId>upex-financial-dto</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>upex-user-dto</artifactId>
                    <groupId>com.upex.user</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.otc</groupId>
            <artifactId>upex-otc-facade</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.upex.data.statistics</groupId>
                    <artifactId>upex-data-statistics-facade</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-fiat-common</artifactId>
                    <groupId>com.upex.fiat.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.assets</groupId>
            <artifactId>upex-assets-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mc-common-utils</artifactId>
                    <groupId>com.upex.mixcontract</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.commons</groupId>
                    <artifactId>upex-rpc-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 根据币种id查询交易对 -->
        <dependency>
            <groupId>com.upex.config</groupId>
            <artifactId>upex-config-facade</artifactId>
        </dependency>
        <!--获取币种的汇率-->
        <dependency>
            <groupId>com.upex</groupId>
            <artifactId>upex-ticker-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.financial</groupId>
            <artifactId>upex-financial-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mc-common-utils</artifactId>
                    <groupId>com.upex.mixcontract</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.mixcontract</groupId>
            <artifactId>mc-common-repo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.commons</groupId>
            <artifactId>upex-datasource</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.datacenter</groupId>
            <artifactId>upex-datacenter-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.otc</groupId>
            <artifactId>upex-fund-facade</artifactId>
            <version>${upex.fund.facade.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-bill-facade</artifactId>
                    <groupId>com.upex.bill</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.assets</groupId>
                    <artifactId>upex-assets-facade</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-data-statistics-facade</artifactId>
                    <groupId>com.upex.data.statistics</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-user-dto</artifactId>
                    <groupId>com.upex.user</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- redisson -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <!--        杠杆-->
        <dependency>
            <groupId>com.upex.margin</groupId>
            <artifactId>upex-margin-facade</artifactId>
            <version>${upex.margin.facade.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.upex.assets</groupId>
                    <artifactId>upex-assets-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.mixcontract</groupId>
                    <artifactId>mc-common-literal</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.mixcontract</groupId>
                    <artifactId>mc-common-utils</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-stream-dto</artifactId>
                    <groupId>com.upex.stream</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.bill</groupId>
                    <artifactId>upex-bill-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.upex.spot</groupId>
                    <artifactId>upex-spot-facade</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>upex-rpc-client</artifactId>
                    <groupId>com.upex.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>upex-log</artifactId>
            <groupId>com.upex.commons</groupId>
        </dependency>
        <dependency>
            <groupId>com.upex.unified.account</groupId>
            <artifactId>upex-unified-account-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.commons</groupId>
            <artifactId>upex-rpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.upex.convert</groupId>
            <artifactId>upex-convert-facade</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.upex.mixcontract</groupId>
                    <artifactId>mc-match-facade</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.upex.kline</groupId>
            <artifactId>upex-kline-feign</artifactId>
        </dependency>
    </dependencies>
</project>
