<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillDelayAccountMapper">

    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillDelayAccount">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="checkOkTime" column="check_ok_time" jdbcType="TIMESTAMP"/>
            <result property="businessLine" column="business_line" jdbcType="TINYINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="bizId" column="biz_id" jdbcType="VARCHAR"/>
            <result property="symbol" column="symbol" jdbcType="VARCHAR"/>
            <result property="amount" column="amount" jdbcType="DECIMAL"/>
            <result property="bizTime" column="biz_time" jdbcType="TIMESTAMP"/>
            <result property="dataExt" column="data_ext" jdbcType="VARCHAR"/>
            <result property="activeFlag" column="active_flag" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        check_ok_time,
        business_line,
        user_id,
        biz_id,
        symbol,
        amount,
        biz_time,
        data_ext,
        active_flag,
        create_time,
        update_time
    </sql>

    <sql id="batch_insert_value">
        #{record.checkOkTime,jdbcType=TIMESTAMP},
        #{record.businessLine,jdbcType=TINYINT},
        #{record.userId,jdbcType=BIGINT},
        #{record.bizId,jdbcType=VARCHAR},
        #{record.symbol,jdbcType=VARCHAR},
        #{record.amount,jdbcType=DECIMAL},
        #{record.bizTime,jdbcType=TIMESTAMP},
        #{record.dataExt,jdbcType=VARCHAR},
        #{record.activeFlag,jdbcType=TINYINT},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP}
    </sql>

    <sql id="batch_exists_value">
        #{record.businessLine,jdbcType=TINYINT},
        #{record.userId,jdbcType=BIGINT},
        #{record.bizId,jdbcType=VARCHAR}
    </sql>

    <sql id="Table_Name">
        bill_delay_account
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.upex.reconciliation.service.dao.entity.BillDelayAccount" useGeneratedKeys="true">
        insert into <include refid="Table_Name"/> (
            check_ok_time,
            business_line,
            user_id,
            biz_id,
            symbol,
            amount,
            biz_time,
            data_ext,
            active_flag,
            create_time,
            update_time
        )
        values
        <foreach collection="list" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>
    
    <select id="selectDelayAccountExistsList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where (business_line,user_id,biz_id) in
        <foreach collection="list" item="record" index="index" open="(" separator="," close=")">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_exists_value"/>
            </trim>
        </foreach>
    </select>

    <select id="selectHistoryDelayAccountList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where user_id = #{userId}
        and active_flag = #{activeFlag}
        and biz_time &lt; check_ok_time
        and check_ok_time &lt; create_time
        limit 1
    </select>


    <update id="updateDelayAccount" parameterType="com.upex.reconciliation.service.dao.entity.BillDelayAccount">
        update <include refid="Table_Name"/>
        set
            active_flag =  #{activeFlag,jdbcType=TINYINT},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
        and user_id =  #{userId,jdbcType=BIGINT}
    </update>
    <update id="updateDelayAccountByTime">
        update <include refid="Table_Name"/>
        set
            active_flag =  #{activeFlag,jdbcType=TINYINT},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where check_ok_time <![CDATA[ <= ]]> #{checkOkDate,jdbcType=TIMESTAMP}
    </update>

    <delete id="deleteByLtCheckTime">
        delete from
        <include refid="Table_Name"/>
        where check_ok_time &lt; #{checkTime}
        and business_line = #{accountType}
        and active_flag = 1
        limit #{batchSize}
    </delete>
</mapper>
