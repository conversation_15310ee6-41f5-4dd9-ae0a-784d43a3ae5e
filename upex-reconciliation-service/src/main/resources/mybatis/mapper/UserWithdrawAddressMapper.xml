<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.upex.reconciliation.service.dao.mapper.cex.UserWithdrawAddressMapper">

    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.cex.entity.UserWithdrawAddress">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="network" property="network" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="coin_id" property="coinId" jdbcType="INTEGER"/>
        <result column="coin_name" property="coinName" jdbcType="VARCHAR"/>
        <result column="chain_coinid" property="chainCoinid" jdbcType="INTEGER"/>
        <result column="bg_uid" property="bgUid" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,  network, address,
        coin_id,coin_name,chain_coinid, bg_uid, create_time, update_time, version
    </sql>

    <!-- 插入 -->
    <insert id="insert">
        INSERT INTO user_withdraw_address (
        network, address,
        coin_id,coin_name,chain_coinid, bg_uid, create_time, update_time, version
        ) VALUES (
         #{network}, #{address},
        #{coinId},#{coinName}, #{chainCoinid}, #{bgUid}, #{createTime}, #{updateTime}, #{version}
        )
    </insert>


    <!-- 根据地址、币种、链ID查询 -->
    <select id="selectByAddress" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM user_withdraw_address
        WHERE address = #{address}
            and coin_name = #{coinName}
            and network = #{network}
    </select>

    <!-- 根据用户ID和币种查询 -->
    <select id="selectByBgUidAndCoin" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM user_withdraw_address
        WHERE bg_uid = #{bgUid}
        AND coin_name = #{coinName}
    </select>

</mapper>
