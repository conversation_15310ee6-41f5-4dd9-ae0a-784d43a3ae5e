<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillCoinUserPropertySnapshotMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillCoinUserProperty">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="coin_id" property="coinId" jdbcType="INTEGER"/>
        <result column="check_time" property="checkTime" jdbcType="TIMESTAMP"/>
        <result column="change_prop1" property="changeProp1" jdbcType="DECIMAL" />
        <result column="prop1" property="prop1" jdbcType="DECIMAL" />
        <result column="change_prop2" property="changeProp2" jdbcType="DECIMAL" />
        <result column="prop2" property="prop2" jdbcType="DECIMAL" />
        <result column="change_prop3" property="changeProp3" jdbcType="DECIMAL" />
        <result column="prop3" property="prop3" jdbcType="DECIMAL" />
        <result column="change_prop4" property="changeProp4" jdbcType="DECIMAL" />
        <result column="prop4" property="prop4" jdbcType="DECIMAL" />
        <result column="change_prop5" property="changeProp5" jdbcType="DECIMAL" />
        <result column="prop5" property="prop5" jdbcType="DECIMAL" />
        <result column="change_prop6" property="changeProp6" jdbcType="DECIMAL" />
        <result column="prop6" property="prop6" jdbcType="DECIMAL" />
        <result column="change_prop7" property="changeProp7" jdbcType="DECIMAL" />
        <result column="prop7" property="prop7" jdbcType="DECIMAL" />
        <result column="change_prop8" property="changeProp8" jdbcType="DECIMAL" />
        <result column="prop8" property="prop8" jdbcType="DECIMAL" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="initial_time" property="initialTime" jdbcType="TIMESTAMP"/>
        <result column="params" property="params" jdbcType="VARCHAR"/>
        <result column="sprop1" property="sprop1" jdbcType="DECIMAL"/>
        <result column="sprop2" property="sprop2" jdbcType="DECIMAL"/>
        <result column="sprop3" property="sprop3" jdbcType="DECIMAL"/>
        <result column="sprop4" property="sprop4" jdbcType="DECIMAL"/>
        <result column="sprop5" property="sprop5" jdbcType="DECIMAL"/>
        <result column="sprop6" property="sprop6" jdbcType="DECIMAL"/>
        <result column="sprop7" property="sprop7" jdbcType="DECIMAL"/>
        <result column="sprop8" property="sprop8" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Table_Name">
        bill_coin_user_property_snapshot_${accountType}_${accountParam}
    </sql>


    <sql id="Base_Column_List">
        id, user_id, coin_id, check_time, change_prop1, prop1, change_prop2, prop2, change_prop3,
        prop3, change_prop4, prop4, change_prop5, prop5, change_prop6, prop6, change_prop7, prop7, change_prop8, prop8, create_time, update_time,initial_time,params,
        sprop1, sprop2, sprop3, sprop4, sprop5, sprop6, sprop7, sprop8
    </sql>

    <sql id="batch_insert_value">
        #{record.userId,jdbcType=BIGINT}, #{record.coinId,jdbcType=INTEGER},
        #{record.checkTime,jdbcType=TIMESTAMP}, #{record.changeProp1,jdbcType=DECIMAL},
        #{record.prop1,jdbcType=DECIMAL},
        #{record.changeProp2,jdbcType=DECIMAL}, #{record.prop2,jdbcType=DECIMAL},
        #{record.changeProp3,jdbcType=DECIMAL},
        #{record.prop3,jdbcType=DECIMAL}, #{record.changeProp4,jdbcType=DECIMAL}, #{record.prop4,jdbcType=DECIMAL},
        #{record.changeProp5,jdbcType=DECIMAL}, #{record.prop5,jdbcType=DECIMAL},
        #{record.changeProp6,jdbcType=DECIMAL}, #{record.prop6,jdbcType=DECIMAL},
        #{record.changeProp7,jdbcType=DECIMAL}, #{record.prop7,jdbcType=DECIMAL},
        #{record.changeProp8,jdbcType=DECIMAL}, #{record.prop8,jdbcType=DECIMAL},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP},
        #{record.initialTime,jdbcType=TIMESTAMP},
        #{record.params,jdbcType=VARCHAR},
        #{record.sprop1,jdbcType=DECIMAL},
        #{record.sprop2,jdbcType=DECIMAL},
        #{record.sprop3,jdbcType=DECIMAL},
        #{record.sprop4,jdbcType=DECIMAL},
        #{record.sprop5,jdbcType=DECIMAL},
        #{record.sprop6,jdbcType=DECIMAL},
        #{record.sprop7,jdbcType=DECIMAL},
        #{record.sprop8,jdbcType=DECIMAL}
    </sql>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert  into
        <include refid="Table_Name"/>
        (user_id, coin_id,
        check_time, change_prop1, prop1,
        change_prop2, prop2, change_prop3,
        prop3, change_prop4, prop4,
        change_prop5, prop5,
        change_prop6, prop6, change_prop7, prop7, change_prop8, prop8,
        create_time,
        update_time,initial_time,params,
        sprop1, sprop2, sprop3, sprop4, sprop5, sprop6, sprop7, sprop8)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where id = #{id}
    </select>

    <update id="updateById" parameterType="com.upex.reconciliation.service.dao.entity.BillCoinUserProperty">
        update
        <include refid="Table_Name"/>
        set
        prop1 = #{data.prop1,jdbcType=DECIMAL},
        prop2 = #{data.prop2,jdbcType=DECIMAL},
        prop3 = #{data.prop3,jdbcType=DECIMAL},
        prop4 = #{data.prop4,jdbcType=DECIMAL},
        prop5 = #{data.prop5,jdbcType=DECIMAL},
        prop6 = #{data.prop6,jdbcType=DECIMAL},
        prop7 = #{data.prop7,jdbcType=DECIMAL},
        prop8 = #{data.prop8,jdbcType=DECIMAL},
        params = #{data.params,jdbcType=VARCHAR},
        sprop1 = #{data.sprop1,jdbcType=DECIMAL},
        sprop2 = #{data.sprop2,jdbcType=DECIMAL},
        sprop3 = #{data.sprop3,jdbcType=DECIMAL},
        sprop4 = #{data.sprop4,jdbcType=DECIMAL},
        sprop5 = #{data.sprop5,jdbcType=DECIMAL},
        sprop6 = #{data.sprop6,jdbcType=DECIMAL},
        sprop7 = #{data.sprop7,jdbcType=DECIMAL},
        sprop8 = #{data.sprop8,jdbcType=DECIMAL},
        update_time = now()
        where id = #{data.id,jdbcType=BIGINT}
    </update>

    <delete id="deleteByCheckTime">
        delete from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        limit #{pageSize}
    </delete>

    <select id="selectMaxCheckTime" resultMap="BaseResultMap">
        select
        max(check_time) check_time,coin_id,user_id
        from
        <include refid="Table_Name"/>
        force index(idx_user_coin_check_time)
        where user_id= #{userId}
        group by coin_id
    </select>


    <select id="selectByUserIdsAndCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime} and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>


    <insert id="batchInsertOrUpdate" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="Table_Name"/>
        (user_id, coin_id,
        check_time, change_prop1, prop1,
        change_prop2, prop2, change_prop3,
        prop3, change_prop4, prop4,
        change_prop5, prop5,
        change_prop6, prop6, change_prop7, prop7, change_prop8, prop8,
        create_time,
        update_time,initial_time,params,
        sprop1, sprop2, sprop3, sprop4, sprop5, sprop6, sprop7, sprop8)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>

    </insert>

    <update id="updateCheckTimeById" parameterType="object">
        update
        <include refid="Table_Name"/>
        set
        check_time = #{checkTime},
        update_time= #{updateTime}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="selectAllAssetsTiming" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time=#{checkTime}
    </select>

    <delete id="batchDelete">
        delete from
        <include refid="Table_Name"/>
        where id >= #{beginId}
        limit #{pageSize}
    </delete>

    <select id="selectUserIds" resultType="java.lang.Long">
        select distinct
        user_id
        from bill_coin_user_property_snapshot_t2_{accountType}_${accountParam}
    </select>


    <select id="selectSysAssetsSnapShotByTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id = #{userId} and check_time = #{checkTime}
    </select>

    <select id="selectSingleUser" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id=#{userId}
        AND check_time=#{checkTime}
        AND coin_id=#{coinId}
    </select>

    <select id="selectInfosByCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where initial_time &lt;= #{checkTime}
        AND user_id=#{userId}
        AND check_time &gt;= #{checkTime}
    </select>

    <select id="selectTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id = #{userId} and check_time &lt;=#{checkTime} order by check_time desc limit 1
    </select>
    <delete id="deleteInitialTimeAfterRecord" parameterType="object">
        delete
        from
        <include refid="Table_Name"/>
        where initial_time &gt; #{resetCheckTime}
    </delete>
    <update id="updateInitAndCheckBetweenRecord" parameterType="object">
        update
        <include refid="Table_Name"/>
        set check_time=#{lastCheckTime}
        where user_id=#{userId}
        and initial_time &lt;= #{resetCheckTime}
        and check_time &gt;= #{resetCheckTime}
    </update>

    <select id="selectRecordsByUserIdAndCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id=#{userId} AND initial_time = #{checkTime}
    </select>

    <select id="selectRecordLimitByUserCoin" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_snapshot_t2_{accountType}_${accountParam}
        force index(idx_user_coin_check_time)
        where user_id =#{userId}
        AND coin_id =#{coinId}
        AND check_time &lt;=#{checkTime}
        order by check_time desc limit 1
    </select>

    <select id="selectCheckTimeOverInitLimitByUerCoin" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id =#{userId}
        AND coin_id =#{coinId}
        AND check_time &gt;= #{checkTime}
        AND initial_time &lt;= #{checkTime}
    </select>

    <select id="selectCoinsByUserAndTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id =#{userId}
        AND check_time = #{checkTime}
    </select>

    <!--    <select id="selectUserCoinHistoryRecord" parameterType="com.upex.bill.domain.QueryUserDTO" resultType="java.lang.Long">-->
    <!--        select id-->
    <!--        from bill_coin_user_property_${accountType}_${accountParam}-->
    <!--        where user_id =#{userId}-->
    <!--            AND coin_id =#{coinId}-->
    <!--            and check_time >= #{startTime}-->
    <!--            and check_time &lt;#{endTime}-->
    <!--        order by id asc-->
    <!--    </select>-->
    <select id="selectAssetListByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id = #{userId} and check_time = (select check_time
        from
        <include refid="Table_Name"/>
        where user_id = #{userId} order by check_time desc limit 1)
    </select>
    <delete id="deleteUserCoinHistoryRecord" parameterType="object">
        delete
        from
        <include refid="Table_Name"/>
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectRangeCheckTimeRecordPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time >= #{startTime}
        and check_time &lt; #{endTime}
        and id > #{minId}
        order by id asc
        limit #{pageSize}
    </select>

    <select id="selectCheckTimeRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        and id > #{beginId}
        order by id asc
        limit #{pageSize};
    </select>

    <select id="selectCoinUserBeforeCheckTimeRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        force index(idx_user_coin_check_time)
        where check_time &lt;= #{checkTime}
        and coin_id= #{coinId}
        and user_id=#{userId}
        order by check_time desc
        limit 1;
    </select>

    <select id="selectAllCoinUserByCheckTimeAndCoinIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        and user_id=#{userId}
    </select>



    <select id="selectCoinUserLatest" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        force index(idx_user_coin_check_time)
        where coin_id= #{coinId}
        and user_id=#{userId}
        order by check_time desc
        limit 1;
    </select>


    <select id="selectCoinUserAfterCheckTimeRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time &gt;= #{checkTime}
        and coin_id= #{coinId}
        and user_id=#{userId}
        order by check_time
        limit 1;
    </select>

    <select id="selectRangeCheckTimeRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time >= #{startTime}
        and check_time &lt; #{endTime}
        order by check_time asc
    </select>


    <select id="selectUserLatestRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id = #{userId}
        and coin_id = #{coinId}
        order by check_time desc
    </select>
    <select id="selectLastUserAssetByUserId"
            resultType="com.upex.reconciliation.service.dao.entity.BillCoinUserProperty">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_snapshot_t2_{accountType}_${accountParam}
        where user_id = #{userId}
        and check_time = (select check_time from bill_coin_user_property_${accountType}_${accountParam}
        where user_id = #{userId} order by check_time desc limit 1 )
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        and coin_id = #{coinId}
        and   user_id in
        <foreach collection="uids" item="uid" open="(" separator="," close=")">
            #{uid}
        </foreach>
    </select>

    <select id="getIdByCheckTime" resultType="long">
        select max(id) from
        <include refid="Table_Name"/>
        <where>
            <if test="operation == '='">
                and check_time = #{checkTime}
            </if>
            <if test="operation == '&lt;='">
                and check_time &lt;= #{checkTime}
            </if>
        </where>
    </select>

    <delete id="deleteByMaxId">
        delete from
        <include refid="Table_Name"/>
        where id &lt; #{maxId}
        limit #{batchSize}
    </delete>

    <select id="selectCoinUserSnapshotAsset" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from <include refid="Table_Name"/> a
        right join (
        select max(id) bid from <include refid="Table_Name"/>
        where user_id = #{userId}
        and check_time &lt;= #{checkTime}
        and coin_id in
        <foreach collection="coinIds" item="coinId" open="(" separator="," close=")">
            #{coinId}
        </foreach>
        group by coin_id
        ) b on a.id = b.bid
    </select>
    <update id="updateSelectiveById">
        update <include refid="Table_Name"/> a
        <set>
            <if test="record.userId != null">user_id=#{record.userId},</if>
            <if test="record.coinId != null">coin_id=#{record.coinId},</if>
            <if test="record.checkTime != null">check_time=#{record.checkTime},</if>
            <if test="record.createTime != null">create_time=#{record.createTime},</if>
            <if test="record.updateTime != null">update_time=#{record.updateTime},</if>
            <if test="record.initialTime != null">initial_time=#{record.initialTime},</if>
            <if test="record.accountType != null">account_type=#{record.accountType},</if>
            <if test="record.accountParam != null">account_param=#{record.accountParam},</if>
            <if test="record.changeProp1 != null">change_prop1=#{record.changeProp1},</if>
            <if test="record.prop1 != null">prop1=#{record.prop1},</if>
            <if test="record.changeProp2 != null">change_prop2=#{record.changeProp2},</if>
            <if test="record.prop2 != null">prop2=#{record.prop2},</if>
            <if test="record.changeProp3 != null">change_prop3=#{record.changeProp3},</if>
            <if test="record.prop3 != null">prop3=#{record.prop3},</if>
            <if test="record.changeProp4 != null">change_prop4=#{record.changeProp4},</if>
            <if test="record.prop4 != null">prop4=#{record.prop4},</if>
            <if test="record.changeProp5 != null">change_prop5=#{record.changeProp5},</if>
            <if test="record.prop5 != null">prop5=#{record.prop5},</if>
            <if test="record.changeProp6 != null">change_prop6=#{record.changeProp6},</if>
            <if test="record.prop6 != null">prop6=#{record.prop6},</if>
            <if test="record.changeProp7 != null">change_prop7=#{record.changeProp7},</if>
            <if test="record.prop7 != null">prop7=#{record.prop7},</if>
            <if test="record.changeProp8 != null">change_prop8=#{record.changeProp8},</if>
            <if test="record.prop8 != null">prop8=#{record.prop8},</if>
            <if test="record.params != null">params=#{record.params},</if>
            <if test="record.sprop1 != null">sprop1=#{record.sprop1},</if>
            <if test="record.sprop2 != null">sprop2=#{record.sprop2},</if>
            <if test="record.sprop3 != null">sprop3=#{record.sprop3},</if>
            <if test="record.sprop4 != null">sprop4=#{record.sprop4},</if>
            <if test="record.sprop5 != null">sprop5=#{record.sprop5},</if>
            <if test="record.sprop6 != null">sprop6=#{record.sprop6},</if>
            <if test="record.sprop7 != null">sprop7=#{record.sprop7},</if>
            <if test="record.sprop8 != null">sprop8=#{record.sprop8},</if>
        </set>
        where id = #{record.id}
    </update>

    <select id="selectCoinUserSnapshotByIdSegment" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        and id &gt;= #{minId}
        and id &lt; #{maxId}
    </select>

    <select id="selectMaxId" resultType="long">
        select max(id) from <include refid="Table_Name"/> where check_time = #{checkTime}
    </select>

    <select id="selectMinId" resultType="long">
        select min(id) from <include refid="Table_Name"/> where check_time = #{checkTime}
    </select>

    <select id="selectLastUserAssetByUserCoinId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        force index(idx_user_coin_check_time)
        where user_id = #{userId}
        and coin_id = #{coinId}
        and check_time &lt; #{checkTime}
        order by check_time desc
        limit 1
    </select>

    <select id="selectCoinUserLtCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time &lt; #{checkTime}
        and coin_id= #{coinId}
        and user_id=#{userId}
    </select>

    <delete id="deleteByIds">
        delete from
        <include refid="Table_Name"/>
        where id in
        <foreach collection="coinUserIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateSpropByUserCoinCheckTime" parameterType="com.upex.reconciliation.service.dao.entity.BillCoinUserProperty">
        update
        <include refid="Table_Name"/>
        set
        sprop1 = #{record.sprop1,jdbcType=DECIMAL},
        sprop2 = #{record.sprop2,jdbcType=DECIMAL},
        sprop3 = #{record.sprop3,jdbcType=DECIMAL},
        sprop4 = #{record.sprop4,jdbcType=DECIMAL},
        sprop5 = #{record.sprop5,jdbcType=DECIMAL},
        sprop6 = #{record.sprop6,jdbcType=DECIMAL},
        sprop7 = #{record.sprop7,jdbcType=DECIMAL},
        sprop8 = #{record.sprop8,jdbcType=DECIMAL},
        update_time = now()
        where user_id = #{record.userId}
        and coin_id = #{record.coinId}
        and check_time = #{record.checkTime}
    </update>
</mapper>