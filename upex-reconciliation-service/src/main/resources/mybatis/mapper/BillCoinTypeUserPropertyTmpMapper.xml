<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillCoinTypeUserPropertyTmpMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillCoinTypeUserPropertyTmp">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="biz_type" property="bizType" jdbcType="VARCHAR"/>
        <result column="coin_id" property="coinId" jdbcType="INTEGER"/>
        <result column="check_time" property="checkTime" jdbcType="TIMESTAMP"/>
        <result column="change_prop1" property="changeProp1" jdbcType="DECIMAL"/>
        <result column="change_prop2" property="changeProp2" jdbcType="DECIMAL"/>
        <result column="change_prop3" property="changeProp3" jdbcType="DECIMAL"/>
        <result column="change_prop4" property="changeProp4" jdbcType="DECIMAL"/>
        <result column="change_prop5" property="changeProp5" jdbcType="DECIMAL"/>
        <result column="change_prop6" property="changeProp6" jdbcType="DECIMAL"/>
        <result column="change_prop7" property="changeProp7" jdbcType="DECIMAL"/>
        <result column="change_prop8" property="changeProp8" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="initial_time" property="initialTime" jdbcType="TIMESTAMP"/>
        <result column="params" property="params" jdbcType="VARCHAR"/>
        <result column="account_type" property="accountType" jdbcType="TINYINT"/>
        <result column="account_param" property="accountParam" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, coin_id, biz_type, check_time, change_prop1, change_prop2, change_prop3,
        change_prop4, change_prop5,change_prop6,change_prop7,change_prop8, create_time,
        update_time,initial_time,params,account_type,account_param
    </sql>
    <sql id="Base_Column_List_No_Id">
        user_id, coin_id, biz_type, check_time, change_prop1, change_prop2, change_prop3,
        change_prop4, change_prop5,change_prop6,change_prop7,change_prop8, create_time,
        update_time,initial_time,params,account_type,account_param
    </sql>
    <sql id="batch_insert_value">
        #{record.userId,jdbcType=BIGINT},
        #{record.coinId,jdbcType=INTEGER},
        #{record.bizType,jdbcType=VARCHAR},
        #{record.checkTime,jdbcType=TIMESTAMP},
        #{record.changeProp1,jdbcType=DECIMAL},
        #{record.changeProp2,jdbcType=DECIMAL},
        #{record.changeProp3,jdbcType=DECIMAL},
        #{record.changeProp4,jdbcType=DECIMAL},
        #{record.changeProp5,jdbcType=DECIMAL},
        #{record.changeProp6,jdbcType=DECIMAL},
        #{record.changeProp7,jdbcType=DECIMAL},
        #{record.changeProp8,jdbcType=DECIMAL},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP},
        #{record.initialTime,jdbcType=TIMESTAMP},
        #{record.params,jdbcType=VARCHAR},
        #{record.accountType,jdbcType=TINYINT},
        #{record.accountParam,jdbcType=VARCHAR}
    </sql>
    <sql id="Table_Name">
        bill_coin_type_user_property_tmp
    </sql>

    <insert id="batchInsert" parameterType="com.upex.reconciliation.service.dao.entity.BillCoinTypeUserPropertyTmp"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
        <include refid="Table_Name"/>
        (<include refid="Base_Column_List_No_Id"></include>)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <delete id="deleteByUserId">
        delete from
        <include refid="Table_Name"/>
        where account_type = #{accountType}
        and account_param = #{accountParam}
        and user_id = #{userId}
        limit #{batchSize}
    </delete>
</mapper>