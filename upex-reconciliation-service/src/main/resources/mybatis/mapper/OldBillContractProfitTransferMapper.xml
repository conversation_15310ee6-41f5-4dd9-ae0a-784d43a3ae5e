<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.OldBillContractProfitTransferMapper">

    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <id property="bizId" column="biz_id" jdbcType="BIGINT"/>
        <id property="batchNo" column="batch_no" jdbcType="BIGINT"/>
        <result property="checkOkTime" column="check_ok_time" jdbcType="TIMESTAMP"/>
        <result property="accountType" column="account_type" jdbcType="TINYINT"/>
        <result property="accountParam" column="account_param" jdbcType="VARCHAR"/>
        <result property="toAccountType" column="to_account_type" jdbcType="TINYINT"/>
        <result property="toAccountParam" column="to_account_param" jdbcType="VARCHAR"/>
        <result property="coinId" column="coin_id" jdbcType="INTEGER"/>
        <result property="transferCount" column="transfer_count" jdbcType="DECIMAL"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="sourceType" column="source_type" jdbcType="INTEGER"/>
        <result property="transferType" column="transfer_type" jdbcType="INTEGER"/>
        <result property="transferTime" column="transfer_time" jdbcType="TIMESTAMP"/>
        <result property="transferInUserId" column="transfer_in_user_id" jdbcType="BIGINT"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,biz_id,batch_no,
        check_ok_time,account_type,account_param,
        to_account_type,to_account_param,coin_id,
        transfer_count,status,source_type,
        transfer_type,transfer_time,transfer_in_user_id,
        version,create_time,update_time
    </sql>

    <sql id="Table_Name">
        <choose>
            <when test="_parameter.containsKey('tableSuffix') and tableSuffix != null and tableSuffix != ''">
                bill_contract_profit_transfer_${tableSuffix}
            </when>
            <otherwise>
                bill_contract_profit_transfer
            </otherwise>
        </choose>
    </sql>

    <sql id="batch_insert_value">
        #{record.id,jdbcType=BIGINT},
        #{record.bizId,jdbcType=BIGINT},
        #{record.batchNo,jdbcType=BIGINT},
        #{record.checkOkTime,jdbcType=TIMESTAMP},
        #{record.accountType,jdbcType=TINYINT},
        #{record.accountParam,jdbcType=VARCHAR},
        #{record.toAccountType,jdbcType=TINYINT},
        #{record.toAccountParam,jdbcType=VARCHAR},
        #{record.coinId,jdbcType=INTEGER},
        #{record.transferCount,jdbcType=DECIMAL},
        #{record.status,jdbcType=INTEGER},
        #{record.sourceType,jdbcType=INTEGER},
        #{record.transferType,jdbcType=INTEGER},
        #{record.transferTime,jdbcType=TIMESTAMP},
        #{record.transferInUserId,jdbcType=INTEGER},
        #{record.version,jdbcType=BIGINT},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP}
    </sql>

    <select id="selectListByToAccountTypeStatusAndTime" resultMap="BaseResultMap">
        select coin_id coin_id,
        sum(transfer_count) transfer_count
        from
        <include refid="Table_Name"/>
        where status in (0, 3)
        and check_ok_time <![CDATA[ < ]]> #{checkOkTime}
        and check_ok_time <![CDATA[ > ]]> #{initTime}
        and to_account_type = #{toAccountType}
        and transfer_type in
        <foreach collection="transferTypes" item="transferType" open="(" separator="," close=")">
            #{transferType}
        </foreach>
        group by coin_id
        having sum(transfer_count) != 0
    </select>

    <select id="selectListByToAccountTypeTime" resultMap="BaseResultMap">
        select coin_id coin_id,
        sum(transfer_count) transfer_count
        from
        <include refid="Table_Name"/>
        where status = 2 and transfer_time > #{checkOkTime}
        and check_ok_time <![CDATA[ < ]]> #{checkOkTime}
        and check_ok_time <![CDATA[ > ]]> #{initTime}
        and to_account_type = #{toAccountType}
        and transfer_type in
        <foreach collection="transferTypes" item="transferType" open="(" separator="," close=")">
            #{transferType}
        </foreach>
        group by coin_id
        having sum(transfer_count) != 0
    </select>


    <select id="selectDelayedTransactionByTimeAndType"
            resultType="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer">
        select to_account_type to_account_type,
        to_account_param to_account_param,
        coin_id coin_id,
        sum(transfer_count) transfer_count
        from
        <include refid="Table_Name"/>
        where status = 2 and transfer_time > #{checkOkTime}
        and check_ok_time <![CDATA[ <= ]]> #{checkOkTime}
        and transfer_type in
        <foreach collection="transferTypes" item="transferType" open="(" separator="," close=")">
            #{transferType}
        </foreach>
        group by to_account_type,
        to_account_param,
        coin_id
        having sum(transfer_count) != 0
    </select>


    <select id="selectDelayedTransactionByStatusAndTimeAndType"
            resultType="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer">
        select to_account_type to_account_type,
        to_account_param to_account_param,
        coin_id coin_id,
        sum(transfer_count) transfer_count
        from
        <include refid="Table_Name"/>
        where status in (0, 3)
        and check_ok_time <![CDATA[ <= ]]> #{checkOkTime}
        and transfer_type in
        <foreach collection="transferTypes" item="transferType" open="(" separator="," close=")">
            #{transferType}
        </foreach>
        group by to_account_type,
        to_account_param,
        coin_id
        having sum(transfer_count) != 0
    </select>


    <select id="selectEachDelayedTransactionByTimeAndType"
            resultType="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where status = 2 and transfer_time > #{checkOkTime}
        and check_ok_time <![CDATA[ <= ]]> #{checkOkTime}
        and transfer_type in
        <foreach collection="transferTypes" item="transferType" open="(" separator="," close=")">
            #{transferType}
        </foreach>
    </select>


    <select id="selectEachDelayedTransactionByStatusAndTimeAndType"
            resultType="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where status in (0, 3)
        and check_ok_time <![CDATA[ <= ]]> #{checkOkTime}
        and transfer_type in
        <foreach collection="transferTypes" item="transferType" open="(" separator="," close=")">
            #{transferType}
        </foreach>
    </select>

    <select id="selectTransferRecordsByStatusAndTime"
            resultType="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where status = #{status}
    </select>

    <select id="selectListByAccountTypeAndCheckTime"
            resultType="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where account_type = #{accountType}
        and account_param = #{accountParam}
        and check_ok_time = #{checkTime}
    </select>

    <select id="sumInitUnProfitTransfers" resultType="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer">
        select coin_id,sum(transfer_count) transfer_count
        from <include refid="Table_Name"/>
        where account_type = #{accountType}
        and account_param = #{accountParam}
        and check_ok_time &lt; #{checkTime}
        group by coin_id
    </select>
</mapper>
