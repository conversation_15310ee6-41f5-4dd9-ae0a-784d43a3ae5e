<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillCoinTypeUserPropertyMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="biz_type" property="bizType" jdbcType="VARCHAR"/>
        <result column="coin_id" property="coinId" jdbcType="INTEGER"/>
        <result column="check_time" property="checkTime" jdbcType="TIMESTAMP"/>
        <result column="change_prop1" property="changeProp1" jdbcType="DECIMAL"/>
        <result column="change_prop2" property="changeProp2" jdbcType="DECIMAL"/>
        <result column="change_prop3" property="changeProp3" jdbcType="DECIMAL"/>
        <result column="change_prop4" property="changeProp4" jdbcType="DECIMAL"/>
        <result column="change_prop5" property="changeProp5" jdbcType="DECIMAL"/>
        <result column="change_prop6" property="changeProp6" jdbcType="DECIMAL"/>
        <result column="change_prop7" property="changeProp7" jdbcType="DECIMAL"/>
        <result column="change_prop8" property="changeProp8" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="initial_time" property="initialTime" jdbcType="TIMESTAMP"/>
        <result column="params" property="params" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, coin_id, biz_type, check_time, change_prop1, change_prop2, change_prop3,
        change_prop4, change_prop5,change_prop6,change_prop7,change_prop8, create_time, update_time,initial_time,params
    </sql>

    <sql id="Table_Name">
        <choose>
            <when test="_parameter.containsKey('tableSuffix') and tableSuffix != null and tableSuffix != ''">
                bill_coin_type_user_property_${accountType}_${tableSuffix}
            </when>
            <otherwise>
                bill_coin_type_user_property_${accountType}_default
            </otherwise>
        </choose>
    </sql>

    <sql id="batch_insert_value">
        #{record.userId,jdbcType=BIGINT}, #{record.coinId,jdbcType=INTEGER},
        #{record.checkTime,jdbcType=TIMESTAMP}, #{record.bizType,jdbcType=VARCHAR},
        #{record.changeProp1,jdbcType=DECIMAL},
        #{record.changeProp2,jdbcType=DECIMAL},
        #{record.changeProp3,jdbcType=DECIMAL},
        #{record.changeProp4,jdbcType=DECIMAL},
        #{record.changeProp5,jdbcType=DECIMAL},
        #{record.changeProp6,jdbcType=DECIMAL},
        #{record.changeProp7,jdbcType=DECIMAL},
        #{record.changeProp8,jdbcType=DECIMAL},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP},
        #{record.initialTime,jdbcType=TIMESTAMP},#{record.params,jdbcType=VARCHAR}
    </sql>

    <delete id="batchDelete">
        delete from
        <include refid="Table_Name"/>
        where id >= #{beginId}
        limit #{pageSize}
    </delete>

    <insert id="batchInsert" parameterType="com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into
        <include refid="Table_Name"/>
        (user_id, coin_id, check_time,
        biz_type,
        change_prop1, change_prop2,
        change_prop3,
        change_prop4, change_prop5,
        change_prop6,change_prop7,change_prop8,
        create_time, update_time,initial_time,params
        )
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <select id="selectByUserIdsAndCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime} and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="selectUserTypeByTimeAndTypeAndCoinId" resultMap="BaseResultMap">
        select coin_id,biz_type,user_id, sum(change_prop1) as change_prop1,sum(change_prop2) as change_prop2 ,
        sum(change_prop3) as change_prop3,sum(change_prop4) as change_prop4 ,
        sum(change_prop5) as change_prop5,sum(change_prop6) as change_prop6,sum(change_prop7) as change_prop7,
        sum(change_prop8) as change_prop8
        from
        <include refid="Table_Name"/>
        where user_id=#{userId}
        and check_time &gt; #{beginTime}
        and check_time &lt;= #{endTime}
        and coin_id = #{coinId}
        and biz_type in
        <foreach collection="bizTypes" item="bizType" open="(" separator="," close=")">
            #{bizType}
        </foreach>
        group by coin_id,biz_type,user_id;
    </select>


    <select id="selectByUserAndTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id = #{userId} and check_time = #{checkTime}
    </select>



    <select id="selectByUserBeforeTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id = #{userId}
        and check_time = (select check_time
        from
        <include refid="Table_Name"/>
        where user_id = #{userId}
        and check_time &lt;= #{checkTime}
        order by check_time desc limit 1);

    </select>



    <select id="selectBetweenTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id = #{userId}
        <if test="startTime != null" >
            and check_time &gt;= #{startTime}
        </if>
        <if test="endTime != null" >
            and check_time &lt;= #{endTime}
        </if>
    </select>


    <select id="selectByUserAndCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id = #{userId} and check_time = #{checkTime}
    </select>

    <select id="selectTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id = #{userId} and check_time &lt;= #{checkTime} order by check_time desc limit 1
    </select>

    <delete id="batchDeleteCheckTime">
        delete from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
    </delete>

    <select id="listAllChange" resultMap="BaseResultMap">
        select biz_type, sum(change_prop1) as change_prop1,sum(change_prop2) as change_prop2 ,
        sum(change_prop3) as change_prop3,sum(change_prop4) as change_prop4 ,
        sum(change_prop5) as change_prop5,sum(change_prop6) as change_prop6,sum(change_prop7) as change_prop7,
        sum(change_prop8) as change_prop8
        from
        <include refid="Table_Name"/>
        where coin_id=#{coinId}
        and user_id=#{userId}
        and check_time &gt; #{startTime}
        and check_time &lt;= #{endTime}

        group by biz_type;
    </select>

    <select id="listAllChangeGroupByCoinBizType" resultMap="BaseResultMap">
        select coin_id,biz_type,user_id, sum(change_prop1) as change_prop1,sum(change_prop2) as change_prop2 ,
        sum(change_prop3) as change_prop3,sum(change_prop4) as change_prop4 ,
        sum(change_prop5) as change_prop5,sum(change_prop6) as change_prop6,sum(change_prop7) as change_prop7,
        sum(change_prop8) as change_prop8
        from
        <include refid="Table_Name"/>
        where user_id=#{userId}
        and check_time &gt; #{startTime}
        and check_time &lt;= #{endTime}

        group by coin_id,biz_type;
    </select>



    <select id="selectAllType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time=#{checkTime}
        and user_id = #{userId}
        and biz_type in
        <foreach collection="bizTypes" item="bizType" open="(" separator="," close=")">
            #{bizType}
        </foreach>
    </select>

    <delete id="deleteInitialTimeAfterRecord" parameterType="object">
        delete
        from
        <include refid="Table_Name"/>
        where check_time &gt; #{resetCheckTime}
    </delete>
    <update id="updateInitAndCheckBetweenRecord" parameterType="object">
        update
        <include refid="Table_Name"/>
        set check_time=#{resetCheckTime}
        where initial_time &lt;= #{resetCheckTime}
        and check_time &gt; #{resetCheckTime}
    </update>
    <select id="selectLastCheckTime" parameterType="object" resultType="java.util.Date">
        select check_time
        from
        <include refid="Table_Name"/>
        where user_id=#{userId}
        order by check_time desc limit 1
    </select>

    <!--    <select id="selectUserCoinTypeHistoryRecord" parameterType="com.upex.bill.domain.QueryUserDTO" resultType="java.lang.Long">-->
    <!--        select id-->
    <!--        from <include refid="Table_Name"/>-->
    <!--        where-->
    <!--            user_id =#{userId}-->
    <!--            AND coin_id =#{coinId}-->
    <!--            and biz_type= #{bizType}-->
    <!--            and check_time >=#{startTime}-->
    <!--            and check_time &lt;#{endTime}-->
    <!--        order by id asc-->
    <!--    </select>-->

    <select id="selectMinDate" resultType="java.util.Date">
        select check_time
        from
        <include refid="Table_Name"/>
        where check_time &lt; #{deleteEndTime}
        order by check_time
        limit 1
    </select>

    <delete id="deleteUserCoinTypeHistoryRecord" parameterType="object">
        delete
        from
        <include refid="Table_Name"/>
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteUserCoinTypeHistoryRecordWithSumZero" parameterType="object">
        delete
        from
        <include refid="Table_Name"/>
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and abs(change_prop1)+abs(change_prop2)+abs(change_prop3) + abs(change_prop4)+abs(change_prop5)
        +abs(prop1)+abs(prop2)+abs(prop3)+abs(prop4)+abs(prop5) = 0
    </delete>

    <select id="getBillBetweenTimeMinId" resultType="java.lang.Long">
        select id
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        and biz_type in
        <foreach collection="bizTypes" item="bizType" open="(" separator="," close=")">
            #{bizType}
        </foreach>
        order by id asc
        limit 1
    </select>

    <select id="getTableMaxId" resultType="java.lang.Long">
        select id
        from
        <include refid="Table_Name"/>
        order by id desc limit 1
    </select>

    <select id="selectBillBetweenTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        and id > #{minId,jdbcType=BIGINT}
        and biz_type in
        <foreach collection="bizTypes" item="bizType" open="(" separator="," close=")">
            #{bizType}
        </foreach>
        order by id asc
        limit #{pageSize}
    </select>

    <select id="selectUserLastCheckTime" resultType="java.util.Date">
        select check_time
        from
        <include refid="Table_Name"/>
        where user_id=#{userId}
        and coin_id = #{coinId}
        and check_time &lt;= #{checkTime}
        order by check_time desc
        limit 1
    </select>

    <select id="selectByUserIdAndCheckTimeAndCoinId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where
        check_time = #{checkTime}
        and user_id =#{userId}
        AND coin_id =#{coinId}
    </select>

    <select id="selectByCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        force index (idx_check_time)
        where check_time = #{checkTime}
        and id > #{startId,jdbcType=BIGINT}
        order by id asc
        limit #{pageSize}
    </select>

    <select id="countByCheckTime" resultType="java.lang.Integer">
        select
        count(*)
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
    </select>


    <insert id="batchInsertTemp" parameterType="com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty"
            useGeneratedKeys="true"
            keyProperty="id">
        insert into<include refid="Table_Name"/>_temp (
        user_id,
        coin_id,
        check_time,
        biz_type,
        change_prop1,
        prop1,
        change_prop2,
        prop2,
        change_prop3,
        prop3,
        change_prop4,
        prop4,
        change_prop5,
        prop5,
        create_time,
        update_time,
        initial_time,
        params
        )
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <select id="selectLastCheckTimeTemp" resultType="java.util.Date">
        select check_time
        from<include refid="Table_Name"/>_temp
        order by check_time desc
        limit 1
    </select>
    <!--    <select id="selectUserInfoListUseMapper" resultType="com.upex.bill.domain.BillUser">-->
    <!--        select-->
    <!--        user_id,-->
    <!--        max(check_time) as check_ok_time-->
    <!--        from <include refid="Table_Name"/>-->
    <!--        force index(uniq_userId_checkTime_coinId_bizType)-->
    <!--        where user_id in-->
    <!--        <foreach collection="userIdList" item="userId" open="(" separator="," close=")">-->
    <!--            #{userId}-->
    <!--        </foreach>-->
    <!--        group by user_id-->
    <!--    </select>-->
    <select id="selectLastCheckTimeByAccount" resultType="java.util.Date">
        select check_time
        from
        <include refid="Table_Name"/>
        order by check_time desc limit 1
    </select>


    <update id="createTable">
        CREATE TABLE IF NOT EXISTS <include refid="Table_Name"></include> (
        `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
        `user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户id',
        `coin_id` int NOT NULL COMMENT '币种',
        `biz_type` varchar(32) NOT NULL COMMENT '业务类型',
        `change_prop1` decimal(36, 16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1变化',
        `change_prop2` decimal(36, 16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2变化',
        `change_prop3` decimal(36, 16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3变化',
        `change_prop4` decimal(36, 16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4变化',
        `change_prop5` decimal(36, 16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5变化',
        `change_prop6` decimal(36, 16) NOT NULL DEFAULT '0.****************' COMMENT '资产值6变化',
        `change_prop7` decimal(36, 16) NOT NULL DEFAULT '0.****************' COMMENT '资产值7变化',
        `change_prop8` decimal(36, 16) NOT NULL DEFAULT '0.****************' COMMENT '资产值8变化',
        `create_time` datetime(3) NOT NULL COMMENT '创建时间',
        `update_time` datetime(3) NOT NULL COMMENT '最后更新时间',
        `check_time` datetime(3) NOT NULL COMMENT '检查时间',
        `initial_time` datetime(3) NOT NULL COMMENT '资产初始时间',
        `params` mediumtext COMMENT '备用参数',
        PRIMARY KEY (`id`) USING BTREE,
        UNIQUE KEY `uniq_userId_checkTime_coinId_bizType` (`user_id`, `check_time`, `coin_id`, `biz_type`) USING BTREE,
        KEY `idx_check_time` (`check_time`) USING BTREE
        ) DEFAULT CHARSET = utf8 COMMENT = '按照用户+币种+业务类型维度汇总数据'
    </update>

    <delete id="deleteByCheckTime">
        delete from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        limit #{batchSize}
    </delete>

    <select id="selectByUserIdAndGtCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where user_id = #{userId}
        and check_time &gt;= #{checkTime}
        and id &gt; #{startId}
        order by id
        limit #{pageSize}
    </select>

    <select id="getTables" resultType="string">
        show tables like '%${tablePrefix}%';
    </select>

    <select id="selectByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        force index(uniq_userId_checkTime_coinId_bizType)
        where user_id=#{userId}
        and check_time &gt;= #{startTime}
        and check_time &lt;= #{endTime}
        <if test="bizTypes != null and bizTypes.size() > 0">
            and biz_type in
            <foreach collection="bizTypes" item="bizType" open="(" separator="," close=")">
                #{bizType}
            </foreach>
        </if>
        <if test="coinIds != null and coinIds.size() > 0">
            and coin_id in
            <foreach collection="coinIds" item="coinId" open="(" separator="," close=")">
                #{coinId}
            </foreach>
        </if>
        <if test="minId != null">
            and id &gt; #{minId}
        </if>
        order by id
        limit #{pageSize}
    </select>

    <select id="selectMaxMinId" resultType="com.upex.reconciliation.service.model.dto.MaxMinIdDTO">
        select max(id) maxId,min(id) minId
        from <include refid="Table_Name"/>
        where check_time = #{checkTime}
    </select>

    <select id="selectByCheckTimeAndIdSegment" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where  check_time = #{checkTime}
        and id &gt;= #{startId}
        and id &lt; #{endId}
    </select>
</mapper>