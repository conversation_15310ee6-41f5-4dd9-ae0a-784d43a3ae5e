<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.FinancialVirtualAccountMapper">

    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.FinancialVirtualAccount">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="coinId" column="coin_id" jdbcType="INTEGER"/>
            <result property="coinName" column="coin_name" jdbcType="VARCHAR"/>
            <result property="groupType" column="group_type" jdbcType="INTEGER"/>
            <result property="totalBalance" column="total_balance" jdbcType="DECIMAL"/>
            <result property="sourceCreateTime" column="source_create_time" jdbcType="TIMESTAMP"/>
            <result property="sourceUpdateTime" column="source_update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
            <result property="preSettleInterest" column="pre_settle_interest" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,coin_id,
        coin_name,group_type,total_balance,
        source_create_time,source_update_time,create_time,
        update_time,version,pre_settle_interest
    </sql>
    <sql id="Table_Name">
        financial_virtual_account
    </sql>
    <sql id="batch_insert_value">
        #{record.id,jdbcType=BIGINT}, #{record.userId,jdbcType=BIGINT}, #{record.coinId,jdbcType=INTEGER},
        #{record.coinName,jdbcType=VARCHAR}, #{record.groupType,jdbcType=INTEGER}, #{record.totalBalance,jdbcType=DECIMAL},
        #{record.sourceCreateTime,jdbcType=TIMESTAMP}, #{record.sourceUpdateTime,jdbcType=TIMESTAMP}, #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP}, #{record.version,jdbcType=BIGINT}, #{record.preSettleInterest,jdbcType=DECIMAL}
    </sql>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert  into
        <include refid="Table_Name"/>
        (id, user_id,coin_id,
        coin_name,group_type,total_balance,
        source_create_time,source_update_time,create_time,
        update_time,version,pre_settle_interest)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>
    <update id="batchUpdateFinancialVirtualAccount">
        update
        <include refid="Table_Name"/>
        <trim prefix="set" suffixOverrides=",">
            user_id=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.userId,jdbcType=BIGINT}
            </foreach>
            coin_id=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.coinId,jdbcType=INTEGER}
            </foreach>
            coin_name=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.coinName,jdbcType=VARCHAR}
            </foreach>
            group_type=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.groupType,jdbcType=INTEGER}
            </foreach>
            total_balance=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.totalBalance,jdbcType=DECIMAL}
            </foreach>
            source_update_time=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.sourceUpdateTime,jdbcType=TIMESTAMP}
            </foreach>
            update_time=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.updateTime,jdbcType=TIMESTAMP}
            </foreach>
            version=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.version,jdbcType=BIGINT}
            </foreach>
            pre_settle_interest=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.preSettleInterest,jdbcType=DECIMAL}
            </foreach>
        </trim>
        WHERE
        <foreach collection="records" item="item" open="( " separator=") or (" close=" )">
            id = #{item.id}
        </foreach>
    </update>
    <select id="selectByIds" resultType="com.upex.reconciliation.service.dao.entity.FinancialVirtualAccount">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where
        <foreach collection="ids" item="id" open="id in (" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="batchSaveOrUpdate" parameterType="java.util.List">
        INSERT INTO <include refid="Table_Name"/> (
        <include refid="Base_Column_List"/>
        )
        VALUES
        <foreach collection="records" item="record" separator=",">
            (
            <include refid="batch_insert_value"/>
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        total_balance = IF(<include refid="update_condition"/>, VALUES(total_balance), total_balance),
        source_create_time = IF(<include refid="update_condition"/>, VALUES(source_create_time), source_create_time),
        update_time = IF(<include refid="update_condition"/>, VALUES(update_time), update_time),
        pre_settle_interest = IF(<include refid="update_condition"/>, VALUES(pre_settle_interest), pre_settle_interest),
        version = IF(<include refid="update_condition"/>, VALUES(version), version),
        source_update_time = IF(source_update_time &lt;= VALUES(source_update_time) AND version = VALUES(version), VALUES(source_update_time), source_update_time)
    </insert>

    <sql id="update_condition">
        source_update_time &lt;= VALUES(source_update_time) AND version &lt; VALUES(version)
    </sql>
</mapper>
