<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.upex.reconciliation.service.dao.mapper.cex.ThirdCexPayTransferHistoryMapper">

    <resultMap id="ThirdCexPayTransferHistoryMap"
               type="com.upex.reconciliation.service.dao.cex.entity.ThirdCexPayTransferHistory">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cex_user_id" property="cexUserId" jdbcType="VARCHAR"/>
        <result column="cex_email" property="cexEmail" jdbcType="VARCHAR" typeHandler="com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler"/>
        <result column="cex_type" property="cexType" jdbcType="INTEGER"/>
        <result column="source_user_id" property="sourceUserId" jdbcType="VARCHAR"/>
        <result column="target_user_id" property="targetUserId" jdbcType="VARCHAR"/>
        <result column="coin_name" property="coinName" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="transfer_id" property="transferId" jdbcType="VARCHAR"/>
        <result column="transfer_time" property="transferTime" jdbcType="TIMESTAMP"/>
        <result column="is_legal" property="isLegal" jdbcType="INTEGER"/>
        <result column="check_sync_time" property="checkSyncTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, cex_user_id, cex_email, cex_type, source_user_id, target_user_id,
        coin_name, amount, transfer_id,transfer_time,is_legal,check_sync_time, create_time, update_time, version
    </sql>

    <!-- 插入 -->
    <insert id="insert">
        INSERT INTO third_cex_pay_transfer_history (
            cex_user_id, cex_email,cex_type, source_user_id, target_user_id,
            coin_name, amount, transfer_id,transfer_time,is_legal,check_sync_time, create_time, update_time, version
        ) VALUES (
            #{cexUserId},#{cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler}, #{cexType}, #{sourceUserId}, #{targetUserId},
            #{coinName}, #{amount}, #{transferId},#{transferTime},#{isLegal},#{checkSyncTime}, #{createTime}, #{updateTime}, #{version}
        )
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="ThirdCexPayTransferHistoryMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_pay_transfer_history
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID和交易所类型查询 -->
    <select id="selectByCexUserAndType" resultMap="ThirdCexPayTransferHistoryMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_pay_transfer_history
        WHERE cex_user_id = #{cexUserId}
        AND cex_type = #{cexType}
        ORDER BY create_time DESC
    </select>

    <!-- 更新 -->
    <update id="update">
        UPDATE third_cex_pay_transfer_history
        SET
            <if test="cexUserId != null">
                cex_user_id = #{cexUserId},
            </if>
            <if test="cexType != null">
                cex_type = #{cexType},
            </if>
            <if test="sourceUserId != null">
                source_user_id = #{sourceUserId},
            </if>
            <if test="targetUserId != null">
                target_user_id = #{targetUserId},
            </if>
            <if test="coinName != null and coinName != ''">
                coin_name = #{coinName},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="transferId != null and transferId != ''">
                transfer_id = #{transferId},
            </if>
            <if test="is_legal !=null">
                    is_legal = #{is_legal},
            </if>
            version = version + 1
        WHERE id = #{id}
    </update>

    <!-- 按条件分页查询 -->
    <select id="selectList" resultMap="ThirdCexPayTransferHistoryMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_pay_transfer_history
        <where>
            <if test="cexUserId != null and cexUserId != ''">
                AND cex_user_id = #{cexUserId}
            </if>
            <if test="cexType != null">
                AND cex_type = #{cexType}
            </if>
            <if test="coinName != null and coinName != ''">
                AND coin_name = #{coinName}
            </if>
            <if test="startTime != null">
                AND transfer_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND transfer_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert">
        INSERT INTO third_cex_pay_transfer_history (
            cex_user_id,cex_email, cex_type, source_user_id, target_user_id,
            coin_name, amount, transfer_id,transfer_time,is_legal,check_sync_time,create_time, update_time, version
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.cexUserId},#{item.cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler}, #{item.cexType}, #{item.sourceUserId}, #{item.targetUserId},
            #{item.coinName}, #{item.amount}, #{item.transferId},#{item.transferTime},#{item.isLegal},#{item.checkSyncTime}, #{item.createTime}, #{item.updateTime}, #{item.version}
            )
        </foreach>
    </insert>

    <select id="selectUnCheckWithdraw" resultMap="ThirdCexPayTransferHistoryMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_pay_transfer_history
        WHERE cex_type = #{cexType}
        AND is_legal = #{isLegal}
        ORDER BY create_time DESC
    </select>

    <update id="checkWithdraw">
        UPDATE third_cex_pay_transfer_history
        SET
            is_legal = #{isLegal}
        WHERE id = #{id}
    </update>

</mapper>
