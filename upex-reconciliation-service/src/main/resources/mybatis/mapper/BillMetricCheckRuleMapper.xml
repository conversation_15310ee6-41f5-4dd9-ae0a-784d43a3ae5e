<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillMetricCheckRuleMapper">
  <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillMetricCheckRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="scene_id" jdbcType="BIGINT" property="sceneId" />
    <result column="metric_data_rule_id" jdbcType="BIGINT" property="metricDataRuleId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="data_check_service" jdbcType="VARCHAR" property="dataCheckService" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
      <where>
          <foreach collection="example.oredCriteria" item="criteria" separator="or">
              <if test="criteria.valid">
                  <trim prefix="(" prefixOverrides="and" suffix=")">
                      <foreach collection="criteria.criteria" item="criterion">
                          <choose>
                              <when test="criterion.noValue">
                                  and ${criterion.condition}
                              </when>
                              <when test="criterion.singleValue">
                                  and ${criterion.condition} #{criterion.value}
                              </when>
                              <when test="criterion.betweenValue">
                                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                              </when>
                              <when test="criterion.listValue">
                                  and ${criterion.condition}
                                  <foreach close=")" collection="criterion.value" item="listItem" open="("
                                           separator=",">
                                      #{listItem}
                                  </foreach>
                              </when>
                          </choose>
                      </foreach>
                  </trim>
              </if>
          </foreach>
      </where>
  </sql>
  <sql id="Base_Column_List">
    id, scene_id, metric_data_rule_id, name, note, data_check_service, create_time, version
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_metric_check_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_metric_check_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.upex.reconciliation.service.dao.entity.BillMetricCheckRule">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bill_metric_check_rule (scene_id, metric_data_rule_id, name, 
      note, data_check_service, create_time, 
      version)
    values (#{sceneId,jdbcType=BIGINT}, #{metricDataRuleId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{note,jdbcType=VARCHAR}, #{dataCheckService,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{version,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.upex.reconciliation.service.dao.entity.BillMetricCheckRule">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bill_metric_check_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sceneId != null">
        scene_id,
      </if>
      <if test="metricDataRuleId != null">
        metric_data_rule_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="dataCheckService != null">
        data_check_service,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sceneId != null">
        #{sceneId,jdbcType=BIGINT},
      </if>
      <if test="metricDataRuleId != null">
        #{metricDataRuleId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="dataCheckService != null">
        #{dataCheckService,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <update id="updateByExampleSelective" parameterType="map">
    update bill_metric_check_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sceneId != null">
        scene_id = #{record.sceneId,jdbcType=BIGINT},
      </if>
      <if test="record.metricDataRuleId != null">
        metric_data_rule_id = #{record.metricDataRuleId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.note != null">
        note = #{record.note,jdbcType=VARCHAR},
      </if>
      <if test="record.dataCheckService != null">
        data_check_service = #{record.dataCheckService,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bill_metric_check_rule
    set id = #{record.id,jdbcType=BIGINT},
      scene_id = #{record.sceneId,jdbcType=BIGINT},
      metric_data_rule_id = #{record.metricDataRuleId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      note = #{record.note,jdbcType=VARCHAR},
      data_check_service = #{record.dataCheckService,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      version = #{record.version,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.upex.reconciliation.service.dao.entity.BillMetricCheckRule">
    update bill_metric_check_rule
    <set>
      <if test="sceneId != null">
        scene_id = #{sceneId,jdbcType=BIGINT},
      </if>
      <if test="metricDataRuleId != null">
        metric_data_rule_id = #{metricDataRuleId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="dataCheckService != null">
        data_check_service = #{dataCheckService,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.upex.reconciliation.service.dao.entity.BillMetricCheckRule">
    update bill_metric_check_rule
    set scene_id = #{sceneId,jdbcType=BIGINT},
      metric_data_rule_id = #{metricDataRuleId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      note = #{note,jdbcType=VARCHAR},
      data_check_service = #{dataCheckService,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <sql id="MysqlSuffix">
    <if test="page != null">
      <![CDATA[ limit #{page.begin} , #{page.length} ]]>
    </if>
  </sql>
</mapper>