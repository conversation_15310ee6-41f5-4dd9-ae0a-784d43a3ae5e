<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillCapitalConfigMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillCapitalConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="billStatisticsTime" column="bill_statistics_time" jdbcType="TIMESTAMP"/>
            <result property="checkOkTime" column="check_ok_time" jdbcType="TIMESTAMP"/>
            <result property="billCapitalValue" column="bill_capital_value" jdbcType="DECIMAL"/>
            <result property="billCapitalAbsValue" column="bill_capital_abs_value" jdbcType="DECIMAL"/>
            <result property="billCapitalNum" column="bill_capital_num" jdbcType="INTEGER"/>
            <result property="billCapitalStatus" column="bill_capital_status" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,bill_statistics_time,check_ok_time,
        bill_capital_value,bill_capital_abs_value,bill_capital_num,bill_capital_status,
        create_time,update_time
    </sql>
    <sql id="Table_Name">
        bill_capital_config
    </sql>
    <insert id="insertBillCapitalConfig" parameterType="com.upex.reconciliation.service.dao.entity.BillCapitalConfig"
            useGeneratedKeys="true" keyProperty="billCapitalConfig.id">
        insert into
        <include refid="Table_Name"/>
        (id, bill_statistics_time,check_ok_time,
        bill_capital_value,bill_capital_abs_value,bill_capital_num,bill_capital_status,
        create_time,update_time
        )
        values (
        #{billCapitalConfig.id,jdbcType=BIGINT},
        #{billCapitalConfig.billStatisticsTime,jdbcType=TIMESTAMP},
        #{billCapitalConfig.checkOkTime,jdbcType=TIMESTAMP},
        #{billCapitalConfig.billCapitalValue,jdbcType=DECIMAL},
        #{billCapitalConfig.billCapitalAbsValue,jdbcType=DECIMAL},
        #{billCapitalConfig.billCapitalNum,jdbcType=INTEGER},
        #{billCapitalConfig.billCapitalStatus,jdbcType=INTEGER},
        #{billCapitalConfig.createTime,jdbcType=TIMESTAMP},
        #{billCapitalConfig.updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <update id="updateConfigDiffAndStatus" parameterType="com.upex.reconciliation.service.dao.entity.BillCapitalConfig">
        update
        <include refid="Table_Name"/>
        set bill_capital_value = #{config.billCapitalValue},
        bill_capital_abs_value = #{config.billCapitalAbsValue},
        bill_capital_status = #{config.billCapitalStatus},
        bill_statistics_time = #{config.billStatisticsTime},
        update_time = #{config.updateTime}
        where id = #{config.id}
    </update>
    <delete id="deleteByCheckOkTime">
        delete
        from
        <include refid="Table_Name"/>
        where check_ok_time = #{checkOkTime}
    </delete>
    <select id="selectConfigByTimeAndStatus" resultType="com.upex.reconciliation.service.dao.entity.BillCapitalConfig">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_ok_time = #{executeTime}
        and bill_capital_status = #{statusCode}
    </select>
    <select id="selectLastConfigByTimeAndStatus" resultType="com.upex.reconciliation.service.dao.entity.BillCapitalConfig">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where bill_capital_status = #{statusCode}
        and check_ok_time <![CDATA[ < ]]> #{executeTime}
        order by check_ok_time desc limit 1
    </select>
    <select id="selectLastConfig" resultType="com.upex.reconciliation.service.dao.entity.BillCapitalConfig">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where bill_capital_status > 0
        order by check_ok_time desc limit 1
    </select>
    <select id="selectLastFinalStateConfig" resultType="com.upex.reconciliation.service.dao.entity.BillCapitalConfig">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        order by check_ok_time desc limit #{capitalBlockedWithdrawCount}
    </select>
    <select id="selectSimilarityCheckOkTimeByTime" resultType="java.util.Date">
        select check_ok_time from
        <include refid="Table_Name"/>
        where DATE_FORMAT(check_ok_time,'%Y-%m-%d %H:%i') = DATE_FORMAT(#{checkOkTime},'%Y-%m-%d %H:%i')
        order by check_ok_time desc limit 1
    </select>
    <select id="selectGeTimeConfigList" resultType="com.upex.reconciliation.service.dao.entity.BillCapitalConfig">
        select check_ok_time from
        <include refid="Table_Name"/>
        where check_ok_time >= #{resetDate}
    </select>
</mapper>
