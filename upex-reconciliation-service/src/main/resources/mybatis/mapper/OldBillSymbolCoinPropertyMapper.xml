<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.OldBillSymbolCoinPropertyMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillSymbolCoinProperty">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="symbol_id" property="symbolId" jdbcType="VARCHAR"/>
        <result column="coin_id" property="coinId" jdbcType="INTEGER"/>
        <result column="change_prop1" property="changeProp1" jdbcType="DECIMAL"/>
        <result column="prop1" property="prop1" jdbcType="DECIMAL"/>
        <result column="change_prop2" property="changeProp2" jdbcType="DECIMAL"/>
        <result column="prop2" property="prop2" jdbcType="DECIMAL"/>
        <result column="change_prop3" property="changeProp3" jdbcType="DECIMAL"/>
        <result column="prop3" property="prop3" jdbcType="DECIMAL"/>
        <result column="change_prop4" property="changeProp4" jdbcType="DECIMAL"/>
        <result column="prop4" property="prop4" jdbcType="DECIMAL"/>
        <result column="change_prop5" property="changeProp5" jdbcType="DECIMAL"/>
        <result column="prop5" property="prop5" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="check_time" property="checkTime" jdbcType="TIMESTAMP"/>
        <result column="settle_price" property="settlePrice" jdbcType="DECIMAL"/>
        <result column="coin_count4" property="coinCount4" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,symbol_id,coin_id,change_prop1,prop1,change_prop2,prop2,
        change_prop3,prop3,change_prop4,prop4,change_prop5,prop5,
        create_time,update_time,check_time
    </sql>
    <sql id="Base_Column_List_No_Id">
        symbol_id,coin_id,change_prop1,prop1,change_prop2,prop2,
        change_prop3,prop3,change_prop4,prop4,change_prop5,prop5,
        create_time,update_time,check_time
    </sql>
    <sql id="Base_Column_List_Value">
        #{record.symbolId},#{record.coinId},#{record.changeProp1},#{record.prop1},#{record.changeProp2},#{record.prop2},
        #{record.changeProp3},#{record.prop3},#{record.changeProp4},#{record.prop4},#{record.changeProp5},#{record.prop5},
        #{record.createTime},#{record.updateTime},#{record.checkTime}
    </sql>
    <sql id="Base_Table_Name">
        bill_symbol_coin_property_${accountType}_${accountParam}
    </sql>

    <select id="selectListByCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Base_Table_Name"></include>
        where check_time = #{checkOkTime}
    </select>
</mapper>