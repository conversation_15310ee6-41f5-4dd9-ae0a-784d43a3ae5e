<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillUserPositionMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillUserPosition">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="symbol_id" jdbcType="VARCHAR" property="symbolId"/>
        <result column="token_id" jdbcType="VARCHAR" property="tokenId"/>
        <result column="coin_id" jdbcType="INTEGER" property="coinId"/>
        <result column="check_ok_time" jdbcType="TIMESTAMP" property="checkOkTime"/>
        <result column="biz_time" jdbcType="TIMESTAMP" property="bizTime"/>
        <result column="l_pos_mgn" jdbcType="DECIMAL" property="lPosMgn"/>
        <result column="l_count" jdbcType="DECIMAL" property="lCount"/>
        <result column="l_avg" jdbcType="DECIMAL" property="lAvg"/>
        <result column="s_pos_mgn" jdbcType="DECIMAL" property="sPosMgn"/>
        <result column="s_count" jdbcType="DECIMAL" property="sCount"/>
        <result column="s_avg" jdbcType="DECIMAL" property="sAvg"/>
        <result column="realized" jdbcType="DECIMAL" property="realized"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, symbol_id, token_id, coin_id, check_ok_time, biz_time,
        l_pos_mgn, l_count, l_avg, s_pos_mgn, s_count, s_avg, create_time, update_time, realized
    </sql>
    <sql id="Base_Column_List_No_Id">
        user_id, symbol_id, token_id, coin_id, check_ok_time, biz_time,
        l_pos_mgn, l_count, l_avg, s_pos_mgn, s_count, s_avg,
        create_time, update_time, realized
    </sql>

    <delete id="batchDelete">
        delete from
        <include refid="Base_Table_Name"/>
        where id >= #{beginId}
        limit #{pageSize}
    </delete>
    <sql id="Base_Column_List_Value">
        #{record.userId},#{record.symbolId},#{record.tokenId}, #{record.coinId},
        #{record.checkOkTime},#{record.bizTime},
        #{record.lPosMgn},#{record.lCount},#{record.lAvg},#{record.sPosMgn},#{record.sCount},#{record.sAvg},
        #{record.createTime},#{record.updateTime},#{record.realized}
    </sql>
    <sql id="Base_Table_Name">
        <choose>
            <when test="_parameter.containsKey('tableSuffix') and tableSuffix != null and tableSuffix != ''">
                bill_user_position_${accountType}_${accountParam}_${tableSuffix}
            </when>
            <otherwise>
                bill_user_position_${accountType}_${accountParam}
            </otherwise>
        </choose>
    </sql>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert  into
        <include refid="Base_Table_Name"></include>
        (
        <include refid="Base_Column_List_No_Id"></include>
        )
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="Base_Column_List_Value"/>
            </trim>
        </foreach>
    </insert>

    <delete id="deleteByCheckTime">
        delete from
        <include refid="Base_Table_Name"/>
        where check_ok_time = #{checkTime}
        limit #{pageSize}
    </delete>

    <select id="selectRangeCheckTimeRecordPageQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Base_Table_Name"></include>
        force index (check_ok_time_id)
        where check_ok_time = #{checkTime}
        and id &gt; #{minId}
        order by id asc
        limit #{pageSize}
    </select>

    <select id="selectByCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Base_Table_Name"></include>
        where check_ok_time = #{checkTime}
        and user_id = #{userId}
    </select>

    <select id="selectByCheckTimeAndCoinId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Base_Table_Name"></include>
        where check_ok_time = #{checkTime}
        and user_id = #{userId}
        and coin_id = #{coinId}
    </select>

    <update id="createTable">
        CREATE TABLE IF NOT EXISTS <include refid="Base_Table_Name"></include> (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
            `user_id` bigint(20) NOT NULL COMMENT '用户id',
            `symbol_id` varchar(64) NOT NULL COMMENT '币对id',
            `token_id` varchar(64) DEFAULT NULL COMMENT '币种token',
            `coin_id` int(11) NOT NULL COMMENT '币种',
            `biz_time` datetime(3) DEFAULT NULL COMMENT '业务时间',
            `l_pos_mgn` decimal(36, 16) NOT NULL DEFAULT '0.0000000000000000' COMMENT '多仓保证金',
            `l_count` decimal(36, 16) NOT NULL DEFAULT '0.0000000000000000' COMMENT '多仓持仓数量',
            `l_avg` decimal(36, 16) NOT NULL DEFAULT '0.0000000000000000' COMMENT '多仓持仓均价',
            `s_pos_mgn` decimal(36, 16) NOT NULL DEFAULT '0.0000000000000000' COMMENT '空仓保证金',
            `s_count` decimal(36, 16) NOT NULL DEFAULT '0.0000000000000000' COMMENT '空仓持仓数量',
            `s_avg` decimal(36, 16) NOT NULL DEFAULT '0.0000000000000000' COMMENT '空仓持仓均价',
            `check_ok_time` datetime(3) NOT NULL COMMENT '对账成功实践',
            `create_time` datetime(3) NOT NULL COMMENT '创建时间',
            `update_time` datetime(3) NOT NULL COMMENT '最后更新时间',
            `realized` decimal(36, 16) NOT NULL DEFAULT '0.0000000000000000' COMMENT '持仓已实现',
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE KEY `idx_user_symbol_coin_check_time` (`user_id`, `symbol_id`, `coin_id`, `check_ok_time`) USING BTREE,
            KEY `check_ok_time_id` (`check_ok_time`,`id`) USING BTREE
        ) DEFAULT CHARSET = utf8 COMMENT = 'coin用户维度-仓位快照';
    </update>

    <select id="getTables" resultType="string">
        show tables like '%${tablePrefix}%';
    </select>

    <delete id="deleteByTableName">
        delete from ${tableName} limit #{pageSize}
    </delete>

    <delete id="deleteAll">
        delete from
        <include refid="Base_Table_Name"/>
        limit #{pageSize}
    </delete>

    <select id="getTableColumns" resultType="string">
        SELECT COLUMN_NAME as 'columnName',
        DATA_TYPE as 'dataType',
        IS_NULLABLE as 'isNullable',
        COLUMN_DEFAULT as 'columnDefault',
        COLUMN_COMMENT as 'columnComment'
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = #{tableSchema}
        AND TABLE_NAME = #{tableName}
    </select>

    <select id="selectByUserIdAndGtCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Base_Table_Name"/>
        where user_id = #{userId}
        and check_ok_time &gt;= #{checkTime}
        and id &gt; #{startId}
        order by id
        limit #{pageSize}
    </select>

    <update id="updateUserPositionReLAvgAndReSAvg">
        update <include refid="Base_Table_Name"/>
        set
        l_pos_mgn = #{lastReLAvg}
        ,s_pos_mgn = #{lastReSAvg}
        ,update_time = now()
        where
        user_id = #{userId}
        and check_ok_time = #{checkTime}
    </update>
</mapper>