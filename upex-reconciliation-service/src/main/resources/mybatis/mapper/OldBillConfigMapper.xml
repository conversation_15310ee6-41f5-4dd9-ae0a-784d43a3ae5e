<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.OldBillConfigMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.bill.entity.OldBillConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="account_type" property="accountType" jdbcType="TINYINT"/>
        <result column="account_param" property="accountParam" jdbcType="VARCHAR"/>
        <result column="sync_pos" property="syncPos" jdbcType="BIGINT"/>
        <result column="check_ok_time" property="checkOkTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, account_type, account_param, sync_pos, check_ok_time, `status`, create_time, 
    update_time
  </sql>

    <sql id="batch_insert_value">
     #{record.accountType,jdbcType=TINYINT},#{record.accountParam,jdbcType=VARCHAR},#{record.syncPos,jdbcType=BIGINT},#{record.checkOkTime,jdbcType=TIMESTAMP},#{record.status,jdbcType=TINYINT},
     #{record.createTime,jdbcType=TIMESTAMP}, #{record.updateTime,jdbcType=TIMESTAMP}
     </sql>
    <select id="selectByTypeAndParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_config
        where account_type = #{accountType} and account_param = #{accountParam}
    </select>


    <insert id="insertSelective" parameterType="com.upex.reconciliation.service.dao.bill.entity.OldBillConfig"
            useGeneratedKeys="true" keyProperty="id">
        insert into bill_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="accountType != null">
                account_type,
            </if>
            <if test="accountParam != null">
                account_param,
            </if>
            <if test="syncPos != null">
                sync_pos,
            </if>
            <if test="checkOkTime != null">
                check_ok_time,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="accountType != null">
                #{accountType,jdbcType=TINYINT},
            </if>
            <if test="accountParam != null">
                #{accountParam,jdbcType=VARCHAR},
            </if>
            <if test="syncPos != null">
                #{syncPos,jdbcType=BIGINT},
            </if>
            <if test="checkOkTime != null">
                #{checkOkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="com.upex.reconciliation.service.dao.bill.entity.OldBillConfig">
        insert into bill_config(account_type, account_param, sync_pos, check_ok_time, `status`, create_time,
        update_time)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.upex.reconciliation.service.dao.bill.entity.OldBillConfig">
        update bill_config
        <set>
            <if test="accountType != null">
                account_type = #{accountType,jdbcType=TINYINT},
            </if>
            <if test="accountParam != null">
                account_param = #{accountParam,jdbcType=VARCHAR},
            </if>
            <if test="syncPos != null">
                sync_pos = #{syncPos,jdbcType=BIGINT},
            </if>
            <if test="flag and checkOkTime != null">
                check_ok_time = #{checkOkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where account_type = #{accountType,jdbcType=TINYINT} and account_param = #{accountParam,jdbcType=VARCHAR}
    </update>

    <update id="createBillUserTable" parameterType="com.upex.reconciliation.service.dao.bill.entity.OldBillConfig">
        CREATE TABLE IF NOT EXISTS `bill_user_${accountType}_${accountParam}` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `check_ok_time` datetime(3) NOT NULL COMMENT '最后一次检查成功时间',
  `status` tinyint(3) NOT NULL COMMENT '当前检查状态',
  `create_time` datetime(3) NOT NULL COMMENT '创建时间',
  `update_time` datetime(3) NOT NULL COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_userId` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='用户信息表';
    </update>

    <update id="createBillCoinTable" parameterType="com.upex.reconciliation.service.dao.bill.entity.OldBillConfig">
        CREATE TABLE IF NOT EXISTS `bill_coin_property_${accountType}_${accountParam}` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `coin_id` int(11) NOT NULL COMMENT '币种',
  `check_time` datetime(3) NOT NULL COMMENT '资产检查时间',
  `change_prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1变化',
  `prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1',
  `change_prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2变化',
  `prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2',
  `change_prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3变化',
  `prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3',
  `change_prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4变化',
  `prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4',
  `change_prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5变化',
  `prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5',
  `create_time` datetime(3) NOT NULL COMMENT '创建时间',
  `update_time` datetime(3) NOT NULL COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_checkTime_coinId` (`check_time`,`coin_id`) USING BTREE,
  KEY `idx_checkTime` (`check_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='按照币维度账单数据明细'


    </update>

    <update id="createBillCoinTypeTable" parameterType="com.upex.reconciliation.service.dao.bill.entity.OldBillConfig">


CREATE TABLE IF NOT EXISTS  `bill_coin_type_property_${accountType}_${accountParam}` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `biz_type` varchar(32) NOT NULL COMMENT '业务类型',
  `change_prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1变化',
  `prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1',
  `change_prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2变化',
  `prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2',
  `change_prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3变化',
  `prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3',
  `change_prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4变化',
  `prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4',
  `change_prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5变化',
  `prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5',
  `create_time` datetime(3) NOT NULL COMMENT '创建时间',
  `update_time` datetime(3) NOT NULL COMMENT '最后更新时间',
  `coin_id` int(11) NOT NULL COMMENT '币种',
  `check_time` datetime(3) NOT NULL COMMENT '检查时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_checkTime_coinId_bizType` (`check_time`,`coin_id`,`biz_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='按照币+业务类型维度汇总数据'
    </update>

    <update id="createBillCoinUser" parameterType="com.upex.reconciliation.service.dao.bill.entity.OldBillConfig">
        CREATE TABLE IF NOT EXISTS `bill_coin_user_property_${accountType}_${accountParam}` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` bigint(20) NOT NULL COMMENT '用户userId',
  `coin_id` int(11) NOT NULL COMMENT '币种',
  `check_time` datetime(3) NOT NULL COMMENT '资产检查时间',
  `change_prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1变化',
  `prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1',
  `change_prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2变化',
  `prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2',
  `change_prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3变化',
  `prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3',
  `change_prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4变化',
  `prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4',
  `change_prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5变化',
  `prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5',
  `create_time` datetime(3) NOT NULL COMMENT '创建时间',
  `update_time` datetime(3) NOT NULL COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_userId_checkTime_coinId` (`user_id`,`check_time`,`coin_id`) USING BTREE,
  KEY `idx_checkTime` (`check_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='按照用户+币种维度汇总数据'

    </update>

    <update id="createBillCoinTypeUserTable" parameterType="com.upex.reconciliation.service.dao.bill.entity.OldBillConfig">

CREATE TABLE `bill_coin_type_user_property_${accountType}_${accountParam}` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `biz_type` varchar(32) NOT NULL COMMENT '业务类型',
  `change_prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1变化',
  `prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1',
  `change_prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2变化',
  `prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2',
  `change_prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3变化',
  `prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3',
  `change_prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4变化',
  `prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4',
  `change_prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5变化',
  `prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5',
  `create_time` datetime(3) NOT NULL COMMENT '创建时间',
  `update_time` datetime(3) NOT NULL COMMENT '最后更新时间',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
  `coin_id` int(11) NOT NULL COMMENT '币种',
  `check_time` datetime(3) NOT NULL COMMENT '检查时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_userId_checkTime_coinId_bizType` (`user_id`,`check_time`,`coin_id`,`biz_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='按照用户+币种+业务类型维度汇总数据'
    </update>

    <select id="listAllBillConfigs" resultMap="BaseResultMap" parameterType="object">
        select
        <include refid="Base_Column_List"/>
        from bill_config
    </select>
    <select id="selectById" resultMap="BaseResultMap" parameterType="object">
        select
        <include refid="Base_Column_List"/>
        from bill_config
        where id=#{id,jdbcType=BIGINT}
    </select>

    <select id="selectMinCheckOkTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_config
        order by check_ok_time asc limit 1
    </select>

    <select id="selectSubSystemMinCheckOkTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_config
        <where>
            <if test="accountTypes != null and accountTypes.size > 0">
               and account_type in
                <foreach collection="accountTypes" item="accountType" index="index" open="(" separator="," close=")">
                    #{accountType}
                </foreach>
            </if>
            <if test="accountParams != null and accountParams.size > 0">
               and account_param in
                <foreach collection="accountParams" item="accountParam" index="index" open="(" separator="," close=")">
                    #{accountParam}
                </foreach>
            </if>
        </where>
        order by check_ok_time asc limit 1
    </select>

    <update id="updateSyncPosAndCheckTime" parameterType="object">
        update bill_config
        set sync_pos=#{syncPos},check_ok_time=#{resetCheckTime}
        where account_type = #{accountType} and account_param = #{accountParam}
    </update>
</mapper>