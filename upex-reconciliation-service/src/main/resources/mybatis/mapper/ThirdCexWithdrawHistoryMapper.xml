<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.upex.reconciliation.service.dao.mapper.cex.ThirdCexWithdrawHistoryMapper">

    <resultMap id="ThirdCexWithdrawHistoryMap"
               type="com.upex.reconciliation.service.dao.cex.entity.ThirdCexWithdrawHistory">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cex_user_id" property="cexUserId" jdbcType="VARCHAR"/>
        <result column="cex_type" property="cexType" jdbcType="INTEGER"/>
        <result column="cex_email" property="cexEmail" jdbcType="VARCHAR" typeHandler="com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler"/>
        <result column="draw_id" property="drawId" jdbcType="VARCHAR"/>
        <result column="coin_name" property="coinName" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="fee1" property="fee1" jdbcType="DECIMAL"/>
        <result column="fee2" property="fee2" jdbcType="DECIMAL"/>
        <result column="fee1_coin" property="fee1Coin" jdbcType="VARCHAR"/>
        <result column="fee2_coin" property="fee2Coin" jdbcType="VARCHAR"/>
        <result column="network" property="network" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR" typeHandler="com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler"/>
        <result column="tx_id" property="txId" jdbcType="VARCHAR"/>
        <result column="bg_user_id" property="bgUserId" jdbcType="VARCHAR"/>
        <result column="is_bg_sys" property="isBgSys" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="draw_begin_time" property="drawBeginTime" jdbcType="TIMESTAMP"/>
        <result column="draw_end_time" property="drawEndTime" jdbcType="TIMESTAMP"/>
        <result column="transfer_type" property="transferType" jdbcType="INTEGER"/>
        <result column="wallet_type" property="walletType" jdbcType="INTEGER"/>
        <result column="info" property="info" jdbcType="VARCHAR"/>
        <result column="confirm_no" property="confirmNo" jdbcType="INTEGER"/>
        <result column="is_legal" property="isLegal" jdbcType="INTEGER"/>
        <result column="check_sync_time" property="checkSyncTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, cex_user_id, cex_email, cex_type, draw_id, coin_name, amount,
        fee1, fee2, fee1_coin, fee2_coin, network,
        address, tx_id, bg_user_id, is_bg_sys, status,
        draw_begin_time, draw_end_time, transfer_type, wallet_type,info,confirm_no, is_legal,
        check_sync_time,create_time, update_time, version
    </sql>

    <!-- 插入 -->
    <insert id="insert">
        INSERT INTO third_cex_withdraw_history (
            cex_user_id,cex_email, cex_type, draw_id, coin_name, amount,
            fee1, fee2, fee1_coin, fee2_coin, network,
            address, tx_id, bg_user_id, is_bg_sys, status,
            draw_begin_time, draw_end_time, transfer_type, wallet_type,info,confirm_no, is_legal,
            check_sync_time,create_time, update_time, version
        ) VALUES (
            #{cexUserId}, #{cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},#{cexType}, #{drawId}, #{coinName}, #{amount},
            #{fee1}, #{fee2}, #{fee1Coin}, #{fee2Coin}, #{network}, #{address,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
            #{txId}, #{bgUserId}, #{isBgSys}, #{status}, #{drawBeginTime},
            #{drawEndTime}, #{transferType}, #{walletType},#{info},#{confirmNo}, #{isLegal},
            #{checkSyncTime},#{createTime}, #{updateTime}, #{version}
        )
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="ThirdCexWithdrawHistoryMap">
        SELECT  <include refid="Base_Column_List" />
        FROM third_cex_withdraw_history
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID和交易所类型查询 -->
    <select id="selectByCexUserAndType" resultMap="ThirdCexWithdrawHistoryMap">
        SELECT  <include refid="Base_Column_List" />
        FROM third_cex_withdraw_history
        WHERE cex_user_id = #{cexUserId}
        AND cex_type = #{cexType}
        ORDER BY draw_end_time DESC
    </select>

    <!-- 按条件分页查询 -->
    <select id="selectList" resultMap="ThirdCexWithdrawHistoryMap">
        SELECT  <include refid="Base_Column_List" />
        FROM third_cex_withdraw_history
        <where>
            <if test="cexUserId != null and cexUserId != ''">
                AND cex_user_id = #{cexUserId}
            </if>
            <if test="cexType != null">
                AND cex_type = #{cexType}
            </if>
            <if test="coinName != null and coinName != ''">
                AND coin_name = #{coinName}
            </if>
            <if test="startTime != null">
                AND draw_begin_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND draw_end_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY draw_end_time DESC
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert">
        INSERT INTO third_cex_withdraw_history (
            cex_user_id, cex_email,cex_type, draw_id, coin_name, amount,
            fee1, fee2, fee1_coin, fee2_coin, network,
            address, tx_id, bg_user_id, is_bg_sys, status,
            draw_begin_time, draw_end_time, transfer_type, wallet_type,info,confirm_no, is_legal,
            check_sync_time,create_time, update_time, version
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.cexUserId},#{item.cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler}, #{item.cexType}, #{item.drawId}, #{item.coinName}, #{item.amount},
            #{item.fee1}, #{item.fee2}, #{item.fee1Coin}, #{item.fee2Coin}, #{item.network}, #{item.address,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
            #{item.txId}, #{item.bgUserId}, #{item.isBgSys}, #{item.status}, #{item.drawBeginTime},
            #{item.drawEndTime}, #{item.transferType}, #{item.walletType},#{item.info},#{item.confirmNo},#{item.isLegal},#{item.checkSyncTime},
            #{item.createTime}, #{item.updateTime}, #{item.version}
            )
        </foreach>
    </insert>

    <!-- 分页查询 -->
    <select id="selectPageByUserIds" resultMap="ThirdCexWithdrawHistoryMap">
        SELECT  <include refid="Base_Column_List" />
        FROM third_cex_withdraw_history
        <where>
            <!-- 用户ID列表 -->
            <if test="cexUserIds != null and !cexUserIds.isEmpty()">
                AND cex_user_id IN
                <foreach item="userId" collection="cexUserIds" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>

            <!-- 交易所类型 -->
            <if test="withdrawReq.cexType != null">
                AND cex_type = #{withdrawReq.cexType}
            </if>

            <!-- 开始时间 -->
            <if test="withdrawReq.startTime != null">
                AND draw_begin_time >= #{withdrawReq.startTime}
            </if>

            <!-- 结束时间 -->
            <if test="withdrawReq.endTime != null">
                AND draw_begin_time &lt;= #{withdrawReq.endTime}
            </if>
        </where>
        LIMIT #{withdrawReq.offset}, #{withdrawReq.pageSize}
    </select>

    <!-- 总数统计 -->
    <select id="countPageByUserIds" resultType="int">
        SELECT COUNT(1)
        FROM third_cex_withdraw_history
        <where>
            <!-- 用户ID列表 -->
            <if test="cexUserIds != null and !cexUserIds.isEmpty()">
                AND cex_user_id IN
                <foreach item="userId" collection="cexUserIds" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>

            <!-- 交易所类型 -->
            <if test="withdrawReq.cexType != null">
                AND cex_type = #{withdrawReq.cexType}
            </if>

            <!-- 开始时间 -->
            <if test="withdrawReq.startTime != null">
                AND draw_begin_time >= #{withdrawReq.startTime}
            </if>

            <!-- 结束时间 -->
            <if test="withdrawReq.endTime != null">
                AND draw_begin_time &lt;= #{withdrawReq.endTime}
            </if>


        </where>
    </select>

    <select id="selectUnCheckWithdraw" resultMap="ThirdCexWithdrawHistoryMap">
        SELECT  <include refid="Base_Column_List" />
        FROM third_cex_withdraw_history
        WHERE cex_type = #{cexType}
        AND is_legal = #{isLegal}
         order by create_time desc limit 10
    </select>

    <update id="checkWithdraw">
        UPDATE third_cex_withdraw_history
        <set>
            <if test="isLegal != null">
                is_legal = #{isLegal},
            </if>
            <if test="bgUserId != null">
                bg_user_id = #{bgUserId},
            </if>
            <if test="isBgSys != null">
                is_bg_sys = #{isBgSys}
            </if>
            where id = #{id}
        </set>
    </update>

    <delete id="deleteByWithdrawIds">
        DELETE FROM third_cex_withdraw_history
        WHERE draw_id IN
        <foreach item="withdrawId" collection="withdrawIds" open="(" separator="," close=")">
            #{withdrawId}
        </foreach>
    </delete>

</mapper>
