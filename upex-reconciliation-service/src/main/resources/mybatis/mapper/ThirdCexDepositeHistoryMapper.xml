<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.upex.reconciliation.service.dao.mapper.cex.ThirdCexDepositeHistoryMapper">

    <resultMap id="ThirdCexDepositeHistoryMap"
               type="com.upex.reconciliation.service.dao.cex.entity.ThirdCexDepositeHistory">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cex_type" property="cexType" jdbcType="INTEGER"/>
        <result column="cex_user_id" property="cexUserId" jdbcType="VARCHAR"/>
        <result column="cex_email" property="cexEmail" jdbcType="VARCHAR" typeHandler="com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler"/>
        <result column="parent_user_id" property="parentUserId" jdbcType="VARCHAR"/>
        <result column="deposite_id" property="depositeId" jdbcType="VARCHAR"/>
        <result column="coin_name" property="coinName" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="network" property="network" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR" typeHandler="com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler"/>
        <result column="address_tag" property="addressTag" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="tx_id" property="txId" jdbcType="VARCHAR"/>
        <result column="deposite_begin_time" property="depositeBeginTime" jdbcType="TIMESTAMP"/>
        <result column="deposite_end_time" property="depositeEndTime" jdbcType="TIMESTAMP"/>
        <result column="wallet_type" property="walletType" jdbcType="INTEGER"/>
        <result column="transfer_type" property="transferType" jdbcType="INTEGER"/>
        <result column="confirm_times" property="confirmTimes" jdbcType="VARCHAR"/>
        <result column="unlock_confirm" property="unlockConfirm" jdbcType="INTEGER"/>
        <result column="fee1" property="fee1" jdbcType="DECIMAL"/>
        <result column="fee2" property="fee2" jdbcType="DECIMAL"/>
        <result column="fee1_coin" property="fee1Coin" jdbcType="VARCHAR"/>
        <result column="fee2_coin" property="fee2Coin" jdbcType="VARCHAR"/>
        <result column="check_sync_time" property="checkSyncTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        cex_type,
        cex_user_id,
        cex_email,
        parent_user_id,
        deposite_id,
        coin_name,
        amount,
        network,
        address,
        address_tag,
        status,
        tx_id,
        deposite_begin_time,
        deposite_end_time,
        wallet_type,
        transfer_type,
        confirm_times,
        unlock_confirm,
        fee1,
        fee2,
        fee1_coin,
        fee2_coin,
        check_sync_time,
        create_time,
        update_time,
        version
    </sql>

    <!-- 插入 -->
    <insert id="insert">
        INSERT INTO third_cex_deposite_history (
        cex_type,
        cex_user_id,
        cex_email,
        parent_user_id,
        deposite_id,
        coin_name,
        amount,
        network,
        address,
        address_tag,
        status,
        tx_id,
        deposite_begin_time,
        deposite_end_time,
        wallet_type,
        transfer_type,
        confirm_times,
        unlock_confirm,
        fee1,
        fee2,
        fee1_coin,
        fee2_coin,
        check_sync_time,
        create_time,
        update_time,
        version
        ) VALUES (
        #{cexType},
        #{cexUserId},
        #{cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
        #{parentUserId},
        #{depositeId},
        #{coinName},
        #{amount},
        #{network},
        #{address,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
        #{addressTag},
        #{status},
        #{txId},
        #{depositeBeginTime},
        #{depositeEndTime},
        #{walletType},
        #{transferType},
        #{confirmTimes},
        #{unlockConfirm},
        #{fee1},
        #{fee2},
        #{fee1Coin},
        #{fee2Coin},
        #{checkSyncTime},
        #{createTime},
        #{updateTime},
        #{version}
        )
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="ThirdCexDepositeHistoryMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_deposite_history
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID和交易所类型查询 -->
    <select id="selectByCexUserAndType" resultMap="ThirdCexDepositeHistoryMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_deposite_history
        WHERE cex_user_id = #{cexUserId}
        AND cex_type = #{cexType}
        ORDER BY deposite_end_time DESC
    </select>



    <!-- 按条件分页查询 -->
    <select id="selectList" resultMap="ThirdCexDepositeHistoryMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_deposite_history
        <where>
            <if test="cexUserId != null">
                AND cex_user_id = #{cexUserId}
            </if>
            <if test="cexType != null">
                AND cex_type = #{cexType}
            </if>
            <if test="coinName != null and coinName != ''">
                AND coin_name = #{coinName}
            </if>
            <if test="startTime != null">
                AND deposite_begin_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND deposite_end_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY deposite_end_time DESC
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert">
        INSERT INTO third_cex_deposite_history (
        cex_type,
        cex_user_id,
        cex_email,
        parent_user_id,
        deposite_id,
        coin_name,
        amount,
        network,
        address,
        address_tag,
        status,
        tx_id,
        deposite_begin_time,
        deposite_end_time,
        wallet_type,
        transfer_type,
        confirm_times,
        unlock_confirm,
        fee1,
        fee2,
        fee1_coin,
        fee2_coin,
        check_sync_time,
        create_time,
        update_time,
        version
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.cexType},
            #{item.cexUserId},
            #{item.cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
            #{item.parentUserId},
            #{item.depositeId},
            #{item.coinName},
            #{item.amount},
            #{item.network},
            #{item.address,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
            #{item.addressTag},
            #{item.status},
            #{item.txId},
            #{item.depositeBeginTime},
            #{item.depositeEndTime},
            #{item.walletType},
            #{item.transferType},
            #{item.confirmTimes},
            #{item.unlockConfirm},
            #{item.fee1},
            #{item.fee2},
            #{item.fee1Coin},
            #{item.fee2Coin},
            #{item.checkSyncTime},
            #{item.createTime},
            #{item.updateTime},
            #{item.version}
            )
        </foreach>
    </insert>


    <select id="selectPageByUserIds" resultMap="ThirdCexDepositeHistoryMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_deposite_history
        <where>
            <!-- 用户ID列表 -->
            <if test="cexUserIds != null and !cexUserIds.isEmpty()">
                AND cex_user_id IN
                <foreach item="userId" collection="cexUserIds" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>

            <!-- 交易所类型 -->
            <if test="depositeReq.cexType != null">
                AND cex_type = #{depositeReq.cexType}
            </if>

            <!-- 开始时间 -->
            <if test="depositeReq.startTime != null">
                AND deposite_begin_time >= #{depositeReq.startTime}
            </if>

            <!-- 结束时间 -->
            <if test="depositeReq.endTime != null">
                AND deposite_begin_time  &lt;= #{depositeReq.endTime}
            </if>

        </where>

        <!-- 分页 -->
        LIMIT #{depositeReq.offset}, #{depositeReq.pageSize}
    </select>

    <select id="countPageByUserIds" resultType="int">
        SELECT COUNT(1)
        FROM third_cex_deposite_history
        <where>
            <if test="cexUserIds != null and !cexUserIds.isEmpty()">
                AND cex_user_id IN
                <foreach item="userId" collection="cexUserIds" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="depositeReq.cexType != null">
                AND cex_type = #{depositeReq.cexType}
            </if>
            <if test="depositeReq.startTime != null">
                AND deposite_begin_time >= #{depositeReq.startTime}
            </if>
            <if test="depositeReq.endTime != null">
                AND deposite_begin_time  &lt;= #{depositeReq.endTime}
            </if>

        </where>
    </select>

    <delete id="deleteByDepositeIds">
        DELETE FROM third_cex_deposite_history
        WHERE deposite_id IN
        <foreach item="depositeId" collection="depositeIds" open="(" separator="," close=")">
            #{depositeId}
        </foreach>
    </delete>

</mapper>
