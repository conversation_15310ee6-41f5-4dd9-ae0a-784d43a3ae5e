<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.AssetsBillCoinTypePropertyMapper" >
  <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.AssetsBillCoinTypeProperty" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="biz_type" property="bizType" jdbcType="VARCHAR" />
    <result column="change_prop1" property="changeProp1" jdbcType="DECIMAL" />
    <result column="prop1" property="prop1" jdbcType="DECIMAL" />
    <result column="change_prop2" property="changeProp2" jdbcType="DECIMAL" />
    <result column="prop2" property="prop2" jdbcType="DECIMAL" />
    <result column="change_prop3" property="changeProp3" jdbcType="DECIMAL" />
    <result column="prop3" property="prop3" jdbcType="DECIMAL" />
    <result column="change_prop4" property="changeProp4" jdbcType="DECIMAL" />
    <result column="prop4" property="prop4" jdbcType="DECIMAL" />
    <result column="change_prop5" property="changeProp5" jdbcType="DECIMAL" />
    <result column="prop5" property="prop5" jdbcType="DECIMAL" />
    <result column="change_prop6" property="changeProp6" jdbcType="DECIMAL" />
    <result column="prop6" property="prop6" jdbcType="DECIMAL" />
    <result column="change_prop7" property="changeProp7" jdbcType="DECIMAL" />
    <result column="prop7" property="prop7" jdbcType="DECIMAL" />
    <result column="change_prop8" property="changeProp8" jdbcType="DECIMAL" />
    <result column="prop8" property="prop8" jdbcType="DECIMAL" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="coin_id" property="coinId" jdbcType="INTEGER" />
    <result column="check_time" property="checkTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, biz_type, change_prop1, prop1, change_prop2, prop2, change_prop3, prop3, change_prop4,
    prop4, change_prop5, prop5, change_prop6, prop6,change_prop7, prop7,
        change_prop8, prop8,create_time, update_time, coin_id, check_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from assets_bill_coin_type_property
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from assets_bill_coin_type_property
    where id = #{id,jdbcType=BIGINT}
  </delete>


  <delete id="deleteByCheckTime">
    delete from
      assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    where check_time &gt; #{checkTime}
      limit #{batchSize}
  </delete>


  <update id="batchUpdate" parameterType="java.util.List">
    update assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    <trim prefix="set" suffixOverrides=",">
      prop1=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.prop1,jdbcType=DECIMAL}
      </foreach>
      prop2=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.prop2,jdbcType=DECIMAL}
      </foreach>
      prop3=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.prop3,jdbcType=DECIMAL}
      </foreach>
      prop4=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.prop4,jdbcType=DECIMAL}
      </foreach>
      prop5=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.prop5,jdbcType=DECIMAL}
      </foreach>
      prop6=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.prop6,jdbcType=DECIMAL}
      </foreach>
      prop7=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.prop7,jdbcType=DECIMAL}
      </foreach>
      prop8=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.prop8,jdbcType=DECIMAL}
      </foreach>
      change_prop1=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.changeProp1,jdbcType=DECIMAL}
      </foreach>
      change_prop2=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.changeProp2,jdbcType=DECIMAL}
      </foreach>
      change_prop3=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.changeProp3,jdbcType=DECIMAL}
      </foreach>
      change_prop4=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.changeProp4,jdbcType=DECIMAL}
      </foreach>
      change_prop5=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.changeProp5,jdbcType=DECIMAL}
      </foreach>
      change_prop6=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.changeProp6,jdbcType=DECIMAL}
      </foreach>
      change_prop7=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.changeProp7,jdbcType=DECIMAL}
      </foreach>
      change_prop8=
      <foreach collection="list" item="item" open="case " close=" end,">
        when id = #{item.id,jdbcType=BIGINT}  then #{item.changeProp8,jdbcType=DECIMAL}
      </foreach>

    </trim>
    WHERE
    <foreach collection="list" item="item" open="( " separator=") or (" close=" )">
      id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <insert id="insertSelective" parameterType="com.upex.reconciliation.service.dao.entity.AssetsBillCoinTypeProperty" >
    insert into assets_bill_coin_type_property
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="bizType != null" >
        biz_type,
      </if>
      <if test="changeProp1 != null" >
        change_prop1,
      </if>
      <if test="prop1 != null" >
        prop1,
      </if>
      <if test="changeProp2 != null" >
        change_prop2,
      </if>
      <if test="prop2 != null" >
        prop2,
      </if>
      <if test="changeProp3 != null" >
        change_prop3,
      </if>
      <if test="prop3 != null" >
        prop3,
      </if>
      <if test="changeProp4 != null" >
        change_prop4,
      </if>
      <if test="prop4 != null" >
        prop4,
      </if>
      <if test="changeProp5 != null" >
        change_prop5,
      </if>
      <if test="prop5 != null" >
        prop5,
      </if>
      <if test="changeProp6 != null" >
        change_prop6,
      </if>
      <if test="prop6 != null" >
        prop6,
      </if>
      <if test="changeProp7 != null" >
        change_prop7,
      </if>
      <if test="prop7 != null" >
        prop7,
      </if>
      <if test="changeProp8 != null" >
        change_prop8,
      </if>
      <if test="prop8 != null" >
        prop8,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="coinId != null" >
        coin_id,
      </if>
      <if test="checkTime != null" >
        check_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="bizType != null" >
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="changeProp1 != null" >
        #{changeProp1,jdbcType=DECIMAL},
      </if>
      <if test="prop1 != null" >
        #{prop1,jdbcType=DECIMAL},
      </if>
      <if test="changeProp2 != null" >
        #{changeProp2,jdbcType=DECIMAL},
      </if>
      <if test="prop2 != null" >
        #{prop2,jdbcType=DECIMAL},
      </if>
      <if test="changeProp3 != null" >
        #{changeProp3,jdbcType=DECIMAL},
      </if>
      <if test="prop3 != null" >
        #{prop3,jdbcType=DECIMAL},
      </if>
      <if test="changeProp4 != null" >
        #{changeProp4,jdbcType=DECIMAL},
      </if>
      <if test="prop4 != null" >
        #{prop4,jdbcType=DECIMAL},
      </if>
      <if test="changeProp5 != null" >
        #{changeProp5,jdbcType=DECIMAL},
      </if>
      <if test="prop5 != null" >
        #{prop5,jdbcType=DECIMAL},
      </if>
      <if test="changeProp6 != null" >
        #{changeProp6,jdbcType=DECIMAL},
      </if>
      <if test="prop6 != null" >
        #{prop6,jdbcType=DECIMAL},
      </if>
      <if test="changeProp7 != null" >
        #{changeProp7,jdbcType=DECIMAL},
      </if>
      <if test="prop7 != null" >
        #{prop7,jdbcType=DECIMAL},
      </if>
      <if test="changeProp8 != null" >
        #{changeProp8,jdbcType=DECIMAL},
      </if>
      <if test="prop8 != null" >
        #{prop8,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="coinId != null" >
        #{coinId,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null" >
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.upex.reconciliation.service.dao.entity.AssetsBillCoinTypeProperty">
    update assets_bill_coin_type_property
    set biz_type     = #{bizType,jdbcType=VARCHAR},
        change_prop1 = #{changeProp1,jdbcType=DECIMAL},
        prop1        = #{prop1,jdbcType=DECIMAL},
        change_prop2 = #{changeProp2,jdbcType=DECIMAL},
        prop2        = #{prop2,jdbcType=DECIMAL},
        change_prop3 = #{changeProp3,jdbcType=DECIMAL},
        prop3        = #{prop3,jdbcType=DECIMAL},
        change_prop4 = #{changeProp4,jdbcType=DECIMAL},
        prop4        = #{prop4,jdbcType=DECIMAL},
        change_prop5 = #{changeProp5,jdbcType=DECIMAL},
        prop5        = #{prop5,jdbcType=DECIMAL},
        change_prop6 = #{changeProp6,jdbcType=DECIMAL},
        prop6        = #{prop6,jdbcType=DECIMAL},
        change_prop7 = #{changeProp7,jdbcType=DECIMAL},
        prop7        = #{prop7,jdbcType=DECIMAL},
        change_prop8 = #{changeProp8,jdbcType=DECIMAL},
        prop8        = #{prop8,jdbcType=DECIMAL},
        create_time  = #{createTime,jdbcType=TIMESTAMP},
        update_time  = #{updateTime,jdbcType=TIMESTAMP},
        coin_id      = #{coinId,jdbcType=INTEGER},
        check_time   = #{checkTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByCheckTime" parameterType="object" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    where check_time=#{checkTime}
    <if test="bizType != null">
      and biz_type = #{bizType,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="selectByCoinIdCheckTime" parameterType="object" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    where check_time=#{checkTime}
    <if test="coinId != null">
      and coin_id = #{coinId,jdbcType=INTEGER}
    </if>
  </select>


  <select id="selectByCoinIdTypeAndCheckTime" parameterType="object" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    where check_time=#{checkTime}
    and coin_id=#{coinId}
    and biz_type=#{bizType}
  </select>
  <insert id="insertRecord" parameterType="com.upex.reconciliation.service.dao.entity.AssetsBillCoinTypeProperty" >
    insert into assets_bill_coin_type_property_${billCheckType}_${billCheckParam} (biz_type, change_prop1,
    prop1, change_prop2, prop2,
    change_prop3, prop3, change_prop4,
    prop4, change_prop5, prop5,
    create_time, update_time, coin_id,
    check_time)
    values (
    <include refid="batch_insert_value"/>
    )
  </insert>
  <update id="updateRecord" parameterType="com.upex.reconciliation.service.dao.entity.AssetsBillCoinTypeProperty" >
    update assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    <set >
      <if test="record.bizType != null" >
        biz_type = #{record.bizType,jdbcType=VARCHAR},
      </if>
      <if test="record.changeProp1 != null" >
        change_prop1 = #{record.changeProp1,jdbcType=DECIMAL},
      </if>
      <if test="record.prop1 != null" >
        prop1 = #{record.prop1,jdbcType=DECIMAL},
      </if>
      <if test="record.changeProp2 != null" >
        change_prop2 = #{record.changeProp2,jdbcType=DECIMAL},
      </if>
      <if test="record.prop2 != null" >
        prop2 = #{record.prop2,jdbcType=DECIMAL},
      </if>
      <if test="record.changeProp3 != null" >
        change_prop3 = #{record.changeProp3,jdbcType=DECIMAL},
      </if>
      <if test="record.prop3 != null" >
        prop3 = #{record.prop3,jdbcType=DECIMAL},
      </if>
      <if test="record.changeProp4 != null" >
        change_prop4 = #{record.changeProp4,jdbcType=DECIMAL},
      </if>
      <if test="record.prop4 != null" >
        prop4 = #{record.prop4,jdbcType=DECIMAL},
      </if>
      <if test="record.changeProp5 != null" >
        change_prop5 = #{record.changeProp5,jdbcType=DECIMAL},
      </if>
      <if test="record.prop5 != null" >
        prop5 = #{record.prop5,jdbcType=DECIMAL},
      </if>
      <if test="record.changeProp6 != null" >
        change_prop6 = #{record.changeProp6,jdbcType=DECIMAL},
      </if>
      <if test="record.prop6 != null" >
        prop6 = #{record.prop6,jdbcType=DECIMAL},
      </if>
      <if test="record.changeProp7 != null" >
        change_prop7 = #{record.changeProp7,jdbcType=DECIMAL},
      </if>
      <if test="record.prop7 != null" >
        prop7 = #{record.prop7,jdbcType=DECIMAL},
      </if>
      <if test="record.changeProp8 != null" >
        change_prop8 = #{record.changeProp8,jdbcType=DECIMAL},
      </if>
      <if test="record.prop8 != null" >
        prop8 = #{record.prop8,jdbcType=DECIMAL},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.coinId != null" >
        coin_id = #{record.coinId,jdbcType=INTEGER},
      </if>
      <if test="record.checkTime != null" >
        check_time = #{record.checkTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{record.id,jdbcType=BIGINT}
  </update>
  <sql id="batch_insert_value">
    #{record.bizType,jdbcType=VARCHAR}, #{record.changeProp1,jdbcType=DECIMAL},
    #{record.prop1,jdbcType=DECIMAL}, #{record.changeProp2,jdbcType=DECIMAL}, #{record.prop2,jdbcType=DECIMAL},
    #{record.changeProp3,jdbcType=DECIMAL}, #{record.prop3,jdbcType=DECIMAL}, #{record.changeProp4,jdbcType=DECIMAL},
    #{record.prop4,jdbcType=DECIMAL}, #{record.changeProp5,jdbcType=DECIMAL}, #{record.prop5,jdbcType=DECIMAL},
    #{record.changeProp6,jdbcType=DECIMAL},
    #{record.prop6,jdbcType=DECIMAL}, #{record.changeProp7,jdbcType=DECIMAL},
    #{record.prop7,jdbcType=DECIMAL}, #{record.changeProp8,jdbcType=DECIMAL},
    #{record.prop8,jdbcType=DECIMAL},
    #{record.createTime,jdbcType=TIMESTAMP}, #{record.updateTime,jdbcType=TIMESTAMP}, #{record.coinId,jdbcType=INTEGER},
    #{record.checkTime,jdbcType=TIMESTAMP}
  </sql>
  <insert id="batchInsert">
    insert into assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    (biz_type, change_prop1,
    prop1, change_prop2, prop2,
    change_prop3, prop3, change_prop4,
    prop4, change_prop5, prop5,
    change_prop6,
    prop6,change_prop7,
    prop7,change_prop8,
    prop8,
    create_time, update_time, coin_id,
    check_time
    )
    values
    <foreach collection="records" item="record" index="index" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <include refid="batch_insert_value"/>
      </trim>
    </foreach>
  </insert>
  <select id="selectLastCheckOkTime" parameterType="object" resultType="date">
    select check_time
    from assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    where check_time &lt;= #{checkTime}
    order by check_time desc
      limit 1
  </select>
  <select id="selectMinDate" resultType="java.util.Date">
    select check_time
    from assets_bill_coin_type_property_${accountType}_${accountParam}
    where check_time &lt; #{deleteEndTime}
    order by check_time
      limit 1
  </select>
  <select id="pageListIdByTime" parameterType="com.upex.reconciliation.service.model.dto.QueryDTO" resultType="java.lang.Long">
    select id
    from assets_bill_coin_type_property_${accountType}_${accountParam}
    where check_time >= #{startTime}
      and check_time &lt; #{endTime}
      and id > #{deleteSyncPos}
    order by id
      limit #{fixLength}
  </select>
  <select id="selectTheLatestData" resultType="com.upex.reconciliation.service.dao.entity.AssetsBillCoinTypeProperty">
    select
    <include refid="Base_Column_List"/>
    from assets_bill_coin_type_property_${accountType}_${accountParam}
    order by check_time desc
    limit 1
  </select>
  <delete id="deleteAfterRecord" parameterType="object">
    delete
    from assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    where check_time &gt; #{resetCheckTime}
  </delete>
  <delete id="deleteHistoryRecord" parameterType="object">
    delete
    from assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    where check_time &lt; #{deleteEndTime}
  </delete>
  <delete id="deleteByIds">
    delete
    from assets_bill_coin_type_property_${accountType}_${accountParam}
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <select id="selectCheckTimeList" resultType="java.util.Date">
    select
      distinct(check_time) as checkTime
    from assets_bill_coin_type_property_${accountType}_${accountParam}
    where
      check_time >= #{startTime}
      and check_time &lt; #{endTime}
    order by check_time
  </select>

  <select id="selectRecordByCheckTime"  parameterType="com.upex.reconciliation.service.model.dto.QueryDTO" resultType="java.lang.Long">
    select
      id
    from
      assets_bill_coin_type_property_${accountType}_${accountParam}
    where
      check_time = #{startTime}
      and id > #{deleteSyncPos}
    order by id
      limit #{fixLength}
  </select>

  <select id="getIdByCheckTime" resultType="long">
    select max(id) from
    assets_bill_coin_type_property_${accountType}_${accountParam}
    <where>
      <if test="operation == '='">
        and check_time = #{checkTime}
      </if>
      <if test="operation == '&lt;='">
        and check_time &lt;= #{checkTime}
      </if>
    </where>
  </select>

  <delete id="deleteByMaxId">
    delete from
    assets_bill_coin_type_property_${accountType}_${accountParam}
    where id &lt; #{maxId}
    limit #{batchSize}
  </delete>

  <select id="countByCheckTime" parameterType="object" resultType="long">
    select
    count(*)
    from assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    where check_time=#{checkTime}
    <if test="bizType != null">
      and biz_type = #{bizType,jdbcType=VARCHAR}
    </if>
  </select>

  <select id="selectPageByCheckTime" parameterType="object" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    where check_time=#{checkTime}
    <if test="bizType != null">
      and biz_type = #{bizType,jdbcType=VARCHAR}
    </if>
    order by id
    limit #{startOffset},#{pageSize}
  </select>

  <select id="getCoinTypeSnapshotAssets" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    where check_time=#{snapshotTime}
    <if test="coinIdsList != null and coinIdsList.size() > 0">
      and coin_id in
      <foreach collection="coinIdsList" item="coinId" open="(" separator="," close=")">
        #{coinId}
      </foreach>
    </if>
    <if test="bizTypeList != null and bizTypeList.size() > 0">
      and biz_type in
      <foreach collection="bizTypeList" item="bizType" open="(" separator="," close=")">
        #{bizType}
      </foreach>
    </if>
  </select>

  <delete id="deleteByLtCheckTime">
    delete from
    assets_bill_coin_type_property_${billCheckType}_${billCheckParam}
    where check_time &lt; #{checkTime}
    limit #{batchSize}
  </delete>
</mapper>