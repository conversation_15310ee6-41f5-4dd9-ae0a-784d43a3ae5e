<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillCoinUserPropertyErrorMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillCoinUserPropertyError">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="coin_id" property="coinId" jdbcType="INTEGER"/>
        <result column="last_biz_id" property="lastBizId" jdbcType="INTEGER"/>
        <result column="check_time" property="checkTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="params" property="params" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Table_Name">
        bill_coin_user_property_error_${accountType}_${accountParam}
    </sql>

    <sql id="Base_Column_List">
        id,user_id,coin_id,check_time,last_biz_id,
        create_time,update_time,params
    </sql>
    <sql id="Base_Column_List_No_Id">
        user_id,coin_id,check_time,last_biz_id,
        create_time,update_time,params
    </sql>
    <sql id="Base_Column_List_Value">
        #{record.userId},#{record.coinId},#{record.checkTime},#{record.lastBizId},
        #{record.createTime},#{record.updateTime},#{record.params}
    </sql>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="Table_Name"/>
        (<include refid="Base_Column_List_No_Id"/>)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="Base_Column_List_Value"/>
            </trim>
        </foreach>
    </insert>

    <select id="selectByUserCoinAndBizId"
            resultType="com.upex.reconciliation.service.dao.entity.BillCoinUserPropertyError">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where user_id = #{userId} and coin_id = #{coinId} and last_biz_id > #{lastBizId}
        order by last_biz_id desc
        limit 1
    </select>

    <select id="selectLatestByUser"
            resultType="com.upex.reconciliation.service.dao.entity.BillCoinUserPropertyError">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where user_id = #{userId}
        order by last_biz_id desc
        limit 1
    </select>


    <delete id="batchDelete">
        delete from
        <include refid="Table_Name"/>
        where id >= #{beginId}
        limit #{pageSize}
    </delete>

    <delete id="deleteByCheckTime">
        delete from
        <include refid="Table_Name"/>
        where check_time <![CDATA[ > ]]> #{startTime} and check_time <![CDATA[ <= ]]> #{checkTime}
    </delete>

    <delete id="deleteById">
        delete from
        <include refid="Table_Name"/>
        where id = #{id}
    </delete>

    <delete id="deleteByLtCheckTime">
        delete from
        <include refid="Table_Name"/>
        where check_time &lt; #{checkTime}
        limit #{batchSize}
    </delete>

    <select id="selectMaxMinId" resultType="com.upex.reconciliation.service.model.dto.MaxMinIdDTO">
        select max(id) maxId,min(id) minId from <include refid="Table_Name"/>
    </select>

    <select id="getByStartIdAndEndId"
            resultType="com.upex.reconciliation.service.dao.entity.BillCoinUserPropertyError">
        select
        <include refid="Base_Column_List"/>
        from <include refid="Table_Name"/>
        where id &gt;= #{startId} and id &lt;= #{endId}
    </select>
</mapper>