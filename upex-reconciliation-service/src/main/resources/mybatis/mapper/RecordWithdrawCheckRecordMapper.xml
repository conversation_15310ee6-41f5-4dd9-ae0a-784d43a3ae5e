<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.ReconWithdrawCheckRecordMapper" >
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.ReconWithdrawCheckRecord" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="user_id" property="userId" jdbcType="BIGINT" />
        <result column="order_id" property="orderId" jdbcType="BIGINT" />
        <result column="trace_id" property="traceId" jdbcType="VARCHAR" />
        <result column="record_code" property="recordCode" jdbcType="VARCHAR" />
        <result column="business_source" property="businessSource" jdbcType="VARCHAR"/>
        <result column="is_pass" property="isPass" jdbcType="BIGINT"/>
        <result column="withdraw_check_result_code" property="withdrawCheckResultCode" jdbcType="VARCHAR"/>
        <result column="withdraw_check_result_name" property="withdrawCheckResultName" jdbcType="VARCHAR"/>
        <result column="request_time" property="requestTime" jdbcType="TIMESTAMP"/>
        <result column="response_time" property="responseTime" jdbcType="TIMESTAMP"/>
        <result column="check_on_time" property="checkOnTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List" >
        id, user_id, order_id, trace_id, record_code, business_source, is_pass, withdraw_check_result_code, withdraw_check_result_name, request_time, response_time, check_on_time,create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.upex.reconciliation.service.dao.entity.ReconWithdrawCheckRecord" >
        insert into recon_withdraw_check_record (id, user_id, order_id, trace_id, record_code, business_source, is_pass, withdraw_check_result_code, withdraw_check_result_name, request_time, response_time,check_on_time)
        values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT},#{traceId,jdbcType=VARCHAR}, #{recordCode,jdbcType=VARCHAR}, #{businessSource,jdbcType=VARCHAR}, #{isPass,jdbcType=BIGINT}, #{withdrawCheckResultCode,jdbcType=VARCHAR}, #{withdrawCheckResultName,jdbcType=VARCHAR},
        #{requestTime,jdbcType=TIMESTAMP},#{responseTime,jdbcType=TIMESTAMP},#{checkOnTime,jdbcType=TIMESTAMP})
    </insert>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select
        <include refid="Base_Column_List" />
        from recon_withdraw_check_record
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByUserIdAndOrderInfo" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select
        <include refid="Base_Column_List" />
        from recon_withdraw_check_record
        where user_id = #{userId,jdbcType=BIGINT}
        <if test="orderId !=null">
        and order_id = #{orderId,jdbcType=BIGINT}
        </if>
        <if test="recordCode !=null">
        and record_code = #{recordCode,jdbcType=VARCHAR}
        </if>
        limit 1
    </select>

    <select id="getUserIdByTime" resultType="long">
        select
        distinct(user_id)
        from recon_withdraw_check_record
        where id>0
        and create_time &gt;= #{startTime}
        and create_time &lt; #{endTime}
        order by id desc
        limit 10
    </select>

    <update id="updateByPrimaryKey" parameterType="com.upex.reconciliation.service.dao.entity.ReconWithdrawCheckRecord" >
        update recon_withdraw_check_record
        <set >
            <if test="userId != null" >
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null" >
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="recordCode != null" >
                record_code = #{recordCode,jdbcType=VARCHAR},
            </if>
            <if test="businessSource !=null">
                business_source = #{businessSource,jdbcType=VARCHAR},
            </if>
            <if test="isPass != null" >
                is_pass = #{isPass,jdbcType=BIGINT},
            </if>
              <if test="withdrawCheckResultCode !=null">
                  withdraw_check_result_code = #{withdrawCheckResultCode,jdbcType=BIGINT},
              </if>
            <if test="withdrawCheckResultName !=null">
                  withdraw_check_result_name = #{withdrawCheckResultName,jdbcType=VARCHAR},
              </if>
            <if test="requestTime != null" >
                request_time = #{requestTime,jdbcType=TIMESTAMP},
            </if>
            <if test="responseTime !=null">
                response_time = #{responseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkOnTime !=null">
                check_on_time = #{checkOnTime,jdbcType=TIMESTAMP},
            </if>
            <if test="traceId != null" >
                trace_id = #{traceId,jdbcType=VARCHAR}
            </if>
            <where>
                id = #{id,jdbcType=BIGINT}
            </where>
        </set>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.upex.reconciliation.service.dao.entity.ReconWithdrawCheckRecord" >
         update recon_withdraw_check_record
        <set >
            <if test="userId != null" >
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null" >
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="recordCode != null" >
                record_code = #{recordCode,jdbcType=VARCHAR},
            </if>
            <if test="businessSource !=null">
                business_source = #{businessSource,jdbcType=VARCHAR},
            </if>
            <if test="isPass != null" >
                is_pass = #{isPass,jdbcType=BIGINT},
            </if>
            <if test="withdrawCheckResultCode !=null">
                withdraw_check_result_code = #{withdrawCheckResultCode,jdbcType=BIGINT},
            </if>
            <if test="withdrawCheckResultName !=null">
                withdraw_check_result_name = #{withdrawCheckResultName,jdbcType=VARCHAR},
            </if>
            <if test="requestTime != null" >
                request_time = #{requestTime,jdbcType=TIMESTAMP},
            </if>
            <if test="responseTime !=null">
                response_time = #{responseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="checkOnTime !=null">
                check_on_time = #{checkOnTime,jdbcType=TIMESTAMP}
            </if>
            <if test="traceId != null" >
                trace_id = #{traceId,jdbcType=VARCHAR},
            </if>
            <where>
                id > 0
                <if test="id !=null">
                    and id = #{id,jdbcType=BIGINT}
                </if>
                <if test="userId !=null">
                    and user_id = #{userId,jdbcType=BIGINT}
                </if>
                <if test="orderId !=null">
                  and  order_id = #{orderId,jdbcType=BIGINT}
                </if>
                <if test="recordCode !=null">
                   and record_code = #{recordCode,jdbcType=VARCHAR}
                </if>
            </where>
        </set>
    </update>
</mapper>