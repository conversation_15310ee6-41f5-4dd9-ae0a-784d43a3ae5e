<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.upex.reconciliation.service.dao.mapper.cex.ThirdCexUserConfigMapper">

    <resultMap id="ThirdCexUserConfigMap" type="com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cex_email" property="cexEmail" jdbcType="VARCHAR" typeHandler="com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler"/>
        <result column="cex_user_id" property="cexUserId" jdbcType="VARCHAR"/>
        <result column="cex_type" property="cexType" jdbcType="INTEGER"/>
        <result column="user_admin_id" property="userAdminId" jdbcType="VARCHAR"/>
        <result column="user_admin_email" property="userAdminEmail" jdbcType="VARCHAR"/>
        <result column="api_key_label" property="apiKeyLabel" jdbcType="VARCHAR"/>
        <result column="api_key_pub" property="apiKeyPub" jdbcType="VARCHAR"/>
        <result column="api_key_private" property="apiKeyPrivate" jdbcType="VARCHAR" typeHandler="com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler"/>
        <result column="api_key" property="apiKey" jdbcType="VARCHAR"/>
        <result column="read_only" property="readOnly" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="api_permmit" property="apiPermit" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            cex_email,
            cex_user_id,
            cex_type,
            user_admin_id,
            user_admin_email,
            api_key_label,
            api_key_pub,
            api_key_private,
            api_key,
            read_only,
            api_permmit,
            status,
            create_time,
            update_time
    </sql>
    
    <select id="selectById" resultMap="ThirdCexUserConfigMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_user_config
        WHERE id = #{id}
    </select>


    <select id="selectByCexTypeAndUserId"  resultMap="ThirdCexUserConfigMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_user_config
        WHERE cex_user_id = #{cexUserId}
        and cex_type = #{cexType}
    </select>

    <insert id="insert">
        INSERT INTO third_cex_user_config (
            cex_email,
            cex_user_id,
            cex_type,
            user_admin_id,
            user_admin_email,
            api_key_label,
            api_key_pub,
            api_key_private,
            api_key,
            read_only,
            api_permmit,
            status,
            create_time,
            update_time
        ) VALUES (
            #{cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
            #{cexUserId},
            #{cexType},
            #{userAdminId},
            #{userAdminEmail},
            #{apiKeyLabel},
            #{apiKeyPub},
            #{apiKeyPrivate,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
            #{apiKey},
            #{readOnly},
            #{apiPermit},
            #{status},
            #{createTime},
            #{updateTime}
        )
    </insert>

    <update id="updateReadOnly">
        UPDATE third_cex_user_config
        <set>
                read_only = #{readOnly},
                update_time =now()
        </set>
        WHERE cex_user_id = #{cexUserId}
        and cex_type = #{cexType}
    </update>

    <update id="update">
        UPDATE third_cex_user_config
        <set>
            <if test="record.cexEmail != null">
                cex_email = #{record.cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
            </if>
            <if test="record.cexUserId != null">
                cex_user_id = #{record.cexUserId},
            </if>
            <if test="record.cexType != null">
                cex_type = #{record.cexType},
            </if>
            <if test="record.userAdminId != null">
                user_admin_id = #{record.userAdminId},
            </if>
            <if test="record.userAdminEmail != null">
                user_admin_email = #{record.userAdminEmail},
            </if>
            <if test="record.apiKeyLabel != null">
                api_key_label = #{record.apiKeyLabel},
            </if>
            <if test="record.apiKeyPub != null">
                api_key_pub = #{record.apiKeyPub},
            </if>
            <if test="record.apiKey != null">
                api_key = #{record.apiKey},
            </if>
            <if test="record.readOnly != null">
                read_only = #{record.readOnly},
            </if>
            <if test="record.apiPermit != null">
                api_permmit = #{record.apiPermit},
            </if>
            <if test="record.status !=null">
                status = #{record.status},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime}
            </if>
        </set>
        WHERE id = #{record.id}
    </update>


    <delete id="deleteById">
        DELETE FROM third_cex_user_config
        WHERE id = #{id}
    </delete>

    <select id="selectAll" resultMap="ThirdCexUserConfigMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_user_config
    </select>
</mapper>
