<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillMetricSceneMapper">
  <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillMetricScene">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="crontab_expr" jdbcType="VARCHAR" property="crontabExpr" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, status, name, note, crontab_expr, type, create_time, version
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_metric_scene
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_metric_scene
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <select id="listByStatus" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bill_metric_scene
    where status = #{status,jdbcType=TINYINT}
  </select>


  <insert id="insert" parameterType="com.upex.reconciliation.service.dao.entity.BillMetricScene">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bill_metric_scene (status, name, note, 
      crontab_expr, type, create_time, 
      version)
    values (#{status,jdbcType=TINYINT}, #{name,jdbcType=VARCHAR}, #{note,jdbcType=VARCHAR}, 
      #{crontabExpr,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{version,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.upex.reconciliation.service.dao.entity.BillMetricScene">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bill_metric_scene
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="status != null">
        status,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="crontabExpr != null">
        crontab_expr,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="crontabExpr != null">
        #{crontabExpr,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <update id="updateByExampleSelective" parameterType="map">
      update bill_metric_scene
      <set>
          <if test="record.id != null">
              id = #{record.id,jdbcType=BIGINT},
          </if>
          <if test="record.status != null">
              status = #{record.status,jdbcType=TINYINT},
          </if>
          <if test="record.name != null">
              name = #{record.name,jdbcType=VARCHAR},
          </if>
          <if test="record.note != null">
              note = #{record.note,jdbcType=VARCHAR},
          </if>
          <if test="record.crontabExpr != null">
              crontab_expr = #{record.crontabExpr,jdbcType=VARCHAR},
          </if>
          <if test="record.type != null">
              type = #{record.type,jdbcType=VARCHAR},
          </if>
          <if test="record.createTime != null">
              create_time = #{record.createTime,jdbcType=TIMESTAMP},
          </if>
          <if test="record.version != null">
              version = #{record.version,jdbcType=BIGINT},
          </if>
      </set>
      <if test="_parameter != null">
          <include refid="Update_By_Example_Where_Clause"/>
      </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bill_metric_scene
    set id = #{record.id,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT},
      name = #{record.name,jdbcType=VARCHAR},
      note = #{record.note,jdbcType=VARCHAR},
      crontab_expr = #{record.crontabExpr,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      version = #{record.version,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.upex.reconciliation.service.dao.entity.BillMetricScene">
    update bill_metric_scene
    <set>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="crontabExpr != null">
        crontab_expr = #{crontabExpr,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.upex.reconciliation.service.dao.entity.BillMetricScene">
    update bill_metric_scene
    set status = #{status,jdbcType=TINYINT},
      name = #{name,jdbcType=VARCHAR},
      note = #{note,jdbcType=VARCHAR},
      crontab_expr = #{crontabExpr,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <sql id="MysqlSuffix">
    <if test="page != null">
      <![CDATA[ limit #{page.begin} , #{page.length} ]]>
    </if>
  </sql>
</mapper>