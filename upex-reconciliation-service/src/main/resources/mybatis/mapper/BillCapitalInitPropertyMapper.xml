<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillCapitalInitPropertyMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillCapitalInitProperty">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="account_type" property="accountType" jdbcType="VARCHAR"/>
        <result column="account_param" property="accountParam" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <result column="business_type_name" property="businessTypeName" jdbcType="VARCHAR"/>
        <result column="coin_id" property="coinId" jdbcType="INTEGER"/>
        <result column="symbol_id" property="symbolId" jdbcType="VARCHAR"/>
        <result column="params" property="params" jdbcType="VARCHAR"/>
        <result column="init_value" property="initValue" jdbcType="DECIMAL"/>
        <result column="check_time" property="checkTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, account_type,account_param,coin_id,symbol_id,business_type,business_type_name,
        init_value,check_time,create_time,update_time,params
    </sql>
    <sql id="Base_Column_List_No_Id">
        account_type,account_param,coin_id,symbol_id,business_type,business_type_name,
        init_value,check_time,create_time,update_time,params
    </sql>
    <sql id="Base_Column_List_Value">
        #{record.accountType},#{record.accountParam},#{record.coinId},#{record.symbolId},#{record.businessType},#{record.businessTypeName},
        #{record.initValue},#{record.checkTime},#{record.createTime},#{record.updateTime},#{record.params}
    </sql>
    <sql id="Base_Table_Name">
        bill_capital_init_property
    </sql>

    <select id="selectRecords" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Base_Table_Name"></include>
        where account_type = #{accountType} and account_param = #{accountParam} and business_type = #{businessType}
    </select>

    <select id="selectRecordsByBusinessType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Base_Table_Name"></include>
        where business_type = #{businessType}
    </select>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert  into
        <include refid="Base_Table_Name"/>
        (<include refid="Base_Column_List_No_Id"/>)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="Base_Column_List_Value"/>
            </trim>
        </foreach>
    </insert>

    <delete id="deleteById">
        delete from
        <include refid="Base_Table_Name"/>
        where id = #{id}
    </delete>

    <delete id="deleteByCheckTime">
        delete from
        <include refid="Base_Table_Name"/>
        where account_type = #{accountType}
        and account_param = #{accountParam}
        and business_type = #{businessType}
        and check_time = #{checkTime}
    </delete>

    <delete id="deleteByBusinessType">
        delete from
        <include refid="Base_Table_Name"/>
        where account_type = #{accountType}
        and account_param = #{accountParam}
        and business_type = #{businessType}
    </delete>
</mapper>