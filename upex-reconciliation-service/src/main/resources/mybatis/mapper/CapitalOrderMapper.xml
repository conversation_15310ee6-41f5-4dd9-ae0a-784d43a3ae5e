<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.CapitalOrderMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillCapitalOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="source_id" jdbcType="BIGINT" property="sourceId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="coin_id" jdbcType="INTEGER" property="coinId"/>
        <result column="chain_coin_id" jdbcType="INTEGER" property="chainCoinId"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="fees" jdbcType="DECIMAL" property="fees"/>
        <result column="biz_type" jdbcType="TINYINT" property="bizType"/>
        <result column="biz_sub_type" jdbcType="TINYINT" property="bizSubType"/>
        <result column="tx_id" jdbcType="VARCHAR" property="txId"/>
        <result column="source" jdbcType="TINYINT" property="source"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="risk_status" jdbcType="TINYINT" property="riskStatus"/>
        <result column="params" jdbcType="VARCHAR" property="params"/>
        <result column="risk_remark" jdbcType="VARCHAR" property="riskRemark"/>
        <result column="source_create_date" jdbcType="TIMESTAMP" property="sourceCreateDate"/>
        <result column="source_update_date" jdbcType="TIMESTAMP" property="sourceUpdateDate"/>
        <result column="bill_time" jdbcType="TIMESTAMP" property="billTime"/>
        <result column="fee_coin_id" jdbcType="INTEGER" property="feeCoinId"/>
        <result column="burn_fee" jdbcType="DECIMAL" property="burnFee"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="address" jdbcType="LONGVARCHAR" property="address"/>
        <result column="address_hash" jdbcType="VARCHAR" property="addressHash"/>
        <result column="address_ls" jdbcType="LONGVARCHAR" property="addressLs"/>
        <result column="address_ls_hash" jdbcType="VARCHAR" property="addressLsHash"/>
        <result column="from_address" jdbcType="VARCHAR" property="fromAddress"/>
        <result column="from_address_crypt" jdbcType="LONGVARCHAR" property="fromAddressCrypt"/>
        <result column="from_address_hash" jdbcType="VARCHAR" property="fromAddressHash"/>
    </resultMap>
    <sql id="Base_Column_List">
        id , source_id, order_id, user_id, coin_id, chain_coin_id, amount, fees, biz_type,
    biz_sub_type, tx_id, `source`, `status`, risk_status, params, risk_remark, source_create_date, 
    source_update_date, bill_time, fee_coin_id, burn_fee, create_date, update_date,
    address, address_hash, address_ls, address_ls_hash, from_address, from_address_crypt, from_address_hash
    </sql>
    <sql id="Table_Name">
        capital_order
    </sql>

    <sql id="batch_insert_value">
        #{record.sourceId, jdbcType=BIGINT},
        #{record.orderId, jdbcType=BIGINT},
        #{record.userId, jdbcType=BIGINT},
        #{record.coinId, jdbcType=INTEGER},
        #{record.chainCoinId, jdbcType=INTEGER},
        #{record.amount, jdbcType=DECIMAL},
        #{record.fees, jdbcType=DECIMAL},
        #{record.bizType, jdbcType=TINYINT},
        #{record.bizSubType, jdbcType=TINYINT},
        #{record.txId, jdbcType=VARCHAR},
        #{record.source, jdbcType=TINYINT},
        #{record.status, jdbcType=TINYINT},
        #{record.riskStatus, jdbcType=TINYINT},
        #{record.params, jdbcType=VARCHAR},
        #{record.riskRemark, jdbcType=VARCHAR},
        #{record.sourceCreateDate, jdbcType=TIMESTAMP},
        #{record.sourceUpdateDate, jdbcType=TIMESTAMP},
        #{record.billTime, jdbcType=TIMESTAMP},
        #{record.feeCoinId, jdbcType=INTEGER},
        #{record.burnFee, jdbcType=DECIMAL},
        #{record.createDate, jdbcType=TIMESTAMP},
        #{record.address, jdbcType=LONGVARCHAR},
        #{record.address_hash, jdbcType=VARCHAR},
        #{record.address_ls, jdbcType=LONGVARCHAR},
        #{record.address_ls_hash, jdbcType=VARCHAR},
        #{record.from_address, jdbcType=VARCHAR},
        #{record.from_address_crypt, jdbcType=LONGVARCHAR},
        #{record.from_address_hash, jdbcType=VARCHAR}
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="Table_Name"/> (
        id, source_id, order_id, user_id, coin_id, chain_coin_id, amount, fees, biz_type, biz_sub_type,
        tx_id, source, status, risk_status, params, risk_remark, source_create_date, source_update_date,
        bill_time, fee_coin_id, burn_fee, create_date, update_date,
        address, address_hash, address_ls, address_ls_hash, from_address, from_address_crypt, from_address_hash
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id}, #{item.sourceId}, #{item.orderId}, #{item.userId}, #{item.coinId}, #{item.chainCoinId},
            #{item.amount}, #{item.fees}, #{item.bizType}, #{item.bizSubType}, #{item.txId}, #{item.source},
            #{item.status}, #{item.riskStatus}, #{item.params}, #{item.riskRemark}, #{item.sourceCreateDate},
            #{item.sourceUpdateDate}, #{item.billTime}, #{item.feeCoinId}, #{item.burnFee}, #{item.createDate},
            #{item.updateDate}, #{item.address}, #{item.addressHash}, #{item.addressLs}, #{item.addressLsHash},
            #{item.fromAddress}, #{item.fromAddressCrypt}, #{item.fromAddressHash}
            )
        </foreach>
    </insert>
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE capital_order
            SET
            source_id = #{item.sourceId},
            user_id = #{item.userId},
            coin_id = #{item.coinId},
            chain_coin_id = #{item.chainCoinId},
            amount = #{item.amount},
            fees = #{item.fees},
            biz_type = #{item.bizType},
            biz_sub_type = #{item.bizSubType},
            tx_id = #{item.txId},
            source = #{item.source},
            status = #{item.status},
            risk_status = #{item.riskStatus},
            params = #{item.params},
            risk_remark = #{item.riskRemark},
            source_create_date = #{item.sourceCreateDate},
            source_update_date = #{item.sourceUpdateDate},
            <if test="item.billTime != null">
                bill_time = #{item.billTime},
            </if>
            fee_coin_id = #{item.feeCoinId},
            burn_fee = #{item.burnFee},
            address = #{item.address},
            address_hash = #{item.addressHash},
            address_ls = #{item.addressLs},
            address_ls_hash = #{item.addressLsHash},
            from_address = #{item.fromAddress},
            from_address_crypt = #{item.fromAddressCrypt},
            from_address_hash = #{item.fromAddressHash},
            update_date = #{item.updateDate}
            WHERE order_id = #{item.orderId}
        </foreach>
    </update>
    <select id="selectByOrderNo" resultType="com.upex.reconciliation.service.dao.entity.BillCapitalOrder">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where order_id = #{orderNo}
    </select>
    <select id="selectAllRechargeFailureAmount"
            resultType="com.upex.reconciliation.service.dao.entity.BillCapitalOrder">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="Table_Name"/>
        WHERE
        STATUS in
        <foreach collection="statusList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and biz_type = 1
        and biz_sub_type in (1, 4, 5)
    </select>
    <select id="selectSysWalletCompensateRechargeFailureAmount"
            resultType="com.upex.reconciliation.service.dao.entity.BillCapitalOrder">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="Table_Name"/>
        WHERE
        STATUS = 1
        and biz_type = 1
        and biz_sub_type = 5
    </select>
    <select id="queryRechargeChain" resultType="com.upex.reconciliation.service.dao.entity.BillCapitalOrder">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="Table_Name"/>
        WHERE
        STATUS = 11
        and biz_type = 1
        and biz_sub_type = 1
    </select>
    <select id="selectByOrderNoList" resultType="com.upex.reconciliation.service.dao.entity.BillCapitalOrder">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where order_id in
        <foreach collection="orderNoList" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
    </select>
    <insert id="batchInsertOrUpdate">
        insert into
        <include refid="Table_Name"/>
        (source_id, order_id, user_id, coin_id, chain_coin_id, amount, fees, biz_type,
        biz_sub_type, tx_id, `source`, `status`, risk_status, params, risk_remark, source_create_date,
        source_update_date, bill_time, fee_coin_id, burn_fee, create_date, update_date,
        address, address_hash, address_ls, address_ls_hash, from_address, from_address_crypt, from_address_hash)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
        on DUPLICATE KEY UPDATE
        source_id = values(source_id),
        order_id = values(order_id),
        user_id = values(user_id),
        coin_id = values(coin_id),
        chain_coin_id = values(chain_coin_id),
        amount = values(amount),
        fees = values(fees),
        biz_type = values(biz_type),
        biz_sub_type = values(biz_sub_type),
        tx_id = values(tx_id),
        `source` = values(`source`),
        status = values(status),
        risk_status = values(risk_status),
        params = values(params),
        risk_remark = values(risk_remark),
        source_create_date = values(source_create_date),
        source_update_date = values(source_update_date),
        bill_time = IF(bill_time IS NOT NULL, bill_time, values(bill_time)),
        fee_coin_id = values(fee_coin_id),
        burn_fee = values(burn_fee),
        address = values(address),
        address_hash = values(address_hash),
        address_ls = values(address_ls),
        address_ls_hash = values(address_ls_hash),
        from_address = values(from_address),
        from_address_crypt = values(from_address_crypt),
        from_address_hash = values(from_address_hash),
        update_date = values(update_date),
    </insert>
</mapper>