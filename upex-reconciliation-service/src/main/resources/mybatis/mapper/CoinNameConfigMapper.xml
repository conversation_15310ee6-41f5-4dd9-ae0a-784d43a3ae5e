<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.upex.reconciliation.service.dao.mapper.cex.CoinNameConfigMapper">

    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.cex.entity.CoinNameConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cex_type" property="cexType" jdbcType="INTEGER"/>
        <result column="cex_coin_name" property="cexCoinName" jdbcType="VARCHAR"/>
        <result column="bg_coin_name" property="bgCoinName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, cex_type, cex_coin_name, bg_coin_name,
        create_time, update_time
    </sql>
    <!-- 插入 -->
    <insert id="insert">
        INSERT INTO coin_name_config (
        cex_type, cex_coin_name, bg_coin_name,
        create_time, update_time
        ) VALUES (
        #{cexType}, #{cexCoinName}, #{bgCoinName},
        #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 批量插入 -->
    <insert id="batchInsert">
        INSERT INTO coin_name_config (
        cex_type, cex_coin_name, bg_coin_name,
        create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.cexType}, #{item.cexCoinName}, #{item.bgCoinName},
            #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 根据交易所类型和币种名称查询 BG 币种名称 -->
    <select id="selectBgCoinNameByCexTypeAndCexCoinName" resultType="string">
        SELECT bg_coin_name
        FROM coin_name_config
        WHERE cex_type = #{cexType}
        AND cex_coin_name = #{cexCoinName}
    </select>

    <!-- 根据交易所类型和币种名称查询完整配置 -->
    <select id="selectByCexTypeAndCexCoinName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM coin_name_config
        WHERE cex_type = #{cexType}
        AND cex_coin_name = #{cexCoinName}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"></include>
        FROM coin_name_config
    </select>
</mapper>