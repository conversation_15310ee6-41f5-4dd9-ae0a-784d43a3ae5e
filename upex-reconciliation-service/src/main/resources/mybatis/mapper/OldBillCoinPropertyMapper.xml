<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.OldBillCoinPropertyMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="coin_id" property="coinId" jdbcType="INTEGER"/>
        <result column="check_time" property="checkTime" jdbcType="TIMESTAMP"/>
        <result column="change_prop1" property="changeProp1" jdbcType="DECIMAL"/>
        <result column="prop1" property="prop1" jdbcType="DECIMAL"/>
        <result column="change_prop2" property="changeProp2" jdbcType="DECIMAL"/>
        <result column="prop2" property="prop2" jdbcType="DECIMAL"/>
        <result column="change_prop3" property="changeProp3" jdbcType="DECIMAL"/>
        <result column="prop3" property="prop3" jdbcType="DECIMAL"/>
        <result column="change_prop4" property="changeProp4" jdbcType="DECIMAL"/>
        <result column="prop4" property="prop4" jdbcType="DECIMAL"/>
        <result column="change_prop5" property="changeProp5" jdbcType="DECIMAL"/>
        <result column="prop5" property="prop5" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , coin_id, check_time, change_prop1, prop1, change_prop2, prop2, change_prop3,
    prop3, change_prop4, prop4, change_prop5, prop5, create_time, update_time
    </sql>

    <sql id="batch_insert_value">
        #{record.coinId,jdbcType=INTEGER}
        ,
        #{record.checkTime,jdbcType=TIMESTAMP},
        #{record.changeProp1,jdbcType=DECIMAL},
        #{record.prop1,jdbcType=DECIMAL},
        #{record.changeProp2,jdbcType=DECIMAL},
        #{record.prop2,jdbcType=DECIMAL},
        #{record.changeProp3,jdbcType=DECIMAL},
        #{record.prop3,jdbcType=DECIMAL},
        #{record.changeProp4,jdbcType=DECIMAL},
        #{record.prop4,jdbcType=DECIMAL},
        #{record.changeProp5,jdbcType=DECIMAL},
        #{record.prop5,jdbcType=DECIMAL},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP}
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_property_${accountType}_${accountParam}
        where coin_id = #{coinId} and check_time=#{checkTime}
    </select>


    <select id="selectRange" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_property_${accountType}_${accountParam}
        where check_time=#{checkTime}
        and id &gt; #{minId}
        limit #{size}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from bill_coin_property_${accountType} _${accountParam}
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty"
            useGeneratedKeys="true" keyProperty="id">
        insert into bill_coin_property_${accountType}_${accountParam} (coin_id,
      check_time, change_prop1, prop1, 
      change_prop2, prop2, change_prop3, 
      prop3, change_prop4, prop4, 
      change_prop5, prop5, create_time, 
      update_time)
        values (#{record.coinId,jdbcType=INTEGER}, #{record.checkTime,jdbcType=TIMESTAMP}, #{record.changeProp1,jdbcType=DECIMAL}, #{record.prop1,jdbcType=DECIMAL}, #{record.changeProp2,jdbcType=DECIMAL}, #{record.prop2,jdbcType=DECIMAL}, #{record.changeProp3,jdbcType=DECIMAL}, #{record.prop3,jdbcType=DECIMAL}, #{record.changeProp4,jdbcType=DECIMAL}, #{record.prop4,jdbcType=DECIMAL}, #{record.changeProp5,jdbcType=DECIMAL}, #{record.prop5,jdbcType=DECIMAL}, #{record.createTime,jdbcType=TIMESTAMP}, #{record.updateTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into bill_coin_property_${accountType}_${accountParam} (coin_id,
        check_time, change_prop1, prop1,
        change_prop2, prop2, change_prop3,
        prop3, change_prop4, prop4,
        change_prop5, prop5, create_time,
        update_time)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <insert id="insertSelective" parameterType="com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty">
        insert into bill_coin_property_${accountType}_${accountParam}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="coinId != null">
                coin_id,
            </if>
            <if test="checkTime != null">
                check_time,
            </if>
            <if test="changeProp1 != null">
                change_prop1,
            </if>
            <if test="prop1 != null">
                prop1,
            </if>
            <if test="changeProp2 != null">
                change_prop2,
            </if>
            <if test="prop2 != null">
                prop2,
            </if>
            <if test="changeProp3 != null">
                change_prop3,
            </if>
            <if test="prop3 != null">
                prop3,
            </if>
            <if test="changeProp4 != null">
                change_prop4,
            </if>
            <if test="prop4 != null">
                prop4,
            </if>
            <if test="changeProp5 != null">
                change_prop5,
            </if>
            <if test="prop5 != null">
                prop5,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="coinId != null">
                #{coinId,jdbcType=INTEGER},
            </if>
            <if test="checkTime != null">
                #{checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="changeProp1 != null">
                #{changeProp1,jdbcType=DECIMAL},
            </if>
            <if test="prop1 != null">
                #{prop1,jdbcType=DECIMAL},
            </if>
            <if test="changeProp2 != null">
                #{changeProp2,jdbcType=DECIMAL},
            </if>
            <if test="prop2 != null">
                #{prop2,jdbcType=DECIMAL},
            </if>
            <if test="changeProp3 != null">
                #{changeProp3,jdbcType=DECIMAL},
            </if>
            <if test="prop3 != null">
                #{prop3,jdbcType=DECIMAL},
            </if>
            <if test="changeProp4 != null">
                #{changeProp4,jdbcType=DECIMAL},
            </if>
            <if test="prop4 != null">
                #{prop4,jdbcType=DECIMAL},
            </if>
            <if test="changeProp5 != null">
                #{changeProp5,jdbcType=DECIMAL},
            </if>
            <if test="prop5 != null">
                #{prop5,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty">
        update bill_coin_property_${accountType}_${accountParam}
        <set>
            <if test="record.coinId != null">
                coin_id = #{record.coinId,jdbcType=INTEGER},
            </if>
            <if test="record.checkTime != null">
                check_time = #{record.checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.changeProp1 != null">
                change_prop1 = #{record.changeProp1,jdbcType=DECIMAL},
            </if>
            <if test="record.prop1 != null">
                prop1 = #{record.prop1,jdbcType=DECIMAL},
            </if>
            <if test="record.changeProp2 != null">
                change_prop2 = #{record.changeProp2,jdbcType=DECIMAL},
            </if>
            <if test="record.prop2 != null">
                prop2 = #{record.prop2,jdbcType=DECIMAL},
            </if>
            <if test="record.changeProp3 != null">
                change_prop3 = #{record.changeProp3,jdbcType=DECIMAL},
            </if>
            <if test="record.prop3 != null">
                prop3 = #{record.prop3,jdbcType=DECIMAL},
            </if>
            <if test="record.changeProp4 != null">
                change_prop4 = #{record.changeProp4,jdbcType=DECIMAL},
            </if>
            <if test="record.prop4 != null">
                prop4 = #{record.prop4,jdbcType=DECIMAL},
            </if>
            <if test="record.changeProp5 != null">
                change_prop5 = #{record.changeProp5,jdbcType=DECIMAL},
            </if>
            <if test="record.prop5 != null">
                prop5 = #{record.prop5,jdbcType=DECIMAL},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where coin_id = #{record.coinId,jdbcType=INTEGER} and check_time=#{record.checkTime,jdbcType=TIMESTAMP}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty">
        update bill_coin_property_${accountType}_${accountParam}
        set coin_id      = #{coinId,jdbcType=INTEGER},
            check_time   = #{checkTime,jdbcType=TIMESTAMP},
            change_prop1 = #{changeProp1,jdbcType=DECIMAL},
            prop1        = #{prop1,jdbcType=DECIMAL},
            change_prop2 = #{changeProp2,jdbcType=DECIMAL},
            prop2        = #{prop2,jdbcType=DECIMAL},
            change_prop3 = #{changeProp3,jdbcType=DECIMAL},
            prop3        = #{prop3,jdbcType=DECIMAL},
            change_prop4 = #{changeProp4,jdbcType=DECIMAL},
            prop4        = #{prop4,jdbcType=DECIMAL},
            change_prop5 = #{changeProp5,jdbcType=DECIMAL},
            prop5        = #{prop5,jdbcType=DECIMAL},
            create_time  = #{createTime,jdbcType=TIMESTAMP},
            update_time  = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_property_${accountType}_${accountParam}
        where check_time = #{checkTime,jdbcType=TIMESTAMP}
    </select>

    <select id="selectByCheckTimeFirst" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_property_${accountType}_${accountParam}
        where check_time = #{checkTime,jdbcType=TIMESTAMP}
        order by id limit 1
    </select>

    <update id="updateById">
        update bill_coin_property_${accountType}_${accountParam}
        set
        is_last = #{isLast}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectDataCheckPropertys" resultMap="BaseResultMap">
        select
        r.id, r.coin_id, r.check_time, r.change_prop1, r.prop1, r.change_prop2, r.prop2, r.change_prop3,
        r.prop3, r.change_prop4, r.prop4, r.change_prop5, r.prop5, r.create_time, r.update_time
        from (
        select DISTINCT
        <include refid="Base_Column_List"/>
        from bill_coin_property_${accountType}_${accountParam}
        order by check_time desc) r
        group by r.coin_id
    </select>

    <select id="selectAssetsByEndTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_property_${accountType}_${accountParam}
        where check_time = (
        select check_time
        from bill_coin_property_${accountType}_${accountParam}
        where check_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        order by check_time desc
        limit 1
        )
    </select>

    <select id="selectLastCheckTime" parameterType="object" resultType="java.util.Date">
        select check_time
        from bill_coin_property_${accountType} _${accountParam}
        order by id desc limit 1
    </select>


    <select id="selectAllAssets" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_property_${accountType}_${accountParam}
        where 1=1
        <if test="checkTime != null">
            AND check_time=#{checkTime}
        </if>
    </select>

    <select id="selectSingleUser" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_property_${accountType}_${accountParam}
        where 1=1
        <if test="checkTime != null">
            AND check_time=#{checkTime}
        </if>
    </select>

    <select id="selectTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_property_${accountType}_${accountParam}
        where check_time &lt;= #{checkTime} order by check_time desc limit 1
    </select>
    <delete id="deleteAfterRecord" parameterType="object">
        delete
        from bill_coin_property_${accountType} _${accountParam}
        where check_time &gt; #{resetCheckTime}
    </delete>
    <delete id="deleteHistoryRecord" parameterType="object">
        delete
        from bill_coin_property_${accountType} _${accountParam}
        where check_time &lt; #{deleteEndTime}
    </delete>

    <select id="listIdByPage" parameterType="object" resultType="java.lang.Long">
        select id
        from bill_coin_property_${accountType} _${accountParam}
        where check_time &lt; #{deleteEndTime}
        order by check_time
            limit #{fixLength}
    </select>
    <select id="selectMinDate" resultType="java.util.Date">
        select check_time
        from bill_coin_property_${accountType} _${accountParam}
        where check_time &lt; #{deleteEndTime}
        order by check_time limit 1
    </select>

    <delete id="deleteByIds" parameterType="object">
        delete
        from bill_coin_property_${accountType}_${accountParam}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCheckTimeList" resultType="java.util.Date">
        select distinct(check_time) as checkTime
        from bill_coin_property_${accountType}_${accountParam}
        where check_time >= #{startTime}
          and check_time &lt; #{endTime}
        order by check_time
    </select>

    <select id="selectLastByCoinId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_property_${accountType}_${accountParam}
        where coin_id = #{coinId}
        and check_time &lt;= #{checkTime}
        order by check_time desc
        limit 1
    </select>

    <select id="selectLastByCoinIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_property_${accountType}_${accountParam}
        where check_time = #{checkTime}
        and coin_id in
        <foreach collection="coinIds" item="coin" open="(" separator="," close=")">
            #{coin}
        </foreach>
    </select>
</mapper>