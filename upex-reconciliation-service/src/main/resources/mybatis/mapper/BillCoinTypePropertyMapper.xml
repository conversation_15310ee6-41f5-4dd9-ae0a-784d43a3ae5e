<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillCoinTypePropertyMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="coin_id" property="coinId" jdbcType="INTEGER"/>
        <result column="check_time" property="checkTime" jdbcType="TIMESTAMP"/>
        <result column="biz_type" property="bizType" jdbcType="VARCHAR"/>
        <result column="change_prop1" property="changeProp1" jdbcType="DECIMAL" />
        <result column="prop1" property="prop1" jdbcType="DECIMAL" />
        <result column="change_prop2" property="changeProp2" jdbcType="DECIMAL" />
        <result column="prop2" property="prop2" jdbcType="DECIMAL" />
        <result column="change_prop3" property="changeProp3" jdbcType="DECIMAL" />
        <result column="prop3" property="prop3" jdbcType="DECIMAL" />
        <result column="change_prop4" property="changeProp4" jdbcType="DECIMAL" />
        <result column="prop4" property="prop4" jdbcType="DECIMAL" />
        <result column="change_prop5" property="changeProp5" jdbcType="DECIMAL" />
        <result column="prop5" property="prop5" jdbcType="DECIMAL" />
        <result column="change_prop6" property="changeProp6" jdbcType="DECIMAL" />
        <result column="prop6" property="prop6" jdbcType="DECIMAL" />
        <result column="change_prop7" property="changeProp7" jdbcType="DECIMAL" />
        <result column="prop7" property="prop7" jdbcType="DECIMAL" />
        <result column="change_prop8" property="changeProp8" jdbcType="DECIMAL" />
        <result column="prop8" property="prop8" jdbcType="DECIMAL" />
        <!--        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>-->
        <!--        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>-->
        <result column="params" property="params" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List" >
        id, coin_id, check_time, biz_type, change_prop1, prop1, change_prop2, prop2, change_prop3, prop3,
        change_prop4, prop4, change_prop5, prop5, change_prop6, prop6, change_prop7, prop7, change_prop8, prop8, create_time, update_time, params
    </sql>

    <sql id="Base_Column_List_No_Id">
        coin_id, check_time, biz_type, change_prop1, prop1, change_prop2, prop2, change_prop3, prop3,
        change_prop4, prop4, change_prop5, prop5, change_prop6, prop6, change_prop7, prop7, change_prop8, prop8, create_time, update_time, params
    </sql>

    <sql id="Base_Column_List_Value">
        #{record.coinId},#{record.checkTime},#{record.bizType},
        #{record.changeProp1},#{record.prop1},#{record.changeProp2},
        #{record.prop2},#{record.changeProp3},#{record.prop3},#{record.changeProp4},#{record.prop4},
        #{record.changeProp5},#{record.prop5},
        #{record.changeProp6},#{record.prop6},
        #{record.changeProp7},#{record.prop7},
        #{record.changeProp8},#{record.prop8},
        #{record.createTime},
        #{record.updateTime},#{record.params}
    </sql>

    <sql id="Table_Name">
        bill_coin_type_property_${accountType}_${accountParam}
    </sql>

    <select id="selectAssetsByEndTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{endTime,jdbcType=TIMESTAMP}
    </select>



    <select id="selectByCoinIdCheckTimeNotInBizTypes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        and coin_id = #{coinId}
        and biz_type not in
        <foreach collection="notInBizTypes" item="bizType" open="(" separator="," close=")">
            #{bizType}
        </foreach>
    </select>


    <select id="selectByCoinIdCheckTimeInBizTypes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        and coin_id = #{coinId}
        and biz_type in
        <foreach collection="inBizTypes" item="bizType" open="(" separator="," close=")">
            #{bizType}
        </foreach>
    </select>

    <select id="selectAllCoinByCheckTimeInBizTypes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        and biz_type in
        <foreach collection="inBizTypes" item="bizType" open="(" separator="," close=")">
            #{bizType}
        </foreach>
    </select>



    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where id = #{id}
    </select>

    <update id="updateById" parameterType="com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty">
        update
        <include refid="Table_Name"/>
        set
        prop1 = #{data.prop1,jdbcType=DECIMAL},
        prop2 = #{data.prop2,jdbcType=DECIMAL},
        prop3 = #{data.prop3,jdbcType=DECIMAL},
        prop4 = #{data.prop4,jdbcType=DECIMAL},
        prop5 = #{data.prop5,jdbcType=DECIMAL},
        prop6 = #{data.prop6,jdbcType=DECIMAL},
        prop7 = #{data.prop7,jdbcType=DECIMAL},
        prop8 = #{data.prop8,jdbcType=DECIMAL}
        where id = #{data.id,jdbcType=BIGINT}
    </update>

    <delete id="batchDelete">
        delete from
        <include refid="Table_Name"/>
        where id >= #{beginId}
        limit #{pageSize}
    </delete>

    <delete id="deleteByCheckTime">
        delete from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
    </delete>

    <insert id="batchInsert">
        insert into
        <include refid="Table_Name"/>
        (
        <include refid="Base_Column_List_No_Id"></include>
        )
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="Base_Column_List_Value"/>
            </trim>
        </foreach>
    </insert>

    <select id="listBillBetweenTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time &gt;#{startTime}
        and check_time &lt;=#{endTime}
    </select>

    <delete id="deleteAfterRecord" parameterType="object">
        delete
        from
        <include refid="Table_Name"/>
        where check_time &gt; #{resetCheckTime}
    </delete>

    <select id="selectCheckTimeRecordPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        and id &gt; #{minId}
        order by id asc
        limit #{pageSize}
    </select>

    <select id="selectRangeCheckTimeRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        order by id asc
    </select>


    <select id="selectByCoinIdCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        and coin_id = #{coinId}
        order by id asc
    </select>

    <select id="selectBizTypeCheckTimeRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        and biz_type = #{bizType}
        order by id asc
    </select>


    <select id="selectBizTypeCoinId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_time = #{checkTime}
        and biz_type = #{bizType}
        and coin_id = #{coinId}
        order by id asc
    </select>

    <select id="getIdByCheckTime" resultType="long">
        select max(id) from
        <include refid="Table_Name"/>
        <where>
            <if test="operation == '='">
                and check_time = #{checkTime}
            </if>
            <if test="operation == '&lt;='">
                and check_time &lt;= #{checkTime}
            </if>
        </where>
    </select>

    <delete id="deleteByMaxId">
        delete from
        <include refid="Table_Name"/>
        where id &lt; #{maxId}
        limit #{batchSize}
    </delete>

    <delete id="deleteByLtCheckTime">
        delete from
        <include refid="Table_Name"/>
        where check_time &lt; #{checkTime}
        limit #{batchSize}
    </delete>
</mapper>