<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillConfigMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sync_pos" property="syncPos" jdbcType="BIGINT"/>
        <result column="check_ok_time" property="checkOkTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="consume_offset" property="consumeOffset" jdbcType="VARCHAR"/>
        <result column="init_time" property="initTime" jdbcType="TIMESTAMP"/>

    </resultMap>

    <sql id="Table_Name">
        bill_config_${accountType}_${accountParam}
    </sql>

    <sql id="Base_Column_List">
        id, sync_pos, check_ok_time, create_time,
        update_time,consume_offset,init_time
    </sql>


    <delete id="delBillDateForReset">
        delete from from
        <include refid="Table_Name"/>
        where check_ok_time > #{checkTime}
    </delete>


    <sql id="batch_insert_value">
        #{record.syncPos,jdbcType=BIGINT},#{record.checkOkTime,jdbcType=TIMESTAMP},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP},#{record.consumeOffset,jdbcType=VARCHAR},#{record.initTime,jdbcType=TIMESTAMP}
    </sql>

    <select id="selectByTypeAndParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        order by id desc
        limit 1;
    </select>


    <select id="selectByTypeAndParamAndCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_ok_time = #{checkTime}
        order by id desc
        limit 1;
    </select>

    <select id="selectFirstByTypeAndParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        order by id
        limit 1;
    </select>


    <select id="selectByTypeAndParamAfterCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_ok_time &gt; #{checkTime}
        order by id desc;
    </select>


    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into<include refid="Table_Name"/>(sync_pos, check_ok_time,
        create_time,
        update_time,consume_offset,init_time)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <delete id="batchDelete">
        delete from
        <include refid="Table_Name"/>
        where id >= #{beginId}
        limit #{pageSize}
    </delete>

    <delete id="deleteByCheckTime">
        delete from
        <include refid="Table_Name"/>
        where check_ok_time = #{checkTime}
    </delete>

    <delete id="deleteByGtCheckTime">
        delete from
        <include refid="Table_Name"/>
        where check_ok_time > #{checkTime}
    </delete>

    <delete id="cleanData">
        delete from bill_coin_property_50_default;
        delete from bill_coin_type_property_50_default;
        delete from bill_coin_user_property_snapshot_50_default;
        delete from bill_coin_user_property_50_default;
        delete from bill_config;
        delete from bill_symbol_coin_property_50_default;
        delete from bill_symbol_property_50_default;
        delete from bill_symbol_trading_config;
        delete from bill_user_position_50_default;
        delete from tb_bill_idempotent_record_50_default;
    </delete>



    <update id="updateOffset" parameterType="com.upex.reconciliation.service.dao.entity.BillConfig">
        update
        <include refid="Table_Name"/>
        set
            consume_offset =  #{record.consumeOffset,jdbcType=VARCHAR},
        where id =  #{record.id,jdbcType=BIGINT}
    </update>


    <update id="createBillUserTable" parameterType="com.upex.reconciliation.service.dao.entity.BillConfig">
        CREATE TABLE IF NOT EXISTS `bill_user_${accountType}_${accountParam}`
        (
        `id` bigint
        (
        20
        ) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
        `user_id` bigint
        (
        20
        ) NOT NULL COMMENT '用户id',
        `check_ok_time` datetime
        (
        3
        ) NOT NULL COMMENT '最后一次检查成功时间',
        `create_time` datetime
        (
        3
        ) NOT NULL COMMENT '创建时间',
        `update_time` datetime
        (
        3
        ) NOT NULL COMMENT '最后更新时间',
        PRIMARY KEY
        (
        `id`
        ),
        UNIQUE KEY `uniq_userId`
        (
        `user_id`
        ) USING BTREE
        ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='用户信息表';
    </update>

    <update id="createBillCoinTable" parameterType="com.upex.reconciliation.service.dao.entity.BillConfig">
        CREATE TABLE IF NOT EXISTS `bill_coin_property_${accountType}_${accountParam}` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
        `coin_id` int(11) NOT NULL COMMENT '币种',
        `check_time` datetime(3) NOT NULL COMMENT '资产检查时间',
        `change_prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1变化',
        `prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1',
        `change_prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2变化',
        `prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2',
        `change_prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3变化',
        `prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3',
        `change_prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4变化',
        `prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4',
        `change_prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5变化',
        `prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5',
        `create_time` datetime(3) NOT NULL COMMENT '创建时间',
        `update_time` datetime(3) NOT NULL COMMENT '最后更新时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `uniq_checkTime_coinId` (`check_time`,`coin_id`) USING BTREE,
        KEY `idx_checkTime` (`check_time`) USING BTREE
        ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='按照币维度账单数据明细'


    </update>

    <update id="createBillCoinTypeTable" parameterType="com.upex.reconciliation.service.dao.entity.BillConfig">


        CREATE TABLE IF NOT EXISTS `bill_coin_type_property_${accountType}_${accountParam}` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
        `biz_type` varchar(32) NOT NULL COMMENT '业务类型',
        `change_prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1变化',
        `prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1',
        `change_prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2变化',
        `prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2',
        `change_prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3变化',
        `prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3',
        `change_prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4变化',
        `prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4',
        `change_prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5变化',
        `prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5',
        `create_time` datetime(3) NOT NULL COMMENT '创建时间',
        `update_time` datetime(3) NOT NULL COMMENT '最后更新时间',
        `coin_id` int(11) NOT NULL COMMENT '币种',
        `check_time` datetime(3) NOT NULL COMMENT '检查时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `uniq_checkTime_coinId_bizType` (`check_time`,`coin_id`,`biz_type`) USING BTREE
        ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='按照币+业务类型维度汇总数据'
    </update>

    <update id="createBillCoinUser" parameterType="com.upex.reconciliation.service.dao.entity.BillConfig">
        CREATE TABLE IF NOT EXISTS `bill_coin_user_property_${accountType}_${accountParam}` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
        `user_id` bigint(20) NOT NULL COMMENT '用户userId',
        `coin_id` int(11) NOT NULL COMMENT '币种',
        `check_time` datetime(3) NOT NULL COMMENT '资产检查时间',
        `change_prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1变化',
        `prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1',
        `change_prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2变化',
        `prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2',
        `change_prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3变化',
        `prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3',
        `change_prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4变化',
        `prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4',
        `change_prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5变化',
        `prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5',
        `create_time` datetime(3) NOT NULL COMMENT '创建时间',
        `update_time` datetime(3) NOT NULL COMMENT '最后更新时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `uniq_userId_checkTime_coinId` (`user_id`,`check_time`,`coin_id`) USING BTREE,
        KEY `idx_checkTime` (`check_time`) USING BTREE
        ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='按照用户+币种维度汇总数据'

    </update>

    <update id="createBillCoinTypeUserTable" parameterType="com.upex.reconciliation.service.dao.entity.BillConfig">

        CREATE TABLE `bill_coin_type_user_property_${accountType}_${accountParam}` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
        `biz_type` varchar(32) NOT NULL COMMENT '业务类型',
        `change_prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1变化',
        `prop1` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值1',
        `change_prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2变化',
        `prop2` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值2',
        `change_prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3变化',
        `prop3` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值3',
        `change_prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4变化',
        `prop4` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值4',
        `change_prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5变化',
        `prop5` decimal(28,16) NOT NULL DEFAULT '0.****************' COMMENT '资产值5',
        `create_time` datetime(3) NOT NULL COMMENT '创建时间',
        `update_time` datetime(3) NOT NULL COMMENT '最后更新时间',
        `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
        `coin_id` int(11) NOT NULL COMMENT '币种',
        `check_time` datetime(3) NOT NULL COMMENT '检查时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `uniq_userId_checkTime_coinId_bizType` (`user_id`,`check_time`,`coin_id`,`biz_type`) USING BTREE
        ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='按照用户+币种+业务类型维度汇总数据'
    </update>

    <select id="listAllBillConfigs" resultMap="BaseResultMap" parameterType="object">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
    </select>
    <select id="selectById" resultMap="BaseResultMap" parameterType="object">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where id=#{id,jdbcType=BIGINT}
    </select>

    <select id="selectMinCheckOkTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        order by check_ok_time asc limit 1
    </select>

    <select id="selectLastByLimit" resultType="com.upex.reconciliation.service.dao.entity.BillConfig">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        <if test="checkTime != null">
            where check_ok_time &lt;= #{checkTime}
        </if>
        order by check_ok_time desc
        limit #{limit}
    </select>

    <select id="selectByLtCheckTimeAsc" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where check_ok_time &lt; #{checkTime}
        order by check_ok_time
        limit #{batchSize};
    </select>

    <select id="selectLatestCheckTime" resultType="java.util.Date">
        select
            check_ok_time
        from
        <include refid="Table_Name"/>
        order by check_ok_time desc
        limit 1;
    </select>
</mapper>