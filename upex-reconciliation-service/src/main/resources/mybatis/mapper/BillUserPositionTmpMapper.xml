<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillUserPositionTmpMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillUserPositionTmp">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="symbol_id" jdbcType="VARCHAR" property="symbolId"/>
        <result column="token_id" jdbcType="VARCHAR" property="tokenId"/>
        <result column="coin_id" jdbcType="INTEGER" property="coinId"/>
        <result column="check_ok_time" jdbcType="TIMESTAMP" property="checkOkTime"/>
        <result column="biz_time" jdbcType="TIMESTAMP" property="bizTime"/>
        <result column="l_pos_mgn" jdbcType="DECIMAL" property="lPosMgn"/>
        <result column="l_count" jdbcType="DECIMAL" property="lCount"/>
        <result column="l_avg" jdbcType="DECIMAL" property="lAvg"/>
        <result column="s_pos_mgn" jdbcType="DECIMAL" property="sPosMgn"/>
        <result column="s_count" jdbcType="DECIMAL" property="sCount"/>
        <result column="s_avg" jdbcType="DECIMAL" property="sAvg"/>
        <result column="realized" jdbcType="DECIMAL" property="realized"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="account_type" property="accountType" jdbcType="TINYINT"/>
        <result column="account_param" property="accountParam" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, symbol_id, token_id, coin_id, check_ok_time, biz_time,
        l_pos_mgn, l_count, l_avg, s_pos_mgn, s_count, s_avg, create_time, update_time, realized,account_type,account_param
    </sql>
    <sql id="Base_Column_List_No_Id">
        user_id, symbol_id, token_id, coin_id, check_ok_time, biz_time,
        l_pos_mgn, l_count, l_avg, s_pos_mgn, s_count, s_avg,
        create_time, update_time, realized,account_type,account_param
    </sql>
    <sql id="Base_Column_List_Value">
        #{record.userId},
        #{record.symbolId},
        #{record.tokenId},
        #{record.coinId},
        #{record.checkOkTime},
        #{record.bizTime},
        #{record.lPosMgn},
        #{record.lCount},
        #{record.lAvg},
        #{record.sPosMgn},
        #{record.sCount},
        #{record.sAvg},
        #{record.createTime},
        #{record.updateTime},
        #{record.realized},
        #{record.accountType},
        #{record.accountParam}
    </sql>
    <sql id="Base_Table_Name">
        bill_user_position_tmp
    </sql>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="Base_Table_Name"></include>
        (
        <include refid="Base_Column_List_No_Id"></include>
        )
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="Base_Column_List_Value"/>
            </trim>
        </foreach>
    </insert>

    <delete id="deleteByUserId">
        delete from
        <include refid="Base_Table_Name"/>
        where account_type = #{accountType}
        and account_param = #{accountParam}
        and user_id = #{userId}
        limit #{pageSize}
    </delete>
    <delete id="deleteByCheckTime">
        delete from
        <include refid="Base_Table_Name"/>
        where check_ok_time = #{checkTime}
        limit #{pageSize}
    </delete>
</mapper>