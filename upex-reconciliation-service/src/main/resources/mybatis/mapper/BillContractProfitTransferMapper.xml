<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillContractProfitTransferMapper">

    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <id property="bizId" column="biz_id" jdbcType="BIGINT"/>
            <id property="batchNo" column="batch_no" jdbcType="BIGINT"/>
            <result property="checkOkTime" column="check_ok_time" jdbcType="TIMESTAMP"/>
            <result property="accountType" column="account_type" jdbcType="TINYINT"/>
            <result property="accountParam" column="account_param" jdbcType="VARCHAR"/>
            <result property="toAccountType" column="to_account_type" jdbcType="TINYINT"/>
            <result property="toAccountParam" column="to_account_param" jdbcType="VARCHAR"/>
            <result property="coinId" column="coin_id" jdbcType="INTEGER"/>
            <result property="transferCount" column="transfer_count" jdbcType="DECIMAL"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="sourceType" column="source_type" jdbcType="INTEGER"/>
            <result property="transferType" column="transfer_type" jdbcType="INTEGER"/>
            <result property="transferTime" column="transfer_time" jdbcType="TIMESTAMP"/>
            <result property="transferInUserId" column="transfer_in_user_id" jdbcType="BIGINT"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,biz_id,batch_no,
        check_ok_time,account_type,account_param,
        to_account_type,to_account_param,coin_id,
        transfer_count,status,source_type,
        transfer_type,transfer_time,transfer_in_user_id,
        version,create_time,update_time
    </sql>

    <sql id="Table_Name">
        <choose>
            <when test="_parameter.containsKey('tableSuffix') and tableSuffix != null and tableSuffix != ''">
                bill_contract_profit_transfer_${tableSuffix}
            </when>
            <otherwise>
                bill_contract_profit_transfer
            </otherwise>
        </choose>
    </sql>

    <sql id="batch_insert_value">
        #{record.id,jdbcType=BIGINT},
        #{record.bizId,jdbcType=BIGINT},
        #{record.batchNo,jdbcType=BIGINT},
        #{record.checkOkTime,jdbcType=TIMESTAMP},
        #{record.accountType,jdbcType=TINYINT},
        #{record.accountParam,jdbcType=VARCHAR},
        #{record.toAccountType,jdbcType=TINYINT},
        #{record.toAccountParam,jdbcType=VARCHAR},
        #{record.coinId,jdbcType=INTEGER},
        #{record.transferCount,jdbcType=DECIMAL},
        #{record.status,jdbcType=INTEGER},
        #{record.sourceType,jdbcType=INTEGER},
        #{record.transferType,jdbcType=INTEGER},
        #{record.transferTime,jdbcType=TIMESTAMP},
        #{record.transferInUserId,jdbcType=INTEGER},
        #{record.version,jdbcType=BIGINT},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP}
    </sql>

    <select id="selectByCheckOkTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="Table_Name" />
        where  check_ok_time > #{timeStart,jdbcType=TIMESTAMP}
        and check_ok_time <![CDATA[ <= ]]>  #{timeEnd,jdbcType=TIMESTAMP}
        and account_type = #{accountType,jdbcType=TINYINT}
        and account_param = #{accountParam,jdbcType=VARCHAR}
        and source_type = #{sourceType,jdbcType=INTEGER}
    </select>

    <select id="selectAllByCheckOkTime"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="Table_Name" />
        where  check_ok_time <![CDATA[ <= ]]> #{checkOkTime,jdbcType=TIMESTAMP}
        and status in ( 0,1) limit #{pageSize}
    </select>

    <select id="selectAllByIdList"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="Table_Name" />
        where status in (0,1,3)
        and id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectDelayedTransactionByStatusAndTime" resultMap="BaseResultMap">
        select coin_id             coin_id,
               sum(transfer_count) transfer_count
        from <include refid="Table_Name" />
        where status in (0, 3)
        and check_ok_time <![CDATA[ <= ]]> #{checkOkTime}
        group by coin_id
        having sum(transfer_count) != 0
    </select>

    <select id="selectEachDelayedTransactionByStatusAndTime" resultMap="BaseResultMap">
        select coin_id             coin_id,
               account_type , account_param,
        sum(transfer_count) transfer_count
        from <include refid="Table_Name" />
        force index(idx_check_ok_time)
        where   check_ok_time > #{startTime}
            and check_ok_time <![CDATA[ <= ]]> #{checkOkTime}
            and to_account_type = #{toAccountType}
        group by coin_id
        having sum(transfer_count) != 0
    </select>



    <select id="selectDelayedTransactionByTime" resultMap="BaseResultMap">
        select coin_id             coin_id,
               sum(transfer_count) transfer_count
        from <include refid="Table_Name" />
        where status = 2 and transfer_time > #{checkOkTime}
        and check_ok_time <![CDATA[ <= ]]> #{checkOkTime}
        group by coin_id
        having sum(transfer_count) != 0
    </select>

    <select id="selectListByToAccountTypeStatusAndTime" resultMap="BaseResultMap">
        select coin_id             coin_id,
               sum(transfer_count) transfer_count
        from <include refid="Table_Name" />
        where status in (0, 3)
        and check_ok_time <![CDATA[ <= ]]> #{checkOkTime}
        and to_account_type = #{toAccountType}
        and transfer_type in
        <foreach collection="transferTypes" item="transferType" open="(" separator="," close=")">
            #{transferType}
        </foreach>
        group by coin_id
        having sum(transfer_count) != 0
    </select>

    <select id="selectListByToAccountTypeTime" resultMap="BaseResultMap">
        select coin_id             coin_id,
               sum(transfer_count) transfer_count
        from <include refid="Table_Name" />
        where status = 2 and transfer_time > #{checkOkTime}
        and check_ok_time <![CDATA[ <= ]]> #{checkOkTime}
        and to_account_type = #{toAccountType}
        and transfer_type in
        <foreach collection="transferTypes" item="transferType" open="(" separator="," close=")">
            #{transferType}
        </foreach>
        group by coin_id
        having sum(transfer_count) != 0
    </select>

    <update id="updateStatus">
        update
        <include refid="Table_Name"/>
        set
        status = #{statusNew,jdbcType=INTEGER},
        version = #{versionNew,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
        and status = #{statusOld,jdbcType=INTEGER}
        and version = #{version,jdbcType=BIGINT}
    </update>

    <update id="updateStatusWithTransferTime">
        update
        <include refid="Table_Name"/>
        set
        status = #{statusNew,jdbcType=INTEGER},
        version = version + 1,
        <if test="transferTime != null">
            transfer_time = #{transferTime,jdbcType=TIMESTAMP},
        </if>
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
        and status = #{statusOld,jdbcType=INTEGER}
        and version = #{version,jdbcType=BIGINT}
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer" useGeneratedKeys="true">
        insert ignore into <include refid="Table_Name" /> (
            id,
            biz_id,
            batch_no,
            check_ok_time,
            account_type,
            account_param,
            to_account_type,
            to_account_param,
            coin_id,
            transfer_count,
            status,
            source_type,
            transfer_type,
            transfer_time,
            transfer_in_user_id,
            version,
            create_time,
            update_time)
        values
        <foreach collection="list" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <select id="selectTransferByIds"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="Table_Name" />
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and status = #{status}
        and source_type = #{sourceType}
    </select>
    <select id="selectDelayedTransactionByTimeAndType"
            resultType="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer">
        select to_account_type to_account_type,
        to_account_param to_account_param,
        coin_id coin_id,
        sum(transfer_count) transfer_count
        from
        <include refid="Table_Name"/>
        where status = 2 and transfer_time > #{checkOkTime}
        and check_ok_time <![CDATA[ <= ]]> #{checkOkTime}
        and transfer_type in
        <foreach collection="transferTypes" item="transferType" open="(" separator="," close=")">
            #{transferType}
        </foreach>
        group by to_account_type,
        to_account_param,
        coin_id
        having sum(transfer_count) != 0
    </select>
    <select id="selectDelayedTransactionByStatusAndTimeAndType"
            resultType="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer">
        select to_account_type to_account_type,
        to_account_param to_account_param,
        coin_id coin_id,
        sum(transfer_count) transfer_count
        from <include refid="Table_Name" />
        where status in (0, 3)
        and check_ok_time <![CDATA[ <= ]]> #{checkOkTime}
        and transfer_type in
        <foreach collection="transferTypes" item="transferType" open="(" separator="," close=")">
            #{transferType}
        </foreach>
        group by to_account_type,
        to_account_param,
        coin_id
        having sum(transfer_count) != 0
    </select>
    <select id="selectTransferRecordsByStatusAndTime"
            resultType="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer">
        select
        <include refid="Base_Column_List" />
        from <include refid="Table_Name" />
        where status = #{status}
    </select>

    <delete id="deleteByCheckTime">
        delete from
        <include refid="Table_Name"/>
        where check_ok_time = #{checkTime}
        and account_type = #{accountType}
        and account_param=#{accountParam}
    </delete>

    <delete id="batchDelete">
        delete from
        <include refid="Table_Name"/>
        where id >= #{beginId}
        limit #{pageSize}
    </delete>

    <select id="selectListByAccountTypeAndCheckTime"
            resultType="com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where account_type = #{accountType}
        and account_param = #{accountParam}
        and check_ok_time = #{checkTime}
    </select>

    <update id="createTable">
        CREATE TABLE IF NOT EXISTS <include refid="Table_Name"></include> (
        `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
        `biz_id` bigint unsigned DEFAULT NULL COMMENT '业务id',
        `batch_no` bigint unsigned NOT NULL COMMENT '批次号',
        `check_ok_time` datetime(3) NOT NULL COMMENT '对账成功时间',
        `account_type` tinyint NOT NULL COMMENT '账户类型',
        `account_param` varchar(32) NOT NULL COMMENT '账户类型',
        `to_account_type` tinyint DEFAULT NULL COMMENT '转入账户类型',
        `to_account_param` varchar(32) DEFAULT NULL COMMENT '转入账户类型',
        `coin_id` int NOT NULL COMMENT '币种',
        `transfer_count` decimal(36, 16) NOT NULL DEFAULT '0.****************' COMMENT '动账金额',
        `status` int DEFAULT NULL COMMENT '状态 默认为0-未执行，1-执行成功，2-执行成功， 3-执行失败',
        `source_type` int NOT NULL COMMENT '数据来源 ',
        `transfer_type` int NOT NULL COMMENT '动账类型',
        `transfer_time` datetime(3) DEFAULT NULL COMMENT '动账时间',
        `transfer_in_user_id` bigint NOT NULL COMMENT '动账uid',
        `version` bigint NOT NULL COMMENT '数据版本号',
        `create_time` datetime(3) NOT NULL COMMENT '创建时间',
        `update_time` datetime(3) NOT NULL COMMENT '最后更新时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `biz_id_source_type` (`biz_id`, `source_type`),
        KEY `idx_type_param_source_time` (
        `account_type`,
        `account_param`,
        `source_type`,
        `check_ok_time`
        ) USING BTREE,
        KEY `idx_check_ok_time` (`check_ok_time`) USING BTREE,
        KEY `idx_status_transfer_time` (`status`, `transfer_time`) USING BTREE
        ) DEFAULT CHARSET = utf8 COMMENT = '盈亏动账记录表';
    </update>

    <select id="selectAllUnProfitTransferByCheckOkTime" resultMap="BaseResultMap">
        select coin_id, sum(transfer_count) transfer_count
        from (
            select coin_id, transfer_count
            from <include refid="Table_Name" />
            where
            status = 2
            and check_ok_time &lt;= #{checkOkTime}
            and transfer_time &gt; #{checkOkTime}
            and account_type in
            <foreach collection="accountTypeList" item="accountType" open="(" separator="," close=")">
                #{accountType}
            </foreach>
            and transfer_type in
            <foreach collection="transferTypes" item="transferType" open="(" separator="," close=")">
                #{transferType}
            </foreach>
            union all
            select coin_id, transfer_count
            from <include refid="Table_Name" />
            where
            status in(0,3)
            and check_ok_time &lt;= #{checkOkTime}
            and account_type in
            <foreach collection="accountTypeList" item="accountType" open="(" separator="," close=")">
                #{accountType}
            </foreach>
            and transfer_type in
            <foreach collection="transferTypes" item="transferType" open="(" separator="," close=")">
                #{transferType}
            </foreach>
        ) as a
        group by coin_id
        having sum(transfer_count) != 0
    </select>

    <select id="getNotProfitTransferMinCheckOkTime" resultType="java.util.Date">
        select min(check_ok_time) from <include refid="Table_Name"/> where  status in (0,3)
    </select>

    <select id="selectAllByToAccountTypeAndCheckOkTime"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="Table_Name" />
        where  check_ok_time <![CDATA[ <= ]]> #{checkOkTime,jdbcType=TIMESTAMP}
        and status in ( 0,1)
        and to_account_type in
        <foreach collection="toAccountTypeList" item="toAccountType" open="(" separator="," close=")">
            #{toAccountType}
        </foreach>
        limit #{pageSize}
    </select>
    <select id="getUserUnProfitTransfer"  resultMap="BaseResultMap">
        select coin_id, sum(transfer_count) transfer_count
        from <include refid="Table_Name" />
        where
        status = 2
        and check_ok_time = #{checkOkTime}
        and transfer_time &gt; #{transferTime}
        and transfer_in_user_id = #{userId}
        and to_account_type in
        <foreach collection="toAccountTypes" item="accountType" open="(" separator="," close=")">
            #{accountType}
        </foreach>
        and transfer_type in
        <foreach collection="transferTypes" item="transferType" open="(" separator="," close=")">
            #{transferType}
        </foreach>
        group by coin_id
        having transfer_count != 0
    </select>
</mapper>
