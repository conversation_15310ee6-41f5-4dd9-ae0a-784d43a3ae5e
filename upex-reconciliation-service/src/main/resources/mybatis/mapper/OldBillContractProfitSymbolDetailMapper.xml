<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.OldBillContractProfitSymbolDetailMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillContractProfitSymbolDetail">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="batchNo" column="batch_no" jdbcType="BIGINT"/>
        <result property="checkOkTime" column="check_ok_time" jdbcType="TIMESTAMP"/>
        <result property="accountType" column="account_type" jdbcType="TINYINT"/>
        <result property="accountParam" column="account_param" jdbcType="VARCHAR"/>
        <result property="profitType" column="profit_type" jdbcType="VARCHAR"/>
        <result property="coinId" column="coin_id" jdbcType="INTEGER"/>
        <result property="symbolId" column="symbol_id" jdbcType="VARCHAR"/>
        <result property="initCount" column="init_count" jdbcType="DECIMAL"/>
        <result property="realizedCount" column="realized_count" jdbcType="DECIMAL"/>
        <result property="unrealizedCount" column="unrealized_count" jdbcType="DECIMAL"/>
        <result property="profitCount" column="profit_count" jdbcType="DECIMAL"/>
        <result property="profitCountIncr" column="profit_count_incr" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,batch_no,check_ok_time,account_type,
        account_param,profit_type,coin_id,
        symbol_id,init_count,realized_count,
        unrealized_count,profit_count,profit_count_incr,
        create_time,update_time
    </sql>
    <sql id="Table_Name">
        bill_contract_profit_symbol_detail_${accountType}_${accountParam}
    </sql>

    <sql id="Table_Name_His">
        bill_contract_profit_symbol_detail_${accountType}_${accountParam}_his
    </sql>

    <sql id="batch_insert_value">
        #{record.id,jdbcType=BIGINT},
        #{record.batchNo,jdbcType=BIGINT},
        #{record.checkOkTime,jdbcType=TIMESTAMP},
        #{record.accountType,jdbcType=TINYINT}
        ,#{record.accountParam,jdbcType=VARCHAR},
        #{record.profitType,jdbcType=VARCHAR},
        #{record.coinId,jdbcType=INTEGER}
        ,#{record.symbolId,jdbcType=VARCHAR},
        #{record.initCount,jdbcType=DECIMAL},
        #{record.realizedCount,jdbcType=DECIMAL}
        ,#{record.unrealizedCount,jdbcType=DECIMAL},
        #{record.profitCount,jdbcType=DECIMAL},
        #{record.profitCountIncr,jdbcType=DECIMAL}
        ,#{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP}
    </sql>

    <select id="selectListByAccountTypeAndCheckTime"
            resultType="com.upex.reconciliation.service.dao.entity.BillContractProfitSymbolDetail">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where account_type = #{accountType}
        and account_param = #{accountParam}
        and check_ok_time = #{checkTime}
    </select>
</mapper>
