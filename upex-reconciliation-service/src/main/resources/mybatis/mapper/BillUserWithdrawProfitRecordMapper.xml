<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillUserWithdrawProfitRecordMapper" >
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillUserWithdrawProfitRecord" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="request_id" property="requestId" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="BIGINT" />
        <result column="parent_user_id" property="parentUserId" jdbcType="BIGINT" />
        <result column="coin_id" property="coinId" jdbcType="INTEGER" />
        <result column="order_id" property="orderId" jdbcType="BIGINT" />
        <result column="profit_usdt_amount" property="profitUsdtAmount" jdbcType="DECIMAL" />
        <result column="profit_count" property="profitCount" jdbcType="DECIMAL" />
        <result column="profit_source" property="profitSource" jdbcType="INTEGER" />
        <result column="record_code" property="recordCode" jdbcType="VARCHAR" />
        <result column="business_source" property="businessSource" jdbcType="VARCHAR"/>
        <result column="request_time" property="requestTime" jdbcType="TIMESTAMP"/>
        <result column="check_begin_time" property="checkBeginTime" jdbcType="TIMESTAMP"/>
        <result column="check_end_time" property="checkEndTime" jdbcType="TIMESTAMP"/>
        <result column="check_ok_time" property="checkOkTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="recon_profit_total_amount" property="reconProfitTotalAmount" jdbcType="DECIMAL" />
        <result column="data_profit_total_amount" property="dataProfitTotalAmount" jdbcType="DECIMAL" />
    </resultMap>
    <sql id="Base_Column_List" >
        id,request_id, user_id, parent_user_id, coin_id, order_id,profit_usdt_amount,profit_count,profit_source,record_code, business_source,request_time, check_begin_time, check_end_time,check_ok_time,create_time, update_time,recon_profit_total_amount,data_profit_total_amount
    </sql>
    <sql id="Base_Column_List_No_Id" >
         request_id,user_id,  parent_user_id, coin_id, order_id,profit_usdt_amount,profit_count,profit_source,record_code, business_source,  request_time, check_begin_time, check_end_time,check_ok_time,create_time, update_time,recon_profit_total_amount,data_profit_total_amount
    </sql>
    <sql id="Base_Column_List_Value">
        #{record.requestId},#{record.userId},#{record.parentUserId},#{record.coinId},#{record.orderId},
        #{record.profitUsdtAmount},#{record.profitCount},#{record.profitSource},#{record.recordCode},#{record.businessSource},#{record.requestTime},#{record.checkBeginTime},#{record.checkEndTime},
        #{record.checkOkTime},#{record.createTime},#{record.updateTime},#{record.reconProfitTotalAmount},#{record.dataProfitTotalAmount}
    </sql>

    <insert id="batchInsert">
        insert into bill_user_withdraw_profit_record
        (
        <include refid="Base_Column_List_No_Id"></include>
        )
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="Base_Column_List_Value"/>
            </trim>
        </foreach>
    </insert>

    <insert id="insert">
        insert into bill_user_withdraw_profit_record
        (
        <include refid="Base_Column_List_No_Id"></include>
        )
        values
       (
                <include refid="Base_Column_List_Value"/>
       )
    </insert>

    <select id="getUserIdsIdRange" resultType="java.lang.Long">
        select distinct parent_user_id from bill_user_withdraw_profit_record where
        id &gt;= #{startId}
        and id &lt; #{endId}
    </select>

    <select id="selectMinId" resultType="java.lang.Long">
        select min(id) from bill_user_withdraw_profit_record where id>0
        and request_time &gt;= #{startDate}
        and request_time &lt; #{endDate}
    </select>

    <select id="selectMaxId" resultType="java.lang.Long">
        select max(id) from bill_user_withdraw_profit_record where id>0
        and request_time &gt;= #{startDate}
        and request_time &lt; #{endDate}
    </select>
</mapper>