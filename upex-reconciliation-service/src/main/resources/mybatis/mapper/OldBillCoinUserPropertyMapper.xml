<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.OldBillCoinUserPropertyMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="coin_id" property="coinId" jdbcType="INTEGER"/>
        <result column="check_time" property="checkTime" jdbcType="TIMESTAMP"/>
        <result column="change_prop1" property="changeProp1" jdbcType="DECIMAL"/>
        <result column="prop1" property="prop1" jdbcType="DECIMAL"/>
        <result column="change_prop2" property="changeProp2" jdbcType="DECIMAL"/>
        <result column="prop2" property="prop2" jdbcType="DECIMAL"/>
        <result column="change_prop3" property="changeProp3" jdbcType="DECIMAL"/>
        <result column="prop3" property="prop3" jdbcType="DECIMAL"/>
        <result column="change_prop4" property="changeProp4" jdbcType="DECIMAL"/>
        <result column="prop4" property="prop4" jdbcType="DECIMAL"/>
        <result column="change_prop5" property="changeProp5" jdbcType="DECIMAL"/>
        <result column="prop5" property="prop5" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="initial_time" property="initialTime" jdbcType="TIMESTAMP"/>
        <result column="params" property="params" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, coin_id, check_time, change_prop1, prop1, change_prop2, prop2, change_prop3,
        prop3, change_prop4, prop4, change_prop5, prop5, create_time, update_time,initial_time,params
    </sql>

    <sql id="batch_insert_value">
        #{record.userId,jdbcType=BIGINT}, #{record.coinId,jdbcType=INTEGER},
        #{record.checkTime,jdbcType=TIMESTAMP}, #{record.changeProp1,jdbcType=DECIMAL},
        #{record.prop1,jdbcType=DECIMAL},
        #{record.changeProp2,jdbcType=DECIMAL}, #{record.prop2,jdbcType=DECIMAL},
        #{record.changeProp3,jdbcType=DECIMAL},
        #{record.prop3,jdbcType=DECIMAL}, #{record.changeProp4,jdbcType=DECIMAL}, #{record.prop4,jdbcType=DECIMAL},
        #{record.changeProp5,jdbcType=DECIMAL}, #{record.prop5,jdbcType=DECIMAL},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP},#{record.initialTime,jdbcType=TIMESTAMP},#{record.params,jdbcType=VARCHAR}
    </sql>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into bill_coin_user_property_${accountType}_${accountParam} (user_id, coin_id,
        check_time, change_prop1, prop1,
        change_prop2, prop2, change_prop3,
        prop3, change_prop4, prop4,
        change_prop5, prop5, create_time,
        update_time,initial_time,params)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>


    <select id="selectByUserIdsAndCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where check_time = #{checkTime}
          and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="selectRange" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where check_time &lt;= #{checkTime}
        and user_id = #{uid}
        order by check_time desc
        limit 1
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where user_id = #{userId}
    </select>



    <update id="updateCheckTimeById" parameterType="object">
        update bill_coin_user_property_${accountType}_${accountParam}
        set
        check_time = #{checkTime},
        update_time= #{updateTime}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="selectAllAssetsTiming" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where 1=1
        <if test="checkTime != null">
            AND check_time=#{checkTime}
        </if>
    </select>


    <select id="selectUserIds" resultType="java.lang.Long">
        select distinct
        user_id
        from bill_coin_user_property_${accountType}_${accountParam}
    </select>


    <select id="selectSysAssetsSnapShotByTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where user_id = #{userId} and check_time = #{checkTime}
    </select>

    <select id="selectSingleUser" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where 1=1
        <if test="userId != null">
            AND user_id=#{userId}
        </if>
        <if test="checkTime != null">
            AND check_time=#{checkTime}
        </if>
        <if test="coinId != null">
            AND coin_id=#{coinId}
        </if>
    </select>

    <select id="selectInfosByCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where initial_time &lt;= #{checkTime}
        AND user_id=#{userId}
        AND check_time &gt;= #{checkTime}
    </select>

    <select id="selectTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bill_coin_user_property_${accountType}_${accountParam}
        where user_id = #{userId} and check_time &lt;=#{checkTime} order by check_time desc limit 1
    </select>
    <delete id="deleteInitialTimeAfterRecord" parameterType="object">
      delete
      from bill_coin_user_property_${accountType}_${accountParam}
      where initial_time &gt; #{resetCheckTime}
    </delete>
    <update id="updateInitAndCheckBetweenRecord" parameterType="object">
      update bill_coin_user_property_${accountType}_${accountParam}
      set check_time=#{lastCheckTime}
      where user_id=#{userId}
      and initial_time &lt;= #{resetCheckTime}
      and check_time &gt;= #{resetCheckTime}
    </update>

    <select id="selectRecordsByUserIdAndCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where user_id=#{userId} AND initial_time = #{checkTime}
    </select>

    <select id="selectRecordLimitByUserCoin" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where user_id =#{userId}
        AND coin_id =#{coinId}
        AND check_time &lt;=#{checkTime}
        order by check_time desc limit 1
    </select>

    <select id="selectCheckTimeOverInitLimitByUerCoin" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where user_id =#{userId}
        AND coin_id =#{coinId}
        AND check_time &gt;= #{checkTime}
        AND initial_time &lt;= #{checkTime}
    </select>

    <select id="selectCoinsByUserAndTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where user_id =#{userId}
        AND check_time = #{checkTime}
    </select>


    <select id="selectAssetListByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where user_id = #{userId} and initial_time = (
            select initial_time
            from bill_coin_user_property_${accountType}_${accountParam}
            where user_id = #{userId} and initial_time &lt;= #{checkTime} order by initial_time desc limit 1
            )
    </select>

    <select id="selectByCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where check_time=#{checkTime}
    </select>


    <delete id="deleteUserCoinHistoryRecord" parameterType="object">
        delete
        from bill_coin_user_property_${accountType}_${accountParam}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectRangeCheckTimeRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where check_time >= #{startTime}
        and check_time &lt;= #{endTime}
        and id > #{minId}
        order by id asc
        limit #{pageSize}
    </select>
    <select id="selectLastUserAssetByUserId" resultType="com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        where user_id = #{userId}
        and check_time = (select check_time from bill_coin_user_property_${accountType}_${accountParam}
        where user_id = #{userId} order by check_time desc limit 1 )
    </select>

    <select id="selectCheckTimeAfterTime" resultType="java.util.Date">
        select
            distinct(check_time) as checkTime
        from bill_coin_user_property_${accountType}_${accountParam}
        force index(`idx_checkTime`)
        where check_time >= #{startTime}
    </select>


    <select id="selectCheckTimeRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_coin_user_property_${accountType}_${accountParam}
        force index(`idx_checkTime`)
        where check_time = #{checkTime}
        and id > #{minId}
        order by id asc
        limit #{pageSize}
    </select>

</mapper>