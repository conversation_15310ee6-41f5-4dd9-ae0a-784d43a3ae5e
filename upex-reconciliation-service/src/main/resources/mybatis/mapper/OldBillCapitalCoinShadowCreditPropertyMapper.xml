<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.OldBillCapitalCoinShadowCreditPropertyMapper">

    <resultMap id="BaseResultMap"
               type="com.upex.reconciliation.service.dao.bill.entity.BillCapitalCoinUserShadowCreditProperty">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="coinId" column="coin_id" jdbcType="INTEGER"/>
        <result property="coinName" column="coin_name" jdbcType="VARCHAR"/>
        <result property="initialTime" column="initial_time" jdbcType="TIMESTAMP"/>
        <result property="checkOkTime" column="check_ok_time" jdbcType="TIMESTAMP"/>
        <result property="capitalCount" column="capital_count" jdbcType="DECIMAL"/>
        <result property="capitalChange" column="capital_change" jdbcType="DECIMAL"/>
        <result property="rate" column="rate" jdbcType="DECIMAL"/>
        <result property="capitalUsdtAmount" column="capital_usdt_amount" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="params" column="params" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,coin_id,coin_name,
        initial_time,check_ok_time,capital_count,
        capital_change,rate,capital_usdt_amount,
        create_time,update_time,params
    </sql>
    <sql id="Table_Name">
        bill_capital_coin_shadow_credit_property
    </sql>
    <sql id="batch_insert_value">
        #{record.coinId,jdbcType=INTEGER}
        ,
        #{record.coinName,jdbcType=VARCHAR},
        #{record.initialTime,jdbcType=TIMESTAMP},
        #{record.checkOkTime,jdbcType=TIMESTAMP},
        #{record.capitalCount,jdbcType=DECIMAL},
        #{record.capitalChange,jdbcType=DECIMAL},
        #{record.rate,jdbcType=DECIMAL},
        #{record.capitalUsdtAmount,jdbcType=DECIMAL},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP},
        #{record.params,jdbcType=VARCHAR}
    </sql>
    <delete id="deleteResetTimeShadowCreditList">
        delete from
        <include refid="Table_Name"/>
        where initial_time > #{resetCheckTime}
    </delete>
</mapper>
