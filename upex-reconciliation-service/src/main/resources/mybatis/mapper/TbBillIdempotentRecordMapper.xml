<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.TbBillIdempotentRecordMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.TbBillIdempotentRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="account_id" jdbcType="BIGINT" property="accountId"/>
        <result column="uni_key" jdbcType="VARCHAR" property="uniKey"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="time_slice" jdbcType="BIGINT" property="timeSlice"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, account_id, uni_key, create_time, time_slice
    </sql>

    <sql id="Table_Name">
        tb_bill_idempotent_record_${accountType}_${accountParam}
    </sql>

    <sql id="batch_insert_value">
        #{record.accountId,jdbcType=BIGINT},
        #{record.uniKey,jdbcType=VARCHAR},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.timeSlice,jdbcType=BIGINT}
    </sql>

    <delete id="batchDelete">
        delete from
        <include refid="Table_Name"/>
        where id >= #{beginId}
        limit #{pageSize}
    </delete>


    <select id="selectRangeCheckTimeRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"></include>
        where  time_slice = #{timeSlice}
        order by id asc
    </select>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="Table_Name"/>
        ( account_id, uni_key,
        create_time,time_slice)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>


    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tb_bill_idempotent_record_t2_${accountType} _${accountParam}
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.upex.reconciliation.service.dao.entity.TbBillIdempotentRecord">
        insert into
        <include refid="Table_Name"/>
        (id, account_id, uni_key,
        create_time,time_slice)
        values (#{id,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, #{uniKey,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{timeSlice,jdbcType=BIGINT})
    </insert>


    <insert id="insertSelective" parameterType="com.upex.reconciliation.service.dao.entity.TbBillIdempotentRecord">
        insert into
        <include refid="Table_Name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="uniKey != null">
                uni_key,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="timeSlice != null">
                time_slice,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="accountId != null">
                #{accountId,jdbcType=BIGINT},
            </if>
            <if test="uniKey != null">
                #{uniKey,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="timeSlice != null">
                #{timeSlice,jdbcType=BIGINT},
            </if>

        </trim>
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.upex.reconciliation.service.dao.entity.TbBillIdempotentRecord">
        update
        <include refid="Table_Name"/>
        set account_id = #{accountId,jdbcType=BIGINT},
        uni_key = #{uniKey,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>