<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.AssetsBillConfigMapper" >
  <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.AssetsBillConfig" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="assets_check_type" property="assetsCheckType" jdbcType="VARCHAR" />
    <result column="assets_check_param" property="assetsCheckParam" jdbcType="VARCHAR" />
    <result column="sync_pos" property="syncPos" jdbcType="BIGINT" />
    <result column="check_ok_time" property="checkOkTime" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="priority_level" property="priorityLevel" jdbcType="INTEGER" />
    <result column="consume_offset" property="consumeOffset" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="base_column_list" >
    id, assets_check_type, assets_check_param, sync_pos, check_ok_time, status, create_time,
    update_time,consume_offset
  </sql>
  <sql id="base_column_list_not_id" >
    assets_check_type, assets_check_param, sync_pos, check_ok_time, status, create_time,
    update_time,consume_offset
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="base_column_list" />
    from assets_bill_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from assets_bill_config
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.upex.reconciliation.service.dao.entity.AssetsBillConfig" useGeneratedKeys="true" keyProperty="id">
    insert into assets_bill_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="assetsCheckType != null" >
        assets_check_type,
      </if>
      <if test="assetsCheckParam != null" >
        assets_check_param,
      </if>
      <if test="syncPos != null" >
        sync_pos,
      </if>
      <if test="checkOkTime != null" >
        check_ok_time,
      </if>
      <if test="status != null" >
        `status`,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="priorityLevel != null" >
        priority_level,
      </if>
      <if test="consumeOffset != null" >
          consume_offset,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="assetsCheckType != null" >
        #{assetsCheckType,jdbcType=VARCHAR},
      </if>
      <if test="assetsCheckParam != null" >
        #{assetsCheckParam,jdbcType=VARCHAR},
      </if>
      <if test="syncPos != null" >
        #{syncPos,jdbcType=BIGINT},
      </if>
      <if test="checkOkTime != null" >
        #{checkOkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="priorityLevel != null" >
        #{priorityLevel,jdbcType=INTEGER},
      </if>
      <if test="consumeOffset != null" >
        #{consumeOffset,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.upex.reconciliation.service.dao.entity.AssetsBillConfig" >
    update assets_bill_config
    <set >
      <if test="assetsCheckType != null" >
        assets_check_type = #{assetsCheckType,jdbcType=VARCHAR},
      </if>
      <if test="assetsCheckParam != null" >
        assets_check_param = #{assetsCheckParam,jdbcType=VARCHAR},
      </if>
      <if test="syncPos != null" >
        sync_pos = #{syncPos,jdbcType=BIGINT},
      </if>
      <if test="checkOkTime != null" >
        check_ok_time = #{checkOkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="consumeOffset != null" >
          consume_offset = #{consumeOffset,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="listAssetsBillConfig" resultMap="BaseResultMap" parameterType="java.lang.Object" >
    select
    <include refid="base_column_list" />
    from assets_bill_config
    order by priority_level asc
  </select>

  <select id="selectByTypeAndParam" resultMap="BaseResultMap" parameterType="java.lang.Object" >
    select
    <include refid="base_column_list" />
    from assets_bill_config
    where  assets_check_type = #{assetsCheckType,jdbcType=VARCHAR}
    and assets_check_param = #{assetsCheckParam,jdbcType=VARCHAR}
    limit 1
  </select>

  <select id="selectMinCheckOkTime" resultMap="BaseResultMap">
    select
    <include refid="base_column_list"/>
    from assets_bill_config
    order by check_ok_time asc limit 1
  </select>
  <update id="updateSyncPosAndTime" parameterType="object">
    update assets_bill_config
    set sync_pos=#{syncPos},check_ok_time=#{resetCheckTime}
    where assets_check_type = #{assetsCheckType,jdbcType=VARCHAR}
    and assets_check_param = #{assetsCheckParam,jdbcType=VARCHAR}
  </update>

  <update id="deleteAll">
    delete from assets_bill_config
    where  assets_check_type = #{assetsCheckType,jdbcType=VARCHAR}
    and assets_check_param = #{assetsCheckParam,jdbcType=VARCHAR}
  </update>
</mapper>