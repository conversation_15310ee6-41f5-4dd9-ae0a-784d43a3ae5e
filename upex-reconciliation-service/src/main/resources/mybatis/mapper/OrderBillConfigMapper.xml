<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.OrderBillConfigMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.OrderBillConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result property="orderType" column="order_type" jdbcType="VARCHAR"/>
        <result property="orderParam" column="order_param" jdbcType="VARCHAR"/>
        <result property="checkOkTime" column="check_ok_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_type`,
        `order_param`,
        `check_ok_time`,
        `status`,
        `create_time`,
        `update_time`
    </sql>

    <sql id="Table_Name">
        order_bill_config
    </sql>

    <!--ignore-->
    <insert id="insert" parameterType="com.upex.reconciliation.service.dao.entity.OrderBillConfig"
            useGeneratedKeys="true" keyProperty="id">
        insert into order_bill_config (
        `order_type`,
        `order_param`,
        `check_ok_time`,
        `status`,
        `create_time`,
        `update_time`
        )
        values (
        #{record.orderType, jdbcType=VARCHAR},
        #{record.orderParam, jdbcType=VARCHAR},
        #{record.checkOkTime,jdbcType=TIMESTAMP},
        #{record.status,jdbcType=TINYINT},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <select id="getOrderBillConfig" resultType="com.upex.reconciliation.service.dao.entity.OrderBillConfig">
        select
        <include refid="Base_Column_List"/>
        from order_bill_config
        where order_type = #{orderType}
        and order_param = #{orderParam}
    </select>

    <update id="updateOrderBillConfig">
        update order_bill_config
        set check_ok_time = #{checkOkTime},
        update_time = #{updateTime},
        status = #{status}
        where order_type = #{orderType,jdbcType=VARCHAR}
        and order_param = #{orderParam,jdbcType=VARCHAR}
    </update>

    <delete id="deleteById">
        delete from
        order_bill_config
        where id = #{id}
    </delete>
</mapper>