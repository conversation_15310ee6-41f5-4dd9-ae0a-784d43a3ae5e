<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.StatisticsDetailPropertyMapper" >
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.StatisticsDetailProperty" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="asset_type" property="assetType" jdbcType="TINYINT" />
        <result column="coin_id" property="coinId" jdbcType="INTEGER" />
        <result column="coin_name" property="coinName" jdbcType="VARCHAR" />
        <result column="coin_count" property="coinCount" jdbcType="DECIMAL" />
        <result column="u_rate" property="uRate" jdbcType="DECIMAL" />
        <result column="snapshot_time" property="snapshotTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="AssetErrorInfoMap" type="com.upex.reconciliation.service.dao.entity.AssetErrorInfo" >
        <result column="coin_id" property="coinId" jdbcType="INTEGER" />
        <result column="coin_name" property="coinName" jdbcType="VARCHAR" />
        <result column="normal_snapshot_time" property="normalSnapshotTime" jdbcType="TIMESTAMP" />
        <result column="anomalous_snapshot_time" property="anomalousSnapshotTime" jdbcType="TIMESTAMP" />
        <result column="normal_coin_count" property="normalCoinCount" jdbcType="DECIMAL" />
        <result column="anomalous_coin_count" property="anomalousCoinCount" jdbcType="DECIMAL" />
        <result column="asset_type" property="assetType" jdbcType="INTEGER" />
        <result column="change_coin_count" property="changeCoinCount" jdbcType="DECIMAL" />
        <result column="change_coin_amount" property="changeCoinAmount" jdbcType="DECIMAL" />
    </resultMap>
    <sql id="Base_Column_List" >
        id,asset_type,coin_name,coin_id,coin_count,u_rate,snapshot_time,create_time
    </sql>
    <select id="selectRecordsBySnapshotTime" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from bill_statistics_asset_detail
        where snapshot_time = #{snapshotTime}
    </select>

    <select id="selectCoinBySnapshotTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset_detail
        where snapshot_time >= #{snapshotTime} and asset_type = 10
    </select>

    <select id="selectRecordsByTimeAndTypeAdCoinId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset_detail
        where snapshot_time = #{snapshotTime} and asset_type = #{assetType}
        <if test="coinId != null" >
            and coin_id = #{coinId}
        </if>
    </select>

    <sql id="batch_insert_value">
      #{record.assetType,jdbcType=TINYINT},
      #{record.coinId,jdbcType=TIMESTAMP},
      #{record.coinName,jdbcType=VARCHAR},
      #{record.coinCount,jdbcType=DECIMAL},
      #{record.uRate,jdbcType=DECIMAL},
      #{record.snapshotTime,jdbcType=TIMESTAMP},
      #{record.createTime,jdbcType=TIMESTAMP},
    </sql>
    <insert id="insertRecords">
        insert ignore into bill_statistics_asset_detail
        (asset_type,coin_id,coin_name,coin_count,u_rate,snapshot_time,create_time)
        values
        <foreach collection="list" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <delete id="deleteDetailsWithinBillByTime">
        delete from bill_statistics_asset_detail where snapshot_time = #{statisticsTime} and asset_type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type,jdbcType=TINYINT}
        </foreach>
    </delete>

    <select id="selectWalletRecords" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset_detail
        where snapshot_time = #{snapshotTime}
        and asset_type = #{assetType}
        <if test="coinId != null and coinId != ''" >
            and coin_id = #{coinId}
        </if>
        <if test="chainCoinName != null and chainCoinName != ''" >
            and coin_name = #{chainCoinName}
        </if>

    </select>
    <select id="listAssetsDetailBySnapshot" resultType="com.upex.reconciliation.service.dao.entity.StatisticsDetailProperty">
        select asset_type,
               coin_id,
               coin_name,
               max(u_rate) as u_rate,
               sum(coin_count) as coin_count
        from bill_statistics_asset_detail
        where snapshot_time = #{snapshot}
          and asset_type in
        <foreach collection="otherAssetsTypeList" item="otherAssets" open="(" separator="," close=")">
            #{otherAssets}
        </foreach>
        group by asset_type, coin_id, coin_name
    </select>
    <select id="listAssetsDetailBySnapshotAndType" resultType="com.upex.reconciliation.service.dao.entity.StatisticsDetailProperty">
        select coin_id,
        coin_name,
        sum(coin_count) as coin_count
        from bill_statistics_asset_detail
        where snapshot_time = #{snapshot}
        and asset_type = #{assetsType}
        group by coin_id, coin_name
    </select>
    <select id="selectBigDifferenceCoinInfo" resultMap="AssetErrorInfoMap">
        SELECT a.coin_id AS coin_id,
               a.coin_name AS coin_name,
               a.asset_type AS asset_type,
               b.coin_count - a.coin_count AS change_coin_count,
               b.coin_count * b.u_rate - a.coin_count * a.u_rate AS change_coin_amount
        FROM (
        SELECT *
        FROM bill_statistics_asset_detail
        WHERE snapshot_time = #{normalSnapshotTime}
        AND asset_type = 11
        ) a
        LEFT JOIN (
        SELECT *
        FROM bill_statistics_asset_detail
        WHERE snapshot_time = #{anomalousSnapshotTime}
        AND asset_type = 11
        ) b
        ON a.asset_type = b.asset_type
        AND a.coin_id = b.coin_id
        AND a.coin_name = b.coin_name
        WHERE b.coin_count - a.coin_count <![CDATA[ <> ]]> 0
        ORDER BY abs(b.coin_count * b.u_rate - a.coin_count * a.u_rate) DESC, a.coin_id, a.asset_type
            limit #{pageSize}
    </select>
    <select id="selectBigDifferenceAssetTypeInfo" resultMap="AssetErrorInfoMap">
        SELECT
        a.coin_id AS coin_id,
        a.coin_name as coin_name,
        a.asset_type AS asset_type,
        b.coin_count - a.coin_count AS change_coin_count,
        b.coin_count * b.u_rate - a.coin_count * a.u_rate AS change_coin_amount
        FROM
        (
        SELECT
        snapshot_time,
        coin_id,
        coin_name,
        asset_type,
        sum(coin_count) as coin_count,
        max(u_rate) as u_rate
        FROM
        bill_statistics_asset_detail
        WHERE
        snapshot_time = #{normalSnapshotTime}
        AND asset_type in
        <foreach collection="assetTypeList" item="assetType" open="(" separator="," close=")">
            #{assetType}
        </foreach>
        and coin_id = #{coinId}
        group by
        snapshot_time,
        coin_id,
        coin_name,
        asset_type
        order by
        asset_type,
        snapshot_time
        ) a
        LEFT JOIN (
        SELECT
        snapshot_time,
        coin_id,
        coin_name,
        asset_type,
        sum(coin_count) as coin_count,
        max(u_rate) as u_rate
        FROM
        bill_statistics_asset_detail
        WHERE
        snapshot_time = #{anomalousSnapshotTime}
        AND asset_type in
        <foreach collection="assetTypeList" item="assetType" open="(" separator="," close=")">
            #{assetType}
        </foreach>
        and coin_id = #{coinId}
        group by
        snapshot_time,
        coin_id,
        coin_name,
        asset_type
        order by
        asset_type,
        snapshot_time
        ) b ON a.asset_type = b.asset_type
        AND a.coin_id = b.coin_id
        AND a.coin_name = b.coin_name
        WHERE
        b.coin_count - a.coin_count <![CDATA[ <> ]]> 0
        ORDER BY
        abs(b.coin_count * b.u_rate - a.coin_count * a.u_rate) DESC,
        a.coin_id,
        a.asset_type
    </select>
    <select id="selectBigDifferenceChainCoinInfo" resultMap="AssetErrorInfoMap">
        SELECT
            a.coin_id AS coin_id,
            a.coin_name AS coin_name,
            a.snapshot_time as normal_snapshot_time,
            b.snapshot_time as anomalous_snapshot_time,
            a.coin_count as normal_coin_count,
            b.coin_count as anomalous_coin_count,
            a.asset_type AS asset_type,
            b.coin_count - a.coin_count AS change_coin_count,
            b.coin_count * b.u_rate - a.coin_count * a.u_rate AS change_coin_amount
        FROM
        (
        SELECT
        snapshot_time,
        coin_id,
        coin_name,
        asset_type,
        sum(coin_count) as coin_count,
        max(u_rate) as u_rate
        FROM
        bill_statistics_asset_detail
        WHERE
        snapshot_time = #{normalSnapshotTime}
        AND asset_type = #{assetType}
        AND coin_id = #{coinId}
        group by
        snapshot_time,
        coin_id,
        coin_name,
        asset_type
        order by
        snapshot_time
        ) a
        LEFT JOIN (
        SELECT
        snapshot_time,
        coin_id,
        coin_name,
        asset_type,
        sum(coin_count) as coin_count,
        max(u_rate) as u_rate
        FROM
        bill_statistics_asset_detail
        WHERE
        snapshot_time = #{anomalousSnapshotTime}
        AND asset_type = #{assetType}
        and coin_id = #{coinId}
        group by
        snapshot_time,
        coin_id,
        coin_name,
        asset_type
        order by
        snapshot_time
        ) b ON a.asset_type = b.asset_type
        AND a.coin_id = b.coin_id
        AND a.coin_name = b.coin_name
        WHERE
        b.coin_count - a.coin_count <![CDATA[ <> ]]> 0
        ORDER BY
        abs(b.coin_count * b.u_rate - a.coin_count * a.u_rate) DESC,
        a.coin_id,
        a.coin_name,
        a.asset_type
    </select>
</mapper>