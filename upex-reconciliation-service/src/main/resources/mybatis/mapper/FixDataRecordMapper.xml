<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.FixDataRecordMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.FixDataRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="account_type" property="accountType" jdbcType="INTEGER"/>
        <result column="account_param" property="accountParam" jdbcType="VARCHAR"/>
        <result column="batch_id" property="batchId" jdbcType="BIGINT"/>
        <result column="table_name_prefix" property="tableNamePrefix" jdbcType="VARCHAR"/>
        <result column="before_data" property="beforeData" jdbcType="VARCHAR"/>
        <result column="after_data" property="afterData" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>


    <sql id="Table_Name">
        fix_data_record
    </sql>

    <sql id="Base_Column_List">
        id, account_type, account_param,batch_id, table_name_prefix, before_data,
        after_data,create_time,update_time
    </sql>

    <sql id="Base_Column_List_without_id">
         account_type, account_param,batch_id, table_name_prefix, before_data,
        after_data,create_time,update_time
    </sql>

    <sql id="batch_insert_value">
        #{record.accountType,jdbcType=INTEGER},#{record.accountParam,jdbcType=VARCHAR},
        #{record.batchId,jdbcType=BIGINT},#{record.tableNamePrefix,jdbcType=VARCHAR},
        #{record.beforeData,jdbcType=VARCHAR},
        #{record.afterData,jdbcType=VARCHAR},#{record.createTime,jdbcType=TIMESTAMP},#{record.updateTime,jdbcType=TIMESTAMP}
    </sql>


    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="Table_Name"/>(
        <include refid="Base_Column_List_without_id"/>
                             )
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>


</mapper>