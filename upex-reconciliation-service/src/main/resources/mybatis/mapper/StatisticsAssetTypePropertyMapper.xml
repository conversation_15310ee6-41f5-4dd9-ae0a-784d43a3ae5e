<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.StatisticsAssetTypePropertyMapper" >
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.StatisticsAssetTypeProperty" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="asset_type" property="assetType" jdbcType="TINYINT" />
        <result column="total" property="totalBalance" jdbcType="DECIMAL" />
        <result column="snapshot_time" property="snapshotTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>
    <sql id="Base_Column_List" >
        id,asset_type,total,snapshot_time,create_time
    </sql>
    <select id="selectRecordsBySnapshotTime" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset_type
        where snapshot_time = #{snapshotTime}
    </select>

    <sql id="batch_insert_value">
      #{record.assetType,jdbcType=TINYINT},
      #{record.totalBalance,jdbcType=DECIMAL},
      #{record.snapshotTime,jdbcType=TIMESTAMP},
      #{record.createTime,jdbcType=TIMESTAMP}
    </sql>
    <insert id="insertRecords">
        insert ignore into bill_statistics_asset_type
        (asset_type,total,snapshot_time,create_time)
        values
        <foreach collection="list" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <delete id="deleteWithinBillByTime">
        delete from bill_statistics_asset_type where snapshot_time = #{statisticsTime} and asset_type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type,jdbcType=TINYINT}
        </foreach>
    </delete>

    <select id="selectRecordsWithOtherByTime" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset_type
        where snapshot_time = #{snapshotTime}
        and asset_type in (5,6,7,10)
    </select>
    <select id="listAssetTypeBySnapshot" resultMap="BaseResultMap">
        select asset_type,
        sum(total) as total
        from bill_statistics_asset_type
        where snapshot_time = #{snapshot}
        and asset_type in
        <foreach collection="otherAssetsTypeList" item="otherAssets" open="(" separator="," close=")">
            #{otherAssets}
        </foreach>
        group by asset_type
    </select>
</mapper>