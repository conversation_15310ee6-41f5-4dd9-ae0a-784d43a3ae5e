<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.FinancialVirtualAccountSnapshotMapper">

    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.FinancialVirtualAccountSnapshot">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="coinId" column="coin_id" jdbcType="INTEGER"/>
            <result property="coinName" column="coin_name" jdbcType="VARCHAR"/>
            <result property="groupType" column="group_type" jdbcType="INTEGER"/>
            <result property="totalBalance" column="total_balance" jdbcType="DECIMAL"/>
            <result property="snapshotTime" column="snapshot_time" jdbcType="TIMESTAMP"/>
            <result property="sourceCreateTime" column="source_create_time" jdbcType="TIMESTAMP"/>
            <result property="sourceUpdateTime" column="source_update_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
            <result property="preSettleInterest" column="pre_settle_interest" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,coin_id,
        coin_name,group_type,total_balance,
        snapshot_time,source_create_time,source_update_time,
        create_time,update_time,version,
        pre_settle_interest
    </sql>
    <sql id="Table_Name">
        financial_virtual_account_snapshot_t${tableSuffix}
    </sql>
    <sql id="batch_insert_value">
        #{record.userId,jdbcType=BIGINT}, #{record.coinId,jdbcType=INTEGER},
        #{record.coinName,jdbcType=VARCHAR}, #{record.groupType,jdbcType=INTEGER}, #{record.totalBalance,jdbcType=DECIMAL},
        #{record.snapshotTime,jdbcType=TIMESTAMP},
        #{record.sourceCreateTime,jdbcType=TIMESTAMP}, #{record.sourceUpdateTime,jdbcType=TIMESTAMP}, #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP}, #{record.version,jdbcType=BIGINT}, #{record.preSettleInterest,jdbcType=DECIMAL}
    </sql>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="Table_Name"/>
        (user_id,coin_id,
        coin_name,group_type,total_balance,
        snapshot_time,source_create_time,source_update_time,
        create_time,update_time,version,
        pre_settle_interest)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

</mapper>
