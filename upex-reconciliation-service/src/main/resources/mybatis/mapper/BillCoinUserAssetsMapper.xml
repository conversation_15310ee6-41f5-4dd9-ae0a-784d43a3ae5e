<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillCoinUserAssetsMapper" >
  <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillCoinUserAssets" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="coin_id" property="coinId" jdbcType="INTEGER" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="check_ok_time" property="checkOkTime" jdbcType="TIMESTAMP" />
    <result column="initial_time" property="initialTime" jdbcType="TIMESTAMP" />
    <result column="prop1" property="prop1" jdbcType="DECIMAL" />
    <result column="prop2" property="prop2" jdbcType="DECIMAL" />
    <result column="prop3" property="prop3" jdbcType="DECIMAL" />
    <result column="prop4" property="prop4" jdbcType="DECIMAL" />
    <result column="prop5" property="prop5" jdbcType="DECIMAL" />
    <result column="params" property="params" jdbcType="VARCHAR" />
    <result column="position_flag" property="positionFlag" jdbcType="BIT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <resultMap id="BaseParamResultMap" type="com.upex.reconciliation.service.dao.entity.BillCoinUserAssets" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="coin_id" property="coinId" jdbcType="INTEGER" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="check_ok_time" property="checkOkTime" jdbcType="TIMESTAMP" />
    <result column="initial_time" property="initialTime" jdbcType="TIMESTAMP" />
    <result column="prop1" property="prop1" jdbcType="DECIMAL" />
    <result column="prop2" property="prop2" jdbcType="DECIMAL" />
    <result column="prop3" property="prop3" jdbcType="DECIMAL" />
    <result column="prop4" property="prop4" jdbcType="DECIMAL" />
    <result column="prop5" property="prop5" jdbcType="DECIMAL" />
    <result column="position_flag" property="positionFlag" jdbcType="BIT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <collection property="billCoinUserAssetsParamList" ofType="com.upex.reconciliation.service.model.dto.BillCoinUserAssetsParam">
      <result column="assets_id" property="assetsId" jdbcType="BIGINT" />
      <result column="bus_line" property = "busLine" jdbcType="INTEGER" />
      <result column="sec_bus_line" property = "secBusLine" jdbcType="VARCHAR" />
      <result column="tid" property = "tId" jdbcType="VARCHAR" />
      <result column="sid" property = "sId" jdbcType="VARCHAR" />
      <result column="hold_mode" property = "holdMode" jdbcType="INTEGER" />
      <result column="mgn_mode" property = "mgnMode" jdbcType="INTEGER" />
      <result column="posi_mgn" property = "posiMgn" jdbcType="DECIMAL" />
      <result column="fx_l_lever" property = "fxLLever" jdbcType="INTEGER" />
      <result column="fx_l_lever_chg" property = "fxLLeverChg" jdbcType="INTEGER" />
      <result column="l_avg" property = "lAvg" jdbcType="DECIMAL" />
      <result column="fx_s_lever" property = "fxSLever" jdbcType="INTEGER" />
      <result column="fx_s_lever_chg" property = "fxSLeverChg" jdbcType="INTEGER" />
      <result column="c_mgn_lever" property = "cMgnLever" jdbcType="INTEGER" />
      <result column="c_mgn_lever_chg" property = "cMgnLeverChg" jdbcType="INTEGER" />
      <result column="s_avg" property = "sAvg" jdbcType="DECIMAL" />
      <result column="l_count_chg" property = "lCountChg" jdbcType="DECIMAL" />
      <result column="l_count" property = "lCount" jdbcType="DECIMAL" />
      <result column="s_count_chg" property = "sCountChg" jdbcType="DECIMAL" />
      <result column="s_count" property = "sCount" jdbcType="DECIMAL" />
      <result column="un_r" property = "unR" jdbcType="DECIMAL" />
      <result column="un_r_in_s" property = "unRInS" jdbcType="DECIMAL" />
      <result column="m_price" property = "mPrice" jdbcType="DECIMAL" />
      <result column="s_price" property = "sPrice" jdbcType="DECIMAL" />
      <result column="m_price_map" property = "mPriceMapParam" jdbcType="VARCHAR" />
      <result column="s_price_map" property = "sPriceMapParam" jdbcType="VARCHAR" />
    </collection>
  </resultMap>

  <sql id="Base_Column_List" >
    id, coin_id, user_id, check_ok_time, initial_time, prop1, prop2, prop3, prop4, prop5,
    position_flag, create_time, update_time
  </sql>

  <sql id="Base_Column_Params_List" >
    id, coin_id, user_id, check_ok_time, initial_time, prop1, prop2, prop3, prop4, prop5,
    params, position_flag, create_time, update_time
  </sql>

  <sql id="Base_Column_Assets_Param_List">
    assets.id,assets.coin_id,assets.user_id,assets.check_ok_time,assets.initial_time,
    assets.prop1,assets.prop2,assets.prop3,assets.prop4,assets.prop5,assets.position_flag,assets.create_time,assets.update_time,
    param.id,param.coin_id,param.assets_id,param.user_id,param.initial_time,param.bus_line,param.sec_bus_line,param.tid,param.sid,
    param.hold_mode,param.mgn_mode,param.posi_mgn,param.fx_l_lever,param.fx_l_lever_chg,param.l_avg,param.fx_s_lever,param.fx_s_lever_chg,
    param.c_mgn_lever,param.c_mgn_lever_chg,param.s_avg,param.l_count_chg,param.l_count,param.s_count_chg,param.s_count,param.un_r,param.un_r_in_s,
    param.m_price,param.s_price,param.m_price_map,param.s_price_map
  </sql>


  <sql id="batch_insert_value">
      #{record.userId,jdbcType=BIGINT}, #{record.coinId,jdbcType=INTEGER},
      #{record.checkOkTime,jdbcType=TIMESTAMP},#{record.initialTime,jdbcType=TIMESTAMP}, #{record.prop1,jdbcType=DECIMAL},
      #{record.prop2,jdbcType=DECIMAL},#{record.prop3,jdbcType=DECIMAL},  #{record.prop4,jdbcType=DECIMAL},
      #{record.prop5,jdbcType=DECIMAL}, #{record.params,jdbcType=VARCHAR},#{record.positionFlag,jdbcType=BIT},
      #{record.createTime,jdbcType=TIMESTAMP},#{record.updateTime,jdbcType=TIMESTAMP}
  </sql>

  <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
    insert into bill_coin_user_assets_${accountType}_${accountParam} (
      user_id,
      coin_id,
      check_ok_time,
      initial_time,
      prop1,
      prop2,
      prop3,
      prop4,
      prop5,
      position_flag,
      create_time,
      update_time
    )
    values
    <foreach collection="list" item="record" index="index" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{record.userId,jdbcType=BIGINT},
        #{record.coinId,jdbcType=INTEGER},
        #{record.checkOkTime,jdbcType=TIMESTAMP},
        #{record.initialTime,jdbcType=TIMESTAMP},
        #{record.prop1,jdbcType=DECIMAL},
        #{record.prop2,jdbcType=DECIMAL},
        #{record.prop3,jdbcType=DECIMAL},
        #{record.prop4,jdbcType=DECIMAL},
        #{record.prop5,jdbcType=DECIMAL},
        #{record.positionFlag,jdbcType=BIT},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP}
      </trim>
    </foreach>
  </insert>

  <select id="selectLastCheckTime" parameterType="object" resultType="java.util.Date">
    select check_ok_time
    from bill_coin_user_assets_${accountType}_${accountParam}
    order by id desc limit 1
  </select>

  <select id="selectSysAssetsSnapShotByTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam}
    where 1=1
    <if test="coinId != null">
      AND coin_id = #{coinId}
    </if>
    and check_ok_time = #{requestTime}
    and user_id = #{userId}
  </select>


  <select id="selectAllAssetsTiming" resultMap="BaseParamResultMap">
    select
    <include refid="Base_Column_Assets_Param_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam} as assets
    left join bill_coin_user_assets_param_${accountType}_${accountParam} param on assets.id = param.assets_id
    where 1=1
    <if test="checkTime != null">
      AND assets.check_ok_time=#{checkTime}
    </if>
  </select>

  <select id="selectAllAssetsTimingSwapMain" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam}
    where 1=1
    <if test="checkTime != null">
      AND check_ok_time=#{checkTime}
    </if>
  </select>

  <select id="selectTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bill_coin_user_assets_${accountType}_${accountParam}
    where user_id = #{userId}
    and check_ok_time &gt;= #{checkOkTime}
    and initial_time &lt;= #{checkOkTime}
    order by check_ok_time desc limit 1
  </select>


  <update id="updateCheckTimeById" parameterType="object">
    update bill_coin_user_assets_${accountType}_${accountParam}
    set check_ok_time = #{checkTime},
    update_time= #{updateTime}
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
        #{id}
    </foreach>
  </update>


  <delete id="deleteInitialTimeAfterRecord" parameterType="object">
      delete
      from bill_coin_user_assets_${accountType}_${accountParam}
      where initial_time &gt; #{resetCheckTime}
    </delete>
  <update id="updateInitAndCheckBetweenRecord" parameterType="object">
      update bill_coin_user_assets_${accountType}_${accountParam}
      set check_ok_time=#{lastCheckTime}
      where initial_time &lt;= #{resetCheckTime}
      and check_ok_time &gt;= #{resetCheckTime}
    </update>

  <select id="selectSingleByParamAndTime" resultMap="BaseParamResultMap">
    select
    <include refid="Base_Column_Assets_Param_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam} as assets
    left join bill_coin_user_assets_param_${accountType}_${accountParam} param on assets.id = param.assets_id
    where assets.initial_time &lt;= #{checkTime}
    <if test="userId != null">
      AND assets.user_id=#{userId}
    </if>
      AND assets.check_ok_time &gt;= #{checkTime}
    <if test="coinId != null">
      AND assets.coin_id=#{coinId}
    </if>
  </select>


  <select id="selectSingleByParamAndTimeSwapMain" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_Params_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam}
    where initial_time &lt;= #{checkTime}
    <if test="userId != null">
      AND user_id=#{userId}
    </if>
    AND check_ok_time &gt;= #{checkTime}
    <if test="coinId != null">
      AND coin_id=#{coinId}
    </if>
  </select>



  <select id="selectRecentNearSnapTime" resultType="com.upex.reconciliation.service.dao.entity.BillCoinUserAssets">
    select
    <include refid="Base_Column_List" />
    from bill_coin_user_assets_${accountType}_${accountParam}
    where user_id = #{userId} and check_ok_time &lt;=#{checkTime}
    order by check_ok_time desc limit 1
  </select>

  <select id="selectRecordsByUserIdAndCheckTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_Params_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam}
    where user_id=#{userId} AND check_ok_time = #{checkTime}
  </select>

   <select id="selectRecordsByUserIdAndTime" resultMap="BaseParamResultMap">
    select
     <include refid="Base_Column_Assets_Param_List"/>
     from bill_coin_user_assets_${accountType}_${accountParam} as assets
     left join bill_coin_user_assets_param_${accountType}_${accountParam} param on assets.id = param.assets_id
    where assets.user_id=#{userId} AND assets.check_ok_time = #{checkTime}
  </select>

  <select id="selectRecordLimitByUserCoinSwapMain" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_Params_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam}
    where user_id =#{userId}
    AND coin_id =#{coinId}
    AND check_ok_time &lt;=#{checkTime}
    order by check_ok_time desc limit 1
  </select>

  <select id="selectRecordLimitByUserCoin" resultMap="BaseParamResultMap">
    select
    <include refid="Base_Column_Assets_Param_List"/>
    from (select
       ass.id,
       ass.coin_id,
       ass.user_id,
       ass.check_ok_time,
       ass.initial_time,
       ass.prop1,
       ass.prop2,
       ass.prop3,
       ass.prop4,
       ass.prop5,
       ass.position_flag,
       ass.create_time,
       ass.update_time
    from bill_coin_user_assets_${accountType}_${accountParam} ass
    where ass.user_id =#{userId}
    AND ass.coin_id =#{coinId}
    AND ass.check_ok_time &lt;=#{checkTime}
    order by ass.check_ok_time desc limit 1) assets
    left join bill_coin_user_assets_param_${accountType}_${accountParam} param on assets.id = param.assets_id
  </select>


  <select id="selectCheckTimeOverInitLimitByUerCoinSwapMain" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_Params_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam}
    where user_id =#{userId}
    AND coin_id =#{coinId}
    AND check_ok_time &gt;= #{checkTime}
    AND initial_time &lt;= #{checkTime}
  </select>

  <select id="selectCheckTimeOverInitLimitByUerCoin" resultMap="BaseParamResultMap">
    select
    <include refid="Base_Column_Assets_Param_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam} as assets
    left join bill_coin_user_assets_param_${accountType}_${accountParam} param on assets.id = param.assets_id
    where assets.user_id =#{userId}
    AND assets.coin_id =#{coinId}
    AND assets.check_ok_time &gt;= #{checkTime}
    AND assets.initial_time &lt;= #{checkTime}
  </select>

  <select id="selectRecordsByUserLimit" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam}
    where user_id =#{userId}
    order by check_ok_time desc limit 1
  </select>

  <select id="selectCoinsByUserAndTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam}
    where user_id =#{userId}
    AND check_ok_time = #{checkTime}
    </select>

  <select id="selectUsersWithPosition" resultType="java.lang.Long">
    select distinct user_id
    from bill_coin_user_assets_${accountType}_${accountParam}
    where id > #{minId}
    <if test="maxId != -1">
      and id &lt;= #{maxId}
    </if>
  </select>

  <select id="selectPositionUsersBetweenIdAndTime" resultType="java.lang.Long">
    select distinct user_id
    from bill_coin_user_assets_${accountType}_${accountParam}
    where id > #{minId}
      and initial_time &lt;= #{initialTime}
  </select>

  <select id="selectMaxId" resultType="java.lang.Long">
    select id
    from bill_coin_user_assets_${accountType}_${accountParam}
    order by id desc limit 1
  </select>

  <select id="selectUserCoinAssetsHistoryRecord" parameterType="com.upex.reconciliation.service.model.domain.QueryUserDTO" resultType="java.lang.Long">
    select id
    from bill_coin_user_assets_${accountType}_${accountParam}
    where
        user_id = #{userId}
        and coin_id = #{coinId}
        and check_ok_time >= #{startTime}
        and check_ok_time &lt; #{endTime}
    order by id asc
  </select>
  <select id="selectByUserAndTime" resultType="com.upex.reconciliation.service.dao.entity.BillCoinUserAssets">
    select
    <include refid="Base_Column_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam}
        where user_id =#{userId}
      and initial_time = #{checkOkTime}
  </select>
  <select id="selectByUserAndCheckTime" resultType="com.upex.reconciliation.service.dao.entity.BillCoinUserAssets">
    select
    <include refid="Base_Column_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam}
        where user_id =#{userId}
      and check_ok_time = #{checkOkTime}
  </select>

  <delete id="deleteByIds" parameterType="object">
    delete
    from bill_coin_user_assets_${accountType}_${accountParam}
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
  <delete id="deleteUserCoinAssetsHistoryRecord">
    delete
    from bill_coin_user_assets_${accountType}_${accountParam}
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <select id="queryList" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_Params_List" />
    from bill_coin_user_assets_${accountType}_${accountParam}
    where id > #{maxId}
    order by id
    limit #{pageSize}
  </select>

  <insert id="batchInsertBackup" useGeneratedKeys="true" keyProperty="id">
    insert ignore into bill_coin_user_assets_${accountType}_${accountParam}_backup (
      user_id,
      coin_id,
      check_ok_time,
      initial_time,
      prop1,
      prop2,
      prop3,
      prop4,
      prop5,
      position_flag,
      create_time,
      update_time
    )
    values
    <foreach collection="list" item="record" index="index" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{record.userId,jdbcType=BIGINT},
        #{record.coinId,jdbcType=INTEGER},
        #{record.checkOkTime,jdbcType=TIMESTAMP},
        #{record.initialTime,jdbcType=TIMESTAMP},
        #{record.prop1,jdbcType=DECIMAL},
        #{record.prop2,jdbcType=DECIMAL},
        #{record.prop3,jdbcType=DECIMAL},
        #{record.prop4,jdbcType=DECIMAL},
        #{record.prop5,jdbcType=DECIMAL},
        #{record.positionFlag,jdbcType=BIT},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP}
      </trim>
    </foreach>
  </insert>

  <select id="queryByCheckTimeAndParam" resultMap="BaseParamResultMap">
    select
      <include refid="Base_Column_Assets_Param_List"/>
    from (
      select
        ass.id,
        ass.coin_id,
        ass.user_id,
        ass.check_ok_time,
        ass.initial_time,
        ass.prop1,
        ass.prop2,
        ass.prop3,
        ass.prop4,
        ass.prop5,
        ass.position_flag,
        ass.create_time,
        ass.update_time
      from
        bill_coin_user_assets_${accountType}_${accountParam} as ass
      where ass.check_ok_time = #{checkTime,jdbcType=TIMESTAMP}
      and ass.id > #{maxId}
      order by ass.id
      limit #{pageSize}
    ) assets
    left join bill_coin_user_assets_param_${accountType}_${accountParam} param on assets.id = param.assets_id
    order by assets.id
  </select>

  <select id="selectSyncMaxId" resultType="java.lang.Long">
    select assets.id as id from bill_coin_user_assets_${accountType}_${accountParam} assets
    inner join (
      select
        user_id,
        coin_id,
        check_ok_time
      from bill_coin_user_assets_${accountType}_${accountParam}_backup
        order by id desc limit 1
    ) backup on backup.user_id = assets.user_id
    and backup.coin_id = assets.coin_id
    and backup.check_ok_time = assets.check_ok_time
    order by assets.id desc
    limit 1
  </select>

  <select id="queryByCheckTimeList" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bill_coin_user_assets_${accountType}_${accountParam}
    where check_ok_time = #{checkTime,jdbcType=TIMESTAMP}
    and id > #{maxId}
    order by id
    limit #{pageSize}
  </select>

  <select id="selectByCheckTimeMaxMinId" resultType="com.upex.reconciliation.service.model.domain.PageIdResponse">
    select min(id) as minId,max(id) as maxId
    from bill_coin_user_assets_${accountType}_${accountParam}
    where check_ok_time = #{checkTime,jdbcType=TIMESTAMP}
  </select>

  <select id="queryByCheckTimeSliceList" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bill_coin_user_assets_${accountType}_${accountParam}
    where check_ok_time = #{checkTime,jdbcType=TIMESTAMP}
    and id >= #{minId}
    and id <![CDATA[ < ]]> #{maxId}
    order by id
  </select>

  <select id="selectUserAssetsByUserIdAndCoinId" parameterType="object" resultMap="BaseParamResultMap">
    select
    <include refid="Base_Column_Assets_Param_List"/>
    from (select
    ass.id,
    ass.coin_id,
    ass.user_id,
    ass.check_ok_time,
    ass.initial_time,
    ass.prop1,
    ass.prop2,
    ass.prop3,
    ass.prop4,
    ass.prop5,
    ass.position_flag,
    ass.create_time,
    ass.update_time
    from bill_coin_user_assets_${accountType}_${accountParam} ass
    where ass.user_id =#{userId}
    AND ass.coin_id =#{coinId}
    order by ass.check_ok_time desc limit 1) assets
    left join bill_coin_user_assets_param_${accountType}_${accountParam} param on assets.id = param.assets_id
  </select>


  <select id="selectLastUserAssets" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bill_coin_user_assets_${accountType}_${accountParam}
    where check_ok_time = (select check_ok_time from bill_coin_user_assets_${accountType}_${accountParam} where user_id =#{userId}
    order by check_ok_time desc limit 1) and user_id =#{userId}
  </select>
  <select id="selectRecordsByUserIdsAndTime" resultMap="BaseParamResultMap">
    select
    <include refid="Base_Column_Assets_Param_List"/>
    from bill_coin_user_assets_${accountType}_${accountParam} as assets
    left join bill_coin_user_assets_param_${accountType}_${accountParam} param on assets.id = param.assets_id
    where assets.check_ok_time >= #{checkTime}
    and assets.initial_time <![CDATA[ <= ]]> #{checkTime}
    AND assets.user_id in
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
      #{userId}
    </foreach>
  </select>
</mapper>