<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.ReconOrderFailureRecordMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.ReconOrderFailureRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="biz_id" property="bizId" jdbcType="BIGINT"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="order_type" property="orderType" jdbcType="TINYINT"/>
        <result column="failure_type" property="failureType" jdbcType="VARCHAR"/>
        <result column="failure_reason" property="failureReason" jdbcType="VARCHAR"/>
        <result column="raw_data" property="rawData" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="retry_count" property="retryCount" jdbcType="TINYINT"/>
        <result column="process_time" property="processTime" jdbcType="TIMESTAMP"/>
        <result column="process_user" property="processUser" jdbcType="VARCHAR"/>
        <result column="process_remark" property="processRemark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, biz_id, order_id, user_id, order_type, failure_type, failure_reason, raw_data,
        status, process_time, process_user, process_remark, create_time, update_time
    </sql>

    <sql id="Table_Name">
        recon_order_failure_record
    </sql>

    <insert id="insert" parameterType="com.upex.reconciliation.service.dao.entity.ReconOrderFailureRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="Table_Name"/>
        (biz_id, order_id, user_id, order_type, failure_type, failure_reason, raw_data,
         status, process_time, process_user, process_remark, create_time, update_time)
        VALUES
        (#{bizId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT},
         #{orderType,jdbcType=TINYINT}, #{failureType,jdbcType=VARCHAR}, #{failureReason,jdbcType=VARCHAR},
         #{rawData,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{processTime,jdbcType=TIMESTAMP},
         #{processUser,jdbcType=VARCHAR}, #{processRemark,jdbcType=VARCHAR},
         #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="Table_Name"/>
        (biz_id, order_id, user_id, order_type, failure_type, failure_reason, raw_data,
         status, process_time, process_user, process_remark, create_time, update_time)
        VALUES
        <foreach collection="records" item="record" separator=",">
            (#{record.bizId,jdbcType=VARCHAR}, #{record.orderId,jdbcType=BIGINT}, #{record.userId,jdbcType=BIGINT},
             #{record.orderType,jdbcType=TINYINT}, #{record.failureType,jdbcType=VARCHAR}, #{record.failureReason,jdbcType=VARCHAR},
             #{record.rawData,jdbcType=VARCHAR}, #{record.status,jdbcType=TINYINT}, #{record.processTime,jdbcType=TIMESTAMP},
             #{record.processUser,jdbcType=VARCHAR}, #{record.processRemark,jdbcType=VARCHAR},
             #{record.createTime,jdbcType=TIMESTAMP}, #{record.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="Table_Name"/>
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByBizId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="Table_Name"/>
        WHERE biz_id = #{bizId,jdbcType=BIGINT}
        LIMIT 1
    </select>

    <select id="selectByOrderId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="Table_Name"/>
        WHERE order_id = #{orderId,jdbcType=BIGINT}
        LIMIT 1
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="Table_Name"/>
        WHERE user_id = #{userId,jdbcType=BIGINT}
        <if test="startTime != null">
            AND create_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY create_time DESC
    </select>

    <update id="updateStatus">
        UPDATE <include refid="Table_Name"/>
        SET status = #{status,jdbcType=TINYINT},
            process_time = NOW(),
            process_user = #{processUser,jdbcType=VARCHAR},
            process_remark = #{processRemark,jdbcType=VARCHAR},
            update_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateRetryCount">
        UPDATE <include refid="Table_Name"/>
        SET retry_count = #{retryCount,jdbcType=TINYINT},
        update_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>


    <select id="selectUnprocessed" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="Table_Name"/>
        WHERE status = 0
        <if test="orderType != null">
            AND order_type = #{orderType,jdbcType=TINYINT}
        </if>
        <if test="failureType != null">
            AND failure_type = #{failureType,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>