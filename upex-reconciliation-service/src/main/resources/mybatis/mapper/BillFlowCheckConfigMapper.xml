<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillFlowCheckConfigMapper" >
  <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillFlowCheckConfig" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="account_type" property="accountType" jdbcType="VARCHAR" />
    <result column="check_type" property="checkType" jdbcType="VARCHAR" />
    <result column="check_desc" property="checkDesc" jdbcType="VARCHAR" />
    <result column="param" property="param" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="base_column_list" >
    id, type, account_type, check_type, check_desc, param, create_time, update_time
  </sql>
  <sql id="base_column_list_not_id" >
    type, account_type, check_type, check_desc, param, create_time, update_time
  </sql>
  <sql id="batch_insert_value">
    #{record.type}
    ,#{record.accountType}
    ,#{record.checkType}
    ,#{record.checkDesc}
    ,#{record.param}
    ,#{record.createTime}
    ,#{record.updateTime}
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="base_column_list" />
    from bill_flow_check_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from bill_flow_check_config
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <update id="updateByPrimaryKeySelective" parameterType="com.upex.reconciliation.service.dao.entity.BillWhiteListConfig">
    update bill_flow_check_config
    <set>
      <if test="type != null">
        type = #{type},
      </if>
      <if test="accountType != null">
        account_type = #{accountType},
      </if>
      <if test="checkType != null">
        check_type = #{checkType},
      </if>
      <if test="checkDesc != null">
        check_desc = #{checkDesc},
      </if>
      <if test="param != null">
        param = #{param},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
    </set>
    where id = #{id}
  </update>

  <insert id="batchInsert" parameterType="com.upex.reconciliation.service.dao.entity.BillWhiteListConfig">
    insert into bill_flow_check_config(<include refid="base_column_list_not_id"></include>)
    values
    <foreach collection="records" item="record" index="index" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <include refid="batch_insert_value"/>
      </trim>
    </foreach>
  </insert>

  <select id="getBillConfigList" resultMap="BaseResultMap">
    select
    <include refid="base_column_list"/>
    from bill_flow_check_config where type = #{type} and account_type = #{accountType}
  </select>
</mapper>