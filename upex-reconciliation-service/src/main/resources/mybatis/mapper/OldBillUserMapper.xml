<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.OldBillUserMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.bill.entity.BillUser">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="check_ok_time" property="checkOkTime" jdbcType="TIMESTAMP"/>
        <result column="initial_time" property="initialTime" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, user_id, check_ok_time,initial_time, `status`, create_time, update_time
    </sql>

    <select id="selectByUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_user_${accountType}_${accountParam}
        where user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <sql id="batch_insert_value">
        #{record.userId}, #{record.checkOkTime},#{record.initialTime}, #{record.status},
        #{record.createTime}, #{record.updateTime}
    </sql>
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into bill_user_${accountType}_${accountParam} (user_id, check_ok_time,
        initial_time,`status`, create_time, update_time)
        values
        <foreach collection="list" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>


    <update id="updateByUserIds">
        update bill_user_${accountType}_${accountParam}
        set check_ok_time = #{checkOkTime}, update_time = #{currentTime}
        where user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

    <update id="updateByUserId" parameterType="com.upex.reconciliation.service.dao.bill.entity.BillUser">
        update bill_user_${accountType}_${accountParam}
        set
        check_ok_time = #{checkOkTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where user_id = #{userId,jdbcType=BIGINT}
    </update>


    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_user_${accountType}_${accountParam}
        where user_id = #{userId}
    </select>

    <select id="selectByUserIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_user_${accountType}_${accountParam}
        where user_id in
        <foreach collection="userIdList" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="selectBySingleUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_user_${accountType}_${accountParam}
        where user_id =#{userId} order by check_ok_time desc limit 1
    </select>

    <select id="listUsersFixLength"  parameterType="object" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_user_${accountType}_${accountParam}
        where initial_time >= #{startTime}
        and initial_time &lt;#{endTime}
        and id &gt;#{deleteSyncPos}
        order by id asc
        limit #{fixLength}
    </select>
    <delete id="deleteInitialTimeAfterRecord" parameterType="object">
      delete
      from bill_user_${accountType}_${accountParam}
      where initial_time &gt; #{resetCheckTime}
    </delete>
    <update id="resetCheckTime" parameterType="object">
      update bill_user_${accountType}_${accountParam}
      set check_ok_time=#{resetCheckTime}
      where check_ok_time &gt; #{resetCheckTime}
    </update>
    <select id="listByCheckTime" parameterType="object" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_user_${accountType}_${accountParam}
        where check_ok_time=#{checkOkTime}
    </select>
    <insert id="insertSingle" useGeneratedKeys="true" keyProperty="id">
        insert into bill_user_${accountType}_${accountParam} (user_id, check_ok_time,
        initial_time,`status`, create_time, update_time)
        values (
        <include refid="batch_insert_value"/>
        )
    </insert>
    <select id="selectOneRecord" parameterType="object" resultType="com.upex.reconciliation.service.dao.bill.entity.BillUser">
        select
        <include refid="Base_Column_List"/>
        from bill_user_${accountType}_${accountParam}
        limit 1
    </select>



    <select id="selectInitialTimeWithNull"  parameterType="object" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_user_${accountType}_${accountParam}
        where initial_time is null
        order by id asc
        limit #{pageSize}
    </select>
    <select id="selectUserList" resultType="com.upex.reconciliation.service.dao.bill.entity.BillUser">
        select
        <include refid="Base_Column_List"/>
        from bill_user_${accountType}_${accountParam}
        where id &gt; #{maxId}
        order by id asc
        limit #{pageSize}
    </select>
    <select id="selectUsersByUserIdsAndType" resultType="java.lang.Long">
        select
        user_id
        from bill_user_${accountType}_${accountParam}
        where user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>
    <select id="selectCheckForTheResults" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from bill_user_${accountType}_${accountParam}
        where status !=1 and user_id=#{fUid}
    </select>

    <select id="selectCheckByUidAndBusinessTime" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from bill_user_${accountType}_${accountParam}
        where status !=1 and user_id=#{fUid}
    </select>

    <update id="updateInitialTimeById">
        update bill_user_${accountType}_${accountParam}
        set initial_time = #{initialTime}, update_time = #{updateTime}
        where id  = #{id}
    </update>


    <select id="selectUserIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_user_${accountType}_${accountParam}
        where id &gt; #{maxId}
        order by id asc
        limit #{pageSize}
    </select>
</mapper>