<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.StatisticsPropertyMapper" >
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.StatisticsProperty" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="statistics_result" property="statisticsResult" jdbcType="TINYINT" />
        <result column="at_user_flag" property="atUserFlag" jdbcType="TINYINT" />
        <result column="statistics_type" property="statisticsType" jdbcType="VARCHAR" />
        <result column="check_bill_time" property="checkBillTime" jdbcType="TIMESTAMP" />
        <result column="snapshot_time" property="snapshotTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="params" property="params" jdbcType="VARCHAR" />
        <result column="bill_diff_value" property="billDiffValue" jdbcType="DECIMAL" />
    </resultMap>
    <sql id="Base_Column_List" >
        id,status,statistics_result,at_user_flag, statistics_type,check_bill_time,snapshot_time,create_time,update_time,params, bill_diff_value
    </sql>
    <select id="selectOneBySnapshotTime" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset
        where snapshot_time = #{snapshotTime}
        and statistics_type = #{type}
    </select>
    <insert id="insertRecord" parameterType="com.upex.reconciliation.service.dao.entity.StatisticsProperty" >
        insert ignore into bill_statistics_asset
         (status,statistics_result,at_user_flag, statistics_type,check_bill_time,snapshot_time,create_time,update_time,params,bill_diff_value)
        values(
         #{status,jdbcType=TINYINT},
         #{statisticsResult,jdbcType=TINYINT},
         #{atUserFlag,jdbcType=TINYINT},
         #{statisticsType,jdbcType=VARCHAR},
         #{checkBillTime,jdbcType=TIMESTAMP},
         #{snapshotTime,jdbcType=TIMESTAMP},
         #{createTime,jdbcType=TIMESTAMP},
         #{updateTime,jdbcType=TIMESTAMP},
         #{params,jdbcType=VARCHAR},
         #{billDiffValue,jdbcType=DECIMAL}
         )
    </insert>
    <update id="updateBySnapshotTime" parameterType="com.upex.reconciliation.service.dao.entity.StatisticsProperty">
        update bill_statistics_asset
        set status = #{status},
            statistics_result = #{statisticsResult},
            at_user_flag = #{atUserFlag},
            bill_diff_value = #{billDiffValue},
            update_time = #{updateTime},
            check_bill_time = #{checkBillTime}
        where snapshot_time = #{snapshotTime}
            and statistics_type = #{statisticsType}
    </update>
    <select id="selectOneNearSnapshotTimeAndStatus" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset
        where snapshot_time &lt;= #{snapshotTime} and status = #{status} order by snapshot_time desc limit 1
    </select>

    <select id="selectLastNearSnapshotTimeAndStatus" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset
        where snapshot_time &gt;= #{snapshotTime} and status = #{status} order by snapshot_time desc limit 1
    </select>

    <delete id="deleteByTime" parameterType="object">
    delete from bill_statistics_asset
    where snapshot_time &gt;= #{snapshotTime}
    </delete>
    <delete id="deleteById">
        delete from bill_statistics_asset
        where id = #{id}
    </delete>

    <update id="updateStatusAfterSnapshotTime" parameterType="com.upex.reconciliation.service.dao.entity.StatisticsProperty">
        update bill_statistics_asset
        set status = #{status},
            statistics_result = #{statisticsResult},
            update_time = #{updateTime}
        where snapshot_time &gt;= #{snapshotTime}
          and statistics_type = #{statisticsType}
    </update>
    <update id="updateConfigById">
        update bill_statistics_asset
        set status            = #{status},
            statistics_result = #{result},
            at_user_flag = #{flag},
            bill_diff_value   = #{value}
        where id = #{id}
    </update>

    <select id="selectFirstDone" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset
        where status = 2 order by snapshot_time limit 1
    </select>
    <select id="selectLastDoneAndCorrect" resultType="com.upex.reconciliation.service.dao.entity.StatisticsProperty">
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset
        where status = 2 and statistics_result > 0
        order by snapshot_time desc limit 1
    </select>
    <select id="selectStatisticsPropertyByStatus" resultType="com.upex.reconciliation.service.dao.entity.StatisticsProperty">
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset
        where snapshot_time >= #{snapshotTime} and status = #{status}
        order by snapshot_time
    </select>
    <select id="selectLastDoneAndCorrectByTime" resultType="com.upex.reconciliation.service.dao.entity.StatisticsProperty">
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset
        where snapshot_time &lt; #{snapshotTime} and status = 2 and statistics_result > 0
        order by snapshot_time desc limit 1
    </select>
    <select id="selectPropertyListByGreaterTimeDesc" resultType="com.upex.reconciliation.service.dao.entity.StatisticsProperty">
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset
        where snapshot_time > #{snapshotTime}
        order by snapshot_time desc
    </select>
    <select id="selectLastNormalConfig" resultType="com.upex.reconciliation.service.dao.entity.StatisticsProperty">
        select
        <include refid="Base_Column_List" />
        from bill_statistics_asset
        where snapshot_time &lt; #{snapshotTime} and status = 2
          and statistics_result > 0 and at_user_flag = 0
        order by snapshot_time desc limit 1
    </select>
</mapper>