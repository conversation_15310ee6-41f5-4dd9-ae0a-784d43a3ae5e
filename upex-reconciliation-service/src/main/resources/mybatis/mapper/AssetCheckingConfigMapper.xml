<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.AssetCheckingConfigMapper">

    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.AssetCheckingConfig">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="bizType" column="biz_type" jdbcType="VARCHAR"/>
        <result property="checkOkTime" column="check_ok_time" jdbcType="TIMESTAMP"/>
        <result property="billCapitalValue" column="bill_capital_value" jdbcType="DECIMAL"/>
        <result property="billCapitalAbsValue" column="bill_capital_abs_value" jdbcType="DECIMAL"/>
        <result property="billCapitalStatus" column="bill_capital_status" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="param" column="param" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            id,
            biz_type,
            check_ok_time,
            bill_capital_value,
            bill_capital_abs_value,
            bill_capital_status,
            create_time,
            update_time,
            param
    </sql>

    <select id="getLastAssetCheckingConfig"  resultMap="BaseResultMap" >
         select
            <include refid="Base_Column_List" />
         from asset_checking_config
         where business_type = #{businessType}
         order by id desc
         limit 1
    </select>


    <select id="getLastAssetCheckingConfigByStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from asset_checking_config
        where business_type = #{businessType}
        and bill_capital_status = #{billCapitalStatus}
        order by id desc
        limit 1
    </select>


</mapper>
