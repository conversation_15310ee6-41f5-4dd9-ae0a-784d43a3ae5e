<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.upex.reconciliation.service.dao.mapper.cex.CexAssetConfigMapper">

    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.cex.entity.CexAssetConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="asset_history_type" property="assetHistoryType" jdbcType="INTEGER"/>
        <result column="cex_type" property="cexType" jdbcType="INTEGER"/>
        <result column="cex_user_id" property="cexUserId" jdbcType="VARCHAR"/>
        <result column="check_sync_time" property="checkSyncTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 查询：根据资产类型获取配置 -->
    <select id="selectByAssetType" resultMap="BaseResultMap">
        SELECT *
        FROM cex_asset_config
        WHERE asset_history_type = #{assetType}
        AND cex_type = #{cexType}
        AND cex_user_id = #{cexUserId}
    </select>

    <!-- 插入：新增资产配置 -->
    <insert id="insert">
        INSERT INTO cex_asset_config (
            asset_history_type,
            check_sync_time,
            cex_type,
            cex_user_id,
            status,
            create_time,
            update_time
        ) VALUES (
            #{assetHistoryType},
            #{checkSyncTime},
            #{cexType},
            #{cexUserId},
            #{status},
            #{createTime},
            #{updateTime}
        )
    </insert>

    <update id="update">
        UPDATE cex_asset_config
        SET
            check_sync_time = #{checkSyncTime},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

</mapper>
