<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.upex.reconciliation.service.dao.mapper.cex.ThirdCexUserMapper">

    <resultMap id="ThirdCexUserMap" type="com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cex_type" property="cexType" jdbcType="INTEGER"/>
        <result column="cex_email" property="cexEmail" jdbcType="VARCHAR" typeHandler="com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler"/>
        <result column="cex_user_id" property="cexUserId" jdbcType="VARCHAR"/>
        <result column="parent_user_id" property="parentUserId" jdbcType="VARCHAR"/>
        <result column="user_kyb" property="userKyb" jdbcType="VARCHAR"/>
        <result column="user_manage_status" property="userManageStatus" jdbcType="INTEGER"/>
        <result column="cex_user_status" property="cexUserStatus" jdbcType="INTEGER"/>
        <result column="use_type" property="useType" jdbcType="INTEGER"/>
        <result column="trade_type" property="tradeType" jdbcType="INTEGER"/>
        <result column="user_type" property="userType" jdbcType="INTEGER"/>
        <result column="api_key_status" property="apiKeyStatus" jdbcType="INTEGER"/>
        <result column="user_manager_email" property="userManagerEmail" jdbcType="VARCHAR"/>
        <result column="user_manager_id" property="userManagerId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
            id,
            cex_type,
            cex_email,
            cex_user_id,
            parent_user_id,
            user_kyb,
            user_manage_status,
            cex_user_status,
            use_type,
            trade_type,
            user_type,
            api_key_status,
            user_manager_email,
            user_manager_id,
            create_time,
            update_time
        </sql>


    <select id="selectById" resultMap="ThirdCexUserMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_user
        WHERE id = #{id}
    </select>
    <!-- 根据交易所类型和用户ID查询单个用户 -->
    <select id="selectByCexTypeAndUserId" resultMap="ThirdCexUserMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_user
        WHERE cex_type = #{cexType}
        AND cex_user_id = #{cexUserId}
    </select>


    <insert id="insert">
        INSERT INTO third_cex_user (
        cex_type,
        cex_email,
        cex_user_id,
        parent_user_id,
        user_kyb,
        user_manage_status,
        cex_user_status,
        use_type,
        trade_type,
        user_type,
        api_key_status,
        user_manager_email,
        user_manager_id,
        create_time,
        update_time
        ) VALUES (
        #{cexType},
        #{cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
        #{cexUserId},
        #{parentUserId},
        #{userKyb},
        #{userManageStatus},
        #{cexUserStatus},
        #{useType},
        #{tradeType},
        #{userType},
        #{apiKeyStatus},
        #{userManagerEmail},
        #{userManagerId},
        #{createTime},
        #{updateTime}
        )
    </insert>

    <update id="update">
        UPDATE third_cex_user
        <set>
            <!-- 交易所类型 -->
            <if test="cexType != null">
                cex_type = #{cexType},
            </if>

            <!-- 交易所注册邮箱 -->
            <if test="cexEmail != null and cexEmail != ''">
                cex_email = #{cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
            </if>

            <!-- 交易所用户ID -->
            <if test="cexUserId != null">
                cex_user_id = #{cexUserId},
            </if>

            <!-- 父用户ID -->
            <if test="parentUserId != null">
                parent_user_id = #{parentUserId},
            </if>
            <if test="userKyb !=null">
                user_kyb = #{userKyb},
            </if>
            <!-- 用户管理状态 -->
            <if test="userManageStatus != null">
                user_manage_status = #{userManageStatus},
            </if>

            <!-- CEX 用户状态 -->
            <if test="cexUserStatus != null">
                cex_user_status = #{cexUserStatus},
            </if>

            <!-- 账户用途 -->
            <if test="useType != null">
                use_type = #{useType},
            </if>

            <!-- 交易模式 -->
            <if test="tradeType != null">
                trade_type = #{tradeType},
            </if>

            <!-- 用户类型 -->
            <if test="userType != null">
                user_type = #{userType},
            </if>

            <!-- API Key 状态 -->
            <if test="apiKeyStatus != null">
                api_key_status = #{apiKeyStatus},
            </if>

            <!-- 用户管理员邮箱 -->
            <if test="userManagerEmail != null and userManagerEmail != ''">
                user_manager_email = #{userManagerEmail},
            </if>
            <if test="userManagerId != null">
                user_manager_id = #{userManagerId},
            </if>

            <!-- 更新时间 -->
            <if test="updateTime != null">
                update_time = #{updateTime}
            </if>
        </set>
        WHERE id = #{id}
    </update>
    <insert id="batchInsert">
        INSERT INTO third_cex_user (
        cex_type,
        cex_email,
        cex_user_id,
        parent_user_id,
        user_kyb,
        user_manage_status,
        cex_user_status,
        use_type,
        trade_type,
        user_type,
        api_key_status,
        user_manager_email,
        user_manager_id,
        create_time,
        update_time
        ) VALUES
        <foreach collection="users" item="user" separator=",">
            (
            #{user.cexType},
            #{user.cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
            #{user.cexUserId},
            #{user.parentUserId},
            #{user.userKyb},
            #{user.userManageStatus},
            #{user.cexUserStatus},
            #{user.useType},
            #{user.tradeType},
            #{user.userType},
            #{user.apiKeyStatus},
            #{user.userManagerEmail},
            #{user.userManagerId},
            #{user.createTime},
            #{user.updateTime}
            )
        </foreach>
    </insert>

    <insert id="batchInsertSubUser">
        INSERT INTO third_cex_user (
        id,
        cex_type,
        cex_email,
        cex_user_id,
        parent_user_id,
        user_kyb,
        user_manage_status,
        cex_user_status,
        use_type,
        trade_type,
        user_type,
        api_key_status,
        user_manager_email,
        user_manager_id,
        create_time,
        update_time
        ) VALUES
        <foreach collection="users" item="user" separator=",">
            (
            #{user.id},
            #{user.cexType},
            #{user.cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
            #{user.cexUserId},
            #{user.parentUserId},
            #{user.userKyb},
            #{user.userManageStatus},
            #{user.cexUserStatus},
            #{user.useType},
            #{user.tradeType},
            #{user.userType},
            #{user.apiKeyStatus},
            #{user.userManagerEmail},
            #{user.userManagerId},
            #{user.createTime},
            #{user.updateTime}
            )
        </foreach>
    </insert>
    <select id="selectByPage"
            parameterType="com.upex.reconciliation.service.service.client.cex.dto.req.common.CexUserListRequest"
            resultMap="ThirdCexUserMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_user
        <where>

            <!-- 交易所类型 -->
            <if test="cexType != null">
                AND cex_type = #{cexType}
            </if>

            <!-- 账户用途 -->
            <if test="useType != null">
                AND use_type = #{useType}
            </if>

            <!-- 用户状态 -->
            <if test="userManagerStatus != null">
                AND user_manage_status = #{userManagerStatus}
            </if>
            <!-- 用户管理人邮箱 -->
            <if test="userManagerEmail != null and userManagerEmail != ''">
                AND user_manager_email = #{userManagerEmail}
            </if>
            <if test="userManagerId !=null and userManagerId !=''" >
                AND user_manager_id = #{userManagerId}
            </if>
            <if test="cexUserId !=null and cexUserId !=''">
                AND cex_user_id = #{cexUserId}
            </if>
            <if test="cexUserStatus !=null ">
                AND cex_user_status = #{cexUserStatus}
            </if>
        </where>
        ORDER BY id DESC
<!--        LIMIT #{offset}, #{pageSize}-->
    </select>


    <select id="countByPage"
            parameterType="com.upex.reconciliation.service.service.client.cex.dto.req.common.CexUserListRequest"
            resultType="int">
        SELECT COUNT(1)
        FROM third_cex_user
        <where>

            <!-- 同上 -->
            <if test="cexType != null">
                AND cex_type = #{cexType}
            </if>
            <if test="useType != null">
                AND use_type = #{useType}
            </if>
            <if test="userManagerStatus != null">
                AND user_manage_status = #{userManagerStatus}
            </if>
            <if test="userManagerEmail != null and userManagerEmail != ''">
                AND user_manager_email = #{userManagerEmail}
            </if>
            <if test="userManagerId !=null and userManagerId !=''" >
                AND user_manager_id = #{userManagerId}
            </if>
            <if test="cexUserId !=null and cexUserId !=''">
                AND cex_user_id = #{cexUserId}
            </if>
            <if test="cexUserStatus !=null ">
                AND cex_user_status = #{cexUserStatus}
            </if>
        </where>
    </select>

    <select id="selectByCondition" parameterType="com.upex.reconciliation.service.service.client.cex.dto.req.common.CexUserListRequest"
            resultMap="ThirdCexUserMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_user
        WHERE id>0
        <if test="cexType != null">
            AND cex_type = #{cexType}
        </if>
        <if test="cexUserId != null">
            AND cex_user_id = #{cexUserId}
        </if>
        <if test="userManagerEmail != null">
            AND user_manager_email = #{userManagerEmail}
        </if>
        <if test="userManagerId != null">
            AND user_manager_id = #{userManagerId}
        </if>
        <if test="userManagerStatus != null">
            AND user_manage_status = #{userManagerStatus}
        </if>
        <if test="cexUserStatus != null">
            AND cex_user_status = #{cexUserStatus}
        </if>
        <if test="useType != null">
            AND use_type = #{useType}
        </if>
    </select>

    <select id="selectSubUserByCexTypeAndParentUserId" resultMap="ThirdCexUserMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_user
        WHERE parent_user_id = #{parentUserId}
        AND cex_type = #{cexType}
    </select>

    <delete id="deleteSubUserByCexTypeAndParentUserId">
        DELETE FROM third_cex_user
        WHERE parent_user_id = #{parentUserId}
        AND cex_type = #{cexType}
    </delete>

    <select id="selectSubUserByPartentUserIds" resultMap="ThirdCexUserMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_user
        WHERE cex_type=#{cexTy} and parent_user_id IN
        <foreach item="item" collection="cexUserIds" separator="," close=")" open="(" index="index">
            #{item}
        </foreach>
    </select>

    <select id="selectAllParentUserByCexType" resultMap="ThirdCexUserMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_user
        WHERE cex_type=#{cexType} and parent_user_id IS NULL
    </select>

    <select id="selectSubUser"
            parameterType="com.upex.reconciliation.service.service.client.cex.dto.req.common.CexUserListRequest"
            resultMap="ThirdCexUserMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_user
        <where>
            <if test="cexUserId !=null">
                and parent_user_id =#{cexUserId}
            </if>

            <!-- 交易所类型 -->
            <if test="cexType != null">
                AND cex_type = #{cexType}
            </if>

            <!-- 账户用途 -->
            <if test="useType != null">
                AND use_type = #{useType}
            </if>

            <!-- 用户状态 -->
            <if test="userManagerStatus != null">
                AND user_manage_status = #{userManagerStatus}
            </if>

            <!-- 用户管理人邮箱 -->
            <if test="userManagerEmail != null and userManagerEmail != ''">
                AND user_manager_email = #{userManagerEmail}
            </if>
        </where>
        ORDER BY id DESC
    </select>
    <update id="updateSubUserApikeyStatus">
        UPDATE third_cex_user
        SET api_key_status = #{apikeyStatus},
        update_time = now()
        WHERE parent_user_id = #{parentCexUserId}
    </update>
</mapper>
