<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillBizTypeConfigMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillBizTypeConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="account_type" property="accountType" jdbcType="TINYINT"/>
        <result column="account_param" property="accountParam" jdbcType="VARCHAR"/>
        <result column="biz_type" property="bizType" jdbcType="VARCHAR"/>
        <result column="biz_type_name" property="bizTypeName" jdbcType="VARCHAR"/>
        <result column="decoder_abandon" property="decoderAbandon" jdbcType="TINYINT"/>
        <result column="allow_value_type" property="allowValueType" jdbcType="TINYINT"/>
        <result column="demo_account" property="demoAccount" jdbcType="TINYINT"/>
        <result column="system_account" property="systemAccount" jdbcType="TINYINT"/>
        <result column="system_account_type" property="systemAccountType" jdbcType="VARCHAR"/>
        <result column="user_ids" property="userIds" jdbcType="VARCHAR"/>
        <result column="biz_type_extend" property="bizTypeExtend" jdbcType="VARCHAR"/>
        <result column="biz_in_out" property="bizInOut" jdbcType="TINYINT"/>
        <result column="lever_spot_in_out" property="leverSpotInOut" jdbcType="TINYINT"/>
        <result column="internal_in_out" property="internalInOut" jdbcType="TINYINT"/>
        <result column="max_amount" property="maxAmount" jdbcType="DECIMAL"/>
        <result column="min_amount" property="minAmount" jdbcType="DECIMAL"/>
        <result column="time_range" property="timeRange" jdbcType="VARCHAR"/>
        <result column="max_qpm" property="maxQpm" jdbcType="INTEGER"/>
        <result column="demo_account_forbid" property="demoAccountForbid" jdbcType="TINYINT"/>
        <result column="pap_account" property="papAccount" jdbcType="TINYINT"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`,`create_time`,`update_time`,`account_type`,`account_param`,`biz_type` ,`biz_type_name`,`decoder_abandon` ,`allow_value_type`,`demo_account`,`system_account`, `system_account_type`
        ,`user_ids`,`biz_type_extend`,`biz_in_out`,`lever_spot_in_out`,`internal_in_out`,`max_amount`,`min_amount`,`time_range`,`max_qpm`,`demo_account_forbid`,pap_account
    </sql>
    <sql id="Base_Column_List_Not_Id">
        `create_time`,`update_time`,`account_type`,`account_param`,`biz_type` ,`biz_type_name`,`decoder_abandon` ,`allow_value_type`,`demo_account`,`system_account`, `system_account_type`
        ,`user_ids`,`biz_type_extend`,`biz_in_out`,`lever_spot_in_out`,`internal_in_out`,`max_amount`,`min_amount`,`time_range`,`max_qpm`,`demo_account_forbid`,pap_account
    </sql>
    <sql id="batch_insert_value">
        #{record.createTime}
        ,#{record.updateTime}
        ,#{record.accountType}
        ,#{record.accountParam}
        ,#{record.bizType}
        ,#{record.bizTypeName}
        ,#{record.decoderAbandon}
        ,#{record.allowValueType}
        ,#{record.demoAccount}
        ,#{record.systemAccount}
        ,#{record.systemAccountType}
        ,#{record.userIds}
        ,#{record.bizTypeExtend}
        ,#{record.bizInOut}
        ,#{record.leverSpotInOut}
        ,#{record.internalInOut}
        ,#{record.maxAmount}
        ,#{record.minAmount}
        ,#{record.timeRange}
        ,#{record.maxQpm}
        ,#{record.demoAccountForbid}
        ,#{record.papAccount}
     </sql>

    <select id="selectById" resultMap="BaseResultMap" parameterType="object">
        select
        <include refid="Base_Column_List"/>
        from bill_biz_type_config
        where id=#{id,jdbcType=BIGINT}
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.upex.reconciliation.service.dao.entity.BillBizTypeConfig">
        update bill_biz_type_config
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="accountType != null">
                account_type = #{accountType},
            </if>
            <if test="accountParam != null">
                account_param = #{accountParam},
            </if>
            <if test="bizType != null">
                biz_type = #{bizType},
            </if>
            <if test="bizTypeName != null">
                biz_type_name = #{bizTypeName},
            </if>
            <if test="decoderAbandon != null">
                decoder_abandon = #{decoderAbandon},
            </if>
            <if test="allowValueType != null">
                allow_value_type = #{allowValueType},
            </if>
            <if test="demoAccount != null">
                demo_account = #{demoAccount},
            </if>
            <if test="systemAccount != null">
                system_account = #{systemAccount},
            </if>
            <if test="systemAccountType != null">
                system_account_type = #{systemAccountType},
            </if>
            <if test="userIds != null">
                user_ids = #{userIds},
            </if>
            <if test="bizTypeExtend != null">
                biz_type_extend = #{bizTypeExtend},
            </if>
            <if test="bizInOut != null">
                biz_in_out = #{bizInOut},
            </if>
            <if test="leverSpotInOut != null">
                lever_spot_in_out = #{leverSpotInOut},
            </if>
            <if test="internalInOut != null">
                internal_in_out = #{internalInOut},
            </if>
            <if test="maxAmount != null">
                max_amount = #{maxAmount},
            </if>
            <if test="minAmount != null">
                min_amount = #{minAmount},
            </if>
            <if test="timeRange != null">
                time_range = #{timeRange},
            </if>
            <if test="maxQpm != null">
                max_qpm = #{maxQpm},
            </if>
            <if test="demoAccountForbid != null">
                demo_account_forbid = #{demoAccountForbid},
            </if>
            <if test="papAccount != null">
                pap_account = #{papAccount},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectByTypeAndParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_biz_type_config
        where account_type = #{accountType} and account_param = #{accountParam}
    </select>

    <insert id="batchInsert" parameterType="com.upex.reconciliation.service.dao.entity.BillBizTypeConfig">
        insert into bill_biz_type_config(<include refid="Base_Column_List_Not_Id"></include>)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <select id="listAllBillConfigs" resultMap="BaseResultMap" parameterType="object">
        select
        <include refid="Base_Column_List"/>
        from bill_biz_type_config
    </select>

    <delete id="deleteById">
        delete from
        bill_biz_type_config
        where id = #{id}
    </delete>

    <select id="selectByTypeAndParamAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bill_biz_type_config
        where account_type = #{accountType}
        and account_param = #{accountParam}
        and biz_type = #{bizType}
    </select>
</mapper>