<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillContractProfitCoinDetailMapper">

    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillContractProfitCoinDetail">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <id property="batchNo" column="batch_no" jdbcType="BIGINT"/>
            <result property="checkOkTime" column="check_ok_time" jdbcType="TIMESTAMP"/>
            <result property="accountType" column="account_type" jdbcType="TINYINT"/>
            <result property="accountParam" column="account_param" jdbcType="VARCHAR"/>
            <result property="profitType" column="profit_type" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="coinId" column="coin_id" jdbcType="INTEGER"/>
            <result property="realizedCount" column="realized_count" jdbcType="DECIMAL"/>
            <result property="changeRealizedCount" column="change_realized_count" jdbcType="DECIMAL"/>
            <result property="unrealizedCount" column="unrealized_count" jdbcType="DECIMAL"/>
            <result property="profitCount" column="profit_count" jdbcType="DECIMAL"/>
            <result property="profitCountIncr" column="profit_count_incr" jdbcType="DECIMAL"/>
            <result property="profitAmount" column="profit_amount" jdbcType="DECIMAL"/>
            <result property="rate" column="rate" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="initCount" column="init_count" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        batch_no,
        check_ok_time,
        account_type,
        account_param,
        profit_type,
        status,
        coin_id,
        realized_count,
        change_realized_count,
        unrealized_count,
        profit_count,
        profit_count_incr,
        profit_amount,
        rate,
        create_time,
        update_time,
        init_count
    </sql>

    <sql id="Table_Name">
        bill_contract_profit_coin_detail_${accountType}_${accountParam}
    </sql>

    <sql id="Table_Name_His">
        bill_contract_profit_coin_detail_${accountType}_${accountParam}_his
    </sql>

    <sql id="batch_insert_value">
        #{record.id,jdbcType=BIGINT},
        #{record.batchNo,jdbcType=BIGINT},
        #{record.checkOkTime,jdbcType=TIMESTAMP},
        #{record.accountType,jdbcType=TINYINT},
        #{record.accountParam,jdbcType=VARCHAR},
        #{record.profitType,jdbcType=VARCHAR},
        #{record.status,jdbcType=INTEGER},
        #{record.coinId,jdbcType=INTEGER},
        #{record.realizedCount,jdbcType=DECIMAL},
        #{record.changeRealizedCount,jdbcType=DECIMAL},
        #{record.unrealizedCount,jdbcType=DECIMAL},
        #{record.profitCount,jdbcType=DECIMAL},
        #{record.profitCountIncr,jdbcType=DECIMAL},
        #{record.profitAmount,jdbcType=DECIMAL},
        #{record.rate,jdbcType=DECIMAL},
        #{record.createTime,jdbcType=TIMESTAMP},
        #{record.updateTime,jdbcType=TIMESTAMP},
        #{record.initCount,jdbcType=DECIMAL}
    </sql>
    <update id="batchUpdateStatus">
        update <include refid="Table_Name" />
        set
        status =  #{newStatus,jdbcType=INTEGER},
        update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and  status =  #{oldStatus,jdbcType=INTEGER}
    </update>

    <select id="getBillContractProfitCoinDetailList" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from <include refid="Table_Name" />
        where check_ok_time =  #{checkOkTime,jdbcType=TIMESTAMP}
        and profit_type = #{profitType,jdbcType=VARCHAR}
    </select>

    <select id="getAllBillContractProfitCoinDetailList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="Table_Name" />
        where check_ok_time = #{checkOkTime,jdbcType=TIMESTAMP}
        and profit_type in
        <foreach collection="profitTypeList" item="profitType" open="(" separator="," close=")">
            #{profitType}
        </foreach>
    </select>
    <select id="getAllAfterRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="Table_Name" />
        where check_ok_time &gt; #{resetCheckTime}
    </select>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="Table_Name" /> (
            id,
            batch_no,
            check_ok_time,
            account_type,
            account_param,
            profit_type,
            status,
            coin_id,
            realized_count,
            change_realized_count,
            unrealized_count,
            profit_count,
            profit_count_incr,
            profit_amount,
            rate,
            create_time,
            update_time,
            init_count)
        values
        <foreach collection="list" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <insert id="batchInsertHis" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="Table_Name_His" /> (
        id,
        batch_no,
        check_ok_time,
        account_type,
        account_param,
        profit_type,
        status,
        coin_id,
        realized_count,
        change_realized_count,
        unrealized_count,
        profit_count,
        profit_count_incr,
        profit_amount,
        rate,
        create_time,
        update_time,
        init_count)
        values
        <foreach collection="list" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <delete id="deleteAfterRecord" parameterType="object">
        delete
        from <include refid="Table_Name" />
        where check_ok_time &gt; #{resetCheckTime}
    </delete>


    <select id="getLastCheckOkTime" parameterType="object" resultType="java.util.Date">
        select check_ok_time
        from <include refid="Table_Name" />
        order by check_ok_time desc
        limit 1
    </select>

    <update id="updateById" parameterType="com.upex.reconciliation.service.dao.entity.BillContractProfitCoinDetail">
        update <include refid="Table_Name" />
        set
        realized_count = #{record.realizedCount},
        profit_count = #{record.profitCount},
        profit_amount = #{record.profitAmount},
        profit_count_incr = #{record.profitCountIncr},
        status =  #{record.status},
        update_time =  #{record.updateTime}
        where  id = #{record.id}
    </update>

    <delete id="deleteByCheckTime">
        delete from
        <include refid="Table_Name"/>
        where check_ok_time = #{checkTime}
    </delete>

    <delete id="batchDelete">
        delete from
        <include refid="Table_Name"/>
        where id >= #{beginId}
        limit #{pageSize}
    </delete>

    <select id="selectListByAccountTypeAndCheckTime"
            resultType="com.upex.reconciliation.service.dao.entity.BillContractProfitCoinDetail">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where account_type = #{accountType}
        and account_param = #{accountParam}
        and check_ok_time = #{checkTime}
    </select>
</mapper>
