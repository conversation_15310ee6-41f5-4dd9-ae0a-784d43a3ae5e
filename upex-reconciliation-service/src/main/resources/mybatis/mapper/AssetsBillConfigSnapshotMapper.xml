<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.AssetsBillConfigSnapshotMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.AssetsBillConfigSnapshot">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="assets_check_type" property="assetsCheckType" jdbcType="TINYINT"/>
        <result column="assets_check_param" property="assetsCheckParam" jdbcType="VARCHAR"/>
        <result column="consume_offset" property="consumeOffset" jdbcType="BIGINT"/>
        <result column="check_ok_time" property="checkOkTime" jdbcType="TIMESTAMP"/>
        <result column="init_time" property="initTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="base_column_list">
        id, assets_check_type, assets_check_param, consume_offset, check_ok_time, init_time, create_time,
        update_time
    </sql>

    <sql id="base_column_list_no_id">
        assets_check_type, assets_check_param, consume_offset, check_ok_time, init_time, create_time,
        update_time
    </sql>

    <sql id="batch_insert_value">
        #{record.assetsCheckType},
        #{record.assetsCheckParam},
        #{record.consumeOffset},
        #{record.checkOkTime},
        #{record.initTime},
        #{record.createTime},
        #{record.updateTime}
    </sql>

    <insert id="batchInsert" parameterType="com.upex.reconciliation.service.dao.entity.AssetsBillConfigSnapshot">
        insert into assets_bill_config_snapshot(<include refid="base_column_list_no_id"></include>)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <select id="selectLastByTypeAndParam" resultMap="BaseResultMap">
        select
        <include refid="base_column_list"/>
        from assets_bill_config_snapshot
        where assets_check_type = #{assetsCheckType} and assets_check_param = #{assetsCheckParam}
        order by check_ok_time desc
        limit 1
    </select>

    <update id="deleteAll">
        delete from assets_bill_config_snapshot
        where assets_check_type = #{assetsCheckType}
        and assets_check_param = #{assetsCheckParam}
    </update>

    <delete id="deleteByCheckTime">
        delete from assets_bill_config_snapshot
        where
        assets_check_type = #{assetAccountType}
        and assets_check_param = #{assetAccountParam}
        and check_ok_time &gt; #{inAllRollbackTime}
    </delete>

    <delete id="deleteByLtCheckTime">
        delete from assets_bill_config_snapshot
        where
        assets_check_type = #{assetAccountType}
        and assets_check_param = #{assetAccountParam}
        and check_ok_time &lt; #{inAllRollbackTime}
        limit #{batchSize}
    </delete>
</mapper>