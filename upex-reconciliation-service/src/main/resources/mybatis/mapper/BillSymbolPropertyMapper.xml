<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillSymbolPropertyMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillSymbolProperty">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="symbol_id" property="symbolId" jdbcType="VARCHAR"/>
        <result column="change_prop1" property="changeProp1" jdbcType="DECIMAL" />
        <result column="prop1" property="prop1" jdbcType="DECIMAL" />
        <result column="change_prop2" property="changeProp2" jdbcType="DECIMAL" />
        <result column="prop2" property="prop2" jdbcType="DECIMAL" />
        <result column="change_prop3" property="changeProp3" jdbcType="DECIMAL" />
        <result column="prop3" property="prop3" jdbcType="DECIMAL" />
        <result column="change_prop4" property="changeProp4" jdbcType="DECIMAL" />
        <result column="prop4" property="prop4" jdbcType="DECIMAL" />
        <result column="change_prop5" property="changeProp5" jdbcType="DECIMAL" />
        <result column="prop5" property="prop5" jdbcType="DECIMAL" />
        <result column="change_prop6" property="changeProp6" jdbcType="DECIMAL" />
        <result column="prop6" property="prop6" jdbcType="DECIMAL" />
        <result column="change_prop7" property="changeProp7" jdbcType="DECIMAL" />
        <result column="prop7" property="prop7" jdbcType="DECIMAL" />
        <result column="change_prop8" property="changeProp8" jdbcType="DECIMAL" />
        <result column="prop8" property="prop8" jdbcType="DECIMAL" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="check_time" property="checkTime" jdbcType="TIMESTAMP"/>
        <result column="initial_time" property="initialTime" jdbcType="TIMESTAMP"/>
        <result column="params" property="params" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, symbol_id,change_prop1, prop1, change_prop2, prop2,
        change_prop3, prop3,change_prop4,prop4, change_prop5, prop5,
        change_prop6, prop6,change_prop7,prop7, change_prop8, prop8,
        create_time, update_time, initial_time ,check_time , params
    </sql>
    <sql id="Base_Column_List_No_Id">
        symbol_id,change_prop1, prop1, change_prop2, prop2,
        change_prop3, prop3,change_prop4,prop4, change_prop5, prop5,
        change_prop6, prop6,change_prop7,prop7, change_prop8, prop8,
        create_time, update_time, initial_time ,check_time , params
    </sql>
    <sql id="Base_Column_List_Value">
        #{record.symbolId},#{record.changeProp1},#{record.prop1},#{record.changeProp2},#{record.prop2},
        #{record.changeProp3},#{record.prop3},#{record.changeProp4},#{record.prop4},#{record.changeProp5},#{record.prop5},
        #{record.changeProp6},#{record.prop6},#{record.changeProp7},#{record.prop7},#{record.changeProp8},#{record.prop8},
        #{record.createTime},#{record.updateTime},#{record.initialTime},#{record.checkTime},#{record.params}
    </sql>
    <sql id="Base_Table_Name">
        bill_symbol_property_${accountType}_${accountParam}
    </sql>

    <select id="selectLastRecords" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Base_Table_Name"></include>
        where check_time = #{checkTime}
    </select>
    <select id="selectMinDate" resultType="java.util.Date">
        select check_time
        from
        <include refid="Base_Table_Name"></include>
        where check_time &lt; #{deleteEndTime}
        order by check_time
        limit 1
    </select>
    <select id="pageListIdByTime" parameterType="com.upex.reconciliation.service.model.dto.QueryDTO"
            resultType="java.lang.Long">
        select id
        from
        <include refid="Base_Table_Name"></include>
        where check_time >= #{startTime}
        and check_time &lt; #{endTime}
        and id > #{deleteSyncPos}
        order by id
        limit #{fixLength}
    </select>

    <delete id="batchDelete">
        delete from
        <include refid="Base_Table_Name"/>
        where id >= #{beginId}
        limit #{pageSize}
    </delete>

    <delete id="deleteByCheckTime">
        delete from
        <include refid="Base_Table_Name"/>
        where check_time = #{checkTime}
    </delete>

    <insert id="batchInsert">
        insert into
        <include refid="Base_Table_Name"></include>
        (
        <include refid="Base_Column_List_No_Id"></include>
        )
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="Base_Column_List_Value"/>
            </trim>
        </foreach>
    </insert>

    <delete id="deleteRecords" parameterType="object">
        delete from
        <include refid="Base_Table_Name"></include>
        where check_time &gt; #{resetCheckTime}
    </delete>
    <delete id="deleteByIds">
        delete
        from
        <include refid="Base_Table_Name"></include>
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectListByCheckTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Base_Table_Name"></include>
        where check_time = #{checkOkTime}
    </select>

    <select id="getIdByCheckTime" resultType="long">
        select max(id) from
        <include refid="Base_Table_Name"/>
        <where>
            <if test="operation == '='">
                and check_time = #{checkTime}
            </if>
            <if test="operation == '&lt;='">
                and check_time &lt;= #{checkTime}
            </if>
        </where>
    </select>

    <delete id="deleteByMaxId">
        delete from
        <include refid="Base_Table_Name"/>
        where id &lt; #{maxId}
        limit #{batchSize}
    </delete>

    <delete id="deleteByLtCheckTime">
        delete from
        <include refid="Base_Table_Name"/>
        where check_time &lt; #{checkTime}
        limit #{batchSize}
    </delete>

    <update id="updateById" parameterType="com.upex.reconciliation.service.dao.entity.BillSymbolProperty">
        update
        <include refid="Base_Table_Name"/>
        set
        symbol_id = #{record.symbolId,jdbcType=VARCHAR},
        check_time = #{record.checkTime,jdbcType=TIMESTAMP},
        change_prop1 = #{record.changeProp1,jdbcType=DECIMAL},
        change_prop2 = #{record.changeProp2,jdbcType=DECIMAL},
        change_prop3 = #{record.changeProp3,jdbcType=DECIMAL},
        change_prop4 = #{record.changeProp4,jdbcType=DECIMAL},
        change_prop5 = #{record.changeProp5,jdbcType=DECIMAL},
        change_prop6 = #{record.changeProp6,jdbcType=DECIMAL},
        change_prop7 = #{record.changeProp7,jdbcType=DECIMAL},
        change_prop8 = #{record.changeProp8,jdbcType=DECIMAL},
        prop1 = #{record.prop1,jdbcType=DECIMAL},
        prop2 = #{record.prop2,jdbcType=DECIMAL},
        prop3 = #{record.prop3,jdbcType=DECIMAL},
        prop4 = #{record.prop4,jdbcType=DECIMAL},
        prop5 = #{record.prop5,jdbcType=DECIMAL},
        prop6 = #{record.prop6,jdbcType=DECIMAL},
        prop7 = #{record.prop7,jdbcType=DECIMAL},
        prop8 = #{record.prop8,jdbcType=DECIMAL},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP}
        where id = #{record.id,jdbcType=BIGINT}
    </update>
</mapper>