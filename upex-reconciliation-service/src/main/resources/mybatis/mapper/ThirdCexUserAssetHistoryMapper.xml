<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.cex.ThirdCexUserAssetHistoryMapper">

    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserAssetHistory">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cex_user_id" property="cexUserId" jdbcType="VARCHAR"/>
        <result column="cex_type" property="cexType" jdbcType="INTEGER"/>
        <result column="third_asset_type" property="thirdAssetType" jdbcType="INTEGER"/>
        <result column="coin_name" property="coinName" jdbcType="VARCHAR"/>
        <result column="total_balance" property="totalBalance" jdbcType="DECIMAL"/>
        <result column="avaiable_balance" property="avaiableBalance" jdbcType="DECIMAL"/>
        <result column="borrowed_balance" property="borrowedBalance" jdbcType="DECIMAL"/>
        <result column="margin_balance" property="marginBalance" jdbcType="DECIMAL"/>
        <result column="change_time" property="changeTime" jdbcType="TIMESTAMP"/>
        <result column="check_sync_time" property="checkSyncTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        cex_user_id,
        cex_type,
        third_asset_type,
        coin_name,
        total_balance,
        avaiable_balance,
        borrowed_balance,
        margin_balance,
        change_time,
        check_sync_time,
        create_time,
        update_time,
        version
    </sql>

    <insert id="insert">
        INSERT INTO third_cex_user_asset_history (
            id,
            cex_user_id,
            cex_type,
            third_asset_type,
            coin_name,
            total_balance,
            avaiable_balance,
            borrowed_balance,
            margin_balance,
            change_time,
            check_sync_time,
            create_time,
            update_time,
            version
        )
        VALUES (
            #{id,jdbcType=BIGINT},
            #{cexUserId,jdbcType=BIGINT},
            #{cexType,jdbcType=INTEGER},
            #{thirdAssetType,jdbcType=INTEGER},
            #{coinName,jdbcType=VARCHAR},
            #{totalBalance,jdbcType=DECIMAL},
            #{avaiableBalance,jdbcType=DECIMAL},
            #{borrowedBalance,jdbcType=DECIMAL},
            #{marginBalance,jdbcType=DECIMAL},
            #{changeTime,jdbcType=TIMESTAMP},
            #{checkSyncTime,jdbcType=TIMESTAMP},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP},
            #{version,jdbcType=INTEGER}
        )
    </insert>

    <insert id="insertBatch">
        INSERT INTO third_cex_user_asset_history (
            id,
            cex_user_id,
            cex_type,
            third_asset_type,
            coin_name,
            total_balance,
            avaiable_balance,
            borrowed_balance,
            margin_balance,
            change_time,
            check_sync_time,
            create_time,
            update_time,
            version
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id,jdbcType=BIGINT},
                #{item.cexUserId,jdbcType=BIGINT},
                #{item.cexType,jdbcType=INTEGER},
                #{item.thirdAssetType,jdbcType=INTEGER},
                #{item.coinName,jdbcType=VARCHAR},
                #{item.totalBalance,jdbcType=DECIMAL},
                #{item.avaiableBalance,jdbcType=DECIMAL},
                #{item.borrowedBalance,jdbcType=DECIMAL},
                #{item.marginBalance,jdbcType=DECIMAL},
                #{item.changeTime,jdbcType=TIMESTAMP},
                #{item.checkSyncTime,jdbcType=TIMESTAMP},
                #{item.createTime,jdbcType=TIMESTAMP},
                #{item.updateTime,jdbcType=TIMESTAMP},
                #{item.version,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective">
        UPDATE third_cex_user_asset_history
        <set>
            <if test="record.cexUserId != null">
                cex_user_id = #{record.cexUserId,jdbcType=BIGINT},
            </if>
            <if test="record.cexType != null">
                cex_type = #{record.cexType,jdbcType=INTEGER},
            </if>
            <if test="record.thirdAssetType != null">
                third_asset_type = #{record.thirdAssetType,jdbcType=INTEGER},
            </if>
            <if test="record.coinName != null">
                coin_name = #{record.coinName,jdbcType=VARCHAR},
            </if>
            <if test="record.totalBalance != null">
                total_balance = #{record.totalBalance,jdbcType=DECIMAL},
            </if>
            <if test="record.avaiableBalance != null">
                avaiable_balance = #{record.avaiableBalance,jdbcType=DECIMAL},
            </if>
            <if test="record.borrowedBalance != null">
                borrowed_balance = #{record.borrowedBalance,jdbcType=DECIMAL},
            </if>
            <if test="record.marginBalance != null">
                margin_balance = #{record.marginBalance,jdbcType=DECIMAL},
            </if>
            <if test="record.changeTime != null">
                change_time = #{record.changeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.version != null">
                version = #{record.version,jdbcType=INTEGER},
            </if>
        </set>
        WHERE id = #{record.id,jdbcType=BIGINT}
    </update>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_user_asset_history
        WHERE id = #{id}
    </select>


    <delete id="deleteByCexUserId">
        DELETE FROM third_cex_user_asset_history
        WHERE cex_user_id = #{cexUserId}
    </delete>

    <select id="selectUserAssetByThirdAssetType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_user_asset_history
        WHERE cex_user_id = #{cexUserId}
          AND cex_type = #{cexType}
          AND third_asset_type IN
        <foreach item="item" collection="thirdAssetTypes" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND check_sync_time  = #{startTime}
    </select>

    <select id="selectMaxCheckSyncTime" resultType="java.util.Date">
        SELECT check_sync_time
        FROM third_cex_user_asset_history
        WHERE cex_user_id = #{cexUserId}
          AND cex_type = #{cexType}
          AND third_asset_type =#{thirdAssetType}
        ORDER BY check_sync_time DESC
        LIMIT 1
    </select>

</mapper>
