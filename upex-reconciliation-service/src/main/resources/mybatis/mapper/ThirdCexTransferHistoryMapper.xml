<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.upex.reconciliation.service.dao.mapper.cex.ThirdCexTransferHistoryMapper">

    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.cex.entity.ThirdCexTransferHistory">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cex_user_id" property="cexUserId" jdbcType="VARCHAR"/>
        <result column="cex_email" property="cexEmail" jdbcType="VARCHAR" typeHandler="com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler"/>
        <result column="cex_type" property="cexType" jdbcType="INTEGER"/>
        <result column="transfer_id" property="transferId" jdbcType="VARCHAR"/>
        <result column="from_user" property="fromUser" jdbcType="VARCHAR"/>
        <result column="to_user" property="toUser" jdbcType="VARCHAR"/>
        <result column="transfer_type" property="transferType" jdbcType="INTEGER"/>
        <result column="coin_name" property="coinName" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="from_account_type" property="fromAccountType" jdbcType="VARCHAR"/>
        <result column="to_account_type" property="toAccountType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="info" property="info" jdbcType="VARCHAR"/>
        <result column="check_sync_time" property="checkSyncTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        cex_user_id,
        cex_email,
        cex_type,
        transfer_id,
        from_user,
        to_user,
        transfer_type,
        coin_name,
        amount,
        from_account_type,
        to_account_type,
        status,
        info,
        change_time,
        check_sync_time,
        create_time,
        update_time,
        version
    </sql>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.upex.reconciliation.service.dao.cex.entity.ThirdCexTransferHistory">
        INSERT INTO third_cex_transfer_history (
            cex_user_id,
            cex_email,
            cex_type,
            transfer_id,
            from_user,
            to_user,
            transfer_type,
            coin_name,
            amount,
            from_account_type,
            to_account_type,
            status,
            info,
            change_time,
            check_sync_time,
            create_time,
            update_time,
            version
        ) VALUES (
            #{cexUserId},
            #{cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},
            #{cexType},
            #{transferId},
            #{fromUser},
            #{toUser},
            #{transferType},
            #{coinName},
            #{amount},
            #{fromAccountType},
            #{toAccountType},
            #{status},
            #{info},
            #{changeTime},
            #{checkSyncTime},
            #{createTime},
            #{updateTime},
            #{version}
        )
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_transfer_history
        WHERE transfer_id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 根据 CEX 用户和交易所类型查询 -->
    <select id="selectByCexUserAndType" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_cex_transfer_history
        WHERE cex_user_id = #{cexUserId}
          AND cex_type = #{cexType}
    </select>

    <!-- 更新 -->
    <update id="update" parameterType="com.upex.reconciliation.service.dao.cex.entity.ThirdCexTransferHistory">
        UPDATE third_cex_transfer_history
        SET
            transfer_id = #{transferId},
            from_user = #{fromUser},
            to_user = #{toUser},
            transfer_type = #{transferType},
            coin_name = #{coinName},
            amount = #{amount},
            from_account_type = #{fromAccountType},
            to_account_type = #{toAccountType},
            status = #{status},
            info = #{info},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 根据 transfer_id 批量删除 -->
    <delete id="deleteByTransferIds" parameterType="java.util.List">
        DELETE FROM third_cex_transfer_history
        WHERE transfer_id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <insert id="batchInsert">
        INSERT INTO third_cex_transfer_history (
            cex_user_id,
            cex_email,
            cex_type,
            transfer_id,
            from_user,
            to_user,
            transfer_type,
            coin_name,
            amount,
            from_account_type,
            to_account_type,
            status,
            info,
            change_time,
            check_sync_time,
            create_time,
            update_time,
            version
        ) VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (
                #{item.cexUserId},#{item.cexEmail,typeHandler=com.upex.reconciliation.service.dao.typehandler.EncryptedStringTypeHandler},#{item.cexType},#{item.transferId},#{item.fromUser},#{item.toUser},#{item.transferType},#{item.coinName},#{item.amount},#{item.fromAccountType},#{item.toAccountType},#{item.status},#{item.info},#{item.changeTime},#{item.checkSyncTime},#{item.createTime},#{item.updateTime},#{item.version}
            )
        </foreach>
    </insert>

</mapper>
