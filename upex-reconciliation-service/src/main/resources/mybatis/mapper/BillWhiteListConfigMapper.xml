<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillWhiteListConfigMapper" >
  <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillWhiteListConfig" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="white_list" property="whiteList" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="base_column_list" >
    id, type, white_list, status, remark, create_time, update_time
  </sql>

  <sql id="base_column_list_not_id" >
    type, white_list, status, remark, create_time, update_time
  </sql>

  <sql id="batch_insert_value">
    #{record.type}
    ,#{record.whiteList}
    ,#{record.status}
    ,#{record.remark}
    ,#{record.createTime}
    ,#{record.updateTime}
  </sql>

  <update id="updateStatusById">
    update bill_white_list_config set status = #{status} where id = #{id}
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.upex.reconciliation.service.dao.entity.BillWhiteListConfig">
    update bill_white_list_config
    <set>
      <if test="type != null">
        type = #{type},
      </if>
      <if test="whiteList != null">
        white_list = #{whiteList},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="remark != null">
        remark = #{remark},
      </if>
    </set>
    where id = #{id}
  </update>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="base_column_list" />
    from bill_white_list_config
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="batchInsert" parameterType="com.upex.reconciliation.service.dao.entity.BillWhiteListConfig">
    insert into bill_white_list_config(<include refid="base_column_list_not_id"></include>)
    values
    <foreach collection="records" item="record" index="index" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <include refid="batch_insert_value"/>
      </trim>
    </foreach>
  </insert>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from bill_white_list_config
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByIds">
    delete from bill_white_list_config
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <select id="getWhileListConfigList" resultMap="BaseResultMap">
    select
    <include refid="base_column_list"/>
    from bill_white_list_config where type = #{type}
    <if test="status != null">
      and status = #{status}
    </if>
  </select>
</mapper>