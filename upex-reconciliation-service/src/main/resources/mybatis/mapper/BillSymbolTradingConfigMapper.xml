<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillSymbolTradingConfigMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillSymbolTradingConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="account_type" property="accountType" jdbcType="TINYINT"/>
        <result column="account_param" property="accountParam" jdbcType="VARCHAR"/>
        <result column="symbol_id" property="symbolId" jdbcType="VARCHAR"/>
        <result column="init_value" property="initValue" jdbcType="DECIMAL"/>
        <result column="check_ok_time" property="checkOkTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,account_type,account_param,symbol_id,init_value,
        check_ok_time,create_time,update_time
    </sql>
    <sql id="Base_Column_List_No_Id">
        account_type,account_param,symbol_id,init_value,
        check_ok_time,create_time,update_time
    </sql>
    <sql id="Base_Column_List_Value">
        #{record.accountType},#{record.accountParam},#{record.symbolId},#{record.initValue},#{record.checkOkTime},
        #{record.createTime},#{record.updateTime}
    </sql>
    <sql id="Base_Table_Name">
        bill_symbol_trading_config
    </sql>

    <delete id="batchDelete">
        delete from
        <include refid="Base_Table_Name"/>
        where id >= #{beginId}
        limit #{pageSize}
    </delete>

    <delete id="deleteByCheckTime">
        delete from
        <include refid="Base_Table_Name"/>
        where account_type = #{accountType} and check_ok_time >= #{checkTime}
    </delete>

    <insert id="batchInsert" parameterType="com.upex.reconciliation.service.dao.entity.BillSymbolTradingConfig">
        insert into
        <include refid="Base_Table_Name"></include>
        (
        <include refid="Base_Column_List_No_Id"></include>
        ) values
        <foreach collection="list" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="Base_Column_List_Value"></include>
            </trim>
        </foreach>
    </insert>

    <select id="selectBillSymbolTradingConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Base_Table_Name"></include>
        where account_type = #{accountType}
        and account_param = #{accountParam}
    </select>

    <delete id="deleteCheckTimeAfterRecords">
        delete from
        <include refid="Base_Table_Name"></include>
        where account_type = #{accountType}
        and account_param = #{accountParam}
        and check_ok_time &gt; #{resetCheckTime}
    </delete>

    <select id="listAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Base_Table_Name"></include>
    </select>
</mapper>