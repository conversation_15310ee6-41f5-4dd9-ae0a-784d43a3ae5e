<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillUserMapper">
    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillUser">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="center_status" property="centerStatus" jdbcType="TINYINT"/>
        <result column="register_time" property="registerTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="ext_status" property="extStatus" jdbcType="VARCHAR"/>
        <result column="params" property="params" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <sql id="Table_Name">
        bill_user
    </sql>

    <sql id="Base_Column_List">
        `id`,`ext_status`,`params`,`register_time`,`create_time`,`update_time`,
       `center_status`,`parent_id`
    </sql>

    <sql id="batch_insert_value">
        #{record.id},
        #{record.extStatus},
        #{record.params},
        #{record.registerTime},
        #{record.createTime},
        #{record.updateTime},
        #{record.centerStatus},
        #{record.parentId}
    </sql>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        insert into<include refid="Table_Name"/> (<include refid="Base_Column_List"></include>)
        values
        <foreach collection="records" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <include refid="batch_insert_value"/>
            </trim>
        </foreach>
    </insert>

    <update id="batchUpdate">
        update
        <include refid="Table_Name"/>
        <trim prefix="set" suffixOverrides=",">
            ext_status=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.extStatus}
            </foreach>
            params=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.params}
            </foreach>
            center_status=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.centerStatus}
            </foreach>
            register_time=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.registerTime}
            </foreach>
            parent_id=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.parentId}
            </foreach>
            update_time=
            <foreach collection="records" item="item" open="case " close=" end,">
                when id = #{item.id} then #{item.updateTime}
            </foreach>
        </trim>
        WHERE
        <foreach collection="records" item="item" open="( " separator=") or (" close=" )">
            id = #{item.id}
        </foreach>
    </update>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="Table_Name"/>
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>