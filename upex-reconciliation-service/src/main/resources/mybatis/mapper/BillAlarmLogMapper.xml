<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.upex.reconciliation.service.dao.mapper.BillAlarmLogMapper">

    <resultMap id="BaseResultMap" type="com.upex.reconciliation.service.dao.entity.BillAlarmLog">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="accountType" column="account_type" jdbcType="VARCHAR"/>
        <result property="alarmTime" column="alarm_time" jdbcType="TIMESTAMP"/>
        <result property="atUserList" column="at_user_list" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="frequency" column="frequency" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="reason" column="reason" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, account_type, alarm_time, at_user_list, code, content, frequency, source, status, reason, create_time, update_time
    </sql>
    <sql id="Base_Column_List_Not_Id">
        account_type, alarm_time, at_user_list, code, content, frequency, source, status, reason, create_time, update_time
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from bill_alarm_log
        where id = #{id}
    </select>

    <!--新增-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into bill_alarm_log(<include refid="Base_Column_List_Not_Id"></include>)
        values (#{accountType}, #{alarmTime}, #{atUserList}, #{code}, #{content}, #{frequency}, #{source},
        #{status}, #{reason}, #{createTime}, #{updateTime})
    </insert>

    <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
        insert into bill_alarm_log(Base_Column_List_Not_Id)
        values
        <foreach collection="records" item="record" separator=",">
        (#{record.accountType}, #{record.alarmTime}, #{record.atUserList}, #{record.code}, #{record.content}, #{record.frequency},
            #{record.source}, #{record.status}, #{record.reason}, #{record.createTime}, #{record.updateTime})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bill_alarm_log
        <set>
            <if test="accountType != null">
                account_type = #{accountType},
            </if>
            <if test="alarmTime != null">
                alarm_time = #{alarmTime},
            </if>
            <if test="atUserList != null and atUserList != ''">
                at_user_list = #{atUserList},
            </if>
            <if test="code != null and code != ''">
                code = #{code},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="frequency != null and frequency != ''">
                frequency = #{frequency},
            </if>
            <if test="source != null and source != ''">
                source = #{source},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="reason != null and reason != ''">
                reason = #{reason},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from bill_alarm_log where id = #{id}
    </delete>

    <delete id="deleteByTime">
        delete from bill_alarm_log where create_time &lt;=#{time}
    </delete>
</mapper>

