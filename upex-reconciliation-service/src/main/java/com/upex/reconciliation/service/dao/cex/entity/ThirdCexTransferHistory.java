package com.upex.reconciliation.service.dao.cex.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import lombok.Data;

/**
 * 母子账户划抓记录实体类
 * 万向划转
 */
@Data
public class ThirdCexTransferHistory {
    private Long id;
    private String cexUserId;       // 用户ID
    private String cexEmail;
    private Integer cexType;        // 交易所类型
    private String transferId;      // 交易ID
    private String fromUser;        // 发送方邮箱/用户
    private String toUser;          // 接收方邮箱/用户ID
    private Integer transferType;   // 类型：母子划转 母用户内部划转
    private String coinName;            // 币种
    private BigDecimal amount;      // 数量
    private String fromAccountType; // 发送方账户类型
    private String toAccountType;   // 接收方账户类型
    private Integer status;         // 状态
    private String info;            // 描述
    private Date checkSyncTime;
    private Date changeTime;
    private Date createTime;
    private Date updateTime;
    private Long version;


}
