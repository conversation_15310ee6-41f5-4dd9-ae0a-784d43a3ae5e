package com.upex.reconciliation.service.utils;

import com.upex.reconciliation.service.common.constants.BillConstants;

import java.util.Date;
import java.util.function.BiConsumer;

public class TimeSliceCalcUtils {

    /**
     * 获取周期长度ms
     *
     * @param timeSliceSize
     * @return
     */
    public static long timeSlicePeriodMs(Long timeSliceSize) {
        return timeSliceSize * BillConstants.ONE_THOUSAND;
    }

    /**
     * 时间片划分最新逻辑，左闭右开区间
     * [12:00  12:01)  -> 12:01
     * 例子：
     * 12:00 <= x < 12:01     归属于 12:01时间片
     *
     * @param timestamp
     * @param timeSliceSize
     * @return
     */
    public static long getTimeSlice(Long timestamp, Long timeSliceSize) {
        if (timestamp == null) {
            return 0;
        }
        long timeSlicePeriodMs = timeSlicePeriodMs(timeSliceSize);
        long base = timestamp / timeSlicePeriodMs * timeSlicePeriodMs;
        return (timestamp - base >= 0) ? (base + timeSlicePeriodMs) : base;
    }

    /**
     * @param currentTimeSlice
     * @param timeSliceSize    单位秒
     * @return
     */
    public static long getNextTimeSlice(long currentTimeSlice, Long timeSliceSize) {
        return currentTimeSlice + timeSlicePeriodMs(timeSliceSize);
    }

    /**
     * @param currentTimeSlice
     * @param timeSliceSize    单位秒
     * @return
     */
    public static long getPreTimeSlice(long currentTimeSlice, Long timeSliceSize) {
        return currentTimeSlice - timeSlicePeriodMs(timeSliceSize);
    }

    /**
     * @param currentTimeSlice
     * @param timeSliceSize    单位秒
     * @return
     */
    public static long getNextTimeSliceWithNum(long currentTimeSlice, Long timeSliceSize, Integer num) {
        return currentTimeSlice + num * timeSlicePeriodMs(timeSliceSize);
    }

    /**
     * 获取存储时间片
     *
     * @param currentTimeSlice
     * @param timeSliceSize
     * @param mergeTimeSliceSize
     * @return
     */
    public static long getSaveTimeSlice(long currentTimeSlice, Long timeSliceSize, Integer mergeTimeSliceSize) {
        boolean result = false;
        long saveTimeSlice = currentTimeSlice;
        while (!result) {
            result = isSaveTimeSlice(saveTimeSlice, timeSliceSize, mergeTimeSliceSize);
            if (!result) {
                saveTimeSlice = getNextTimeSlice(saveTimeSlice, timeSliceSize);
            }
        }
        return saveTimeSlice;
    }

    /**
     * 是否是存储时间片
     *
     * @param currentTimeSlice
     * @param timeSliceSize
     * @param mergeTimeSliceSize
     * @return
     */
    public static boolean isSaveTimeSlice(long currentTimeSlice, Long timeSliceSize, Integer mergeTimeSliceSize) {
        Long period = currentTimeSlice / timeSlicePeriodMs(timeSliceSize);
        // System.out.println(period);
        return period % mergeTimeSliceSize == 0;
    }

    /**
     * 获取时间片间隔
     *
     * @param startTimeSlice
     * @param endTimeSlice
     * @param timeSliceSize
     * @return
     */
    public static int timeSliceInterval(long startTimeSlice, long endTimeSlice, Long timeSliceSize) {
        long period = timeSlicePeriodMs(timeSliceSize);
        Long startPeriod = startTimeSlice / period;
        Long endPeriod = endTimeSlice / period;
        return (int) (endPeriod - startPeriod);
    }

    /**
     * 获取下一个存储周期
     *
     * @param currentTimeSlice
     * @param timeSliceSize
     * @param mergeTimeSliceSize
     * @return
     */
    public static long getNextSaveTimeSlice(long currentTimeSlice, Long timeSliceSize, Integer mergeTimeSliceSize) {
        long currentSaveTimeSlice = getSaveTimeSlice(currentTimeSlice, timeSliceSize, mergeTimeSliceSize);
        long period = timeSlicePeriodMs(timeSliceSize);
        long nextSaveTimeSlice = currentSaveTimeSlice + mergeTimeSliceSize * period;
        return nextSaveTimeSlice;
    }

    /**
     * 获取上一周期
     *
     * @param currentTimeSlice
     * @param timeSliceSize
     * @param mergeTimeSliceSize
     * @return
     */
    public static long getPreSaveTimeSlice(long currentTimeSlice, Long timeSliceSize, Integer mergeTimeSliceSize) {
        long currentSaveTimeSlice = getSaveTimeSlice(currentTimeSlice, timeSliceSize, mergeTimeSliceSize);
        long period = timeSlicePeriodMs(timeSliceSize);
        long preSaveTimeSlice = currentSaveTimeSlice - mergeTimeSliceSize * period;
        return preSaveTimeSlice;
    }

    /**
     * 切割日期
     *
     * @param startDate
     * @param endDate
     * @param interval
     * @param sliceConsumer
     */
    public static void slice(Date startDate, Date endDate, int interval, BiConsumer<Date, Date> sliceConsumer) {
        // 拆分时间范围并逐段查询
        Date chunkStartDate = startDate;
        while (chunkStartDate.before(endDate)) {
            Date chunkEndDate;
            // 计算下一个时间点，如果超过终点则取终点
            chunkEndDate = DateUtil.addMinute(chunkStartDate, interval);
            if (chunkEndDate.after(endDate)) {
                chunkEndDate = endDate;
            }
            sliceConsumer.accept(chunkStartDate, chunkEndDate);
            chunkStartDate = chunkEndDate;
        }
    }
}
