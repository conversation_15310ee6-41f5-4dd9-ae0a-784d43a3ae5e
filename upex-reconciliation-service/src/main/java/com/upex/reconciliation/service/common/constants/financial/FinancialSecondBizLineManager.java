package com.upex.reconciliation.service.common.constants.financial;

public interface FinancialSecondBizLineManager {
    /**
     * LaunchPool
     */
    String LAUNCH_POOL = "LaunchPool";

    /**
     * BgbEarn
     */
    String BGB_EARN = "BgbEarn";

    /**
     * 单币理财 - 活期
     */
    String SAVINGS = "Savings";

    /**
     * 单币理财 - 定期
     */
    String SAVINGS_FIXED = "SavingsFixed";

    /**
     * 鲨鱼鳍
     */
    String SHARK_FIN = "SharkFin";

    /**
     * 双币
     */
    String DUAL_INVEST = "DualInvest";

    /**
     * pos质押
     */
    String POS_STAKING = "PosStaking";

    /**
     * 趋势智盈
     */
    String TREND = "Trend";

    /**
     * 区间猎手
     */
    String RANGE_SNIPER = "RangeSniper";

    /**
     * bgb质押
     */
    String BGB_STAKING = "BgbStaking";

    /**
     * 雪球理财
     */
    String AVALANCHE = "Avalanche";

    /**
     * 基金超市
     */
    String FUND_MARKET = "FundMarket";

    /**
     * 现货定投
     */
    String DIRECT_INVEST = "DirectInvest";

    /**
     * PoolX
     */
    String POOL_X = "PoolX";

    /**
     * 所有
     */
    String ALL = "All";

    /**
     * 多个
     */
    String NOT_ONLY = "NotOnly";
}
