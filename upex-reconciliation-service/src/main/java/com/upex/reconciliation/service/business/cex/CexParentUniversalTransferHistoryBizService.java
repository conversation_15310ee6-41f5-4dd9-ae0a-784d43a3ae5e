package com.upex.reconciliation.service.business.cex;

import com.alibaba.google.common.collect.Lists;
import com.binance.connector.client.common.ApiException;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.dao.cex.entity.CexAssetConfig;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexDepositeHistory;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexTransferHistory;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.model.config.ApolloThirdCexAssetConfig;
import com.upex.reconciliation.service.service.CexTransferHistoryService;
import com.upex.reconciliation.service.service.client.cex.CexConstants;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.UniversalTransferRecordReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import com.upex.reconciliation.service.service.client.cex.enmus.CexAssetHistoryTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.CexTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.UniversalTransferRecordTypeEnum;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CexParentUniversalTransferHistoryBizService implements ICexAssetSyncHistory {

    @Resource
    private CexApiService cexApiService;

    @Resource
    CexTransferHistoryService cexTransferHistoryService;

    @Resource
    AlarmNotifyService alarmNotifyService;

    @Resource
    BillDbHelper billDbHelper;


    public List<ThirdCexTransferHistory> queryParentUniversalTransferRecordAndSync(UniversalTransferRecordReq universalTransferRecordReq) {
        CommonRes<List<ThirdCexTransferHistory>> res = cexApiService.queryUniversialTransferList(universalTransferRecordReq);
        List<ThirdCexTransferHistory> cexTransferHistories = new ArrayList<>();
        if (res.getSuccess() && CollectionUtils.isNotEmpty(res.getData())) {
            log.info("QueryParentUniversalTransferRecord,Size:{}", res.getData().size());
            for (ThirdCexTransferHistory cexTransferHistory : res.getData()) {
                cexTransferHistory.setCheckSyncTime(universalTransferRecordReq.getCheckSyncTime());
                cexTransferHistories.add(cexTransferHistory);
            }
        }
        return cexTransferHistories;
    }

    @Override
    public void syncAssetHistory(CexAssetConfig cexAssetConfig, ThirdCexUserConfig userConfig, Date startTime, Date endTime, Date checkSyncTime) {
        if (System.currentTimeMillis() - startTime.getTime() > CexConstants.HAFL_YEAR_MIL_SEC) {
            log.warn("syncParentUniversalTransferRecordTimeOverLatestHalfYear,userId:{},startTime:{},endTime:{}", userConfig.getCexUserId(), startTime, endTime);
            return;
        }
        for (UniversalTransferRecordTypeEnum type : UniversalTransferRecordTypeEnum.values()) {
            try {
                UniversalTransferRecordReq universalTransferRecordReq = new UniversalTransferRecordReq(userConfig.getCexType(), userConfig.getCexUserId(), userConfig.getApiKey(), userConfig.getApiKeyPrivate(), type.getName(), startTime, endTime, checkSyncTime);
                List<ThirdCexTransferHistory> cexTransferHistories = queryParentUniversalTransferRecordAndSync(universalTransferRecordReq);
                ApolloThirdCexAssetConfig apolloThirdCexAssetConfig = ReconciliationApolloConfigUtils.getThirdCexAssetConfig();
                billDbHelper.doDbOpInReconMasterTransaction(() -> {
                    if (CollectionUtils.isNotEmpty(cexTransferHistories)) {
                        List<List<ThirdCexTransferHistory>> partitionList = Lists.partition(cexTransferHistories, apolloThirdCexAssetConfig.getSqlInsertSize());
                        for (List<ThirdCexTransferHistory> partition : partitionList) {
                            cexTransferHistoryService.batchInsert(partition);
                        }
                        log.info("InsertParentUniversalTransferRecord,Size:{}", partitionList.size());
                    }
                    syncAssetHistory(cexAssetConfig, userConfig, startTime, endTime, checkSyncTime);
                    return null;
                });
            } catch (ApiException e) {
                log.error("queryParentUniversalTransferRecordAndSyncError,userId:{},type:{},startTime:{},endTime:{},e:{}", userConfig.getCexUserId(), type, startTime, endTime, e);
                alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_ASSET_SYNC_EXCEPTION, userConfig.getCexUserId(), CexTypeEnum.fromType(userConfig.getCexType()).getName(), CexAssetHistoryTypeEnum.PARENT_UNIVERSIAL_TRANSFER.getName(), e.getMessage());
            }
        }
    }

    @Override
    public CexAssetHistoryTypeEnum getAssetHistoryType() {
        return CexAssetHistoryTypeEnum.PARENT_UNIVERSIAL_TRANSFER;
    }
}
