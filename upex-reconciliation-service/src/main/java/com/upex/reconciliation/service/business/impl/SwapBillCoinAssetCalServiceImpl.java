package com.upex.reconciliation.service.business.impl;


import com.upex.common.bean.api.APIResponse;
import com.upex.contract.entrance.dto.request.QueryMarkPriceByTimeParam;
import com.upex.contract.entrance.facade.ContractInfoFacade;
import com.upex.contract.process.dto.dto.ContractApiMarkpriceDTO;
import com.upex.reconciliation.facade.dto.results.ReconAccountAssetsInfoResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.params.SysAssetsParams;
import com.upex.reconciliation.service.business.StatisticsAssetsService;
import com.upex.reconciliation.service.business.BusinessRegistry;
import com.upex.reconciliation.service.business.SwapBillCoinAssetCalService;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserAssets;
import com.upex.utils.log.AlarmUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2021/6/30 下午3:47
 * @Description
 */
@Slf4j
@Service
public class SwapBillCoinAssetCalServiceImpl implements SwapBillCoinAssetCalService {

//    @Resource(name = "billCoinUserAssetsMapperWrapper")
//    private BillCoinUserAssetsMapper billCoinUserAssetsMapper;
    @Resource
    private BusinessRegistry businessRegistry;
    @Resource
    private ContractInfoFacade contractInfoFacade;
    @Resource
    private StatisticsAssetsService statisticsAssetsService;


    @Override
    public List<BillCoinUserProperty> getAndCalSingleUserAssets(Long userId, SysAssetsParams sysAssetsParams, Date paramTime, Boolean isMaster) {
        Integer accountType = sysAssetsParams.getAccountType();
        String accountParam = sysAssetsParams.getAccountParam();
        Integer coinId = sysAssetsParams.getCoinId();
        List<BillCoinUserAssets> sources = null;
//        List<BillCoinUserAssets> sources = queryBillAssetsWithSwapMain(userId,coinId,accountType,accountParam,paramTime, isMaster);
        if (sources == null) {
            return null;
        }
        List<BillCoinUserProperty> list = calculateByMarketPrice(sources,userId,accountType,accountParam,paramTime);
        return list;
    }
//    public List<BillCoinUserAssets> queryBillAssetsWithSwapMain(Long userId, Integer coinId, Integer accountType, String accountParam, Date paramTime, Boolean isMaster){
//        List<BillCoinUserAssets> sources = new ArrayList<>();
//        BillCoinUserAssets billCoinUserAssets = billCoinUserAssetsMapper.selectRecentNearSnapTime(accountType, accountParam, userId, paramTime, isMaster);
//        if (billCoinUserAssets != null) {
//            sources = billCoinUserAssetsMapper.selectSingleByParamAndTimeSwapMain(userId, coinId, accountType, accountParam, billCoinUserAssets.getCheckOkTime(), isMaster);
//        }
//        return  sources;
//    }

    @Override
    public List<BillCoinUserProperty> queryBillAssetsWithSwapMain(Long userId, Integer accountType, String accountParam, Date snapshotTime) {
        //List<BillCoinUserAssets> assetsList = statisticsAssetsService.queryRecordsExtensionStrategy(userId, accountType, accountParam, snapshotTime);

        List<BillCoinUserAssets> assetsList = statisticsAssetsService.queryRecordsExtensionStrategyEachCoin(userId, accountType, accountParam, snapshotTime, true);
        //上述记录可能包含不同对帐时间的数据（由于存在有初始记录但没有流水的用户），此时通过统一价格存在一定误差
        List<BillCoinUserProperty> list = calculateByMarketPrice(assetsList, userId, accountType, accountParam, snapshotTime);
        return list;
    }

    public List<BillCoinUserProperty> calculateByMarketPrice(List<BillCoinUserAssets> sources, Long userId, Integer accountType, String accountParam, Date paramTime) {
        List<BillCoinUserProperty> returnList = new ArrayList<>();
        if (CollectionUtils.isEmpty(sources)) {
            return  returnList;
        }
        //获取统一固定markPrice
        QueryMarkPriceByTimeParam queryParam = new QueryMarkPriceByTimeParam();
        queryParam.setEndTime(paramTime.getTime());
        queryParam.setProductCode(accountParam.toLowerCase());
        APIResponse<ContractApiMarkpriceDTO> markParam = contractInfoFacade.getMarkPriceByEndTime(queryParam);

        BigDecimal markPrice = null;
        if (markParam.isSuccess()) {
            String markPriceString = Optional.of(markParam).map(APIResponse::getData).map(ContractApiMarkpriceDTO::getMarkPrice).orElseGet(null);
            //price需要设定为null，不
            if (markPriceString != null) {
                markPrice = new BigDecimal(markParam.getData().getMarkPrice());
            }
        }
        if (markPrice == null) {
            AlarmUtils.error("swap main shows exception = {} and param = {}",markParam.getMessage(),queryParam);
        }
        for (BillCoinUserAssets source : sources) {
            ReconAccountAssetsInfoResult info = new ReconAccountAssetsInfoResult();
            info.setCoinId(source.getCoinId());
            info.setUserId(source.getUserId());
            info.setParams(source.getParams());
            info.setProp1(source.getProp1());
            info.setProp2(source.getProp2());
            info.setProp3(source.getProp3());
            info.setProp4(source.getProp4());
            info.setProp5(source.getProp5());
            ReconAccountAssetsInfoResult reconAccountAssetsInfoResult = businessRegistry
                    .getBusinessCalExtension(AccountTypeEnum.toEnum(accountType.byteValue()))
                    .calAccountEquity(info, accountParam.toLowerCase(),markPrice);
            BillCoinUserProperty target = new BillCoinUserProperty();
            target.setUserId(source.getUserId());
            target.setCoinId(source.getCoinId());
            target.setProp1(reconAccountAssetsInfoResult.getProp1());
            target.setProp2(reconAccountAssetsInfoResult.getProp2());
            target.setProp3(reconAccountAssetsInfoResult.getProp3());
            target.setProp4(reconAccountAssetsInfoResult.getProp4());
            target.setProp5(reconAccountAssetsInfoResult.getProp5());
            returnList.add(target);
        }
        return  returnList;
    }

}
