package com.upex.reconciliation.service.consumer.kafka;


import com.alibaba.otter.canal.client.kafka.MessageDeserializer;
import com.alibaba.otter.canal.protocol.Message;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Configuration
@Slf4j
public class ReconKafkaNormalService {

    private static String TOPIC = "recon_consumer_time_type_50_local_1122";

    @Value("${upex.recon.kafka.namesrvAddr}")
    private String kafkaServerAddr;

    /**
     * 重置kafka消费位点
     */
    public void resetPartitionSites() {
        Map<String, Object> config = new HashMap<String, Object>();
        config.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServerAddr);
        config.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, MessageDeserializer.class);
        config.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MessageDeserializer.class);
        config.put(ConsumerConfig.GROUP_ID_CONFIG, "upex-reconciliation-leichuang-local02");

        config.put("key.serializer.encoding", "UTF8");
        config.put("value.serializer.encoding", "UTF8");

        // 如果找不到偏移量，设置earliest,则从最新消费开始,也就是消费者一开始最新消费的时候
        // 一定要注意顺序，读取时候的顺序会影响
        // config.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        // 消费者
        KafkaConsumer<String, Message> consumer = new KafkaConsumer<String, Message>(config);
        consumer.subscribe(Arrays.asList(TOPIC));
        while (true) {
            ConsumerRecords<String, Message> consumerRecords = consumer.poll(5000);
            log.info("ConsumerRecords size: " + consumerRecords.count());
            consumerRecords.forEach(new java.util.function.Consumer<ConsumerRecord<String, Message>>() {

                @Override
                public void accept(ConsumerRecord<String, Message> consumerRecord) {
                    log.info("ReconKafkaNormalService started to consume messages " +
                            consumerRecord.topic() + "\t"
                            + consumerRecord.offset() + "\t"
                            + consumerRecord.key() + "\t"
                            + consumerRecord.value() + "\t"
                    );
                }
            });
        }
    }


}

