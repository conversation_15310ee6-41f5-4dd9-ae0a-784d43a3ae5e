package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SubUserMarginAccountInfoRes implements IBinanceApiBaseRes {

    private String marginLevel;
    private String totalAssetOfBtc;
    private String totalLiabilityOfBtc;
    private String totalNetAssetOfBtc;
    private List<MarginAccountInfoInnerRes> marginUserAssetVoList;
    private MarginTradeCoeff marginTradeCoeffVo;
    @Data
    class MarginTradeCoeff {

        /**
         * "forceLiquidationBar": "1.********",  // 强平风险率
         * "marginCallBar": "1.********",        // 补仓风险率
         * "normalBar": "2.********"	          // 初始风险率
         * "canTrade": true
         */
        private BigDecimal normalBar;
        private BigDecimal marginCallBar;
        private BigDecimal forceLiquidationBar;
        private Boolean canTrade;
    }
}
