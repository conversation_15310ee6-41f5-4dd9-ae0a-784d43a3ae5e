package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SubUserContractAccountInfoInnerRes {
    /**
     * {
     * 			"asset": "USDT",			            // 资产
     * 			"walletBalance": "23.********",         // 余额
     * 			"unrealizedProfit": "0.********",       // 未实现盈亏
     * 			"marginBalance": "23.********",         // 保证金余额
     * 			"maintenanceMargin": "0.********",	        // 维持保证金
     * 			"initialMargin": "0.********",          // 当前所需起始保证金
     * 			"positionInitialMargin": "0.********",  // 持仓所需起始保证金(基于最新标记价格)
     * 			"openOrderInitialMargin": "0.********", // 当前挂单所需起始保证金(基于最新标记价格)
     * 			"crossWalletBalance": "23.********",    // 全仓账户余额
     * 			"crossUnPnl": "0.********"              // 全仓持仓未实现盈亏
     * 			"availableBalance": "23.********",      // 可用余额
     * 			"maxWithdrawAmount": "23.********",     // 最大可转出余额
     * 			"updateTime": *************             // 更新时间
     * 		        }
     */

    private String asset;

    private BigDecimal walletBalance;

    private BigDecimal unrealizedProfit;

    private BigDecimal availableBalance;

    private BigDecimal marginBalance;

    private BigDecimal maintenanceMargin;

    private BigDecimal initialMargin;

    private BigDecimal positionInitialMargin;

    private BigDecimal openOrderInitialMargin;

    private BigDecimal maxWithdrawAmount;

    private Long updateTime;
}
