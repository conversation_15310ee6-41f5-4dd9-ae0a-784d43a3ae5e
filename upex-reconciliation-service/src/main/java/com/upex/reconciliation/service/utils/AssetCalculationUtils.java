package com.upex.reconciliation.service.utils;

import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillCoinProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty;
import com.upex.reconciliation.service.model.dto.AbstractProperty;

import java.math.BigDecimal;

public class AssetCalculationUtils {

    public static BigDecimal calculateUserBillInOutAssetsResult(AccountTypeEnum accountTypeEnum, BillCoinProperty accountAssetsInfoResult){
        if(accountAssetsInfoResult == null){
            return BigDecimal.ZERO;
        }
        AbstractProperty abstractProperty = BeanCopierUtil.copyProperties(accountAssetsInfoResult, BillCoinTypeProperty.class);
        return calculateUserBillAssetsResult(accountTypeEnum,abstractProperty);
    }


    public static BigDecimal calculateUserBillInOutChangeAssetsResult(AccountTypeEnum accountTypeEnum, AbstractProperty property){
        BigDecimal balance = BigDecimal.ZERO;
        if (property != null) {
            switch (accountTypeEnum) {
                case LEVER_FULL:
                case LEVER_ONE:
                    balance = property.getChangeProp1().add(property.getChangeProp2());
                    break;
                case USDT_MIX_CONTRACT_BL:
                case USD_MIX_CONTRACT_BL:
                case S_USD_MIX_CONTRACT_BL:
                case S_USDT_MIX_CONTRACT_BL:
                case USDC_MIX_CONTRACT_BL:
                case S_USDC_MIX_CONTRACT_BL:
                    balance = property.getChangeProp2().add(property.getChangeProp3());
                    break;
                default:
                    balance = property.getChangePropSum();
                    break;
            }
        }
        return balance;
    }


    public static BigDecimal calculateUserBillAssetsResult(AccountTypeEnum accountTypeEnum, AbstractProperty property){
        BigDecimal balance = BigDecimal.ZERO;
        if (property != null) {
            switch (accountTypeEnum) {
                case FINANCIAL:
                    balance = property.getProp1();
                    break;
                case LEVER_FULL:
                case LEVER_ONE:
                    balance = property.getLeverSum();
                    break;
                case USDT_MIX_CONTRACT_BL:
                case USD_MIX_CONTRACT_BL:
                case S_USD_MIX_CONTRACT_BL:
                case S_USDT_MIX_CONTRACT_BL:
                case USDC_MIX_CONTRACT_BL:
                case S_USDC_MIX_CONTRACT_BL:
                    balance = property.getProp2().add(property.getProp3());
                    break;
                default:
                    balance = property.getPropSum();
                    break;
            }
        }
        return balance;
    }


}
