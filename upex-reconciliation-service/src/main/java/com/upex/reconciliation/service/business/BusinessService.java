package com.upex.reconciliation.service.business;

import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillAllConfig;

import java.util.List;
import java.util.Map;

/**
 * @Author: allen
 * @Date: 2020-05-20 15:46
 * @DES: 业务系统对接
 */
public interface BusinessService {


    Map<Long, Map<Integer, AccountAssetsInfoResult>> getUserAssetsMapSingleThread(AccountTypeEnum accountTypeEnum, List<Long> userIds, com.upex.bill.dto.params.BaseRequest baseRequest, BillAllConfig billConfig);

    List<AccountAssetsInfoResult> getUserAssetsSingleThread(AccountTypeEnum accountTypeEnum, List<Long> userIds, com.upex.bill.dto.params.BaseRequest baseRequest, BillAllConfig billConfig);

    void failureOrderInit(String jobParam);

    void failureOrderRepair();

}


