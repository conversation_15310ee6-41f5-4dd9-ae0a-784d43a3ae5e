package com.upex.reconciliation.service.business.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalCause;
import com.github.benmanes.caffeine.cache.RemovalListener;
import com.upex.reconciliation.facade.enums.ApolloKeyEnum;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.BillCheckService;
import com.upex.reconciliation.service.business.BillFlowCheckService;
import com.upex.reconciliation.service.dao.entity.BillFlowCheckConfig;
import com.upex.reconciliation.service.model.config.UserPermissionConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.service.BillFlowCheckConfigService;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * bill流水检测服务实现类
 *
 * <AUTHOR>
 * @Date 2024/12/25
 */
@Slf4j
@Service
public class BillFlowCheckServiceImpl implements BillFlowCheckService {

    @Resource
    private BillFlowCheckConfigService billFlowCheckConfigService;

    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;

    // 创建缓存
    private Cache<String, BigDecimal> inOutCache = Caffeine.newBuilder()
            .expireAfterWrite(ReconciliationApolloConfigUtils.getApolloObjectConfig(ApolloKeyEnum.BILL_FLOW_EXPIRE_CONFIG, Long.class), TimeUnit.MINUTES)
            .removalListener((RemovalListener<String, BigDecimal>) (key, total, removalCause) -> {
                if (Objects.equals(removalCause, RemovalCause.EXPIRED) || Objects.equals(removalCause, RemovalCause.COLLECTED)
                        || Objects.equals(removalCause, RemovalCause.SIZE)) {
                    checkBillFlowInOut(key, total);
                }
            }).build();

    @Override
    public BigDecimal storeOrSumInOut(CommonBillChangeData commonBillChangeData) {
        String orderId = commonBillChangeData.getOrderId();
        byte accountType = commonBillChangeData.getAccountType();
        BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountType);
        BigDecimal propSum = billCheckService.getChangePropSumByBillChangeData(commonBillChangeData);
        // 累加金额
        BigDecimal sumTotal = inOutCache.asMap().merge(orderId, propSum, BigDecimal::add);
        // 金额为0，说明出入相等
        if (sumTotal.compareTo(BigDecimal.ZERO) == 0) {
            inOutCache.invalidate(orderId);
        }
        return sumTotal;
    }

    @Override
    public Set<String> getBizTypeInOutSet(byte accountType) {
        return billFlowCheckConfigService.getBizTypeInOutSet(String.valueOf(accountType));
    }

    @Override
    public List<UserPermissionConfig> getUserPermissionCode(byte accountType, String bizType) {
        return billFlowCheckConfigService.getUserPermissionCode(String.valueOf(accountType), bizType);
    }

    @Override
    public BillFlowCheckConfig getSubAccount(byte accountType, String bizType) {
        return billFlowCheckConfigService.getSubAccount(String.valueOf(accountType), bizType);
    }

    public void checkBillFlowInOut(String orderId, BigDecimal total) {
        if (total.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        log.warn("orderId:{}, difference total:{}", orderId, total);
    }
}
