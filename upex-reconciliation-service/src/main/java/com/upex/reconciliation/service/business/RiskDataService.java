package com.upex.reconciliation.service.business;


import com.upex.reconciliation.facade.model.ReconBillUserVo;
import com.upex.reconciliation.facade.params.ReconCheckResultsParams;

/**
 * 风控数据服务
 *
 */
public interface RiskDataService {

    /**
     * 全链路数据上报---提币
     *
     * @param reconCheckResultsParams
     * @param reconBillUserVo
     */
    void riskUploadDataForWithdraw(ReconCheckResultsParams reconCheckResultsParams, ReconBillUserVo reconBillUserVo);


}
