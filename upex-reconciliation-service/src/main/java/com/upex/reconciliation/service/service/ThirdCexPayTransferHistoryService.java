package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexPayTransferHistory;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexWithdrawHistory;
import com.upex.reconciliation.service.dao.mapper.cex.ThirdCexPayTransferHistoryMapper;
import com.upex.utils.task.TaskManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ThirdCexPayTransferHistoryService {

    @Resource
    BillDbHelper billDbHelper;

    @Resource
    ThirdCexPayTransferHistoryMapper thirdCexPayTransferHistoryMapper;

    @Resource
    TaskManager taskManager;

    public int insert(ThirdCexPayTransferHistory history) {
        return billDbHelper.doDbOpInReconMaster(() ->
                thirdCexPayTransferHistoryMapper.insert(history));

    }

    public int batchInsert(List<ThirdCexPayTransferHistory> records) {
        return billDbHelper.doDbOpInReconMaster(() ->
                thirdCexPayTransferHistoryMapper.batchInsert(records));
    }

    public List<ThirdCexPayTransferHistory> selectUnCheckWithdraw(Integer cexType, Integer isLegal) {
        return billDbHelper.doDbOpInReconMasterTransaction(() -> thirdCexPayTransferHistoryMapper.selectUnCheckWithdraw(cexType, isLegal));
    }

    public int checkWithdraw(Long id, Integer isLegal) {
        return billDbHelper.doDbOpInReconMasterTransaction(() -> thirdCexPayTransferHistoryMapper.checkWithdraw(id, isLegal));
    }


}
