package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.client.kafka.MessageDeserializer;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.alibaba.otter.canal.protocol.Message;
import com.google.common.util.concurrent.RateLimiter;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.enums.SQLTypeEnum;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.business.convert.model.ConvertBill;
import com.upex.reconciliation.service.business.convert.ReconOrderDataMatcher;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.model.config.ReconOrderConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.upex.reconciliation.service.utils.MetricsUtil.*;
import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.*;

/**
 * 闪兑现货流水处理
 **/

@Slf4j
public class ConvertSpotConsumerRunnable implements KafkaConsumerLifecycle {
    private Byte accountType;
    private volatile boolean running = true;
    private Map<String, Object> consumerConfig;
    private String topic;
    private String groupId;
    private Integer partitionNum;
    private Map<Integer, KafkaConsumer<String, Message>> partitionConsumerMap = new HashMap<>();
    private Set<Integer> closeConsumerPatition = new HashSet<>();
    private Map<Integer, Long> partitionOffsetMap = new ConcurrentHashMap<>();
    private Map<Integer, RateLimiter> partitionRateLimiterMap = new HashMap<>();
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    private AlarmNotifyService alarmNotifyService;
    private ReconOrderDataMatcher orderDataMatcher;
    private ReconOrderConfig reconOrderConfig;
    private KafkaConsumerConfig kafkaConsumerConfig;

    public ConvertSpotConsumerRunnable(ReconciliationSpringContext context,String kafkaServers, KafkaConsumerConfig kafkaConsumerConfig) {

        this.orderDataMatcher = context.getOrderDataMatcher();
        this.alarmNotifyService = context.getAlarmNotifyService();

        this.accountType = AccountTypeEnum.SPOT.getCode();
        this.topic = kafkaConsumerConfig.getTopicName();
        this.groupId = EnvUtil.getKafkaConsumerGroup(kafkaConsumerConfig.getConsumerGroupId());
        this.partitionNum = kafkaConsumerConfig.getPartitionNum();
        accountAssetsServiceFactory = context.getAccountAssetsServiceFactory();
        this.kafkaConsumerConfig = kafkaConsumerConfig;
        consumerConfig = new HashMap<String, Object>();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServers);
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MessageDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, this.groupId);
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, kafkaConsumerConfig.getKafkaBatchSize());
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
        reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
    }

    @Override
    public void run() {
        // 初始化
        log.info("ConvertSpotConsumerRunnable consumerRunnables.run");
        init();
        log.info("ConvertSpotConsumerRunnable init finished");
        for (Map.Entry<Integer, KafkaConsumer<String, Message>> entry : partitionConsumerMap.entrySet()) {
            new Thread(() -> {
                try {
                    startConsume(entry.getKey(), entry.getValue());
                } catch (Exception e) {
                    log.error("ConvertSpotConsumerRunnable.startConsume error accountType {} partition {}",
                            accountType, entry.getKey(), e);
                }
            }, getThreadPrefixName() + "-" + entry.getKey()).start();
        }
    }

    private void startConsume(Integer partition, KafkaConsumer<String, Message> consumer) {
        log.info("ConvertSpotConsumerRunnable startConsume accountType {} partition {}", accountType, partition);
        while (running) {
            try {
                // 从kafka集群中拉取消息
                ConsumerRecords<String, Message> consumerRecords = consumer.poll(Duration.ofMillis(3000));
                consumerRecords.forEach(new java.util.function.Consumer<ConsumerRecord<String, Message>>() {
                    @Override
                    public void accept(ConsumerRecord<String, Message> consumerRecord) {
                        MetricsUtil.histogram(HISTOGRAM_KAFKA_CONSUMER + accountType + "_" + partition, () -> {
                            partitionOffsetMap.put(partition, consumerRecord.offset());
                            Long consumerTimestamp = System.currentTimeMillis();
                            List<FlatMessage> flatMessages = canalMessageDecode(consumerRecord.value());
                            handle(accountType, flatMessages, consumerRecord.offset(),
                                    consumerRecord.partition(), consumerRecord.timestamp(), consumerTimestamp);
                        });
                    }
                });

                consumer.commitSync();
                int messageCount = consumerRecords.count();
                if (messageCount > 0) {
                    partitionRateLimiterMap.get(partition).acquire(messageCount);
                    double rate = partitionRateLimiterMap.get(partition).getRate();
                    if (rate != kafkaConsumerConfig.getMsgRateLimit()) {
                        log.info("ConvertSpotConsumerRunnable recover rateLimiter accountType:{} partition:{} rate:{} newRate:{}",
                                accountType, partition, rate, kafkaConsumerConfig.getMsgRateLimit());
                        partitionRateLimiterMap.get(partition).setRate(kafkaConsumerConfig.getMsgRateLimit());
                    }
                }
            } catch (Exception e) {
                log.error("ConvertSpotConsumerRunnable startConsume error accountType {} partition {} error:{}",
                        accountType, partition, e.getMessage(), e);
                alarmNotifyService.alarm(accountType, KAFKA_POLL_CONSUMER_ERROR, accountType, partition);
            }
        }
        consumer.close();
        closeConsumerPatition.add(partition);
        log.info("ConvertSpotConsumerRunnable consumer.close success {} {}", accountType, partition);
    }

    /**
     * 初始化kafka
     */
    private void init() {
        KafkaConsumer<String, Message> consumer = new KafkaConsumer<String, Message>(consumerConfig);
        try {
            consumer.subscribe(Arrays.asList(topic));
            Set<TopicPartition> assignment = new HashSet<>();
            while (assignment.isEmpty()) {
                consumer.poll(Duration.ofSeconds(3));
                assignment = consumer.assignment();
                log.info("ConvertSpotConsumerRunnable try consumer.assignment {} {} {}", accountType, topic, groupId);
            }

            // 获取最新位点
            Map<TopicPartition, Long> endOffsets = consumer.endOffsets(assignment);
            Map<TopicPartition, OffsetAndMetadata> offsetMap = new HashMap<>();
            // 设置每个分区从最新位点开始消费
            for (TopicPartition partition : assignment) {
                long latestOffset = endOffsets.get(partition);
                offsetMap.put(partition, new OffsetAndMetadata(latestOffset));
                consumer.seek(partition, latestOffset);
            }

            consumer.commitSync();
            log.info("ConvertSpotConsumerRunnable finished start topic: {} config:{} offsetMap:{}",
                    topic, JSONObject.toJSONString(consumerConfig), offsetMap);
        } catch (Exception e) {
            log.error("ConvertSpotConsumerRunnable.init reset offset error ", e);
        } finally {
            consumer.close();
        }
        // 创建监听器，数量和partition数量保持一致
        for (int i = 0; i < partitionNum; i++) {
            KafkaConsumer<String, Message> currentConsumer = new KafkaConsumer<String, Message>(consumerConfig);
            List<TopicPartition> partitions = new ArrayList<>();
            partitions.add(new TopicPartition(topic, i));
            currentConsumer.assign(partitions);
            partitionRateLimiterMap.put(i, RateLimiter.create(kafkaConsumerConfig.getMsgRateLimit()));
            partitionConsumerMap.put(i, currentConsumer);
        }
    }


    /**
     * 消息处理
     *
     * @param accountType
     * @param flatMessages
     * @param offset
     * @param partition
     * @param kafkaTimestamp
     * @param consumerTimestamp
     * @return
     */
    public void handle(Byte accountType, List<FlatMessage> flatMessages, Long offset, Integer partition, Long kafkaTimestamp, Long consumerTimestamp) {
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        for (FlatMessage flatMessage : flatMessages) {
            List<CommonBillChangeData> commonBillChangeDataList = buildBillChangeDataList(accountType, apolloBizConfig, flatMessage, offset, partition);
            for (CommonBillChangeData commonBillChangeData : commonBillChangeDataList) {
                String bizType = commonBillChangeData.getBizType();
                if (reconOrderConfig.getFlowTypes().contains(bizType)) {
                    // 转换为ReconOrderData
                    ConvertBill bill = convertToReconOrderData(commonBillChangeData);
                    orderDataMatcher.saveFlowOrderData(bill,bizType);
                    log.info("ConvertSpotConsumerRunnable handle commonBillChangeData bizType {},bizId：{}",bizType,commonBillChangeData.getOrderId());
                }
            }
        }
    }

    /**
     * 将CommonBillChangeData转换为ReconOrderData
     */
    private ConvertBill convertToReconOrderData(CommonBillChangeData commonBillChangeData) {
        ConvertBill bill = new ConvertBill();

        // 设置订单ID
        if (commonBillChangeData.getOrderId() != null) {
            bill.setOrderId(Long.valueOf(commonBillChangeData.getOrderId()));
        }
        bill.setBillType(commonBillChangeData.getBizType());
        // 设置熟悉
        bill.setAccountId(commonBillChangeData.getAccountId());
        bill.setCoinId(commonBillChangeData.getCoinId());
        bill.setBalanceChange(commonBillChangeData.getChangeProp1());
        return bill;
    }




    /**
     * 消息解析
     *
     * @param accountType
     * @param apolloBizConfig
     * @param flatMessage
     * @param offset
     * @param partition
     * @return
     */
    private List<CommonBillChangeData> buildBillChangeDataList(Byte accountType, ApolloReconciliationBizConfig apolloBizConfig, FlatMessage flatMessage, Long offset, Integer partition) {
        MetricsUtil.counter(COUNTER_BINLOG_CMD_ALL);
        String typeStr = flatMessage.getType();
        boolean isDdl = flatMessage.getIsDdl();
        if (isDdl) {
            return Collections.emptyList();
        }
        SQLTypeEnum sqlTypeEnum = SQLTypeEnum.convert(typeStr);
        if (null == sqlTypeEnum) {
            log.error("ConvertSpotConsumerRunnable unable to resolve sqlType:{}", typeStr);
            return Collections.emptyList();
        }
        List<Map<String, String>> dataList = flatMessage.getData();
        switch (sqlTypeEnum) {
            case DELETE:
            case UPDATE:
                return Collections.emptyList();
            case INSERT:
                MetricsUtil.counter(COUNTER_BINLOG_CMD_INSERT);
                if (apolloBizConfig.getReconKafkaOpsConfig().isKafkaConsumeSkipBusinessLogic()) {
                    List<String> createTimes = new ArrayList<String>();
                    List<String> bizTimes = new ArrayList<String>();
                    List<String> createDates = new ArrayList<String>();
                    List<String> bizIds = new ArrayList<String>();
                    if (CollectionUtils.isNotEmpty(dataList)) {
                        for (Map<String, String> dataMap : dataList) {
                            if (dataMap.get("create_time") != null) {
                                createTimes.add(dataMap.get("create_time"));
                            }
                            if (dataMap.get("biz_time") != null) {
                                bizTimes.add(dataMap.get("biz_time"));
                            }
                            if (dataMap.get("create_date") != null) {
                                createDates.add(dataMap.get("create_date"));
                            }
                            if (dataMap.get("bill_id") != null) {
                                bizIds.add((dataMap.get("bill_id")));
                            } else if (dataMap.get("id") != null) {
                                bizIds.add((dataMap.get("id")));
                            }
                        }
                    }
                    return Collections.emptyList();
                }
                List<CommonBillChangeData> list = accountAssetsServiceFactory.getMessageDecoder(AccountTypeEnum.SPOT.getCode()).messageDecode(alarmNotifyService, dataList, flatMessage, partition, offset, accountType);
                return list;
        }
        return Collections.emptyList();
    }


    @Override
    public void shutdown() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return closeConsumerPatition.size() != partitionNum;
    }

    @Override
    public String getThreadPrefixName() {
        return "kafka-consumer-profit-convert-spot";
    }
}


