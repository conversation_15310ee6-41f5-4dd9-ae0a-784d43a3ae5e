package com.upex.reconciliation.service.dao.typehandler;

import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class EncryptedStringTypeHandler extends BaseTypeHandler<String> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        try {
            ps.setString(i, HmacUtil.encrypt(parameter));
        }catch (Exception e){
            ps.setString(i, parameter);
        }
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        try {
            return HmacUtil.decrypt(rs.getString(columnName));
        }catch (Exception e){
            return rs.getString(columnName);
        }
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        try {
            return HmacUtil.decrypt(rs.getString(columnIndex));
        }catch (Exception e){
            return rs.getString(columnIndex);
        }
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        try {
            return HmacUtil.decrypt(cs.getString(columnIndex));
        }catch (Exception e){
            return cs.getString(columnIndex);
        }
    }
}
