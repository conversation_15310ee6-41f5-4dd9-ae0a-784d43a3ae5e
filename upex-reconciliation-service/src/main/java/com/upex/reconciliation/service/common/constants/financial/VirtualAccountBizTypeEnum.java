package com.upex.reconciliation.service.common.constants.financial;

import com.upex.spot.dto.enums.SpotBillBizTypeEnum;
import lombok.Getter;

@Getter
public enum VirtualAccountBizTypeEnum {

    SAVINGS_IN(101, FinancialTypeEnum.FINANCIAL_SAVINGS.getGroupType(), SpotBillBizTypeEnum.SINGLE_USER_OUT.getCode(), "savings申购"),
    SAVINGS_OUT(102, FinancialTypeEnum.FINANCIAL_SAVINGS.getGroupType(), SpotBillBizTypeEnum.SINGLE_RELEASE_USER_IN.getCode(), "savings赎回"),
    SAVINGS_PRE_INTEREST(103, FinancialTypeEnum.FINANCIAL_SAVINGS.getGroupType(), 0, "savings计息"),
    SAVINGS_SETTLE_INTEREST(104, FinancialTypeEnum.FINANCIAL_SAVINGS.getGroupType(), SpotBillBizTypeEnum.SINGLE_INTEREST_USER_IN.getCode(), "savings利息发放"),
    BGB_EARN_IN(201, FinancialTypeEnum.FINANCIAL_BGB_EARN.getGroupType(), SpotBillBizTypeEnum.LOCK_USER_OUT.getCode(), "bgbEarn申购"),
    BGB_EARN_OUT(202, FinancialTypeEnum.FINANCIAL_BGB_EARN.getGroupType(), SpotBillBizTypeEnum.LOCK_RELEASE_USER_IN.getCode(), "bgbEarn赎回"),
    BGB_EARN_PRE_INTEREST(203, FinancialTypeEnum.FINANCIAL_BGB_EARN.getGroupType(), 0, "bgbEarn计息"),
    BGB_EARN_SETTLE_INTEREST(204, FinancialTypeEnum.FINANCIAL_BGB_EARN.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "bgbEarn结息"),
    LAUNCH_POOL_IN(301, FinancialTypeEnum.FINANCIAL_LAUNCH_POOL.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_USER_OUT.getCode(), "launchPool申购"),
    LAUNCH_POOL_OUT(302, FinancialTypeEnum.FINANCIAL_LAUNCH_POOL.getGroupType(), SpotBillBizTypeEnum.LOCK_RELEASE_USER_IN.getCode(), "launchPool赎回"),
    LAUNCH_POOL_PRE_INTEREST(303, FinancialTypeEnum.FINANCIAL_LAUNCH_POOL.getGroupType(), 0, "launchPool计息"),
    LAUNCH_POOL_SETTLE_INTEREST(304, FinancialTypeEnum.FINANCIAL_LAUNCH_POOL.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "launchPool结息"),
    SHARK_FIN_IN(401, FinancialTypeEnum.FINANCIAL_SHARK_FIN.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_USER_OUT.getCode(), "sharkFin申购"),
    SHARK_FIN_OUT(402, FinancialTypeEnum.FINANCIAL_SHARK_FIN.getGroupType(), SpotBillBizTypeEnum.LOCK_RELEASE_USER_IN.getCode(), "sharkFin赎回"),
    SHARK_FIN_PRE_INTEREST(403, FinancialTypeEnum.FINANCIAL_SHARK_FIN.getGroupType(), 0, "sharkFin计息"),
    SHARK_FIN_SETTLE_INTEREST(404, FinancialTypeEnum.FINANCIAL_SHARK_FIN.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "sharkFin结息"),
    DUAL_INVEST_IN(501, FinancialTypeEnum.FINANCIAL_DUAL_INVEST.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_DOUBLE_BUY_USER_OUT.getCode(), "dualInvest冻结申购"),
    DUAL_INVEST_DIRECT_IN(502, FinancialTypeEnum.FINANCIAL_DUAL_INVEST.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_USER_OUT.getCode(), "dualInvest直接申购"),
    DUAL_INVEST_OUT(503, FinancialTypeEnum.FINANCIAL_DUAL_INVEST.getGroupType(), SpotBillBizTypeEnum.LOCK_RELEASE_USER_IN.getCode(), "dualInvest赎回"),
    DUAL_INVEST_PRE_INTEREST(504, FinancialTypeEnum.FINANCIAL_DUAL_INVEST.getGroupType(), 0, "dualInvest记息"),
    DUAL_INVEST_SETTLE_INTEREST(505, FinancialTypeEnum.FINANCIAL_DUAL_INVEST.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "dualInvest结息"),
    DUAL_INVEST_SPOT_OUT(506, FinancialTypeEnum.FINANCIAL_DUAL_INVEST.getGroupType(), SpotBillBizTypeEnum.LOCK_RELEASE_USER_IN.getCode(), "dualInvest现货赎回"),
    SAVINGS_FIXED_IN(601, FinancialTypeEnum.FINANCIAL_SAVINGS_FIXED.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_SINGLE_REGULAR_USER_OUT.getCode(), "savingsFixed申购"),
    SAVINGS_FIXED_OUT(602, FinancialTypeEnum.FINANCIAL_SAVINGS_FIXED.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_SINGLE_REGULAR_USER_IN.getCode(), "savingsFixed赎回"),
    SAVINGS_FIXED_PRE_INTEREST(603, FinancialTypeEnum.FINANCIAL_SAVINGS_FIXED.getGroupType(), 0, "savingsFixed记息"),
    SAVINGS_FIXED_SETTLE_INTEREST(604, FinancialTypeEnum.FINANCIAL_SAVINGS_FIXED.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "savingsFixed结息"),
    SAVINGS_FIXED_DEDUCT_INTEREST(605, FinancialTypeEnum.FINANCIAL_SAVINGS_FIXED.getGroupType(), 0, "savingsFixed利息罚息"),
    @Deprecated
    SAVINGS_FIXED_DEDUCT_PRINCIPAL(606, FinancialTypeEnum.FINANCIAL_SAVINGS_FIXED.getGroupType(), 0, "savingsFixed本金罚息"),

    SAVINGS_FIXED_SYS_DEDUCT_PRINCIPAL(607, FinancialTypeEnum.FINANCIAL_SAVINGS_FIXED.getGroupType(), 0, "savingsFixed本金系统账户罚息"),
    SAVINGS_FIXED_OUT_TO_FREEZE(608, FinancialTypeEnum.FINANCIAL_SAVINGS_FIXED.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_SINGLE_REGULAR_USER_FREEZE_IN.getCode(), "savingsFixed赎回到冻结"),

    POS_STAKING_IN(701, FinancialTypeEnum.FINANCIAL_POS_STAKING.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_POS_PLEDGE_USER_OUT.getCode(), "posStaking申购"),
    POS_STAKING_OUT(702, FinancialTypeEnum.FINANCIAL_POS_STAKING.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_POS_PLEDGE_USER_IN.getCode(), "posStaking赎回"),
    POS_STAKING_PRE_INTEREST(703, FinancialTypeEnum.FINANCIAL_POS_STAKING.getGroupType(), 0, "posStaking记息"),
    POS_STAKING_SETTLE_INTEREST(704, FinancialTypeEnum.FINANCIAL_POS_STAKING.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "posStaking结息"),
    @Deprecated
    POS_STAKING_DEDUCT_PRINCIPAL(705, FinancialTypeEnum.FINANCIAL_POS_STAKING.getGroupType(), 0, "posStaking本金罚息"),

    POS_STAKING_SYS_DEDUCT_PRINCIPAL(706, FinancialTypeEnum.FINANCIAL_POS_STAKING.getGroupType(), 0, "posStaking本金系统账户罚息"),
    TREND_IN(801, FinancialTypeEnum.FINANCIAL_TREND.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_TREND_USER_OUT.getCode(), "trend申购"),
    TREND_OUT(802, FinancialTypeEnum.FINANCIAL_TREND.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_TREND_USER_IN.getCode(), "trend赎回"),
    TREND_PRE_INTEREST(803, FinancialTypeEnum.FINANCIAL_TREND.getGroupType(), 0, "trend计息"),
    TREND_SETTLE_INTEREST(804, FinancialTypeEnum.FINANCIAL_TREND.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "trend结息"),
    RANGE_SNIPER_IN(901, FinancialTypeEnum.FINANCIAL_RANGE_SNIPER.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_RANGE_HUNTER_USER_OUT.getCode(), "rangeSniper申购"),
    RANGE_SNIPER_OUT(902, FinancialTypeEnum.FINANCIAL_RANGE_SNIPER.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_RANGE_HUNTER_USER_IN.getCode(), "rangeSniper赎回"),
    RANGE_SNIPER_PRE_INTEREST(903, FinancialTypeEnum.FINANCIAL_RANGE_SNIPER.getGroupType(), 0, "rangeSniper计息"),
    RANGE_SNIPER_SETTLE_INTEREST(904, FinancialTypeEnum.FINANCIAL_RANGE_SNIPER.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "rangeSniper计息"),
    RANGE_SNIPER_SPOT_OUT(905, FinancialTypeEnum.FINANCIAL_RANGE_SNIPER.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_RANGE_HUNTER_USER_IN.getCode(), "rangeSniper现货赎回"),
    BGB_STAKING_IN(1001, FinancialTypeEnum.FINANCIAL_BGB_STAKING.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_BGB_STAKING_USER_OUT.getCode(), "bgbStaking申购"),
    BGB_STAKING_OUT(1002, FinancialTypeEnum.FINANCIAL_BGB_STAKING.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_BGB_STAKING_USER_IN.getCode(), "bgbStaking赎回"),
    BGB_STAKING_PRE_INTEREST(1003, FinancialTypeEnum.FINANCIAL_BGB_STAKING.getGroupType(), 0, "bgbStaking计息"),
    BGB_STAKING_SETTLE_INTEREST(1004, FinancialTypeEnum.FINANCIAL_BGB_STAKING.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "bgbStaking结息"),
    BGB_STAKING_DEDUCT_PRINCIPAL(1005, FinancialTypeEnum.FINANCIAL_BGB_STAKING.getGroupType(), 0, "bgbStaking本金罚息"),
    AVALANCHE_IN(1101, FinancialTypeEnum.FINANCIAL_AVALANCHE.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_AVALANCHE_USER_OUT.getCode(), "雪球申购"),
    AVALANCHE_OUT(1102, FinancialTypeEnum.FINANCIAL_AVALANCHE.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_AVALANCHE_USER_IN.getCode(), "雪球赎回"),
    AVALANCHE_PRE_INTEREST(1103, FinancialTypeEnum.FINANCIAL_AVALANCHE.getGroupType(), 0, "雪球预结息"),
    AVALANCHE_SETTLE_INTEREST(1104, FinancialTypeEnum.FINANCIAL_AVALANCHE.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "雪球结息"),
    FUND_MARKET_IN(1201, FinancialTypeEnum.FINANCIAL_FUND_MARKET.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_FUND_SUPERMARKET_USER_OUT.getCode(), "fundMarket申购"),
    FUND_MARKET_OUT(1202, FinancialTypeEnum.FINANCIAL_FUND_MARKET.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_FUND_SUPERMARKET_USER_IN.getCode(), "fundMarket赎回"),
    FUND_MARKET_PRE_INTEREST(1203, FinancialTypeEnum.FINANCIAL_FUND_MARKET.getGroupType(), 0, "fundMarket计息"),
    FUND_MARKET_SETTLE_INTEREST(1204, FinancialTypeEnum.FINANCIAL_FUND_MARKET.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "fundMarket结息"),
    DIRECT_INVEST_IN(1301, FinancialTypeEnum.FINANCIAL_DIRECT_INVEST.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_FUND_INVESTMENT_USER_OUT.getCode(), "现货定投申购"),
    DIRECT_INVEST_OUT(1302, FinancialTypeEnum.FINANCIAL_DIRECT_INVEST.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_FUND_INVESTMENT_USER_IN.getCode(), "现货定投赎回"),
    DIRECT_INVEST_PRE_INTEREST(1303, FinancialTypeEnum.FINANCIAL_DIRECT_INVEST.getGroupType(), 0, "现货定投预结息"),
    DIRECT_INVEST_SETTLE_INTEREST(1304, FinancialTypeEnum.FINANCIAL_DIRECT_INVEST.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "现货定投结息"),

    POOL_X_IN(1401, FinancialTypeEnum.FINANCIAL_POOL_X.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_POOL_X_USER_OUT.getCode(), "poolX申购"),
    POOL_X_OUT(1402, FinancialTypeEnum.FINANCIAL_POOL_X.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_POOL_X_USER_IN.getCode(), "poolX赎回"),
    POOL_X_PRE_INTEREST(1403, FinancialTypeEnum.FINANCIAL_POOL_X.getGroupType(), 0, "poolX计息"),
    POOL_X_SETTLE_INTEREST(1404, FinancialTypeEnum.FINANCIAL_POOL_X.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "poolX结息"),

    SAVINGS_BATCH_SETTLE_INTEREST(1504, FinancialTypeEnum.FINANCIAL_SAVINGS.getGroupType(), SpotBillBizTypeEnum.BATCH_INTEREST_USER_IN.getCode(), "savings批量结息"),

    POS_STAKING_VIP_IN(1601, FinancialTypeEnum.FINANCIAL_POS_STAKING.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_POS_PLEDGE_USER_OUT.getCode(), "posStaking私募申购"),
    POS_STAKING_VIP_OUT(1602, FinancialTypeEnum.FINANCIAL_POS_STAKING.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_POS_PLEDGE_USER_IN.getCode(), "posStaking私募赎回"),
    POS_STAKING_VIP_CHAIN_IN(1604, FinancialTypeEnum.FINANCIAL_POS_STAKING.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_POS_VIP_SYS_OUT.getCode(), "posStaking私募待上链-系统出"),
    POS_STAKING_VIP_CHAIN_OUT(1605, FinancialTypeEnum.FINANCIAL_POS_STAKING.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_POS_VIP_SYS_IN.getCode(), "posStaking私募待上链-系统入"),
    ON_CHAIN_ELITE_IN(1701, FinancialTypeEnum.FINANCIAL_ON_CHAIN_ELITE.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_ONCHAINELITE_USER_FREEZE_OUT.getCode(), "链上精选申购"),
    ON_CHAIN_ELITE_OUT(1702, FinancialTypeEnum.FINANCIAL_ON_CHAIN_ELITE.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_ONCHAINELITE_USER_FREEZE_IN.getCode(), "链上精选赎回"),
    ON_CHAIN_ELITE_SETTLE_INTEREST(1704, FinancialTypeEnum.FINANCIAL_ON_CHAIN_ELITE.getGroupType(), SpotBillBizTypeEnum.LOCK_INTEREST_USER_IN.getCode(), "链上精选结息"),
//    CUSTOM_STRUCTURED_IN(1801, FinancialTypeEnum.FINANCIAL_CUSTOM_STRUCTURED.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_CUSTOM_USER_OUT.getCode(), "囤币卖币申购"),
//    CUSTOM_STRUCTURED_OUT(1802, FinancialTypeEnum.FINANCIAL_CUSTOM_STRUCTURED.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_CUSTOM_SYS_OUT.getCode(), "囤币卖币理财赎回"),
//    CUSTOM_STRUCTURED_SPOT_OUT(1803, FinancialTypeEnum.FINANCIAL_CUSTOM_STRUCTURED.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_CUSTOM_USER_IN.getCode(), "囤币卖币现货赎回"),

 //   CUSTOM_STRUCTURED_IN(1801, FinancialTypeEnum.FINANCIAL_CUSTOM_STRUCTURED.getGroupType(), SpotBillBizTypeEnum.FINANCIAL_SINGLE_REGULAR_USER_OUT.getCode(), "savingsFixed申购"),

    ;

    /**
     * 理财业务类型
     */
    private final Integer finBizType;

    /**
     * 理财业务
     */
    private final Integer groupType;

    /**
     * 业务类型
     */
    private final Integer bizType;

    /**
     * 描述
     */
    private final String desc;

    VirtualAccountBizTypeEnum(Integer finBizType, Integer groupType, Integer bizType, String desc) {
        this.finBizType = finBizType;
        this.groupType = groupType;
        this.bizType = bizType;
        this.desc = desc;
    }

    public static VirtualAccountBizTypeEnum getEnum(Integer finBizType) {
        for (VirtualAccountBizTypeEnum bizTypeEnum : VirtualAccountBizTypeEnum.values()) {
            if (bizTypeEnum.getFinBizType().equals(finBizType)) {
                return bizTypeEnum;
            }
        }
        return null;
    }
}
