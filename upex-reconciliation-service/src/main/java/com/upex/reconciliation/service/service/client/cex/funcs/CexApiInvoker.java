package com.upex.reconciliation.service.service.client.cex.funcs;

import com.upex.reconciliation.service.service.client.cex.client.ICexApiClient;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CommonReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;

@FunctionalInterface
public interface CexApiInvoker {
    CommonRes apply(ICexApiClient client, CommonReq req);
}
