package com.upex.reconciliation.service.model.domain;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.upex.mixcontract.common.literal.enums.*;
import com.upex.reconciliation.service.common.constants.enums.CommonBillChangeDataMsgType;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.utils.NumberUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Slf4j
public class ContractBillChangeModel implements IBillChange {

    @JSONField(name = "id")
    private Long id;

    @JSONField(name = "account_id")
    private Long accountId;

    @JSONField(name = "business_line")
    private BusinessLineEnum businessLine;

    @JSONField(name = "second_business_line")
    private String secondBusinessLine;

    //去掉feeTokenId ， 直接使用tokenId， 遇到多币种时， 生成多条记录。 其中， 关于资产的变化每一种都生成
    @JSONField(name = "token_id")
    private String tokenId;

    @JSONField(name = "symbol_id")
    private String symbolId;


    @JSONField(name = "margin_mode")
    private Integer marginMode;

    @JSONField(name = "hold_mode")
    private Integer holdMode;

    @JSONField(name = "hold_side")
    private Integer holdSide;

    @JSONField(name = "biz_type")
    private String bizType;

    @JSONField(name = "biz_no")
    private String bizNo;

    @JSONField(name = "before_balance_available")
    private BigDecimal beforeBalanceAvailable = BigDecimal.ZERO;

    @JSONField(name = "balance_available_change")
    private BigDecimal balanceAvailableChange = BigDecimal.ZERO;

    @JSONField(name = "before_balance_locked")
    private BigDecimal beforeBalanceLocked = BigDecimal.ZERO;

    @JSONField(name = "balance_locked_change")
    private BigDecimal balanceLockedChange = BigDecimal.ZERO;

    @JSONField(name = "before_position_locked_margin")
    private BigDecimal beforePositionLockedMargin = BigDecimal.ZERO;

    @JSONField(name = "position_locked_margin_change")
    private BigDecimal positionLockedMarginChange = BigDecimal.ZERO;

    @JSONField(name = "before_position_margin")
    private BigDecimal beforePositionMargin = BigDecimal.ZERO;

    @JSONField(name = "position_margin_change")
    private BigDecimal positionMarginChange = BigDecimal.ZERO;

    @JSONField(name = "before_position_available")
    private BigDecimal beforePositionAvailable = BigDecimal.ZERO;

    @JSONField(name = "position_available_change")
    private BigDecimal positionAvailableChange = BigDecimal.ZERO;


    // 双向时，这个字段表示 当前数量
    // 单向，做多： 这个字段表示，当前多数量
    /*
    当前多数量 < 0：（单向卖）
      当前空数量 = 当前空数量 + abs(当前多数量)
      当前多数量 = 0

      方向时做多，单项
      if(beforePositionLocked < 0)
      第一条流水，  当前空数量= beforePositionLocked  +abs（）


     */
    @JSONField(name = "before_position_locked")
    private BigDecimal beforePositionLocked = BigDecimal.ZERO;


    @JSONField(name = "position_locked_change")
    private BigDecimal positionLockedChange = BigDecimal.ZERO;

    //balanceChange当有当前帐户与外部帐户之间有资产往来时， 才会变化; 且不包括手续费
    @JSONField(name = "balance_change")
    private BigDecimal balanceChange = BigDecimal.ZERO;
//结算币种的改变量

    @JSONField(name = "quote_token_balance_change")
    private BigDecimal quoteTokenBalanceChange = BigDecimal.ZERO;

    @JSONField(name = "count_change")
    private BigDecimal countChange = BigDecimal.ZERO;

    @JSONField(name = "fee")
    private BigDecimal fee = BigDecimal.ZERO;
//结算币种的手续费

    @JSONField(name = "quote_token_fee")
    private BigDecimal quoteTokenFee = BigDecimal.ZERO;


    @JSONField(name = "show_type")
    private Byte showType;


    @JSONField(name = "show_flag")
    private Byte showFlag;


    @JSONField(name = "remark_type")
    private RemarkTypeEnum remarkType;


    @JSONField(name = "biz_time")
    private Date bizTime;

    @JSONField(name = "create_time")
    private Date createTime;


    @JSONField(name = "params")
    private String params;

    @JSONField(name = "version")
    private Long version;


    @Override
    public CommonBillChangeData getBillChangeData(Byte accountType) {
        CommonBillChangeData commonBillChangeData = CommonBillChangeData.builder()
                .bizId(this.id)
                .accountId(this.accountId)
                .bizType(this.bizType)
                .symbolId(this.symbolId)
                .tokenId(this.tokenId)
                .bizTime(this.bizTime)
                .marginMode(this.marginMode != null ? MarginModeEnum.toEnum(this.marginMode) : null)
                .holdMode(HoldModeEnum.toEnum(this.holdMode))
                .holdSide(HoldSideEnum.toEnum(this.holdSide))
                .msgType(CommonBillChangeDataMsgType.NORMAL)
                .build();
        commonBillChangeData.setBizTimeFromId(this.bizTime);
        //金额转换
        if (this.getBalanceAvailableChange() != null) {
            commonBillChangeData.setChangeProp1(this.balanceAvailableChange);
            commonBillChangeData.setProp1(this.beforeBalanceAvailable.add(this.balanceAvailableChange));
            commonBillChangeData.setChangeProp2(this.balanceAvailableChange);
            commonBillChangeData.setProp2(this.beforeBalanceAvailable.add(this.balanceAvailableChange));
        } else {
            log.error("ContractBillChangeModel.getBillChangeData balanceAvailableChange is null {}", JSON.toJSONString(this));
        }
        //处理仓位占用 只有逐仓的时候才有
        BigDecimal prop3 = this.beforePositionMargin.add(this.positionMarginChange);
        if (StringUtils.isNoneBlank(params)) {
            // 每次根据内存仓位数据统计逐仓保证金总数（fm）
            // ava=before_balance_available+balance_available_change+fm+position_margin_change+(单项需要加上财务记录表params里面sPnl单项盈亏字段）
            // 参考连接 https://bglimited.larksuite.com/wiki/HcVkwM70Hi74tLkrGzAuDh01skg
            JSONObject paramsJson = JSONObject.parseObject(params);
            BigDecimal ava = paramsJson.getBigDecimal("ava");
            BigDecimal sPnl = paramsJson.getBigDecimal("sPnl");
            if (ava != null) {
                prop3 = NumberUtil.subtract(ava, this.beforeBalanceAvailable, this.balanceAvailableChange, sPnl);
            }
        }
        commonBillChangeData.setChangeProp3(this.positionMarginChange);
        commonBillChangeData.setProp3(prop3);

        //处理已实现操作
        FinanceBizTypeEnum financeBizTypeEnum = FinanceBizTypeEnum.toEnum(this.getBizType());
        if (financeBizTypeEnum.isPositionProfitType() && this.getQuoteTokenBalanceChange() != null) {
            commonBillChangeData.setChangeProp4(this.getBalanceChange());
            commonBillChangeData.setChangeProp5(this.getQuoteTokenBalanceChange());
        }
        if (this.fee.compareTo(BigDecimal.ZERO) != 0) {
            commonBillChangeData.setChangeFee(this.fee);
        }

        commonBillChangeData.setParams(this.getParams());
        commonBillChangeData.setCountChange(this.getCountChange());
//        log.info("ContractBillChangeModel convert format success {}", JSONObject.toJSONString(commonBillChangeData));
        return commonBillChangeData;
    }
}
