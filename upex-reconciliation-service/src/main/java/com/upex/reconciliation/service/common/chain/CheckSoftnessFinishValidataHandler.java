package com.upex.reconciliation.service.common.chain;

import com.upex.bill.dto.enums.BillExceptionEnum;
import com.upex.reconciliation.service.service.BillCoinUserPropertyService;
import com.upex.reconciliation.service.common.exception.SoftnessValidataException;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 软性校验，出错不抛异常
 * @ClassName: CheckEmptyValidataHandler
 * @date 2022/4/29 11:35 AM
 * <AUTHOR>
*/
@Slf4j
@Component
public class CheckSoftnessFinishValidataHandler extends AbstractCheckHandler{


    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;

    public CheckSoftnessFinishValidataHandler(BillCoinUserPropertyService billCoinUserPropertyService) {
        this.billCoinUserPropertyService = billCoinUserPropertyService;
    }

    @Override
    public void doHandler(Long userId, Long snapShotTime) {

        BillCoinUserProperty billCoinUserPropertyToCheckExist = billCoinUserPropertyService.selectBySingleUserIdLatest(userId, "default",10 );
        if (Objects.isNull(billCoinUserPropertyToCheckExist)) {
            log.error("userId:{} is not exist", userId);
            throw new SoftnessValidataException(Integer.parseInt(BillExceptionEnum.USER_NOT_EXISTS.getCode()), "当前用户不存在：" + userId);
        }
    }
}
