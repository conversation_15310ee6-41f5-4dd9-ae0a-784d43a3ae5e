package com.upex.reconciliation.service.service;


import com.upex.reconciliation.service.dao.entity.BillContractProfitCoinDetail;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_coin_detail(币种维度盈亏明细表)】的数据库操作Service
 * @createDate 2023-06-09 17:18:46
 */
public interface BillContractProfitCoinDetailService {
    /**
     * 获取币种维度盈亏明细数据，返回Map结构
     *
     * @param accountType
     * @param accountParam
     * @param checkOkTime
     * @param profitType
     * @return
     */
    Map<Integer, BillContractProfitCoinDetail> getBillContractProfitCoinDetailMap(Byte accountType, String accountParam, Date checkOkTime, String profitType);

    /**
     * 获取最新的一条数据
     *
     * @param accountType
     * @param accountParam
     * @param profitType
     * @return
     */
    Date getLastCheckOkTime(Byte accountType, String accountParam, String profitType);

    /**
     * 批量插入数据
     *
     * @param accountType
     * @param accountParam
     * @param billContractProfitCoinDetailList
     * @return
     */
    public int batchInsert(Byte accountType, String accountParam, List<BillContractProfitCoinDetail> billContractProfitCoinDetailList);

    /**
     * 批量删除数据
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     */
    int deleteByCheckTime(Byte accountType, String accountParam, Date checkTime);

    /**
     * 批量删除
     *
     * @param minId
     * @param deleteSize
     * @param accountType
     * @param accountParam
     * @return
     */
    Boolean batchDelete(Long minId, Long deleteSize, Byte accountType, String accountParam);

    List<BillContractProfitCoinDetail> selectListByAccountTypeAndCheckTime(Byte accountType, String accountParam, Date checkTime);
}
