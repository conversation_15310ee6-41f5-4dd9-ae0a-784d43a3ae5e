package com.upex.reconciliation.service.dao.mapper.cex;


import com.upex.reconciliation.service.dao.cex.entity.UserWithdrawAddress;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserWithdrawAddressMapper {

    /**
     * 插入充值地址记录
     *
     * @param record 记录对象
     * @return 影响行数
     */
    int insert(UserWithdrawAddress record);


    /**
     * 根据地址、币种和链ID查询充值地址信息
     *

     */
    UserWithdrawAddress selectByAddress(@Param("address") String address,
                                              @Param("coinName") String coinName,
                                              @Param("network") String network);

    /**
     * 根据用户ID和币种查询充值地址
     *
     * @param bgUid 用户ID
     * @param coinName 币种
     * @return 查询结果
     */
    List<UserWithdrawAddress> selectByBgUidAndCoin(@Param("bgUid") String bgUid,
                                                   @Param("coinName") String coinName);
}

