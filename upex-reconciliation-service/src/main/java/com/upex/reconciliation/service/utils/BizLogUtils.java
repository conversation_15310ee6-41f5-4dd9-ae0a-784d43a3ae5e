package com.upex.reconciliation.service.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.google.common.base.Stopwatch;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.prometheus.MonitorSummaryUtil;
import com.upex.reconciliation.service.common.constants.enums.LogLevelEnum;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.PRINT_LOG_FOR_SLOW_INTERFACE_ALARM;

/**
 * 业务日志分级打印工具类
 */
@Slf4j
public class BizLogUtils {
    /***告警通知服务***/
    private static volatile AlarmNotifyService alarmNotifyService = null;

    /**
     * 获取告警通知服务
     *
     * @return
     */
    private static AlarmNotifyService getAlarmNotifyService() {
        if (alarmNotifyService == null) {
            synchronized (BizLogUtils.class) {
                if (alarmNotifyService == null) {
                    alarmNotifyService = SpringUtil.getBean(AlarmNotifyService.class);
                }
            }
        }
        return alarmNotifyService;
    }

    public static void log(LogLevelEnum logLevelEnum, ApolloReconciliationBizConfig apolloReconciliationBizConfig, String format, Object... arguments) {
        // 日志等级跟kafka消费tps速率联动
        // 大于800 1
        // 大于300 2
        // 300及以下无限制
//        Integer mqConsumerRateLimit = apolloReconciliationBizConfig.getMqConsumerRateLimit();
//        if (mqConsumerRateLimit > BillConstants.HIGH_MQ_CONSUME_SPEED) {
//            apolloReconciliationBizConfig.setLogLevelCode(LogLevelEnum.MAIN_PROCESS.getCode());
//        }
//        else if (mqConsumerRateLimit > BillConstants.LOW_MQ_CONSUME_SPEED) {
//            apolloReconciliationBizConfig.setLogLevelCode(LogLevelEnum.KEY_RESULT.getCode());
//        }
        if (logLevelEnum.getCode() <= apolloReconciliationBizConfig.getLogLevelCode()) {
            log.info(format, arguments);
        }
    }

    /**
     * 白名单打日志
     *
     * @param userId
     * @param whiteList
     * @param format
     * @param arguments
     */
    public static void info(Long userId, Set<Long> whiteList, String format, Object... arguments) {
        if (CollectionUtils.isNotEmpty(whiteList) && whiteList.contains(userId)) {
            log.info(format, arguments);
        }
    }

    public static void info(Boolean isLog, String format, Object... arguments) {
        if (isLog != null && isLog) {
            log.info(format, arguments);
        }
    }

    public static void log(LogLevelEnum logLevelEnum, GlobalBillConfig globalBillConfig, String format, Object... arguments) {
        if (logLevelEnum.getCode() <= globalBillConfig.getLogLevelCode()) {
            log.info(format, arguments);
        }
    }

    public static void log(LogLevelEnum logLevelEnum, Integer configLogLevel, String format, Object... arguments) {
        if (logLevelEnum.getCode() <= configLogLevel) {
            log.info(format, arguments);
        }
    }

    /**
     * 通用埋点上报&日志打印
     *
     * @param stopwatch
     * @param logSign
     * @param userId
     * @param accountType
     */
    //  加uid打印
    public static void printLogForSlowInterface(Stopwatch stopwatch, String logSign, Long userId, Byte accountType) {
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        // 开关替换，提币接口独立一个日志开关
        if (globalBillConfig.isWithdrawInterfaceLogOpen()) {
            // 耗时已经通过 grafana进行统计，原日志层面耗时打印，仅打印高耗时的log
            long milliSecondConsumed = stopwatch.elapsed(TimeUnit.MILLISECONDS);
            if (milliSecondConsumed > globalBillConfig.getWithDrawSlowLogThreshold()) {
                log.info("CheckBillResultServiceImpl-{} ,accountType {},time:{} , uid {} .......", logSign, accountType != null ? accountType : 0, stopwatch, userId);
            }
            if (milliSecondConsumed > globalBillConfig.getWithDrawSlowLogAlarmThreshold()) {
                getAlarmNotifyService().alarm(PRINT_LOG_FOR_SLOW_INTERFACE_ALARM, logSign, milliSecondConsumed, userId != null ? userId : "", accountType != null ? accountType : "");
            }
            MonitorSummaryUtil.record(logSign, stopwatch.elapsed(TimeUnit.MILLISECONDS));
            stopwatch.reset().start();
        }
    }
}
