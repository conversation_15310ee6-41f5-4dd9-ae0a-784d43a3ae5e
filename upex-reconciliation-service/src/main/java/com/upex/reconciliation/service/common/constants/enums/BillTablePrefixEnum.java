package com.upex.reconciliation.service.common.constants.enums;


/**
 * 对账表前缀枚举类
 * @Date 2022/7/11 4:24 PM
 * <AUTHOR>
 */
public enum BillTablePrefixEnum {
    /**
     * 用户表
     */
    BILL_USER((byte) 1, "用户表","bill_user"),

    /**
     * 币种表
     */
    BILL_COIN_PROPERTY((byte) 2, "币种表","bill_coin_property"),

    /**
     * 币种类型表
     */
    BILL_COIN_TYPE_PROPERTY((byte) 3, "币种类型表","bill_coin_type_property"),

    /**
     * 用户币种类型表
     */
    BILL_COIN_TYPE_USER_PROPERTY((byte) 4, "用户币种类型表","bill_coin_type_user_property"),

    /**
     * 用户币种表
     */
    BILL_COIN_USER_PROPERTY((byte) 5,"用户币种表","bill_coin_user_property"),

    /**
     * 用户币种资产表 目前只有合约在用
     */
    BILL_COIN_USER_ASSETS((byte) 6, "用户币种资产表","bill_coin_user_assets"),

    /**
     * 交易对盈亏表 目前只有53在用
     */
    BILL_SYMBOL_PROPERTY((byte) 7,"交易对盈亏表","bill_symbol_property"),

    /**
     * 交易对类型盈亏表 目前只有53在用
     */
    BILL_SYMBOL_BIZTYPE_PROPERTY((byte) 8,"交易对类型盈亏表","bill_symbol_biztype_property"),

    /**
     * 总账资产币种表-总账使用
     */
    ASSETS_BILL_COIN_PROPERTY((byte) 9,"总账资产币种表","assets_bill_coin_property"),

    /**
     * 总账资产币种类型表-总账使用
     */
    ASSETS_BILL_COIN_TYPE_PROPERTY((byte) 10,"总账资产币种类型表","assets_bill_coin_type_property"),


    /**
     * 用户币种表-快照
     */
    BILL_COIN_USER_PROPERTY_SNAPSHOT((byte) 11,"用户币种快照表","bill_coin_user_property_snapshot"),

    ;

    /**
     * 代码
     */
    private byte code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 表前缀
     */
    private String tablePrefix;

    public static BillTablePrefixEnum toEnum(byte code) {
        for (BillTablePrefixEnum item : BillTablePrefixEnum.values()) {
            if (item.code == code) {
                return item;
            }
        }
        return null;
    }

    public static BillTablePrefixEnum toEnum(String tablePrefix) {
        for (BillTablePrefixEnum item : BillTablePrefixEnum.values()) {
            if (item.tablePrefix.equalsIgnoreCase(tablePrefix)) {
                return item;
            }
        }
        return null;
    }

    public Boolean isUserData(){
        return this == BILL_COIN_TYPE_USER_PROPERTY ||
                this == BILL_COIN_USER_PROPERTY ||
                this == BILL_COIN_USER_ASSETS;
    }

    BillTablePrefixEnum(byte code, String desc, String tablePrefix) {
        this.code = code;
        this.desc = desc;
        this.tablePrefix = tablePrefix;
    }

    public byte getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getTablePrefix() {
        return tablePrefix;
    }
}
