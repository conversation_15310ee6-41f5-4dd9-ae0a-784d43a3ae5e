package com.upex.reconciliation.service.business.convert.remote;

import com.upex.spot.dto.params.bill.QuerySpotBillVO;
import com.upex.spot.dto.result.bill.NewSpotBillInfoResult;
import com.upex.spot.facade.query.SpotBillsQueryClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class SpotBillService {

//    @Resource
//    private SpotBillFeignService spotBillFeignService;

    @Resource
    private SpotBillsQueryClient spotBillsQueryClient;

//    /**
//     * 获取流水订单
//     */
//    public List<NewSpotBillInfoResult> getSpotBill(Long userId, Long orderId) {
//
//        // 调用 SpotBillsQueryClient 获取流水订单
//        QuerySpotBillVO querySpotBillVO = new QuerySpotBillVO();
//        querySpotBillVO.setUserId(userId);
//        querySpotBillVO.setBizOrderId(orderId);
//        List<NewSpotBillInfoResult> resultList = spotBillsQueryClient.getBillByUserIdAndBizOrderId(querySpotBillVO);
//        if (resultList == null || resultList.isEmpty()) {
//            log.warn("No spot bills found for userId: {} and orderId: {}", userId, orderId);
//            return new ArrayList<>();
//        }
//        return resultList;
//    }

    /**
     * 获取流水订单(固定业务类型的方式)
     */
    public List<NewSpotBillInfoResult> getSpotBillByBizType(Long userId, Long orderId,List<String> bizTypeList) {
        // 调用 SpotBillsQueryClient 获取流水订单
        if (CollectionUtils.isEmpty(bizTypeList)) {
            return new ArrayList<>();
        }
        List<QuerySpotBillVO> paramList = new ArrayList<>();
        for (String bizType : bizTypeList) {
            QuerySpotBillVO querySpotBillVO = new QuerySpotBillVO();
            querySpotBillVO.setUserId(userId);
            querySpotBillVO.setBizOrderId(orderId);
            querySpotBillVO.setBizType(Integer.parseInt(bizType));
            paramList.add(querySpotBillVO);
        }
        List<NewSpotBillInfoResult> resultList = spotBillsQueryClient.getBillListByUserIdAndBizTypeAndOrderId(paramList);
        if (resultList == null || resultList.isEmpty()) {
            log.warn("No spot bills found for userId: {} and orderId: {}", userId, orderId);
            return new ArrayList<>();
        }
        return resultList;
    }
}
