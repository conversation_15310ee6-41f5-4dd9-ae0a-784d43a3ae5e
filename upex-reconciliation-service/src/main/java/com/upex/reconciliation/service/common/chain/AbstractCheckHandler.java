package com.upex.reconciliation.service.common.chain;

import org.springframework.stereotype.Component;

/**
 * 
 * @ClassName: AbstractCheckHandler
 * @date 2022/4/29 11:31 AM
 * <AUTHOR>
*/
@Component
public abstract class AbstractCheckHandler {
    protected AbstractCheckHandler next;

    public void setNext(AbstractCheckHandler next) {
        this.next = next;
    }

    /**
     * 执行方法
     * @param userId 用户id
     * @param snapShotTime 特定时间
     */
    public abstract void doHandler(Long userId, Long snapShotTime);

    @Component
    public static class Builder{
        private AbstractCheckHandler head;
        private AbstractCheckHandler tail;
        public Builder addHandler(AbstractCheckHandler handler){
            if (this.head == null){
                this.head = this.tail = handler;
                return this;
            }
            this.tail.setNext(handler);
            this.tail = handler;
            return this;
        }

        public AbstractCheckHandler build(){
            return this.head;
        }
    }
}
