package com.upex.reconciliation.service.common.constants;

import com.upex.mixcontract.common.framework.AttributeMap;
import com.upex.mixcontract.common.framework.event.Event;
import com.upex.reconciliation.service.model.CmdRunEvent;
import io.netty.util.AttributeKey;

public class ReconConstants {

    public static final AttributeKey<AttributeMap> BILL_MAIN_LOGIC_CONTEXT = AttributeKey.valueOf("BILL_MAIN_LOGIC_CONTEXT");


    public static final Event<CmdRunEvent> CMD_BEFORE_EVENT = new Event<>("beforeE", CmdRunEvent.class);


}
