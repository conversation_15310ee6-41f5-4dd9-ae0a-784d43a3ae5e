package com.upex.reconciliation.service.dao.cex.entity;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.ApiKeyPermission;
import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import lombok.Data;

import java.util.Date;
@Data
public class ThirdCexUserConfig {
    private Long id;
    private String cexEmail;
    private String cexUserId;
    private Integer cexType;
    private String userAdminId;
    private String userAdminEmail;
    private String apiKeyLabel;
    private String apiKeyPub;
    @JsonIgnore
    private String apiKeyPrivate; // 已加密存储
    private String apiKey;
    private Integer readOnly;
    private Integer status;
    private String apiPermit;
    private Date createTime;
    private Date updateTime;

    private ApiKeyPermission apiKeyPermission;

    public void setApiPermit(String apiPermit) {
        this.apiPermit = apiPermit;
        this.apiKeyPermission = JSONObject.parseObject(apiPermit, ApiKeyPermission.class);
    }



    // Getter and Setter
}
