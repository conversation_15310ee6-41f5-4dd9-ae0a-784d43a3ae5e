package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.CexAssetConfig;
import com.upex.reconciliation.service.dao.mapper.cex.CexAssetConfigMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CexAssetconfigService {

    @Resource
    CexAssetConfigMapper cexAssetConfigMapper;

    @Resource
    BillDbHelper billDbHelper;

    public CexAssetConfig selectByAssetType(Integer cexType,String cexUserId,Integer assetType) {
        return billDbHelper.doDbOpInReconMaster(() -> cexAssetConfigMapper.selectByAssetType(cexType, cexUserId,assetType));
    }

    public int insert(CexAssetConfig record) {
        return billDbHelper.doDbOpInReconMaster(() -> cexAssetConfigMapper.insert(record));
    }

    public int update(CexAssetConfig record) {
        return billDbHelper.doDbOpInReconMaster(() -> cexAssetConfigMapper.update(record));
    }
}
