package com.upex.reconciliation.service.business.module;

import com.upex.reconciliation.service.model.BillCmdResult;
import com.upex.reconciliation.service.model.BillCmdWrapper;
import com.upex.reconciliation.service.model.MonitorCmdWrapper;

import java.util.concurrent.BlockingQueue;

public abstract class AbstractMonitorProcessor {
    protected BlockingQueue<MonitorCmdWrapper> cmdQueue;


    /**
     * 消费 消息
     * 从队列中拿数据，同时处理业务逻辑
     *
     * @return
     */
    public abstract BillCmdResult takeCommand();


    public abstract void offerCommand(MonitorCmdWrapper cmdWrapper);
}
