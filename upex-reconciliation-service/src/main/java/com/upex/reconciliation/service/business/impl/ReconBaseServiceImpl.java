package com.upex.reconciliation.service.business.impl;

import com.upex.reconciliation.service.business.ReconBaseService;
import com.upex.reconciliation.service.service.AssetsBillConfigService;
import com.upex.reconciliation.service.service.BillAllConfigService;
import com.upex.reconciliation.service.service.BillCoinTypeUserPropertyService;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.AssetsCheckTypeEnum;
import com.upex.reconciliation.service.dao.entity.AssetsBillConfig;
import com.upex.reconciliation.service.dao.entity.BillAllConfig;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.reconciliation.service.utils.SplitTableUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class ReconBaseServiceImpl implements ReconBaseService {


    @Resource
    private BillAllConfigService billAllConfigService;

    @Resource
    private AssetsBillConfigService assetsBillConfigService;

    @Resource
    private BillCoinTypeUserPropertyService billCoinTypeUserPropertyService;

    @Override
    public Date getGlobalTime() {
        // 获取全局对账时间
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        List<AssetsCheckConfig> assetsCheckConfigList = globalBillConfig.getAssetsCheckList();
        AssetsCheckConfig assetsCheckConfig = getByAccountType(assetsCheckConfigList, AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getCode());
        BillAllConfig billConfig = billAllConfigService.getMinCheckOkBillConfig(assetsCheckConfig != null ? assetsCheckConfig.getSubSystemList() : Collections.emptyList());
        if (assetsCheckConfig != null && assetsCheckConfig.isOpen()) {
            AssetsBillConfig assetsBillConfig = assetsBillConfigService.selectByTypeAndParam(AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getCode(), BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM);
            return assetsBillConfig.getCheckOkTime();
        } else {
            return billConfig.getCheckOkTime();
        }
    }

    private AssetsCheckConfig getByAccountType(List<AssetsCheckConfig> assetsCheckConfigList, String accountType) {
        if (CollectionUtils.isNotEmpty(assetsCheckConfigList)) {
            for (AssetsCheckConfig assetsCheckConfig : assetsCheckConfigList) {
                if (Objects.equals(accountType, assetsCheckConfig.getAssetsCheckType())) {
                    return assetsCheckConfig;
                }
            }
        }
        return null;
    }


}
