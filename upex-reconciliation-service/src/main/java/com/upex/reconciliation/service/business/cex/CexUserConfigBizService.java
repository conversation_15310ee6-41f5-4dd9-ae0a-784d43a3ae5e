package com.upex.reconciliation.service.business.cex;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.model.config.ApolloThirdCexAssetConfig;
import com.upex.reconciliation.service.service.CexUserConfigService;
import com.upex.reconciliation.service.service.ThirdCexUserService;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.*;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonApiKeyPermissionRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.user.CexUserConfigInnerRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.user.CexUserConfigListRes;
import com.upex.reconciliation.service.service.client.cex.enmus.ApiKeyStatusEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ReadOnlyEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum;
import com.upex.reconciliation.service.service.client.cex.utils.Ed25519Utils;
import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.utils.task.BaseTask;
import com.upex.utils.task.TaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.upex.reconciliation.service.common.constants.enums.BillRedisKeyEnum.THIRD_CEX_ASSET_CONFIG_KEY;
import static com.upex.reconciliation.service.common.constants.enums.BillRedisKeyEnum.THIRD_CEX_ASSET_SECRET_KEY;

@Slf4j
@Service
public class CexUserConfigBizService {

    @Resource
    CexUserConfigService cexUserConfigService;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    ThirdCexUserService thirdCexUserService;

    @Resource
    CexApiService cexApiService;

    @Resource(name = "thirdCexTaskManager")
    TaskManager taskManager;

    @Resource
    CexApiAggregateBizService cexApiAggregateBizService;

    @Resource
    CexUserBizService thirdCexUserBizService;

    @Resource
    CexParentUserAssetHistoryBizService cexParentUserAssetHistoryBizService;

    @Resource
    BillDbHelper billDbHelper;


    public int setApiKeyMonitor(SetApiKeyMonitorReq setApiKeyMonitorReq) {
        ThirdCexUserConfig thirdCexUserConfig = cexUserConfigService.getEffectiveAssetMonitorConfigByUserId(setApiKeyMonitorReq.getCexType(), setApiKeyMonitorReq.getCexUserId());
        if (thirdCexUserConfig != null) {
            throw new ApiException(ReconCexExceptionEnum.USER_CONFIGUREED_MONITOR_APIKEY);
        }
        ThirdCexUser thirdCexUser = thirdCexUserService.selectByCexTypeAndUserId(setApiKeyMonitorReq.getCexType(), setApiKeyMonitorReq.getCexUserId());
        if (thirdCexUser == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_NOT_EXISTS);
        }
        if (thirdCexUser.getApiKeyStatus().equals(ApiKeyStatusEnum.EFFECT.getType())) {
            throw new ApiException(ReconCexExceptionEnum.USER_CONFIGUREED_MONITOR_APIKEY);
        }
        ThirdCexUserConfig existThirdCexUserConfig = cexUserConfigService.selectById(setApiKeyMonitorReq.getId());
        if (existThirdCexUserConfig == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_APIKEY_CONFIG_NOT_EXISTS);
        }
        CommonRes<CommonApiKeyPermissionRes> permissionres = cexApiService.queryApikeyPermission(new CommonReq(setApiKeyMonitorReq.getCexType(), setApiKeyMonitorReq.getCexUserId(), existThirdCexUserConfig.getApiKey(), existThirdCexUserConfig.getApiKeyPrivate()));
        if (!permissionres.getSuccess()) {
            throw new ApiException(ReconCexExceptionEnum.APIPERMISSION_FAIL);
        }
        if (!permissionres.getData().isReadOnly()) {
            throw new ApiException(ReconCexExceptionEnum.MONITOR_APIKEY_ONLY_READ);
        }
        return updateApiKeyStatus(existThirdCexUserConfig, thirdCexUser, setApiKeyMonitorReq);
    }

    public ThirdCexUserConfig getThirdCexUserConfigByUserId(Integer cexType, String userId) {
        ThirdCexUserConfig thirdCexUserConfig = null;
        Object configObj = redisTemplate.opsForValue().get(String.format(THIRD_CEX_ASSET_CONFIG_KEY.getKey(), cexType, userId));
//        if (configObj != null) {
//            Pair<String, String> secret = JSONObject.parseObject((String) configObj, Pair.class);
//            thirdCexUserConfig = new ThirdCexUserConfig();
//            thirdCexUserConfig.setApiKey(secret.getKey());
//            thirdCexUserConfig.setApiKeyPrivate(secret.getValue());
//            return thirdCexUserConfig;
//        }
        thirdCexUserConfig = cexUserConfigService.getEffectiveAssetMonitorConfigByUserId(cexType, userId);
        if (thirdCexUserConfig == null) {
            //获取母用户config
            ThirdCexUser user = thirdCexUserService.selectByCexTypeAndUserId(cexType, userId);
            if (user == null) {
                throw new ApiException(ReconCexExceptionEnum.USER_NOT_EXISTS);
            }
            ThirdCexUser parentUser = thirdCexUserService.selectByCexTypeAndUserId(cexType, user.getParentUserId());
            if (parentUser == null) {
                throw new ApiException(ReconCexExceptionEnum.USER_NOT_CONFIG_EEFTIVE_APIKEY);
            }
            thirdCexUserConfig = cexUserConfigService.getEffectiveAssetMonitorConfigByUserId(cexType, parentUser.getCexUserId());
            if (thirdCexUserConfig == null) {
                throw new ApiException(ReconCexExceptionEnum.USER_NOT_CONFIG_EEFTIVE_APIKEY);
            }
        }
        ApolloThirdCexAssetConfig config = ReconciliationApolloConfigUtils.getThirdCexAssetConfig();
//        redisTemplate.opsForValue().set(String.format(THIRD_CEX_ASSET_CONFIG_KEY.getKey(), cexType, userId),
//                JSONObject.toJSONString(new Pair<>(thirdCexUserConfig.getApiKey(), thirdCexUserConfig.getApiKeyPrivate())), config.getQuerySecretSave(), TimeUnit.MINUTES);
        return thirdCexUserConfig;
    }

    public CommonRes<CexUserConfigListRes> queryApikeyList(CommonReq commonReq) {
        ThirdCexUser user = thirdCexUserService.selectByCexTypeAndUserId(commonReq.getCexType(), commonReq.getCexUserId());
        if (user == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_NOT_EXISTS);
        }
        List<ThirdCexUserConfig> userConfigs = cexUserConfigService.getConfigsByUserId(commonReq.getCexType(), commonReq.getCexUserId());
        CexUserConfigListRes cexUserConfigListRes = new CexUserConfigListRes();
        List<CexUserConfigInnerRes> configInnerResList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userConfigs)) {
            userConfigs.forEach(userConfig -> {
                CexUserConfigInnerRes configInnerRes = new CexUserConfigInnerRes();
                BeanUtils.copyProperties(userConfig, configInnerRes);
                configInnerResList.add(configInnerRes);
            });
            cexUserConfigListRes.addAll(configInnerResList);
        }
        return CommonRes.getSucApiBaseRes(cexUserConfigListRes);
    }

    public CommonRes<CommonApiKeyPermissionRes>  queryApiPermission(QueryApiKeyPermissionReq request, String userId) throws Exception {
        String secretKey = String.format(THIRD_CEX_ASSET_SECRET_KEY.getKey(), request.getCexType(), request.getCexUserId(), request.getApiKeyLabel(), userId);
        Object secretObj = redisTemplate.opsForValue().get(secretKey);
        if (secretObj == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_SECRET_EXPIRED);
        }
        Pair<String, String> secret = JSONObject.parseObject((String) secretObj, Pair.class);
        if (!Ed25519Utils.bytesToHex(Ed25519Utils.decodePublicKeyFromPEM(secret.getKey()).getEncoded())
                .equals(
                        Ed25519Utils.bytesToHex(Ed25519Utils.decodePublicKeyFromPEM(request.getApiKeyPub()).getEncoded()))) {
            throw new ApiException(ReconCexExceptionEnum.USER_SECRET_NOT_MATCH);
        }
        ThirdCexUser user = thirdCexUserService.selectByCexTypeAndUserId(request.getCexType(), request.getCexUserId());
        if (user == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_NOT_EXISTS);
        }
        CommonRes<CommonApiKeyPermissionRes> permissionres = cexApiService.queryApikeyPermission(new CommonReq(request.getCexType(), request.getCexUserId(), request.getApiKey(), HmacUtil.decrypt(secret.getValue())));
        return permissionres;
    }

    public int addConfig(AddApiKeyReq request, String userId, String userEmail) throws Exception {
        String secretKey = String.format(THIRD_CEX_ASSET_SECRET_KEY.getKey(), request.getCexType(), request.getCexUserId(), request.getApiKeyLabel(), userId);

        Object secretObj = redisTemplate.opsForValue().get(secretKey);
        if (secretObj == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_SECRET_EXPIRED);
        }
        Pair<String, String> secret = JSONObject.parseObject((String) secretObj, Pair.class);
        if (!Ed25519Utils.bytesToHex(Ed25519Utils.decodePublicKeyFromPEM(secret.getKey()).getEncoded())
                .equals(
                        Ed25519Utils.bytesToHex(Ed25519Utils.decodePublicKeyFromPEM(request.getApiKeyPub()).getEncoded()))) {
            throw new ApiException(ReconCexExceptionEnum.USER_SECRET_NOT_MATCH);
        }
        ThirdCexUser user = thirdCexUserService.selectByCexTypeAndUserId(request.getCexType(), request.getCexUserId());
        if (user == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_NOT_EXISTS);
        }

        CommonRes<CommonApiKeyPermissionRes> permissionres = cexApiService.queryApikeyPermission(new CommonReq(request.getCexType(), request.getCexUserId(), request.getApiKey(), HmacUtil.decrypt(secret.getValue())));
        if (!permissionres.getSuccess()) {
            throw new ApiException(ReconCexExceptionEnum.APIPERMISSION_FAIL);
        }
        if (request.getIfAssetMonitor() && !permissionres.getData().isReadOnly()) {
            throw new ApiException(ReconCexExceptionEnum.MONITOR_APIKEY_ONLY_READ);
        }
        List<ThirdCexUserConfig> userConfigs =cexUserConfigService.getConfigsByUserId(request.getCexType(), request.getCexUserId());
        if(CollectionUtils.isNotEmpty(userConfigs)){
          Optional<ThirdCexUserConfig> optional = userConfigs.stream().filter(config -> config.getApiKey().equals(request.getApiKey())).findFirst();
          if(optional.isPresent()){
              throw new ApiException(ReconCexExceptionEnum.APIKEY_EXISTS);
          }
        }
        if (request.getIfAssetMonitor()) {
            ThirdCexUserConfig thirdCexUserConfig = cexUserConfigService.getEffectiveAssetMonitorConfigByUserId(request.getCexType(), request.getCexUserId());
            if (thirdCexUserConfig != null) {
                throw new ApiException(ReconCexExceptionEnum.USER_CONFIGUREED_MONITOR_APIKEY);
            }
        }
        int count = saveUserConfig(request, permissionres, HmacUtil.decrypt(secret.getValue()), userId, userEmail);
        if(request.getIfAssetMonitor()) {
            thirdCexUserBizService.updateUser(permissionres, user, request);
            taskManager.executeBatch(new BaseTask() {
                @Override
                protected void baseRun() throws Exception {
                    thirdCexUserBizService.syncSubUser(request.getCexType(), user.getCexUserId());
                }
            });
            taskManager.executeBatch(new BaseTask() {
                @Override
                protected void baseRun() throws Exception {
                    cexParentUserAssetHistoryBizService.queryUserAssetAndSync(new UserAssetListReq(request.getCexType(), request.getCexUserId(), request.getApiKey(), HmacUtil.decrypt(secret.getValue())));
                }
            });
        }
        return count;
    }

    int saveUserConfig(AddApiKeyReq request, CommonRes permissionres, String privateKey, String userId, String userEmail) {
        ThirdCexUserConfig config = new ThirdCexUserConfig();
        config.setCexEmail(request.getCexEmail());
        config.setCexUserId(String.valueOf(request.getCexUserId()));
        config.setCexType(request.getCexType());
        config.setApiKeyLabel(request.getApiKeyLabel());
        config.setApiKey(request.getApiKey());
        config.setApiKeyPub(request.getApiKeyPub());
        config.setApiKeyPrivate(privateKey);
        config.setApiPermit(JSONObject.toJSONString(permissionres.getData()));
        config.setUserAdminId(userId);
        config.setUserAdminEmail(userEmail);
        config.setReadOnly(request.getIfAssetMonitor() ? ReadOnlyEnum.READ_ONLY.getType() : ReadOnlyEnum.NOT_READ_ONLY.getType());
        if (permissionres.getSuccess()) {
            config.setStatus(ApiKeyStatusEnum.EFFECT.getType());
        } else {
            config.setStatus(ApiKeyStatusEnum.INVALID.getType());
        }
        return cexUserConfigService.addConfig(config);
    }

    public int updateApiKeyStatus(ThirdCexUserConfig userConfig, ThirdCexUser thirdCexUser, SetApiKeyMonitorReq setApiKeyMonitorReq) {
        return billDbHelper.doDbOpInReconMasterTransaction(() -> {
            thirdCexUser.setApiKeyStatus(ApiKeyStatusEnum.EFFECT.getType());
            thirdCexUser.setUpdateTime(new Date());
            thirdCexUserService.update(thirdCexUser);
            cexUserConfigService.updateReadOnly(setApiKeyMonitorReq.getCexType(), setApiKeyMonitorReq.getCexUserId(), ReadOnlyEnum.NOT_READ_ONLY.getType());
            thirdCexUserService.updateSubUserApikeyStatus(thirdCexUser.getCexUserId(), ApiKeyStatusEnum.EFFECT.getType());
            userConfig.setStatus(ApiKeyStatusEnum.EFFECT.getType());
            userConfig.setReadOnly(ReadOnlyEnum.READ_ONLY.getType());
            userConfig.setUpdateTime(new Date());
            return cexUserConfigService.update(userConfig);
        } );
    }
}

