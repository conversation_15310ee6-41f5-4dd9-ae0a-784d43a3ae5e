package com.upex.reconciliation.service.common;

import com.upex.reconciliation.service.common.constants.BaseEnum;
import com.upex.reconciliation.service.common.constants.IntCodeBaseEnum;
import com.upex.reconciliation.service.common.constants.StrCodeBaseEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;

/**
 * 2022/8/11 3:33 PM
 * 通用enum解析器
 */
public class DefaultBaseEnumHandler extends BaseTypeHandler<BaseEnum> {

    private final Class<? extends BaseEnum> type;
    private final BaseEnum[] enums;

    private final Class<? extends BaseEnum> clazz;

    public DefaultBaseEnumHandler(Class<? extends BaseEnum> type) {
        try {
            if (type == null) {
                throw new IllegalArgumentException("Type argument cannot be null");
            }
            Class<?> enumClass = getEnumClass(type);
            if (enumClass.equals(IntCodeBaseEnum.class)) {
                clazz = IntCodeBaseEnum.class;
            } else if (enumClass.equals(StrCodeBaseEnum.class)) {
                clazz = StrCodeBaseEnum.class;
            } else {
                throw new IllegalArgumentException(type.getSimpleName() + " does not have inherit the IntCodeBaseEnum or StrCodeBaseEnum.");
            }
            this.type = type;
            this.enums = type.getEnumConstants();
            if (this.enums == null) {
                throw new IllegalArgumentException(type.getSimpleName() + " does not represent an enum type.");
            }
        } catch (Exception e) {
            // System.out.println();
            throw new RuntimeException(e);
        }
    }

    /**
     * 获得真实的enum类型
     */
    private Class<?> getEnumClass(Class<?> type) {
        if (null == type) {
            return null;
        }
        if (type.getName().equals(BaseEnum.class.getName())) {
            return type;
        }
        Class<?>[] interfaces = type.getInterfaces();
        if (interfaces.length == 0) {
            return null;
        }
        for (Class<?> anInterface : interfaces) {
            Class<?> enumClass = getEnumClass(anInterface);
            if (null != enumClass) {
                if (enumClass.getName().equals(BaseEnum.class.getName())) {
                    return anInterface;
                }
                return enumClass;
            }
        }
        return null;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, BaseEnum parameter, JdbcType jdbcType) throws SQLException {
        if (clazz.equals(IntCodeBaseEnum.class)) {
            ps.setInt(i, parameter.getIntCode());
        } else if (clazz.equals(StrCodeBaseEnum.class)) {
            ps.setString(i, parameter.getStrCode());
        }
    }

    private BaseEnum convertIntToDict(int value) {
        for (BaseEnum anEnum : enums) {
            if (anEnum.getIntCode() == value) {
                return anEnum;
            }
        }
        return null;
    }

    private BaseEnum convertStrToDict(String value) {
        for (BaseEnum anEnum : enums) {
            if (Objects.equals(anEnum.getStrCode(), value)) {
                return anEnum;
            }
        }
        return null;
    }

    @Override
    public BaseEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        if (clazz.equals(IntCodeBaseEnum.class)) {
            return convertIntToDict(rs.getInt(columnName));
        } else if (clazz.equals(StrCodeBaseEnum.class)) {
            return convertStrToDict(rs.getString(columnName));
        } else {
            return null;
        }
    }

    @Override
    public BaseEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        if (clazz.equals(IntCodeBaseEnum.class)) {
            return convertIntToDict(rs.getInt(columnIndex));
        } else if (clazz.equals(StrCodeBaseEnum.class)) {
            return convertStrToDict(rs.getString(columnIndex));
        } else {
            return null;
        }
    }

    @Override
    public BaseEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        if (clazz.equals(IntCodeBaseEnum.class)) {
            return convertIntToDict(cs.getInt(columnIndex));
        } else if (clazz.equals(StrCodeBaseEnum.class)) {
            return convertStrToDict(cs.getString(columnIndex));
        } else {
            return null;
        }
    }

}
