package com.upex.reconciliation.service.utils;


import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanCopier;
import net.sf.cglib.core.ReflectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * 属性拷贝
 * BeanCopier 的原理是通过cglib字节码动态生成类，完成属性拷贝
 *
 * <AUTHOR>
 */
@Slf4j
public class BeanCopierUtil {
    /**
     * BeanCopier缓存
     */
    private volatile static Map<String, BeanCopier> beanCopierCacheMap = new ConcurrentHashMap<>();

    /**
     * 将source对象的属性拷贝到target对象中去
     *
     * @param source source对象
     * @param target target对象
     */
    public static void copyProperties(Object source, Object target) {
        String cacheKey = source.getClass().toString() + target.getClass().toString();

        BeanCopier beanCopier = null;
        if (!beanCopierCacheMap.containsKey(cacheKey)) {
            synchronized (BeanCopierUtil.class) {
                if (!beanCopierCacheMap.containsKey(cacheKey)) {
                    beanCopier = BeanCopier.create(source.getClass(), target.getClass(), false);
                    beanCopierCacheMap.put(cacheKey, beanCopier);
                } else {
                    beanCopier = beanCopierCacheMap.get(cacheKey);
                }
            }
        } else {
            beanCopier = beanCopierCacheMap.get(cacheKey);
        }

        beanCopier.copy(source, target, null);
    }

    /**
     * 将source对象的属性拷贝到target对象中去
     *
     * @param source source对象
     * @param clazz  传类型
     */
    public static <T> T copyProperties(Object source, Class<T> clazz) {
        T object = (T) ReflectUtils.newInstance(clazz);
        copyProperties(source, object);
        return object;
    }

    /**
     * 集合属性赋值
     * 注意：该方法入参需要的是一个线程安全的List
     *
     * @param source
     * @param clazz
     * @return
     */
    public static <T> CopyOnWriteArrayList<T> copyPropertiesList(List<?> source, Class<T> clazz) {
        return Collections.synchronizedList(source).stream().parallel().map(element -> {
            T object = (T) ReflectUtils.newInstance(clazz);
            copyProperties(element, object);
            return object;
        }).collect(Collectors.toCollection(CopyOnWriteArrayList::new));
    }
}
