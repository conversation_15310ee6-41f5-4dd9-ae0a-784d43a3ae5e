package com.upex.reconciliation.service.business.cex;

import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.CexAssetConfig;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserAssetHistory;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.service.ThirdCexUserAssetHistoryService;
import com.upex.reconciliation.service.service.ThirdCexUserService;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.UserAssetListReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.*;
import com.upex.reconciliation.service.service.client.cex.enmus.CexAssetHistoryTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ThirdAssetType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class CexParentUserAssetHistoryBizService extends AbstractCexAssetSyncHistory implements ICexAssetSyncHistory{

    @Resource
    ThirdCexUserAssetHistoryService thirdCexUserAssetHistoryService;

    @Resource
    CexApiAggregateBizService cexApiAggregateBizService;

    @Resource
    BillDbHelper billDbHelper;


    public CommonRes queryUserAssetAndSync(UserAssetListReq userAssetListReq) {
        CommonRes<CommonTotalAssetRes> commonRes = queryTotalCoinAssetFromApi(userAssetListReq);
        log.info("queryUserAssetAndSync start,userId:{},cexType:{}", userAssetListReq.getCexUserId(), userAssetListReq.getCexType());
        return commonRes;
    }
    CommonRes<CommonTotalAssetRes> queryTotalCoinAssetFromApi(UserAssetListReq commonReq) {
        CommonRes<CommonTotalAssetRes> commonRes = serialQueryCoinAssetFromApi(commonReq);
        return commonRes;
    }

    CommonRes<CommonTotalAssetRes> serialQueryCoinAssetFromApi(UserAssetListReq commonReq) {
        CommonTotalAssetRes commonTotalAssetRes = new CommonTotalAssetRes();
        for (ThirdAssetType thirdAssetType : ThirdAssetType.parentUserAssetTypeEnums()) {
            CommonRes<CommonCoinAssetRes> coinAssetRes = cexApiAggregateBizService.queryUserAssetByAssetType(commonReq, thirdAssetType);
            if (coinAssetRes.getSuccess() && coinAssetRes.getData() != null) {
                commonTotalAssetRes.addThirdAssetTypeList(thirdAssetType, coinAssetRes.getData());
            }
            log.info("queryUserAssetByAssetType,userId:{},cexType:{},assetType:{},coinAssetResSuc:{}", commonReq.getCexUserId(), commonReq.getCexType(), thirdAssetType, coinAssetRes.getSuccess());
        }
        return CommonRes.getSucApiBaseRes(commonTotalAssetRes);
    }

    public CommonRes<CommonTotalAssetRes> queryCoinAsset(UserAssetListReq userAssetListReq) {
        List<ThirdCexUserAssetHistory> totalUserAsset = thirdCexUserAssetHistoryService.selectTotalUserAsset(userAssetListReq.getCexUserId(), userAssetListReq.getCexType());
        CommonRes<CommonTotalAssetRes> commonRes = cexApiAggregateBizService.buildCommonTotalAssetRes(totalUserAsset,ThirdAssetType.parentUserAssetTypes(), userAssetListReq);
        return commonRes;
    }

    @Override
    public void syncAssetHistory(CexAssetConfig cexAssetConfig,ThirdCexUserConfig userConfig, Date startTime, Date endTime, Date checkSyncTime) {
        UserAssetListReq userAssetListReq = new UserAssetListReq(userConfig.getCexType(), userConfig.getCexUserId(), userConfig.getApiKey(), userConfig.getApiKeyPrivate(),checkSyncTime);
        CommonRes commonRes=queryUserAssetAndSync(userAssetListReq);
        billDbHelper.doDbOpInReconMaster(()->{
            cexApiAggregateBizService.syncTotalAsset(commonRes, userAssetListReq.getCexType(), userAssetListReq.getCexUserId(),userAssetListReq.getCheckSyncTime());
            log.info("SyncTotalAsset,userId:{},cexType:{},checkSyncTime:{}", userAssetListReq.getCexUserId(), userAssetListReq.getCexType(),userAssetListReq.getCheckSyncTime());
            saveModAssetConfig(cexAssetConfig, CexAssetHistoryTypeEnum.PARENT_BALANCE, userConfig, checkSyncTime);
            return null;
        });
    }

    @Override
    public CexAssetHistoryTypeEnum getAssetHistoryType() {
        return CexAssetHistoryTypeEnum.PARENT_BALANCE;
    }
}
