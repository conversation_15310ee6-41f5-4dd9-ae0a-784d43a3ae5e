package com.upex.reconciliation.service.business;


import com.upex.bill.dto.params.AssetsBaseRequest;
import com.upex.bill.dto.results.AssetsInfoResult;
import com.upex.bill.dto.results.PageResponse;
import com.upex.bill.dto.results.SyncInfoResult;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DES： 资产对账接口--无用户
 */
public interface AssetsService {


    PageResponse<AssetsInfoResult> queryBillInfo(AssetsCheckConfig assetsCheckConfig, AssetsBaseRequest request, Integer pageNo, Integer pageSize, Date nextCheckOkTime, GlobalBillConfig globalBillConfig, Map<Integer, BigDecimal> feeMaps);

    /**
     * 查询截止到对账结束时间所有币的总资产  (增量流水反推))，一定要注意是一个事务
     *
     * @param request
     * @return
     */
    List<AssetsInfoResult> queryAllAssets(AssetsBaseRequest request);


    List<AssetsInfoResult> queryAssets(List<Long> syncIds, AssetsBaseRequest request);


    PageResponse<SyncInfoResult> querySyncInfo(AssetsCheckConfig assetsCheckConfig,AssetsBaseRequest request, Integer pageNo, Integer pageSize);


    List<AssetsInfoResult> queryAllAssetsType(AssetsBaseRequest request);
}
