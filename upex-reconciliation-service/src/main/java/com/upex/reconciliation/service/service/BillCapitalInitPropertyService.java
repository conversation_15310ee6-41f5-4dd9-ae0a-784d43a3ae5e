package com.upex.reconciliation.service.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.enums.CapitalInitBusinessTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillCapitalInitProperty;
import com.upex.reconciliation.service.dao.mapper.BillCapitalInitPropertyMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class BillCapitalInitPropertyService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billCapitalInitPropertyMapper")
    private BillCapitalInitPropertyMapper billCapitalInitPropertyMapper;
    @Resource
    private OldBillContractProfitTransferService oldBillContractProfitTransferService;

    /**
     * 获取初始化数据
     *
     * @param accountType
     * @param accountParam
     * @return
     */
    public List<BillCapitalInitProperty> selectRecords(String accountType, String accountParam, Integer businessType) {
        return dbHelper.doDbOpInReconMaster(() -> billCapitalInitPropertyMapper.selectRecords(accountType, accountParam, businessType));
    }

    /**
     * 获取业务类型初始值
     *
     * @param businessType
     * @return
     */
    public List<BillCapitalInitProperty> selectRecordsByBusinessType(Integer businessType) {
        return dbHelper.doDbOpInReconMaster(() -> billCapitalInitPropertyMapper.selectRecordsByBusinessType(businessType));
    }

    /**
     * 获取初始化数据
     *
     * @param accountType
     * @param accountParam
     * @return
     */
    public List<BillCapitalInitProperty> selectRecords(String accountType, String accountParam, List<Integer> businessTypes) {
        List<BillCapitalInitProperty> resultList = new ArrayList<>();
        for (Integer businessType : businessTypes) {
            resultList.addAll(this.selectRecords(accountType, accountParam, businessType));
        }
        return resultList;
    }

    /**
     * 批量插入
     *
     * @param records
     * @return
     */
    public Integer batchInsert(List<BillCapitalInitProperty> records) {
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }
        return dbHelper.doDbOpInReconMaster(() -> billCapitalInitPropertyMapper.batchInsert(records));
    }

    /**
     * 批量插入
     *
     * @param id
     * @return
     */
    public Integer deleteById(Long id) {
        return dbHelper.doDbOpInReconMaster(() -> billCapitalInitPropertyMapper.deleteById(id));
    }

    /**
     * 根据checkTime删除数据
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date checkTime, Integer businessType) {
        return dbHelper.doDbOpInReconMaster(() -> billCapitalInitPropertyMapper.deleteByCheckTime(accountType, accountParam, checkTime, businessType));
    }

    public Boolean deleteByBusinessType(Byte accountType, String accountParam, Integer businessType) {
        return dbHelper.doDbOpInReconMaster(() -> billCapitalInitPropertyMapper.deleteByBusinessType(accountType, accountParam, businessType));
    }

    /**
     * 初始化现货待冻账数据 查询老bill库 插入新库
     *
     * @param checkOkTime
     */
    public void initSpotInitUnProfitTransfer(Long checkOkTime) {
        Map<Integer, BigDecimal> initUnProfitTransferMap = oldBillContractProfitTransferService.sumInitUnProfitTransfers(AccountTypeEnum.SPOT.getCode(), AccountTypeEnum.SPOT.getAccountParam(), new Date(checkOkTime));
        log.info("BillCapitalInitPropertyService.initSpotInitUnProfitTransfer checkOkTime:{} data:{}", checkOkTime, JSON.toJSONString(initUnProfitTransferMap));
        if (initUnProfitTransferMap != null && !initUnProfitTransferMap.isEmpty()) {
            List<BillCapitalInitProperty> insertList = new ArrayList<>();
            Date nowDate = new Date();
            for (Map.Entry<Integer, BigDecimal> entry : initUnProfitTransferMap.entrySet()) {
                BillCapitalInitProperty capitalInitProperty = new BillCapitalInitProperty();
                capitalInitProperty.setAccountType(String.valueOf(AccountTypeEnum.SPOT.getCode()));
                capitalInitProperty.setAccountParam(String.valueOf(AccountTypeEnum.SPOT.getAccountParam()));
                capitalInitProperty.setBusinessType(CapitalInitBusinessTypeEnum.SPOT_UN_PROFIT_TRANSFER.getCode());
                capitalInitProperty.setBusinessTypeName(CapitalInitBusinessTypeEnum.SPOT_UN_PROFIT_TRANSFER.getDesc());
                capitalInitProperty.setInitValue(entry.getValue());
                capitalInitProperty.setCoinId(entry.getKey());
                capitalInitProperty.setSymbolId("");
                capitalInitProperty.setCreateTime(nowDate);
                capitalInitProperty.setUpdateTime(nowDate);
                insertList.add(capitalInitProperty);
            }
            if (CollectionUtils.isNotEmpty(insertList)) {
                batchInsert(insertList);
            }
        }
    }

    /**
     * 修复期初数据
     *
     * @param jobParam
     */
    public void repairCapitalInitProperty(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        log.info("repairCapitalInitProperty data:{}", jobParam);
        String action = jsonObject.getString("action");
        JSONArray delIds = jsonObject.getJSONArray("delIds");
        JSONArray insertList = jsonObject.getJSONArray("insertList");
        if ("delete".equals(action)) {
            if (delIds == null || delIds.size() == 0) {
                return;
            }
            for (int i = 0; i < delIds.size(); i++) {
                deleteById(delIds.getLong(i));
            }
        } else if ("insert".equals(action)) {
            if (insertList == null || insertList.size() == 0) {
                return;
            }
            List<BillCapitalInitProperty> capitalInitPropertyList = Convert.toList(BillCapitalInitProperty.class, insertList);
            if (CollectionUtils.isNotEmpty(capitalInitPropertyList)) {
                batchInsert(capitalInitPropertyList);
            }
        }
    }
}