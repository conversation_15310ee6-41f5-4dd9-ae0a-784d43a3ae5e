package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.mapper.OldBillCoinUserPropertyMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;


@Service
public class OldBillCoinUserService {


    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "oldBillCoinUserPropertyMapper")
    private OldBillCoinUserPropertyMapper oldBillCoinUserPropertyMapper;


    public BillCoinUserProperty selectRangeCheckTimeRecord(Date checkTime, Long uid, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInBillMaster(() -> oldBillCoinUserPropertyMapper.selectRange(checkTime, uid, accountType, accountParam));
    }


    public List<BillCoinUserProperty> selectByUserId(Long userId, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInBillMaster(() -> oldBillCoinUserPropertyMapper.selectByUserId(userId, accountType, accountParam));
    }


    public List<BillCoinUserProperty> selectAssetListByUserId(Long userId, Date checkTime, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInBillMaster(() -> oldBillCoinUserPropertyMapper.selectAssetListByUserId(userId, checkTime, accountType, accountParam));

    }


    public List<BillCoinUserProperty> selectByCheckTime(Date checkTime, Byte accountType, String accountParam) {
        List<BillCoinUserProperty> list = dbHelper.doDbOpInBillMaster(() -> oldBillCoinUserPropertyMapper.selectByCheckTime(checkTime, accountType, accountParam));
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        return Collections.emptyList();

    }

}
