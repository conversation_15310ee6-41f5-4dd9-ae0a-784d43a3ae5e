package com.upex.reconciliation.service.consumer.decoder;

import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.google.common.collect.Sets;
import com.upex.config.coin.SpotCoinDTO;
import com.upex.config.facade.coin.CoinConfigService;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.ReconKafkaOpsConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class LeverDecoder extends AbstractMessageDecoder {
    @Resource
    private CoinConfigService coinConfigService;
    private Set<String> feeTypeList = Sets.newHashSet("15", "41", "21");

    @Override
    public List<CommonBillChangeData> doMessageDecode(List<Map<String, String>> dataList, FlatMessage flatMessage, Integer partition, Long offset, Byte accountType) {
        List<CommonBillChangeData> commonBillChangeDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Map<String, String> map : dataList) {
                SpotCoinDTO spotCoinDTO = coinConfigService.getCoinBaseInfoByCoinName(map.get("token_id")).get();
                if (spotCoinDTO == null) {
                    log.info("LeverDecoder.messageDecode data:{}", JSON.toJSONString(map));
                    continue;
                }
                Long billId = Long.valueOf(map.get("id"));
                CommonBillChangeData commonBillChangeData = new CommonBillChangeData();
                commonBillChangeData.setAccountType(accountType);
                commonBillChangeData.setPartition(partition);
                commonBillChangeData.setOffset(offset);
                commonBillChangeData.setBizId(billId);
                commonBillChangeData.setOrderId(map.get("biz_id"));
                commonBillChangeData.setSymbolId(map.get("symbol_code"));
                commonBillChangeData.setCoinId(spotCoinDTO.getCoinId());
                commonBillChangeData.setAccountId(Long.parseLong(map.get("account_id")));
                commonBillChangeData.setBizType(map.get("biz_type"));
                commonBillChangeData.setBizTime(DateUtil.getMillisecondDate(map.get("biz_time")));
                commonBillChangeData.setCreateTime(DateUtil.getMillisecondDate(map.get("create_time")));
                commonBillChangeData.setBizTimeFromId(DateUtil.getMillisecondDate(map.get("create_time")));
                BigDecimal freeChange = new BigDecimal(map.get("free_change"));
                BigDecimal lockedChange = new BigDecimal(map.get("locked_change"));
                BigDecimal borrowedChange = new BigDecimal(map.get("borrowed_change"));
                BigDecimal freeBefore = new BigDecimal(map.get("free_before"));
                BigDecimal lockedBefore = new BigDecimal(map.get("locked_before"));
                BigDecimal borrowedBefore = new BigDecimal(map.get("borrowed_before"));
                BigDecimal freeAfter = freeBefore.add(freeChange);
                BigDecimal lockedAfter = lockedBefore.add(lockedChange);
                BigDecimal borrowedAfter = borrowedBefore.add(borrowedChange);
                commonBillChangeData.setChangeProp1(freeChange);
                commonBillChangeData.setChangeProp2(lockedChange);
                commonBillChangeData.setChangeProp3(borrowedChange);
                commonBillChangeData.setChangeProp3(borrowedChange);
                commonBillChangeData.setProp1(freeAfter);
                commonBillChangeData.setProp2(lockedAfter);
                commonBillChangeData.setProp3(borrowedAfter);
                long bizTimeFromId = SerialNoGenerator.getTimeById(billId);
                commonBillChangeData.setBizTimeFromId(new Date(bizTimeFromId));
                if (this.feeTypeList.contains(commonBillChangeData.getBizType())) {
                    commonBillChangeData.setChangeFee(freeChange);
                }
                commonBillChangeDataList.add(commonBillChangeData);

                ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
                ReconKafkaOpsConfig reconKafkaOpsConfig = apolloBizConfig.getReconKafkaOpsConfig();
                if (Math.abs(commonBillChangeData.getBizTime().getTime() - bizTimeFromId) > reconKafkaOpsConfig.getKafkaMessageBizTimeAndCreateTimeGapThreshold()) {
                    // biz time和create time偏差过大，一般是延迟入账导致，需关注
                    log.error("LeverDecoder.messageDecode message decode error, bizTimeFromId and createDate gap too large  billId {} ,cate date {} , bizTimeFromId {}",
                            commonBillChangeData.getBizId(), commonBillChangeData.getBizTime().getTime(), bizTimeFromId);
                }
            }
        }
        return commonBillChangeDataList;
    }
}
