package com.upex.reconciliation.service.business.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.Cache;
import com.upex.commons.support.exception.ApiException;
import com.upex.config.coin.CoinChainDTO;
import com.upex.config.coin.SpotCoinChainDTO;
import com.upex.config.coin.SpotCoinDTO;
import com.upex.config.coin.SpotCoinPricePartitionDTO;
import com.upex.config.common.SpotCoinStatusEnum;
import com.upex.config.facade.CoinChainUtil;
import com.upex.config.facade.coin.CoinConfigService;
import com.upex.config.facade.coin.inner.SpotCoinPriceFacade;
import com.upex.config.facade.onChainCoin.inner.OnChainCoinPriceFacade;
import com.upex.config.facade.swap.SwapTokenConfigService;
import com.upex.config.onChainCoin.OnChainCoinPricePartitionDTO;
import com.upex.config.swap.SwapTokenConfigDTO;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.enums.ApolloKeyEnum;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.model.config.ApolloCommonServiceConfig;
import com.upex.reconciliation.service.utils.CacheUtils;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.NumberUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.ticker.facade.TickerSpotPriceFeignClientWrapper;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.ticker.facade.enums.RightCoinTypeEnum;
import com.upex.user.dto.UserLoginListDTO;
import com.upex.user.facade.SystemUserFeignClient;
import com.upex.utils.log.AlarmUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.util.Strings;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.common.constants.BillConstants.SEPARATOR;


/**
 * <AUTHOR>
 * @Date 2021/10/8 上午10:06
 * @Description
 */
@Slf4j
@Service
public class CommonServiceImpl implements CommonService {


    @Resource
    private TickerSpotPriceFeignClientWrapper tickerSpotPriceFeignClientWrapper;

    @Resource
    private SwapTokenConfigService swapTokenConfigService;

    @Resource
    private CoinConfigService coinConfigService;

    @Resource
    private SystemUserFeignClient systemUserFeignClient;

    @Resource
    private OnChainCoinPriceFacade onChainCoinPriceFacade;

    /**
     * 所有币种信息map
     */
    private Cache<Long, Map<Integer, String>> allCoinInfoCache = CacheUtils.getNewCache(10, 5, TimeUnit.MINUTES);

    /**
     * 所有系统用户缓存
     */
    private Cache<Long, Map<Long, UserLoginListDTO>> allSystemUserCache = CacheUtils.getNewCache(10, 5, TimeUnit.MINUTES);

    /**
     * 所有币种汇率map
     */
    private Cache<Long, Map<Integer, PriceVo>> allPriceCache = CacheUtils.getNewCache(10, 5, TimeUnit.MINUTES);
    /**
     * 所有币种指定快照时间汇率map
     */
    private Cache<Long, Map<Integer, PriceVo>> allSnapshotPriceCache = CacheUtils.getNewCache(10, 5, TimeUnit.MINUTES);
    /**
     * 所有币种指定快照时间汇率map
     */
    private Cache<Long, Map<Integer, PriceVo>> allSnapshotPriceCacheNew = CacheUtils.getNewCache(10, 5, TimeUnit.MINUTES);
    /**
     * 所有币种指定快照时间汇率map
     */
    private Cache<Long, Map<String, PriceVo>> allSnapshotCoinPriceCache = CacheUtils.getNewCache(10, 5, TimeUnit.MINUTES);

    /**
     * 所有系统用户信息map
     */
    private Cache<Long, Map<Long, UserLoginListDTO>> allSysUserMapCache = CacheUtils.getNewCache(1, 1, TimeUnit.DAYS);


    @Override
    public Map<Integer, String> getChainInfoMap() {
        List<CoinChainDTO> coinChainDTOS = CoinChainUtil.getAllCoinChain();
        return coinChainDTOS.stream().collect(Collectors.toMap(CoinChainDTO::getChainCoinId, CoinChainDTO::getDesc, (k1, k2) -> k1));
    }


    @Override
    public Map<Integer, String> getAllCoinsMap(AccountTypeEnum accountType) {
        return accountType.isWallet() ? getAllWalletCoinsMap() : getAllCoinsMap();
    }

    @Override
    public Map<Integer, PriceVo> getCoinIdTradePriceMap(Long snapshotTime) {
        Map<Integer, PriceVo> resultMap = new HashMap<>();
        try {
            List<SpotCoinPricePartitionDTO> spotCoinPriceByDataTime = spotCoinPriceFacade.getSpotCoinPriceByDataTime(snapshotTime);
            if (CollectionUtils.isEmpty(spotCoinPriceByDataTime)) {
                log.error("spotCoinPriceFacade coinId getRatesMap is empty, param:{}", snapshotTime);
                return resultMap;
            }
            // 默认开关true，兼容其他站点
            ApolloCommonServiceConfig apolloConfig = ReconciliationApolloConfigUtils.getApolloObjectConfig(ApolloKeyEnum.COMMON_SERVICE_APOLLO_CONFIG, ApolloCommonServiceConfig.class);
            if (apolloConfig.getQueryTradePriceOpen()) {
                return spotCoinPriceByDataTime.stream().collect(Collectors.toMap(SpotCoinPricePartitionDTO::getCoinId, e -> new PriceVo(1, new BigDecimal(e.getTradePrice()), snapshotTime)));
            } else {
                return spotCoinPriceByDataTime.stream().collect(Collectors.toMap(SpotCoinPricePartitionDTO::getCoinId, e -> new PriceVo(1, new BigDecimal(e.getCoinPrice()), snapshotTime)));
            }
        } catch (Exception e) {
            log.error("getRatesToUSDTCoinNameMap execute error, exception:", e);
        }
        return resultMap;
    }


    private Map<Integer, String> getAllWalletCoinsMap() {
        Map<Integer, String> result = new ConcurrentHashMap<>();

        Map<Integer, SpotCoinChainDTO> allCoinsMap = getAllWalletCoinInfoList();
        Map<Integer, String> chainInfoMap = getChainInfoMap();

        for (Map.Entry<Integer, SpotCoinChainDTO> entry : allCoinsMap.entrySet()) {
            SpotCoinChainDTO spotCoinChainDTO = entry.getValue();
            result.put(entry.getKey(), getRealCoinName(spotCoinChainDTO.getCoinName(),
                    chainInfoMap.get(spotCoinChainDTO.getChainCoinId())));
        }

        return result;
    }

    /**
     * 获取系统用户集合
     *
     * @param userId
     * <AUTHOR>
     * @date 2023/3/2 21:04
     */
    @Override
    public boolean isSysUser(Long userId) {
        return getAllSysUserMapCache(DateUtil.getZeroOClick(new Date()).getTime()).containsKey(userId);
    }

    @Override
    public Map<Long, UserLoginListDTO> getAllSysUserMapCache(Long snapShotTime) {
        return CacheUtils.computeIfAbsent(allSysUserMapCache, snapShotTime, this::getSystemUserInfoMap);

    }

    @Override
    public Map<Long, UserLoginListDTO> getSystemUserInfoMap() {
        List<UserLoginListDTO> allSystemAccountList = systemUserFeignClient.getAllSystemAccountList();
        return allSystemAccountList.stream().collect(Collectors.toMap(UserLoginListDTO::getUserId, Function.identity(), (k1, k2) -> k1));
    }


    @Override
    public Map<Integer, SpotCoinChainDTO> getAllWalletCoinInfoList() {
        List<SpotCoinChainDTO> coinChains = coinConfigService.getCoinChains();
        return coinChains.stream()
                .collect(Collectors.toMap(spot -> spot.getId().intValue(), java.util.function.Function.identity()));
    }

    @Override
    public String getRealCoinName(String coinName, String chainName) {
        return StringUtils.isNotEmpty(coinName) ? Strings.toUpperCase(StringUtils.isNotEmpty(chainName) ?
                coinName.concat(SEPARATOR).concat(chainName) : coinName) : "";
    }

    @Override
    public Map<Integer, PriceVo> getRatesToUSDTCoinIdMap(AccountTypeEnum accountTypeEnum) {
        // 返回coinId和汇率关系
        Map<Integer, PriceVo> resultMap = new ConcurrentHashMap<>();

        // 获取所有的币种名称Map
        Map<Integer, String> allCoinsMap = this.getAllCoinsMap(accountTypeEnum);
        // 获取所有币种的汇率
        Map<String, PriceVo> ratesToUSDTMap = this.getRatesToUSDT();

        // 打印币种信息和币种汇率
        if (ReconciliationApolloConfigUtils.getGlobalBillConfig().isShowCoinInfoAndRatesLog()) {
            log.info("allCoinsMap={}", JSONObject.toJSONString(allCoinsMap));
            log.info("ratesToUSDTMap={}", JSONObject.toJSONString(ratesToUSDTMap));
        }


        for (Map.Entry<Integer, String> entry : allCoinsMap.entrySet()) {
            if (StringUtils.isBlank(entry.getValue()) || StringUtils.isBlank(entry.getValue())) {
                continue;
            }
            String leftCoinName = entry.getValue();
            Integer coinId = entry.getKey();
            if (accountTypeEnum.isWallet()) {
                leftCoinName = leftCoinName.split(SEPARATOR)[0];
            }
            String coinName = (leftCoinName + BillConstants.UNDERSCORE_SEPARATOR + RightCoinTypeEnum.USDT).toLowerCase();
            PriceVo priceVo = getPriceVo(ratesToUSDTMap, coinName);
            resultMap.put(coinId, priceVo);
        }
        return resultMap;
    }


    private PriceVo getPriceVo(Map<String, PriceVo> ratesToUSDTMap, String coinName) {
        PriceVo priceVo = ratesToUSDTMap.get(coinName);
        if (ObjectUtil.isEmpty(priceVo)) {
            priceVo = new PriceVo();
            priceVo.setPrice(BigDecimal.ZERO);
        }
        return priceVo;
    }


    @Override
    public Map<String, PriceVo> getRatesToUSDT() {
        Map<String, PriceVo> resultMap = new ConcurrentHashMap<>();

        // 获取所有现货价格
        Map<String, PriceVo> allSpotPrices = tickerSpotPriceFeignClientWrapper.getSpotPrices(RightCoinTypeEnum.USDT);
        Map<Integer, String> allChainMap = getChainInfoMap();
        List<SwapTokenConfigDTO> responseSwapTokens = swapTokenConfigService.getAllTokens();
        Map<String, PriceVo> allSwapPrices = responseSwapTokens.stream()
                .collect(Collectors.toMap(
                        swap -> (swap.getSymbol() + SEPARATOR + allChainMap.get(swap.getSwapChainId()) + SEPARATOR + swap.getSwapContractAddress() + BillConstants.UNDERSCORE_SEPARATOR + RightCoinTypeEnum.USDT).toLowerCase(),
                        swap -> {
                            PriceVo priceVo = new PriceVo();
                            priceVo.setPrice(ObjectUtil.isEmpty(swap.getPrice()) ? BigDecimal.ZERO : swap.getPrice());
                            return priceVo;
                        }, (k1, k2) -> k1));

        resultMap.putAll(allSpotPrices);
        resultMap.putAll(allSwapPrices);

        return resultMap;
    }

    @Override
    public BigDecimal checkRateByCoinIdAndReturnUSDT(Integer coinId, Map<String, PriceVo> rates) {
        String leftCoinName = getAvailableCoinName(coinId);
        String coinName = (leftCoinName + BillConstants.UNDERSCORE_SEPARATOR + RightCoinTypeEnum.USDT).toLowerCase();
        if (rates == null) {
            log.info("retry to get all rates!");
            rates = getRatesToUSDT();
        }
        if (rates.containsKey(coinName)) {
            return rates.get(coinName).getPrice();
        }
        log.error("this coinId:{}, coinName:{} rate map does not have it and return rate=1!", coinId, coinName);
        //AlarmUtils.error("this coinId:{}, coinName:{} rate map does not have it and return rate=1!",coinId,coinName);
        return BigDecimal.ONE;
    }

    @Override
    public String getAvailableCoinName(Integer coinId) {
        if (coinId == null) {
            return null;
        }
        Optional<SpotCoinDTO> spotCoinDTO = coinConfigService.getCoinOptional(coinId, SpotCoinStatusEnum.AVAILABLE.getCode());
        if (Objects.nonNull(spotCoinDTO) && !spotCoinDTO.equals(Optional.empty())) {
            String coinName = spotCoinDTO.map(SpotCoinDTO::getCoinName).orElseGet(null);
            if (Objects.nonNull(coinName)) {
                return coinName;
            }
        }
        return null;
    }


    @Override
    public Integer getCoinIdByName(String coinName) {
        Optional<SpotCoinDTO> optional = coinConfigService.getCoinByCoinNameFromAll(coinName);
        return optional.orElse(new SpotCoinDTO()).getCoinId();
    }


    //
//    @Resource
//    private CoinConfigService coinConfigService;
//    @Resource
//    private TickerSpotPriceFeignClientWrapper tickerSpotPriceFeignClientWrapper;
//    @Resource
//    private SystemUserFeignClient systemUserFeignClient;
//    @Resource
//    private SwapTokenConfigService swapTokenConfigService;
    @Resource
    private SpotCoinPriceFacade spotCoinPriceFacade;

    //
    @Override
    public String getCoinName(Integer coinId) {
        return coinConfigService.getCoinOptional(coinId, SpotCoinStatusEnum.ALL.getCode())
                .map(SpotCoinDTO::getCoinName)
                .orElseGet(() -> {
                    //log.error("this coin id = {} and have no matched name !",coinId);
                    return null;
                });
    }

    //
//    @Override
//    public String getAvailableCoinName(Integer coinId) {
//        if (coinId == null) {
//            return null;
//        }
//        Optional<SpotCoinDTO> spotCoinDTO = coinConfigService.getCoinOptional(coinId, SpotCoinStatusEnum.AVAILABLE.getCode());
//        if (Objects.nonNull(spotCoinDTO) && !spotCoinDTO.equals(Optional.empty())) {
//            String coinName = spotCoinDTO.map(SpotCoinDTO::getCoinName).orElseGet(null);
//            if (Objects.nonNull(coinName)) {
//                return coinName;
//            }
//        }
//        return null;
//    }
//
//    @Override
//    public BigDecimal checkRateByCoinNameAndReturnUSDT(String leftCoinName, Map<String, PriceVo> rates) {
//        if (rates == null || StringUtils.isBlank(leftCoinName)) {
//            log.error("checkRateByCoinNameAndReturnUSDT rates is null error");
//            return BigDecimal.ZERO;
//        }
//
//        String coinName = (leftCoinName + UNDERSCORE_SEPARATOR + RightCoinTypeEnum.USDT).toLowerCase();
//        PriceVo priceVo = rates.getOrDefault(coinName, new PriceVo(BillConstants.ONE,BigDecimal.ZERO, System.currentTimeMillis()));
//        return NumberUtil.isNullDefaultZero(priceVo.getPrice());
//    }
//
//    @Override
//    public BigDecimal checkRateByCoinNameAndReturnUSDT(String leftCoinName, Map<String, PriceVo> rates, TotalAssetsEnum assetsEnum) {
//        if (assetsEnum.isWallet()){
//            leftCoinName = leftCoinName.split(BillConstants.SEPARATOR)[0];
//        }
//        return checkRateByCoinNameAndReturnUSDT(leftCoinName, rates);
//    }
//
//    @Override
//    public BigDecimal checkRateByCoinIdAndReturnUSDT(Integer coinId, Map<String, PriceVo> rates) {
//        String leftCoinName = getAvailableCoinName(coinId);
//        String coinName = (leftCoinName + UNDERSCORE_SEPARATOR + RightCoinTypeEnum.USDT).toLowerCase();
//        if (rates == null) {
//            log.info("retry to get all rates!");
//            rates = getRatesToUSDT();
//        }
//        if (rates.containsKey(coinName)) {
//            return rates.get(coinName).getPrice();
//        }
//        log.error("this coinId:{}, coinName:{} rate map does not have it and return rate=1!",coinId,coinName);
//        AlarmUtils.error("this coinId:{}, coinName:{} rate map does not have it and return rate=1!",coinId,coinName);
//        return  BigDecimal.ONE;
//    }
//
//    /**
//     * 所有币种汇率
//     * @param coinId
//     * @param rates
//     * @return
//     */
//    @Override
//    public BigDecimal checkRateByAllCoinIdAndReturnUSDT(Integer coinId, Map<String, PriceVo> rates) {
//        String leftCoinName = getCoinName(coinId);
//        String coinName = (leftCoinName + UNDERSCORE_SEPARATOR + RightCoinTypeEnum.USDT).toLowerCase();
//        if (rates == null) {
//            log.info("retry to get all rates!");
//            // rates = getRatesToUSDT();
//            return  BigDecimal.ONE;
//        }
//        if (rates.containsKey(coinName)) {
//            return rates.get(coinName).getPrice();
//        }
//        log.warn("this coinId:{}, coinName:{} rate map does not have it and return rate=1!",coinId,coinName);
//        // AlarmUtils.error("this coinId:{}, coinName:{} rate map does not have it and return rate=1!",coinId,coinName);
//        return  BigDecimal.ONE;
//    }
//
    @Override
    public BigDecimal checkRateBySwapTokenIdReturnUSDT(Integer coinId, Map<Integer, PriceVo> rates) {
        if (rates == null) {
            log.error("rates is null error");
            return BigDecimal.ZERO;
        }
        if (rates.containsKey(coinId)) {
            return NumberUtil.isNullDefaultZero(rates.get(coinId).getPrice());
        }
        log.error("this coinId:{}, rate map does not have it and return rate=0!", coinId);
        return BigDecimal.ZERO;
    }


    @Override
    public List<Long> getSystemUserIds() {
        List<UserLoginListDTO> allSystemAccountList = systemUserFeignClient.getAllSystemAccountList();
        return allSystemAccountList.stream().map(UserLoginListDTO::getUserId).collect(Collectors.toList());
    }

    //
//
//    @Override
//    public List<Long> getSystemUserIdsWithoutAbandon() {
//        List<String> systemAssetsExcludeUserTypeLists = ApolloBillConfigUtil.getAssetStatisticsConfig().getSystemAssetsExcludeUserTypeLists();
//        List<UserLoginListDTO> allSystemAccountList = systemUserFeignClient.getAllSystemAccountList();
//        return allSystemAccountList.stream().
//                filter(dto -> !systemAssetsExcludeUserTypeLists.contains(dto.getUserType())).
//                map(UserLoginListDTO::getUserId).collect(Collectors.toList());
//    }
//
//    @Override
//    public List<Long> getSystemUserIdsAbandon() {
//        List<String> platformAssetsLists = ApolloBillConfigUtil.getAssetStatisticsConfig().getPlatformAssetsExcludeUserTypeLists();
//        List<UserLoginListDTO> allSystemAccountList = systemUserFeignClient.getAllSystemAccountList();
//        return allSystemAccountList.stream().
//                filter(dto -> platformAssetsLists.contains(dto.getUserType())).
//                map(UserLoginListDTO::getUserId).collect(Collectors.toList());
//    }
//
//    @Override
//    public List<Long> getSystemUserIds() {
//        List<UserLoginListDTO> allSystemAccountList = systemUserFeignClient.getAllSystemAccountList();
//        return allSystemAccountList.stream().map(UserLoginListDTO::getUserId).collect(Collectors.toList());
//    }
//
//    @Override
//    public Map<Long,UserLoginListDTO> getSystemUserInfoMap(){
//        List<UserLoginListDTO> allSystemAccountList = systemUserFeignClient.getAllSystemAccountList();
//        return allSystemAccountList.stream().collect(Collectors.toMap(UserLoginListDTO::getUserId, Function.identity(), (k1, k2) -> k1));
//    }
//
//    @Override
//    public Map<Long,UserLoginListDTO> getSystemUserInfoMapCache(Long snapshotTime){
//        return CacheUtils.computeIfAbsent(allSystemUserCache, snapshotTime, this::getSystemUserInfoMap);
//    }
//
//    @Override
//    public Map<String, PriceVo> getRatesToUSDT() {
//        Map<String, PriceVo> resultMap = new ConcurrentHashMap<>();
//
//        // 获取所有现货价格
//        Map<String, PriceVo> allSpotPrices = tickerSpotPriceFeignClientWrapper.getSpotPrices(RightCoinTypeEnum.USDT);
//
////        获取swap所有币种汇率，没有的按0计算
//        Map<Integer, String> allChainMap = getChainInfoMap();
//        List<SwapTokenConfigDTO> responseSwapTokens = swapTokenConfigService.getAllTokens();
//        Map<String, PriceVo> allSwapPrices = responseSwapTokens.stream()
//                .collect(Collectors.toMap(
//                        swap -> (swap.getSymbol() + SEPARATOR + allChainMap.get(swap.getSwapChainId()) + SEPARATOR + swap.getSwapContractAddress() + UNDERSCORE_SEPARATOR + RightCoinTypeEnum.USDT).toLowerCase(),
//                        swap -> {
//                            PriceVo priceVo = new PriceVo();
//                            priceVo.setPrice(ObjectUtil.isEmpty(swap.getPrice()) ? BigDecimal.ZERO : swap.getPrice());
//                            return priceVo;
//                        }, (k1, k2) -> k1));
//
//        resultMap.putAll(allSpotPrices);
//        resultMap.putAll(allSwapPrices);
//
//        return resultMap;
//    }
//
//    @Override
//    public Map<Integer,PriceVo> getRatesToUSDTCoinIdMap(AccountTypeEnum accountTypeEnum){
//        // 返回coinId和汇率关系
//        Map<Integer,PriceVo> resultMap = new ConcurrentHashMap<>();
//
//        // 获取所有的币种名称Map
//        Map<Integer, String> allCoinsMap = this.getAllCoinsMap(accountTypeEnum);
//        // 获取所有币种的汇率
//        Map<String, PriceVo> ratesToUSDTMap = this.getRatesToUSDT();
//
//        // 打印币种信息和币种汇率
//        if (ApolloBillConfigUtil.getGlobalBillConfig().isShowCoinInfoAndRatesLog()) {
//            log.info("allCoinsMap={}", JSONObject.toJSONString(allCoinsMap));
//            log.info("ratesToUSDTMap={}", JSONObject.toJSONString(ratesToUSDTMap));
//        }
//
//
//        for (Map.Entry<Integer, String> entry : allCoinsMap.entrySet()) {
//            if(StringUtils.isBlank(entry.getValue()) || StringUtils.isBlank(entry.getValue())){
//                continue;
//            }
//            String leftCoinName = entry.getValue();
//            Integer coinId = entry.getKey();
//            if (accountTypeEnum.isWallet()) {
//                leftCoinName = leftCoinName.split(SEPARATOR)[0];
//            }
//            String coinName = (leftCoinName + UNDERSCORE_SEPARATOR + RightCoinTypeEnum.USDT).toLowerCase();
//            PriceVo priceVo = getPriceVo(ratesToUSDTMap, coinName);
//            resultMap.put(coinId, priceVo);
//        }
//        return resultMap;
//    }
//
//    @Override
//    public Map<String, BigDecimal> getCoinNameRatesMap(Long snapshotTime){
//        Map<String, BigDecimal> resultMap = new HashMap<>();
//        try {
//            List<SpotCoinPricePartitionDTO> spotCoinPriceByDataTime = spotCoinPriceFacade.getSpotCoinPriceByDataTime(snapshotTime);
//            if (CollectionUtils.isEmpty(spotCoinPriceByDataTime)) {
//                log.error("spotCoinPriceFacade coinName getRatesMap is empty, param:{}", snapshotTime);
//                return resultMap;
//            }
//            return spotCoinPriceByDataTime.stream().collect(Collectors.toMap(SpotCoinPricePartitionDTO::getCoinName, e -> new BigDecimal(e.getCoinPrice())));
//        }catch (Exception e) {
//            log.error("getRatesToUSDTCoinNameMap execute error, exception:", e);
//        }
//        return resultMap;
//    }
//
//    @Override
//    public Map<String, PriceVo> getCoinNameRatesPriceMap(Long snapshotTime){
//        Map<String, PriceVo> resultMap = new HashMap<>();
//        try {
//            List<SpotCoinPricePartitionDTO> spotCoinPriceByDataTime = spotCoinPriceFacade.getSpotCoinPriceByDataTime(snapshotTime);
//            if (CollectionUtils.isEmpty(spotCoinPriceByDataTime)) {
//                log.error("spotCoinPriceFacade coinName getRatesMap is empty, param:{}", snapshotTime);
//                return resultMap;
//            }
//
//            return spotCoinPriceByDataTime.stream().collect(Collectors.toMap(e -> getCoinName(e.getCoinName()), e -> new PriceVo(1, new BigDecimal(e.getCoinPrice()), snapshotTime)));
//        }catch (Exception e) {
//            log.error("getRatesToUSDTCoinNameMap execute error, exception:", e);
//        }
//        return resultMap;
//    }
//
//    private String getCoinName(String leftCoinName) {
//        return (leftCoinName + UNDERSCORE_SEPARATOR + RightCoinTypeEnum.USDT).toLowerCase();
//    }
//
    @Override
    public Map<Integer, PriceVo> getCoinIdRatesMap(Long snapshotTime) {
        List<SpotCoinPricePartitionDTO> spotCoinPriceByDataTime = spotCoinPriceFacade.getSpotCoinPriceByDataTime(snapshotTime);
        if (CollectionUtils.isEmpty(spotCoinPriceByDataTime)) {
            log.error("spotCoinPriceFacade coinId getRatesMap is empty, param:{}", snapshotTime);
            throw new RuntimeException("spotCoinPriceFacade coinId getRatesMap is empty");
        }
        return spotCoinPriceByDataTime.stream().collect(Collectors.toMap(SpotCoinPricePartitionDTO::getCoinId, e -> new PriceVo(1, new BigDecimal(e.getCoinPrice()), snapshotTime)));
    }

    //
//    @Override
//    public Map<String, PriceVo> getCoinNameRatesPriceMapCache(Long snapshotTime){
//        return CacheUtils.computeIfAbsent(allSnapshotCoinPriceCache, snapshotTime, () -> getCoinNameRatesPriceMap(snapshotTime));
//    }
//
    @Override
    public Map<Integer, PriceVo> getCoinIdRatesMapCache(Long snapshotTime) {
        return CacheUtils.computeIfAbsent(allSnapshotPriceCache, snapshotTime, () -> getCoinIdRatesMap(snapshotTime));
    }

    @Override
    public Map<Integer, PriceVo> getNewCoinIdRatesMapCache(Long snapshotTime, AccountTypeEnum accountTypeEnum) {
        if (AccountTypeEnum.ONCHAIN.equals(accountTypeEnum)) {
            return CacheUtils.computeIfAbsent(allSnapshotPriceCacheNew, snapshotTime, () -> getOnChainCoinPriceMap(snapshotTime));
        } else {
            return getCoinIdRatesMapCache(snapshotTime);
        }
    }

    private Map<Integer, PriceVo> getOnChainCoinPriceMap(Long snapshotTime) {
        Map<Integer, PriceVo> coinPriceMap = new HashMap<>();
        List<OnChainCoinPricePartitionDTO> onChainCoinPriceList = getOnChainCoinPriceList(snapshotTime);
        if (CollectionUtils.isNotEmpty(onChainCoinPriceList)) {
            coinPriceMap = onChainCoinPriceList.stream().collect(Collectors.toMap(OnChainCoinPricePartitionDTO::getCoinId, onChain -> new PriceVo(0, new BigDecimal(onChain.getCoinPrice()), snapshotTime)));
        }
        return coinPriceMap;
    }

    private List<OnChainCoinPricePartitionDTO> getOnChainCoinPriceList(Long snapshotTime) {
        List<OnChainCoinPricePartitionDTO> onChainCoinPriceList = new ArrayList<>();
        try {
            List<OnChainCoinPricePartitionDTO> onChainCoinPricePartitionDTOList = onChainCoinPriceFacade.getOnChainCoinPriceByDataTime(snapshotTime);
            if (CollectionUtils.isEmpty(onChainCoinPricePartitionDTOList)) {
                return onChainCoinPriceList;
            }
            return onChainCoinPricePartitionDTOList;
        } catch (ApiException e) {
            log.error("getOnChainCoinPriceByDataTime snapshotTime error:{}", snapshotTime, e);
            if (Objects.equals(HttpStatus.BAD_REQUEST.value(), e.getCode())) {
                return onChainCoinPriceList;
            }
            throw e;
        }
    }

    //
//    private PriceVo getPriceVo(Map<String, PriceVo> ratesToUSDTMap, String coinName) {
//        PriceVo priceVo = ratesToUSDTMap.get(coinName);
//        if (ObjectUtil.isEmpty(priceVo)) {
//            priceVo = new PriceVo();
//            priceVo.setPrice(BigDecimal.ZERO);
//        }
//        return priceVo;
//    }
//
//    /**
//     * 将根据名称获取汇率转换为根据币种id获取汇率
//     * @param ratesToUSDTMap 汇率，为了保持汇率一致
//     * @return java.util.Map<java.lang.Integer,com.upex.ticker.facade.dto.PriceVo>
//     * @throws
//     * @Date 2022/9/20 00:46
//     * <AUTHOR>
//     */
//    @Override
//    public Map<Integer,PriceVo> convertToCoinIdMap(Map<String, PriceVo> ratesToUSDTMap){
//        // 获取所有的币种名称Map
//        Map<Integer, String> allCoinsMap = this.getAllCoinsMap();
//        // 获取所有币种的汇率
//        if (ApolloBillConfigUtil.getGlobalBillConfig().isShowCoinInfoAndRatesLog()) {
//            log.warn("allCoinsMap={}", JSONObject.toJSONString(allCoinsMap));
//            log.warn("ratesToUSDTMap={}", JSONObject.toJSONString(ratesToUSDTMap));
//        }
//        // 返回coinId和汇率关系
//        Map<Integer,PriceVo> coinIdRatePriceMap = new HashMap<>();
//        for (Map.Entry<Integer, String> entry : allCoinsMap.entrySet()) {
//            if(StringUtils.isBlank(entry.getValue()) || StringUtils.isBlank(entry.getValue())){
//                continue;
//            }
//            String leftCoinName = entry.getValue();
//            Integer coinId = entry.getKey();
//            String coinName = (leftCoinName + UNDERSCORE_SEPARATOR + RightCoinTypeEnum.USDT).toLowerCase();
//            PriceVo priceVo = getPriceVo(ratesToUSDTMap, coinName);
//            coinIdRatePriceMap.put(coinId, priceVo);
//        }
//        return coinIdRatePriceMap;
//    }
//
//    @Override
//    public List<String> getSubSystem() {
//        return Optional.ofNullable(ApolloBillConfigUtil.getAssetStatisticsConfig())
//                .map(AssetStatisticsConfig::getSubSystemList)
//                .orElseThrow(() -> {
//                    return new ApiException(BillExceptionEnum.SUBSYSTEM_LIST_IS_NULL);
//                });
//    }
//
    @Override
    public Map<Integer, String> getAllCoinsMap() {
        Map<Integer, String> resultMap = new ConcurrentHashMap<>();

        Map<Integer, String> allCoinsMap = coinConfigService.getAllCoinsMap();
        if (allCoinsMap == null) {
            AlarmUtils.error("common getAllCoinsMap result is null!");
            throw new ApiException(BillExceptionEnum.SYSTEM_ERROR);
        }

//        获取swap所有币种信息
        Map<Integer, String> allChainMap = getChainInfoMap();
        List<SwapTokenConfigDTO> responseSwapTokens = swapTokenConfigService.getAllTokens();
        Map<Integer, String> allSwapCoinsMap = responseSwapTokens.stream()
                .collect(Collectors.toMap(SwapTokenConfigDTO::getId, swap -> {
                    return (swap.getSymbol() + SEPARATOR + allChainMap.get(swap.getSwapChainId()).toUpperCase() + SEPARATOR + swap.getSwapContractAddress());
                }, (k1, k2) -> k1));

        resultMap.putAll(allCoinsMap);
        resultMap.putAll(allSwapCoinsMap);

        return resultMap;
    }

    //
//    /**
//     * 获取全部币种（包含维护的老币种）,value取值需要大写
//     * 对账系统保存钱包币种id为"币+链"唯一值，所以获取币种信息时，钱包需要单独获取Apollo的数据
//     * @return Map
//     */
//    @Override
//    public Map<Integer, String> getAllCoinsMap(AccountTypeEnum accountType) {
//        return accountType.isWallet() ? getAllWalletCoinsMap() : getAllCoinsMap();
//    }
//
//    /**
//     * 获取钱包链map
//     * @return java.util.Map<java.lang.Integer,java.lang.String>
//     * @throws
//     * @Date 2022/8/26 14:46
//     * <AUTHOR>
//     */
//    private Map<Integer, String> getAllWalletCoinsMap() {
//        Map<Integer, String> result = new ConcurrentHashMap<>();
//
//        Map<Integer, SpotCoinChainDTO> allCoinsMap = getAllWalletCoinInfoList();
//        Map<Integer, String> chainInfoMap = getChainInfoMap();
//
//        for (Map.Entry<Integer, SpotCoinChainDTO> entry : allCoinsMap.entrySet()) {
//            SpotCoinChainDTO spotCoinChainDTO = entry.getValue();
//            result.put(entry.getKey(), getRealCoinName(spotCoinChainDTO.getCoinName(),
//                    chainInfoMap.get(spotCoinChainDTO.getChainCoinId())));
//        }
//
//        return result;
//    }
//
//
//    @Override
//    public String getRealCoinName(String coinName, String chainName) {
//        return StringUtils.isNotEmpty(coinName) ? Strings.toUpperCase(StringUtils.isNotEmpty(chainName) ?
//                coinName.concat(SEPARATOR).concat(chainName) : coinName) : "";
//    }
//
//    /**
//     * 获取所有链信息
//     * @return java.util.Map<java.lang.Integer,java.lang.String>
//     * @Date 2022/8/22 10:17 AM
//     * <AUTHOR>
//     */
//    @Override
//    public Map<Integer, String> getChainInfoMap() {
//        List<CoinChainDTO> coinChainDTOS = CoinChainUtil.getAllCoinChain();
//        return coinChainDTOS.stream().collect(Collectors.toMap(CoinChainDTO::getChainCoinId, CoinChainDTO::getDesc, (k1, k2) -> k1));
//    }
//
    @Override
    public Map<String, Integer> getAllCoinId2Name() {
        Map<Integer, String> allCoinsMap = getAllCoinsMap();
        return reversalMap(allCoinsMap);

    }

    //
//    /**
//     * 反转map
//     *
//     * @return {@link Map<String,Integer>}
//     * <AUTHOR>
//     * @date 下午4:31 2021/12/3
//     **/
    @Override
    public Map<String, Integer> reversalMap(Map<Integer, String> map) {
        HashMap<String, Integer> retMap = new HashMap<>();
        map.forEach((coinId, coinName) -> {
            retMap.putIfAbsent(coinName.toUpperCase(), coinId);
        });
        return retMap;
    }

    //
//    /**
//     * 获取所有钱包币种信息集合
//     * @return java.util.Map<java.lang.Integer,java.lang.String>
//     * @Date 2022/8/20 3:15 PM
//     * <AUTHOR>
//     */
//    @Override
//    public Map<Integer, SpotCoinChainDTO> getAllWalletCoinInfoList() {
//        List<SpotCoinChainDTO> coinChains = coinConfigService.getCoinChains();
//        return coinChains.stream()
//                .collect(Collectors.toMap(spot -> spot.getId().intValue(), Function.identity()));
//    }
//
    @Override
    public Map<Integer, String> getAllCoinsMapCache(Long snapShotTime) {
        return CacheUtils.computeIfAbsent(allCoinInfoCache, snapShotTime, this::getAllCoinsMap);
    }
//
//    @Override
//    public Map<Integer, PriceVo> getRatesToUSDTCoinIdMapCache(AccountTypeEnum accountTypeEnum, Long snapShotTime) {
//        return CacheUtils.computeIfAbsent(allPriceCache, snapShotTime, () -> getRatesToUSDTCoinIdMap(accountTypeEnum));
//    }
//
//    @Override
//    public Map<Long, UserLoginListDTO> getAllSysUserMapCache(Long snapShotTime){
//        return CacheUtils.computeIfAbsent(allSysUserMapCache, snapShotTime, this::getSystemUserInfoMap);
//
//    }
//
//    /**
//     * 获取系统用户集合
//     * @param userId
//     * <AUTHOR>
//     * @date 2023/3/2 21:04
//     */
//    @Override
//    public boolean isSysUser(Long userId){
//        return getAllSysUserMapCache(DateUtil.getZeroOClick(new Date()).getTime()).containsKey(userId);
//    }

    @Override
    public UserLoginListDTO getSysUserInfo(Long userId) {
        return getAllSysUserMapCache(DateUtil.getZeroOClick(new Date()).getTime()).get(userId);
    }

    @Override
    public String getSysUserDisplayStr(Long userId) {
        UserLoginListDTO userLoginListDTO = getSysUserInfo(userId);
        String sysUserType = isSysUser(userId) ? String.format("%s(%s)", userLoginListDTO.getUserType(), userLoginListDTO.getRemark()) : null;
        return sysUserType;
    }
//
//    /**
//     * 根据获得对应的coinId
//     * @param coinName
//     * @return
//     */
//    @Override
//    public Integer getCoinIdByName(String coinName){
//        Optional<SpotCoinDTO> optional = coinConfigService.getCoinByCoinNameFromAll(coinName);
//        return optional.orElse(new SpotCoinDTO()).getCoinId();
//    }


}
