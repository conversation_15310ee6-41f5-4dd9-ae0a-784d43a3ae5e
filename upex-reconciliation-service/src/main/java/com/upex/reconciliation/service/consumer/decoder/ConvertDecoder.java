package com.upex.reconciliation.service.consumer.decoder;

import com.alibaba.otter.canal.protocol.FlatMessage;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;

import java.util.List;
import java.util.Map;

public class ConvertDecoder extends AbstractMessageDecoder {
    @Override
    public List<CommonBillChangeData> doMessageDecode(List<Map<String, String>> dataList, FlatMessage flatMessage, Integer partition, Long offset, Byte accountType) {
        return List.of();
    }
}
