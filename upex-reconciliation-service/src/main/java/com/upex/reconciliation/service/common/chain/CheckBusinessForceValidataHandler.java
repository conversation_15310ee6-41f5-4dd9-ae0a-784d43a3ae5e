package com.upex.reconciliation.service.common.chain;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;
import com.upex.reconciliation.service.business.CommonService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 业务逻辑校验，强制校验，出错抛出异常
 * @ClassName: CheckBusinessValidataHandler
 * @date 2022/4/29 11:37 AM
 * <AUTHOR>
*/
@Component
public class CheckBusinessForceValidataHandler extends AbstractCheckHandler{

    @Resource
    private CommonService commonService;


    public CheckBusinessForceValidataHandler(CommonService commonService) {
        this.commonService = commonService;
    }

    @Override
    public void doHandler(Long userId, Long snapShotTime) {
        Long currentTime = System.currentTimeMillis();
//        如果大于当前时间，抛出异常
        if (snapShotTime.compareTo(currentTime) > 0) {
            throw new ApiException(BillExceptionEnum.TIME_GREATER_THAN_CURRENT_TIME, "时间不能大于当前时间!");
        }
        if (Objects.nonNull(next)) {
            next.doHandler(userId, snapShotTime);
        }
    }
}
