package com.upex.reconciliation.service.service.client.cex.dto.res.common;

import com.upex.reconciliation.service.service.client.cex.enmus.ThirdAssetType;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Data
public class CommonTotalAssetRes {
    /**
     * 所有账户总额
     */
    private BigDecimal totalAmount;

    private String totalAmountStr;

    /**
     * 折算币种名称
     */
    private String calAssetCoinName;
    /**
     * 是否母用户
     */
    private String parentUserId;

    private Date createTime;

    /**
     * 各账户资产余额
     */
    List<ThirdAssetTypeAmount> thirdAssetTypeAmounts;

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
        this.totalAmountStr = totalAmount.toPlainString();
    }

    @Data
    public static class ThirdAssetTypeAmount {
        /**
         * 第三方资产账户类型
         * PARENT_FUNDING(10), 资金账户
         * PARENT_SPOT(11),现货账户
         * PARENT_MARGIN(12),全仓杠杆
         * PARENT_ISOLATED_MARGIN(13),逐仓杠杆
         * PARENT_UCONTRACT(14),U合约
         * PARENT_COIN_CONTRACT(15),币合约
         * PARENT_FLEX_EARN(16),活期理财
         * PARENT_LOCKED_EARN(17),定期理财
         * <p>
         * SUB_SPOT(30),子账户现货
         * SUB_MARGIN(31),子账户全仓
         * SUB_ISOLATED_MARGIN(32),子账户逐仓
         * SUB_UCONTRACT(33),子账户U合约
         * SUB_COIN_CONTRACT(34);子账户币合约
         */
        ThirdAssetType thirdAssetType;
        /**
         * 资产数量
         */
        BigDecimal amount;
        /**
         * 币种明细
         */
        CommonCoinAssetRes coinAssetRes;

        String amountStr;

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
            this.amountStr = amount.toPlainString();
        }
    }

    public synchronized void addThirdAssetTypeList(ThirdAssetType thirdAssetType, CommonCoinAssetRes commonCoinAssetRes) {
        if (thirdAssetTypeAmounts == null) {
            thirdAssetTypeAmounts = new ArrayList<>();
        }
        // 先查找是否存在该类型
        Optional<ThirdAssetTypeAmount> existing = thirdAssetTypeAmounts.stream()
                .filter(item -> java.util.Objects.equals(item.getThirdAssetType(), thirdAssetType))
                .findFirst();

        ThirdAssetTypeAmount thirdAssetTypeAmount;

        if (existing.isPresent()) {
            thirdAssetTypeAmount = existing.get();
        } else {
            thirdAssetTypeAmount = new ThirdAssetTypeAmount();
            thirdAssetTypeAmounts.add(thirdAssetTypeAmount);
        }
        // 统一赋值
        thirdAssetTypeAmount.setThirdAssetType(thirdAssetType);
        thirdAssetTypeAmount.setCoinAssetRes(commonCoinAssetRes);
        if (CollectionUtils.isNotEmpty(commonCoinAssetRes) && commonCoinAssetRes.get(0).getCreateTime() != null) {
            this.createTime = commonCoinAssetRes.get(0).getCreateTime();
        }
    }


    public synchronized void setThirdTypeAssetAmount(ThirdAssetType thirdAssetType, BigDecimal amount) {
        if (thirdAssetTypeAmounts == null) {
            thirdAssetTypeAmounts = new ArrayList<>();
        }
        // 先查找是否存在该类型
        Optional<ThirdAssetTypeAmount> existing = thirdAssetTypeAmounts.stream()
                .filter(item -> java.util.Objects.equals(item.getThirdAssetType(), thirdAssetType))
                .findFirst();

        ThirdAssetTypeAmount thirdAssetTypeAmount;

        if (existing.isPresent()) {
            thirdAssetTypeAmount = existing.get();
        } else {
            thirdAssetTypeAmount = new ThirdAssetTypeAmount();
            thirdAssetTypeAmounts.add(thirdAssetTypeAmount);
        }
        thirdAssetTypeAmount.setAmount(amount);
        thirdAssetTypeAmount.setThirdAssetType(thirdAssetType);
    }
}
