package com.upex.reconciliation.service.business;

import com.upex.reconciliation.service.model.BillCmdWrapper;

import java.util.Date;

/**
 * 多纬度资金监控的数据准备工作的基类，所有的实现类都通过继承他来进行实现自己的 数据准备方法
 * 需要数据输入的基类类型
 */

public interface MonitorWithInputDataRuleService {


    /**
     * 数据准备工作，将所有的数据最终包装成 BillCmdWrapper 的包装类格式
     */
    public BillCmdWrapper processScene(Long sceneId, Integer sceneType, Date executeTime, BillCmdWrapper billCmdWrapper);


}
