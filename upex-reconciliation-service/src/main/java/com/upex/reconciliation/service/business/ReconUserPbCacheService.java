package com.upex.reconciliation.service.business;

import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * 用户权限服务接口
 *
 * <AUTHOR>
 * @Date 2025/1/10
 */
public interface ReconUserPbCacheService {

    /**
     * 校验用户权限
     *
     * @param userId
     * @param permissionCode
     */
    Pair<Boolean, String> checkUserPermission(Long userId, Long permissionCode);

    /**
     * 校验用户权限列表
     *
     * @param userId
     * @param permissionCodeList
     */
    Pair<Boolean, String> checkUserPermissions(Long userId, List<Long> permissionCodeList);

    /**
     * 是否是子账户
     *
     * @param userId
     * @return
     */
    Boolean isSubAccount(Long userId);

    /**
     * 是否是nd broker子账户
     *
     * @param userId
     * @return
     */
    Boolean isBroker(Long userId);
}
