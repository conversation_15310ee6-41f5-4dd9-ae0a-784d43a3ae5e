package com.upex.reconciliation.service.business.billengine;


import com.upex.mixcontract.common.framework.AttributeMap;
import com.upex.mixcontract.common.framework.BaseLogicGroup;
import com.upex.mixcontract.common.framework.ThreadLocalUtils;
import com.upex.mixcontract.common.framework.command.AbstractCommand;
import com.upex.mixcontract.common.framework.module.AbstractModule;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.module.impl.BillTimeSliceCheckModule;
import com.upex.reconciliation.service.business.module.impl.BillUserAssetCheckModule;
import com.upex.reconciliation.service.business.module.impl.BillUserCheckModule;
import com.upex.reconciliation.service.business.module.impl.FlowMonitorDataProcessModule;
import com.upex.reconciliation.service.common.constants.ReconConstants;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.model.config.BillCommonConfig;
import com.upex.reconciliation.service.business.module.AbstractBillModule;
import io.netty.util.AttributeKey;

import java.util.List;


public class BillLogicGroup extends BaseLogicGroup {
    private final AccountTypeEnum accountTypeEnum;
    private final AbstractBillModule userCheckModule;
    private final AbstractBillModule saveModule;
    private final AbstractBillModule timeSliceModule;
    private final AbstractBillModule userAssetCheckModule;
    // 仅有flow类型的数据准备需要沉淀到具体的业务线里
    private final AbstractBillModule flowMonitorDataProcessModule;

    // 定义一些业务需要的上文的位置
    public static final AttributeKey<ReconciliationSpringContext> ENGINE_SPRING_CONTEXT_KEY = AttributeKey.valueOf("ENGINE_SPRING_CONTEXT_KEY");
    public static final AttributeKey<Boolean> IS_MAIN_LOGIC_GROUP = AttributeKey.valueOf("IS_MAIN_LOGIC_GROUP");
    public static final AttributeKey<AccountTypeEnum> ACCOUNT_TYPE_ENUM_KEY = AttributeKey.valueOf("ACCOUNT_TYPE_ENUM_KEY");
    private final AbstractBillEngine engine;


    public BillLogicGroup(String name, AttributeMap attrMap, List<Class<? extends AbstractCommand>> cmdClasses, List<Class<? extends AbstractModule>> moduleClasses, AccountTypeEnum accountTypeEnum, AbstractBillEngine engine) {
        super(name, attrMap, cmdClasses, moduleClasses);
        this.accountTypeEnum = accountTypeEnum;
        userCheckModule = getModule(BillUserCheckModule.class);
        timeSliceModule = getModule(BillTimeSliceCheckModule.class);
        userAssetCheckModule = getModule(BillUserAssetCheckModule.class);
        flowMonitorDataProcessModule = getModule(FlowMonitorDataProcessModule.class);
        saveModule = null;
        this.engine = engine;
    }


    public BillCommonConfig getBillConfig() {
        return ThreadLocalUtils.checkContextAndRun(ReconConstants.BILL_MAIN_LOGIC_CONTEXT, (context) -> {
            BillCommonConfig billCommonConfig = null;
            return billCommonConfig;
        }, true);
    }

    public AbstractBillEngine getEngine() {
        return engine;
    }

    public AbstractBillModule getUserCheckModule() {
        return userCheckModule;
    }

    public AbstractBillModule getSaveModule() {
        return saveModule;
    }

    public AbstractBillModule getTimeSliceModule() {
        return timeSliceModule;
    }

    public AbstractBillModule getUserAssetCheckModule() {
        return userAssetCheckModule;
    }

    public AccountTypeEnum getAccountTypeEnum() {
        return accountTypeEnum;
    }


    public AbstractBillModule getFlowMonitorDataProcessModule() {
        return flowMonitorDataProcessModule;
    }

}
