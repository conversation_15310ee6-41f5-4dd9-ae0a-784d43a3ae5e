package com.upex.reconciliation.service.business.convert.remote;


import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.upex.kline.dto.request.KLineSearchRequest;
import com.upex.kline.dto.response.KLinePeriodDto;
import com.upex.kline.feign.inner.KlineInnerFeignClient;
import com.upex.reconciliation.service.business.convert.model.KLineDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 调用内部K线服务
 * doc：<a href="https://larkfive.sg.larksuite.com/wiki/NuJrwpERNiihFpkiH02lgAD5gKg">...</a>
 */
@Service
@Slf4j
public class KlineInnerService {

//    @Resource
//    private KlineFeignService klineFeignService;

    private static final String KLINE_CACHE_PREFIX = "kline_cache_index:";


    @Resource
    private KlineInnerFeignClient klineInnerFeignClient;
    // 本地缓存，减少重复调用
    private final Cache<String, KLineDataDTO> klineCache = Caffeine.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .maximumSize(1000)
            .build();

    /**
     * 获取现货1分钟指数K线数据(如果没有指数k线会返回普通k线)
     *
     * @param productCode 币种名称（btcusdt）
     * @param endTime     收盘时间（字符串类型，毫秒时间戳）
     * @return K线数据列表
     */
    public KLineDataDTO getSpotKline1mIndex(String productCode, Long endTime) {


        // 将时间戳按分钟取整，作为缓存key的一部分
        long minuteTimestamp = getMinuteTimestamp(endTime);
        String cacheKey = KLINE_CACHE_PREFIX + productCode + ":" + minuteTimestamp;
        // 尝试从缓存获取
        KLineDataDTO cachedResult = klineCache.getIfPresent(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }
        KLineDataDTO kLineDataDTO=null;
        KLineSearchRequest kLineSearchRequest = new KLineSearchRequest();
        try {
            kLineSearchRequest.setProductCode(productCode);
            kLineSearchRequest.setEndTime(endTime);
            // 数据条数
            kLineSearchRequest.setInnerCount(1L);
            // 1现货
            kLineSearchRequest.setBizType(1);
            // k线类型：指数k线
            kLineSearchRequest.setKLineType(3);
            // k线步长单位：1分钟
            kLineSearchRequest.setKLineStepUnit("1m");
            List<KLinePeriodDto> klineDataV2 = klineInnerFeignClient.getKlineDataV2(kLineSearchRequest);
            if (CollectionUtils.isNotEmpty(klineDataV2)) {
                KLinePeriodDto kLinePeriodDto = klineDataV2.get(0);
                // 存入缓存
                if (kLinePeriodDto != null) {
                    kLineDataDTO=new KLineDataDTO(kLinePeriodDto,kLineSearchRequest.getKLineType());
                    klineCache.put(cacheKey, kLineDataDTO);
                }
            }
        } catch (Exception e) {
            log.error("get getSpotKline1m error for productCode: {}, endTime: {}", productCode, endTime, e);
        }
       // 获取现货k线
        if (kLineDataDTO == null) {
            try {
                // k线类型：现货k线
                kLineSearchRequest.setKLineType(1);
                List<KLinePeriodDto> klineDataV2 = klineInnerFeignClient.getKlineDataV2(kLineSearchRequest);
                if (CollectionUtils.isNotEmpty(klineDataV2)) {
                    KLinePeriodDto kLinePeriodDto = klineDataV2.get(0);
                    // 存入缓存
                    if (kLinePeriodDto != null) {
                        kLineDataDTO=new KLineDataDTO(kLinePeriodDto,kLineSearchRequest.getKLineType());
                        klineCache.put(cacheKey, kLineDataDTO);
                    }
                }
            } catch (Exception e) {
                log.error("get getSpotKline1m error for productCode: {}, endTime: {}", productCode, endTime, e);
            }
        }
        return kLineDataDTO;
    }


    /**
     * 将时间戳按分钟取整
     * (2025-06-30 22:51:59返回2025-06-30 22:51:00)
     *
     * @param timestamp 毫秒时间戳
     * @return 按分钟取整后的时间戳
     */
    private long getMinuteTimestamp(Long timestamp) {
        if (timestamp == null) {
            return 0L;
        }
        // 转为分钟
        return timestamp / 60000 * 60000;
    }

}
