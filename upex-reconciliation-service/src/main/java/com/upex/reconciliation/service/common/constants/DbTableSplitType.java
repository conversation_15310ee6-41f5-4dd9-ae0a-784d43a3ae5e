package com.upex.reconciliation.service.common.constants;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;

/**
 * <AUTHOR>
 * @date 2023/7/8 17:16
 */
public enum DbTableSplitType {

    SPLIT_TYPE_MONTH("Month", "按月拆表"),
    SPLIT_TYPE_10DAY("10Day", "按10天拆表"),
    ;

    private String key;
    private String desc;

    DbTableSplitType(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    public static DbTableSplitType toEnum(String key) {
        for (DbTableSplitType item : DbTableSplitType.values()) {
            if (item.key.equals(key)) {
                return item;
            }
        }
        throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
    }
}
