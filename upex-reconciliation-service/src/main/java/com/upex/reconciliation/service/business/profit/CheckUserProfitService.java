package com.upex.reconciliation.service.business.profit;

import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;

/**
 * 用户盈利检测接口
 *
 * <AUTHOR>
 * @Date 2025/4/25
 */
public interface CheckUserProfitService {

    /**
     * 用户盈利检测
     *
     * @param checkResultsParams
     * @param globalBillConfig
     * @param timePeriod
     * @return
     */
    boolean checkProfit(ReconCheckResultsParams checkResultsParams, GlobalBillConfig globalBillConfig, Integer timePeriod);
}
