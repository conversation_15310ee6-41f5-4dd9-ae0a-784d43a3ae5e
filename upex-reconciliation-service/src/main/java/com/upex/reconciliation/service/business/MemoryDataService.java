package com.upex.reconciliation.service.business;

import com.upex.reconciliation.service.common.concurrent.ConcurrentSortMap;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.dto.SymbolCheckProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.SortedMap;

/**
 * 内存数据操作
 */
@Slf4j
@Service
public class MemoryDataService {
    /***时间戳-》业务线-》coinId，资产***/
    private ConcurrentSortMap<Long, Map<String, Map<Integer, BillCoinProperty>>> ledgerTimeSliceCoinPropertyMap = new ConcurrentSortMap();
    /***时间戳-》业务线-》bizType，资产***/
    private ConcurrentSortMap<Long, Map<String, Map<String, BillCoinTypeProperty>>> ledgerTimeSliceCoinTypePropertyMap = new ConcurrentSortMap();
    /***时间戳-》业务线-》仓位信息****/
    private ConcurrentSortMap<Long, Map<String, List<SymbolCheckProperty>>> ledgerTimeSliceSymbolCheckPropertyMap = new ConcurrentSortMap();
    /***时间戳-》业务线-》盈亏换汇明细****/
    private ConcurrentSortMap<Long, Map<String, List<BillContractProfitCoinDetail>>> ledgerTimeSliceContractProfitCoinDetailMap = new ConcurrentSortMap();
    /***时间戳-》业务线-》盈亏换汇明细****/
    private ConcurrentSortMap<Long, Map<String, List<BillContractProfitSymbolDetail>>> ledgerTimeSliceContractProfitSymbolDetailMap = new ConcurrentSortMap();
    private ConcurrentSortMap<Long, Map<String, List<BillTransferFeeCoinDetail>>> ledgerTimeSliceTransferFeeCoinDetailMap = new ConcurrentSortMap();
    /***时间戳-》业务线,完成标记***/
    private ConcurrentSortMap<Long, Map<String, Boolean>> ledgerTimeSliceKafkaComplete = new ConcurrentSortMap();

    /**
     * 获取总账业务线数据完成标记
     *
     * @return
     */
    public SortedMap<Long, Map<String, Boolean>> getLedgerTimeSliceKafkaComplete() {
        return ledgerTimeSliceKafkaComplete;
    }

    /**
     * 获取总账coin资产
     *
     * @return
     */
    public SortedMap<Long, Map<String, Map<Integer, BillCoinProperty>>> getLedgerTimeSliceCoinPropertyMap() {
        return ledgerTimeSliceCoinPropertyMap;
    }

    /**
     * 获取业务线总账资产
     *
     * @return
     */
    public SortedMap<Long, Map<String, Map<String, BillCoinTypeProperty>>> getLedgerTimeSliceCoinTypePropertyMap() {
        return ledgerTimeSliceCoinTypePropertyMap;
    }

    /**
     * 获取业务线仓位信息
     *
     * @return
     */
    public SortedMap<Long, Map<String, List<SymbolCheckProperty>>> getLedgerTimeSliceSymbolCheckPropertyMap() {
        return ledgerTimeSliceSymbolCheckPropertyMap;
    }

    /**
     * 获取业务线盈亏换汇明细数据
     *
     * @return
     */
    public ConcurrentSortMap<Long, Map<String, List<BillContractProfitCoinDetail>>> getLedgerTimeSliceContractProfitCoinDetailMap() {
        return ledgerTimeSliceContractProfitCoinDetailMap;
    }

    /**
     * 获取业务线盈亏换汇明细数据
     *
     * @return
     */
    public ConcurrentSortMap<Long, Map<String, List<BillContractProfitSymbolDetail>>> getLedgerTimeSliceContractProfitSymbolDetailMap() {
        return ledgerTimeSliceContractProfitSymbolDetailMap;
    }

    /**
     * 获取业务线转账手续费明细数据
     *
     * @return
     */
    public ConcurrentSortMap<Long, Map<String, List<BillTransferFeeCoinDetail>>> getLedgerTimeSliceTransferFeeCoinDetailMap() {
        return ledgerTimeSliceTransferFeeCoinDetailMap;
    }

    /**
     * 移除时间片
     *
     * @param timeSliceKey
     */
    public void removeLedgerTimeSlice(Long timeSliceKey) {
        ledgerTimeSliceKafkaComplete.remove(timeSliceKey);
        ledgerTimeSliceCoinPropertyMap.remove(timeSliceKey);
        ledgerTimeSliceCoinTypePropertyMap.remove(timeSliceKey);
        ledgerTimeSliceSymbolCheckPropertyMap.remove(timeSliceKey);
        ledgerTimeSliceTransferFeeCoinDetailMap.remove(timeSliceKey);
        ledgerTimeSliceContractProfitCoinDetailMap.remove(timeSliceKey);
    }

    /**
     * 获取时间片大小
     *
     * @return
     */
    public Integer getLedgerTimeSliceSize(String accountType) {
        Integer count = 0;
        try {
            for (Map.Entry<Long, Map<String, Boolean>> entry : ledgerTimeSliceKafkaComplete.entrySet()) {
                Boolean result = entry.getValue().get(accountType);
                count += result != null ? 1 : 0;
            }
        } catch (Exception e) {
        }
        return count;
    }

    /**
     * 获取总账最小时间片
     *
     * @return
     */
    public Long getLedgerMinTimeSlice() {
        return ledgerTimeSliceKafkaComplete.firstKey(System.currentTimeMillis());
    }
}
