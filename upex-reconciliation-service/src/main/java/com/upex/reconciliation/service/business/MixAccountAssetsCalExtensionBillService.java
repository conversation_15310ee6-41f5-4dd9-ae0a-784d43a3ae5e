package com.upex.reconciliation.service.business;


import com.upex.reconciliation.facade.dto.results.ReconAccountAssetsInfoResult;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserAssets;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface MixAccountAssetsCalExtensionBillService {
    /**
     * 根据期初资产+流水推算期末资产
     *
     * @param startAccountAssets
     * @param billList
     * @param accountParam
     * @param useFixPrice        使用固定价格
     * @return
     */
    BillCoinTypeUserProperty calEndAccountAssets(BillCoinTypeUserProperty startAccountAssets,
                                                 List<ReconAccountAssetsInfoResult> billList, String accountParam,
                                                 BigDecimal useFixPrice);
    /**
     * 根据期初资产+流水推算期末资产
     *
     * @param startAccountAssets
     * @param billList
     * @param accountParam
     * @param useFixPrice        使用固定价格
     * @param markPriceMap
     * @param settlePriceMap
     * @return
     */
    default BillCoinTypeUserProperty newCalEndAccountAssets(BillCoinTypeUserProperty startAccountAssets,
                                                            List<ReconAccountAssetsInfoResult> billList, String accountParam,
                                                            BigDecimal useFixPrice, Map<String, BigDecimal> markPriceMap, Map<Integer, BigDecimal> settlePriceMap) {return null;}


    /**
     * 计算账户权益
     *
     * @param accountAssets
     * @param accountParam
     * @param useFixPrice
     * @return
     */
    ReconAccountAssetsInfoResult calAccountEquity(ReconAccountAssetsInfoResult accountAssets, String accountParam,
                                                  BigDecimal useFixPrice);

    /**
     * 计算账户权益
     *
     * @param billCoinUserAssets
     * @param accountParam
     * @param markPriceMap
     * @param rateMap
     * @return
     */
    ReconAccountAssetsInfoResult calAccountEquity(BillCoinUserAssets billCoinUserAssets, Byte accountType, String accountParam,
                                                  Map<String, BigDecimal> markPriceMap, Map<Integer, BigDecimal> rateMap);
}
