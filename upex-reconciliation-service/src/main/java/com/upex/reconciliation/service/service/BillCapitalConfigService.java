package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillCapitalConfig;
import com.upex.reconciliation.service.dao.mapper.BillCapitalConfigMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class BillCapitalConfigService {


    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "billCapitalConfigMapper")
    private BillCapitalConfigMapper billCapitalConfigMapper;


    public List<BillCapitalConfig> selectLastFinalStateConfig(Integer capitalBlockedWithdrawCount) {
        return dbHelper.doDbOpInSnapshotGlobalMaster(() -> billCapitalConfigMapper.selectLastFinalStateConfig(capitalBlockedWithdrawCount));
    }


    public BillCapitalConfig selectLastConfigByTimeAndStatus(Date executeTime, Integer statusCode){
        return dbHelper.doDbOpInSnapshotGlobalMaster(() -> billCapitalConfigMapper.selectLastConfigByTimeAndStatus(executeTime,statusCode));

    }

}
