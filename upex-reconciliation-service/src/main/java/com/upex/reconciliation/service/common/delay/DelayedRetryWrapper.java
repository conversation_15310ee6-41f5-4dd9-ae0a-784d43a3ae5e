package com.upex.reconciliation.service.common.delay;

import lombok.Data;

import java.util.Objects;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 延迟重试任务包装类
 *
 * <AUTHOR>
 * @Date 2024/12/16
 */
@Data
public class DelayedRetryWrapper<T, R> implements Delayed {

    /**
     * 延迟时间(单位：毫秒)
     */
    private Long delayTime;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount = 3;

    /**
     * 数据
     */
    private T data;

    /**
     * 执行回调函数
     */
    private Function<T, R> function;

    public DelayedRetryWrapper(Long delayTime, T data, Function<T, R> function) {
        this(delayTime, null, data, function);
    }

    public DelayedRetryWrapper(Long delayTime, Integer maxRetryCount, T data, Function<T, R> function) {
        if (Objects.isNull(delayTime)) {
            // 1分钟
            delayTime = 1 * 60 * 1000L;
        }
        this.delayTime = System.currentTimeMillis() + delayTime;
        if (Objects.nonNull(maxRetryCount)) {
            this.maxRetryCount = maxRetryCount;
        }
        this.data = data;
        this.function = function;
    }

    @Override
    public long getDelay(TimeUnit unit) {
        return unit.convert(delayTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
    }

    @Override
    public int compareTo(Delayed o) {
        return (int) (this.getDelay(TimeUnit.MILLISECONDS) - o.getDelay(TimeUnit.MILLISECONDS));
    }
}
