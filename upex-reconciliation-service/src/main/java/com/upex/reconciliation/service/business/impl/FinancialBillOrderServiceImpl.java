package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.FinancialBillOrderService;
import com.upex.reconciliation.service.common.constants.enums.BillRedisKeyEnum;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.facade.dto.results.FinancialBillOrderDTO;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class FinancialBillOrderServiceImpl implements FinancialBillOrderService {
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Override
    public void billHandle(CommonBillChangeData commonBillChangeData) {
        String redisTxIdPartitionKey = null;
        try {
            byte accountType = commonBillChangeData.getAccountType();
            if (!AccountTypeEnum.toEnum(accountType).isSpot()) {
                // 只处理理财账户的bgsol流水
                return;
            }
            //订单类型过滤前置
            ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
            if (!apolloBizConfig.getFinanceBgsolWithdrawTypeSet().contains(commonBillChangeData.getBizType())) {
                return;
            }

            // 指定业务线， 指定类型流水处理
            log.info("FinancialFinancialBillOrderServiceImpl billHandle commonBillChangeData:{}", JSONObject.toJSONString(commonBillChangeData));

            // 获取redis中的数据  key: 业务线
            redisTxIdPartitionKey = String.format(BillRedisKeyEnum.RECON_FINANCE_ORDER_KEY.getKey(), accountType);
            FinancialBillOrderDTO financialBillOrderDTO = new FinancialBillOrderDTO();
            financialBillOrderDTO.setCoinId(commonBillChangeData.getCoinId());
            financialBillOrderDTO.setWithdrawalAmount(commonBillChangeData.getChangePropSum(accountType));
            redisTemplate.opsForZSet().add(redisTxIdPartitionKey, JSONObject.toJSONString(financialBillOrderDTO), commonBillChangeData.getBizTime().getTime());
            log.info("billHandle success, redisTxIdPartitionKey:{}", redisTxIdPartitionKey);
        } catch (Exception e) {
            log.error("billHandle error, commonBillChangeData:{}, redisTxIdPartitionKey:{}", JSONObject.toJSONString(commonBillChangeData), redisTxIdPartitionKey, e);
        }
    }
}
