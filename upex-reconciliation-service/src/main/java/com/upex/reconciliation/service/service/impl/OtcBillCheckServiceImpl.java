package com.upex.reconciliation.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AbstractBillCheckService;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.dao.entity.BillCoinProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillWalletSupplementConfig;
import com.upex.reconciliation.service.model.alarm.CheckResult;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.*;
import com.upex.reconciliation.service.utils.NumberUtil;
import com.upex.ticker.facade.dto.PriceVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.DO_BATCH_CHECK_PROPERTY_SUCCESS;

@Service
@Slf4j
public class OtcBillCheckServiceImpl extends AbstractBillCheckService {

    @Resource
    private AlarmNotifyService alarmNotifyService;

    @Override
    public Byte getAccountType() {
        return AccountTypeEnum.OTC.getCode();
    }

    @Override
    public CheckResult doCheckProperty(ApolloReconciliationBizConfig apolloBizConfig, String accountUniqueId, Long userId, Integer coinId, List<CommonBillChangeData> billChangeDataList, BillCoinUserProperty billCoinUserProperty) {
        CommonBillChangeData lastChange = billChangeDataList.get(billChangeDataList.size() - 1);
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        BigDecimal totalChangeProp1 = BigDecimal.ZERO;
        BigDecimal totalChangeProp2 = BigDecimal.ZERO;
        for (CommonBillChangeData bill : billChangeDataList) {
            totalChangeProp1 = totalChangeProp1.add(bill.getChangeProp1());
            totalChangeProp2 = totalChangeProp2.add(bill.getChangeProp2());
        }
        if (billCoinUserProperty.getProp1().add(totalChangeProp1).compareTo(lastChange.getProp1()) != 0) {
            log.error("OtcBillCheckServiceImpl.checkProperty accountUniqueId:{}  lastChange:{},billCoinUserProperty:{},totalChangeProp1:{},userCoinPropertyMap {}"
                    , accountUniqueId, JSONObject.toJSONString(lastChange), totalChangeProp1.toPlainString(), JSON.toJSONString(billCoinUserProperty));
            return CheckResult.fail(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), lastChange.getBizTime())
                    .addUserPropCheckData(lastChange.getAccountId(), lastChange.getCoinId(), billCoinUserProperty.getProp1(), totalChangeProp1, lastChange.getProp1(), lastChange.getBizId(), "prop1");
        }
        if (billCoinUserProperty.getProp2().add(totalChangeProp2).compareTo(lastChange.getProp2()) != 0) {
            log.error("OtcBillCheckServiceImpl.checkProperty accountUniqueId:{}  lastChange:{},billCoinUserProperty:{},totalChangeProp2:{} userCoinPropertyMap {}"
                    , accountUniqueId, JSONObject.toJSONString(lastChange), totalChangeProp1.toPlainString(), JSON.toJSONString(billCoinUserProperty));
            return CheckResult.fail(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), lastChange.getBizTime())
                    .addUserPropCheckData(lastChange.getAccountId(), lastChange.getCoinId(), billCoinUserProperty.getProp2(), totalChangeProp2, lastChange.getProp2(), lastChange.getBizId(), "prop2");
        }
        return CheckResult.DEFAULT_SUCCESS;
    }

    @Override
    public boolean checkBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty) {
        if (commonBillChangeData.getProp1().subtract(commonBillChangeData.getChangeProp1()).compareTo(billCoinUserProperty.getProp1()) == 0
                && commonBillChangeData.getProp2().subtract(commonBillChangeData.getChangeProp2()).compareTo(billCoinUserProperty.getProp2()) == 0) {
            return true;
        }
        return false;
    }

    @Override
    public boolean checkCurrentBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty) {
        if (commonBillChangeData.getProp1().compareTo(billCoinUserProperty.getProp1()) == 0
                && commonBillChangeData.getProp2().compareTo(billCoinUserProperty.getProp2()) == 0) {
            return true;
        }
        return false;
    }

    @Override
    public BigDecimal getPropSumByUserProperty(BillCoinUserProperty coinUserProperty) {
        return NumberUtil.add(coinUserProperty.getProp1(), coinUserProperty.getProp2());
    }

    @Override
    protected CheckResult doBatchCheckProperty(ApolloReconciliationBizConfig apolloBizConfig, String accountUniqueId, Long userId, Integer coinId, List<CommonBillChangeData> billChangeDataList, BillCoinUserProperty billCoinUserProperty) {
        CheckResult result = doCheckProperty(apolloBizConfig, accountUniqueId, userId, coinId, billChangeDataList, billCoinUserProperty);
        if (result.isSuccess()) {
            log.info("OtcBillCheckServiceImpl.doBatchCheckProperty success accountUniqueId:{} billCoinUserProperty:{} billChangeDataList:{}", accountUniqueId, JSON.toJSONString(billCoinUserProperty), JSONObject.toJSONString(billChangeDataList.stream().limit(10).collect(Collectors.toList())));
            alarmNotifyService.alarm(AccountTypeEnum.OTC.getCode(), DO_BATCH_CHECK_PROPERTY_SUCCESS, AccountTypeEnum.OTC.getCode(), accountUniqueId);
        }
        return result;
    }

    @Override
    protected UserAssetsNegativeModel doCheckNegative(BillCoinUserProperty billCoinUserProperty, BigDecimal unRealized) {
        if (billCoinUserProperty.getProp1() != null && billCoinUserProperty.getProp1().compareTo(BigDecimal.ZERO) < 0) {
            UserAssetsNegativeModel userAssetsNegativeModel = UserAssetsNegativeModel.builder()
                    .propValue(billCoinUserProperty.getProp1())
                    .build();
            return userAssetsNegativeModel;
        }
        if (billCoinUserProperty.getProp2() != null && billCoinUserProperty.getProp2().compareTo(BigDecimal.ZERO) < 0) {
            UserAssetsNegativeModel userAssetsNegativeModel = UserAssetsNegativeModel.builder()
                    .propValue(billCoinUserProperty.getProp2())
                    .build();
            return userAssetsNegativeModel;
        }
        return null;
    }

    @Override
    public <T extends AbstractProperty> boolean checkAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult, ApolloReconciliationBizConfig apolloBillConfig, Map<Integer, PriceVo> ratesToUSDTCoinIdMap, Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap) {
        if (property == null || totalAccountAssetsInfoResult == null) {
            return false;
        }
        if (property.getProp1().compareTo(totalAccountAssetsInfoResult.getProp1()) != 0) {
            return false;
        }

        if (property.getProp2().compareTo(totalAccountAssetsInfoResult.getProp2()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractSProperty> boolean checkSpropAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult, ApolloReconciliationBizConfig apolloBillConfig, Map<Integer, PriceVo> ratesToUSDTCoinIdMap, Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap) {
        if (property == null || totalAccountAssetsInfoResult == null) {
            return false;
        }
        if (property.getSprop1().compareTo(totalAccountAssetsInfoResult.getProp1()) != 0) {
            return false;
        }

        if (property.getSprop2().compareTo(totalAccountAssetsInfoResult.getProp2()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public BigDecimal getPropSumByProperty(AbstractProperty billCoinProperty) {
        return billCoinProperty == null ? BigDecimal.ZERO : billCoinProperty.getProp1().add(billCoinProperty.getProp2());
    }

    @Override
    public BigDecimal getPropByTransferFee(AbstractProperty abstractProperty) {
        return abstractProperty != null ? abstractProperty.getProp1() : BigDecimal.ZERO;
    }

    @Override
    public BigDecimal getChangePropSumByProperty(AbstractProperty abstractProperty) {
        return NumberUtil.add(abstractProperty.getChangeProp1(), abstractProperty.getChangeProp2());
    }

    @Override
    public BigDecimal getPropSumByBillChangeData(CommonBillChangeData currentBill) {
        return NumberUtil.add(currentBill.getProp1(), currentBill.getProp2());
    }

    @Override
    public BigDecimal getChangePropSumByBillChangeData(CommonBillChangeData currentBill) {
        return NumberUtil.add(currentBill.getChangeProp1(), currentBill.getChangeProp2());
    }

    @Override
    public BigDecimal getChangePropSumByAbstractProperty(AbstractProperty abstractProperty) {
        return NumberUtil.add(abstractProperty.getChangeProp1(), abstractProperty.getChangeProp2());
    }

    /**
     * 获取业务线类型
     *
     * @return
     */
    @Override
    public List<String> getBusinessType() {
        return Arrays.asList(AccountTypeEnum.OTC.getBizTypePrefix());
    }

    @Override
    public <T extends AbstractProperty> boolean checkPosition(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult) {
        return false;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitAssets(T oldProperty, BillCoinUserProperty newBillCoinUserProperty) {
        if (oldProperty == null || newBillCoinUserProperty == null) {
            return false;
        }
        if (oldProperty.getProp1().compareTo(newBillCoinUserProperty.getProp1()) != 0) {
            return false;
        }
        if (oldProperty.getProp2().compareTo(newBillCoinUserProperty.getProp2()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinAssets(T oldProperty, BillCoinProperty billCoinProperty) {
        if (oldProperty == null || billCoinProperty == null) {
            return false;
        }
        if (oldProperty.getProp1().compareTo(billCoinProperty.getProp1()) != 0) {
            return false;
        }
        if (oldProperty.getProp2().compareTo(billCoinProperty.getProp2()) != 0) {
            return false;
        }
        if (oldProperty.getProp3().compareTo(billCoinProperty.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeAssets(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        if (oldProperty == null || newBillCoinTypeProperty == null) {
            return false;
        }
        if (oldProperty.getProp1().compareTo(newBillCoinTypeProperty.getProp1()) != 0) {
            return false;
        }
        if (oldProperty.getProp2().compareTo(newBillCoinTypeProperty.getProp2()) != 0) {
            return false;
        }
        if (oldProperty.getProp3().compareTo(newBillCoinTypeProperty.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinChangeAssets(T oldProperty, BillCoinProperty billCoinProperty) {
        if (oldProperty == null || billCoinProperty == null) {
            return false;
        }
        if (oldProperty.getChangeProp1().compareTo(billCoinProperty.getChangeProp1()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp2().compareTo(billCoinProperty.getChangeProp2()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp3().compareTo(billCoinProperty.getChangeProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeChangeAssets(T oldProperty, BillCoinTypeProperty billCoinTypeProperty) {
        if (oldProperty == null || billCoinTypeProperty == null) {
            return false;
        }
        if (oldProperty.getChangeProp1().compareTo(billCoinTypeProperty.getChangeProp1()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp2().compareTo(billCoinTypeProperty.getChangeProp2()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp3().compareTo(billCoinTypeProperty.getChangeProp3()) != 0) {
            return false;
        }
        return true;
    }


    @Override
    public <T extends AbstractProperty> boolean checkCoinAssetsByAccountType(T oldProperty, BillCoinProperty newBillCoinUserProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        if (oldProperty != null) {
            dataCalResultDTO.setFactor1(oldProperty.getProp1().add(oldProperty.getProp2()).add(oldProperty.getProp3()).add(oldProperty.getProp4()).add(oldProperty.getProp5()));
        }
        if (newBillCoinUserProperty != null) {
            dataCalResultDTO.setFactor2(newBillCoinUserProperty.getProp1().add(newBillCoinUserProperty.getProp2()).add(newBillCoinUserProperty.getProp3()).add(newBillCoinUserProperty.getProp4()).add(newBillCoinUserProperty.getProp5()));
        }
        BigDecimal tolerance = coinComparisonToleranceMap.getOrDefault(newBillCoinUserProperty.getCoinId(), BigDecimal.ZERO);
        dataCalResultDTO.setResult(dataCalResultDTO.getFactor1().subtract(dataCalResultDTO.getFactor2()).abs());
        dataCalResultDTO.setPass(dataCalResultDTO.getResult().compareTo(tolerance) <= 0);
        return dataCalResultDTO.isPass();
    }


    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeInitAssetsByAccountType(List<T> oldPropertyList, BillCoinTypeProperty newBillCoinTypeProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        if (CollectionUtils.isEmpty(oldPropertyList) || newBillCoinTypeProperty == null) {
            return false;
        }
        BigDecimal tolerance = coinComparisonToleranceMap.getOrDefault(newBillCoinTypeProperty.getCoinId(), BigDecimal.ZERO);
        BigDecimal oldSum = BigDecimal.ZERO;
        for (T odlProperty : oldPropertyList) {
            oldSum = oldSum.add(odlProperty.getProp1()).add(odlProperty.getProp2()).add(odlProperty.getProp3()).add(odlProperty.getProp4()).add(odlProperty.getProp5());
        }
        BigDecimal newSum = newBillCoinTypeProperty.getProp1().add(newBillCoinTypeProperty.getProp2()).add(newBillCoinTypeProperty.getProp3()).add(newBillCoinTypeProperty.getProp4()).add(newBillCoinTypeProperty.getProp5());
        dataCalResultDTO.setFactor1(oldSum);
        dataCalResultDTO.setFactor2(newSum);
        dataCalResultDTO.setResult(oldSum.subtract(newSum).abs());
        if (oldSum.subtract(newSum).abs().compareTo(tolerance) > 0) {
            log.info("CheckAssetSpotServiceImpl checkCoinTypeAssetsByAccountType failed , old sum {}, new sum {} , property {}", oldSum, newSum, JSONObject.toJSONString(newBillCoinTypeProperty));
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeAssetsByAccountType(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        if (oldProperty == null) {
            return Objects.isNull(newBillCoinTypeProperty) || newBillCoinTypeProperty.getChangePropSum().compareTo(BigDecimal.ZERO) == 0;
        }
        if (oldProperty.getChangeProp1().add(oldProperty.getChangeProp2()).compareTo(newBillCoinTypeProperty.getChangeProp1().add(newBillCoinTypeProperty.getChangeProp2())) != 0) {
            return false;
        }
        return true;
    }


    @Override
    public <T extends AbstractProperty> BigDecimal sumForInAll(T property) {
        if (property == null) {
            return BigDecimal.ZERO;
        }
        return property.getProp1().add(property.getProp2()).add(property.getProp3()).add(property.getProp4()).add(property.getProp5());
    }

    @Override
    public void cleanAndSetAssetsProperty(AbstractProperty abstractProperty, Byte accountType) {
        BigDecimal prop1 = abstractProperty.getProp1();
        BigDecimal prop2 = abstractProperty.getProp2();
        BigDecimal changeProp1 = abstractProperty.getChangeProp1();
        BigDecimal changeProp2 = abstractProperty.getChangeProp2();
        abstractProperty.cleaPropAndChangeProp();
        abstractProperty.setProp1(prop1);
        abstractProperty.setProp2(prop2);
        abstractProperty.setChangeProp1(changeProp1);
        abstractProperty.setChangeProp2(changeProp2);
    }
}
