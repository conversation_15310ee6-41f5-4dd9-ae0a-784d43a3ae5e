package com.upex.reconciliation.service.service.client.cex.utils;

import net.i2p.crypto.eddsa.EdDSAPrivateKey;
import net.i2p.crypto.eddsa.EdDSAPublicKey;
import net.i2p.crypto.eddsa.spec.EdDSANamedCurveTable;
import net.i2p.crypto.eddsa.spec.EdDSAParameterSpec;
import net.i2p.crypto.eddsa.spec.EdDSAPrivateKeySpec;
import net.i2p.crypto.eddsa.spec.EdDSAPublicKeySpec;

import org.apache.commons.codec.binary.Hex;

import java.io.*;
import java.security.*;
import java.security.spec.*;
import java.util.Base64;

public class Ed25519Utils {

    static {
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
    }

    /**
     * 生成 Ed25519 密钥对（使用安全随机种子）
     */
    public static KeyPair generateKeyPair() throws Exception {
        SecureRandom random = new SecureRandom();
        byte[] seed = new byte[32];
        random.nextBytes(seed);
        return generateKeyPairFromSeed(seed);
    }

    /**
     * 从指定 seed 生成 Ed25519 密钥对
     */
    public static KeyPair generateKeyPairFromSeed(byte[] seed) throws Exception {
        if (seed.length != 32) throw new IllegalArgumentException("Seed must be 32 bytes");

        EdDSAParameterSpec spec = EdDSANamedCurveTable.getByName(EdDSANamedCurveTable.ED_25519);
        EdDSAPrivateKeySpec privKeySpec = new EdDSAPrivateKeySpec(seed, spec);
        PrivateKey privateKey = new EdDSAPrivateKey(privKeySpec);

        EdDSAPublicKeySpec pubKeySpec = new EdDSAPublicKeySpec(privKeySpec.getA(), spec);
        PublicKey publicKey = new EdDSAPublicKey(pubKeySpec);

        return new KeyPair(publicKey, privateKey);
    }

    /**
     * 将公钥转换为 PEM 格式
     */
    public static String encodePublicKeyToPEM(PublicKey publicKey) throws IOException {
        byte[] encoded = publicKey.getEncoded(); // DER 编码的 SubjectPublicKeyInfo
        String base64 = Base64.getEncoder().encodeToString(encoded);
        StringBuilder pem = new StringBuilder();
        pem.append("-----BEGIN PUBLIC KEY-----\n");
        int len = base64.length();
        for (int i = 0; i < len; i += 64) {
            pem.append(base64.substring(i, Math.min(i + 64, len))).append("\n");
        }
        pem.append("-----END PUBLIC KEY-----");
        return pem.toString();
    }

    /**
     * 从 PEM 格式解析出公钥
     */
    public static PublicKey decodePublicKeyFromPEM(String pem) throws Exception {
        String base64Data = pem
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s+", "");

        byte[] decoded = Base64.getDecoder().decode(base64Data);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decoded);
        KeyFactory keyFactory = KeyFactory.getInstance("Ed25519", "BC");
        return keyFactory.generatePublic(keySpec);
    }

    /**
     * 将私钥转换为 PEM 格式
     */
    public static String encodePrivateKeyToPEM(PrivateKey privateKey) throws IOException {
        byte[] encoded = privateKey.getEncoded(); // DER 编码的 PKCS#8
        String base64 = Base64.getEncoder().encodeToString(encoded);
        StringBuilder pem = new StringBuilder();
        pem.append("-----BEGIN PRIVATE KEY-----\n");
        int len = base64.length();
        for (int i = 0; i < len; i += 64) {
            pem.append(base64.substring(i, Math.min(i + 64, len))).append("\n");
        }
        pem.append("-----END PRIVATE KEY-----\n");
        return pem.toString();
    }

    /**
     * 从 PEM 格式解析出私钥
     */
    public static PrivateKey decodePrivateKeyFromPEM(String pem) throws Exception {
        String base64Data = pem
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s+", "");

        byte[] decoded = Base64.getDecoder().decode(base64Data);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decoded);
        KeyFactory keyFactory = KeyFactory.getInstance("Ed25519", "BC");
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 获取原始 32 字节格式的公钥（十六进制字符串）
     */
    public static String getRawPublicKeyHex(PublicKey publicKey) {
        if (publicKey instanceof EdDSAPublicKey) {
            EdDSAPublicKey edPublicKey = (EdDSAPublicKey) publicKey;
            return Hex.encodeHexString(edPublicKey.getEncoded());
        } else {
            throw new IllegalArgumentException("Unsupported public key type");
        }
    }

    /**
     * 签名数据
     */
    public static byte[] sign(byte[] data, PrivateKey privateKey) throws Exception {
        Signature signature = Signature.getInstance("Ed25519", "BC");
        signature.initSign(privateKey);
        signature.update(data);
        return signature.sign();
    }

    /**
     * 验证签名
     */
    public static boolean verify(byte[] data, byte[] signature, PublicKey publicKey) throws Exception {
        Signature sig = Signature.getInstance("Ed25519", "BC");
        sig.initVerify(publicKey);
        sig.update(data);
        return sig.verify(signature);
    }

    /**
     * Hex 工具方法（替代 HexFormat.of()）
     */
    public static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    /**
     * 测试主函数
     */
    public static void main(String[] args) throws Exception {
        // 1. 生成密钥对
        KeyPair keyPair = generateKeyPair();

        // 2. 打印原始私钥和公钥
        System.out.println("Private Key (hex): " + bytesToHex(keyPair.getPrivate().getEncoded()));
        System.out.println("Public Key (hex):  " + bytesToHex(keyPair.getPublic().getEncoded()));

        // 3. 转换为 PEM 并打印
        String pemPubKey = encodePublicKeyToPEM(keyPair.getPublic());
        System.out.println("\nPEM Public Key:\n" + pemPubKey);

        String pemPrivKey = encodePrivateKeyToPEM(keyPair.getPrivate());
        System.out.println("PEM Private Key:\n" + pemPrivKey);

        // 4. 从 PEM 解析回密钥
        PublicKey parsedPubKey = decodePublicKeyFromPEM(pemPubKey);
        PrivateKey parsedPrivKey = decodePrivateKeyFromPEM(pemPrivKey);

        System.out.println("Parsed Public Key (hex): " + bytesToHex(parsedPubKey.getEncoded()));
        System.out.println("Parsed Private Key (hex): " + bytesToHex(parsedPrivKey.getEncoded()));

        // 5. 签名测试
        String message = "Hello, Ed25519!";
        byte[] data = message.getBytes();
        byte[] signature = sign(data, parsedPrivKey);

        // 6. 验证签名
        boolean valid = verify(data, signature, parsedPubKey);
        System.out.println("\nSignature is valid: " + valid);
    }
}
