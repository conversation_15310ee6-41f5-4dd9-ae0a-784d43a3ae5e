package com.upex.reconciliation.service.utils;

import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.DateUnitConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.core.util.CronExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

public class DateUtil {
    private static final Logger logger = LoggerFactory.getLogger(DateUtil.class);

    public static final Date DATABASE_DEFAULT_DATE = DateUtil.str2date("1971-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
    public static final int MILLIS_IN_A_DAY = 1000 * 60 * 60 * 24; // 时间计算格式

    public static final String FMT_yyyyMM = "yyyyMM";
    public static final String FMT_yyyyMMdd = "yyyyMMdd";
    public static final String FMT_yyyyMMddHHmm = "yyyyMMddHHmm";
    public static final String FMT_yyyyMMddHHmmss = "yyyyMMddHHmmss";
    public static final String FMT_yyyyMMddHHmmssSSS = "yyyyMMddHHmmssSSS";
    public static final String FMT_yyyy_MM_dd = "yyyy-MM-dd";
    public static final String FMT_yyyy_MM_dd_HH_mm = "yyyy-MM-dd HH:mm";
    public static final String FMT_yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd HH:mm:ss";
    public static final String FMT_yyyy_MM_dd_HH_mm_ss_SSS = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String FMT_HH_mm_ss = "HH:mm:ss";
    public static final String FMT_YYYY_MM_DD_DIAGONAL = "yyyy/MM/dd";

    public static final String FMT_yyyy_MM_dd_HH = "yyyy-MM-dd HH";

    public static final String FMT_yyyyMMddHH = "yyyyMMddHH";

    public static final Map<String, SimpleDateFormat> SDF_MAP = new HashMap<>();
    public static final SimpleDateFormat FORMAT_ZERO_ZONE = new SimpleDateFormat("yyyy-MM-dd 08:00:00");
    public static final String UTC_0 = "UTC+0";

    static {
        SDF_MAP.put(FMT_yyyyMM, new SimpleDateFormat(FMT_yyyyMM));
        FORMAT_ZERO_ZONE.setTimeZone(TimeZone.getTimeZone("GMT+0:00"));
    }

    public static final Map<String, Integer> CALENDAR_MAP = new HashMap<>();

    static {
        CALENDAR_MAP.put(DateUnitConstants.MONTH, Calendar.MONTH);
        CALENDAR_MAP.put(DateUnitConstants.DAY, Calendar.DATE);
        CALENDAR_MAP.put(DateUnitConstants.HOUR, Calendar.HOUR);
        CALENDAR_MAP.put(DateUnitConstants.MINUTE, Calendar.MINUTE);
        CALENDAR_MAP.put(DateUnitConstants.SECOND, Calendar.SECOND);
        CALENDAR_MAP.put(DateUnitConstants.MILLISECOND, Calendar.MILLISECOND);
    }

    /**
     * 时间单位转换
     */
    public static final Map<String, TimeUnit> TIME_UNIT_MAP = new HashMap<>();

    static {
        TIME_UNIT_MAP.put(DateUnitConstants.DAY, TimeUnit.DAYS);
        TIME_UNIT_MAP.put(DateUnitConstants.HOUR, TimeUnit.HOURS);
        TIME_UNIT_MAP.put(DateUnitConstants.MINUTE, TimeUnit.MINUTES);
        TIME_UNIT_MAP.put(DateUnitConstants.SECOND, TimeUnit.SECONDS);
        TIME_UNIT_MAP.put(DateUnitConstants.MILLISECOND, TimeUnit.MILLISECONDS);
    }


    /**
     * 字符日期串转换成DATE对象
     *
     * @param dateStr
     * @param pattern
     * @return
     */
    public static Date str2date(String dateStr, String pattern) {
        if (StringUtils.isEmpty(dateStr) || StringUtils.isEmpty(pattern)) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            return sdf.parse(dateStr);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return null;
        }
    }

    public static String getSimpleDateStr(Date date) {
        if (date == null) {
            return "";
        }
        return DateUtil.date2str(date, FMT_yyyy_MM_dd);
    }

    public static String getDefaultDateStr(Date date) {
        if (date == null) {
            return "";
        }
        return DateUtil.date2str(date, FMT_yyyy_MM_dd_HH_mm_ss);
    }

    public static String getMillisecondDateStr(Date date) {
        if (date == null) {
            return "";
        }
        return DateUtil.date2str(date, FMT_yyyy_MM_dd_HH_mm_ss_SSS);
    }


    public static Date getMillisecondDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        return DateUtil.str2dateWithLenientFalse(dateStr, FMT_yyyy_MM_dd_HH_mm_ss_SSS);
    }

    public static Date getDefaultDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return new Date();
        }
        return DateUtil.str2date(dateStr, FMT_yyyy_MM_dd_HH_mm_ss);
    }

    public static String getHHmmssDateStr(Date date) {
        if (date == null) {
            return "";
        }
        return DateUtil.date2str(date, FMT_HH_mm_ss);
    }

    public static String getFormatDate(final long date) {
        Date d = new Date(date);
        return getFormatDate(d, "yyyy-MM-dd HH:mm:ss");
    }

    public static String getFormatDate(final Date date, final String pattern) {
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        return format.format(date);
    }

    public static long getDefaultDateLong(String str) {
        if (StringUtils.isBlank(str)) {
            return 0L;
        }
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(FMT_yyyy_MM_dd_HH_mm_ss);
        try {
            date = simpleDateFormat.parse(str);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return date.getTime();
    }

    /**
     * DATE对象转换成字符日期串
     *
     * @param pattern
     * @return String
     */
    public static String date2str(Date date, String pattern) {
        if (date == null || StringUtils.isEmpty(pattern)) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            return sdf.format(date);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return null;
        }
    }

    public static String date2str(Date date) {
        return date2str(date, FMT_yyyy_MM_dd_HH_mm_ss);
    }

    /**
     * 返回系统当前的完整日期时间 <br>
     * 格式 1：2008-05-02 13:12:44 <br>
     * 格式 2：2008/05/02 13:12:44 <br>
     * 格式 3：2008年5月2日 13:12:44 <br>
     * 格式 4：2008年5月2日 13时12分44秒 <br>
     * 格式 5：2008年5月2日 星期五 13:12:44 <br>
     * 格式 6：2008年5月2日 星期五 13时12分44秒 <br>
     * 格式 7：20080502 <br>
     * 格式 8：20080502131244 <br>
     * 格式 9：2008-05-02 <br>
     * 格式 10：2008_05 <br>
     * 格式 11：2008 <br>
     * 格式 12：200805 <br>
     * 格式 13：2008-05 <br>
     * 格式 13：13 <br>
     * 格式 default：yyyyMMddHHmmss:20080502131244 <br>
     *
     * @param formatType (formatType) :格式代码号
     * @return 字符串
     */
    public static String get(int formatType, Date date) {
        SimpleDateFormat sdf = null;
        switch (formatType) {
            case 1:
                sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                break;
            case 2:
                sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                break;
            case 3:
                sdf = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss");
                break;
            case 4:
                sdf = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");
                break;
            case 5:
                sdf = new SimpleDateFormat("yyyy年MM月dd日 E HH:mm:ss");
                break;
            case 6:
                sdf = new SimpleDateFormat("yyyy年MM月dd日 E HH时mm分ss秒");
                break;
            case 7:
                sdf = new SimpleDateFormat("yyyyMMdd");
                break;
            case 8:
                sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                break;
            case 9:
                sdf = new SimpleDateFormat("yyyy-MM-dd");
                break;
            case 10:
                sdf = new SimpleDateFormat("yyyy_MM");
                break;
            case 11:
                sdf = new SimpleDateFormat("yyyy");
                break;
            case 12:
                sdf = new SimpleDateFormat("yyyyMM");
                break;
            case 13:
                sdf = new SimpleDateFormat("yyyy-MM");
                break;
            case 14:
                sdf = new SimpleDateFormat("yyyy年MM月dd日");
                break;
            case 15:
                sdf = new SimpleDateFormat("MM月dd日");
                break;
            case 16:
                sdf = new SimpleDateFormat("HH");
                break;
            default:
                sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                break;
        }
        sdf.setLenient(false);
        return sdf.format(date);
    }

    public static Date get(int formatType, Date date, String pattern) {
        String dateStr = get(formatType, date);
        return str2dateWithLenientFalse(dateStr, pattern);

    }


    public static boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            throw new IllegalArgumentException("Both dates must not be null");
        }

        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();

        cal1.setTime(date1);
        cal2.setTime(date2);

        // 比较年份
        if (cal1.get(Calendar.YEAR) != cal2.get(Calendar.YEAR)) {
            return false;
        }

        // 比较月份（注意Calendar的月份是从0开始的，所以需要+1）
        if (cal1.get(Calendar.MONTH) != cal2.get(Calendar.MONTH)) {
            return false;
        }

        // 比较日期
        if (cal1.get(Calendar.DAY_OF_MONTH) != cal2.get(Calendar.DAY_OF_MONTH)) {
            return false;
        }

        // 如果上述所有条件都满足，则两个日期是同一个自然日
        return true;
    }


    public static Long getGapDays(Date date1, Date date2) {

        // 将java.util.Date转换为java.time.LocalDate
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 计算两个LocalDate对象之间相差的天数
        long daysBetween = ChronoUnit.DAYS.between(localDate1, localDate2);
        return daysBetween;
    }


    /**
     * 获取指定时间0点
     *
     * @param date 指定时间
     * @return
     */
    public static Date getZeroOClick(Date date) {
        if (date == null) {
            return null;
        }
        return getZeroOClick(date.getTime());
    }

    /**
     * 获取指定时间0点
     *
     * @param date 指定时间
     * @return
     */
    public static Date getZeroOClick(Long date) {
        if (date == null) {
            // 指定时间为空，获取当前时间的0点
            date = System.currentTimeMillis();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DATE), 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }


    /**
     * 获取指定时间的零时区
     *
     * @param date 指定时间
     * @return
     */
    public static Date getZeroZoneTimeDefaultNow(Date date) {
        // 指定时间为空，获取当前时间的0点
        if (date == null) {
            date = new Date();
        }
        // 设置时区为零时区
        return DateUtil.getDefaultDate(FORMAT_ZERO_ZONE.format(date));
    }

    /**
     * 获取指定时间零时区0点
     *
     * @return
     */
    public static Date getZeroTimeZoneZeroDate() {
        return getZeroTimeZoneZeroDate(System.currentTimeMillis());
    }

    /**
     * 获取指定时间零时区0点
     *
     * @param date 指定时间
     * @return
     */
    public static Date getZeroTimeZoneZeroDate(Date date) {
        if (date == null) {
            return null;
        }
        return getZeroTimeZoneZeroDate(date.getTime());
    }

    /**
     * 获取指定时间零时区0点
     *
     * @param date 指定时间
     * @return
     */
    public static Date getZeroTimeZoneZeroDate(Long date) {
        if (date == null) {
            // 指定时间为空，获取当前时间的0点
            date = System.currentTimeMillis();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DATE), 8, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date utcDateFormat(Date date, String parser, String zoneId) {
        String time = DateTimeFormatter
                .ofPattern(parser)
                .format(date.toInstant().atZone(ZoneId.of(zoneId)).toLocalDateTime());
        return DateUtil.str2date(time, parser);
    }

    /**
     * 获取当前时间24点
     *
     * @param date
     * @return
     */
    public static Date get24Clock(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DATE), 23, 59,
                59);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 加 months 月
     *
     * @param date
     * @param months
     * @return
     */
    public static Date addMonth(Date date, int months) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, months);
        return calendar.getTime();
    }

    /**
     * 加 days 天
     *
     * @param date
     * @param days
     * @return
     */
    public static Date addDay(Date date, int days) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        calendar.setTime(date);
        calendar.add(Calendar.DATE, days);
        return calendar.getTime();
    }


    /**
     * 减 days 天
     *
     * @param date
     * @param days
     * @return
     */
    public static Date subtractDay(Date date, int days) {
        if (date == null) {
            return null;
        }
        return new Date(date.getTime() - days * BillConstants.ONE_DAY_MIL_SEC);
    }

    /**
     * 加 hours 小时
     *
     * @param date
     * @param hours
     * @return
     */
    public static Date addHour(Date date, int hours) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        calendar.setTime(date);
        calendar.add(Calendar.HOUR, hours);
        return calendar.getTime();
    }

    /**
     * 加 minute 分钟
     *
     * @param date
     * @param minute
     * @return
     */
    public static Date addMinute(Date date, int minute) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minute);
        return calendar.getTime();
    }

    /**
     * 加 seconds 秒
     *
     * @param date
     * @param seconds
     * @return
     */
    public static Date addSecond(Date date, int seconds) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        calendar.setTime(date);
        calendar.add(Calendar.SECOND, seconds);
        return calendar.getTime();
    }

    /**
     * 加 seconds 秒
     *
     * @param date
     * @param millisecond
     * @return
     */
    public static Date addMillisecond(Date date, int millisecond) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        calendar.setTime(date);
        calendar.add(Calendar.MILLISECOND, millisecond);
        return calendar.getTime();
    }

    /**
     * 字符日期串转换成DATE对象
     *
     * @param dateStr
     * @param pattern
     * @return
     */
    public static Date str2dateWithLenientFalse(String dateStr, String pattern) {
        if (StringUtils.isEmpty(dateStr) || StringUtils.isEmpty(pattern)) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            sdf.setLenient(false);
            return sdf.parse(dateStr);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return null;
        }
    }

    /**
     * 获取该月的第一天 0点
     *
     * @param date
     * @return
     */
    public static Date getMonthStartTime(Date date) {
        if (date == null) {
            date = new Date();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 1, 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }


    /**
     * 获取UTC+8, 0点时间
     *
     * @param dateParam 指定时间
     * @return
     */
    public static Date getDayStartTime(Long dateParam) {
        Long date = dateParam;
        if (date == null) {
            // 指定时间为空，获取当前时间的0点
            date = System.currentTimeMillis();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DATE), 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }


    /**
     * 获取该月的第一天 0点
     *
     * @param date
     * @return
     */
    public static Date getLastMonthEndTime(Date date) {
        Date newDate = getMonthStartTime(date);
        return DateUtil.addMinute(newDate, -5);
    }

    /**
     * 获取当月的指定天
     *
     * @param date
     * @param day
     * @return
     */
    public static Date getCurMonthSpecialDay(Date date, Integer day) {
        if (date == null) {
            date = new Date();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        Integer maxDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        if (day == 0 || maxDay.compareTo(day) < 0) {
            day = maxDay;
        }
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), day, 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当月的指定天 传入0 是月末
     *
     * @param date
     * @param day
     * @return
     */
    public static Date getNextMonthSpecialDay(Date date, Integer day) {
        if (date == null) {
            date = new Date();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        Integer maxDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        if (day == 0 || maxDay.compareTo(day) < 0) {
            day = maxDay;
        }
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), day, 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 取得两个时间之间相差的天数,参数类型为Date
     *
     * @param startDate 开始的时间
     * @param endDate   结束的时间
     * @return (endDate - startDate + 1)
     */
    public static int getDaysBetween(Date startDate, Date endDate) {
        Calendar calendarStart = Calendar.getInstance();
        calendarStart.setTime(startDate);
        calendarStart.set(calendarStart.get(Calendar.YEAR), calendarStart.get(Calendar.MONTH),
                calendarStart.get(Calendar.DATE), 0, 0, 0);
        calendarStart.set(Calendar.MILLISECOND, 0);

        Calendar calendarEnd = Calendar.getInstance();
        calendarEnd.setTime(endDate);
        calendarEnd.set(calendarEnd.get(Calendar.YEAR), calendarEnd.get(Calendar.MONTH), calendarEnd.get(Calendar.DATE),
                0, 0, 0);
        calendarEnd.set(Calendar.MILLISECOND, 0);

        long startTime = calendarStart.getTimeInMillis();
        long endTime = calendarEnd.getTimeInMillis();
        long totalDate = Math.abs((endTime - startTime)) / (MILLIS_IN_A_DAY);
        return (int) totalDate + 1;
    }

    /*
     * public static void main(String[] args) { Date a = DateUtil.getDefaultDate("2016-02-12 00:00:00"); Date b =
     * DateUtil.getCurMonthSpecialDay(a, 0); System.err.println(a); System.err.println(b); }
     */

    public static Date getDefaultDate(Long dateMillis) {
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        calendar.setTimeInMillis(dateMillis);
        return calendar.getTime();
    }

    /**
     * @return String yyyy-MM-dd HH:mm:ss
     * @Description: long 类型转成日期
     * @Param lo 毫秒数
     */
    public static String longToDate(long lo) {
        Date date = new Date(lo);
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sd.format(date);
    }

    /**
     * @return String yyyy-MM-dd HH:mm:ss.SSS
     * @Description: long 类型转成日期
     * @Param lo 毫秒数
     */
    public static String getMillisecondDateStr(long lo) {
        Date date = new Date(lo);
        SimpleDateFormat sd = new SimpleDateFormat(FMT_yyyy_MM_dd_HH_mm_ss_SSS);
        return sd.format(date);
    }

    /**
     * 获取当前时间整点
     *
     * @param time
     * @return
     */
    public static Date getTimeAtHour(Date time) {
        LocalDateTime localDateTime = time.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().withMinute(0).withSecond(0).withNano(0);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取最近的整数小时
     *
     * @return
     */
    public static Date getLastOneHour(Date dateTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateTime);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取最近的整数分钟
     *
     * @return
     */
    public static Date getLastTenMin() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE)) / 10 * 10);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取时间的分钟数
     *
     * @return
     */
    public static int getMinute(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MINUTE);
    }

    /**
     * 获取最近的五分钟
     *
     * @return
     */
    public static Date getLastFiveMin() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE)) / 5 * 5);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取最近的指定分钟
     *
     * @return
     */
    public static Date getLastMin(Date dateTime, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateTime);
        calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE)) / minute * minute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }



    /**
     * 获取最近的指定分钟
     *
     * @return
     */
    public static Date getLastMin(Long dateTime, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(dateTime);
        calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE)) / minute * minute);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定时间的最近五分钟
     *
     * @param dateTime
     * @return
     */
    public static Date getLastFiveMin(Date dateTime) {
        Calendar calendar = Calendar.getInstance();
        return getLastFiveMin(calendar, dateTime);
    }


    /**
     * 获取指定时间的最近五分钟
     *
     * @param dateTime
     * @return
     */
    public static Date getStartOfDay(Date dateTime) {
        Calendar calendar = Calendar.getInstance();
        return getStartOfDay(calendar, dateTime);
    }

    public static Date getStartOfDay(Calendar calendar, Date dateTime) {
        calendar.setTime(dateTime);
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }


    public static Date getLastFiveMin(Calendar calendar, Date dateTime) {
        calendar.setTime(dateTime);
        calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE)) / 5 * 5);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取最近的整数分钟
     *
     * @return
     */
    public static Date getLastMin(Date dateTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateTime);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 加指定类型的时间
     *
     * @param date
     * @param size
     * @return
     */
    public static Date add(Date date, int size, String type) {
        if (date == null) {
            return null;
        }
        if (size == 0) {
            return date;
        }
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        calendar.setTime(date);
        calendar.add(CALENDAR_MAP.get(type), size);
        return calendar.getTime();
    }

    /**
     * 获取指定间隔的时间戳
     *
     * @param size
     * @return
     */
    public static Long getTimestamp(int size, String type) {
        Calendar calendar = Calendar.getInstance(Locale.CHINA);
        Date beginDate = new Date();
        calendar.setTime(beginDate);
        calendar.add(CALENDAR_MAP.get(type), size);
        return calendar.getTimeInMillis() - beginDate.getTime();
    }

    /**
     * 获取UNIX时间（零时区）
     */
    public static String getUnixTime() {
        return new Date(System.currentTimeMillis()).toInstant().toString();
    }


    /**
     * 时间分片列表
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param endTime
     * @return
     */
    public static Map<Long, Long> getTimeSliceList(long beginTime, long endTime, String timeUnit, long sliceInterval) {
        if (sliceInterval <= 0) {
            throw new IllegalArgumentException("getTimeSliceList() sliceInterval cannot be less than or equal to 0");
        }
        // 定义返回结果
        Map<Long, Long> sliceMap = new ConcurrentHashMap<>();
        sliceInterval = TIME_UNIT_MAP.get(timeUnit).toMillis(sliceInterval);
        while (beginTime + sliceInterval < endTime) {
            sliceMap.put(beginTime, beginTime + sliceInterval);
            beginTime += sliceInterval;
        }
        sliceMap.put(beginTime, endTime);
        return sliceMap;
    }

    /**
     * 日期格式化字符串
     *
     * @param pattern
     * @param date
     * @return
     */
    public static String format2Str(String pattern, Date date) {
        SimpleDateFormat sdf = SDF_MAP.get(pattern);
        if (Objects.isNull(sdf)) {
            sdf = new SimpleDateFormat(pattern);
            SDF_MAP.put(pattern, sdf);
        }
        return sdf.format(date);
    }

    /**
     * 验证时间是否在指定范围内
     *
     * @param expression
     * @param date
     * @return
     * @throws ParseException
     */
    public static boolean isIncludeTimeRange(String expression, Date date) {
        try {
            CronExpression cronExpression = new CronExpression(expression);
            return cronExpression.isSatisfiedBy(date);
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 是否本月第一天
     *
     * @param date
     * @return
     */
    public static boolean isFirstDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH) == 1;
    }


    /**
     * 获取本月天数
     *
     * @param date
     * @return
     */

    public static int getDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取零时区时间
     *
     * @return
     */
    public static Date getTimeByZoneZero() {
        return getZeroTimeZoneZeroDate(utcDateFormat(new Date(), FMT_yyyy_MM_dd, UTC_0));
    }

    /**
     * 时间比较 date1 < date 2 = -1 date1=date2 =0 date1>date2 =1
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Integer compare(Date date1, Date date2) {
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(date1);
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date2);
        return calendar1.before(calendar2) ? -1 :
                calendar1.after(calendar2) ? 1 : 0;
    }

    public static Date getDayDateByTimeStr(String timeStr) {
        String dayTimeStr = getFormatDate(new Date(), FMT_yyyy_MM_dd) + " " + timeStr;
        return str2date(dayTimeStr, FMT_yyyy_MM_dd_HH_mm_ss);
    }

    /**
     * 获取今天是今年的第几天
     *
     * @return
     */
    public static int getDayOfYear() {
        LocalDate now = LocalDate.now();
        return now.getDayOfYear();
    }

    public static boolean isSameHour(Date date1, Date date2) {
        String s1 = DateUtil.date2str(date1,"yyyyMMddHH");
        String s2 = DateUtil.date2str(date2,"yyyyMMddHH");
        return (s1.equals(s2));
    }
}

