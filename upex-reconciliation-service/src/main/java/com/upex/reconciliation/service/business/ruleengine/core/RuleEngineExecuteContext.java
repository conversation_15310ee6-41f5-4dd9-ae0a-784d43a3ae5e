package com.upex.reconciliation.service.business.ruleengine.core;

import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.service.business.ruleengine.config.RuleEngineProcessConfig;
import com.upex.reconciliation.service.business.RuleEngineDataService;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 规则引擎执行环境容器
 */
@Data
public class RuleEngineExecuteContext {
    /***日志***/
    private Logger log = LoggerFactory.getLogger(RuleEngineExecuteContext.class);
    /***消息告警***/
    private AlarmNotifyService alarmNotifyService;
    /***数据能力***/
    private RuleEngineDataService ruleEngineDataService;
    /***流程配置***/
    private RuleEngineProcessConfig ruleEngineProcessConfig;
    /***流程交互容器****/
    private JSONObject context = new JSONObject();
    /***流程扩展配置***/
    private JSONObject extensionConfig = new JSONObject();
}
