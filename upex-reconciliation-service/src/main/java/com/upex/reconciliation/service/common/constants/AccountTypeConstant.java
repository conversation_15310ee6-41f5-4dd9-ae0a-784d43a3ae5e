package com.upex.reconciliation.service.common.constants;

/**
 * @Author: allen
 * @Date: 2020-05-14 11:22
 * @DES: 业务系统标示
 */
public final class AccountTypeConstant {

    /**
     * spot
     */
    public static final byte ACCOUNTTYPE_SPOT = (byte) 10;

    /**
     * 杠杆账户
     */
    public static final byte ACCOUNTTYPE_LEVER = (byte) 20;


    /**
     * otc账户
     */
    public static final byte ACCOUNTTYPE_OTC = (byte) 30;


    /**
     * 合约主账户
     */
    public static final byte ACCOUNTTYPE_SWAP_MAIN = (byte) 40;

    /**
     * 合约保证金账户
     */
    public static final byte ACCOUNTTYPE_SWAP_MARGIN = (byte) 41;

    /**
     * 对账时间间隔
     */
    public static final Long TIME_INTERVAL = 5 * 60 * 1000L;

    /**
     * 资产统计时间间隔 1小时
     */
    public static final Long STATISTICS_INTERNAL = 60 * 60 * 1000L;


}
