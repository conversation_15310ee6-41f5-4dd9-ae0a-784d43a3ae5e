package com.upex.reconciliation.service.common.constants.enums;

/**
 * 业务日志分级管理枚举，code越小越重要
 */
public enum LogLevelEnum {


    MAIN_PROCESS(1, "mainProcessLogOpen","主流程日志，包括各个module的入出口日志打印"),
    KEY_RESULT(2, "keyResultLogOpen","关键结果打印，一些细分步骤里的结果打印"),
    KEY_INPUT(3, "keyInputLogOpen","关键输入打印，一些细分步骤里的输入打印"),
    NON_KEY_RESULT(4, "keyInputLogOpen","非关键结果打印，一些细分步骤里的输入打印"),
    NON_KEY_INPUT(5, "keyInputLogOpen","非关键输入打印，一些细分步骤里的输入打印"),

    VITAL_DEBUG(6, "debugLogOpen","debug重要详细日志，重要的debug过程日志"),

    DEBUG(9, "debugLogOpen","debug详细日志，详细的debug过程日志"),

    FULL(10, "fullLogOpen","全量日志打印"),

            ;
    private int code;
    private String apolloKey;

    private String desc;

    LogLevelEnum(int code, String apolloKey, String desc) {
        this.code = code;
        this.apolloKey = apolloKey;
        this.desc = desc;
    }


    public int getCode() {
        return code;
    }

    public String getApolloKey() {
        return apolloKey;
    }

    public String getDesc() {
        return desc;
    }
}
