package com.upex.reconciliation.service.service.client.cex.enmus;

import lombok.Getter;

@Getter
public enum UserManagerStatusEnum {

    ENABLE(1,"启用"),
    DISABLE(0,"禁用");

    private Integer type;
    private String name;
     UserManagerStatusEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static UserManagerStatusEnum fromType(Integer type) {
        for (UserManagerStatusEnum userManagerStatusEnum : UserManagerStatusEnum.values()) {
            if (userManagerStatusEnum.getType().equals(type)) {
                return userManagerStatusEnum;
            }
        }
        return null;
    }
}
