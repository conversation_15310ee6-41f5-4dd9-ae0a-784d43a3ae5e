package com.upex.reconciliation.service.service.impl;

import com.upex.reconciliation.service.service.BillContractProfitCoinDetailService;
import com.upex.reconciliation.service.dao.entity.BillContractProfitCoinDetail;
import com.upex.reconciliation.service.dao.mapper.BillContractProfitCoinDetailMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_coin_detail(币种维度盈亏明细表)】的数据库操作Service实现
 * @createDate 2023-06-09 17:18:46
 */
@Service
public class BillContractProfitCoinDetailServiceImpl implements BillContractProfitCoinDetailService {

    @Resource(name = "billContractProfitCoinDetailMapperWrapper")
    private BillContractProfitCoinDetailMapper billContractProfitCoinDetailMapper;

    /**
     * @param accountType
     * @param accountParam
     * @param checkOkTime
     * @param profitType
     * @return
     */
    @Override
    public Map<Integer, BillContractProfitCoinDetail> getBillContractProfitCoinDetailMap(Byte accountType, String accountParam, Date checkOkTime, String profitType) {
        List<BillContractProfitCoinDetail> billContractProfitCoinDetailList = billContractProfitCoinDetailMapper.getBillContractProfitCoinDetailList(accountType, accountParam, checkOkTime, profitType);
        if (CollectionUtils.isEmpty(billContractProfitCoinDetailList)) {
            billContractProfitCoinDetailList = new ArrayList<>();
        }
        return billContractProfitCoinDetailList.stream().collect(Collectors.toMap(BillContractProfitCoinDetail::getCoinId, Function.identity(), (key1, key2) -> key2));
    }

    @Override
    public Date getLastCheckOkTime(Byte accountType, String accountParam, String profitType) {

        return billContractProfitCoinDetailMapper.getLastCheckOkTime(accountType, accountParam, profitType);
    }

    @Override
    public int batchInsert(Byte accountType, String accountParam, List<BillContractProfitCoinDetail> billContractProfitCoinDetailList) {
        if (CollectionUtils.isNotEmpty(billContractProfitCoinDetailList)) {
            //MonitorSummaryUtil.record(JobMonitorMetricNameEnum.BILL_CONTRACT_PROFIT_COIN_DETAIL_INSERT_COUNT.getName(), billContractProfitCoinDetails.size());
            return billContractProfitCoinDetailMapper.batchInsert(accountType, accountParam, billContractProfitCoinDetailList);
            //MonitorSummaryUtil.record(JobMonitorMetricNameEnum.BILL_CONTRACT_PROFIT_COIN_DETAIL_INSERT_TIME_SUMMARY.getName() ,new Date().getTime()-currentDate.getTime());
        }
        return 0;
    }

    @Override
    public int deleteByCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return billContractProfitCoinDetailMapper.deleteByCheckTime(accountType, accountParam, checkTime);
    }

    @Override
    public Boolean batchDelete(Long beginId, Long pageSize, Byte accountType, String accountParam) {
        return billContractProfitCoinDetailMapper.batchDelete(beginId, pageSize, accountType, accountParam);
    }

    @Override
    public List<BillContractProfitCoinDetail> selectListByAccountTypeAndCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return billContractProfitCoinDetailMapper.selectListByAccountTypeAndCheckTime(accountType, accountParam, checkTime);
    }
}




