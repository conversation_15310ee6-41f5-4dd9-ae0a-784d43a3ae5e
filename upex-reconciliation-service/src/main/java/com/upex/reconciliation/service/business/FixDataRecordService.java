package com.upex.reconciliation.service.business;


import com.upex.reconciliation.service.dao.entity.FixDataRecord;
import com.upex.reconciliation.service.dao.mapper.FixDataRecordMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class FixDataRecordService {

    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "fixDataRecordMapper")
    private FixDataRecordMapper fixDataRecordMapper;

    public int batchInsert(List<FixDataRecord> records) {
        if (CollectionUtils.isNotEmpty(records)) {
            return dbHelper.doDbOpInReconMaster(() -> fixDataRecordMapper.batchInsert(records));
        }
        return 0;
    }


}
