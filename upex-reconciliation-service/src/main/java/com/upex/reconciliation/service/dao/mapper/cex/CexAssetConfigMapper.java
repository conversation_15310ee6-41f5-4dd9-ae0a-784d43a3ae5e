package com.upex.reconciliation.service.dao.mapper.cex;

import com.upex.reconciliation.service.dao.cex.entity.CexAssetConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CexAssetConfigMapper {

    /**
     * 根据资产类型查询同步时间
     *
     * @param assetType 资产类型
     * @return CexAssetConfig 对象
     */
    CexAssetConfig selectByAssetType(@Param("cexType") Integer cexType,@Param("cexUserId")String cexUserId,@Param("assetType") Integer assetType);

    /**
     * 插入新的资产配置记录
     *
     * @param record 记录对象
     * @return 影响行数
     */
    int insert(CexAssetConfig record);

    int update(CexAssetConfig record);
}
