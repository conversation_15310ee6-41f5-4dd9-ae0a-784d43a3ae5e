package com.upex.reconciliation.service.service;


import com.upex.reconciliation.service.dao.entity.TbBillIdempotentRecord;
import com.upex.reconciliation.service.dao.mapper.TbBillIdempotentRecordMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TbBillIdempotentRecordService {


    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "tbBillIdempotentRecordMapper")
    private TbBillIdempotentRecordMapper tbBillIdempotentRecordMapper;



    public int batchInsert(List<TbBillIdempotentRecord> records, Byte accountType, String accountParam) {
        if(CollectionUtils.isNotEmpty(records)){
            return dbHelper.doDbOpInReconMaster(() -> tbBillIdempotentRecordMapper.batchInsert(records, accountType, accountParam));
        }
        return 0;
    }


    public List<TbBillIdempotentRecord> selectRangeCheckTimeRecord(Integer accountType,
                                                             String accountParam,
                                                             Long timeSlice) {
        return dbHelper.doDbOpInReconMaster(() -> tbBillIdempotentRecordMapper.selectRangeCheckTimeRecord(accountType, accountParam, timeSlice));
    }

    public Boolean batchDelete(Long beginId, Long pageSize, Byte accountType, String accountParam){
        return dbHelper.doDbOpInReconMaster(() -> tbBillIdempotentRecordMapper.batchDelete(beginId,pageSize,accountType,accountParam));
    }


}
