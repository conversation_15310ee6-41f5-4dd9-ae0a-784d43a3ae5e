package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.upex.margin.constant.MarginBizTypeEnum;
import com.upex.margin.facade.inner.InnerMarginAssetFeignClientWrapper;
import com.upex.margin.req.inner.UpdateAssetParams;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AbstractTransferService;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.enums.ProfitTransferTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.TRANSFER_SERVICE_ERROR;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LeverTransferServiceImpl extends AbstractTransferService {
    @Resource
    private InnerMarginAssetFeignClientWrapper marginAssetFeignClient;
    @Resource
    private AlarmNotifyService alarmNotifyService;


    @Override
    public Boolean transfer(AccountTypeEnum anEnum, BillContractProfitTransfer item, Date transferTime, Map<Integer, String> allCoinsMap) {
        try {
            UpdateAssetParams params = new UpdateAssetParams();
            params.setAccountId(item.getTransferInUserId());
            params.setBizType(getBillTypeEnum(item.getTransferType(), item.getTransferCount()));
            params.setBizOrderId(item.getId());
            params.setBizTime(transferTime);
            // 币种 动账金额
            params.setTokenId(allCoinsMap.get(item.getCoinId()));
            params.setFreeChange(item.getTransferCount());
            log.info("LeverTransferServiceImpl transfer params:{}", JSON.toJSONString(params));
            // 杠杆对应的动账接口
            return marginAssetFeignClient.updateAsset(params);
        } catch (Exception e) {
            alarmNotifyService.alarm(TRANSFER_SERVICE_ERROR, anEnum.getCode());
            throw e;
        }
    }

    private MarginBizTypeEnum getBillTypeEnum(int transferType, BigDecimal bigDecimal) {
        if (ProfitTransferTypeEnum.SYSTEM_LEVER_FEE.getCode() == (transferType)) {
            if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                return MarginBizTypeEnum.SYS_FEE_ACCOUNT_OUT;
            } else {
                return MarginBizTypeEnum.SYS_FEE_ACCOUNT_IN;
            }
        } else if (ProfitTransferTypeEnum.SYSTEM_LEVER_FEE_RESET.getCode() == (transferType)) {
            if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                return MarginBizTypeEnum.SYS_FEE_ACCOUNT_OUT;
            } else {
                return MarginBizTypeEnum.SYS_FEE_ACCOUNT_IN;
            }
        }
        return null;
    }
}
