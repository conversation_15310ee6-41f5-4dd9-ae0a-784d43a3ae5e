package com.upex.reconciliation.service.common.constants.enums;

import com.upex.mixcontract.common.literal.enums.BusinessTypeEnum;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 利润类型枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TransferFeeTypeEnum {
    SPOT_DEAL_FEE("spot_deal_fee", "现货手续费"),
    CONTRACT_DEAL_FEE("contract_deal_fee", "合约手续费"),
    UTA_SPOT_DEAL_FEE("uta_spot_deal_fee", "统一账户现货杠杆手续费"),
    UTA_CONTRACT_DEAL_FEE("uta_contract_deal_fee", "统一账户合约手续费"),
    ;
    private String code;
    private String desc;

    /**
     * 类型转枚举
     *
     * @param code
     * @return
     */
    public static TransferFeeTypeEnum toEnum(String code) {
        for (TransferFeeTypeEnum item : TransferFeeTypeEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static TransferFeeTypeEnum toEnum(BusinessTypeEnum businessTypeEnum) {
        if (businessTypeEnum.isSpot()) {
            return UTA_SPOT_DEAL_FEE;
        } else if (businessTypeEnum.isContract()) {
            return UTA_CONTRACT_DEAL_FEE;
        }
        throw new RuntimeException("获取TransferFeeTypeEnum类型错误，请检查配置！");
    }


    /**
     * 根据业务类型获取枚举
     *
     * @return
     * @Param isUtaContract 是否是统一账户合约 accountType 统一账户时使用
     */
    public static TransferFeeTypeEnum getTransferFeeTypeByAccountType(Byte accountType, Boolean isUtaContract) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        if (accountTypeEnum.isContract()) {
            return CONTRACT_DEAL_FEE;
        } else if (accountTypeEnum.isSpot()) {
            return SPOT_DEAL_FEE;
        } else if (accountTypeEnum.isUta()) {
            if (isUtaContract) {
                return UTA_CONTRACT_DEAL_FEE;
            } else {
                return UTA_SPOT_DEAL_FEE;
            }
        }
        throw new RuntimeException("获取类型错误，请检查配置！");
    }
}