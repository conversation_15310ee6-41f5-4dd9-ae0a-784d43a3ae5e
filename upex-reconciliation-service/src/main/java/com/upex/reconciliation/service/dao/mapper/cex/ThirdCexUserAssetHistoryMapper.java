package com.upex.reconciliation.service.dao.mapper.cex;

import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserAssetHistory;
import org.apache.ibatis.annotations.Param;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
@Resource
public interface ThirdCexUserAssetHistoryMapper {

    /**
     * 插入单条记录
     */
    int insert(ThirdCexUserAssetHistory record);

    /**
     * 批量插入记录
     */
    int insertBatch(@Param("list") List<ThirdCexUserAssetHistory> list);

    /**
     * 根据主键更新非空字段
     */
    int updateByPrimaryKeySelective(ThirdCexUserAssetHistory record);

    /**
     * 根据主键查询记录
     */
    ThirdCexUserAssetHistory selectByPrimaryKey(Long id);

    /**
     * 根据用户ID、交易所类型、账户类型和币种查询记录
     */
    List<ThirdCexUserAssetHistory> selectByCondition(@Param("cexUserId") String cexUserId,
                                                     @Param("cexType") Integer cexType,
                                                     @Param("accountType") Integer accountType,
                                                     @Param("coinName") String coinName);

    /**
     * 根据用户ID删除记录（谨慎使用）
     */
    int deleteByCexUserId(String cexUserId);

    List<ThirdCexUserAssetHistory> selectUserAssetByThirdAssetType(@Param("cexUserId") String cexUserId, @Param("cexType") Integer cexType,List<Integer> thirdAssetTypes, @Param("startTime") Date startTime);

    Date selectMaxCheckSyncTime(@Param("cexUserId") String cexUserId, @Param("cexType") Integer cexType,@Param("thirdAssetType") Integer thirdAssetType);
}
