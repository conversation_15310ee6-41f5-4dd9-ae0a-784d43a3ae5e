package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.ReconWithdrawCheckRecord;
import com.upex.reconciliation.service.dao.mapper.ReconWithdrawCheckRecordMapper;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.utils.thread.MdcContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.upex.reconciliation.service.common.constants.enums.BillRedisKeyEnum.RECON_WITHDRAWAL_CHECK_FINAL_RESULT;

@Slf4j
@Service
public class ReconWithdrawRecordService {

    @Resource
    private BillDbHelper billDbHelper;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    ReconWithdrawCheckRecordMapper reconWithdrawCheckRecordMapper;

    /**
     * 存盘提币结果，缓存记录中间提币状态，避免重复存盘，记录最后一次存盘结果
     *
     * @param record
     * @return
     */
    public int save(ReconWithdrawCheckRecord record) {
        Long userId = record.getUserId();
        Long orderId = record.getOrderId();
        String recordCode = record.getRecordCode();
        Integer isPass= record.getIsPass();

        // 构建缓存key
        String cacheKey = String.format(RECON_WITHDRAWAL_CHECK_FINAL_RESULT.getKey(), userId, orderId);

        // 从缓存中获取最新的提币结果
        Object cachedResult = redisTemplate.opsForValue().get(cacheKey);
        // 如果结果没有变化，则不进行更新
        if (Objects.nonNull(cachedResult) && isPass.equals(Integer.valueOf(cachedResult.toString()))) {
            log.info("ReconWithDrawResultNoChange,traceId:{}", record.getTraceId());
            return 0;
        }
        // 查询数据库中是否存在该记录
        ReconWithdrawCheckRecord existingRecord = billDbHelper.doDbOpInReconMaster(() -> reconWithdrawCheckRecordMapper.selectByUserIdAndOrderInfo(userId, orderId, recordCode));
        int result;
        if (existingRecord != null) {
            // 记录存在，执行更新操作
            record.setId(existingRecord.getId()); // 确保更新时使用正确的ID
            result = billDbHelper.doDbOpInReconMaster(() -> reconWithdrawCheckRecordMapper.updateByPrimaryKey(record));
            log.info("UpdatewithdrawResut,userId:{},orderId:{},recordCode:{},result:{},savecount:{}", userId, orderId, recordCode, isPass, result);
        } else {
            // 记录不存在，执行插入操作
            result = billDbHelper.doDbOpInReconMaster(() -> reconWithdrawCheckRecordMapper.insert(record));
            log.info("SavewithdrawResut,userId:{},orderId:{},recordCode:{},result:{},savecount:{} ", userId, orderId, recordCode, isPass, result);
        }
        // 更新缓存
        redisTemplate.opsForValue().set(cacheKey, isPass.toString(), ReconciliationApolloConfigUtils.getGlobalBillConfig().getWithdrawCheckSaveResultCacheTimeoutMinute(), TimeUnit.MINUTES);
        return result;

    }
    public List<Long> getUserIdByTime(Date startTime, Date endTime){
        return billDbHelper.doDbOpInReconMaster(() -> reconWithdrawCheckRecordMapper.getUserIdByTime(startTime,endTime));
    }
}

