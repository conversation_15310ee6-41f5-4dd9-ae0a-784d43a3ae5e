package com.upex.reconciliation.service.business.impl;

import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.BillCheckService;
import com.upex.reconciliation.service.business.MonitorNonInputDataRuleService;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.module.impl.BillTimeSliceCheckModule;
import com.upex.reconciliation.service.business.module.processor.MonitorCheckProcessor;
import com.upex.reconciliation.service.dao.entity.BillCoinProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty;
import com.upex.reconciliation.service.dao.entity.BillConfig;
import com.upex.reconciliation.service.model.MonitorCmdWrapper;
import com.upex.reconciliation.service.model.config.FeeCheckAccountTypeConfig;
import com.upex.reconciliation.service.model.config.FeeCheckConfig;
import com.upex.reconciliation.service.model.config.MonitorSceneTaskConfig;
import com.upex.reconciliation.service.model.dto.BillTimeSliceDTO;
import com.upex.reconciliation.service.model.enums.MonitorCmdEnum;
import com.upex.reconciliation.service.model.param.MonitorFeeAggregationData;
import com.upex.reconciliation.service.utils.CalculationCheckUtils;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 手续费对账
 */
@Service
@Slf4j
public class FeeAggregationMonitorDataRuleServiceImpl implements MonitorNonInputDataRuleService {

    @Resource
    private MonitorCheckProcessor monitorCheckProcessor;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;


    // 不需要手续费biz type，直接通过fee字段获取手续费
    private static final int BY_FEE_TYPE = 1;

    // 需要手续费biz type来获取手续费
    private static final int BY_BIZ_TYPE = 2;


    @Resource
    private BillEngineManager billEngineManager;


    @Override
    public void processScene(Long sceneId, Date bizTime, Byte accountType) {
        try {
            MonitorSceneTaskConfig monitorSceneTaskConfig = ReconciliationApolloConfigUtils.getMonitorSceneTaskConfig(sceneId);
            // 获取到业务线的所有配置
            FeeCheckConfig feeCheckConfig = ReconciliationApolloConfigUtils.getFeeCheckConfig();
            // 所有业务线的内容
            List<FeeCheckAccountTypeConfig> configs = feeCheckConfig.getConfigs();
            Map<Byte, FeeCheckAccountTypeConfig> configMap = configs.stream().collect(Collectors.toMap(FeeCheckAccountTypeConfig::getAccountType, Function.identity()));
            FeeCheckAccountTypeConfig feeCheckAccountTypeConfig = configMap.get(accountType);
            if (feeCheckAccountTypeConfig == null) {
                return;
            }
            BillLogicGroup billLogicGroup = billEngineManager.getBillLogicGroup(feeCheckAccountTypeConfig.getAccountType());
            BillTimeSliceCheckModule timeSliceCheckModule = (BillTimeSliceCheckModule) billLogicGroup.getTimeSliceModule();
            // 直接获取，当前对账时刻，用这个时间片进行手续费对账
            BillTimeSliceDTO billTimeSliceDTO = timeSliceCheckModule.getLastBillTimeSliceDTO();
            if (billTimeSliceDTO == null) {
                // 还未初始化，直接跳过
                return;
            }
            BillConfig billConfig = billTimeSliceDTO.getBillConfig();
            Date checkTime = billConfig.getCheckOkTime();
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(feeCheckAccountTypeConfig.getAccountType());
            BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountTypeEnum.getCode());
            // 所有币种成交额
            List<BillCoinTypeProperty> volomeBillCoinTypePropertyList = billTimeSliceDTO.getCoinTypePropertyMap().values().stream().filter(item -> feeCheckAccountTypeConfig.getVolumeBizTypes().contains(item.getBizType())).collect(Collectors.toList());
            Map<Integer, List<BillCoinTypeProperty>> volomeBillCoinTypePropertyMap = CollectionUtils.isEmpty(volomeBillCoinTypePropertyList) ? new HashMap<>() : volomeBillCoinTypePropertyList.stream().collect(Collectors.groupingBy(BillCoinTypeProperty::getCoinId));

            Map<Integer, BigDecimal> feeMap = new HashMap<>();
            // 所有币种手续费
            if (BY_FEE_TYPE == feeCheckAccountTypeConfig.getType()) {
                Map<Integer, BillCoinProperty> billCoinMap = billTimeSliceDTO.getCoinPropertyMap();
                feeMap = billCoinMap.entrySet()
                        .stream()
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                e -> {
                                    BillCoinProperty billCoinProperty = e.getValue();
                                    return billCoinProperty.getChangeFee().negate();
                                }
                        ));
            } else if (BY_BIZ_TYPE == feeCheckAccountTypeConfig.getType()) {
                Map<String, BillCoinTypeProperty> billCoinTypeMap = billTimeSliceDTO.getCoinTypePropertyMap();
                List<BillCoinTypeProperty> feeBillCoinTypePropertyList = (List<BillCoinTypeProperty>) billCoinTypeMap.values();
                Map<Integer, List<BillCoinTypeProperty>> feeBillCoinTypePropertyMap = CollectionUtils.isEmpty(feeBillCoinTypePropertyList) ? new HashMap<>() : feeBillCoinTypePropertyList.stream().collect(Collectors.groupingBy(BillCoinTypeProperty::getCoinId));
                feeMap = feeBillCoinTypePropertyMap.entrySet()
                        .stream()
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                e -> {
                                    List<BillCoinTypeProperty> billCoinTypePropertyList = e.getValue();
                                    return CalculationCheckUtils.sumChange(billCheckService, billCoinTypePropertyList, accountTypeEnum);
                                }
                        ));
            }

            // 取fee和成交map的并集
            Set<Integer> allCoinSet = new HashSet<>();
            allCoinSet.addAll(volomeBillCoinTypePropertyMap.keySet());
            allCoinSet.addAll(feeMap.keySet());

            // 以成交额进行循环，可以查到哪些币种有成交，但没有手续费
            for (Integer coinId : allCoinSet) {
                List<BillCoinTypeProperty> coinVolumeList = volomeBillCoinTypePropertyMap.get(coinId);
                // fee
                BigDecimal coinFee = feeMap.getOrDefault(coinId, BigDecimal.ZERO);
                // 总成交
                BigDecimal coinVolume = CalculationCheckUtils.sumChange(billCheckService, coinVolumeList, accountTypeEnum);
                MonitorFeeAggregationData feeAggregationData = new MonitorFeeAggregationData();
                feeAggregationData.setCoinId(coinId);
                feeAggregationData.setBizTime(checkTime);
                feeAggregationData.setAccountType(feeCheckAccountTypeConfig.getAccountType());
                feeAggregationData.setSceneName(feeCheckAccountTypeConfig.getName());
                Map<Integer, BigDecimal> minToleranceMapoleranceMap = feeCheckAccountTypeConfig.getMinToleranceMap();
                feeAggregationData.setMinTolerance(minToleranceMapoleranceMap.get(coinId) != null ? minToleranceMapoleranceMap.get(coinId) : feeCheckAccountTypeConfig.getDefaultMinTolerance());
                Map<Integer, BigDecimal> maxToleranceMapoleranceMap = feeCheckAccountTypeConfig.getMaxToleranceMap();
                feeAggregationData.setMaxTolerance(maxToleranceMapoleranceMap.get(coinId) != null ? maxToleranceMapoleranceMap.get(coinId) : feeCheckAccountTypeConfig.getDefaultMaxTolerance());
                feeAggregationData.setFeeCountChange(coinFee);
                feeAggregationData.setVolumeCountChange(coinVolume);
                // 仅对账有成交额的币种
                MonitorCmdWrapper monitorCmdWrapper = new MonitorCmdWrapper(monitorSceneTaskConfig, MonitorCmdEnum.AGGREGATION_TYPE, null, feeAggregationData, checkTime);
                monitorCheckProcessor.offerCommand(monitorCmdWrapper);
            }
            log.info("FeeAggregationMonitorDataRuleServiceImpl finished accountType {}, checkTime {}", feeCheckAccountTypeConfig.getAccountType(), DateUtil.getDefaultDateStr(checkTime));
        } catch (Exception e) {
            log.error("FeeAggregationMonitorDataRuleServiceImpl error", e);
        }
    }


}
