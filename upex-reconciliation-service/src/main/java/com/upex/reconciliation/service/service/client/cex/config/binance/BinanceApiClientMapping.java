package com.upex.reconciliation.service.service.client.cex.config.binance;

import com.binance.connector.client.common.ApiClient;
import com.upex.reconciliation.service.service.client.cex.enmus.BinanceApiClientLabel;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
public class BinanceApiClientMapping {

    private static final String DEFAULT = "default";



    public final Map<String, BinanceApiClientLabel> APICLIENT_MAPPING=new HashMap<>();

    public final Map<String, ApiClient> apiClientMap=new HashMap<>();

    @Resource(name = "defaultApiClient")
    ApiClient apiClient;

    @Resource(name = "fapiClient")
    ApiClient fapiClient;

    @Resource(name = "papiClient")
    ApiClient papiClient;

    @Resource(name = "sapiClient")
    ApiClient sapiClient;

    @Resource(name = "dapiClient")
    ApiClient dapiClient;
    @PostConstruct
    public void init() {
        APICLIENT_MAPPING.put(DEFAULT, BinanceApiClientLabel.defaultapi);
        APICLIENT_MAPPING.put(BinancePathConfig.queryUContractAccountInfoAccount, BinanceApiClientLabel.fapi);
        APICLIENT_MAPPING.put(BinancePathConfig.queryCoinContractAccountInfoAccount, BinanceApiClientLabel.dapi);
        apiClientMap.put(DEFAULT,apiClient);
        apiClientMap.put(BinancePathConfig.queryUContractAccountInfoAccount,fapiClient);
        apiClientMap.put(BinancePathConfig.queryCoinContractAccountInfoAccount,dapiClient);
    }

    public BinanceApiClientLabel getHost(String path) {
        return APICLIENT_MAPPING.getOrDefault(path, APICLIENT_MAPPING.get(DEFAULT));
    }

    public ApiClient getApiClient(String path) {
        return apiClientMap.getOrDefault(path, apiClientMap.get(DEFAULT));
    }
}
