package com.upex.reconciliation.service.business.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.upex.financial.dto.object.req.FinancialAccountListQueryParam;
import com.upex.financial.dto.rsp.bill.AccountInfoVO;
import com.upex.financial.facade.bill.FinancialAssetsInnerFeign;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.FinancialVirtualAccountBizService;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.dao.entity.FinancialVirtualAccount;
import com.upex.reconciliation.service.dao.entity.FinancialVirtualAccountSnapshot;
import com.upex.reconciliation.service.service.FinancialVirtualAccountService;
import com.upex.reconciliation.service.service.FinancialVirtualAccountSnapshotService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.GroupByKeyUtil;
import com.upex.reconciliation.service.utils.TimeSliceCalcUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FinancialVirtualAccountBizServiceImpl implements FinancialVirtualAccountBizService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource
    private FinancialVirtualAccountService financialVirtualAccountService;
    @Resource
    private FinancialVirtualAccountSnapshotService financialVirtualAccountSnapshotService;
    @Resource
    private FinancialAssetsInnerFeign financialAssetsInnerFeign;
    @Resource
    private AlarmNotifyService alarmNotifyService;

    @Override
    public int saveHandler(List<FinancialVirtualAccount> financialVirtualAccounts) {
        if (CollectionUtils.isEmpty(financialVirtualAccounts)) {
            return 0;
        }
        List<FinancialVirtualAccount> dbFinancialVirtualAccounts = financialVirtualAccountService.listFinancialVirtualAccountByIds(financialVirtualAccounts);
        if (CollectionUtils.isEmpty(dbFinancialVirtualAccounts)) {
            return financialVirtualAccountService.batchSave(financialVirtualAccounts);
        }
        Set<String> dbUniqKeySet = dbFinancialVirtualAccounts.stream().map(financialVirtualAccount -> GroupByKeyUtil.groupByBusinessLineAndUserIdAndBizId(financialVirtualAccount.getUserId(), financialVirtualAccount.getGroupType(), financialVirtualAccount.getCoinId())).collect(Collectors.toSet());
        List<FinancialVirtualAccount> insertFinancialVirtualAccounts = new ArrayList<>();
        List<FinancialVirtualAccount> alarmFinancialVirtualAccounts = new ArrayList<>();

        for (FinancialVirtualAccount financialVirtualAccount : financialVirtualAccounts) {
            if (dbUniqKeySet.contains(GroupByKeyUtil.groupByBusinessLineAndUserIdAndBizId(financialVirtualAccount.getUserId(), financialVirtualAccount.getGroupType(), financialVirtualAccount.getCoinId()))) {
                // 存在，不做处理
                alarmFinancialVirtualAccounts.add(financialVirtualAccount);
            }else {
                insertFinancialVirtualAccounts.add(financialVirtualAccount);
            }
        }
        if (CollectionUtils.isNotEmpty(alarmFinancialVirtualAccounts)) {
            log.info("FinancialVirtualAccountBizServiceImpl saveHandler 存在的虚拟账户：{}", JSONObject.toJSONString(alarmFinancialVirtualAccounts));
        }
        // 插入逻辑，直接插入实时余额表
        return financialVirtualAccountService.batchSave(insertFinancialVirtualAccounts);
    }

    @Override
    public void modifyHandler(List<FinancialVirtualAccount> financialVirtualAccounts) {
        if (CollectionUtils.isEmpty(financialVirtualAccounts)) {
            return;
        }
        // 查询数据库，获取最新余额
        List<FinancialVirtualAccount> dbFinancialVirtualAccounts = financialVirtualAccountService.listFinancialVirtualAccountByIds(financialVirtualAccounts);
        if (CollectionUtils.isEmpty(dbFinancialVirtualAccounts)) {
            log.error("modifyHandler 数据库中不存在该虚拟账户，无法修改");
            saveHandler(financialVirtualAccounts);
            return;
        }

        // 插入理财快照表
        List<FinancialVirtualAccount> insertFinancialSnapshotList = new ArrayList<>();
        // 修改理财余额
        List<FinancialVirtualAccount> updateFinancialList = new ArrayList<>();
        // 插入理财余额
        List<FinancialVirtualAccount> insertFinancialList = new ArrayList<>();

        Map<String, FinancialVirtualAccount> dbFinancialVirtualAccountsMap = dbFinancialVirtualAccounts.stream().collect(Collectors.toMap(e -> GroupByKeyUtil.groupByBusinessLineAndUserIdAndBizId(e.getUserId(), e.getGroupType(), e.getCoinId()), Function.identity(), (a, b) -> b));
        for (FinancialVirtualAccount financialVirtualAccount : financialVirtualAccounts) {
            FinancialVirtualAccount dbFinancialVirtualAccount = dbFinancialVirtualAccountsMap.get(GroupByKeyUtil.groupByBusinessLineAndUserIdAndBizId(financialVirtualAccount.getUserId(), financialVirtualAccount.getGroupType(), financialVirtualAccount.getCoinId()));
            if (dbFinancialVirtualAccount == null) {
                log.error("modifyHandler 数据库中不存在该虚拟账户，可能有插入消息丢失或者顺序错乱，无法修改， financialVirtualAccount:{}", JSONObject.toJSONString(financialVirtualAccount));
                insertFinancialList.add(financialVirtualAccount);
                continue;
            }
            if (Objects.nonNull(financialVirtualAccount.getOldTotalBalance())) {
                if (dbFinancialVirtualAccount.getTotalBalance().compareTo(financialVirtualAccount.getOldTotalBalance()) != 0) {
                    log.error("modifyHandler 数据库中总金额不一致，可能有修改消息丢失或者顺序错乱，无法修改， financialVirtualAccount:{}, dbFinancialVirtualAccount:{}", JSONObject.toJSONString(financialVirtualAccount), JSONObject.toJSONString(dbFinancialVirtualAccount));
                }
            }
            if (Objects.nonNull(financialVirtualAccount.getOldUpdateTime())) {
                if (!dbFinancialVirtualAccount.getSourceUpdateTime().equals(financialVirtualAccount.getOldUpdateTime())) {
                    log.error("modifyHandler 数据库中修改时间不一致，可能有修改消息丢失或者顺序错乱，无法修改 financialVirtualAccount:{}, dbFinancialVirtualAccount:{}", JSONObject.toJSONString(financialVirtualAccount), JSONObject.toJSONString(dbFinancialVirtualAccount));
                }
            }
            if (financialVirtualAccount.getSourceUpdateTime().compareTo(dbFinancialVirtualAccount.getSourceUpdateTime()) < 0) {
                log.info("modifyHandler 消息的修改时间小于数据库中最新时间，丢弃数据，无法修改 financialVirtualAccount:{}, dbFinancialVirtualAccount:{}", JSONObject.toJSONString(financialVirtualAccount), JSONObject.toJSONString(dbFinancialVirtualAccount));
                continue;
            }
            updateFinancialList.add(financialVirtualAccount);
            if (!DateUtil.isSameHour(financialVirtualAccount.getSourceUpdateTime(), dbFinancialVirtualAccount.getSourceUpdateTime())) {
                if (financialVirtualAccount.getSourceUpdateTime().compareTo(dbFinancialVirtualAccount.getSourceUpdateTime()) < 0) {
                    log.info("modifyHandler 消息的修改时间小于数据库中最新时间，丢弃数据");
                    continue;
                }
                insertFinancialSnapshotList.add(dbFinancialVirtualAccount);
            }
        }
        // 修改逻辑，跨小时修改实时余额表，插入快照表
        // 不跨小时，只修改实时余额表
        crossHourDataHandler(insertFinancialSnapshotList, updateFinancialList);
        saveHandler(insertFinancialList);
    }

    @Override
    public int fixHandler(Long minId, Integer pageSize) {
        FinancialAccountListQueryParam param = new FinancialAccountListQueryParam();
        param.setMinId(minId);
        param.setPageSize(pageSize);
        int syncCount = 0;
        try {
            while (true) {
                List<AccountInfoVO> accountInfoList = financialAssetsInnerFeign.queryUserAccountList(param);
                if (CollectionUtil.isNotEmpty(accountInfoList)) {
                    syncCount += accountInfoList.size();
                    saveOrUpdate(accountInfoList);
                }
                if (CollectionUtil.isEmpty(accountInfoList) || accountInfoList.size() < pageSize) {
                    break;
                }
                param.setMinId(accountInfoList.get(accountInfoList.size() - 1).getId());
            }
        } catch (Exception e) {
            alarmNotifyService.alarmException(String.format("同步理财余额表失败，minId:%s", param.getMinId()));
            log.error("fixHandler minId:{}, pageSize:{}  error:", param.getMinId(), pageSize, e);
        }
        log.info("FinancialVirtualAccountBizServiceImpl fixHandler minId:{}, pageSize:{} syncCount:{}", minId, pageSize, syncCount);
        return syncCount;
    }

    /**
     * 批量保存或者更新
     *
     * @param accountInfoList
     * @return
     */
    private int saveOrUpdate(List<AccountInfoVO> accountInfoList) {
        if (CollectionUtil.isEmpty(accountInfoList)) {
            return 0;
        }
        List<FinancialVirtualAccount> financialVirtualAccountList = accountInfoList.stream().map(this::converToFinancialVirtualAccount).collect(Collectors.toList());
        return financialVirtualAccountService.batchSaveOrUpdate(financialVirtualAccountList);
    }

    /**
     * 转换
     *
     * @param accountInfo
     * @return
     */
    private FinancialVirtualAccount converToFinancialVirtualAccount(AccountInfoVO accountInfo) {
        FinancialVirtualAccount account = new FinancialVirtualAccount();
        account.setId(accountInfo.getId());
        account.setUserId(accountInfo.getAccountId());
        account.setCoinId(accountInfo.getCoinId());
        account.setCoinName(accountInfo.getCoinName());
        account.setGroupType(accountInfo.getGroupType());
        account.setTotalBalance(accountInfo.getTotalBalance());
        account.setSourceCreateTime(accountInfo.getCreateTime());
        account.setSourceUpdateTime(accountInfo.getUpdateTime());
        account.setVersion(accountInfo.getVersion());
        account.setPreSettleInterest(accountInfo.getPreSettleInterest());
        Date now = new Date();
        account.setCreateTime(now);
        account.setUpdateTime(now);
        return account;
    }

    /**
     * 跨小时修改实时余额表，插入快照表
     *
     * @param insertFinancialSnapshotList 插入快照表集合
     * @param updateFinancialList         修改实时余额表集合
     * <AUTHOR>
     * @date 2024/12/17 16:16
     */
    private void crossHourDataHandler(List<FinancialVirtualAccount> insertFinancialSnapshotList, List<FinancialVirtualAccount> updateFinancialList) {
        dbHelper.doDbOpInReconMasterTransaction(() -> {
            if (CollectionUtils.isNotEmpty(updateFinancialList)) {
                financialVirtualAccountService.batchUpdateFinancialVirtualAccount(getFinancialVirtualAccountById(updateFinancialList));
            }
            if (CollectionUtils.isNotEmpty(insertFinancialSnapshotList)) {
                List<FinancialVirtualAccountSnapshot> financialVirtualAccountSnapshots = insertFinancialSnapshotList.stream().map(this::buildFinancialVirtualAccountSnapshot).collect(Collectors.toList());
                financialVirtualAccountSnapshotService.batchSave(getAccountSnapshotByUk(financialVirtualAccountSnapshots));
            }
            return null;
        });
    }

    public static List<FinancialVirtualAccount> getFinancialVirtualAccountById(List<FinancialVirtualAccount> accounts) {
        return accounts.stream()
                .collect(Collectors.toMap(
                        FinancialVirtualAccount::getId,
                        Function.identity(),
                        (account1, account2) -> account1.getSourceUpdateTime().before(account2.getSourceUpdateTime()) ? account2 : account1
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
    }

    public static List<FinancialVirtualAccountSnapshot> getAccountSnapshotByUk(List<FinancialVirtualAccountSnapshot> accounts) {
        return accounts.stream()
                .collect(Collectors.toMap(
                        account -> GroupByKeyUtil.group(account.getGroupType(), account.getUserId(), account.getCoinId(), account.getSnapshotTime()),
                        Function.identity(),
                        (account1, account2) -> account1.getSourceUpdateTime().before(account2.getSourceUpdateTime()) ? account1 : account2
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
    }

    private FinancialVirtualAccountSnapshot buildFinancialVirtualAccountSnapshot(FinancialVirtualAccount financialVirtualAccount) {
        FinancialVirtualAccountSnapshot snapshot = new FinancialVirtualAccountSnapshot();
        Date createDate = new Date();
        snapshot.setUserId(financialVirtualAccount.getUserId());
        snapshot.setCoinId(financialVirtualAccount.getCoinId());
        snapshot.setCoinName(financialVirtualAccount.getCoinName());
        snapshot.setGroupType(financialVirtualAccount.getGroupType());
        snapshot.setTotalBalance(financialVirtualAccount.getTotalBalance());
        long timeOffset = TimeSliceCalcUtils.getTimeSlice(financialVirtualAccount.getSourceUpdateTime().getTime(), BillConstants.ONE_HOUR_MIL);
        snapshot.setSnapshotTime(new Date(timeOffset));
        snapshot.setSourceCreateTime(financialVirtualAccount.getSourceCreateTime());
        snapshot.setSourceUpdateTime(financialVirtualAccount.getSourceUpdateTime());
        snapshot.setCreateTime(createDate);
        snapshot.setUpdateTime(createDate);
        snapshot.setVersion(financialVirtualAccount.getVersion());
        snapshot.setPreSettleInterest(financialVirtualAccount.getPreSettleInterest());

        return snapshot;
    }
}
