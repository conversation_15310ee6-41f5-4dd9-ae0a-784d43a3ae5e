package com.upex.reconciliation.service.business.cex;

import com.alibaba.google.common.collect.Lists;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.CexAssetConfig;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexTransferHistory;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.model.config.ApolloThirdCexAssetConfig;
import com.upex.reconciliation.service.service.CexTransferHistoryService;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.ParentSubTransferHistoryReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import com.upex.reconciliation.service.service.client.cex.enmus.CexAssetHistoryTypeEnum;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CexParentSubTransferHistoryBizService extends AbstractCexAssetSyncHistory implements ICexAssetSyncHistory{

    @Resource
    private CexApiService cexApiService;

    @Resource
    CexTransferHistoryService cexTransferHistoryService;

    @Resource
    BillDbHelper billDbHelper;

    public List<ThirdCexTransferHistory>  queryParentSubTransferRecordAndSync(ParentSubTransferHistoryReq parentSubTransferHistoryReq) {
        CommonRes<List<ThirdCexTransferHistory>> res = cexApiService.queryParentSubTransferRecord(parentSubTransferHistoryReq);
        List<ThirdCexTransferHistory> cexTransferHistories =new ArrayList<>();
        if (res.getSuccess() && CollectionUtils.isNotEmpty(res.getData())) {
            log.info("QueryParentSubTransferRecord,Size:{}", res.getData().size());
            for(ThirdCexTransferHistory cexTransferHistory : res.getData()){
                cexTransferHistory.setCheckSyncTime(parentSubTransferHistoryReq.getCheckSyncTime());
                cexTransferHistories.add(cexTransferHistory);
            }
        }
        return cexTransferHistories;
    }


    @Override
    public void syncAssetHistory(CexAssetConfig cexAssetConfig,ThirdCexUserConfig userConfig, Date startTime, Date endTime, Date checkSyncTime) {
        ParentSubTransferHistoryReq parentSubTransferHistoryReq = new ParentSubTransferHistoryReq(userConfig.getCexType(), userConfig.getCexUserId(), userConfig.getApiKey(), userConfig.getApiKeyPrivate(), startTime, endTime,checkSyncTime);
        List<ThirdCexTransferHistory> cexTransferHistories =queryParentSubTransferRecordAndSync(parentSubTransferHistoryReq);
        billDbHelper.doDbOpInReconMasterTransaction(()-> {
            ApolloThirdCexAssetConfig apolloThirdCexAssetConfig = ReconciliationApolloConfigUtils.getThirdCexAssetConfig();
            if(CollectionUtils.isNotEmpty(cexTransferHistories)) {
                List<List<ThirdCexTransferHistory>> partitionList = Lists.partition(cexTransferHistories, apolloThirdCexAssetConfig.getSqlInsertSize());
                for (List<ThirdCexTransferHistory> partition : partitionList) {
                    cexTransferHistoryService.batchInsert(partition);
                }
                log.info("InsertParentSubTransferRecord,Size:{}", partitionList.size());
            }
            saveModAssetConfig(cexAssetConfig, CexAssetHistoryTypeEnum.PARENT_SUB_TRANSFER, userConfig, checkSyncTime);
            return null;
        });
    }

    @Override
    public CexAssetHistoryTypeEnum getAssetHistoryType() {
        return CexAssetHistoryTypeEnum.PARENT_SUB_TRANSFER;
    }
}
