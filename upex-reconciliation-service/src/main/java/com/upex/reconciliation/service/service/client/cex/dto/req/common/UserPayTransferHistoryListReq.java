package com.upex.reconciliation.service.service.client.cex.dto.req.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class UserPayTransferHistoryListReq extends CommonReq{

    private Date startTime;

    private Date endTime;

    private Date checkSyncTime;


    public UserPayTransferHistoryListReq(Integer cexType, String cexUserId, String apiKey, String privateKey, Date startTime, Date endTime,Date checkSyncTime) {
        super(cexType, cexUserId, apiKey, privateKey);
        this.startTime = startTime;
        this.endTime = endTime;
        this.checkSyncTime = checkSyncTime;
    }
}
