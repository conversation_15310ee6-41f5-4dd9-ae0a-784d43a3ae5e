package com.upex.reconciliation.service.business.impl;

import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.BillCheckService;
import com.upex.reconciliation.service.business.MonitorNonInputDataRuleService;
import com.upex.reconciliation.service.business.module.processor.MonitorCheckProcessor;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.dao.entity.BillCoinProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty;
import com.upex.reconciliation.service.dao.entity.BillConfig;
import com.upex.reconciliation.service.model.MonitorCmdWrapper;
import com.upex.reconciliation.service.model.config.CommissionCheckAccountTypeConfig;
import com.upex.reconciliation.service.model.config.CommissionCheckConfig;
import com.upex.reconciliation.service.model.config.FeeCheckAccountTypeConfig;
import com.upex.reconciliation.service.model.config.MonitorSceneTaskConfig;
import com.upex.reconciliation.service.model.enums.MonitorCmdEnum;
import com.upex.reconciliation.service.model.param.MonitorCommissionAggregationData;
import com.upex.reconciliation.service.service.BillCoinPropertyService;
import com.upex.reconciliation.service.service.BillCoinTypePropertyService;
import com.upex.reconciliation.service.service.BillConfigService;
import com.upex.reconciliation.service.utils.CalculationCheckUtils;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 返佣对账
 */
@Service
@Slf4j
public class CommissionAggregationMonitorDataRuleServiceImpl implements MonitorNonInputDataRuleService {
    @Resource
    private BillCoinTypePropertyService billCoinTypePropertyService;

    @Resource
    private MonitorCheckProcessor monitorCheckProcessor;

    @Resource
    private BillConfigService billConfigService;

    @Resource
    private BillCoinPropertyService billCoinPropertyService;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;


    // 不需要手续费biz type，直接通过fee字段获取手续费
    private static final int BY_FEE_TYPE = 1;

    // 需要手续费biz type来获取手续费
    private static final int BY_BIZ_TYPE = 2;


    @Override
    public void processScene(Long sceneId, Date bizTime, Byte accountType) {
        try {
            MonitorSceneTaskConfig monitorSceneTaskConfig = ReconciliationApolloConfigUtils.getMonitorSceneTaskConfig(sceneId);
            // 获取到业务线的所有配置
            CommissionCheckConfig feeCheckConfig = ReconciliationApolloConfigUtils.getCommissionCheckConfig();
            List<FeeCheckAccountTypeConfig> feeConfigs = feeCheckConfig.getFeeConfigs();
            List<CommissionCheckAccountTypeConfig> commissionConfigs = feeCheckConfig.getCommissionConfigs();
            // 前一天早上8点的时间
            Date lastDayBizTime = new Date(bizTime.getTime() - BillConstants.ONE_DAY_MIL_SEC);
            Date lastTwoDayBizTime = new Date(bizTime.getTime() - BillConstants.TWO_DAY_MIL_SEC);

            Map<Integer, BigDecimal> feeTotalMap = new HashMap<>();
            Map<Integer, BigDecimal> commissionTotalMap = new HashMap<>();

            // 手续费统计
            for (FeeCheckAccountTypeConfig feeCheckAccountTypeConfig : feeConfigs) {
                AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(feeCheckAccountTypeConfig.getAccountType());
                BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountTypeEnum.getCode());
                BillConfig billConfig = billConfigService.selectByTypeAndParam(feeCheckAccountTypeConfig.getAccountType(), feeCheckAccountTypeConfig.getAccountParam());
                if (billConfig.getCheckOkTime().compareTo(bizTime) < 0) {
                    // 还未对到当前时刻，直接跳出
                    log.info("CommissionAggregationMonitorDataRuleServiceImpl fast-fail return , accountType {} checkTime {} before {}", accountTypeEnum.getCode(), DateUtil.getDefaultDateStr(billConfig.getCheckOkTime()), bizTime);
                    return;
                }
                if (BY_FEE_TYPE == feeCheckAccountTypeConfig.getType()) {
                    List<BillCoinProperty> currentBillCoinPropertyList = billCoinPropertyService.selectCheckTimeRecord(Integer.valueOf(feeCheckAccountTypeConfig.getAccountType()), feeCheckAccountTypeConfig.getAccountParam(), lastDayBizTime);
                    List<BillCoinProperty> lastDayBillCoinPropertyList = billCoinPropertyService.selectCheckTimeRecord(Integer.valueOf(feeCheckAccountTypeConfig.getAccountType()), feeCheckAccountTypeConfig.getAccountParam(), lastTwoDayBizTime);
                    Map<Integer, BillCoinProperty> currentBillCoinPropertyMap = currentBillCoinPropertyList.stream().collect(Collectors.toMap(BillCoinProperty::getCoinId, Function.identity()));
                    Map<Integer, BillCoinProperty> lastDayBillCoinPropertyMap = lastDayBillCoinPropertyList.stream().collect(Collectors.toMap(BillCoinProperty::getCoinId, Function.identity()));
                    for (Integer coinId : currentBillCoinPropertyMap.keySet()) {
                        BillCoinProperty billCoinProperty = currentBillCoinPropertyMap.get(coinId);
                        BillCoinProperty lastBillCoinProperty = lastDayBillCoinPropertyMap.get(coinId) != null ? lastDayBillCoinPropertyMap.get(coinId) : new BillCoinProperty();
                        BigDecimal feeTotal = billCoinProperty.getFee().subtract(lastBillCoinProperty != null ? lastBillCoinProperty.getFee() : BigDecimal.ZERO).negate();
                        BigDecimal value = feeTotalMap.computeIfAbsent(coinId, item -> BigDecimal.ZERO);
                        feeTotalMap.put(coinId, value.add(feeTotal));
                    }
                } else if (BY_BIZ_TYPE == feeCheckAccountTypeConfig.getType()) {
                    List<BillCoinTypeProperty> currentAllBillCoinTypePropertyList = billCoinTypePropertyService.selectAllCoinByCheckTimeInBizTypes(Integer.valueOf(feeCheckAccountTypeConfig.getAccountType()), feeCheckAccountTypeConfig.getAccountParam(), lastDayBizTime, feeCheckAccountTypeConfig.getFeeBizTypes());
                    List<BillCoinTypeProperty> lastDayAllBillCoinTypePropertyList = billCoinTypePropertyService.selectAllCoinByCheckTimeInBizTypes(Integer.valueOf(feeCheckAccountTypeConfig.getAccountType()), feeCheckAccountTypeConfig.getAccountParam(), lastTwoDayBizTime, feeCheckAccountTypeConfig.getFeeBizTypes());
                    Map<Integer, List<BillCoinTypeProperty>> currentAllBillCoinTypePropertyMap = currentAllBillCoinTypePropertyList.stream().collect(Collectors.groupingBy(BillCoinTypeProperty::getCoinId));
                    Map<Integer, List<BillCoinTypeProperty>> lastDayAllBillCoinTypePropertyMap = lastDayAllBillCoinTypePropertyList.stream().collect(Collectors.groupingBy(BillCoinTypeProperty::getCoinId));
                    for (Integer coinId : currentAllBillCoinTypePropertyMap.keySet()) {
                        List<BillCoinTypeProperty> currentBillCoinTypePropertyList = currentAllBillCoinTypePropertyMap.get(coinId);
                        List<BillCoinTypeProperty> lastBillCoinTypePropertyList = lastDayAllBillCoinTypePropertyMap.get(coinId);
                        List<BillCoinTypeProperty> userBillAggregationMonitorDataList = CalculationCheckUtils.buildFeeAggregationData(coinId, currentBillCoinTypePropertyList, lastBillCoinTypePropertyList);
                        BillCoinTypeProperty feeBillCoinTypeProperty = CalculationCheckUtils.sumChangeFee(userBillAggregationMonitorDataList);
                        BigDecimal feeTotal = billCheckService.getChangePropSumByProperty(feeBillCoinTypeProperty);
                        BigDecimal value = feeTotalMap.getOrDefault(coinId, BigDecimal.ZERO);
                        feeTotalMap.put(coinId, value.add(feeTotal));
                    }
                }
            }

            // 返佣统计
            for (CommissionCheckAccountTypeConfig commissionConfig : commissionConfigs) {
                AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(commissionConfig.getAccountType());
                BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountTypeEnum.getCode());
                List<BillCoinTypeProperty> currentAllBillCoinTypePropertyList = billCoinTypePropertyService.selectAllCoinByCheckTimeInBizTypes(Integer.valueOf(commissionConfig.getAccountType()), commissionConfig.getAccountParam(), bizTime, commissionConfig.getCommissionBizTypes());
                List<BillCoinTypeProperty> lastDayAllBillCoinTypePropertyList = billCoinTypePropertyService.selectAllCoinByCheckTimeInBizTypes(Integer.valueOf(commissionConfig.getAccountType()), commissionConfig.getAccountParam(), lastDayBizTime, commissionConfig.getCommissionBizTypes());
                Map<Integer, List<BillCoinTypeProperty>> currentAllBillCoinTypePropertyMap = currentAllBillCoinTypePropertyList.stream().collect(Collectors.groupingBy(BillCoinTypeProperty::getCoinId));
                Map<Integer, List<BillCoinTypeProperty>> lastDayAllBillCoinTypePropertyMap = lastDayAllBillCoinTypePropertyList.stream().collect(Collectors.groupingBy(BillCoinTypeProperty::getCoinId));
                for (Integer coinId : currentAllBillCoinTypePropertyMap.keySet()) {
                    List<BillCoinTypeProperty> currentBillCoinTypePropertyList = currentAllBillCoinTypePropertyMap.get(coinId);
                    List<BillCoinTypeProperty> lastBillCoinTypePropertyList = lastDayAllBillCoinTypePropertyMap.get(coinId);
                    List<BillCoinTypeProperty> userBillAggregationMonitorDataList = CalculationCheckUtils.buildFeeAggregationData(coinId, currentBillCoinTypePropertyList, lastBillCoinTypePropertyList);
                    BillCoinTypeProperty feeBillCoinTypeProperty = CalculationCheckUtils.sumChangeFee(userBillAggregationMonitorDataList);
                    BigDecimal commissionTotal = billCheckService.getChangePropSumByProperty(feeBillCoinTypeProperty);
                    BigDecimal value = commissionTotalMap.getOrDefault(coinId, BigDecimal.ZERO);
                    commissionTotalMap.put(coinId, value.add(commissionTotal));
                }
            }

            // 取fee和成交map的并集
            Set<Integer> allCoinSet = new HashSet<>();
            allCoinSet.addAll(feeTotalMap.keySet());
            allCoinSet.addAll(commissionTotalMap.keySet());

            for (Integer coinId : allCoinSet) {
                BigDecimal coinFee = feeTotalMap.get(coinId);
                BigDecimal commissionFee = commissionTotalMap.getOrDefault(coinId, BigDecimal.ZERO);
                MonitorCommissionAggregationData commissionAggregationData = new MonitorCommissionAggregationData();
                commissionAggregationData.setCoinId(coinId);
                commissionAggregationData.setBizTime(bizTime);
                commissionAggregationData.setSceneName(monitorSceneTaskConfig.getSceneName());
                Map<Integer, BigDecimal> minToleranceMapoleranceMap = feeCheckConfig.getMinToleranceMap();
                commissionAggregationData.setMinTolerance(minToleranceMapoleranceMap.get(coinId) != null ? minToleranceMapoleranceMap.get(coinId) : feeCheckConfig.getDefaultMinTolerance());
                Map<Integer, BigDecimal> maxToleranceMapoleranceMap = feeCheckConfig.getMaxToleranceMap();
                commissionAggregationData.setMaxTolerance(maxToleranceMapoleranceMap.get(coinId) != null ? maxToleranceMapoleranceMap.get(coinId) : feeCheckConfig.getDefaultMaxTolerance());
                commissionAggregationData.setFeeCountChange(coinFee);
                commissionAggregationData.setCommissionCountChange(commissionFee);
                MonitorCmdWrapper monitorCmdWrapper = new MonitorCmdWrapper(monitorSceneTaskConfig, MonitorCmdEnum.AGGREGATION_TYPE, null, commissionAggregationData, bizTime);
                monitorCheckProcessor.offerCommand(monitorCmdWrapper);
            }
            log.info("FeeAggregationMonitorDataRuleServiceImpl finished , checkTime {}", DateUtil.getDefaultDateStr(bizTime));
        } catch (Exception e) {
            log.error("FeeAggregationMonitorDataRuleServiceImpl error", e);
        }
    }


}

