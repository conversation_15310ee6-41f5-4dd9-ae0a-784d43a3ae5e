package com.upex.reconciliation.service.service.impl;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.service.OldBillContractProfitSymbolDetailService;
import com.upex.reconciliation.service.dao.entity.BillContractProfitSymbolDetail;
import com.upex.reconciliation.service.dao.mapper.OldBillContractProfitSymbolDetailMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_coin_detail(币种维度盈亏明细表)】的数据库操作Service实现
 * @createDate 2023-06-09 17:18:46
 */
@Service
public class OldBillContractProfitSymbolDetailServiceImpl implements OldBillContractProfitSymbolDetailService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "oldBillContractProfitSymbolDetailMapper")
    private OldBillContractProfitSymbolDetailMapper oldBillContractProfitSymbolDetailMapper;

    @Override
    public List<BillContractProfitSymbolDetail> selectListByAccountTypeAndCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInBillMaster(() -> {
            return oldBillContractProfitSymbolDetailMapper.selectListByAccountTypeAndCheckTime(accountType, accountParam, checkTime);
        });
    }
}




