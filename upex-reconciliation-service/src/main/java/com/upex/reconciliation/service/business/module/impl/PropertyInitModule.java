package com.upex.reconciliation.service.business.module.impl;

import com.upex.mixcontract.common.framework.AttributeMap;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.module.AbstractBillModule;
import com.upex.reconciliation.service.business.module.IBillDataDotsModule;
import com.upex.reconciliation.service.common.constants.ReconciliationCommandEnum;
import com.upex.reconciliation.service.common.constants.enums.QueryUserAssetsSceneEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillUser;
import com.upex.reconciliation.service.model.BillCmdResult;
import com.upex.reconciliation.service.model.BillCmdWrapper;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.ReconKafkaOpsConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.service.BillUserService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.MetricsUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.utils.log.AlarmUtils;
import com.upex.utils.task.TaskManager;
import com.upex.utils.task.TaskVoidBatchResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.PROPERTY_INIT_USER_ASSETS_ERROR;
import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.PROPERTY_INIT_USER_NOT_EXISTS;
import static com.upex.reconciliation.service.utils.MetricsUtil.HISTOGRAM_SINGLE_LOAD_USER_ASSETS;

@Slf4j
public class PropertyInitModule extends AbstractBillModule implements IBillDataDotsModule {
    private BillUserCheckModule billUserCheckModule;
    private TaskManager asyncLoadUserTaskManager;
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    private AlarmNotifyService alarmNotifyService;
    private BlockingQueue<BillCmdWrapper> errorDelayQueue = new LinkedBlockingQueue<>(3000);
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private BillUserService billUserService;

    @Override
    public void init(AttributeMap initContext) {
        accountTypeEnum = initContext.get(BillLogicGroup.ACCOUNT_TYPE_ENUM_KEY);
        billUserCheckModule = getModule(BillUserCheckModule.class);
        ReconciliationSpringContext reconciliationSpringContext = initContext.get(BillLogicGroup.ENGINE_SPRING_CONTEXT_KEY);

        this.asyncLoadUserTaskManager = reconciliationSpringContext.getAsyncLoadUserTaskManager();
        this.billUserService = reconciliationSpringContext.getBillUserService();
        accountAssetsServiceFactory = reconciliationSpringContext.getAccountAssetsServiceFactory();
        alarmNotifyService = reconciliationSpringContext.getAlarmNotifyService();
        // 一场延迟数据重新入队
        scheduler.scheduleAtFixedRate(() -> {
            while (true) {
                BillCmdWrapper cmdWrapper = errorDelayQueue.poll();
                if (cmdWrapper != null) {
                    offerCommand(cmdWrapper);
                } else {
                    break;
                }
            }
        }, 3, 2, TimeUnit.SECONDS);
    }

    public PropertyInitModule(BillLogicGroup logicGroup) {
        super(logicGroup);
        this.cmdQueue = new LinkedBlockingQueue<>(3000);
    }

    @Override
    public void dataDots() {
    }

    @Override
    public void offerCommand(BillCmdWrapper cmdWrapper) {
        try {
            if (ReconciliationCommandEnum.INIT_ASSET.equals(cmdWrapper.getCommandEnum())) {
                cmdQueue.put(cmdWrapper);
            }
        } catch (Exception e) {
            String errorMsg = "BillUserCheckModule Command queue is full : " + cmdWrapper.getCommandEnum().getName()
                    + " queue size: " + this.cmdQueue.size() + " logicGroup: " + logicGroup.getName();
            AlarmUtils.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }
    }

    @Override
    public BillCmdResult takeCommand() {
        try {
            ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
            ReconKafkaOpsConfig reconKafkaOpsConfig = apolloBizConfig.getReconKafkaOpsConfig();
            Map<Long, BillCmdWrapper> cmdWrapperMap = new ConcurrentHashMap<>();
            List<Long> userIds = new ArrayList<>();
            for (int i = 0; i < reconKafkaOpsConfig.getPropertyInitModuleBatchSize(); i++) {
                BillCmdWrapper cmdWrapper = cmdQueue.poll(1, TimeUnit.SECONDS);
                if (cmdWrapper == null) {
                    break;
                }
                CommonBillChangeData commandData = (CommonBillChangeData) cmdWrapper.getCommandData();
                log.info("PropertyInitModule takeCommand accountType:{}, queue size:{} accountId:{}, bizId:{} errorDelayQueueSize:{}", accountTypeEnum.getCode(), this.cmdQueue.size(), commandData.getAccountId(), commandData.getBizId(), errorDelayQueue.size());
                cmdWrapperMap.put(commandData.getAccountId(), cmdWrapper);
                userIds.add(commandData.getAccountId());
            }
            if (MapUtils.isEmpty(cmdWrapperMap)) {
                return null;
            }

            // 用户验证
            if (apolloBizConfig.isReloadUserAssetsForceCheckUserExist()) {
                Set<Long> existsUserId = billUserService.selectByIds(userIds).stream().map(BillUser::getId).collect(Collectors.toSet());
                for (Long userId : userIds) {
                    if (!existsUserId.contains(userId)) {
                        BillCmdWrapper cmdWrapper = cmdWrapperMap.remove(userId);
                        if (cmdWrapper != null) {
                            errorDelayQueue.put(cmdWrapper);
                            CommonBillChangeData commandData = (CommonBillChangeData) cmdWrapper.getCommandData();
                            log.info("PropertyInitModule takeCommand errorDelayData accountType:{}, queue size:{} accountId:{}, bizId:{} errorDelayQueueSize:{}", accountTypeEnum.getCode(), this.cmdQueue.size(), commandData.getAccountId(), commandData.getBizId(), errorDelayQueue.size());
                            alarmNotifyService.alarm(accountTypeEnum.getCode(), PROPERTY_INIT_USER_NOT_EXISTS, accountTypeEnum.getCode(), commandData.getAccountId());
                        }
                    }
                }
            }

            // 批量加载用户资产
            TaskVoidBatchResult taskResult = asyncLoadUserTaskManager.forEachSubmitBatchAndWait(new ArrayList<BillCmdWrapper>(cmdWrapperMap.values()), (BillCmdWrapper fromCmdWrapper) -> {
                try {
                    MetricsUtil.histogram(HISTOGRAM_SINGLE_LOAD_USER_ASSETS + accountTypeEnum.getCode(), () -> {
                        CommonBillChangeData currentBill = (CommonBillChangeData) fromCmdWrapper.getCommandData();
                        List<BillCoinUserProperty> billCoinUserPropertyList = accountAssetsServiceFactory.getBillCoinUserPropertyList(currentBill.getAccountType(), currentBill.getAccountId(), currentBill.getLastCheckOkTime().getTime(), QueryUserAssetsSceneEnum.RELOAD);
                        BillCmdWrapper toCmdWrapper = new BillCmdWrapper(ReconciliationCommandEnum.INIT_ASSET_COMPLETE, billCoinUserPropertyList, currentBill);
                        billUserCheckModule.offerCommand(toCmdWrapper);
                        cmdWrapperMap.remove(currentBill.getAccountId());
                    });
                } catch (Exception e) {
                    log.error("PropertyInitModule task failed, accountType:{}, queue size:{}", accountTypeEnum.getCode(), this.cmdQueue.size(), e);
                    throw e;
                }
            }, cmdWrapperMap.values().size());
            if ((taskResult != null && taskResult.getFails().size() > 0)) {
                log.error("PropertyInitModule task failed, accountType:{}, queue size:{}", accountTypeEnum.getCode(), this.cmdQueue.size());
            }
            if (MapUtils.isNotEmpty(cmdWrapperMap)) {
                alarmNotifyService.alarm(accountTypeEnum.getCode(), PROPERTY_INIT_USER_ASSETS_ERROR, accountTypeEnum.getCode());
                for (Map.Entry<Long, BillCmdWrapper> entry : cmdWrapperMap.entrySet()) {
                    BillCmdWrapper fromCmdWrapper = entry.getValue();
                    CommonBillChangeData currentBill = (CommonBillChangeData) fromCmdWrapper.getCommandData();
                    log.error("PropertyInitModule task failed, accountType:{}, queue size:{} accountId:{} bizId:{} lastCheckOkTime:{}", accountTypeEnum.getCode(), this.cmdQueue.size(), currentBill.getAccountId(), currentBill.getBizId(), DateUtil.date2str(currentBill.getLastCheckOkTime()));
                    this.cmdQueue.put(fromCmdWrapper);
                }
                Thread.currentThread().sleep(1000);
            }
        } catch (Exception e) {
            log.error("PropertyInitModule task failed, accountType:{} queue size:{}", accountTypeEnum.getCode(), this.cmdQueue, e);
            alarmNotifyService.alarm(accountTypeEnum.getCode(), PROPERTY_INIT_USER_ASSETS_ERROR, accountTypeEnum.getCode());
        }
        return null;
    }
}
