package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AbstractBillCheckService;
import com.upex.reconciliation.service.dao.entity.BillCoinProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillWalletSupplementConfig;
import com.upex.reconciliation.service.model.alarm.CheckResult;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.AbstractProperty;
import com.upex.reconciliation.service.model.dto.AbstractSProperty;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.model.dto.DataCalResultDTO;
import com.upex.reconciliation.service.utils.NumberUtil;
import com.upex.ticker.facade.dto.PriceVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class OnchainBillCheckServiceImpl extends AbstractBillCheckService {

    @Override
    public Byte getAccountType() {
        return AccountTypeEnum.ONCHAIN.getCode();
    }

    @Override
    public boolean checkBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty) {
        if (commonBillChangeData.getProp1().subtract(commonBillChangeData.getChangeProp1()).compareTo(billCoinUserProperty.getProp1()) == 0
                && commonBillChangeData.getProp2().subtract(commonBillChangeData.getChangeProp2()).compareTo(billCoinUserProperty.getProp2()) == 0) {
            return true;
        }
        return false;
    }

    @Override
    public boolean checkCurrentBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty) {
        //暂时没调用
        return false;
    }

    @Override
    public BigDecimal getPropSumByUserProperty(BillCoinUserProperty coinUserProperty) {
        // 参考现货
        return NumberUtil.add(coinUserProperty.getProp1(), coinUserProperty.getProp2());
    }

    @Override
    public <T extends AbstractProperty> boolean checkAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult, ApolloReconciliationBizConfig apolloBillConfig, Map<Integer, PriceVo> ratesToUSDTCoinIdMap, Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap) {
        //TODO
        return false;
    }

    @Override
    public <T extends AbstractSProperty> boolean checkSpropAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult, ApolloReconciliationBizConfig apolloBillConfig, Map<Integer, PriceVo> ratesToUSDTCoinIdMap, Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap) {
        return false;
    }

    @Override
    public BigDecimal getPropSumByProperty(AbstractProperty billCoinProperty) {
        return billCoinProperty == null ? BigDecimal.ZERO : billCoinProperty.getProp1().add(billCoinProperty.getProp2());
    }

    @Override
    public BigDecimal getPropByTransferFee(AbstractProperty abstractProperty) {
        //没调用
        return null;
    }

    @Override
    public BigDecimal getChangePropSumByProperty(AbstractProperty abstractProperty) {
        //参考现货
        return NumberUtil.add(abstractProperty.getChangeProp1(), abstractProperty.getChangeProp2());
    }

    @Override
    public BigDecimal getPropSumByBillChangeData(CommonBillChangeData currentBill) {
        //参考现货
        return NumberUtil.add(currentBill.getProp1(), currentBill.getProp2());
    }

    @Override
    public BigDecimal getChangePropSumByBillChangeData(CommonBillChangeData currentBill) {
        //参考现货
        return NumberUtil.add(currentBill.getChangeProp1(), currentBill.getChangeProp2());
    }

    @Override
    public BigDecimal getChangePropSumByAbstractProperty(AbstractProperty abstractProperty) {
        //参考现货
        return NumberUtil.add(abstractProperty.getChangeProp1(), abstractProperty.getChangeProp2());
    }

    public CheckResult doCheckProperty(ApolloReconciliationBizConfig apolloBizConfig, String accountUniqueId, Long userId, Integer coinId, List<CommonBillChangeData> billChangeDataList, BillCoinUserProperty billCoinUserProperty) {
        CommonBillChangeData lastChange = billChangeDataList.get(billChangeDataList.size() - 1);
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        BigDecimal totalChangeProp1 = BigDecimal.ZERO;
        BigDecimal totalChangeProp2 = BigDecimal.ZERO;
        for (CommonBillChangeData bill : billChangeDataList) {
            totalChangeProp1 = totalChangeProp1.add(bill.getChangeProp1());
            totalChangeProp2 = totalChangeProp2.add(bill.getChangeProp2());
        }
        if (billCoinUserProperty.getProp1().add(totalChangeProp1).compareTo(lastChange.getProp1()) != 0) {
            log.error("OnchainBillCheckServiceImpl.checkBillCoinProperty getProp1 accountUniqueId:{}  lastChange:{},billCoinUserProperty:{},totalChangeProp2:{}"
                    , accountUniqueId, JSONObject.toJSONString(lastChange), totalChangeProp1.toPlainString());
            return CheckResult.fail(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), lastChange.getBizTime())
                    .addUserPropCheckData(lastChange.getAccountId(), lastChange.getCoinId(), billCoinUserProperty.getProp1(), totalChangeProp1, lastChange.getProp1(), lastChange.getBizId(), "prop1");
        }
        if (billCoinUserProperty.getProp2().add(totalChangeProp2).compareTo(lastChange.getProp2()) != 0) {
            log.error("OnchainBillCheckServiceImpl.checkBillCoinProperty getProp2 accountUniqueId:{}  lastChange:{},billCoinUserProperty:{},totalChangeProp2:{}"
                    , accountUniqueId, JSONObject.toJSONString(lastChange), totalChangeProp2.toPlainString());
            return CheckResult.fail(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), lastChange.getBizTime())
                    .addUserPropCheckData(lastChange.getAccountId(), lastChange.getCoinId(), billCoinUserProperty.getProp2(), totalChangeProp2, lastChange.getProp2(), lastChange.getBizId(), "prop2");
        }
        return CheckResult.DEFAULT_SUCCESS;
    }

    @Override
    public List<String> getBusinessType() {
        return List.of(AccountTypeEnum.ONCHAIN.getBizTypePrefix());
    }

    @Override
    public <T extends AbstractProperty> boolean checkPosition(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult) {
        return false;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitAssets(T oldProperty, BillCoinUserProperty newBillCoinUserProperty) {
        //没调用
        return false;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinAssets(T oldProperty, BillCoinProperty newBillCoinUserProperty) {
        return false;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinChangeAssets(T oldProperty, BillCoinProperty newBillCoinUserProperty) {
        return false;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeAssets(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        return false;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeChangeAssets(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        return false;
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinAssetsByAccountType(T oldProperty, BillCoinProperty newBillCoinUserProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        return false;
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeInitAssetsByAccountType(List<T> oldPropertyList, BillCoinTypeProperty newBillCoinTypeProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        return false;
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeAssetsByAccountType(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        return false;
    }

    @Override
    public <T extends AbstractProperty> BigDecimal sumForInAll(T property) {
        return null;
    }

    @Override
    public void cleanAndSetAssetsProperty(AbstractProperty abstractProperty, Byte accountType) {
        BigDecimal prop1 = abstractProperty.getProp1();
        BigDecimal prop2 = abstractProperty.getProp2();
        BigDecimal changeProp1 = abstractProperty.getChangeProp1();
        BigDecimal changeProp2 = abstractProperty.getChangeProp2();
        abstractProperty.cleaPropAndChangeProp();
        abstractProperty.setProp1(prop1);
        abstractProperty.setProp2(prop2);
        abstractProperty.setChangeProp1(changeProp1);
        abstractProperty.setChangeProp2(changeProp2);
    }
}
