package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SimpleEarnFlexPositionRes implements IBinanceApiBaseRes{
    /**
     * {
     *     "total": 4,
     *     "rows": [
     *         {
     *             "totalAmount": "105.04816276",
     *             "latestAnnualPercentageRate": "0.01297993",
     *             "asset": "1000CAT",
     *             "canRedeem": true,
     *             "collateralAmount": "0",
     *             "productId": "1000CAT001",
     *             "yesterdayRealTimeRewards": "0.00443907",
     *             "cumulativeBonusRewards": "0",
     *             "cumulativeRealTimeRewards": "0.00635035",
     *             "cumulativeTotalRewards": "0.00635035",
     *             "autoSubscribe": true
     *         }
     *    ]
     * }
     */
    private Integer total;
    private List<SimpleEarnFlexPositionInnerRes> rows;
    @Data
    public class SimpleEarnFlexPositionInnerRes {
        private BigDecimal totalAmount;
        private BigDecimal latestAnnualPercentageRate;
        private String asset;
        private Boolean canRedeem;
        private BigDecimal collateralAmount;
        private String productId;
        private BigDecimal yesterdayRealTimeRewards;
        private BigDecimal cumulativeBonusRewards;
        private BigDecimal cumulativeRealTimeRewards;
        private BigDecimal cumulativeTotalRewards;
        private Boolean autoSubscribe;

    }
}
