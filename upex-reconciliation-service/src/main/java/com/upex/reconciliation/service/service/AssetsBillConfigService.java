package com.upex.reconciliation.service.service;


import com.upex.reconciliation.service.dao.entity.AssetsBillConfig;
import com.upex.reconciliation.service.dao.entity.BillAllConfig;
import com.upex.reconciliation.service.dao.mapper.AssetsBillConfigMapper;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class AssetsBillConfigService {


    @Resource
    private BillDbHelper dbHelper;


    @Resource
    private AssetsBillConfigMapper assetsBillConfigMapper;

    @Resource
    private BillConfigService billConfigService;

    @Resource
    private BillAllConfigService billAllConfigService;

    public AssetsBillConfig selectByTypeAndParam(String assetsCheckType, String assetsCheckParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillConfigMapper.selectByTypeAndParam(assetsCheckType, assetsCheckParam));
    }

    public List<AssetsBillConfig> listAssetsBillConfig() {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillConfigMapper.listAssetsBillConfig());
    }


    public int insertSelective(AssetsBillConfig record) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillConfigMapper.insertSelective(record));
    }

    public AssetsBillConfig selectMinCheckOkTime(){
        return dbHelper.doDbOpInReconMaster(()->assetsBillConfigMapper.selectMinCheckOkTime());
    }

    public int updateByPrimaryKeySelective(AssetsBillConfig record) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillConfigMapper.updateByPrimaryKeySelective(record));
    }

    public List<AssetsBillConfig> findNeedCheckConfigList(Date currentTime) {
        Long billBeginTime = 0L;
        List<AssetsBillConfig> resultList = new ArrayList<>();
        List<AssetsCheckConfig> allAssetsCheckList = ReconciliationApolloConfigUtils.getAllAssetsCheckList();
        if (CollectionUtils.isEmpty(allAssetsCheckList)) {
            return resultList;
        }
        List<AssetsBillConfig> assetsBillConfigs = listAssetsBillConfig();
        Map<String, AssetsBillConfig> assetsBillConfigMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(assetsBillConfigs)) {
            assetsBillConfigs.stream().forEach(assetsBillConfig -> assetsBillConfigMap.putIfAbsent(assetsBillConfig.getCheckUniqKey(), assetsBillConfig));
        }
        for (AssetsCheckConfig assetsCheckConfig : allAssetsCheckList) {
            if (!assetsCheckConfig.isOpen()) {
                log.warn("asset check is not open assetCheckConfig={}", assetsBillConfigs);
                continue;
            }
            AssetsBillConfig assetsBillConfig = assetsBillConfigMap.get(assetsCheckConfig.getCheckUniqKey());
            if (assetsBillConfig == null) {
                billBeginTime = assetsCheckConfig.getBillBeginTime();
                assetsBillConfig = new AssetsBillConfig();
                assetsBillConfig.setAssetsCheckType(assetsCheckConfig.getAssetsCheckType());
                assetsBillConfig.setAssetsCheckParam(assetsCheckConfig.getAssetsCheckParam());
                assetsBillConfig.setSyncPos(0L);
                assetsBillConfig.setCheckOkTime(new Date(billBeginTime));
                assetsBillConfig.setCreateTime(new Date());
                assetsBillConfig.setUpdateTime(new Date());
            } else {
                if (assetsBillConfig.getCheckOkTime() == null) {
                    continue;
                }
                if (currentTime.getTime() - assetsBillConfig.getCheckOkTime().getTime() < assetsCheckConfig.getTimeSliceAssetsCheckInterval()) {
                    continue;
                }
            }
            if (CollectionUtils.isNotEmpty(assetsCheckConfig.getSubSystemList())) {
                //检查billConfig
                List<BillAllConfig> billConfigs = billAllConfigService.listAllBillConfigs();
                if (CollectionUtils.isEmpty(billConfigs)) {
                    continue;
                }
                Date minCheckOkTime = billConfigs.stream().filter(billConfig -> assetsCheckConfig.getSubSystemList().contains(billConfig.getUniqKey())).min(Comparator.comparing(billConfig -> billConfig.getCheckOkTime())).get().getCheckOkTime();
                if (minCheckOkTime.getTime() - assetsBillConfig.getCheckOkTime().getTime() < assetsCheckConfig.getTimeSliceAssetsCheckInterval()) {
                    continue;
                }
                assetsBillConfig.setBusinessCheckOkTime(minCheckOkTime);
            }
            resultList.add(assetsBillConfig);
        }
        return resultList;
    }

    public int deleteAll(String assetsCheckType, String assetsCheckParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillConfigMapper.deleteAll(assetsCheckType, assetsCheckParam));
    }
}
