package com.upex.reconciliation.service.business.impl;

import com.upex.contract.process.dto.enums.MarginBizTypeEnum;
import com.upex.mixcontract.common.literal.enums.FinanceBizTypeEnum;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.MonitorCheckService;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.model.MonitorCmdWrapper;
import com.upex.reconciliation.service.model.param.BillMonitorData;
import com.upex.reconciliation.service.model.param.IncomeCheckData;
import com.upex.reconciliation.service.model.param.MonitorIncomeAggregationData;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.spot.dto.enums.SpotBillBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class IncomeAggregationMonitorCheckServiceImpl implements MonitorCheckService {


    @Autowired
    private AlarmNotifyService alarmNotifyService;


    private static final int MSG_CONTENT_LENGTH_LIMIT = 100;


    @Override
    public boolean processScene(Long sceneId, MonitorCmdWrapper monitorCmdWrapper) {
        BillMonitorData billAggregationMonitorData = monitorCmdWrapper.getAggregationMonitorData();
        boolean ifSendMsg = true;
        StringBuilder stringBuffer = new StringBuilder();
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(billAggregationMonitorData.getAccountType());
        if (billAggregationMonitorData instanceof MonitorIncomeAggregationData) {
            MonitorIncomeAggregationData aggregationMonitorDatas = (MonitorIncomeAggregationData) monitorCmdWrapper.getAggregationMonitorData();
            stringBuffer.append(aggregationMonitorDatas.getSceneName()).append("\n").append("公式：应收=实收\n").append("时间:").append(DateUtil.getDefaultDateStr(billAggregationMonitorData.getBizTime())).append("\n").append("系统账号:").append(billAggregationMonitorData.getUserId()).append("\n");
            // 第一个数据是系统账户的数据
            // 第二个数据是其他用户的手续费汇总数据
            List<IncomeCheckData> incomeCheckDataList = aggregationMonitorDatas.getIncomeCheckDataList();
            int lineCount = 0;
            // 使用lambda表达式进行排序
            incomeCheckDataList.sort((p1, p2) -> p2.getDiffAmountUsdt().compareTo(p1.getDiffAmountUsdt()));
            for (IncomeCheckData incomeCheckData : incomeCheckDataList) {
                boolean result = incomeCheckData.checkIncome();
                if (!result) {
                    // 添加一行内容
                    stringBuffer.append("coinId(").append(incomeCheckData.getCoinId()).append("):").append(incomeCheckData.getCoinName()).append(",  实收:").append(incomeCheckData.getIncomeCountChange().stripTrailingZeros().toPlainString()).append(",  应收:").append(incomeCheckData.getFeeCountChange().stripTrailingZeros().toPlainString()).append(",  差额:").append(incomeCheckData.getFeeCountChange().subtract(incomeCheckData.getIncomeCountChange()).stripTrailingZeros().toPlainString()).append("; ");
                    if (!CollectionUtils.isEmpty(incomeCheckData.getBizTypeChangeMap())) {
                        for (Map.Entry<String, BigDecimal> entry : incomeCheckData.getBizTypeChangeMap().entrySet()) {
                            stringBuffer.append("   bizType(").append(transBizTypeToDes(entry.getKey(), accountTypeEnum)).append("):").append(entry.getValue().stripTrailingZeros().toPlainString());
                        }
                    }
                    stringBuffer.append("\n");
                    ifSendMsg = false;
                }
                lineCount++;
                if (lineCount >= MSG_CONTENT_LENGTH_LIMIT) {
                    break;
                }
            }
            stringBuffer.append("======================\n");
            stringBuffer.append("存在差异的coin总数量有 ").append(incomeCheckDataList.size()).append(" 个\n");
        }

        if (!ifSendMsg) {
            // 推送聚合模板的告警
            alarmNotifyService.alarm(AlarmTemplateEnum.INCOME_SCENE3_CHECK, stringBuffer.toString());
        }
        return ifSendMsg;
    }


    public String transBizTypeToDes(String bizType, AccountTypeEnum accountTypeEnum) {
        if (accountTypeEnum.isContract()) {
            return FinanceBizTypeEnum.toEnum(bizType).getDesc();
        } else if (accountTypeEnum.isLever()) {
            return MarginBizTypeEnum.toEnum(bizType).getDesc();
        } else if (AccountTypeEnum.SPOT.equals(accountTypeEnum)) {
            return SpotBillBizTypeEnum.toEnum(Integer.valueOf(bizType)).getDesc();
        }
        return bizType;
    }


}
