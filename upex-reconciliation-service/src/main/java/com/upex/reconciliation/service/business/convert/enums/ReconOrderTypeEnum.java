package com.upex.reconciliation.service.business.convert.enums;

public enum ReconOrderTypeEnum {

    //闪兑
    CONVERT((byte) 1, "用户闪兑对账"),
    // onchain
    ONCHAIN((byte) 2, "onchain订单"),
    ;
    private final byte code;
    private final String desc;

    ReconOrderTypeEnum(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public byte getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }


}
