package com.upex.reconciliation.service.business.profit.impl;

import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import com.upex.reconciliation.service.business.profit.CheckProfitGrayService;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import org.springframework.stereotype.Service;

/**
 * hash灰度
 *
 * <AUTHOR>
 * @Date 2025/4/25
 */
@Service
public class HashCheckProfitGrayServiceImpl implements CheckProfitGrayService {

    @Override
    public boolean match(ReconCheckResultsParams checkResultsParams, GlobalBillConfig billConfig) {
        return checkResultsParams.getUserId() % 100 + 1 <= billConfig.getProfitGrayPercent();
    }
}