package com.upex.reconciliation.service.common.listener;

import com.github.rholder.retry.Attempt;
import com.github.rholder.retry.RetryListener;
import com.upex.reconciliation.service.common.constants.BillConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 监听类，每次重试都会自动触发监听器
 * 可在监听类中处理调用失败逻辑代码：如多次调用失败可发送邮件提醒等
 * @return {@link null }
 * <AUTHOR>
 * @date 2023/2/28 00:33
 */
@Slf4j
@Component
public class AssetsRetryerListener implements RetryListener {

    //@Resource
    //private AlarmManageService alarmManageService;

    /**
     * 报警重试次数
     */
    private long retryNumber = 0;
    /**
     * 报警发送lark开关
     */
    private Boolean errorOpen = true;
    /**
     * 扩展参数
     */
    private String params = "";
    /**
     * 要发送到的报警群
     */
    private String preConfigKey = BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM;


    @Override
    public <V> void onRetry(Attempt<V> attempt) {
        if(attempt.hasException()){
            log.error("AssetsRetryerListener Listener Be triggered, --------第" + attempt.getAttemptNumber() + "次重试---------扩展参数：" + params,attempt.getExceptionCause());
            if (attempt.getAttemptNumber() >= retryNumber && errorOpen) {
                //alarmManageService.alarmSystemCheckBillException(attempt.getExceptionCause(), preConfigKey);
            }
        }
    }

    public void setRetryNumber(long retryNumber) {
        this.retryNumber = retryNumber;
    }

    public void setErrorOpen(Boolean errorOpen) {
        this.errorOpen = errorOpen;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public void setPreConfigKey(String preConfigKey) {
        this.preConfigKey = preConfigKey;
    }
}