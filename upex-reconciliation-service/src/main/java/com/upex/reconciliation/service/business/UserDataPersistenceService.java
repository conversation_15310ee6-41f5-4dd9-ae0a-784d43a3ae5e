package com.upex.reconciliation.service.business;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.upex.bill.dto.enums.BillExceptionEnum;
import com.upex.commons.support.exception.ApiException;
import com.upex.mixcontract.process.facade.dto.MixAccountAssetsExtension;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.createtablebyroute.BillCoinTypeUserPropertyTableCreator;
import com.upex.reconciliation.service.business.module.impl.BillTimeSliceCheckModule;
import com.upex.reconciliation.service.business.module.impl.BillUserCheckModule;
import com.upex.reconciliation.service.business.module.impl.BillUserProfitCheckModule;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.common.constants.enums.*;
import com.upex.reconciliation.service.dao.bill.entity.BillUser;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.dao.mapper.*;
import com.upex.reconciliation.service.model.config.*;
import com.upex.reconciliation.service.model.dto.BillTimeSliceDTO;
import com.upex.reconciliation.service.model.dto.FixDataParam;
import com.upex.reconciliation.service.model.dto.PositionStatisticsDTO;
import com.upex.reconciliation.service.model.dto.SyncBillUserDTO;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.service.impl.BillCoinUserPropertySnapshotService;
import com.upex.reconciliation.service.utils.*;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.utils.task.TaskManager;
import com.upex.utils.task.TaskVoidBatchResult;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.USER_WITHDRAWAL_CHECK_PROFIT_ERROR;
import static com.upex.reconciliation.service.common.constants.BillConstants.SEPARATOR;
import static com.upex.reconciliation.service.utils.MetricsUtil.HISTOGRAM_PERSISTENCE_SAVE_TIME_SLICE_DATA;

@Service
@Slf4j
public class UserDataPersistenceService {
    @Resource
    private BillEngineManager reconciliationApplication;
    @Resource
    private BillConfigService billConfigService;
    @Resource
    private BillCoinPropertyService billCoinPropertyService;
    @Resource
    private BillCoinUserPropertyMapper billCoinUserPropertyMapper;
    @Resource
    private BillConfigMapper billConfigMapper;
    @Resource
    private BillAllConfigMapper billAllConfigMapper;
    @Resource
    private BillDbHelper dbHelper;
    @Resource
    private TbBillIdempotentRecordMapper tbBillIdempotentRecordMapper;
    @Resource
    private BillCoinUserPropertySnapshotMapper billCoinUserPropertySnapshotMapper;
    @Resource
    private BillCoinTypeUserPropertyMapper billCoinTypeUserPropertyMapper;
    @Resource
    private BillCoinPropertyMapper billCoinPropertyMapper;
    @Resource
    private BillCoinTypePropertyMapper billCoinTypePropertyMapper;
    @Resource
    private BillSymbolCoinPropertyMapper billSymbolCoinPropertyMapper;
    @Resource
    private BillSymbolPropertyMapper billSymbolPropertyMapper;
    @Resource
    private BillSymbolPropertyService billSymbolPropertyService;
    @Resource
    private BillCoinTypePropertyService billCoinTypePropertyService;
    @Resource
    private BillCoinUserPropertySnapshotService billCoinUserPropertySnapshotService;
    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;
    @Resource
    private BillSymbolCoinPropertyService billSymbolCoinPropertyService;
    @Resource
    private BillUserPositionService billUserPositionService;
    @Resource
    private TbBillIdempotentRecordService tbBillIdempotentRecordService;
    @Resource
    private BillSymbolTradingConfigMapper billSymbolTradingConfigMapper;
    @Resource
    private AssetsBillConfigService assetsBillConfigService;
    @Resource
    private AssetsBillCoinPropertyService assetsBillCoinPropertyService;
    @Resource
    private AssetsBillCoinTypePropertyService assetsBillCoinTypePropertyService;
    @Resource
    private BillCoinUserPropertyErrorService billCoinUserPropertyErrorService;
    @Resource
    private BillContractProfitTransferService billContractProfitTransferService;
    @Resource
    private BillContractProfitSymbolDetailService billContractProfitSymbolDetailService;
    @Resource
    private BillContractProfitCoinDetailService billContractProfitCoinDetailService;
    @Resource
    private FixDataRecordService fixDataRecordService;
    @Resource
    private OldBillCoinUserService oldBillCoinUserService;
    @Resource
    private OldBillUserService oldBillUserService;
    @Resource(name = "taskManager")
    private TaskManager taskManager;
    @Resource(name = "kafkaProducer")
    private KafkaProducer<String, String> kafkaProducer;
    @Resource
    private BillCoinTypeUserPropertyService billCoinTypeUserPropertyService;
    @Resource
    private BillAllConfigService billAllConfigService;
    @Resource
    private SerialNoGenerator noGenerator;
    @Resource
    private AssetsBillConfigSnapshotService assetsBillConfigSnapshotService;
    @Resource
    private BillCapitalInitPropertyService billCapitalInitPropertyService;
    @Resource
    private ReconSystemAccountService reconSystemAccountService;
    @Resource
    private OldBillCapitalCoinUserShadowCreditPropertyService oldBillCapitalCoinUserShadowCreditPropertyService;
    @Resource
    private OldBillCapitalCoinShadowCreditPropertyService oldBillCapitalCoinShadowCreditPropertyService;
    @Resource
    private BillCoinUserPropertyAssetSnapshotService billCoinUserPropertyAssetSnapshotService;
    @Resource
    private BillCoinUserPropertyAssetSnapshotMapper billCoinUserPropertyAssetSnapshotMapper;
    @Resource
    private AssetsContractProfitCoinDetailService assetsContractProfitCoinDetailService;
    @Resource
    private AssetsContractProfitSymbolDetailService assetsContractProfitSymbolDetailService;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private BillTransferFeeCoinDetailService billTransferFeeCoinDetailService;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    /***业务线最后存盘时间片***/
    private Map<Byte, BillTimeSliceDTO> lastSaveTimeSliceDTOMap = new ConcurrentHashMap<>();
    @Resource
    private BillCoinTypeUserPropertyTableCreator billCoinTypeUserPropertyTableCreator;
    @Resource
    private UserQueryService userQueryService;
    @Resource
    private BillEngineManager billEngineManager;
    @Resource
    private CommonService commonService;
    @Resource
    private AlarmNotifyService alarmNotifyService;

    /**
     * 分别进行个人纬度save，时间片纬度save
     */
    public void saveTimeSliceDataByAccountType(Byte accountType) {
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        saveData(apolloBizConfig);
    }

    public void fixData(FixDataParam fixDataParam) {
        log.info("execute fixData with fixDataParam {}", JSONObject.toJSONString(fixDataParam));
        Date now = new Date();
        Long batchId = now.getTime();
        if (fixDataParam.getUserCoinId() != null) {
            // 校验是否存在
            BillCoinUserProperty billCoinUserProperty = billCoinUserPropertyService.selectById(fixDataParam.getAccountType(), fixDataParam.getAccountParam(), fixDataParam.getUserCoinId());
            if (billCoinUserProperty != null) {
                BillCoinUserProperty beforeBillCoinUserProperty = new BillCoinUserProperty();
                BeanCopierUtil.copyProperties(billCoinUserProperty, beforeBillCoinUserProperty);
                billCoinUserProperty.setProp1(fixDataParam.getUserCoinProp1() != null ? fixDataParam.getUserCoinProp1() : billCoinUserProperty.getProp1());
                billCoinUserProperty.setProp2(fixDataParam.getUserCoinProp2() != null ? fixDataParam.getUserCoinProp2() : billCoinUserProperty.getProp2());
                billCoinUserProperty.setProp3(fixDataParam.getUserCoinProp3() != null ? fixDataParam.getUserCoinProp3() : billCoinUserProperty.getProp3());
                billCoinUserProperty.setProp4(fixDataParam.getUserCoinProp4() != null ? fixDataParam.getUserCoinProp4() : billCoinUserProperty.getProp4());
                billCoinUserProperty.setProp5(fixDataParam.getUserCoinProp5() != null ? fixDataParam.getUserCoinProp5() : billCoinUserProperty.getProp5());
                if (fixDataParam.getLastBizId() != null) {
                    JSONObject paramJson = JSON.parseObject(billCoinUserProperty.getParams());
                    paramJson.put("lastBizId", fixDataParam.getLastBizId());
                    billCoinUserProperty.setParams(JSONObject.toJSONString(paramJson));
                }
                billCoinUserPropertyService.updateById(fixDataParam.getAccountType(), fixDataParam.getAccountParam(), billCoinUserProperty);
                FixDataRecord fixDataRecord = new FixDataRecord();
                fixDataRecord.setAccountType(Integer.valueOf(fixDataParam.getAccountType()));
                fixDataRecord.setAccountParam(fixDataParam.getAccountParam());
                fixDataRecord.setBatchId(batchId);
                fixDataRecord.setTableNamePrefix(BillTablePrefixEnum.BILL_COIN_USER_PROPERTY.getTablePrefix());
                fixDataRecord.setBeforeData(JSONObject.toJSONString(beforeBillCoinUserProperty));
                fixDataRecord.setAfterData(JSONObject.toJSONString(billCoinUserProperty));
                fixDataRecord.setCreateTime(now);
                fixDataRecord.setUpdateTime(now);
                fixDataRecordService.batchInsert(List.of(fixDataRecord));
            }
        }

        if (fixDataParam.getUserCoinSnapshotId() != null) {
            // 校验是否存在
            BillCoinUserProperty billCoinUserPropertySnapshot = billCoinUserPropertySnapshotService.selectById(fixDataParam.getAccountType(), fixDataParam.getAccountParam(), fixDataParam.getUserCoinSnapshotId());
            if (billCoinUserPropertySnapshot != null) {
                BillCoinUserProperty beforeBillCoinUserPropertySnapshot = new BillCoinUserProperty();
                BeanCopierUtil.copyProperties(billCoinUserPropertySnapshot, beforeBillCoinUserPropertySnapshot);
                billCoinUserPropertySnapshot.setProp1(fixDataParam.getUserCoinProp1() != null ? fixDataParam.getUserCoinProp1() : billCoinUserPropertySnapshot.getProp1());
                billCoinUserPropertySnapshot.setProp2(fixDataParam.getUserCoinProp2() != null ? fixDataParam.getUserCoinProp2() : billCoinUserPropertySnapshot.getProp2());
                billCoinUserPropertySnapshot.setProp3(fixDataParam.getUserCoinProp3() != null ? fixDataParam.getUserCoinProp3() : billCoinUserPropertySnapshot.getProp3());
                billCoinUserPropertySnapshot.setProp4(fixDataParam.getUserCoinProp4() != null ? fixDataParam.getUserCoinProp4() : billCoinUserPropertySnapshot.getProp4());
                billCoinUserPropertySnapshot.setProp5(fixDataParam.getUserCoinProp5() != null ? fixDataParam.getUserCoinProp5() : billCoinUserPropertySnapshot.getProp5());
                if (fixDataParam.getLastBizId() != null) {
                    JSONObject paramJson = JSON.parseObject(billCoinUserPropertySnapshot.getParams());
                    paramJson.put("lastBizId", fixDataParam.getLastBizId());
                    billCoinUserPropertySnapshot.setParams(JSONObject.toJSONString(paramJson));
                }
                billCoinUserPropertySnapshotService.updateById(fixDataParam.getAccountType(), fixDataParam.getAccountParam(), billCoinUserPropertySnapshot);
                FixDataRecord fixDataRecord = new FixDataRecord();
                fixDataRecord.setAccountType(Integer.valueOf(fixDataParam.getAccountType()));
                fixDataRecord.setAccountParam(fixDataParam.getAccountParam());
                fixDataRecord.setBatchId(batchId);
                fixDataRecord.setTableNamePrefix(BillTablePrefixEnum.BILL_COIN_USER_PROPERTY_SNAPSHOT.getTablePrefix());
                fixDataRecord.setBeforeData(JSONObject.toJSONString(beforeBillCoinUserPropertySnapshot));
                fixDataRecord.setAfterData(JSONObject.toJSONString(billCoinUserPropertySnapshot));
                fixDataRecord.setCreateTime(now);
                fixDataRecord.setUpdateTime(now);
                fixDataRecordService.batchInsert(List.of(fixDataRecord));

            }
        }


        if (fixDataParam.getBillCoinId() != null) {
            // 校验是否存在
            BillCoinProperty billCoinProperty = billCoinPropertyService.selectById(fixDataParam.getAccountType(), fixDataParam.getAccountParam(), fixDataParam.getBillCoinId());
            if (billCoinProperty != null) {
                BillCoinProperty beforeBillCoinProperty = new BillCoinProperty();
                BeanCopierUtil.copyProperties(billCoinProperty, beforeBillCoinProperty);
                billCoinProperty.setProp1(fixDataParam.getBillCoinProp1() != null ? fixDataParam.getBillCoinProp1() : billCoinProperty.getProp1());
                billCoinProperty.setProp2(fixDataParam.getBillCoinProp2() != null ? fixDataParam.getBillCoinProp2() : billCoinProperty.getProp2());
                billCoinProperty.setProp3(fixDataParam.getBillCoinProp3() != null ? fixDataParam.getBillCoinProp3() : billCoinProperty.getProp3());
                billCoinProperty.setProp4(fixDataParam.getBillCoinProp4() != null ? fixDataParam.getBillCoinProp4() : billCoinProperty.getProp4());
                billCoinProperty.setProp5(fixDataParam.getBillCoinProp5() != null ? fixDataParam.getBillCoinProp5() : billCoinProperty.getProp5());
                billCoinPropertyService.updateByBillCoinProperty(fixDataParam.getAccountType(), fixDataParam.getAccountParam(), billCoinProperty);
                FixDataRecord fixDataRecord = new FixDataRecord();
                fixDataRecord.setAccountType(Integer.valueOf(fixDataParam.getAccountType()));
                fixDataRecord.setAccountParam(fixDataParam.getAccountParam());
                fixDataRecord.setBatchId(batchId);
                fixDataRecord.setTableNamePrefix(BillTablePrefixEnum.BILL_COIN_PROPERTY.getTablePrefix());
                fixDataRecord.setBeforeData(JSONObject.toJSONString(beforeBillCoinProperty));
                fixDataRecord.setAfterData(JSONObject.toJSONString(billCoinProperty));
                fixDataRecord.setCreateTime(now);
                fixDataRecord.setUpdateTime(now);
                fixDataRecordService.batchInsert(List.of(fixDataRecord));
            }
        }


        if (fixDataParam.getBillCoinTypeId() != null) {
            // 校验是否存在
            BillCoinTypeProperty billCoinTypeProperty = billCoinTypePropertyService.selectById(fixDataParam.getAccountType(), fixDataParam.getAccountParam(), fixDataParam.getBillCoinTypeId());
            if (billCoinTypeProperty != null) {
                BillCoinTypeProperty beforeBillCoinTypeProperty = new BillCoinTypeProperty();
                BeanCopierUtil.copyProperties(billCoinTypeProperty, beforeBillCoinTypeProperty);
                billCoinTypeProperty.setProp1(fixDataParam.getBillCoinTypeProp1() != null ? fixDataParam.getBillCoinTypeProp1() : billCoinTypeProperty.getProp1());
                billCoinTypeProperty.setProp2(fixDataParam.getBillCoinTypeProp2() != null ? fixDataParam.getBillCoinTypeProp2() : billCoinTypeProperty.getProp2());
                billCoinTypeProperty.setProp3(fixDataParam.getBillCoinTypeProp3() != null ? fixDataParam.getBillCoinTypeProp3() : billCoinTypeProperty.getProp3());
                billCoinTypeProperty.setProp4(fixDataParam.getBillCoinTypeProp4() != null ? fixDataParam.getBillCoinTypeProp4() : billCoinTypeProperty.getProp4());
                billCoinTypeProperty.setProp5(fixDataParam.getBillCoinTypeProp5() != null ? fixDataParam.getBillCoinTypeProp5() : billCoinTypeProperty.getProp5());
                billCoinTypePropertyService.updateById(fixDataParam.getAccountType(), fixDataParam.getAccountParam(), billCoinTypeProperty);
                FixDataRecord fixDataRecord = new FixDataRecord();
                fixDataRecord.setAccountType(Integer.valueOf(fixDataParam.getAccountType()));
                fixDataRecord.setAccountParam(fixDataParam.getAccountParam());
                fixDataRecord.setBatchId(batchId);
                fixDataRecord.setTableNamePrefix(BillTablePrefixEnum.BILL_COIN_TYPE_PROPERTY.getTablePrefix());
                fixDataRecord.setBeforeData(JSONObject.toJSONString(beforeBillCoinTypeProperty));
                fixDataRecord.setAfterData(JSONObject.toJSONString(billCoinTypeProperty));
                fixDataRecord.setCreateTime(now);
                fixDataRecord.setUpdateTime(now);
                fixDataRecordService.batchInsert(List.of(fixDataRecord));
            }
        }

        // 落盘操作记录:
        // 时间&业务线，修改前数据，修改后数据，都以json格式来弄

        log.info("finished fixData with fixDataParam {}", JSONObject.toJSONString(fixDataParam));

    }

    /**
     * 回退数据方法
     */
    public void resetTimeSliceData(Long resetCheckTime, Byte accountType) {
        // 从apollo获取所有业务线的引擎配置
        ApolloReconciliationBizConfig bizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        resetBillData(bizConfig, new Date(resetCheckTime));
    }

    private void saveData(ApolloReconciliationBizConfig apolloBizConfig) {
        if (!EnvUtil.isLocalDebugByAccountType(apolloBizConfig.getAccountType())) {
            if (apolloBizConfig == null || !apolloBizConfig.isOpen() || !apolloBizConfig.isSaveTimeSliceOpen()) {
                return;
            }
        }
        // 获取时间片存储
        BillLogicGroup billLogicGroup = reconciliationApplication.getBillLogicGroup(apolloBizConfig.getAccountType());
        if (billLogicGroup == null) {
            return;
        }
        BillTimeSliceCheckModule timeSliceModule = (BillTimeSliceCheckModule) billLogicGroup.getTimeSliceModule();
        BillUserCheckModule billUserCheckModule = (BillUserCheckModule) billLogicGroup.getUserCheckModule();
        SortedMap<Long, BillTimeSliceDTO> saveTimeSliceDTOMap = timeSliceModule.getSaveTimeSliceDTOMap();
        log.info("UserDataPersistenceService.saveTimeSliceDTOMap less getMergeTimeSliceSize: {} {}  {}",
                apolloBizConfig.getAccountType(), saveTimeSliceDTOMap.size(), apolloBizConfig.getMergeTimeSliceSize());
        //if (saveTimeSliceDTOMap.size() < apolloBizConfig.getMergeTimeSliceSize()) {
        //    return;
        //}
        int saveTimeSliceDTOMapSize = saveTimeSliceDTOMap.size();
        for (int i = 0; i < saveTimeSliceDTOMapSize; i++) {
            Long timeSliceKey = saveTimeSliceDTOMap.firstKey();
            BillTimeSliceDTO billTimeSliceDTO = saveTimeSliceDTOMap.get(timeSliceKey);
            log.info("UserDataPersistenceService.saveTimeSliceDTOMap doDbOpInReconMasterTransaction: {} {} ", apolloBizConfig.getAccountType(), timeSliceKey);
            if (billTimeSliceDTO == null) {
                log.info("UserDataPersistenceService.saveTimeSliceDTOMap billTimeSliceDTO is null: {} {} ", apolloBizConfig.getAccountType(), timeSliceKey);
                return;
            }
            if (!billLogicGroup.getEngine().isRunning()) {
                log.info("UserDataPersistenceService.saveTimeSliceDTOMap Engine is stop remove saveTimeSlice accountType:{} {} ", apolloBizConfig.getAccountType(), timeSliceKey);
                saveTimeSliceDTOMap.remove(timeSliceKey);
                continue;
            }
            Date nowDate = new Date();
            Long intervalMs = TimeUnitEnum.toDuration(apolloBizConfig.getBusinessDalySaveTime()).toMillis();
            if (nowDate.getTime() - timeSliceKey < intervalMs) {
                log.info("UserDataPersistenceService.saveTimeSliceDTOMap lte businessDalySaveTime accountType:{} saveTime:{} businessDalySaveTime:{}", apolloBizConfig.getAccountType(), DateUtil.longToDate(timeSliceKey), apolloBizConfig.getBusinessDalySaveTime());
                return;
            }
            String saveTimeSliceStatus = (String) redisTemplate.opsForValue().get(RedisUtil.getSaveTimeSliceStatusKey(apolloBizConfig.getAccountType()));
            if (Boolean.FALSE.toString().equals(saveTimeSliceStatus)) {
                log.info("UserDataPersistenceService.saveTimeSliceDTOMap delay message accountType:{} {} ", apolloBizConfig.getAccountType(), DateUtil.longToDate(timeSliceKey));
                return;
            }
            if (apolloBizConfig.getSaveDataWaitLedgerCheckOk()) {
                String ledgerLastCheckOkTime = (String) redisTemplate.opsForValue().get(RedisUtil.getBusinessLastCheckOkTime(AccountTypeEnum.INTERNAL.getBizTag()));
                if (StringUtils.isEmpty(ledgerLastCheckOkTime) || Long.parseLong(ledgerLastCheckOkTime) < timeSliceKey) {
                    log.info("UserDataPersistenceService.saveTimeSliceDTOMap ledger delay accountType:{} ledgerLastCheckOkTime:{} checkOkTime:{} ", apolloBizConfig.getAccountType(), ledgerLastCheckOkTime, DateUtil.longToDate(timeSliceKey));
                    return;
                }
            }

            MetricsUtil.histogram(HISTOGRAM_PERSISTENCE_SAVE_TIME_SLICE_DATA + apolloBizConfig.getAccountType(), () -> {
                StopWatch stopWatch = new StopWatch();
                // 计算合约prop1权益
                BillTimeSliceDTO lastSaveTimeSliceDTO = lastSaveTimeSliceDTOMap.getOrDefault(apolloBizConfig.getAccountType(), new BillTimeSliceDTO());
                //calculateContractBalance(apolloBizConfig, billTimeSliceDTO, lastSaveTimeSliceDTO);
                // 处理乱序用户资产数据
                stopWatch.start("buildCoinUserPropertyError");
                List<BillCoinUserPropertyError> billCoinUserPropertyErrors = buildCoinUserPropertyError(apolloBizConfig, billTimeSliceDTO, lastSaveTimeSliceDTO);
                billTimeSliceDTO.setBillCoinUserPropertyErrorList(billCoinUserPropertyErrors);
                stopWatch.stop();
                // 把用户更新和插入列表 提出事物外
                List<BillCoinUserProperty> coinUserInsertList = new ArrayList<>();
                List<BillCoinUserProperty> coinUserUpdateList = new ArrayList<>();
                List<BillCoinUserProperty> coinUserAssetInsertList = new ArrayList<>();
                stopWatch.start("buildCoinUserPropertyInsertOrUpdateList");
                buildCoinUserPropertyInsertOrUpdateList(apolloBizConfig, billTimeSliceDTO, coinUserInsertList, coinUserUpdateList, coinUserAssetInsertList);
                stopWatch.stop();
                // 批量写Kafka 用户资产快照 用户增量更新
                stopWatch.start("sendKafkaMessage");
                sendKafkaMessage(apolloBizConfig, billTimeSliceDTO, coinUserInsertList, coinUserUpdateList);
                stopWatch.stop();
                stopWatch.start("doDbOpInReconMasterTransaction");
                // 批量写到数据库
                dbHelper.doDbOpInReconMasterTransaction(() -> {
                    saveTimeSlice(apolloBizConfig, billTimeSliceDTO, coinUserInsertList, coinUserUpdateList, coinUserAssetInsertList);
                    return null;
                });
                stopWatch.stop();
                log.info("UserDataPersistenceService.saveData save time slice success accountType {} elapsed time log:{}", apolloBizConfig.getAccountType(), stopWatch.prettyPrint());
            });
            saveTimeSliceDTOMap.remove(timeSliceKey);
            lastSaveTimeSliceDTOMap.put(apolloBizConfig.getAccountType(), billTimeSliceDTO);
        }
        log.info("UserDataPersistenceService.saveData finished accountType={},saveTimeSliceDTOMap:{}",
                apolloBizConfig.getAccountType(), saveTimeSliceDTOMap.size());
    }

    /**
     * 计算合约prop1权益
     *
     * @param apolloBizConfig
     * @param billTimeSliceDTO
     * @param lastSaveTimeSliceDTO
     */
    private void calculateContractBalance(ApolloReconciliationBizConfig apolloBizConfig, BillTimeSliceDTO billTimeSliceDTO, BillTimeSliceDTO lastSaveTimeSliceDTO) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        Date checkOkTime = billTimeSliceDTO.getBillConfig().getCheckOkTime();
        Date nowDate = new Date();
        if (!accountTypeEnum.haveUserPosition()) {
            return;
        }
        // 计算仓位未实现
        Map<String, AtomicInteger> logHitMap = new ConcurrentHashMap<>();
        logHitMap.computeIfAbsent("oldTimeSliceCoinUserPropertyListSize", key -> new AtomicInteger(billTimeSliceDTO.getTimeSliceCoinUserPropertyList().size()));
        MixAccountAssetsExtension mixAccountAssetsExtension = accountAssetsServiceFactory.queryPriceByTime((int) apolloBizConfig.getAccountType(), checkOkTime.getTime());
        Map<String, BigDecimal> mPriceMap = mixAccountAssetsExtension.getMPriceMap();
        Map<Integer, BigDecimal> sPriceMap = mixAccountAssetsExtension.getSPriceMap();
        log.info("mixAccountAssetsExtension.getMPriceMap accountType:{} checkOkTime:{} mPriceMap:{} sPriceMap:{}", accountTypeEnum.getCode(), DateUtil.date2str(checkOkTime), JSON.toJSONString(mPriceMap), JSON.toJSONString(sPriceMap));
        Map<Long, Map<Integer, BigDecimal>> userCoinUnRealizedMap = new HashMap<>();
        Map<String, List<BillUserPosition>> billUserPositionMap = billTimeSliceDTO.getBillUserPositionList().stream().collect(Collectors.groupingBy(BillUserPosition::groupByUserCoinKey));
        for (Map.Entry<String, List<BillUserPosition>> entry : billUserPositionMap.entrySet()) {
            for (BillUserPosition billUserPosition : entry.getValue()) {
                if (billUserPosition.getSCount().compareTo(BigDecimal.ZERO) == 0 && billUserPosition.getLCount().compareTo(BigDecimal.ZERO) == 0) {
                    logHitMap.computeIfAbsent("emptyPositionCount", key -> new AtomicInteger(0)).incrementAndGet();
                }
            }
            PositionStatisticsDTO positionStatisticsDTO = BillUserPosition.calculatePositionStatistics(entry.getValue(), mPriceMap, sPriceMap, apolloBizConfig, reconSystemAccountService);
            userCoinUnRealizedMap.computeIfAbsent(BillUserPosition.getUserIdFromKey(entry.getKey()), key -> new HashMap<>()).put(BillUserPosition.getCoinIdFromKey(entry.getKey()), positionStatisticsDTO.getTotalMarginUnRealized());
        }
        // 加载不存在用户资产 如果内存存在则不加载
        List<Pair<Long, Integer>> loadUserIdList = new ArrayList<>();
        for (Map.Entry<Long, Map<Integer, BigDecimal>> userCoinUnRealizedEntry : userCoinUnRealizedMap.entrySet()) {
            Long userId = userCoinUnRealizedEntry.getKey();
            for (Map.Entry<Integer, BigDecimal> coinUnRealized : userCoinUnRealizedEntry.getValue().entrySet()) {
                Integer coinId = coinUnRealized.getKey();
                BillCoinUserProperty billCoinUserProperty = billTimeSliceDTO.getCoinUserPropertyMap().getOrDefault(userId, Collections.emptyMap()).get(coinId);
                logHitMap.computeIfAbsent("positionCount", key -> new AtomicInteger(0)).incrementAndGet();
                if (billCoinUserProperty == null) {
                    billCoinUserProperty = lastSaveTimeSliceDTO.getCoinUserPropertyMap().getOrDefault(userId, Collections.emptyMap()).get(coinId);
                    if (billCoinUserProperty != null) {
                        billTimeSliceDTO.getCoinUserPropertyMap().computeIfAbsent(userId, key -> new HashMap<>()).put(coinId, billCoinUserProperty);
                        logHitMap.computeIfAbsent("lastSaveTimeSliceCount", key -> new AtomicInteger(0)).incrementAndGet();
                    } else {
                        loadUserIdList.add(Pair.of(userId, coinId));
                        logHitMap.computeIfAbsent("loadDbCount", key -> new AtomicInteger(0)).incrementAndGet();
                    }
                }
            }
        }
        TaskVoidBatchResult queryResultIsEmpty = taskManager.forEachSubmitBatchAndWait(loadUserIdList, (Pair<Long, Integer> userCoinIdPair) -> {
            Long userId = userCoinIdPair.getKey();
            Integer coinId = userCoinIdPair.getValue();
            BillCoinUserProperty billCoinUserProperty = billCoinUserPropertyService.selectUserCoinRecord(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), userId, coinId);
            if (billCoinUserProperty != null) {
                billCoinUserProperty.reloadDataParamsConvert(accountTypeEnum);
                billTimeSliceDTO.getCoinUserPropertyMap().computeIfAbsent(userId, v -> new HashMap<>()).put(coinId, billCoinUserProperty);
                billTimeSliceDTO.getLastDbCoinUserPropertyMap().put(billCoinUserProperty.groupByCoinUser(), billCoinUserProperty);
            }
        }, apolloBizConfig.getSaveDataQueryUserCoinConcurrence());
        if (queryResultIsEmpty != null && queryResultIsEmpty.getFails().size() > 0) {
            log.error("UserDataPersistenceService calculateContractBalance.getFails() accountType:{} size={}", accountTypeEnum.getCode(), queryResultIsEmpty.getFails().size());
            throw new RuntimeException("UserDataPersistenceService calculateContractBalance.getFails() accountType:" + accountTypeEnum.getCode() + " size=" + queryResultIsEmpty.getFails().size());
        }
        // 计算用户余额（未实现+余额+保证金）
        BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountTypeEnum.getCode());
        for (Map.Entry<Long, Map<Integer, BigDecimal>> entry : userCoinUnRealizedMap.entrySet()) {
            Long userId = entry.getKey();
            for (Map.Entry<Integer, BigDecimal> coinUnRealizedEntry : entry.getValue().entrySet()) {
                Integer coinId = coinUnRealizedEntry.getKey();
                BigDecimal positionUnRealized = coinUnRealizedEntry.getValue();
                Map<Integer, BillCoinUserProperty> userCoinPropertyMap = billTimeSliceDTO.getCoinUserPropertyMap().get(userId);
                if (userCoinPropertyMap != null && userCoinPropertyMap.containsKey(coinId)) {
                    BillCoinUserProperty billCoinUserProperty = userCoinPropertyMap.get(coinId);
                    BigDecimal propSum = billCheckService.getPropSumByUserProperty(billCoinUserProperty);
                    billCoinUserProperty.setProp1(propSum.add(positionUnRealized));
                } else {
                    throw new RuntimeException("UserDataPersistenceService calculateContractBalance userCoinPropertyMap is null or not containsKey userId:" + userId + " coinId:" + coinId + " positionUnRealized:" + positionUnRealized);
                }
            }
        }
        // 缓存数据重新刷新时间
        billTimeSliceDTO.getCoinUserPropertyMap().forEach((userId, currentCoinMap) -> {
            currentCoinMap.forEach((coinId, currentCoinUserProperty) -> {
                currentCoinUserProperty.setCheckTime(checkOkTime);
                currentCoinUserProperty.setCreateTime(nowDate);
                currentCoinUserProperty.setUpdateTime(nowDate);
            });
        });
        log.info("UserDataPersistenceService calculateContractBalance end accountType:{} checkOkTime:{} logHitMap:{}", accountTypeEnum.getCode(), DateUtil.date2str(checkOkTime), JSON.toJSONString(logHitMap));
    }

    /**
     * 处理乱序数据
     *
     * @param apolloBizConfig
     * @param billTimeSliceDTO
     */
    private List<BillCoinUserPropertyError> buildCoinUserPropertyError(
            ApolloReconciliationBizConfig apolloBizConfig,
            BillTimeSliceDTO billTimeSliceDTO,
            BillTimeSliceDTO lastSaveTimeSliceDTO) {
        AccountTypeEnum accountTypeEnum = billTimeSliceDTO.getAccountTypeEnum();
        List<BillCoinUserProperty> currentBillCoinUserPropertyList = billTimeSliceDTO.getTimeSliceCoinUserPropertyList();
        if (CollectionUtils.isEmpty(currentBillCoinUserPropertyList)) {
            return Lists.newArrayList();
        }
        // 查询出当前时间段的资产快照数据
        Map<String, AtomicInteger> logHitMap = new ConcurrentHashMap<>();
        logHitMap.computeIfAbsent("oldTimeSliceCoinUserPropertyListSize", key -> new AtomicInteger(currentBillCoinUserPropertyList.size()));
        TaskVoidBatchResult queryResultIsEmpty = taskManager.forEachSubmitBatchAndWait(currentBillCoinUserPropertyList, (BillCoinUserProperty currentBillCoinUserProperty) -> {
            // 有限取本地缓存 不存在加载数据
            BillCoinUserProperty billCoinUserProperty = lastSaveTimeSliceDTO.getCoinUserPropertyMap().getOrDefault(currentBillCoinUserProperty.getUserId(), Collections.emptyMap()).get(currentBillCoinUserProperty.getCoinId());
            if (billCoinUserProperty != null && billCoinUserProperty.getId() != null) {
                billTimeSliceDTO.getLastDbCoinUserPropertyMap().put(billCoinUserProperty.groupByCoinUser(), billCoinUserProperty);
                logHitMap.computeIfAbsent("lastSaveTimeSliceCount", key -> new AtomicInteger(0)).incrementAndGet();
            } else {
                BillCoinUserProperty lastBillCoinUserProperty = billCoinUserPropertyService.selectUserCoinRecord(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), currentBillCoinUserProperty.getUserId(), currentBillCoinUserProperty.getCoinId());
                logHitMap.computeIfAbsent("loadDbCount", key -> new AtomicInteger(0)).incrementAndGet();
                if (lastBillCoinUserProperty != null) {
                    lastBillCoinUserProperty.reloadDataParamsConvert(accountTypeEnum);
                    billTimeSliceDTO.getLastDbCoinUserPropertyMap().put(lastBillCoinUserProperty.groupByCoinUser(), lastBillCoinUserProperty);
                }
            }
        }, apolloBizConfig.getSaveDataQueryUserCoinConcurrence());
        if (queryResultIsEmpty != null && queryResultIsEmpty.getFails().size() > 0) {
            log.error("buildCoinUserPropertyError.concurrentSelectCoinUserSnapshot getUids:{} getFails:{}", queryResultIsEmpty.getFails().size());
            throw new RuntimeException("buildCoinUserPropertyError.concurrentSelectCoinUserSnapshot queryResultIsEmpty.getFails().size=" + queryResultIsEmpty.getFails().size());
        }
        // 查询出当期 快照数据 查询出下期快照数据 coin+user维度对比lastBillId是否乱序 如果乱序打印日志
        List<BillCoinUserPropertyError> billCoinUserPropertyErrorList = new ArrayList<>();
        for (BillCoinUserProperty currentBillCoinUserProperty : currentBillCoinUserPropertyList) {
            BillCoinUserProperty lastBillCoinUserProperty = billTimeSliceDTO.getLastDbCoinUserPropertyMap().get(currentBillCoinUserProperty.groupByCoinUser());
            if (lastBillCoinUserProperty != null && lastBillCoinUserProperty.getLastBizId() != null && currentBillCoinUserProperty.getLastBizId() != null
                    && lastBillCoinUserProperty.getLastBizId() > currentBillCoinUserProperty.getLastBizId()) {
                BillCoinUserPropertyError billCoinUserPropertyError = BillCoinUserPropertyError.generateFromCoinUserProperty(lastBillCoinUserProperty, accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
                billCoinUserPropertyError.setCheckTime(billTimeSliceDTO.getBillConfig().getCheckOkTime());
                billCoinUserPropertyError.setCreateTime(new Date());
                billCoinUserPropertyError.setUpdateTime(new Date());
                billCoinUserPropertyErrorList.add(billCoinUserPropertyError);
            }
        }
        billCoinUserPropertyErrorList.addAll(billTimeSliceDTO.getBillCoinUserPropertyErrorList());
        log.info("buildCoinUserPropertyError.concurrentSelectCoinUserSnapshot end accountType:{} checkTime:{} logHitMap:{}", accountTypeEnum.getCode(), DateUtil.date2str(billTimeSliceDTO.getBillConfig().getCheckOkTime()), JSON.toJSONString(logHitMap));
        return billCoinUserPropertyErrorList;
    }

    public void addBillConfig(BillTimeSliceDTO result, BillTimeSliceDTO current) {
        BillConfig currentBillConfig = current.getBillConfig();
        result.setBillConfig(currentBillConfig);
    }

    public void addCoinProperty(BillTimeSliceDTO result, BillTimeSliceDTO current) {
        Date createTime = new Date();
        current.getCoinPropertyMap().forEach((k, v) -> {
            BillCoinProperty bp = result.getCoinPropertyMap().computeIfAbsent(k, item -> BillCoinProperty.builder()
                    .coinId(k).createTime(createTime).updateTime(createTime).build());
            bp.collectCurrentChange(v);
        });
    }


    public void addCoinTypeProperty(BillTimeSliceDTO result, BillTimeSliceDTO current) {
        Date createTime = new Date();
        current.getCoinTypePropertyMap().forEach((k, v) -> {
            BillCoinTypeProperty bp = result.getCoinTypePropertyMap().computeIfAbsent(k, item -> {
                BillCoinTypeProperty billCoinTypeProperty = new BillCoinTypeProperty();
                billCoinTypeProperty.setCoinId(BillCoinTypeProperty.getCoinIdFromKey(k));
                billCoinTypeProperty.setBizType(BillCoinTypeProperty.getBizTypeFromKey(k));
                billCoinTypeProperty.setCreateTime(createTime);
                billCoinTypeProperty.setUpdateTime(createTime);
                return billCoinTypeProperty;
            });
            bp.collectCurrentChange(v);
        });
    }


    public void addCoinUserProperty(BillTimeSliceDTO result, BillTimeSliceDTO current) {
        Date createTime = new Date();
        current.getCoinUserPropertyMap().forEach((userId, currentCoinMap) -> {
            Map<Integer, BillCoinUserProperty> resultCoinMap = result.getCoinUserPropertyMap().computeIfAbsent(userId, item -> new HashMap<>());
            currentCoinMap.forEach((k, v) -> {
                BillCoinUserProperty bp = resultCoinMap.computeIfAbsent(k, item -> BillCoinUserProperty.builder()
                        .userId(userId).coinId(k).createTime(createTime).updateTime(createTime).build());
                bp.collectCurrentChange(v);
            });
        });
    }

    public void addSymbolProperty(BillTimeSliceDTO result, BillTimeSliceDTO current) {
        current.getSymbolPropertyMap().forEach((k, v) -> {
            BillSymbolProperty bp = result.getSymbolPropertyMap().computeIfAbsent(k, item -> {
                BillSymbolProperty billSymbolProperty = new BillSymbolProperty();
                billSymbolProperty.setSymbolId(v.getSymbolId());
                if (v.getCreateTime() == null) {
                    billSymbolProperty.setCreateTime(new Date());
                    log.info("UserDataPersistenceService addSymbolProperty createTime is null: {}", JSON.toJSONString(billSymbolProperty));
                } else {
                    billSymbolProperty.setCreateTime(v.getCreateTime());
                }
                billSymbolProperty.setUpdateTime(v.getUpdateTime());
                billSymbolProperty.setInitialTime(v.getInitialTime());
                billSymbolProperty.setCheckTime(v.getCheckTime());
                return billSymbolProperty;
            });
            bp.setProp5(v.getProp5());
            bp.setChangeProp5(NumberUtil.add(bp.getChangeProp5(), v.getChangeProp5()));
        });
    }

    private void resetBillData(ApolloReconciliationBizConfig bizConfig, Date resetCheckTime) {

        int billConfigDelRecord = billConfigService.delBillDateForReset(bizConfig.getAccountType(), bizConfig.getAccountParam(), resetCheckTime);

        int billCoinDelRecord = billCoinPropertyMapper.deleteAfterRecord(resetCheckTime, bizConfig.getAccountType(), bizConfig.getAccountParam());

        int billCoinTypeDelRecord = billCoinTypePropertyMapper.deleteAfterRecord(resetCheckTime, bizConfig.getAccountType(), bizConfig.getAccountParam());
        int billSymbolCoinDelRecord = billSymbolCoinPropertyMapper.deleteRecords(resetCheckTime, bizConfig.getAccountType(), bizConfig.getAccountParam());

        log.info("UserDataPersistenceService.resetBillData resetCheckTime:{} billConfigDelRecord:{} billCoinDelRecord:{} billCoinTypeDelRecord:{} billSymbolCoinDelRecord:{}"
                , resetCheckTime, billConfigDelRecord, billCoinDelRecord, billCoinTypeDelRecord, billSymbolCoinDelRecord);

    }


    public void rollBackData(Boolean internalFlag, Boolean leverSpotFlag, Boolean businessFlag, Byte accountType, Long resetCheckTime, Long batchSize, Boolean allowOver2Day) {
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        if (globalBillConfig.isAlarmCheck()) {
            log.info("resetCheckTime checkParam error, globalBillConfig.isAlarmCheck() is true, please modify false");
            return;
        }
        if (resetCheckTime > System.currentTimeMillis()) {
            log.info("resetCheckTime checkParam error, resetCheckTime > 当前时间, please modify resetCheckTime");
            return;
        }
        Date nowDate = new Date();
        ApolloProfitTransferConfig apolloProfitTransferConfig = ReconciliationApolloConfigUtils.getApolloProfitTransferConfig();
        // 总账脏数据回退
        for (AssetsCheckConfig assetsCheckConfig : globalBillConfig.getAssetsCheckList()) {
            if (assetsCheckConfig.isOpen()) {
                log.info("globalBillConfig rollBackData error assetsCheckConfig.isOpen()=true");
                continue;
            }
            if (assetsCheckConfig.getAssetsCheckType().equalsIgnoreCase(AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getCode()) && !internalFlag) {
                continue;
            }
            if (assetsCheckConfig.getAssetsCheckType().equalsIgnoreCase(AssetsCheckTypeEnum.LEVER_SPOT_TOTAL_ASSETS.getCode()) && !leverSpotFlag) {
                continue;
            }
            AssetsCheckTypeEnum assetsCheckTypeEnum = AssetsCheckTypeEnum.toEnum(assetsCheckConfig.getAssetsCheckType());
            AssetsBillConfig assetsBillConfig = assetsBillConfigService.selectByTypeAndParam(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam());
            Date inAllRollbackTime = new Date(resetCheckTime - globalBillConfig.getMergeTimeSliceSizeSecond() * BillConstants.MILLIS_PER_SECOND);
            if (((nowDate.getTime() - inAllRollbackTime.getTime()) > BillConstants.ONE_DAY_MIL_SEC * 2) && !allowOver2Day) {
                log.info("globalBillConfig rollBackData error Over 2Days assetAccountType:{} assetCheckOkTime:{}", assetsCheckTypeEnum.getCode(), DateUtil.date2str(inAllRollbackTime));
                continue;
            }
            log.info("ledger rollBackData start assetsBillCoinPropertyService.deleteByCheckTime assetAccountType:{} rollbackTime:{}", assetsCheckTypeEnum.getCode(), DateUtil.date2str(inAllRollbackTime));
            assetsBillConfig.setCheckOkTime(inAllRollbackTime);
            boolean result = true;
            while (result) {
                result = assetsBillCoinPropertyService.deleteByCheckTime(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam(), inAllRollbackTime, batchSize);
            }
            log.info("ledger rollBackData start assetsBillCoinTypePropertyService.deleteByCheckTime assetAccountType:{} rollbackTime:{}", assetsCheckTypeEnum.getCode(), DateUtil.date2str(inAllRollbackTime));
            result = true;
            while (result) {
                result = assetsBillCoinTypePropertyService.deleteByCheckTime(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam(), inAllRollbackTime, batchSize);
            }
            log.info("ledger rollBackData start assetsContractProfitCoinDetailService.deleteByCheckTime assetAccountType:{} rollbackTime:{}", assetsCheckTypeEnum.getCode(), DateUtil.date2str(inAllRollbackTime));
            result = true;
            while (result) {
                result = assetsContractProfitCoinDetailService.deleteByCheckTime(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam(), inAllRollbackTime, batchSize);
            }
            log.info("ledger rollBackData start assetsContractProfitSymbolDetailService.deleteByCheckTime assetAccountType:{} rollbackTime:{}", assetsCheckTypeEnum.getCode(), DateUtil.date2str(inAllRollbackTime));
            result = true;
            while (result) {
                result = assetsContractProfitSymbolDetailService.deleteByCheckTime(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam(), inAllRollbackTime, batchSize);
            }
            log.info("ledger rollBackData start assetsBillConfigSnapshotService.deleteByCheckTime assetAccountType:{} rollbackTime:{}", assetsCheckTypeEnum.getCode(), DateUtil.date2str(inAllRollbackTime));
            if (apolloProfitTransferConfig.isResetCheckTransfer()) {
                log.info("ledger rollBackData start billContractProfitTransferService.generateChargeRecordForResetBill accountType:{} rollbackTime:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.longToDate(resetCheckTime));
                billContractProfitTransferService.generateChargeRecordForResetBill(AccountTypeEnum.INTERNAL, inAllRollbackTime, new Date());
            }
            assetsBillConfigSnapshotService.deleteByCheckTime(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam(), inAllRollbackTime);
            assetsBillConfigService.updateByPrimaryKeySelective(assetsBillConfig);
            log.info("ledger rollBackData end assetsBillConfigSnapshotService.deleteByCheckTime assetAccountType:{} rollbackTime:{}", assetsCheckTypeEnum.getCode(), DateUtil.date2str(inAllRollbackTime));
            // 回退授信资产
            //resetShadowFinanceCheckTime(assetsCheckTypeEnum.getCode(), inAllRollbackTime);
        }

        // 业务线脏数据回退
        if (!businessFlag) {
            log.info("business rollBackData error businessFlag is false");
            return;
        }
        AssetsCheckConfig internalAssetsCheckConfig = globalBillConfig.getAssetsCheckConfig(AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getCode());
        AssetsBillConfig innerassetsBillConfig = assetsBillConfigService.selectByTypeAndParam(AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getCode(), AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getAssetsParam());
        if (internalAssetsCheckConfig.getSubSystemList().contains(accountType.toString())) {
            if ((resetCheckTime < innerassetsBillConfig.getCheckOkTime().getTime())) {
                log.info("business rollBackData error Over AssetCheckOkTime accountType:{} BusniessResetCheckTime:{} AssetCheckOkTime:{}", accountType, DateUtil.date2str(new Date(resetCheckTime)), DateUtil.date2str(innerassetsBillConfig.getCheckOkTime()));
                return;
            }
        }
        log.info("business rollBackData start,business rollBackTime:{},accountType:{}", resetCheckTime, accountType);
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        if (apolloBizConfig.isOpen()) {
            log.info("business rollBackData error apolloBizConfig.isOpen=true");
            return;
        }
        Long beginId = 0L;
        Boolean result = true;
        // 校验入参时间的有效性
        BillConfig rollbackBillConfig = billConfigService.selectByTypeAndParamAndCheckTime(accountType, accountTypeEnum.getAccountParam(), new Date(resetCheckTime));
        if (rollbackBillConfig == null) {
            log.error("UserDataPersistenceService.resetCheckOkData invalid rollbackTime {} ", resetCheckTime);
            return;
        }

        // 数据动账逻辑回退
        if (apolloProfitTransferConfig.isResetCheckTransfer()) {
            log.info("business rollBackData start billContractProfitTransferService.generateChargeRecordForResetBill accountType:{} rollbackTime:{}", accountType, DateUtil.longToDate(resetCheckTime));
            billContractProfitTransferService.generateChargeRecordForResetBill(accountTypeEnum, rollbackBillConfig.getCheckOkTime(), new Date());
        }
        log.info("business rollBackData start billConfigService.selectByTypeAndParamAfterCheckTime accountType:{} rollbackTime:{}", accountType, DateUtil.longToDate(resetCheckTime));
        List<BillConfig> billConfigList = billConfigService.selectByTypeAndParamAfterCheckTime(accountType, accountTypeEnum.getAccountParam(), new Date(resetCheckTime));
        for (BillConfig billConfig : billConfigList) {
            Date checkTime = billConfig.getCheckOkTime();
            billCoinPropertyService.deleteByCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime);
            billCoinTypePropertyService.deleteByCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime);
            log.info("business rollBackData start billCoinUserPropertyService.deleteByCheckTime accountType:{} rollbackTime:{}", accountType, DateUtil.date2str(checkTime));
            result = true;
            while (result) {
                result = billCoinUserPropertyService.deleteByCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime, batchSize);
            }
            if (apolloBizConfig.isCoinTypeUserCheckOpen()) {
                log.info("business rollBackData start billCoinTypeUserPropertyService.deleteByCheckTime accountType:{} rollbackTime:{}", accountType, DateUtil.date2str(checkTime));
                result = true;
                while (result) {
                    result = billCoinTypeUserPropertyService.deleteByCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime, batchSize);
                }
            }
            if (accountTypeEnum.haveUserPosition()) {
                log.info("business rollBackData start billSymbolCoinPropertyService.deleteByCheckTime accountType:{} rollbackTime:{}", accountType, DateUtil.date2str(checkTime));
                billSymbolCoinPropertyService.deleteByCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime);
                billSymbolPropertyService.deleteByCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime);
                billCapitalInitPropertyService.deleteByCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime, CapitalInitBusinessTypeEnum.CONTRACT_SYMBOL_REALIZED.getCode());
                log.info("business rollBackData start billUserPositionService.deleteByCheckTime accountType:{} rollbackTime:{}", accountType, DateUtil.date2str(checkTime));
                result = true;
                while (result) {
                    result = billUserPositionService.deleteByCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime, batchSize);
                }
            }
            Date startTime = new Date(checkTime.getTime() - (apolloBizConfig.getMergeTimeSliceSize() * apolloBizConfig.getTimeSliceSize() * 1000L));
            billCoinUserPropertyErrorService.deleteByCheckTime(accountType, accountTypeEnum.getAccountParam(), startTime, checkTime);
            if (apolloBizConfig.isTransferFeeOpen()) {
                billTransferFeeCoinDetailService.deleteByCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime);
            }
        }

        // 查询用户信息
        log.info("business rollBackData start billCoinUserPropertySnapshotService.selectCheckTimeRecord accountType:{} rollbackTime:{}", accountType, DateUtil.longToDate(resetCheckTime));
        Map<Integer, Set<Long>> coinUserMap = new HashMap<>();
        for (BillConfig billConfig : billConfigList) {
            Date checkTime = billConfig.getCheckOkTime();
            beginId = 0L;
            while (true) {
                List<BillCoinUserProperty> billCoinUserPropertySnapshots = billCoinUserPropertySnapshotService.selectCheckTimeRecord(Integer.valueOf(accountType), accountTypeEnum.getAccountParam(), checkTime, beginId, batchSize);
                if (CollectionUtils.isEmpty(billCoinUserPropertySnapshots)) {
                    break;
                }
                billCoinUserPropertySnapshots.forEach(billCoinUserProperty -> {
                    Set<Long> userSet = coinUserMap.computeIfAbsent(billCoinUserProperty.getCoinId(), item -> new HashSet<>());
                    userSet.add(billCoinUserProperty.getUserId());
                });
                beginId = billCoinUserPropertySnapshots.get(billCoinUserPropertySnapshots.size() - 1).getId();
            }
        }

        // 回置 bill_coin_user 纬度的数据
        log.info("business rollBackData start billCoinUserPropertySnapshotService.selectCoinUserBeforeCheckTimeRecord accountType:{} rollbackTime:{}", accountType, DateUtil.longToDate(resetCheckTime));
        List<BillCoinUserProperty> billCoinUserPropertyList = new ArrayList<>();
        for (Integer coinId : coinUserMap.keySet()) {
            Set<Long> userIdSet = coinUserMap.get(coinId);
            for (Long userId : userIdSet) {
                BillCoinUserProperty billCoinUserProperty = billCoinUserPropertySnapshotService.selectCoinUserBeforeCheckTimeRecord(Integer.valueOf(accountType), accountTypeEnum.getAccountParam(), coinId, userId, new Date(resetCheckTime));
                if (billCoinUserProperty != null) {
                    billCoinUserPropertyList.add(billCoinUserProperty);
                }
                if (billCoinUserPropertyList.size() >= 1000) {
                    try {
                        billCoinUserPropertyService.batchInsert(billCoinUserPropertyList, accountType, accountTypeEnum.getAccountParam());
                    } catch (Exception e) {
                        log.error("UserDataPersistenceService.rollBackData 回退数据异常");
                        billCoinUserPropertyService.batchInsertIgnore(billCoinUserPropertyList, accountType, accountTypeEnum.getAccountParam());
                    }
                    billCoinUserPropertyList = new ArrayList<>();
                }
            }
        }
        try {
            billCoinUserPropertyService.batchInsert(billCoinUserPropertyList, accountType, accountTypeEnum.getAccountParam());
        } catch (Exception e) {
            log.error("UserDataPersistenceService.rollBackData 回退数据异常");
            billCoinUserPropertyService.batchInsertIgnore(billCoinUserPropertyList, accountType, accountTypeEnum.getAccountParam());
        }

        // 删除配置信息
        log.info("business rollBackData start billCoinUserPropertySnapshotService.deleteByCheckTime accountType:{} rollbackTime:{}", accountType, DateUtil.longToDate(resetCheckTime));
        for (BillConfig billConfig : billConfigList) {
            result = true;
            while (result) {
                result = billCoinUserPropertySnapshotService.deleteByCheckTime(accountType, accountTypeEnum.getAccountParam(), billConfig.getCheckOkTime(), batchSize);
            }
        }
        // 更新业务线配置
        log.info("business rollBackData start billAllConfigService.updateByPrimaryKeySelective accountType:{} rollbackTime:{}", accountType, DateUtil.longToDate(resetCheckTime));
        BillAllConfig billAllConfigOld = billAllConfigService.selectByTypeAndParam(apolloBizConfig.getAccountType(), apolloBizConfig.getAccountParam());
        if (billAllConfigOld != null) {
            billAllConfigOld.setCheckOkTime(new Date(resetCheckTime));
            billAllConfigOld.setUpdateTime(new Date());
            billAllConfigService.updateByPrimaryKeySelective(billAllConfigOld);
        }
        // 删除配置信息
        billConfigService.deleteByGtCheckTime(accountType, accountTypeEnum.getAccountParam(), new Date(resetCheckTime));
        log.info("business rollBackData end accountType:{} rollbackTime:{}", accountType, DateUtil.longToDate(resetCheckTime));
    }


    public void fixCoinUserPropertyFromOldBill(Byte accountType, String accountParam, Long minIdParam, Integer
            pageSize) {
        Long minId = minIdParam != null ? minIdParam : 0L;
        List<BillCoinUserProperty> billCoinUserPropertyList = new ArrayList<>();
        while (true) {
            log.info("fixCoinUserPropertyFromOldBill inner loop with accountType {} minId {}", accountType, minId);
            List<BillUser> billUsers = oldBillUserService.selectUserIdList(accountType, accountParam, minId, pageSize);
            if (CollectionUtils.isEmpty(billUsers)) {
                break;
            }
            List<Long> uids = billUsers.stream().map(BillUser::getUserId).collect(Collectors.toList());
            for (Long uid : uids) {
                List<BillCoinUserProperty> billCoinUserPropertySnapshotList = billCoinUserPropertySnapshotService.selectMaxCheckTime(accountType, accountParam, uid);
                if (CollectionUtils.isEmpty(billCoinUserPropertySnapshotList)) {
                    continue;
                }
                List<Integer> coinIds = billCoinUserPropertySnapshotList.stream().map(BillCoinUserProperty::getCoinId).collect(Collectors.toList());
                List<BillCoinUserProperty> billCoinUserPropertySList = billCoinUserPropertyService.selectUserCoinIds(Integer.valueOf(accountType), accountParam, uid, coinIds);
                Set<Integer> newCoinIdSet = billCoinUserPropertySList.stream().map(BillCoinUserProperty::getCoinId).collect(Collectors.toSet());
                List<Integer> insertCoinIds = coinIds.stream().filter(item -> !newCoinIdSet.contains(item)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(insertCoinIds)) {
                    for (Integer coinId : insertCoinIds) {
                        BillCoinUserProperty billCoinUserPropertySnapshot = billCoinUserPropertySnapshotService.selectCoinUserLatest(Integer.valueOf(accountType), accountParam, coinId, uid);
                        if (billCoinUserPropertySnapshot != null) {
                            billCoinUserPropertyList.add(billCoinUserPropertySnapshot);
                        }
                        if (billCoinUserPropertyList.size() >= 1000) {
                            billCoinUserPropertyService.batchInsert(billCoinUserPropertyList, accountType, accountParam);
                            billCoinUserPropertyList = new ArrayList<>();
                        }
                    }
                }
            }
            minId = billUsers.get(billUsers.size() - 1).getId();
        }
        billCoinUserPropertyService.batchInsert(billCoinUserPropertyList, accountType, accountParam);
        log.info("finished fixCoinUserPropertyFromOldBill with accountType {} , count {}", accountType, billCoinUserPropertyList.size());
    }

    public boolean saveTimeSlice(ApolloReconciliationBizConfig apolloBizConfig, BillTimeSliceDTO
            billTimeSliceDTO, List<BillCoinUserProperty> coinUserInsertList, List<BillCoinUserProperty> coinUserUpdateList, List<BillCoinUserProperty> coinUserAssetInsertList) {
        Byte accountType = apolloBizConfig.getAccountType();
        String accountParam = apolloBizConfig.getAccountParam();
        Integer insertSqlMaxSize = apolloBizConfig.getSingleSqlMaxSize();
        Map<Integer, BillCoinProperty> coinPropertyMap = billTimeSliceDTO.getCoinPropertyMap();
        Map<String, BillCoinTypeProperty> coinTypePropertyMap = billTimeSliceDTO.getCoinTypePropertyMap();
        Map<String, BillSymbolCoinProperty> symbolCoinPropertyMap = billTimeSliceDTO.getSymbolCoinPropertyMap();
        Map<Long, Map<Integer, BillCoinUserProperty>> userPropertyMap = billTimeSliceDTO.getCoinUserPropertyMap();
        Map<Long, Map<String, BillCoinTypeUserProperty>> coinTypeUserPropertyMap = billTimeSliceDTO.getCoinTypeUserPropertyMap();
        Map<String, BillSymbolProperty> symbolPropertyMap = billTimeSliceDTO.getSymbolPropertyMap();
        List<BillUserPosition> billUserPositionList = billTimeSliceDTO.getBillUserPositionList();
        Map<String, BillCapitalInitProperty> symbolrealizedInitPropertyMap = billTimeSliceDTO.getSymbolrealizedInitPropertyMap();
        List<BillCoinUserPropertyError> billCoinUserPropertyErrorList = billTimeSliceDTO.getBillCoinUserPropertyErrorList();
        List<BillContractProfitCoinDetail> billContractProfitCoinDetailList = billTimeSliceDTO.getBillContractProfitCoinDetailList();
        List<BillContractProfitSymbolDetail> billContractProfitSymbolDetailList = billTimeSliceDTO.getBillContractProfitSymbolDetailList();
        List<BillContractProfitTransfer> billContractProfitTransferList = billTimeSliceDTO.getBillContractProfitTransferList();
        Collection<BillTransferFeeCoinDetail> billTransferFeeCoinDetailList = billTimeSliceDTO.getBillTransferFeeCoinDetailMap().values();
        // 1 个人纬度资产数据，有数据时进行update，无数据时进行插入
        // 用于reboot逻辑从db加载数据到内存
        StopWatch stopWatch = new StopWatch();
        Date now = new Date();
        Date checkTime = null;
        if (billTimeSliceDTO.getBillConfig() != null) {
            BillConfig billConfig = billTimeSliceDTO.getBillConfig();
            billConfig.setCreateTime(now);
            billConfig.setUpdateTime(now);
            checkTime = billConfig.getCheckOkTime();
            stopWatch.start("billConfigMapper.batchInsert");
            billConfigMapper.batchInsert(List.of(billConfig), apolloBizConfig.getAccountType(), apolloBizConfig.getAccountParam());
            stopWatch.stop();
        }
        Date lastCheckTime = null;
        if (Objects.nonNull(checkTime)) {
            lastCheckTime = new Date(TimeSliceCalcUtils.getPreTimeSlice(checkTime.getTime(), BillConstants.FIVE_MINE_MIL_SEC));
        }
        if (billTimeSliceDTO.getBillConfig() != null) {
            stopWatch.start("billAllConfigMapper.insertOrUpdate");
            BillAllConfig billAllConfigOld = billAllConfigMapper.selectByTypeAndParam(apolloBizConfig.getAccountType(), apolloBizConfig.getAccountParam());
            if (billAllConfigOld != null) {
                billAllConfigOld.setCheckOkTime(billTimeSliceDTO.getBillConfig().getCheckOkTime());
                billAllConfigOld.setUpdateTime(now);
                billAllConfigMapper.updateByPrimaryKeySelective(billAllConfigOld);
            } else {
                BillAllConfig billAllConfig = new BillAllConfig();
                billAllConfig.setAccountType(apolloBizConfig.getAccountType());
                billAllConfig.setAccountParam(apolloBizConfig.getAccountParam());
                billAllConfig.setCheckOkTime(billTimeSliceDTO.getBillConfig().getCheckOkTime());
                billAllConfig.setCreateTime(now);
                billAllConfig.setUpdateTime(now);
                billAllConfig.setSyncPos(0L);
                billAllConfigMapper.batchInsert(List.of(billAllConfig));
            }
            BizLogUtils.log(LogLevelEnum.MAIN_PROCESS, apolloBizConfig, "UserDataPersistenceService.saveTimeSlice bill bill_all_config batchInsert or update success {} , consume time {} ms", billTimeSliceDTO.getBillConfig().getCheckOkTime());
            stopWatch.stop();
        }

        // 按用户纬度尽心插库
        if (apolloBizConfig.isSaveOptimizeOpen()) {
            List<BillCoinUserProperty> insertList = new ArrayList<>();
            List<BillCoinUserProperty> updateList = new ArrayList<>();
            for (Map.Entry<Long, Map<Integer, BillCoinUserProperty>> entry : userPropertyMap.entrySet()) {
                for (BillCoinUserProperty bcuP : entry.getValue().values()) {
                    if (Objects.nonNull(lastCheckTime) && bcuP.getFristCreateTime().compareTo(lastCheckTime) > 0) {
                        insertList.add(bcuP);
                    } else {
                        updateList.add(bcuP);
                    }
                    if (insertList.size() >= insertSqlMaxSize) {
                        billCoinUserPropertyMapper.batchInsert(insertList, accountType, accountParam);
                        insertList = new ArrayList<>();
                    }
                    if (updateList.size() >= insertSqlMaxSize) {
                        billCoinUserPropertyMapper.batchUpdate(updateList, accountType, accountParam);
                        updateList = new ArrayList<>();
                    }
                }
            }
            if (insertList.size() > 0) {
                billCoinUserPropertyMapper.batchInsert(insertList, accountType, accountParam);
            }
            if (updateList.size() > 0) {
                billCoinUserPropertyMapper.batchUpdate(updateList, accountType, accountParam);
            }
        } else {
            stopWatch.start("billCoinUserPropertyMapper.batchInsert:" + apolloBizConfig.isSaveOptimizeOpen());
            List<List<BillCoinUserProperty>> insertCoinUserPropertyPartition = Lists.partition(coinUserInsertList, insertSqlMaxSize);
            for (List<BillCoinUserProperty> billCoinUserPropertyList : insertCoinUserPropertyPartition) {
                if (CollectionUtils.isNotEmpty(billCoinUserPropertyList)) {
                    billCoinUserPropertyMapper.batchInsert(billCoinUserPropertyList, accountType, accountParam);
                    billCoinUserPropertySnapshotMapper.batchInsert(billCoinUserPropertyList, accountType, accountParam);
                    BizLogUtils.log(LogLevelEnum.MAIN_PROCESS, apolloBizConfig, "UserDataPersistenceService.saveTimeSlice bill billCoinUserPropertyList batchInsert success {}", billCoinUserPropertyList.size());
                }
            }
            stopWatch.stop();
            stopWatch.start("billCoinUserPropertyMapper.batchUpdate:" + apolloBizConfig.isSaveOptimizeOpen());
            List<List<BillCoinUserProperty>> updateCoinUserPropertyPartition = Lists.partition(coinUserUpdateList, insertSqlMaxSize);
            for (List<BillCoinUserProperty> billCoinUserPropertyList : updateCoinUserPropertyPartition) {
                if (CollectionUtils.isNotEmpty(billCoinUserPropertyList)) {
                    billCoinUserPropertyMapper.batchUpdate(billCoinUserPropertyList, accountType, accountParam);
                    billCoinUserPropertySnapshotMapper.batchInsert(billCoinUserPropertyList, accountType, accountParam);
                    BizLogUtils.log(LogLevelEnum.MAIN_PROCESS, apolloBizConfig, "UserDataPersistenceService.saveTimeSlice bill billCoinUserPropertyList batchInsert success {}", billCoinUserPropertyList.size());
                }
            }
            stopWatch.stop();
        }

        // bill_coin_type_user落盘
        if (apolloBizConfig.isCoinTypeUserCheckOpen()) {
            stopWatch.start("billCoinTypeUserPropertyMapper.insertOrUpdate");
            List<BillCoinTypeUserProperty> billCoinTypeUserPropertyList = new ArrayList<>();
            for (Map.Entry<Long, Map<String, BillCoinTypeUserProperty>> entry : coinTypeUserPropertyMap.entrySet()) {
                if (MapUtils.isEmpty(entry.getValue())) {
                    continue;
                }
                billCoinTypeUserPropertyList.addAll(entry.getValue().values());
            }
            // 分批次落盘
            List<List<BillCoinTypeUserProperty>> billCoinTypeUserPropertyPartitions = Lists.partition(billCoinTypeUserPropertyList, insertSqlMaxSize);
            String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(accountType.toString(), billTimeSliceDTO.getBillConfig().getCheckOkTime());
            for (List<BillCoinTypeUserProperty> billCoinTypeUserList : billCoinTypeUserPropertyPartitions) {
                if (CollectionUtils.isNotEmpty(billCoinTypeUserList)) {
                    billCoinTypeUserPropertyMapper.batchInsert(billCoinTypeUserList, accountType, tableSuffix);
                    BizLogUtils.log(LogLevelEnum.MAIN_PROCESS, apolloBizConfig, "UserDataPersistenceService.saveTimeSlice bill billCoinTypeUserList batchInsert success {} ,size {}, consume time {} ms", billTimeSliceDTO.getBillConfig().getCheckOkTime(), billCoinTypeUserList.size());
                }
            }
            stopWatch.stop();
        }

        stopWatch.start("billCoinPropertyMapper.batchInsert");
        List<List<BillCoinProperty>> billCoinListOfList = Lists.partition(new ArrayList<>(coinPropertyMap.values()), insertSqlMaxSize);
        for (List<BillCoinProperty> billCoinPropertyList : billCoinListOfList) {
            if (CollectionUtils.isNotEmpty(billCoinPropertyList)) {
                billCoinPropertyMapper.batchInsert(billCoinPropertyList, accountType, accountParam);
                BizLogUtils.log(LogLevelEnum.MAIN_PROCESS, apolloBizConfig, "UserDataPersistenceService.saveTimeSlice bill billCoinPropertyList batchInsert success {} ,size {}, consume time {} ms", billTimeSliceDTO.getBillConfig().getCheckOkTime(), billCoinPropertyList.size());
            }
        }
        stopWatch.stop();

        stopWatch.start("billCoinTypePropertyMapper.batchInsert");
        List<List<BillCoinTypeProperty>> billCoinTypeListOfList = Lists.partition(new ArrayList<>(coinTypePropertyMap.values()), insertSqlMaxSize);
        for (List<BillCoinTypeProperty> billCoinTypePropertyList : billCoinTypeListOfList) {
            if (CollectionUtils.isNotEmpty(billCoinTypePropertyList)) {
                billCoinTypePropertyMapper.batchInsert(billCoinTypePropertyList, accountType, accountParam);
                BizLogUtils.log(LogLevelEnum.MAIN_PROCESS, apolloBizConfig, "UserDataPersistenceService.saveTimeSlice bill billCoinTypePropertyList batchInsert success {} ,size {}, consume time {} ms", billTimeSliceDTO.getBillConfig().getCheckOkTime(), billCoinTypePropertyList.size());
            }
        }
        stopWatch.stop();

        stopWatch.start("billCoinUserPropertyErrorService.batchInsert");
        List<List<BillCoinUserPropertyError>> billCoinUserPropertyErrorListOfList = Lists.partition(billCoinUserPropertyErrorList, insertSqlMaxSize);
        for (List<BillCoinUserPropertyError> coinUserPropertyErrorList : billCoinUserPropertyErrorListOfList) {
            billCoinUserPropertyErrorService.batchInsert(coinUserPropertyErrorList, accountType, accountParam);
            BizLogUtils.log(LogLevelEnum.MAIN_PROCESS, apolloBizConfig, "UserDataPersistenceService.saveTimeSlice bill coinUserPropertyErrorList batchInsert success {} ,size {}, consume time {} ms", billTimeSliceDTO.getBillConfig().getCheckOkTime(), coinUserPropertyErrorList.size());
        }
        stopWatch.stop();

        stopWatch.start("billSymbolCoinPropertyMapper.batchInsert");
        List<List<BillSymbolCoinProperty>> billSymbolCoinListOfList = Lists.partition(new ArrayList<>(symbolCoinPropertyMap.values()), insertSqlMaxSize);
        for (List<BillSymbolCoinProperty> billSymbolCoinProperties : billSymbolCoinListOfList) {
            if (CollectionUtils.isNotEmpty(billSymbolCoinProperties)) {
                billSymbolCoinPropertyMapper.batchInsert(billSymbolCoinProperties, accountType, accountParam);
                log.info("UserDataPersistenceService.saveTimeSlice bill billSymbolCoinProperties save success {} ,size {}", billTimeSliceDTO.getBillConfig().getCheckOkTime(), billSymbolCoinProperties.size());

            }
        }
        stopWatch.stop();

        // 交易对已实现持久化
        if (CollectionUtils.isNotEmpty(symbolPropertyMap.values())) {
            stopWatch.start("billSymbolPropertyService.batchInsert");
            List<List<BillSymbolProperty>> billSymbolPropertyPartitions = Lists.partition(new ArrayList<>(symbolPropertyMap.values()), insertSqlMaxSize);
            for (List<BillSymbolProperty> symbolProperties : billSymbolPropertyPartitions) {
                billSymbolPropertyService.batchInsert(symbolProperties, accountType, accountParam);
                BizLogUtils.log(LogLevelEnum.MAIN_PROCESS, apolloBizConfig, "UserDataPersistenceService.saveTimeSlice bill symbolPropertyMap batchInsert success {} ,size {}, consume time {} ms", billTimeSliceDTO.getBillConfig().getCheckOkTime(), symbolProperties.size());
            }
            stopWatch.stop();
        }

        if (CollectionUtils.isNotEmpty(billUserPositionList)) {
            stopWatch.start("billUserPositionService.batchInsert");
            List<List<BillUserPosition>> positionPartitions = Lists.partition(billUserPositionList.stream()
                    .filter(item -> (item.getLCount().compareTo(BigDecimal.ZERO) > 0 || item.getSCount().compareTo(BigDecimal.ZERO) > 0))
                    .collect(Collectors.toList()), insertSqlMaxSize);
            for (List<BillUserPosition> positionList : positionPartitions) {
                billUserPositionService.batchInsert(positionList, accountType, accountParam);
                BizLogUtils.log(LogLevelEnum.MAIN_PROCESS, apolloBizConfig, "UserDataPersistenceService.saveTimeSlice bill positionPartitions batchInsert success {} ,size {}, consume time {} ms", billTimeSliceDTO.getBillConfig().getCheckOkTime(), positionList.size());
            }
            stopWatch.stop();
        }

        // 交易对期初持久化
        if (CollectionUtils.isNotEmpty(symbolrealizedInitPropertyMap.values())) {
            stopWatch.start("symbolrealizedInitPropertyMap.batchInsert");
            List<BillCapitalInitProperty> capitalInitPropertyList = billCapitalInitPropertyService.selectRecords(String.valueOf(accountType), accountParam, CapitalInitBusinessTypeEnum.CONTRACT_SYMBOL_REALIZED.getCode());
            Map<String, BillCapitalInitProperty> capitalInitPropertyMap = capitalInitPropertyList.stream().collect(Collectors.toMap(BillCapitalInitProperty::getSymbolId, item -> item));
            List<BillCapitalInitProperty> insertCapitalInitPropertyList = new ArrayList<>();
            symbolrealizedInitPropertyMap.forEach((symbolId, capitalInitProperty) -> {
                if (capitalInitPropertyMap.get(symbolId) == null) {
                    insertCapitalInitPropertyList.add(capitalInitProperty);
                }
            });
            billCapitalInitPropertyService.batchInsert(insertCapitalInitPropertyList);
            BizLogUtils.log(LogLevelEnum.MAIN_PROCESS, apolloBizConfig, "UserDataPersistenceService.saveTimeSlice bill insertCapitalInitPropertyList batchInsert success {} ,size {}, consume time {} ms", billTimeSliceDTO.getBillConfig().getCheckOkTime(), insertCapitalInitPropertyList.size());
            stopWatch.stop();
        }

        if (CollectionUtils.isNotEmpty(billTransferFeeCoinDetailList)) {
            stopWatch.start("billContractProfitTransferService.batchInsert");
            billTransferFeeCoinDetailService.batchInsert(billTransferFeeCoinDetailList, accountType, accountParam);
            log.info("UserDataPersistenceService.saveTimeSlice bill BillContractProfitTransferList save success {} ,size {}", billTimeSliceDTO.getBillConfig().getCheckOkTime(), billTransferFeeCoinDetailList.size());
            stopWatch.stop();
        }

        if (CollectionUtils.isNotEmpty(billContractProfitTransferList)) {
            stopWatch.start("billContractProfitTransferService.batchInsert");
            List<List<BillContractProfitTransfer>> billContractProfitTransferLists = Lists.partition(billTimeSliceDTO.getBillContractProfitTransferList(), insertSqlMaxSize);
            for (List<BillContractProfitTransfer> billContractProfitTransfers : billContractProfitTransferLists) {
                billContractProfitTransferService.batchInsert(billContractProfitTransfers);
                log.info("UserDataPersistenceService.saveTimeSlice bill BillContractProfitTransferList save success {} ,size {}", billTimeSliceDTO.getBillConfig().getCheckOkTime(), billContractProfitTransfers.size());
            }
            stopWatch.stop();
        }

        if (CollectionUtils.isNotEmpty(billContractProfitSymbolDetailList)) {
            stopWatch.start("billContractProfitSymbolDetailService.batchInsert");
            List<List<BillContractProfitSymbolDetail>> billContractProfitSymbolDetailLists = Lists.partition(billTimeSliceDTO.getBillContractProfitSymbolDetailList(), insertSqlMaxSize);
            for (List<BillContractProfitSymbolDetail> contractProfitSymbolDetails : billContractProfitSymbolDetailLists) {
                billContractProfitSymbolDetailService.batchInsert(accountType, accountParam, contractProfitSymbolDetails);
                log.info("UserDataPersistenceService.saveTimeSlice bill BillContractProfitSymbolDetailList save success {} ,size {}", billTimeSliceDTO.getBillConfig().getCheckOkTime(), contractProfitSymbolDetails.size());
            }
            stopWatch.stop();
        }

        if (CollectionUtils.isNotEmpty(billContractProfitCoinDetailList)) {
            stopWatch.start("billContractProfitCoinDetailService.batchInsert");
            List<List<BillContractProfitCoinDetail>> billContractProfitCoinDetailLists = Lists.partition(billTimeSliceDTO.getBillContractProfitCoinDetailList(), insertSqlMaxSize);
            for (List<BillContractProfitCoinDetail> billContractProfitSymbolDetails : billContractProfitCoinDetailLists) {
                billContractProfitCoinDetailService.batchInsert(accountType, accountParam, billContractProfitSymbolDetails);
                log.info("UserDataPersistenceService.saveTimeSlice bill BillContractProfitCoinDetailList save success {} ,size {}", billTimeSliceDTO.getBillConfig().getCheckOkTime(), billContractProfitSymbolDetails.size());
            }
            stopWatch.stop();
        }

        log.info("UserDataPersistenceService.saveTimeSlice save time slice success accountType:{} checkTime:{} coinPropertyMapSize:{} coinTypePropertyMapSize:{} userPropertyMap:{} coinUserInsertList:{} coinUserUpdateList:{} symbolPropertyMap:{} symbolCoinPropertyMapSize:{} billUserPositionList:{} billTransferFeeCoinDetailListSize:{} billContractProfitTransferListSize:{} billContractProfitSymbolDetailListSize:{} billContractProfitCoinDetailListSize:{} elapsed time log:{}", accountType, DateUtil.date2str(checkTime), coinPropertyMap.values().size(), coinTypePropertyMap.values().size(), userPropertyMap.values().size(), coinUserInsertList.size(), coinUserUpdateList.size(), symbolPropertyMap.values().size(), symbolCoinPropertyMap.values().size(), billUserPositionList.size(), billTransferFeeCoinDetailList.size(), billContractProfitTransferList.size(), billContractProfitSymbolDetailList.size(), billContractProfitCoinDetailList.size(), stopWatch.prettyPrint());
        return true;
    }


    public void resetCheckOkData(Byte accountType, String accountParam, Long resetTimestamp, Long deleteSize) {
        Long beginId = 0L;
        Boolean result = true;
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        // 校验入参时间的有效性
        BillConfig BillConfig = billConfigService.selectByTypeAndParamAndCheckTime(accountType, accountParam, new Date(resetTimestamp));
        if (BillConfig == null) {
            log.error("UserDataPersistenceService.resetCheckOkData invalid resetTimestamp {} ", resetTimestamp);
            return;
        }

        List<BillConfig> billConfigList = billConfigService.selectByTypeAndParamAfterCheckTime(accountType, accountParam, new Date(resetTimestamp));
        Map<Integer, Set<Long>> coinUserMap = new HashMap<>();
        for (BillConfig billConfig : billConfigList) {
            Date checkTime = billConfig.getCheckOkTime();
            billConfigService.deleteByCheckTime(accountType, accountParam, checkTime);
            billCoinPropertyService.deleteByCheckTime(accountType, accountParam, checkTime);
            billCoinTypePropertyService.deleteByCheckTime(accountType, accountParam, checkTime);

            beginId = 0L;
            while (true) {
                List<BillCoinUserProperty> billCoinUserPropertySnapshots = billCoinUserPropertySnapshotService.selectCheckTimeRecord(Integer.valueOf(accountType), accountParam, checkTime, beginId, deleteSize);
                if (CollectionUtils.isEmpty(billCoinUserPropertySnapshots)) {
                    break;
                }
                billCoinUserPropertySnapshots.forEach(billCoinUserProperty -> {
                    Set<Long> userSet = coinUserMap.computeIfAbsent(billCoinUserProperty.getCoinId(), item -> new HashSet<>());
                    userSet.add(billCoinUserProperty.getUserId());
                });
                beginId = billCoinUserPropertySnapshots.get(billCoinUserPropertySnapshots.size() - 1).getId();
            }

            result = true;
            while (result) {
                result = billCoinUserPropertySnapshotService.deleteByCheckTime(accountType, accountParam, checkTime, deleteSize);
            }
            result = true;
            while (result) {
                result = billCoinUserPropertyService.deleteByCheckTime(accountType, accountParam, checkTime, deleteSize);
            }

            if (accountTypeEnum.haveUserPosition()) {
                billSymbolCoinPropertyService.deleteByCheckTime(accountType, accountParam, checkTime);
                billSymbolPropertyService.deleteByCheckTime(accountType, accountParam, checkTime);
                billUserPositionService.deleteByCheckTime(accountType, accountParam, checkTime, deleteSize);
                billCapitalInitPropertyService.deleteByCheckTime(accountType, accountParam, checkTime, CapitalInitBusinessTypeEnum.CONTRACT_SYMBOL_REALIZED.getCode());
            }
        }

        result = true;
        beginId = 0L;
        while (result) {
            result = tbBillIdempotentRecordService.batchDelete(beginId, deleteSize, accountType, accountParam);
            beginId = beginId + deleteSize;
        }

        // 回置 bill_coin_user 纬度的数据
        List<BillCoinUserProperty> billCoinUserPropertyList = new ArrayList<>();
        for (Integer coinId : coinUserMap.keySet()) {
            Set<Long> userIdSet = coinUserMap.get(coinId);
            for (Long userId : userIdSet) {
                BillCoinUserProperty billCoinUserProperty = billCoinUserPropertySnapshotService.selectCoinUserBeforeCheckTimeRecord(Integer.valueOf(accountType), accountParam, coinId, userId, new Date(resetTimestamp));
                if (billCoinUserProperty != null) {
                    billCoinUserPropertyList.add(billCoinUserProperty);
                }
                if (billCoinUserPropertyList.size() >= 1000) {
                    billCoinUserPropertyService.batchInsert(billCoinUserPropertyList, accountType, accountParam);
                    billCoinUserPropertyList = new ArrayList<>();
                }
            }
        }
        billCoinUserPropertyService.batchInsert(billCoinUserPropertyList, accountType, accountParam);
    }

    public void cleanData(Byte accountType, String accountParam, Long deleteSize) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);

        Long minId = 0L;
        Boolean result = true;
        // 删除 bill_coin_property_50_default
        while (result) {
            result = billCoinPropertyService.batchDelete(minId, deleteSize, accountType, accountParam);
            minId = minId + deleteSize;
        }

        result = true;
        minId = 0L;
        // 删除 bill_coin_type_property_50_default
        while (result) {
            result = billCoinTypePropertyService.batchDelete(minId, deleteSize, accountType, accountParam);
            minId = minId + deleteSize;
        }

        result = true;
        minId = 0L;
        // 删除 bill_coin_user_property_snapshot_50_default
        while (result) {
            result = billCoinUserPropertySnapshotService.batchDelete(minId, deleteSize, accountType, accountParam);
            minId = minId + deleteSize;
        }
        result = true;
        minId = 0L;
        // 删除 bill_coin_user_property_snapshot_50_default
        while (result) {
            result = billCoinUserPropertyAssetSnapshotService.batchDelete(minId, deleteSize, accountType, accountParam);
            minId = minId + deleteSize;
        }
        result = true;
        minId = 0L;
        // 删除 bill_coin_user_property_50_default
        while (result) {
            result = billCoinUserPropertyService.batchDelete(minId, deleteSize, accountType, accountParam);
            minId = minId + deleteSize;
        }
        result = true;
        minId = 0L;
        // 删除 bill_coin_user_property_error_50_default
        while (result) {
            result = billCoinUserPropertyErrorService.batchDelete(minId, deleteSize, accountType, accountParam);
            minId = minId + deleteSize;
        }
        if (accountTypeEnum.haveUserPosition()) {
            result = true;
            minId = 0L;
            // 删除 bill_symbol_coin_property_50_default
            while (result) {
                result = billSymbolCoinPropertyService.batchDelete(minId, deleteSize, accountType, accountParam);
                minId = minId + deleteSize;
            }
            result = true;
            minId = 0L;
            // 删除 bill_symbol_property_50_default
            while (result) {
                result = billSymbolPropertyService.batchDelete(minId, deleteSize, accountType, accountParam);
                minId = minId + deleteSize;
            }
            billCapitalInitPropertyService.deleteByBusinessType(accountType, accountParam, CapitalInitBusinessTypeEnum.CONTRACT_SYMBOL_REALIZED.getCode());
            result = true;
            minId = 0L;
            // 删除 bill_user_position_50_default 默认删除当天表
            Date nowDate = new Date();
            String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(accountType.toString(), nowDate);
            while (result) {
                result = billUserPositionService.deleteAll(accountType, accountParam, tableSuffix, deleteSize);
            }
        }

        if (accountTypeEnum.haveContractProfitDetail()) {
            result = true;
            minId = 0L;
            // 删除 bill_user_position_50_default
            while (result) {
                result = billContractProfitSymbolDetailService.batchDelete(minId, deleteSize, accountType, accountParam);
                minId = minId + deleteSize;
            }
            result = true;
            minId = 0L;
            // 删除 bill_user_position_50_default
            while (result) {
                result = billContractProfitCoinDetailService.batchDelete(minId, deleteSize, accountType, accountParam);
                minId = minId + deleteSize;
            }
            result = true;
            minId = 0L;
            // 删除 bill_user_position_50_default
//            while (result) {
//                result = billContractProfitTransferService.batchDelete(minId, deleteSize, accountType, accountParam);
//                minId = minId + deleteSize;
//            }
        }

        result = true;
        minId = 0L;
        // 删除 tb_bill_idempotent_record_50_default
        while (result) {
            result = tbBillIdempotentRecordService.batchDelete(minId, deleteSize, accountType, accountParam);
            minId = minId + deleteSize;
        }

        result = true;
        minId = 0L;
        // 删除billConfig
        while (result) {
            Long maxId = minId + deleteSize;
            result = billConfigService.batchDelete(accountType, accountParam, minId, deleteSize);
            minId = maxId;
        }
    }

    /**
     * 构建用户资产更新 插入队列
     */
    public boolean buildCoinUserPropertyInsertOrUpdateList(ApolloReconciliationBizConfig
                                                                   apolloBizConfig, BillTimeSliceDTO
                                                                   billTimeSliceDTO, List<BillCoinUserProperty> insertList, List<BillCoinUserProperty> updateList, List<BillCoinUserProperty> insertAssetList) {
        Date checkTime = billTimeSliceDTO.getBillConfig().getCheckOkTime();
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        List<BillCoinUserProperty> allCoinUserPropertyList = billTimeSliceDTO.getTimeSliceCoinUserPropertyList();
        if (CollectionUtils.isEmpty(allCoinUserPropertyList)) {
            return true;
        }
        // 获取合约换汇账号
        Long exchangeUserId = null;
        if (accountTypeEnum.haveUserPosition()) {
            exchangeUserId = reconSystemAccountService.queryExchangeUserId(accountTypeEnum);
        }
        // 判断是新增还是更新
        for (BillCoinUserProperty billCoinUserProperty : allCoinUserPropertyList) {
            BillCoinUserProperty lastDbCoinUserProperty = billTimeSliceDTO.getLastDbCoinUserPropertyMap().get(billCoinUserProperty.groupByCoinUser());
            if (lastDbCoinUserProperty != null && lastDbCoinUserProperty.getId() != null) {
                billCoinUserProperty.setId(lastDbCoinUserProperty.getId());
                updateList.add(billCoinUserProperty);
            } else {
                insertList.add(billCoinUserProperty);
            }
            // 如果是合约换汇账户 设置期末值 新增 propx=changepropx 更新 propx=propx+changepropx
            if (exchangeUserId != null && exchangeUserId.equals(billCoinUserProperty.getUserId())) {
                billCoinUserProperty.setProp1(lastDbCoinUserProperty != null ? lastDbCoinUserProperty.getProp1().add(billCoinUserProperty.getChangeProp1()) : billCoinUserProperty.getChangeProp1());
                billCoinUserProperty.setProp2(lastDbCoinUserProperty != null ? lastDbCoinUserProperty.getProp2().add(billCoinUserProperty.getChangeProp2()) : billCoinUserProperty.getChangeProp2());
                billCoinUserProperty.setProp3(lastDbCoinUserProperty != null ? lastDbCoinUserProperty.getProp3().add(billCoinUserProperty.getChangeProp3()) : billCoinUserProperty.getChangeProp3());
                billCoinUserProperty.setProp4(lastDbCoinUserProperty != null ? lastDbCoinUserProperty.getProp4().add(billCoinUserProperty.getChangeProp4()) : billCoinUserProperty.getChangeProp4());
                billCoinUserProperty.setProp5(lastDbCoinUserProperty != null ? lastDbCoinUserProperty.getProp5().add(billCoinUserProperty.getChangeProp5()) : billCoinUserProperty.getChangeProp5());
            }
            // sprop赋值 上期期末+当期change
            billCoinUserProperty.setSprop1(lastDbCoinUserProperty != null ? lastDbCoinUserProperty.getSprop1().add(billCoinUserProperty.getChangeProp1()) : billCoinUserProperty.getProp1());
            billCoinUserProperty.setSprop2(lastDbCoinUserProperty != null ? lastDbCoinUserProperty.getSprop2().add(billCoinUserProperty.getChangeProp2()) : billCoinUserProperty.getProp2());
            billCoinUserProperty.setSprop3(lastDbCoinUserProperty != null ? lastDbCoinUserProperty.getSprop3().add(billCoinUserProperty.getChangeProp3()) : billCoinUserProperty.getProp3());
            billCoinUserProperty.setSprop4(lastDbCoinUserProperty != null ? lastDbCoinUserProperty.getSprop4().add(billCoinUserProperty.getChangeProp4()) : billCoinUserProperty.getProp4());
            billCoinUserProperty.setSprop5(lastDbCoinUserProperty != null ? lastDbCoinUserProperty.getSprop5().add(billCoinUserProperty.getChangeProp5()) : billCoinUserProperty.getProp5());
            billCoinUserProperty.setSprop6(lastDbCoinUserProperty != null ? lastDbCoinUserProperty.getSprop6().add(billCoinUserProperty.getChangeProp6()) : billCoinUserProperty.getProp6());
            billCoinUserProperty.setSprop7(lastDbCoinUserProperty != null ? lastDbCoinUserProperty.getSprop7().add(billCoinUserProperty.getChangeProp7()) : billCoinUserProperty.getProp7());
            billCoinUserProperty.setSprop8(lastDbCoinUserProperty != null ? lastDbCoinUserProperty.getSprop8().add(billCoinUserProperty.getChangeProp8()) : billCoinUserProperty.getProp8());
        }
        return true;
    }

    /**
     * 存盘之前写入kafka
     *
     * @param apolloBizConfig
     * @param coinUserInsertList
     * @param coinUserUpdateList
     */
    public void sendKafkaMessage(ApolloReconciliationBizConfig apolloBizConfig, BillTimeSliceDTO
            billTimeSliceDTO, List<BillCoinUserProperty> coinUserInsertList, List<BillCoinUserProperty> coinUserUpdateList) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        for (BillCoinUserProperty billCoinUserProperty : coinUserInsertList) {
            billCoinUserProperty.setAccountType(accountTypeEnum.getCode());
            billCoinUserProperty.setAccountParam(accountTypeEnum.getAccountParam());
            if (apolloBizConfig.getSendKafkaUserIncrementSyncTopicOpen()) {
                SyncBillUserDTO syncBillUserDTO = new SyncBillUserDTO();
                syncBillUserDTO.setAccountType(accountTypeEnum.getCode());
                syncBillUserDTO.setUserId(billCoinUserProperty.getUserId());
                syncBillUserDTO.setRegisterTime(billTimeSliceDTO.getBillConfig().getCheckOkTime());
                ProducerRecord<String, String> incrementRecord = new ProducerRecord<>
                        (KafkaTopicEnum.RECON_USER_INCREMENT_SYNC_TOPIC.getCode(), syncBillUserDTO.getUserId().toString(), JSON.toJSONString(syncBillUserDTO));
                kafkaProducer.send(incrementRecord);
            }
            if (apolloBizConfig.getSendKafkaUserSnapshotSyncTopicOpen()) {
                ProducerRecord<String, String> snapshotRecord = new ProducerRecord<>(KafkaTopicEnum.RECON_USER_SNAPSHOT_SYNC_TOPIC.getCode(), billCoinUserProperty.getUserId().toString(), JSON.toJSONString(billCoinUserProperty));
                kafkaProducer.send(snapshotRecord);
            }
        }
        if (apolloBizConfig.getSendKafkaUserSnapshotSyncTopicOpen()) {
            for (BillCoinUserProperty billCoinUserProperty : coinUserUpdateList) {
                billCoinUserProperty.setAccountType(accountTypeEnum.getCode());
                billCoinUserProperty.setAccountParam(accountTypeEnum.getAccountParam());
                ProducerRecord<String, String> snapshotRecord = new ProducerRecord<>(KafkaTopicEnum.RECON_USER_SNAPSHOT_SYNC_TOPIC.getCode(), billCoinUserProperty.getUserId().toString(), JSON.toJSONString(billCoinUserProperty));
                kafkaProducer.send(snapshotRecord);
            }
        }
        if (apolloBizConfig.getSendKafkaUserPositionSyncTopicOpen()) {
            List<BillUserPosition> billUserPositionList = billTimeSliceDTO.getBillUserPositionList();
            String time = DateUtil.date2str(billTimeSliceDTO.getBillConfig().getCheckOkTime(), "HH:mm:ss");
            if (CollectionUtils.isNotEmpty(billUserPositionList)
                    && apolloBizConfig.getSendKafkaUserPositionTime().contains(time)
                    && accountTypeEnum.haveUserPosition()) {
                for (BillUserPosition billUserPosition : billUserPositionList) {
                    billUserPosition.setAccountType(accountTypeEnum.getCode());
                    billUserPosition.setAccountParam(accountTypeEnum.getAccountParam());
                    ProducerRecord<String, String> snapshotRecord = new ProducerRecord<>(KafkaTopicEnum.RECON_USER_POSITION_SYNC_TOPIC.getCode(), billUserPosition.getUserId().toString(), JSON.toJSONString(billUserPosition));
                    kafkaProducer.send(snapshotRecord);
                }
            }
        }
    }

    /**
     * 删除coinTypeUser数据
     *
     * @param accountType
     * @param accountParam
     * @param startCheckTime
     * @param endCheckTime
     * @param deleteSize
     */
    public void deleteCoinTypeUserProperty(Byte accountType, String accountParam, Long startCheckTime, Long
            endCheckTime, Long deleteSize) {
        log.info("deleteCoinTypeUserProperty start accountType:{} startCheckTime:{} endCheckTime:{}", accountParam, startCheckTime, endCheckTime);
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        Date startTime = new Date(startCheckTime + globalBillConfig.getMergeTimeSliceSizeSecond() * BillConstants.MILLIS_PER_SECOND);
        while (startTime.getTime() < endCheckTime) {
            boolean result = true;
            while (result) {
                result = billCoinTypeUserPropertyService.deleteByCheckTime(accountType, accountParam, startTime, deleteSize);
            }
            startTime = new Date(startTime.getTime() + globalBillConfig.getMergeTimeSliceSizeSecond() * BillConstants.MILLIS_PER_SECOND);
            log.info("deleteCoinTypeUserProperty next time accountType:{} startCheckTime:{} endCheckTime:{}", accountParam, startCheckTime, endCheckTime);
        }
        log.info("deleteCoinTypeUserProperty end accountType:{} startCheckTime:{} endCheckTime:{}", accountParam, startCheckTime, endCheckTime);
    }

    /**
     * 回退已使用授信资产
     *
     * @param assetCheckType 总账类型
     * @param resetCheckTime 回退时间
     * <AUTHOR>
     * @date 2023/11/28 16:01
     */
    private void resetShadowFinanceCheckTime(String assetCheckType, Date resetCheckTime) {
        if (AssetsCheckTypeEnum.isInternalTotalAssets(assetCheckType)) {
            return;
        }

        oldBillCapitalCoinUserShadowCreditPropertyService.removeResetTimeShadowCreditList(resetCheckTime);
        oldBillCapitalCoinShadowCreditPropertyService.removeResetTimeShadowCreditList(resetCheckTime);
    }


    /**
     * 验证参数
     */
    private void checkParam(Byte accountType, String accountParam, Long rollbackTime, Long batchSize, String
            assetAccountType, String assetAccountParam) {
        // 回退时关闭提币检测
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        if (globalBillConfig.isAlarmCheck()) {
            log.info("resetCheckTime checkParam error, globalBillConfig.isAlarmCheck() is true, please modify false");
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }

        if (rollbackTime > System.currentTimeMillis()) {
            log.info("resetCheckTime checkParam error, resetCheckTime > 当前时间, please modify resetCheckTime");
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }
    }

    /**
     * 打印提币盈利检测告警
     *
     * @param jobParam
     */
    public void userProfitAlarmStartByUserId(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        String action = jsonObject.getString("action");
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        if ("add".equals(action)) {
            Long userId = jsonObject.getLong("userId");
            Long requestDate = jsonObject.getLong("requestDate");
            redisTemplate.opsForList().rightPush(String.format(BillRedisKeyEnum.RECON_WITHDRAWAL_PROFIT_RISK_USER_ALARM.getKey()), JSON.toJSONString(Map.of("userId", userId, "requestDate", requestDate)));
        } else if ("alarm".equals(action)) {
//            Long userId1 = jsonObject.getLong("userId");
//            Long requestDate1 = jsonObject.getLong("requestDate");
//            redisTemplate.opsForList().rightPush(String.format(BillRedisKeyEnum.RECON_WITHDRAWAL_PROFIT_RISK_USER_ALARM.getKey()), JSON.toJSONString(Map.of("userId", userId1, "requestDate", requestDate1)));
            while(true){
                String redisUserInfo = (String) redisTemplate.opsForList().leftPop(String.format(BillRedisKeyEnum.RECON_WITHDRAWAL_PROFIT_RISK_USER_ALARM.getKey()));
                if (StringUtils.isEmpty(redisUserInfo)) {
                    return;
                }

                JSONObject redisUserInfoJson = JSONObject.parseObject(redisUserInfo);
                Long userId = redisUserInfoJson.getLong("userId");
                Long requestDate = redisUserInfoJson.getLong("requestDate");
                Map<Integer, Map<String, BigDecimal>> parentUserCoinBizTypeProfitMap = getUserProfitCheckModuleCoinBizProfit(userId, requestDate);
                Map<Integer, Map<String, BigDecimal>> subTotalUserCoinBizTypeProfitMap = new HashMap<>();
                if (globalBillConfig.isCheckProfitAccountChildAccount()) {
                    org.apache.commons.lang3.tuple.Pair<Boolean, List<Long>> pair = userQueryService.getChildListByParentId(userId, globalBillConfig.getChildAccountTypeList(), globalBillConfig.getMaxChildAccountCount(), globalBillConfig.getMaxChildAccountCount());
                    if (pair.getLeft()) {
                        if (CollectionUtils.isNotEmpty(pair.getRight())) {
                            for (Long childId : pair.getRight()) {
                                Map<Integer, Map<String, BigDecimal>> subUserCoinBizTypeProfitMap = getUserProfitCheckModuleCoinBizProfit(childId, requestDate);
                                subUserCoinBizTypeProfitMap.forEach((coinId, bizTypeProfitMap) -> {
                                    bizTypeProfitMap.forEach((bizType, profit) -> {
                                        subTotalUserCoinBizTypeProfitMap.computeIfAbsent(coinId, k -> new HashMap<>()).compute(bizType, (k, oldValue) -> oldValue == null ? profit : oldValue.add(profit));
                                    });
                                });
                            }
                        } else {
                            log.info("checkProfitAccount userId:{} childAccount exceeds:{}", userId, globalBillConfig.getMaxChildAccountCount());
                        }
                    }
                }

                Map<Integer, PriceVo> rates = commonService.getCoinIdTradePriceMap(requestDate);
                Map<String, BigDecimal> totalSumProfitMap = new HashMap<>();
                totalSumProfitMap.put("all", BigDecimal.ZERO);
                Map<String, Map<String, BigDecimal>> totalUserCoinProfitMap = new TreeMap<>();
                // 折U 告警 父用户
                Map<String, Map<String, BigDecimal>> parentUserCoinBizTypeProfitUsdtMap = new TreeMap<>();
                parentUserCoinBizTypeProfitMap.forEach((coinId, bizTypeProfitMap) -> {
                    bizTypeProfitMap.forEach((bizType, profit) -> {
                        BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(coinId, rates);
                        BigDecimal profitAmountUsdt = profit.multiply(rate);
                        parentUserCoinBizTypeProfitUsdtMap.computeIfAbsent(coinId.toString(), k -> new TreeMap<>()).compute(bizType, (k, oldValue) -> oldValue == null ? profitAmountUsdt : oldValue.add(profitAmountUsdt));
                        totalUserCoinProfitMap.computeIfAbsent(coinId.toString(), k -> new HashMap<>()).compute("profit", (k, oldValue) -> oldValue == null ? profitAmountUsdt : oldValue.add(profitAmountUsdt));
                        totalSumProfitMap.compute("all", (k, oldValue) -> oldValue == null ? profitAmountUsdt : oldValue.add(profitAmountUsdt));
                    });
                });
                // 折U 告警 子用户
                Map<String, Map<String, BigDecimal>> subTotalUserCoinBizTypeProfitUsdtMap = new TreeMap<>();
                subTotalUserCoinBizTypeProfitMap.forEach((coinId, bizTypeProfitMap) -> {
                    bizTypeProfitMap.forEach((bizType, profit) -> {
                        BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(coinId, rates);
                        BigDecimal profitAmountUsdt = profit.multiply(rate);
                        subTotalUserCoinBizTypeProfitUsdtMap.computeIfAbsent(coinId.toString(), k -> new TreeMap<>()).compute(bizType, (k, oldValue) -> oldValue == null ? profitAmountUsdt : oldValue.add(profitAmountUsdt));
                        totalUserCoinProfitMap.computeIfAbsent(coinId.toString(), k -> new HashMap<>()).compute("profit", (k, oldValue) -> oldValue == null ? profitAmountUsdt : oldValue.add(profitAmountUsdt));
                        totalSumProfitMap.compute("all", (k, oldValue) -> oldValue == null ? profitAmountUsdt : oldValue.add(profitAmountUsdt));
                    });
                });
                // 总盈利增加价格
                totalUserCoinProfitMap.forEach((coinId, coinProfitMap) -> {
                    coinProfitMap.put("price", commonService.checkRateBySwapTokenIdReturnUSDT(Integer.valueOf(coinId), rates));
                });

                Map<String, Object> alarmMap = new HashMap<>();
                alarmMap.put("userId", userId);
                alarmMap.put("profit", parentUserCoinBizTypeProfitUsdtMap);
                alarmMap.put("subProfit", subTotalUserCoinBizTypeProfitUsdtMap);
                alarmMap.put("totalProfit", totalUserCoinProfitMap);
                alarmMap.put("totalSumProfit", totalSumProfitMap.get("all"));
                alarmNotifyService.alarm(USER_WITHDRAWAL_CHECK_PROFIT_ERROR, userId, JSONObject.toJSONString(alarmMap));
                log.info("userProfitAlarmStartByUserId userId:{} alarmMap:{}");
            }
        }
    }

    /**
     * 获取盈利检测数据
     *
     * @param userId
     * @param requestDate
     * @return
     */
    private Map<Integer, Map<String, BigDecimal>> getUserProfitCheckModuleCoinBizProfit(Long userId, Long requestDate) {
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        Long beginDate = DateUtil.addMinute(new Date(requestDate), BillConstants.NEG_ONE * 1440).getTime();
        List<String> checkProfitSubSystemList = globalBillConfig.getCheckProfitSubSystemList();
        Map<Integer, Map<String, BigDecimal>> totalUserCoinBizTypeProfitMap = new HashMap<>();
        for (int i = 0; i < globalBillConfig.getCheckProfitSubSystemList().size(); i++) {
            Byte accountType = Byte.valueOf(checkProfitSubSystemList.get(i).split(SEPARATOR)[0]);
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
            String consumerKey = KafkaTopicEnum.RECON_SYNC_BUSINESS_BILL_FLOW_TOPIC.getConsumerName() + "-" + accountType;
            KafkaConsumerConfig kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig(consumerKey);
            if (kafkaConsumerConfig == null) {
                continue;
            }
            if (!EnvUtil.isRunningKafkaConsumer(consumerKey) || !kafkaConsumerConfig.getIsOpen()) {
                log.info("userProfitStartByAccountTypeAndUser start not running instance {} consumerName:{}  kafkaConsumerConfig:{}", EnvUtil.getServiceInstanceName(), consumerKey, JSONObject.toJSONString(kafkaConsumerConfig));
                continue;
            }
            BillUserProfitCheckModule userProfitCheckModule = billEngineManager.getUserProfitCheckModule(accountType);
            if (userProfitCheckModule == null) {
                continue;
            }
            Map<Integer, Map<String, BigDecimal>> userCoinBizTypeProfitMap = userProfitCheckModule.recalculateUserCoinBizTypeProfit(userId, beginDate, requestDate);
            userCoinBizTypeProfitMap.forEach((coinId, bizTypeProfitMap) -> {
                bizTypeProfitMap.forEach((bizType, profit) -> {
                    String assetsBizType = accountTypeEnum.getBizTypePrefix() + BillConstants.SEPARATOR + bizType;
                    totalUserCoinBizTypeProfitMap.computeIfAbsent(coinId, k -> new HashMap<>()).compute(assetsBizType, (k, oldValue) -> oldValue == null ? profit : oldValue.add(profit));
                });
            });
        }
        return totalUserCoinBizTypeProfitMap;
    }

}

