package com.upex.reconciliation.service.utils;

import org.springframework.context.expression.MapAccessor;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

/**
 * El表达式工具类
 * <AUTHOR>
 */
public class SpelExpressionUtils {
    public static <T> T getValue(Object object,String expression,Class<T> clazz) {
        ExpressionParser parser = new SpelExpressionParser();
        Expression exp = parser.parseExpression(expression);
        StandardEvaluationContext context = new StandardEvaluationContext(object);
        // 使用MapAcessor属性访问器直接访问对象属性
        context.addPropertyAccessor(new MapAccessor());
        return exp.getValue(context,clazz);
    }
}
