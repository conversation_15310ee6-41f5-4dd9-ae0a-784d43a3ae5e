package com.upex.reconciliation.service.common.delay;

/**
 * 重试次数变量
 *
 * <AUTHOR>
 * @Date 2024/12/16
 */
public class RetryCountThreadLocal {

    /**
     * 重试次数
     */
    private static ThreadLocal<Integer> RETRY_THREAD_LOCAL = ThreadLocal.withInitial(() -> new Integer(-1));

    public static void set(Integer retryCount) {
        RETRY_THREAD_LOCAL.set(retryCount);
    }

    public static Integer get() {
        return RETRY_THREAD_LOCAL.get();
    }

    public static Integer removeAndGet() {
        Integer retryCount = get();
        remove();
        return retryCount;
    }

    public static void remove() {
        RETRY_THREAD_LOCAL.remove();
    }
}
