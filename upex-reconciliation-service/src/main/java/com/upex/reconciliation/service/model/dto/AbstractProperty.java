package com.upex.reconciliation.service.model.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.PropEnum;
import com.upex.reconciliation.service.utils.MatchUtils;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: allen
 * @Date: 2020-05-16 18:36
 * @DES: 抽离属性值
 */
@Data
public abstract class AbstractProperty extends AbstractLedgerMessage implements Serializable {
    /**
     * 资产值1变化
     */
    protected BigDecimal changeProp1 = BigDecimal.ZERO;

    /**
     * 资产值1
     */
    protected BigDecimal prop1 = BigDecimal.ZERO;

    /**
     * 资产值2变化
     */
    protected BigDecimal changeProp2 = BigDecimal.ZERO;

    /**
     * 资产值2
     */
    protected BigDecimal prop2 = BigDecimal.ZERO;

    /**
     * 资产值3变化
     */
    protected BigDecimal changeProp3 = BigDecimal.ZERO;

    /**
     * 资产值3
     */
    protected BigDecimal prop3 = BigDecimal.ZERO;

    /**
     * 资产值4变化
     */
    protected BigDecimal changeProp4 = BigDecimal.ZERO;

    /**
     * 资产值4
     */
    protected BigDecimal prop4 = BigDecimal.ZERO;

    /**
     * 资产值5变化
     */
    protected BigDecimal changeProp5 = BigDecimal.ZERO;

    /**
     * 资产值5
     */
    protected BigDecimal prop5 = BigDecimal.ZERO;

    /**
     * 资产值6变化
     */
    protected BigDecimal changeProp6 = BigDecimal.ZERO;

    /**
     * 资产值6
     */
    protected BigDecimal prop6 = BigDecimal.ZERO;

    /**
     * 资产值7变化
     */
    protected BigDecimal changeProp7 = BigDecimal.ZERO;

    /**
     * 资产值7
     */
    protected BigDecimal prop7 = BigDecimal.ZERO;

    /**
     * 资产值8变化
     */
    protected BigDecimal changeProp8 = BigDecimal.ZERO;

    /**
     * 资产值8
     */
    protected BigDecimal prop8 = BigDecimal.ZERO;

    /**
     * 是否修改
     * 代表的是数据，有没有变动，无论是资产还是时间的变化
     */
    protected Boolean updateFlag = false;

    /**
     * 从业务系统初始化的时间
     */
    protected Date fristCreateTime = new Date(0L);
    /***手续费***/
    protected BigDecimal changeFee = BigDecimal.ZERO;
    /***扩展参数***/
    protected String params;

    /**
     * 清除变化
     */
    public void cleanChangeProp() {
        this.setChangeProp1(BigDecimal.ZERO);
        this.setChangeProp2(BigDecimal.ZERO);
        this.setChangeProp3(BigDecimal.ZERO);
        this.setChangeProp4(BigDecimal.ZERO);
        this.setChangeProp5(BigDecimal.ZERO);
        this.setChangeProp6(BigDecimal.ZERO);
        this.setChangeProp7(BigDecimal.ZERO);
        this.setChangeProp8(BigDecimal.ZERO);
    }

    public void cleanProp() {
        this.setProp1(BigDecimal.ZERO);
        this.setProp2(BigDecimal.ZERO);
        this.setProp3(BigDecimal.ZERO);
        this.setProp4(BigDecimal.ZERO);
        this.setProp5(BigDecimal.ZERO);
        this.setProp6(BigDecimal.ZERO);
        this.setProp7(BigDecimal.ZERO);
        this.setProp8(BigDecimal.ZERO);
    }

    public void cleaPropAndChangeProp() {
        cleanChangeProp();
        cleanProp();
    }

    /**
     * prop1+prop2+prop3+prop4+prop5
     *
     * @return
     */
    public BigDecimal getPropSum() {
        return this.prop1.add(this.prop2).add(this.prop3).add(this.prop4).add(this.prop5).add(this.prop6).add(this.prop7).add(this.prop8);
    }

    public BigDecimal getProp2to5Sum() {
        return this.prop2.add(this.prop3).add(this.prop4).add(this.prop5);
    }

    public BigDecimal getChangeProp2to5Sum() {
        return this.changeProp2.add(this.changeProp3).add(this.changeProp4).add(this.changeProp5);
    }

    /**
     * changeProp1+changeProp2+changeProp3+changeProp4+changeProp5
     *
     * @return
     */
    public BigDecimal getChangePropSum() {
        return this.changeProp1.add(this.changeProp2).add(this.changeProp3).add(this.changeProp4).add(this.changeProp5).add(this.changeProp6).add(this.changeProp7).add(this.changeProp8);
    }

    /**
     * 币币联合检测
     *
     * @return
     */
    public BigDecimal getSpotComboCheck() {
        return this.prop1.add(this.prop3);
    }

    /**
     * 杠杆资产计算prop1+prop2
     *
     * @return
     */
    public BigDecimal getLeverSum() {
        return this.prop1.add(this.prop2);
    }

    /**
     * 理财资产计算changeProp2+changeProp3
     *
     * @return
     */
    public BigDecimal getProp2Prop3Sum() {
        return this.prop2.add(this.prop3);
    }

    /**
     * changeProp是否都是0
     *
     * @return
     */
    public boolean isChangePropZero() {
        return (this.getChangeProp1().compareTo(BigDecimal.ZERO) == 0
                && this.getChangeProp2().compareTo(BigDecimal.ZERO) == 0
                && this.getChangeProp3().compareTo(BigDecimal.ZERO) == 0
                && this.getChangeProp4().compareTo(BigDecimal.ZERO) == 0
                && this.getChangeProp5().compareTo(BigDecimal.ZERO) == 0
                && this.getChangeProp6().compareTo(BigDecimal.ZERO) == 0
                && this.getChangeProp7().compareTo(BigDecimal.ZERO) == 0
                && this.getChangeProp8().compareTo(BigDecimal.ZERO) == 0);
    }
    
    /**
     * changeProp是否都是0
     *
     * @return
     */
    public boolean isChangePropSumZero() {
        BigDecimal changePropSum = this.changeProp1.add(this.changeProp2).add(this.changeProp3).add(this.changeProp4).add(this.changeProp5).add(this.changeProp6).add(this.changeProp7).add(this.changeProp8);
        return changePropSum.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 期末值加当期change
     *
     * @param currentProp 当期change
     */
    public void getPropByCurrent(AbstractProperty currentProp) {
        this.setProp1(this.getProp1().add(currentProp.getChangeProp1()));
        this.setChangeProp1(this.getChangeProp1().add(currentProp.getChangeProp1()));
        this.setProp2(this.getProp2().add(currentProp.getChangeProp2()));
        this.setChangeProp2(this.getChangeProp2().add(currentProp.getChangeProp2()));
        this.setProp3(this.getProp3().add(currentProp.getChangeProp3()));
        this.setChangeProp3(this.getChangeProp3().add(currentProp.getChangeProp3()));
        this.setProp4(this.getProp4().add(currentProp.getChangeProp4()));
        this.setChangeProp4(this.getChangeProp4().add(currentProp.getChangeProp4()));
        this.setProp5(this.getProp5().add(currentProp.getChangeProp5()));
        this.setChangeProp5(this.getChangeProp5().add(currentProp.getChangeProp5()));
        this.setProp6(this.getProp6().add(currentProp.getChangeProp6()));
        this.setChangeProp6(this.getChangeProp6().add(currentProp.getChangeProp6()));
        this.setProp7(this.getProp7().add(currentProp.getChangeProp7()));
        this.setChangeProp7(this.getChangeProp7().add(currentProp.getChangeProp7()));
        this.setProp8(this.getProp8().add(currentProp.getChangeProp8()));
        this.setChangeProp8(this.getChangeProp8().add(currentProp.getChangeProp8()));
        this.setChangeFee(this.getChangeFee().add(currentProp.getChangeFee()));
        BigDecimal fee = BigDecimal.ZERO;
        if (MatchUtils.isNotEmptyJson(this.getParams())) {
            JSONObject paramJson = JSON.parseObject(this.getParams());
            fee = paramJson.getBigDecimal("fee") != null ? paramJson.getBigDecimal("fee") : BigDecimal.ZERO;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("changeFee", this.getChangeFee());
        paramMap.put("fee", fee.add(currentProp.getChangeFee()));
        this.setParams(JSON.toJSONString(paramMap));
    }

    public void collectCurrentChange(AbstractProperty currentProp) {
        this.setProp1(currentProp.getProp1());
        this.setChangeProp1(this.getChangeProp1().add(currentProp.getChangeProp1()));
        this.setProp2(currentProp.getProp2());
        this.setChangeProp2(this.getChangeProp2().add(currentProp.getChangeProp2()));
        this.setProp3(currentProp.getProp3());
        this.setChangeProp3(this.getChangeProp3().add(currentProp.getChangeProp3()));
        this.setProp4(currentProp.getProp4());
        this.setChangeProp4(this.getChangeProp4().add(currentProp.getChangeProp4()));
        this.setProp5(currentProp.getProp5());
        this.setChangeProp5(this.getChangeProp5().add(currentProp.getChangeProp5()));
        this.setProp6(currentProp.getProp6());
        this.setChangeProp6(this.getChangeProp6().add(currentProp.getChangeProp6()));
        this.setProp7(currentProp.getProp7());
        this.setChangeProp7(this.getChangeProp7().add(currentProp.getChangeProp7()));
        this.setProp8(currentProp.getProp8());
        this.setChangeProp8(this.getChangeProp8().add(currentProp.getChangeProp8()));
    }


    /**
     * 期末值初始化，先将期末值置为期初
     *
     * @param beginProp 期初资产
     */
    public void getPropByBefore(AbstractProperty beginProp, boolean needBefore) {
        this.setProp1(this.getProp1().add(beginProp.getProp1()));
        this.setProp2(this.getProp2().add(beginProp.getProp2()));
        this.setProp3(this.getProp3().add(beginProp.getProp3()));
        this.setProp4(this.getProp4().add(beginProp.getProp4()));
        this.setProp5(this.getProp5().add(beginProp.getProp5()));
        this.setProp6(this.getProp6().add(beginProp.getProp6()));
        this.setProp7(this.getProp7().add(beginProp.getProp7()));
        this.setProp8(this.getProp8().add(beginProp.getProp8()));
        if (needBefore) {
            this.setChangeProp1(this.getChangeProp1().add(beginProp.getChangeProp1()));
            this.setChangeProp2(this.getChangeProp2().add(beginProp.getChangeProp2()));
            this.setChangeProp3(this.getChangeProp3().add(beginProp.getChangeProp3()));
            this.setChangeProp4(this.getChangeProp4().add(beginProp.getChangeProp4()));
            this.setChangeProp5(this.getChangeProp5().add(beginProp.getChangeProp5()));
            this.setChangeProp6(this.getChangeProp6().add(beginProp.getChangeProp6()));
            this.setChangeProp7(this.getChangeProp7().add(beginProp.getChangeProp7()));
            this.setChangeProp8(this.getChangeProp8().add(beginProp.getChangeProp8()));
            this.setChangeFee(this.getChangeFee().add(beginProp.getChangeFee()));
        }
        BigDecimal fee = BigDecimal.ZERO;
        if (MatchUtils.isNotEmptyJson(beginProp.getParams())) {
            JSONObject paramJson = JSON.parseObject(beginProp.getParams());
            fee = paramJson.getBigDecimal("fee") != null ? paramJson.getBigDecimal("fee") : BigDecimal.ZERO;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("changeFee", this.getChangeFee());
        paramMap.put("fee", fee);
        this.setParams(JSON.toJSONString(paramMap));

    }

    public static BigDecimal addChangeData(AccountTypeEnum accountTypeEnum, AbstractProperty abstractProperty) {
        switch (accountTypeEnum) {
            case FINANCIAL:
                return abstractProperty.getChangeProp1();
            case LEVER_FULL:
            case LEVER_ONE:
            case P_LEVER_FULL:
            case P_LEVER_ONE:
            case DEMO_LEVER_ONE:
            case DEMO_LEVER_FULL:
                return abstractProperty.getChangeProp1().add(abstractProperty.getChangeProp2());
            case S_USDT_MIX_CONTRACT_BL:
            case S_USD_MIX_CONTRACT_BL:
            case USDT_MIX_CONTRACT_BL:
            case USD_MIX_CONTRACT_BL:
            case USDC_MIX_CONTRACT_BL:
            case S_USDC_MIX_CONTRACT_BL:
            case P_USDT_MIX_CONTRACT_BL:
            case P_USD_MIX_CONTRACT_BL:
            case P_USDC_MIX_CONTRACT_BL:
                return abstractProperty.getChangeProp2()
                        .add(abstractProperty.getChangeProp3());
            case UNIFIED:
                return abstractProperty.getChangeProp2()
                        .add(abstractProperty.getChangeProp3());
            default:
                return abstractProperty.getChangeProp1()
                        .add(abstractProperty.getChangeProp2())
                        .add(abstractProperty.getChangeProp3())
                        .add(abstractProperty.getChangeProp4())
                        .add(abstractProperty.getChangeProp5());
        }
    }

    /**
     * 判断资产是否有负数
     *
     * @return
     */
    public boolean isNegativeProp() {
        if (prop1 != null && prop1.compareTo(BigDecimal.ZERO) < 0) {
            return true;
        }
        if (prop2 != null && prop2.compareTo(BigDecimal.ZERO) < 0) {
            return true;
        }
        if (prop3 != null && prop3.compareTo(BigDecimal.ZERO) < 0) {
            return true;
        }
        if (prop4 != null && prop4.compareTo(BigDecimal.ZERO) < 0) {
            return true;
        }
        if (prop5 != null && prop5.compareTo(BigDecimal.ZERO) < 0) {
            return true;
        }
        return false;
    }

    /**
     * 判断资产是否有正数
     *
     * @return
     */
    public boolean isPositiveProp() {
        if (prop1 != null && prop1.compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        if (prop2 != null && prop2.compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        if (prop3 != null && prop3.compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        if (prop4 != null && prop4.compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        if (prop5 != null && prop5.compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        return false;
    }

    /**
     * prop是否都是0
     *
     * @return
     */
    public boolean isPropZero() {
        return (this.getProp1().compareTo(BigDecimal.ZERO) == 0
                && this.getProp2().compareTo(BigDecimal.ZERO) == 0
                && this.getProp3().compareTo(BigDecimal.ZERO) == 0
                && this.getProp4().compareTo(BigDecimal.ZERO) == 0
                && this.getProp5().compareTo(BigDecimal.ZERO) == 0
                && this.getProp6().compareTo(BigDecimal.ZERO) == 0
                && this.getProp7().compareTo(BigDecimal.ZERO) == 0
                && this.getProp8().compareTo(BigDecimal.ZERO) == 0);
    }

    public BigDecimal getFee() {
        BigDecimal fee = BigDecimal.ZERO;
        if (MatchUtils.isNotEmptyJson(this.getParams())) {
            JSONObject paramJson = JSON.parseObject(this.getParams());
            fee = paramJson.getBigDecimal("fee") != null ? paramJson.getBigDecimal("fee") : BigDecimal.ZERO;
        }
        return fee;
    }

    /**
     * 获取业务线change
     *
     * @return
     */
    public BigDecimal getChangePropSum(Byte accountType) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        BigDecimal propSum = BigDecimal.ZERO;
        switch (accountTypeEnum) {
            case LEVER_FULL:
            case LEVER_ONE:
            case P_LEVER_FULL:
            case P_LEVER_ONE:
            case DEMO_LEVER_ONE:
            case DEMO_LEVER_FULL:
                propSum = this.changeProp1.add(this.changeProp2);
                break;
            case USDT_MIX_CONTRACT_BL:
            case USD_MIX_CONTRACT_BL:
            case USDC_MIX_CONTRACT_BL:
            case P_USDT_MIX_CONTRACT_BL:
            case P_USD_MIX_CONTRACT_BL:
            case P_USDC_MIX_CONTRACT_BL:
                propSum = this.changeProp2.add(this.changeProp3);
                break;
            case FINANCIAL:
                propSum = this.changeProp1;
                break;
            default:
                propSum = this.getChangePropSum();
                break;
        }
        return propSum;
    }

    public void addChangeProp(AbstractProperty abstractProperty) {
        this.setChangeProp1(this.getChangeProp1().add(abstractProperty.getChangeProp1()));
        this.setChangeProp2(this.getChangeProp2().add(abstractProperty.getChangeProp2()));
        this.setChangeProp3(this.getChangeProp3().add(abstractProperty.getChangeProp3()));
        this.setChangeProp4(this.getChangeProp4().add(abstractProperty.getChangeProp4()));
        this.setChangeProp5(this.getChangeProp5().add(abstractProperty.getChangeProp5()));
        this.setChangeProp6(this.getChangeProp6().add(abstractProperty.getChangeProp6()));
        this.setChangeProp7(this.getChangeProp7().add(abstractProperty.getChangeProp7()));
        this.setChangeProp8(this.getChangeProp8().add(abstractProperty.getChangeProp8()));
    }

    public void setChangePropZero() {
        this.setChangeProp1(BigDecimal.ZERO);
        this.setChangeProp2(BigDecimal.ZERO);
        this.setChangeProp3(BigDecimal.ZERO);
        this.setChangeProp4(BigDecimal.ZERO);
        this.setChangeProp5(BigDecimal.ZERO);
        this.setChangeProp6(BigDecimal.ZERO);
        this.setChangeProp7(BigDecimal.ZERO);
        this.setChangeProp8(BigDecimal.ZERO);
    }

    public static void addAssetsProp(AbstractProperty before, AbstractProperty after) {
        before.setProp1(before.getProp1().add(after.getChangeProp1()));
        before.setProp2(before.getProp2().add(after.getChangeProp2()));
        before.setProp3(before.getProp3().add(after.getChangeProp3()));
        before.setProp4(before.getProp4().add(after.getChangeProp4()));
        before.setProp5(before.getProp5().add(after.getChangeProp5()));
        before.setProp6(before.getProp6().add(after.getChangeProp6()));
        before.setProp7(before.getProp7().add(after.getChangeProp7()));
        before.setProp8(before.getProp8().add(after.getChangeProp8()));
        before.setChangeProp1(before.getChangeProp1().add(after.getChangeProp1()));
        before.setChangeProp2(before.getChangeProp2().add(after.getChangeProp2()));
        before.setChangeProp3(before.getChangeProp3().add(after.getChangeProp3()));
        before.setChangeProp4(before.getChangeProp4().add(after.getChangeProp4()));
        before.setChangeProp5(before.getChangeProp5().add(after.getChangeProp5()));
        before.setChangeProp6(before.getChangeProp6().add(after.getChangeProp6()));
        before.setChangeProp7(before.getChangeProp7().add(after.getChangeProp7()));
        before.setChangeProp8(before.getChangeProp8().add(after.getChangeProp8()));
    }

    /**
     * 获取合约右币 已实现
     *
     * @param accountTypeEnum
     * @return
     */
    public BigDecimal getQuoteTokenAssets(AccountTypeEnum accountTypeEnum) {
        if (accountTypeEnum.isUta()) {
            return this.getProp7();
        } else if (accountTypeEnum.isContract()) {
            return this.getProp5();
        }
        return BigDecimal.ZERO;
    }

    /**
     * 根据propEnum设置值
     *
     * @param propEnum
     * @param value
     */
    public void setPropByPropEnum(PropEnum propEnum, BigDecimal value) {
        switch (propEnum) {
            case PROP_1:
                this.setProp1(value);
                break;
            case PROP_2:
                this.setProp2(value);
                break;
            case PROP_3:
                this.setProp3(value);
                break;
            case PROP_4:
                this.setProp4(value);
                break;
            case PROP_5:
                this.setProp5(value);
                break;
            case PROP_6:
                this.setProp6(value);
                break;
            case PROP_7:
                this.setProp7(value);
                break;
            case PROP_8:
                this.setProp8(value);
                break;
            default:
                throw new IllegalArgumentException("propEnum is not valid+" + propEnum.name());
        }
    }

    /**
     * 根据propEnum设置值
     *
     * @param propEnum
     * @param value
     */
    public void setChangePropByPropEnum(PropEnum propEnum, BigDecimal value) {
        switch (propEnum) {
            case PROP_1:
                this.setChangeProp1(value);
                break;
            case PROP_2:
                this.setChangeProp2(value);
                break;
            case PROP_3:
                this.setChangeProp3(value);
                break;
            case PROP_4:
                this.setChangeProp4(value);
                break;
            case PROP_5:
                this.setChangeProp5(value);
                break;
            case PROP_6:
                this.setChangeProp6(value);
                break;
            case PROP_7:
                this.setChangeProp7(value);
                break;
            case PROP_8:
                this.setChangeProp8(value);
                break;
            default:
                throw new IllegalArgumentException("propEnum is not valid+" + propEnum.name());
        }
    }

    /**
     * 根据propEnum设置值
     *
     * @param propEnum
     */
    public BigDecimal getChangePropByPropEnum(PropEnum propEnum) {
        switch (propEnum) {
            case PROP_1:
                return this.getChangeProp1();
            case PROP_2:
                return this.getChangeProp2();
            case PROP_3:
                return this.getChangeProp3();
            case PROP_4:
                return this.getChangeProp4();
            case PROP_5:
                return this.getChangeProp5();
            case PROP_6:
                return this.getChangeProp6();
            case PROP_7:
                return this.getChangeProp7();
            case PROP_8:
                return this.getChangeProp8();
            default:
                throw new IllegalArgumentException("propEnum is not valid+" + propEnum.name());
        }
    }

    /**
     * 根据propEnum设置值
     *
     * @param propEnum
     */
    public BigDecimal getPropByPropEnum(PropEnum propEnum) {
        switch (propEnum) {
            case PROP_1:
                return this.getProp1();
            case PROP_2:
                return this.getProp2();
            case PROP_3:
                return this.getProp3();
            case PROP_4:
                return this.getProp4();
            case PROP_5:
                return this.getProp5();
            case PROP_6:
                return this.getProp6();
            case PROP_7:
                return this.getProp7();
            case PROP_8:
                return this.getProp8();
            default:
                throw new IllegalArgumentException("propEnum is not valid+" + propEnum.name());
        }
    }
}
