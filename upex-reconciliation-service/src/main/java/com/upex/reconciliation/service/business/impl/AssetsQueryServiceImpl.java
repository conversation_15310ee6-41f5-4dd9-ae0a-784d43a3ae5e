package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fiat.fund.facade.client.account.FundAccountQueryFacade;
import com.fiat.fund.facade.model.request.account.FundBalanceQueryRequest;
import com.fiat.fund.facade.model.response.account.FundAccountBalanceResponse;
import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.commons.support.model.ResponseResult;
import com.upex.convert.facade.feign.inner.InnerConvertOrderFeignClient;
import com.upex.convert.facade.results.OslBalanceVo;
import com.upex.financial.dto.params.common.req.FinancialVirtualAccountBalanceParam;
import com.upex.financial.dto.params.common.rsp.FinancialVirtualAccountBalanceRsp;
import com.upex.financial.facade.account.FinancialAccountInnerFeign;
import com.upex.margin.facade.inner.InnerMarginOverviewFeignClient;
import com.upex.margin.req.inner.AssetsOverviewReq;
import com.upex.margin.res.inner.AssetsOverviewRes;
import com.upex.margin.res.inner.CrossTokenAssetsRes;
import com.upex.margin.res.inner.IsolateSymbolAssetsRes;
import com.upex.mixcontract.process.facade.feign.inner.InnerAccountFeignClient;
import com.upex.mixcontract.process.facade.params.query.AccountQueryParam;
import com.upex.mixcontract.process.facade.results.AccountListDTO;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AssetsQueryService;
import com.upex.reconciliation.service.business.BusinessRegistry;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.common.listener.AssetsRetryerListener;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.BusinessLineMappingEnum;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.reconciliation.service.utils.RetryUtils;
import com.upex.spot.dto.params.assets.QueryAssetsDTO;
import com.upex.spot.dto.result.assets.UserAssetsResult;
import com.upex.spot.facade.query.SpotAssetsQueryServiceClient;
import com.upex.utils.framework.ApiResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class AssetsQueryServiceImpl implements AssetsQueryService {

    @Resource
    private SpotAssetsQueryServiceClient spotAssetsQueryServiceClient;
    @Resource
    private FundAccountQueryFacade fundAccountQueryFacade;
    @Resource
    private InnerAccountFeignClient innerAccountFeignClient;
    @Resource
    private InnerMarginOverviewFeignClient innerMarginOverviewFeignClient;
    @Resource
    private CommonService commonService;
    @Resource
    private AssetsRetryerListener assetsRetryerListener;
    @Resource
    private FinancialAccountInnerFeign financialAccountInnerFeign;
    @Autowired
    private BusinessRegistry businessRegistry;
    @Resource
    private InnerConvertOrderFeignClient innerConvertOrderFeignClient;



    @Override
    public List<AccountAssetsInfoResult> queryUserAssets(Long userId, AccountTypeEnum accountTypeEnum, GlobalBillConfig globalBillConfig) throws ExecutionException, RetryException {
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<AccountAssetsInfoResult> userAssetList;
        switch (accountTypeEnum) {
            case SPOT:
                userAssetList = new ArrayList<>(listSpotAssets(userId, accountTypeEnum));
                // 如果是OSL承兑账户，则查询OSL账户余额并合并
                Map<Long, BigDecimal> oslFlashExConfig = globalBillConfig.getOslFlashExConfig();
                if (!oslFlashExConfig.isEmpty() && oslFlashExConfig.containsKey(userId)) {
                    List<AccountAssetsInfoResult> oslBalance = queryOslBalance(userId, accountTypeEnum);
                    userAssetList = new ArrayList<>(Stream.concat(userAssetList.stream(), oslBalance.stream()).collect(Collectors.toMap(AccountAssetsInfoResult::getCoinId, e -> e, this::mergeBalance))
                            .values());
                }
                break;
            case OTC:
                userAssetList = new ArrayList<>(listOtcAssets(userId, accountTypeEnum));
                break;
            case LEVER_FULL:
            case LEVER_ONE:
                userAssetList = new ArrayList<>(listLeverAssets(userId, accountTypeEnum));
                break;
            case USDT_MIX_CONTRACT_BL:
            case USD_MIX_CONTRACT_BL:
            case USDC_MIX_CONTRACT_BL:
                userAssetList = new ArrayList<>(listMixAssets(userId, accountTypeEnum));
                break;
            case FINANCIAL:
                userAssetList = new ArrayList<>(listFinancialAssets(userId, accountTypeEnum));
                break;
            default:
                throw new IllegalArgumentException("业务线类型不存在，accountType:"+accountTypeEnum.getUniqKey());
        }
        log.info("queryUserAssets response userId:{},accountTypeEnum:{},userAssetList size:{},time:{}"
                ,userId,accountTypeEnum.getUniqKey(), JSONObject.toJSONString(userAssetList), stopwatch.stop());
        return userAssetList;
    }

    private AccountAssetsInfoResult mergeBalance(AccountAssetsInfoResult e1, AccountAssetsInfoResult e2) {
        AccountAssetsInfoResult result = new AccountAssetsInfoResult();
        BeanUtils.copyProperties(e1, result);
        result.setProp1(safeAdd(e1.getProp1(), e2.getProp1()));
        result.setProp2(safeAdd(e1.getProp2(), e2.getProp2()));
        result.setProp3(safeAdd(e1.getProp3(), e2.getProp3()));
        result.setProp4(safeAdd(e1.getProp4(), e2.getProp4()));
        result.setProp5(safeAdd(e1.getProp5(), e2.getProp5()));
        return result;
    }

    private static BigDecimal safeAdd(BigDecimal v1, BigDecimal v2) {
        if (v1 == null && v2 == null) {
            return null;
        }
        if (v1 == null) {
            return v2;
        }
        if (v2 == null) {
            return v1;
        }
        return v1.add(v2);
    }

    private List<AccountAssetsInfoResult> queryOslBalance(Long userId, AccountTypeEnum accountTypeEnum) {
        log.info("OslFlashEx, userId: {}, accountTypeEnum: {}", userId, accountTypeEnum);
        ApiResult<List<OslBalanceVo>> listApiResult = innerConvertOrderFeignClient.curPosition();
        log.info("OslFlashEx, queryResult: {}", JSON.toJSONString(listApiResult));
        if (listApiResult == null || listApiResult.getData() == null) {
            log.error("queryOslBalance empty");
            return new ArrayList<>();
        }
        return listApiResult.getData().stream().map(balance -> {
            AccountAssetsInfoResult accountAssetsInfoResult = new AccountAssetsInfoResult();
            accountAssetsInfoResult.setUserId(userId);
            accountAssetsInfoResult.setCoinId(balance.getCoinId());
            accountAssetsInfoResult.setProp1(balance.getExposure());
            accountAssetsInfoResult.setAccountType(accountTypeEnum.getCode());
            accountAssetsInfoResult.setAccountParam(BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM);
            return accountAssetsInfoResult;
        }).collect(Collectors.toList());
    }


    /**
     * 实时查询现货资产
     * @param userId
     * @param accountTypeEnum
     * @return
     */
    private List<AccountAssetsInfoResult> listSpotAssets(Long userId,  AccountTypeEnum accountTypeEnum) throws ExecutionException, RetryException {
        QueryAssetsDTO queryAssetsDTO = new QueryAssetsDTO();
        queryAssetsDTO.setUserId(userId);

        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        Retryer<ResponseResult<UserAssetsResult>> retryHandle = RetryUtils.getRetryer(globalBillConfig.getSleepTime(), globalBillConfig.getReTryNumber(), TimeUnit.MILLISECONDS, assetsRetryerListener);
        ResponseResult<UserAssetsResult> userAssetsResultResponseResult = retryHandle.call(() -> spotAssetsQueryServiceClient.assetsV2(queryAssetsDTO));
        if(Objects.isNull(userAssetsResultResponseResult) || Objects.isNull(userAssetsResultResponseResult.getData())){
            return Lists.newArrayList();
        }

        UserAssetsResult userAssetsResult = userAssetsResultResponseResult.getData();
        return userAssetsResult.getBalanceList().stream().map(balance -> {
            AccountAssetsInfoResult accountAssetsInfoResult = new AccountAssetsInfoResult();
            accountAssetsInfoResult.setUserId(userId);
            accountAssetsInfoResult.setCoinId(balance.getCoinId());
            accountAssetsInfoResult.setProp1(new BigDecimal(balance.getAvailable()));
            accountAssetsInfoResult.setProp2(new BigDecimal(balance.getLock()));
            accountAssetsInfoResult.setProp3(new BigDecimal(balance.getFrozen()));
            accountAssetsInfoResult.setAccountType(accountTypeEnum.getCode());
            accountAssetsInfoResult.setAccountParam(BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM);
            return accountAssetsInfoResult;
        }).collect(Collectors.toList());
    }


    /**
     * 实时查询OTC资产
     * @param userId
     * @param accountTypeEnum
     * @return
     */
    private List<AccountAssetsInfoResult> listOtcAssets(Long userId,  AccountTypeEnum accountTypeEnum) throws ExecutionException, RetryException {
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        FundBalanceQueryRequest request = new FundBalanceQueryRequest();
        request.setUserId(userId);
        Retryer<ResponseResult<List<FundAccountBalanceResponse>>> retryHandle = RetryUtils.getRetryer(globalBillConfig.getSleepTime(), globalBillConfig.getReTryNumber(), TimeUnit.MILLISECONDS, assetsRetryerListener);
        ResponseResult<List<FundAccountBalanceResponse>> responseResult = retryHandle.call(() -> fundAccountQueryFacade.queryUserAssetAll(request));
        if(Objects.isNull(responseResult) || Objects.isNull(responseResult.getData())) {
            return Lists.newArrayList();
        }

        List<FundAccountBalanceResponse> fundAccountBalanceList = responseResult.getData();
        if(CollectionUtils.isEmpty(fundAccountBalanceList)){
            return Lists.newArrayList();
        }
        List<AccountAssetsInfoResult> resultList = new ArrayList<>(fundAccountBalanceList.size());
        for (FundAccountBalanceResponse fundAccountBalanceResponse : fundAccountBalanceList) {
            Integer baseCoinId = commonService.getCoinIdByName(fundAccountBalanceResponse.getCurrencyCode());
            AccountAssetsInfoResult accountAssetsInfoResult = new AccountAssetsInfoResult();
            accountAssetsInfoResult.setUserId(userId);
            accountAssetsInfoResult.setCoinId(baseCoinId);
            accountAssetsInfoResult.setProp1(fundAccountBalanceResponse.getAvailableBalance());
            accountAssetsInfoResult.setProp2(fundAccountBalanceResponse.getFrozenBalance());
            accountAssetsInfoResult.setAccountType(accountTypeEnum.getCode());
            accountAssetsInfoResult.setAccountParam(BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM);
            resultList.add(accountAssetsInfoResult);
        }
        return  resultList;
    }

    /**
     * 实时查询合约资产
     * @param userId
     * @param accountTypeEnum
     * @return
     */
    private List<AccountAssetsInfoResult> listMixAssets(Long userId,  AccountTypeEnum accountTypeEnum) throws ExecutionException, RetryException {
        AccountQueryParam param = new AccountQueryParam();
        param.setUserId(userId);
        param.setBusinessLine(BusinessLineMappingEnum.toBusinessLine((int) accountTypeEnum.getCode()));

        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        Retryer<AccountListDTO> retryHandle = RetryUtils.getRetryer(globalBillConfig.getSleepTime(), globalBillConfig.getReTryNumber(), TimeUnit.MILLISECONDS, assetsRetryerListener);

        AccountListDTO accountListDTO = retryHandle.call(() -> innerAccountFeignClient.queryAccountList(param));
        if(Objects.isNull(accountListDTO) || CollectionUtils.isEmpty(accountListDTO.getBalanceDTOs())){
            return Lists.newArrayList();
        }

        Map<String, Integer> allCoinId2Name = commonService.getAllCoinId2Name();
        return accountListDTO.getBalanceDTOs().stream().map(balanceDTO -> {
            Integer coinId = allCoinId2Name.getOrDefault(balanceDTO.getTokenId(), null);
            AccountAssetsInfoResult accountAssetsInfoResult = new AccountAssetsInfoResult();
            accountAssetsInfoResult.setUserId(balanceDTO.getAccountId());
            accountAssetsInfoResult.setCoinId(coinId);
            accountAssetsInfoResult.setProp1(balanceDTO.getEquity());
            accountAssetsInfoResult.setAccountType(accountTypeEnum.getCode());
            accountAssetsInfoResult.setAccountParam(BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM);
            return accountAssetsInfoResult;
        }).collect(Collectors.toList());
    }

    /**
     * 实时查询杠杆(逐仓 & 全仓)资产
     * @param userId
     * @param accountTypeEnum
     * @return
     */
    private List<AccountAssetsInfoResult> listLeverAssets(Long userId,  AccountTypeEnum accountTypeEnum) throws ExecutionException, RetryException {
        AssetsOverviewReq assetsOverviewReq = new AssetsOverviewReq();
        assetsOverviewReq.setAccountId(userId);

        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        Retryer<AssetsOverviewRes> retryHandle = RetryUtils.getRetryer(globalBillConfig.getSleepTime(), globalBillConfig.getReTryNumber(), TimeUnit.MILLISECONDS, assetsRetryerListener);

        AssetsOverviewRes assetsOverviewRes = retryHandle.call(() -> innerMarginOverviewFeignClient.getStatisticsExceptZero(assetsOverviewReq));
        if(Objects.isNull(assetsOverviewRes) || CollectionUtils.isEmpty(assetsOverviewRes.getIsolateSymbolAssets())){
            return Lists.newArrayList();
        }

        List<AccountAssetsInfoResult> accountAssetsInfoResultList = new ArrayList<>();
        // 杠杆(逐仓)
        if(CollectionUtils.isNotEmpty(assetsOverviewRes.getIsolateSymbolAssets())) {
            for (IsolateSymbolAssetsRes isolateSymbolAsset : assetsOverviewRes.getIsolateSymbolAssets()) {
                String baseTokenId = isolateSymbolAsset.getBaseTokenId();
                String quoteTokenId = isolateSymbolAsset.getQuoteTokenId();

                Integer baseCoinId = commonService.getCoinIdByName(baseTokenId);
                Integer quoteCoinId = commonService.getCoinIdByName(quoteTokenId);

                AccountAssetsInfoResult baseAccountAssetsInfoResult = new AccountAssetsInfoResult();
                baseAccountAssetsInfoResult.setUserId(userId);
                baseAccountAssetsInfoResult.setCoinId(baseCoinId);
                baseAccountAssetsInfoResult.setProp1(isolateSymbolAsset.getBaseFree());
                baseAccountAssetsInfoResult.setProp2(isolateSymbolAsset.getBaseLocked());
                baseAccountAssetsInfoResult.setProp3(isolateSymbolAsset.getBaseBorrowed());
                baseAccountAssetsInfoResult.setAccountType(accountTypeEnum.getCode());
                baseAccountAssetsInfoResult.setAccountParam(BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM);

                AccountAssetsInfoResult quoteAccountAssetsInfoResult = new AccountAssetsInfoResult();
                quoteAccountAssetsInfoResult.setUserId(userId);
                quoteAccountAssetsInfoResult.setCoinId(quoteCoinId);
                quoteAccountAssetsInfoResult.setProp1(isolateSymbolAsset.getQuoteFree());
                quoteAccountAssetsInfoResult.setProp2(isolateSymbolAsset.getQuoteLocked());
                quoteAccountAssetsInfoResult.setProp3(isolateSymbolAsset.getQuoteBorrowed());
                quoteAccountAssetsInfoResult.setAccountType(accountTypeEnum.getCode());
                quoteAccountAssetsInfoResult.setAccountParam(BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM);

                accountAssetsInfoResultList.add(baseAccountAssetsInfoResult);
                accountAssetsInfoResultList.add(quoteAccountAssetsInfoResult);
            }
        }

        // 杠杆(全仓)
        if (CollectionUtils.isNotEmpty(assetsOverviewRes.getCrossTokenAssets())) {
            for (CrossTokenAssetsRes crossTokenAsset : assetsOverviewRes.getCrossTokenAssets()) {
                Integer coinId = commonService.getCoinIdByName(crossTokenAsset.getTokenId());

                AccountAssetsInfoResult accountAssetsInfoResult = new AccountAssetsInfoResult();
                accountAssetsInfoResult.setUserId(userId);
                accountAssetsInfoResult.setCoinId(coinId);
                accountAssetsInfoResult.setProp1(crossTokenAsset.getFree());
                accountAssetsInfoResult.setProp2(crossTokenAsset.getLocked());
                accountAssetsInfoResult.setProp3(crossTokenAsset.getBorrowed());

                accountAssetsInfoResult.setAccountType(accountTypeEnum.getCode());
                accountAssetsInfoResult.setAccountParam(BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM);
                accountAssetsInfoResultList.add(accountAssetsInfoResult);
            }
        }
        return accountAssetsInfoResultList;
    }

    /**
     * 实时查询理财资产
     * @param userId
     * @param accountTypeEnum
     * @return
     * @throws ExecutionException
     * @throws RetryException
     */
    private List<AccountAssetsInfoResult> listFinancialAssets(Long userId,  AccountTypeEnum accountTypeEnum) throws ExecutionException, RetryException {
        FinancialVirtualAccountBalanceParam virtualAccountBalanceParam = new FinancialVirtualAccountBalanceParam();
        virtualAccountBalanceParam.setUserId(userId);


        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        Retryer<FinancialVirtualAccountBalanceRsp> retryHandle = RetryUtils.getRetryer(globalBillConfig.getSleepTime(), globalBillConfig.getReTryNumber(), TimeUnit.MILLISECONDS, assetsRetryerListener);

        FinancialVirtualAccountBalanceRsp virtualAccountBalance = retryHandle.call(() -> financialAccountInnerFeign.getVirtualAccountBalance(virtualAccountBalanceParam));
        if(Objects.isNull(virtualAccountBalance) || MapUtils.isEmpty(virtualAccountBalance.getCoinBalanceMap())){
            return Lists.newArrayList();
        }

        return virtualAccountBalance.getCoinBalanceMap().entrySet().stream().map(coinBalanceEntry -> {
            AccountAssetsInfoResult accountAssetsInfoResult = new AccountAssetsInfoResult();
            accountAssetsInfoResult.setUserId(userId);
            accountAssetsInfoResult.setCoinId(coinBalanceEntry.getKey());
            accountAssetsInfoResult.setProp1(coinBalanceEntry.getValue());
            accountAssetsInfoResult.setAccountType(accountTypeEnum.getCode());
            accountAssetsInfoResult.setAccountParam(BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM);
            return accountAssetsInfoResult;
        }).collect(Collectors.toList());
    }
}
