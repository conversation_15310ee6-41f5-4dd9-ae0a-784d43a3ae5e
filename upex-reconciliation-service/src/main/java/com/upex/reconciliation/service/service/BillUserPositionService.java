package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.business.createtablebyroute.BillUserPositionTableCreator;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillUserPosition;
import com.upex.reconciliation.service.dao.mapper.BillUserPositionMapper;
import com.upex.reconciliation.service.utils.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class BillUserPositionService {


    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "billUserPositionMapper")
    private BillUserPositionMapper billUserPositionMapper;
    @Resource
    private BillUserPositionTableCreator billUserPositionTableCreator;

    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date checkTime, Long pageSize) {
        String tableSuffix = billUserPositionTableCreator.getTableSuffixName(accountType.toString(), checkTime);
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionMapper.deleteByCheckTime(accountType, accountParam, checkTime, pageSize, tableSuffix));
    }

    public int batchInsert(List<BillUserPosition> records, Byte accountType, String accountParam) {
        int result = 0;
        if (CollectionUtils.isNotEmpty(records)) {
            Map<Date, List<BillUserPosition>> recordsMap = records.stream().collect(Collectors.groupingBy(BillUserPosition::getCheckOkTime));
            for (Map.Entry<Date, List<BillUserPosition>> dateListEntry : recordsMap.entrySet()) {
                String tableSuffix = billUserPositionTableCreator.getTableSuffixName(String.valueOf(accountType), dateListEntry.getKey());
                result += dbHelper.doDbOpInReconMaster(() -> billUserPositionMapper.batchInsert(records, accountType, accountParam, tableSuffix));
            }
        }
        return result;
    }

    public List<BillUserPosition> selectRangeCheckTimeRecordPageQuery(Integer accountType,
                                                                      String accountParam,
                                                                      Date checkTime,
                                                                      Long minId,
                                                                      Integer pageSize) {
        String tableSuffix = billUserPositionTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionMapper.selectRangeCheckTimeRecordPageQuery(accountType, accountParam, checkTime, minId, pageSize, tableSuffix));
    }


    public List<BillUserPosition> selectByCheckTime(Integer accountType,
                                                    String accountParam,
                                                    Date checkTime,
                                                    Long userId) {
        String tableSuffix = billUserPositionTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionMapper.selectByCheckTime(accountType, accountParam, checkTime, userId, tableSuffix));
    }

    public List<BillUserPosition> selectByCheckTimeAndCoinId(Integer accountType,
                                                             String accountParam,
                                                             Date checkTime,
                                                             Long userId,
                                                             Integer coinId) {
        String tableSuffix = billUserPositionTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionMapper.selectByCheckTimeAndCoinId(accountType, accountParam, checkTime, userId, coinId, tableSuffix));
    }


    /**
     * 创建表 保持不变
     *
     * @param accountType
     * @param accountParam
     * @param tableSuffix
     * @return
     */
    private Integer createTable(Byte accountType,
                                String accountParam,
                                String tableSuffix) {
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionMapper.createTable(accountType, accountParam, tableSuffix));
    }

    /**
     * 创建表
     *
     * @param accountType
     * @param accountParam
     * @param day
     */
    public void createTableForDay(Byte accountType,
                                  String accountParam,
                                  Integer day) {
        if (day != null && day > 0) {
            Date nowDate = new Date();
            for (int i = 0; i < day; i++) {
                String tableSuffix = billUserPositionTableCreator.getTableSuffixName(String.valueOf(accountType), DateUtil.addDay(nowDate, i));
                createTable(accountType, accountParam, tableSuffix);
            }
        }
    }

    /**
     * 获取分表
     * 删除逻辑表名后缀先不改
     *
     * @param accountType
     * @param accountParam
     * @return
     */
    public List<String> getTables(Byte accountType, String accountParam, String tablePrefix) {
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionMapper.getTables(accountType, accountParam, tablePrefix));
    }

    /**
     * 删除逻辑表名后缀先不改
     *
     * @param tableName
     * @param pageSize
     * @return
     */
    public Boolean deleteByTableName(String tableName, Long pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionMapper.deleteByTableName(tableName, pageSize));
    }

    /**
     * 删除全部数据
     *
     * @param accountType
     * @param accountParam
     * @param tablePrefix
     * @param pageSize
     * @return
     */
    public Boolean deleteAll(Byte accountType, String accountParam, String tablePrefix, Long pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionMapper.deleteAll(accountType, accountParam, tablePrefix, pageSize));
    }

    /**
     * 获取表列名
     *
     * @param tableSchema
     * @param tableName
     * @return
     */
    public List<String> getTableColumns(String tableSchema, String tableName) {
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionMapper.getTableColumns(tableSchema, tableName));
    }

    public List<BillUserPosition> selectByUserIdAndGtCheckTime(Byte accountType, String accountParam, Date checkTime, Long startId, Integer pageSize, Long userId) {
        String tableSuffix = billUserPositionTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionMapper.selectByUserIdAndGtCheckTime(accountType, accountParam, checkTime, startId, pageSize, userId, tableSuffix));
    }

    /**
     * 删除数据表名后缀逻辑先不改
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    public Boolean tableExists(Byte accountType, String accountParam, Date checkTime) {
        String tableSuffix = billUserPositionTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        String tablePrefix = String.format("bill_user_position_%s_%s_%s", accountType, accountParam, tableSuffix);
        List<String> tables = dbHelper.doDbOpInReconMaster(() -> billUserPositionMapper.getTables(accountType, accountParam, tablePrefix));
        return tables.size() > 0;
    }

    /**
     * 更新用户持仓的lastReLAvg和lastReSAvg
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @param userId
     * @param lastReLAvg
     * @param lastReSAvg
     * @return
     */
    public Boolean updateUserPositionReLAvgAndReSAvg(Byte accountType, String accountParam, Date checkTime, Long userId, BigDecimal lastReLAvg, BigDecimal lastReSAvg) {
        String tableSuffix = billUserPositionTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionMapper.updateUserPositionReLAvgAndReSAvg(accountType, accountParam, checkTime, userId, tableSuffix, lastReLAvg, lastReSAvg));
    }
}
