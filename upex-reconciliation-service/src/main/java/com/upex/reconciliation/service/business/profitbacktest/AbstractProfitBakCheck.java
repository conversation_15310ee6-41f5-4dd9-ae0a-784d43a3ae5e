package com.upex.reconciliation.service.business.profitbacktest;


import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.business.ReconUserAssetsSnapShotService;
import com.upex.reconciliation.service.business.UserQueryService;
import com.upex.reconciliation.service.business.impl.ReconCheckBillResultServiceImpl;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.dto.ProfitBakCheckReq;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
public abstract class AbstractProfitBakCheck implements IProfitBakCheck {

    @Resource
    ReconCheckBillResultServiceImpl reconCheckBillResultService;

    public static final String PROFIT_BAKCHECK_FROM_XXLJOB = "xxl-job-profit-bakcheck";

    @Override
    public void checkProfit() {
        ProfitBakCheckReq profitBakCheckReq = getProfitCheckReq();
        if (profitBakCheckReq == null) {
            return;
        }
        checkProfit(profitBakCheckReq);
    }

    private void checkProfit(ProfitBakCheckReq profitBakCheckReq) {
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        if (globalBillConfig == null) {
            return;
        }
        List<Integer> timePeriodProfitList = globalBillConfig.getTimePeriodProfitList();
        if (CollectionUtils.isEmpty(timePeriodProfitList)) {
            return;
        }
        if (CollectionUtils.isEmpty(profitBakCheckReq.getUserIds())) {
            return;
        }
        for (Long userId : profitBakCheckReq.getUserIds()) {
            if(userId==null){
                continue;
            }
            for (Integer timePeriod : timePeriodProfitList) {
                ReconCheckResultsParams checkResultsParams = new ReconCheckResultsParams();
                checkResultsParams.setRequestDate(Objects.requireNonNullElse(profitBakCheckReq.getReqStartTime(), new Date()).getTime());
                checkResultsParams.setBusinessSource(PROFIT_BAKCHECK_FROM_XXLJOB);
                checkResultsParams.setUserId(userId);
                reconCheckBillResultService.checkProfitAccountByGray(checkResultsParams, globalBillConfig, timePeriod);
            }
        }
    }
}
