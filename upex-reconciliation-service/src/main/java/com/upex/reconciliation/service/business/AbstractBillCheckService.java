package com.upex.reconciliation.service.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.cache.Cache;
import com.google.common.collect.Maps;
import com.upex.commons.support.util.SiteUtil;
import com.upex.mixcontract.process.facade.dto.MixAccountAssetsExtension;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.business.module.impl.BillUserCheckModule;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.LogLevelEnum;
import com.upex.reconciliation.service.common.constants.enums.SymbolCoinPropEnum;
import com.upex.reconciliation.service.common.constants.enums.SymbolPropEnum;
import com.upex.reconciliation.service.common.constants.negativeCheck.NegativeCheckCacheMigrateEnum;
import com.upex.reconciliation.service.common.constants.negativeCheck.NegativeCheckResultEnum;
import com.upex.reconciliation.service.common.constants.negativeCheck.NegativeCheckTypeEnum;
import com.upex.reconciliation.service.common.thread.BaseAsyncTaskManager;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.alarm.CheckResult;
import com.upex.reconciliation.service.model.alarm.UserAssetNegativeErrorModel;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.*;
import com.upex.reconciliation.service.service.ReconSystemAccountService;
import com.upex.reconciliation.service.utils.*;
import com.upex.user.dto.UserLoginListDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.*;
import static com.upex.reconciliation.service.utils.DateUtil.FMT_yyyy_MM_dd_HH_mm_ss;

/**
 * 对账抽象类
 */
@Slf4j
public abstract class AbstractBillCheckService implements BillCheckService {
    @Resource
    private AlarmNotifyService alarmNotifyService;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private BillEngineManager billEngineManager;
    /***入出对账计数器****/
    private Map<Byte, AtomicInteger> inOutCheckResultCounterMap = new ConcurrentHashMap<>();
    /**
     * 系统用户
     */
    private Cache<String, Set<Long>> systemUserIdCache = CacheUtils.getNewCache(100, 5, TimeUnit.MINUTES);

    @Resource
    private ReconSystemAccountService reconSystemAccountService;
    @Resource
    private InitDataService initDataService;
    @Resource(name = "negativeAutoRepairTaskManager")
    private BaseAsyncTaskManager negativeAutoRepairTaskManager;

    @Override
    public boolean checkUserFlow(ApolloReconciliationBizConfig apolloBizConfig, Byte accountType, String accountUniqueId, Long userId, Integer coinId, List<CommonBillChangeData> billChangeDataList, BillCoinUserProperty billCoinUserProperty, List<CommonBillChangeData> errorCommonBillChangeDataList) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        CheckResult result = doCheckProperty(apolloBizConfig, accountUniqueId, userId, coinId, billChangeDataList, billCoinUserProperty);
        if (result.isFail()) {
            log.error("AbstractBillCheckService.checkUserFlow doCheckProperty result {} {} {} ", accountType, result, accountUniqueId);
            billChangeDataList.addAll(errorCommonBillChangeDataList);
            if (billChangeDataList.size() >= 1) {
                BizLogUtils.log(LogLevelEnum.DEBUG, apolloBizConfig, "BllUserCheckModule checkUserFlow error accountType {} propertyData:{} billData:{}", accountType, JSON.toJSONString(billCoinUserProperty), JSON.toJSONString(billChangeDataList.get(0)));
                alarmNotifyService.alarm(accountTypeEnum.getCode(), CHECK_USER_PROPERTY_TEMPLATE, Map.of("checkResult", result));
            }
            result = doBatchCheckProperty(apolloBizConfig, accountUniqueId, userId, coinId, billChangeDataList, billCoinUserProperty);
            if (result.isSuccess()) {
                repairBillFlowByCoinUserProperty(billCoinUserProperty, billChangeDataList);
                BizLogUtils.log(LogLevelEnum.DEBUG, apolloBizConfig, "AbstractBillCheckService.doBatchCheckProperty success accountType:{} result:{} data:{}",
                        accountType, result, JSONObject.toJSONString(billChangeDataList.stream().limit(10).collect(Collectors.toList())));
            }
        }
        if (result.isSuccess()) {
            // 模拟盘校验
            result = doCheckPapTradingFlow(apolloBizConfig, accountTypeEnum, billChangeDataList);
        }
        return result.isSuccess();
    }

    /**
     * 模拟盘校验
     *
     * @param apolloBizConfig
     * @param accountTypeEnum
     * @param billChangeDataList
     * @return
     */
    private CheckResult doCheckPapTradingFlow(ApolloReconciliationBizConfig apolloBizConfig, AccountTypeEnum accountTypeEnum, List<CommonBillChangeData> billChangeDataList) {
        CheckResult result = CheckResult.DEFAULT_SUCCESS;
        if (apolloBizConfig.isPapTradingEnvironment()) {
            if (apolloBizConfig.isCheckPapTradingFlowOpen()) {
                for (CommonBillChangeData changeData : billChangeDataList) {
                    if (!SiteUtil.checkPaptradingByUserId(changeData.getAccountId())) {
                        result = CheckResult.DEFAULT_FAIL;
                        alarmNotifyService.alarm(apolloBizConfig.getAccountType(), CHECK_PAPTRADING_ENV_BY_USER_ID_ERROR, apolloBizConfig.getAccountType(), changeData.getBizType(), changeData.getAccountId(), changeData.getBizId());
                    }
                }
            }
        } else {
            for (CommonBillChangeData changeData : billChangeDataList) {
                if (SiteUtil.checkPaptradingByUserId(changeData.getAccountId())) {
                    result = CheckResult.DEFAULT_FAIL;
                    alarmNotifyService.alarm(apolloBizConfig.getAccountType(), CHECK_NOT_PAPTRADING_ENV_BY_USER_ID_ERROR, apolloBizConfig.getAccountType(), changeData.getBizType(), changeData.getAccountId(), changeData.getBizId());
                }
            }
        }
        return result;
    }

    @Override
    public boolean timeSliceBillCheck(BillTimeSliceDTO endSliceDTO, Date checkOkTime, ApolloReconciliationBizConfig apolloBizConfig, CommonService commonService) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        // 入出 中断
        if (apolloBizConfig.isInOutCheckOpen()) {
            boolean result = doProcessBillCheckForTimeSlice(accountTypeEnum, endSliceDTO, apolloBizConfig, checkOkTime);
            log.info("BillTimeSliceCheckModule.timeSliceBillCheck processBillCheckForTimeSlice in and out result accountType:{} {} time is {}",
                    apolloBizConfig.getAccountType(), result, DateUtil.getDefaultDateStr(checkOkTime));
            if (!result) {
                return result;
            }
        }
        // 数据验证 不中断
        if (apolloBizConfig.isCoinTypeUserCheckOpen()) {
            boolean result = doProcessCoinTypeUserCheckForTimeSlice(accountTypeEnum, endSliceDTO, apolloBizConfig, checkOkTime);
            if (!result) {
                BizLogUtils.log(LogLevelEnum.KEY_INPUT, apolloBizConfig, "BillTimeSliceCheckModule.timeSliceBillCheck doProcessCoinTypeUserCheckForTimeSlice result accountType:{} {} time is {}",
                        apolloBizConfig.getAccountType(), result, DateUtil.getDefaultDateStr(checkOkTime));
            }
        }
        // 负值检测 不中断
        if (apolloBizConfig.isTimeSliceNegativeOpen()) {
            checkNegative(accountTypeEnum, endSliceDTO, apolloBizConfig, checkOkTime, commonService);
        }
        return doTimeSliceBillCheck(endSliceDTO, checkOkTime, apolloBizConfig);
    }

    /**
     * 负值检测
     *
     * @param accountTypeEnum
     * @param endSliceDTO
     * @param apolloBizConfig
     * @param checkOkTime
     * @return
     */
    private boolean checkNegative(AccountTypeEnum accountTypeEnum, BillTimeSliceDTO endSliceDTO, ApolloReconciliationBizConfig apolloBizConfig, Date checkOkTime, CommonService commonService) {
        //  获取redis负值缓存
        Map<String, UserAssetNegativeErrorModel> userCoinPropNegativeCache = new HashMap<>();
        String userCoinNegative = (String) redisTemplate.opsForValue().get(RedisUtil.getUserCoinNegativeKeyV2(accountTypeEnum.getCode()));
        if (StringUtils.isNotEmpty(userCoinNegative) && !"[]".equals(userCoinNegative) && !"{}".equals(userCoinNegative)) {
            userCoinPropNegativeCache = JSON.parseObject(userCoinNegative, new TypeReference<>() {
            });
        }

        // 计算合约 用户资产未实现
        Map<Long, Map<Integer, BigDecimal>> userCoinUnRealizedMap = calculateUserUnRealized(accountTypeEnum, endSliceDTO, apolloBizConfig, checkOkTime);

        // 本次时间片负值数据
        Map<String, UserAssetNegativeErrorModel> timeSliceUserCoinPropNegative = new HashMap<>();

        // 遍历用户，计算资产负值
        String checkOkTimeStr = DateUtil.date2str(checkOkTime);
        Map<Long, Map<Integer, BillCoinUserProperty>> coinUserPropertyMap = endSliceDTO.getCoinUserPropertyMap();
        for (Map.Entry<Long, Map<Integer, BillCoinUserProperty>> userEntry : coinUserPropertyMap.entrySet()) {
            // 统计用户资产
            Map<Integer, BigDecimal> unRealizedMap = userCoinUnRealizedMap.getOrDefault(userEntry.getKey(), new HashMap<>());
            UserAssetsDTO userAssetsDTO = getUserAssetsDTO(unRealizedMap, apolloBizConfig, checkOkTime, userEntry);

            // 组装负值数据
            assemblyNegative(accountTypeEnum, apolloBizConfig, checkOkTimeStr, userEntry, userAssetsDTO, timeSliceUserCoinPropNegative, userCoinPropNegativeCache);
        }

        // 旧redisKey负值（即job重跑的负值）迁移
        mergeOldCacheToNew(timeSliceUserCoinPropNegative, userCoinPropNegativeCache, accountTypeEnum);
        // 历史负值处理： 超时历史负值剔除并异步重跑
        autoRepairExpiredNegative(accountTypeEnum, apolloBizConfig, checkOkTime, userCoinPropNegativeCache);

        // 负值告警
        if (apolloBizConfig.isTimeSliceNegativeAlarmOpen()) {
            // 是否告警判断：`当前时间（currentTime）` - `时间片时间（checkOkTime）` < 阈值，才告警（防止因为对账追平期间持续告警）
            if (System.currentTimeMillis() - checkOkTime.getTime() < apolloBizConfig.getCacheNegativeAlarmTimeThreshold()) {
                // 1. 时间片负值告警
                doNegativeAlarm(accountTypeEnum, apolloBizConfig, checkOkTime, commonService, timeSliceUserCoinPropNegative, true);

                // 2. 历史负值告警
                doNegativeAlarm(accountTypeEnum, apolloBizConfig, checkOkTime, commonService, userCoinPropNegativeCache, false);
            }
        }

        // 时间片负值和历史负值合并后写入缓存
        Map<String, UserAssetNegativeErrorModel> finalCacheMap = new HashMap<>();
        finalCacheMap.putAll(timeSliceUserCoinPropNegative);
        finalCacheMap.putAll(userCoinPropNegativeCache);
        // 更新新key负值缓存
        redisTemplate.opsForValue().set(RedisUtil.getUserCoinNegativeKeyV2(accountTypeEnum.getCode()), JSON.toJSONString(finalCacheMap));

        return true;
    }

    /**
     * 自动重跑超时负值缓存（防止由于异常导致的部分负值一直不更新）
     *
     * @param accountTypeEnum
     * @param apolloBizConfig
     * @param checkOkTime
     * @param userCoinPropNegativeCache
     */
    private void autoRepairExpiredNegative(AccountTypeEnum accountTypeEnum, ApolloReconciliationBizConfig apolloBizConfig, Date checkOkTime, Map<String, UserAssetNegativeErrorModel> userCoinPropNegativeCache) {
        // 获取上次重跑时间
        String lastAutoRepairTimeStr = (String) redisTemplate.opsForValue().get(RedisUtil.getUserCoinNegativeAutoRepairKey(accountTypeEnum.getCode()));
        long lastAutoRepairTime;
        if (lastAutoRepairTimeStr == null) {
            // 为空，说明是首次进入，设为当前时间片时间，直接返回
            redisTemplate.opsForValue().set(RedisUtil.getUserCoinNegativeAutoRepairKey(accountTypeEnum.getCode()), String.valueOf(checkOkTime.getTime()));
            return;
        }

        lastAutoRepairTime = Long.parseLong(lastAutoRepairTimeStr);

        // 定时检测（默认一个小时）
        if (checkOkTime.getTime() - lastAutoRepairTime > apolloBizConfig.getCacheNegativeAutoRepairInterval()) {
            // 检测超时的key（`时间片时间`-`负值检测时间` > 阈值（默认等于定时检测时间））
            Set<String> expiredCacheKey = userCoinPropNegativeCache.entrySet().stream().filter(e -> checkOkTime.getTime() - DateUtil.getDefaultDateLong(e.getValue().getCheckTimeStr()) > apolloBizConfig.getCacheNegativeAutoRepairInterval()).map(Map.Entry::getKey).collect(Collectors.toSet());
            // 从当前缓存中删除，并异步调用负值初始化job重新初始化该负值
            Map<String, UserAssetNegativeErrorModel> toRepairMap = new ConcurrentHashMap<>();
            for (String key : expiredCacheKey) {
                // 剔除
                UserAssetNegativeErrorModel remove = userCoinPropNegativeCache.remove(key);
                // 待重跑数据
                toRepairMap.put(key, remove);
            }
            // 异步重跑负值
            if (!toRepairMap.isEmpty()) {
                negativeAutoRepairTaskManager.submit(() -> initDataService.asyncRepairRedisKeyOfUsers(accountTypeEnum, toRepairMap));
            }
            // 检测完成，更新检测时间
            redisTemplate.opsForValue().set(RedisUtil.getUserCoinNegativeAutoRepairKey(accountTypeEnum.getCode()), String.valueOf(checkOkTime.getTime()));
        }

    }

    private static void assemblyNegative(AccountTypeEnum accountTypeEnum, ApolloReconciliationBizConfig apolloBizConfig, String checkOkTimeStr, Map.Entry<Long, Map<Integer, BillCoinUserProperty>> userEntry, UserAssetsDTO userAssetsDTO, Map<String, UserAssetNegativeErrorModel> timeSliceUserCoinPropNegative, Map<String, UserAssetNegativeErrorModel> userCoinPropNegativeCache) {
        // 用户维度负值资产检测
        if (apolloBizConfig.isTimeSliceUserAssetsNegativeOpen()) {
            String userCoinKey = userEntry.getKey() + BillConstants.POUND_SIGN + NegativeCheckTypeEnum.TOTAL_ASSETS.getCode();
            String userCoinPropNegativeKey = accountTypeEnum.getCode() + BillConstants.POUND_SIGN + userCoinKey;
            if (userAssetsDTO.getTotalUsdtValue().compareTo(BigDecimal.ZERO) < 0) {
                UserAssetNegativeErrorModel userAssetNegativeErrorModel = userCoinPropNegativeCache.getOrDefault(userCoinPropNegativeKey, new UserAssetNegativeErrorModel());
                userAssetNegativeErrorModel.setPropValue(userAssetsDTO.getAssetsUsdtValue());
                userAssetNegativeErrorModel.setUnRealized(userAssetsDTO.getUnRealizedUsdtValue());
                userAssetNegativeErrorModel.setTotalValue(userAssetsDTO.getTotalUsdtValue());
                fillParamToModel(userAssetNegativeErrorModel, accountTypeEnum, userEntry.getKey(), NegativeCheckTypeEnum.TOTAL_ASSETS.getCode(), userAssetsDTO, checkOkTimeStr, false);

                timeSliceUserCoinPropNegative.put(userCoinPropNegativeKey, userAssetNegativeErrorModel);
                BizLogUtils.info(apolloBizConfig.getNegativeCheckPrintDetailLog(), "AbstractBillCheckService.checkNegative error user accountType:{} userId:{} checkTimeStr:{} userAssetsDTO:{}", accountTypeEnum.getCode(), userEntry.getKey(), checkOkTimeStr, JSON.toJSONString(userAssetsDTO));
            }
            // 时间片负值单独处理，从缓存中移除
            userCoinPropNegativeCache.remove(userCoinPropNegativeKey);

        } else {
            // 用户币种维度负值资产检测
            for (Map.Entry<Integer, BillCoinUserProperty> coinEntry : userEntry.getValue().entrySet()) {
                BillCoinUserProperty coinUserProperty = coinEntry.getValue();
                Tuple3<BigDecimal, BigDecimal, BigDecimal> coinUsdtTotalTuple3 = userAssetsDTO.getTotalDetailMap().get(coinEntry.getKey());
                String userCoinKey = coinUserProperty.groupByUserCoin();
                String userCoinPropNegativeKey = accountTypeEnum.getCode() + BillConstants.POUND_SIGN + userCoinKey;
                if (coinUsdtTotalTuple3 != null && coinUsdtTotalTuple3.getT3().compareTo(BigDecimal.ZERO) < 0) {
                    Tuple3<BigDecimal, BigDecimal, BigDecimal> coinUsdtAssetsTuple3 = userAssetsDTO.getAssetsDetailMap().getOrDefault(coinEntry.getKey(), Tuples.of(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO));
                    Tuple3<BigDecimal, BigDecimal, BigDecimal> UnRealizedDetailTuple3 = userAssetsDTO.getUnRealizedDetailMap().getOrDefault(coinEntry.getKey(), Tuples.of(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO));

                    UserAssetNegativeErrorModel userAssetNegativeErrorModel = userCoinPropNegativeCache.getOrDefault(userCoinPropNegativeKey, new UserAssetNegativeErrorModel());
                    userAssetNegativeErrorModel.setPropValue(coinUsdtAssetsTuple3.getT3());
                    userAssetNegativeErrorModel.setUnRealized(UnRealizedDetailTuple3.getT3());
                    userAssetNegativeErrorModel.setTotalValue(coinUsdtTotalTuple3.getT3());
                    fillParamToModel(userAssetNegativeErrorModel, accountTypeEnum, coinUserProperty.getUserId(), coinUserProperty.getCoinId(), userAssetsDTO, checkOkTimeStr, true);

                    timeSliceUserCoinPropNegative.put(userCoinPropNegativeKey, userAssetNegativeErrorModel);
                    BizLogUtils.info(apolloBizConfig.getNegativeCheckPrintDetailLog(), "AbstractBillCheckService.checkNegative error coin accountType:{} userId:{} checkTimeStr:{} userAssetsDTO:{}", accountTypeEnum.getCode(), userEntry.getKey(), checkOkTimeStr, JSON.toJSONString(userAssetsDTO));
                }
                // 时间片负值单独处理，从缓存中移除
                userCoinPropNegativeCache.remove(userCoinPropNegativeKey);
            }
        }
    }

    private void doNegativeAlarm(AccountTypeEnum accountTypeEnum, ApolloReconciliationBizConfig apolloBizConfig, Date checkOkTime, CommonService commonService, Map<String, UserAssetNegativeErrorModel> timeSliceUserCoinPropNegative, boolean isCurrentTimeSliceNegative) {
        // 1 判断时间合法且满足延迟要求
        Map<String, UserAssetNegativeErrorModel> meetDelayRequirementMap = timeSliceUserCoinPropNegative.entrySet().stream().filter(entry -> delayCheck(entry.getValue(), apolloBizConfig.getCheckNegativeDelayTime())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (!meetDelayRequirementMap.isEmpty()) {
            log.info("BillTimeSliceCheckModule.checkNegative accountType:{} checkOkTime:{}, meetDelaySize:{}", accountTypeEnum.getCode(), DateUtil.date2str(checkOkTime), meetDelayRequirementMap.size());
            // 2 进行白名单校验
            Map<String, Tuple2<UserAssetNegativeErrorModel, String>> alarmMap = alarmWhitelistFiltering(meetDelayRequirementMap, apolloBizConfig);
            // 如果是缓存中历史负值，进行告警频率控制
            if (!isCurrentTimeSliceNegative) {
                alarmMap = alarmMap.entrySet().stream().filter(e -> frequentFilter(e.getValue().getT1(), checkOkTime, apolloBizConfig.getCacheNegativeAlarmTimeInterval())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            }
            // 系统用户查询
            Map<Long, UserLoginListDTO> allSysUserMapCache = commonService.getAllSysUserMapCache(DateUtil.getZeroOClick(new Date()).getTime());
            // 对于经过白名单过滤后剩下的负值（alarmMap），进行告警
            for (Map.Entry<String, Tuple2<UserAssetNegativeErrorModel, String>> entry : alarmMap.entrySet()) {
                Map<String, Object> alarmList = assemblyMap(accountTypeEnum, commonService, entry.getValue(), allSysUserMapCache);
                alarmNotifyService.alarm(accountTypeEnum.getCode(), CHECK_NEGATIVE_ERROR, alarmList);
            }
            // 更新最后告警时间为时间片时间
            updateAlarmTime(timeSliceUserCoinPropNegative, alarmMap, checkOkTime);
            log.info("BillTimeSliceCheckModule.checkNegative accountType:{} checkOkTime:{}, alarmSize:{}", accountTypeEnum.getCode(), DateUtil.date2str(checkOkTime), alarmMap.size());
        }
    }


    /**
     * 更新告警时间为本次时间片时间
     *
     * @param userCoinPropNegative
     * @param alarmMap
     * @param checkOkTime
     */
    private void updateAlarmTime(Map<String, UserAssetNegativeErrorModel> userCoinPropNegative, Map<String, Tuple2<UserAssetNegativeErrorModel, String>> alarmMap, Date checkOkTime) {
        long time = checkOkTime.getTime();
        for (Map.Entry<String, Tuple2<UserAssetNegativeErrorModel, String>> entry : alarmMap.entrySet()) {
            userCoinPropNegative.get(entry.getKey()).setLatestAlarmTime(time);
        }
    }

    private boolean frequentFilter(UserAssetNegativeErrorModel model, Date checkOkTime, Long negativeAlarmTimeInterval) {
        // 判断`时间片时间`与`上次告警时间`差值，若大于间隔，则进行告警
        long latestAlarmTime = model.getLatestAlarmTime() == null ? checkOkTime.getTime() : model.getLatestAlarmTime();
        return (checkOkTime.getTime() - latestAlarmTime) > negativeAlarmTimeInterval;
    }

    private void mergeOldCacheToNew(Map<String, UserAssetNegativeErrorModel> timeSliceUserCoinPropNegative, Map<String, UserAssetNegativeErrorModel> hisUserCoinPropNegative, AccountTypeEnum accountTypeEnum) {
        //  获取旧redisKey负值缓存（此数据来源于负值重跑job）
        Map<String, UserAssetNegativeErrorModel> oldUserCoinPropNegativeCache = new HashMap<>();
        String oldUserCoinNegative = (String) redisTemplate.opsForValue().get(RedisUtil.getUserCoinNegativeKey(accountTypeEnum.getCode()));
        if (StringUtils.isNotEmpty(oldUserCoinNegative) && !"[]".equals(oldUserCoinNegative) && !"{}".equals(oldUserCoinNegative)) {
            oldUserCoinPropNegativeCache = JSON.parseObject(oldUserCoinNegative, new TypeReference<>() {
            });
        }
        // 1. 旧缓存无值，2. 旧缓存重新初始化未完成，3.旧缓存已迁移完成， 则跳过
        if (oldUserCoinPropNegativeCache.isEmpty() || oldUserCoinPropNegativeCache.get(NegativeCheckCacheMigrateEnum.complete_repair.name()) == null || oldUserCoinPropNegativeCache.get(NegativeCheckCacheMigrateEnum.complete_merge.name()) != null) {
            return;
        }
        log.info("BillTimeSliceCheckModule.mergeOldCacheToNew start timeSliceUserCoinPropNegative:{}", timeSliceUserCoinPropNegative.size());
        // 至此说明旧缓存重新初始化已完成，开始迁移旧缓存到新缓存
        for (Map.Entry<String, UserAssetNegativeErrorModel> entry : oldUserCoinPropNegativeCache.entrySet()) {
            // 如果时间片缓存里已经有相同userCoinKey的负值，则以时间片缓存为准，旧的舍弃
            if (timeSliceUserCoinPropNegative.containsKey(entry.getKey())) {
                continue;
            }
            // 迁移标志过滤
            if (NegativeCheckCacheMigrateEnum.containName(entry.getKey())) {
                continue;
            }
            // userCoinKey不在时间片缓存里的，加入进去
            timeSliceUserCoinPropNegative.put(entry.getKey(), entry.getValue());
            // 并从历史缓存中移除
            hisUserCoinPropNegative.remove(entry.getKey());
        }
        // 迁移完成，清空旧key缓存，塞入迁移完成标志
        oldUserCoinPropNegativeCache.clear();
        oldUserCoinPropNegativeCache.put(NegativeCheckCacheMigrateEnum.complete_merge.name(), new UserAssetNegativeErrorModel());
        // 更新旧key负值缓存
        redisTemplate.opsForValue().set(RedisUtil.getUserCoinNegativeKey(accountTypeEnum.getCode()), JSON.toJSONString(oldUserCoinPropNegativeCache));
        log.info("BillTimeSliceCheckModule.mergeOldCacheToNew end timeSliceUserCoinPropNegative:{}", timeSliceUserCoinPropNegative.size());
    }

    private static Map<String, Object> assemblyMap(AccountTypeEnum accountTypeEnum, CommonService commonService, Tuple2<UserAssetNegativeErrorModel, String> errorModelAlarmTypeTuple, Map<Long, UserLoginListDTO> allSysUserMapCache) {
        UserAssetNegativeErrorModel negativeErrorModel = errorModelAlarmTypeTuple.getT1();
        boolean isSysUser = allSysUserMapCache.containsKey(negativeErrorModel.getUserId());
        String sysUserDisplayStr = "";
        if (isSysUser) {
            UserLoginListDTO userLoginListDTO = commonService.getSysUserInfo(negativeErrorModel.getUserId());
            sysUserDisplayStr = String.format("%s(%s)", userLoginListDTO.getUserType(), userLoginListDTO.getRemark());
        }
        Map<String, Object> alarmList = new HashMap<>();
        alarmList.put("accountType", accountTypeEnum.getCode());
        alarmList.put("isSysUser", isSysUser);
        alarmList.put("sysUserDisplayStr", sysUserDisplayStr);
        alarmList.put("alarmType", errorModelAlarmTypeTuple.getT2());
        alarmList.put("isCoin", NegativeCheckTypeEnum.TOTAL_ASSETS.getCode() != negativeErrorModel.getCoinId());
        alarmList.put("model", negativeErrorModel);
        return alarmList;
    }

    private boolean delayCheck(UserAssetNegativeErrorModel negativeErrorModel, long delayTime) {
        return StringUtils.isNotEmpty(negativeErrorModel.getCheckTimeStr()) && StringUtils.isNotEmpty(negativeErrorModel.getErrorTimeStr())
                && DateUtil.str2date(negativeErrorModel.getCheckTimeStr(), FMT_yyyy_MM_dd_HH_mm_ss).getTime() - DateUtil.str2date(negativeErrorModel.getErrorTimeStr(), FMT_yyyy_MM_dd_HH_mm_ss).getTime() > delayTime;
    }

    private Map<Long, Map<Integer, BigDecimal>> calculateUserUnRealized(AccountTypeEnum accountTypeEnum, BillTimeSliceDTO endSliceDTO, ApolloReconciliationBizConfig apolloBizConfig, Date checkOkTime) {
        Map<Long, Map<Integer, BigDecimal>> userCoinUnRealizedMap = new HashMap<>();
        if (accountTypeEnum.checkNegativeCalculatePositionUnRealized()) {
            MixAccountAssetsExtension mixAccountAssetsExtension = accountAssetsServiceFactory.queryPriceByTime((int) apolloBizConfig.getAccountType(), checkOkTime.getTime());
            Map<String, BigDecimal> mPriceMap = mixAccountAssetsExtension.getMPriceMap();
            Map<Integer, BigDecimal> sPriceMap = mixAccountAssetsExtension.getSPriceMap();
            log.info("mixAccountAssetsExtension.getMPriceMap accountType:{} checkOkTime:{} mPriceMap:{} sPriceMap:{}", accountTypeEnum.getCode(), DateUtil.date2str(checkOkTime), JSON.toJSONString(mPriceMap), JSON.toJSONString(sPriceMap));
            Map<String, List<BillUserPosition>> billUserPositionMap = endSliceDTO.getBillUserPositionList().stream().collect(Collectors.groupingBy(BillUserPosition::groupByUserCoinKey));
            for (Map.Entry<String, List<BillUserPosition>> entry : billUserPositionMap.entrySet()) {
                // 折算未实现
                PositionStatisticsDTO positionStatisticsDTO = BillUserPosition.calculatePositionStatistics(entry.getValue(), mPriceMap, sPriceMap, apolloBizConfig, reconSystemAccountService);
                Long userId = BillUserPosition.getUserIdFromKey(entry.getKey());
                Integer coinId = BillUserPosition.getCoinIdFromKey(entry.getKey());
                userCoinUnRealizedMap.computeIfAbsent(userId, key -> new HashMap<>()).put(coinId, positionStatisticsDTO.getTotalMarginUnRealized());
            }
        }
        return userCoinUnRealizedMap;
    }

    private UserAssetsDTO getUserAssetsDTO(Map<Integer, BigDecimal> unRealizedMap, ApolloReconciliationBizConfig apolloBizConfig, Date checkOkTime, Map.Entry<Long, Map<Integer, BillCoinUserProperty>> userEntry) {
        BillUserCheckModule userCheckModule = (BillUserCheckModule) billEngineManager.getBillLogicGroup(apolloBizConfig.getAccountType()).getUserCheckModule();
        return userCheckModule.getMemUserAssetsToUsdt(userEntry.getKey(), checkOkTime, unRealizedMap, apolloBizConfig);
    }

    /**
     * 填充参数到 UserAssetNegativeErrorModel
     *
     * @param errorModel
     * @param accountTypeEnum
     * @param userId
     * @param coinId
     * @param userAssetsDTO
     * @param checkOkTimeStr
     */
    private static void fillParamToModel(UserAssetNegativeErrorModel errorModel, AccountTypeEnum accountTypeEnum, long userId, int coinId, UserAssetsDTO userAssetsDTO, String checkOkTimeStr, boolean isCoin) {
        errorModel.setAccountType(accountTypeEnum.getCode());

        errorModel.setUserId(userId);
        errorModel.setCoinId(coinId);
        errorModel.setUserCoinKey(userId + BillConstants.POUND_SIGN + coinId);

        if (isCoin) {
            errorModel.getCoinAssets().setTotal(errorModel.getTotalValue());
            errorModel.getCoinAssets().setPropValue(errorModel.getPropValue());
            errorModel.getCoinAssets().setUnRealized(errorModel.getUnRealized());
        } else {
            errorModel.getCoinAssets().setTotal(BigDecimal.ZERO);
            errorModel.getCoinAssets().setPropValue(BigDecimal.ZERO);
            errorModel.getCoinAssets().setUnRealized(BigDecimal.ZERO);
        }

        errorModel.getUserTotalAssets().setTotal(userAssetsDTO.getTotalUsdtValue());
        errorModel.getUserTotalAssets().setPropValue(userAssetsDTO.getAssetsUsdtValue());
        errorModel.getUserTotalAssets().setUnRealized(userAssetsDTO.getUnRealizedUsdtValue());

        errorModel.getUserNegativeSum().setTotal(userAssetsDTO.getNegativeTotalUsdtValue());
        errorModel.getUserNegativeSum().setPropValue(userAssetsDTO.getNegativeAssetsUsdtValue());
        errorModel.getUserNegativeSum().setUnRealized(userAssetsDTO.getNegativeUnRealizedUsdtValue());

        errorModel.setErrorTimeStr(errorModel.getErrorTimeStr() != null ? errorModel.getErrorTimeStr() : checkOkTimeStr);
        errorModel.setCheckTimeStr(checkOkTimeStr);
    }

    /**
     * 根据系统账号类型获取系统账号
     *
     * @param systemAccountType
     * @return
     */
    private Set<Long> getNegativeUserWhiteList(String systemAccountType) {
        if (StringUtils.isNotBlank(systemAccountType)) {
            try {
                Set<Long> systemUserIds = CacheUtils.computeIfAbsent(systemUserIdCache, systemAccountType, () -> reconSystemAccountService.getSystemAccountIds(systemAccountType));
                return systemUserIds;
            } catch (Exception e) {
                log.error("getSystemAccountIds systemAccountType:{} error:{}", systemAccountType, e);
            }
        }
        return Collections.emptySet();
    }

    /**
     * 负值检测
     * 1、timeSliceUserAssetsNegativeOpen：业务线负值检测模式（用户维度、币种维度）
     * 2、负值白名单金额阀值告警（折U）
     * negativeUserWhiteList:{
     * "userId#币种(-1全币种)": 1000
     * }
     * 3、系统账号白名单阀值金额(折U)
     * neSystemAccountType：{
     * "SPOT_RECON_FEE#币种(-1全币种)": 1000
     * }
     *
     * @param apolloBizConfig
     * @return
     */
    private String checkUserNegativeAlarm(UserAssetNegativeErrorModel userAssetNegativeErrorModel, ApolloReconciliationBizConfig apolloBizConfig) {
        String userCoinKey = userAssetNegativeErrorModel.getUserCoinKey();
        String[] userCoinKeySplit = userCoinKey.split(BillConstants.POUND_SIGN);
        Long userId = Long.parseLong(userCoinKeySplit[0]);
        Integer coinId = Integer.parseInt(userCoinKeySplit[1]);
        String userAllKey = userId + BillConstants.POUND_SIGN + NegativeCheckTypeEnum.TOTAL_ASSETS.getCode();
        // 个人资产总和/单币种资产总和
        BigDecimal totalUsdtValue = userAssetNegativeErrorModel.getTotalValue();

        // 通用规则校验 大于该值则不报警
        if (totalUsdtValue.compareTo(apolloBizConfig.getNegativeCheckDefaultThresholdValue()) >= 0) {
            BizLogUtils.info(apolloBizConfig.getNegativeCheckPrintDetailLog(), "negativeCheckDefaultThresholdValue check unPass, accountType:{}  userId:{} coinId:{} TotalUsdtValue:{}", apolloBizConfig.getAccountType(), userId, coinId, totalUsdtValue.toPlainString());
            return null;
        }

        // 用户阈值规则检测
        for (Map.Entry<String, BigDecimal> entry : apolloBizConfig.getNegativeUserThresholdValue().entrySet()) {
            // userAllKey or userCoinKey = 配置key时,校验阈值
            if (userAllKey.equals(entry.getKey()) || userCoinKey.equals(entry.getKey())) {
                BizLogUtils.info(apolloBizConfig.getNegativeCheckPrintDetailLog(), "AbstractBillCheckService.checkUserNegativeAlarm hit apollo accountType:{} userCoinKey:{} thresholdValue:{}", apolloBizConfig.getAccountType(), entry.getKey(), entry.getValue());
                // 告警阈值判断（小于阈值告警）
                if (thresholdCheck(userAssetNegativeErrorModel, Integer.parseInt(entry.getKey().split(BillConstants.POUND_SIGN)[1]), entry.getValue(), apolloBizConfig)) {
                    return entry.getKey() + ": " + entry.getValue();
                }
            }
        }

        // 系统账号阈值规则检测
        for (Map.Entry<String, BigDecimal> entry : apolloBizConfig.getNegativeSystemUserThresholdValue().entrySet()) {
            String[] thresholdSplit = entry.getKey().split(BillConstants.POUND_SIGN);
            Set<Long> systemUserIdSet = getNegativeUserWhiteList(thresholdSplit[0]);
            // 该系统类型下账号列表包含当前用户，且当前coin=配置coin时（比如"SYSTEM_TYPE_A#123"），校验阈值
            int configCoinId = Integer.parseInt(thresholdSplit[1]);
            if (CollectionUtils.isNotEmpty(systemUserIdSet) && systemUserIdSet.contains(userId)
                    && (NegativeCheckTypeEnum.TOTAL_ASSETS.getCode() == configCoinId || coinId == configCoinId)) {
                BizLogUtils.info(apolloBizConfig.getNegativeCheckPrintDetailLog(), "AbstractBillCheckService.checkUserNegativeAlarm hit systemUser accountType:{} userCoinKey:{} thresholdValue:{}", apolloBizConfig.getAccountType(), entry.getKey(), entry.getValue());
                // 告警阈值判断（小于阈值告警）
                if (thresholdCheck(userAssetNegativeErrorModel, configCoinId, entry.getValue(), apolloBizConfig)) {
                    return entry.getKey() + ": " + entry.getValue();
                }
            }
        }

        BizLogUtils.info(apolloBizConfig.getNegativeCheckPrintDetailLog(), "AbstractBillCheckService.checkUserNegativeAlarm end accountType:{}  userCoinKey:{} TotalUsdtValue:{} NegativeUserThresholdValue:{} negativeSystemUserThresholdValue:{} isAlarm:{}", apolloBizConfig.getAccountType(), userCoinKey, JSON.toJSONString(userAssetNegativeErrorModel), JSON.toJSONString(apolloBizConfig.getNegativeUserThresholdValue()), JSON.toJSONString(apolloBizConfig.getNegativeSystemUserThresholdValue()));
        // 未匹配任何规则，则不告警
        return null;
    }

    /**
     * 告警白名单过滤，命中白名单的则不用告警
     *
     * @param userCoinPropNegativeCache
     * @param apolloBizConfig
     * @return 需要告警的列表及其触发类型
     */
    private Map<String, Tuple2<UserAssetNegativeErrorModel, String>> alarmWhitelistFiltering(Map<String, UserAssetNegativeErrorModel> userCoinPropNegativeCache, ApolloReconciliationBizConfig apolloBizConfig) {
        Map<String, Tuple2<UserAssetNegativeErrorModel, String>> result = new HashMap<>();
        // 查询apollo配置的系统用户列表
        Map<String, Tuple2<Set<Long>, Map<Integer, BigDecimal>>> negativeSystemUserThresholdValueNew = new HashMap<>();
        for (Map.Entry<String, Map<Integer, BigDecimal>> entry : apolloBizConfig.getNegativeSystemUserThresholdValueV2().entrySet()) {
            Set<Long> systemUserIdSet = getNegativeUserWhiteList(entry.getKey());
            negativeSystemUserThresholdValueNew.put(entry.getKey(), Tuples.of(systemUserIdSet, entry.getValue()));
        }

        // 遍历map，进行白名单过滤
        for (Map.Entry<String, UserAssetNegativeErrorModel> entry : userCoinPropNegativeCache.entrySet()) {
            Tuple2<Boolean, String> whitelistCheckResult = whitelistCheck(entry, apolloBizConfig, negativeSystemUserThresholdValueNew);
            // 白名单校验未通过（未匹配任何白名单 / 超过白名单阈值)，则加入告警集合
            if (!whitelistCheckResult.getT1()) {
                result.put(entry.getKey(), Tuples.of(entry.getValue(), whitelistCheckResult.getT2()));
            }
            // 白名单校验通过，舍弃该负值结果（不用告警）
        }
        return result;
    }

    /**
     * 白名单规则校验
     *
     * @param entry
     * @param apolloBizConfig
     * @param negativeSystemUserThresholdValueNew
     * @return Tuple2<Boolean, String>
     *     Tuple.T1 = true 表示白名单校验通过；
     *     Tuple.T1 = false 表示白名单校验未通过，且：Tuple.T2："" = 未命中任何白名单, 其他值 = 超过白名单阈值（具体到超过的那一项配置）
     */
    private Tuple2<Boolean, String> whitelistCheck(Map.Entry<String, UserAssetNegativeErrorModel> entry, ApolloReconciliationBizConfig apolloBizConfig,Map<String, Tuple2<Set<Long>, Map<Integer, BigDecimal>>> negativeSystemUserThresholdValueNew) {
        UserAssetNegativeErrorModel negativeErrorModel = entry.getValue();
        // 1. 是否在负值检测白名单
        if (apolloBizConfig.getNegativeUserWhiteList().contains(negativeErrorModel.getUserId())) {
            BizLogUtils.info(apolloBizConfig.getNegativeCheckPrintDetailLog(), "AbstractBillCheckService.checkUserNegativeAlarm negativeUserWhiteList accountType:{}  userCoinKey:{} ,model:{}", apolloBizConfig.getAccountType(), negativeErrorModel.getUserCoinKey(), JSON.toJSONString(negativeErrorModel));
            return Tuples.of(true, BillConstants.EMPTY);
        }

        // 2. 是否在负值告警默认金额白名单
        if (negativeErrorModel.getTotalValue().compareTo(apolloBizConfig.getNegativeCheckDefaultThresholdValue()) >= 0) {
            BizLogUtils.info(apolloBizConfig.getNegativeCheckPrintDetailLog(), "AbstractBillCheckService.checkUserNegativeAlarm negativeCheckDefaultThresholdValue accountType:{}  userCoinKey:{} ,model:{}", apolloBizConfig.getAccountType(), negativeErrorModel.getUserCoinKey(), JSON.toJSONString(negativeErrorModel));
            return Tuples.of(true, BillConstants.EMPTY);
        }

        String[] userCoinKeySplit = negativeErrorModel.getUserCoinKey().split(BillConstants.POUND_SIGN);
        Long userId = Long.parseLong(userCoinKeySplit[0]);
        int coinId = Integer.parseInt(userCoinKeySplit[1]);

        // 是否所有规则都未匹配
        boolean alwaysUnMatch = true;
        // 3. 是否在用户级阈值白名单
        for (Map.Entry<Long, Map<Integer, BigDecimal>> userConfigEntry : apolloBizConfig.getNegativeUserThresholdValueV2().entrySet()) {
            // 配置用户=当前用户，进行阈值校验
            if (userConfigEntry.getKey().equals(userId)) {
                // 该用户所有阈值配置校验，所有阈值都不超过才算通过白名单校验
                String userWhitelistResult = userWhitelistCheck(userConfigEntry.getValue(), coinId, negativeErrorModel);
                BizLogUtils.info(apolloBizConfig.getNegativeCheckPrintDetailLog(), "AbstractBillCheckService.checkUserNegativeAlarm negativeUserThresholdValueNew accountType:{}  userCoinKey:{} , userConfigEntry:{},userWhitelistResult:{}, model:{}", apolloBizConfig.getAccountType(), negativeErrorModel.getUserCoinKey(), JSON.toJSONString(userConfigEntry), userWhitelistResult, JSON.toJSONString(negativeErrorModel));
                alwaysUnMatch = alwaysUnMatch && NegativeCheckResultEnum.NO_MATCH.name().equals(userWhitelistResult);
                // 超过白名单阈值
                if (!(NegativeCheckResultEnum.PASS.name().equals(userWhitelistResult) || NegativeCheckResultEnum.NO_MATCH.name().equals(userWhitelistResult))) {
                    return Tuples.of(false, userConfigEntry.getKey() + BillConstants.POUND_SIGN + userWhitelistResult);
                }
            }
        }

        // 4. 是否在系统账号级阈值白名单
        for (Map.Entry<String, Tuple2<Set<Long>, Map<Integer, BigDecimal>>> sysUserConfigEntry : negativeSystemUserThresholdValueNew.entrySet()) {
            Set<Long> systemUserIdSet = sysUserConfigEntry.getValue().getT1();
            // 该系统类型下账号列表包含当前用户，进行阈值校验
            if (CollectionUtils.isNotEmpty(systemUserIdSet) && systemUserIdSet.contains(userId)) {
                String userWhitelistResult = userWhitelistCheck(sysUserConfigEntry.getValue().getT2(), coinId, negativeErrorModel);
                BizLogUtils.info(apolloBizConfig.getNegativeCheckPrintDetailLog(), "AbstractBillCheckService.checkUserNegativeAlarm negativeUserThresholdValueNew accountType:{}  userCoinKey:{} , userConfigEntry:{},userWhitelistResult:{}, model:{}", apolloBizConfig.getAccountType(), negativeErrorModel.getUserCoinKey(), JSON.toJSONString(sysUserConfigEntry), userWhitelistResult, JSON.toJSONString(negativeErrorModel));
                alwaysUnMatch = alwaysUnMatch && NegativeCheckResultEnum.NO_MATCH.name().equals(userWhitelistResult);
                if (!(NegativeCheckResultEnum.PASS.name().equals(userWhitelistResult) || NegativeCheckResultEnum.NO_MATCH.name().equals(userWhitelistResult))) {
                    // 超过白名单阈值
                    return Tuples.of(false, sysUserConfigEntry.getKey() + BillConstants.POUND_SIGN + userWhitelistResult);
                }
            }
        }

        if (!alwaysUnMatch) {
            // 通过白名单校验
            return Tuples.of(true, BillConstants.EMPTY);
        }

        BizLogUtils.info(apolloBizConfig.getNegativeCheckPrintDetailLog(), "AbstractBillCheckService.checkUserNegativeAlarm end accountType:{}  userCoinKey:{}, model:{}", apolloBizConfig.getAccountType(), negativeErrorModel.getUserCoinKey(), JSON.toJSONString(negativeErrorModel));
        // 5. 未命中任何白名单
        return Tuples.of(false, BillConstants.EMPTY);
    }

    /**
     * 校验负值在阈值白名单（所有阈值都校验通过才行）内
     *
     * @param coinThresholdConfigEntry
     * @param coinId
     * @param negativeErrorModel
     * @return
     */
    private String userWhitelistCheck(Map<Integer, BigDecimal> coinThresholdConfigEntry, int coinId, UserAssetNegativeErrorModel negativeErrorModel) {
        boolean isCheckUser = NegativeCheckTypeEnum.TOTAL_ASSETS.getCode() == coinId;
        // 是否匹配到规则标识
        boolean matchRule = false;
        // 遍历规则
        for (Map.Entry<Integer, BigDecimal> thresholdEntry : coinThresholdConfigEntry.entrySet()) {
            int configCoinId = thresholdEntry.getKey();
            // 用户维度检测
            if (isCheckUser) {
                // 非总负值规则，跳过
                if (NegativeCheckTypeEnum.TOTAL_ASSETS.getCode() != configCoinId) {
                    continue;
                }
            } else {
                // 币种维度校验，非总负值和该币种规则，跳过
                if (!(NegativeCheckTypeEnum.TOTAL_ASSETS.getCode() == configCoinId || coinId == configCoinId)) {
                    continue;
                }
            }
            matchRule = true;
            // 超过阈值，则白名单校验不通过，返回超过的阈值配置
            if (!withinWhitelistThreshold(negativeErrorModel, configCoinId, thresholdEntry.getValue())) {
                return JSON.toJSONString(thresholdEntry);
            }
        }
        return matchRule ? NegativeCheckResultEnum.PASS.name() : NegativeCheckResultEnum.NO_MATCH.name();
    }


    /**
     * 校验负值是否在单个阈值内（即：计算的负值 >= 阈值的负值）
     *
     * @param model
     * @param configCoinId
     * @param threshold
     * @return
     */
    private boolean withinWhitelistThreshold(UserAssetNegativeErrorModel model, Integer configCoinId, BigDecimal threshold) {
        BigDecimal checkValue;
        if (NegativeCheckTypeEnum.TOTAL_ASSETS.getCode() == configCoinId) {
            // 总负值阈值校验
            checkValue = model.getUserNegativeSum().getTotal();
        } else {
            // 单币种阈值校验
            checkValue = model.getCoinAssets().getTotal();
        }
        return checkValue.compareTo(threshold) >= 0;
    }

    /**
     * 阈值校验
     * @param model
     * @param coinId
     * @param threshold
     * @param apolloBizConfig
     * @return
     */
    private boolean thresholdCheck(UserAssetNegativeErrorModel model, Integer coinId, BigDecimal threshold, ApolloReconciliationBizConfig apolloBizConfig) {
        BigDecimal checkValue;
        if (NegativeCheckTypeEnum.TOTAL_ASSETS.getCode() == coinId) {
            // 总负值阈值校验
            checkValue = model.getUserNegativeSum().getTotal();
        } else {
            // 单币种阈值校验
            checkValue = model.getTotalValue();
        }
        boolean hit =  checkValue.compareTo(threshold) < 0;
        // 若命中，打印日志
        if (hit) {
            BizLogUtils.info(apolloBizConfig.getNegativeCheckPrintDetailLog(), "AbstractBillCheckService.checkUserNegativeAlarm hit {} thresholdValue accountType:{} userCoinKey:{} thresholdValue:{}  checkValue:{}", NegativeCheckTypeEnum.getCheckTypeName(coinId), apolloBizConfig.getAccountType(), model.getUserCoinKey(), threshold, checkValue);
        }
        return hit;
    }

    protected CheckResult doCheckProperty(ApolloReconciliationBizConfig apolloBizConfig, String accountUniqueId, Long userId, Integer coinId, List<CommonBillChangeData> billChangeDataList, BillCoinUserProperty billCoinUserProperty) {
        return CheckResult.DEFAULT_SUCCESS;
    }

    protected UserAssetsNegativeModel doCheckNegative(BillCoinUserProperty billCoinUserProperty, BigDecimal unRealized) {
        return null;
    }

    protected CheckResult doCheckFlowInOutBizType(AccountTypeEnum accountTypeEnum, List<CommonBillChangeData> billList) {
        ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());

        // 获取业务类型为[入]，流水为[负]数的数据
        List<String> transferIns = apolloBillConfig.getTransferIn().stream().filter(transferIn -> !apolloBillConfig.getExcludeTransferIn().contains(transferIn)).collect(Collectors.toList());
        List<CommonBillChangeData> negativeFlowList = billList.stream()
                .filter(flow -> MatchUtils.checkValidate(transferIns, flow.getBizType()) && flow.isNegativeProp())
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(negativeFlowList)) {
            // 发送报警
            String alarmTitle = "[入金]";
            //alarmNotifyService.alarmCheckFlowInOutBizType(accountTypeEnum.getCode(), alarmTitle, negativeFlowList);
            return CheckResult.DEFAULT_SUCCESS;
        }

        // 获取业务类型为[出]，流水为[正]数的数据
        List<String> transferOuts = apolloBillConfig.getTransferOut().stream().filter(transferOut -> !apolloBillConfig.getExcludeTransferOut().contains(transferOut)).collect(Collectors.toList());
        List<CommonBillChangeData> positiveFlowList = billList.stream()
                .filter(flow -> MatchUtils.checkValidate(transferOuts, flow.getBizType()) && flow.isPositiveProp())
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(positiveFlowList)) {
            // 发送报警
            String alarmTitle = "[出金]";
            //alarmNotifyService.alarmCheckFlowInOutBizType(accountTypeEnum.getCode(), alarmTitle, positiveFlowList);
            return CheckResult.DEFAULT_SUCCESS;
        }
        // todo jason 暂时不对入出类型
        return CheckResult.DEFAULT_SUCCESS;
    }

    protected boolean doTimeSliceBillCheck(BillTimeSliceDTO endSliceDTO, Date checkOkTime, ApolloReconciliationBizConfig apolloBizConfig) {
        return true;
    }


    protected boolean doProcessCoinTypeUserCheckForTimeSlice(AccountTypeEnum accountTypeEnum, BillTimeSliceDTO endAssetTimeSliceDTO, ApolloReconciliationBizConfig apolloBillConfig, Date checkOkTime) {
        Map<Long, Map<String, BillCoinTypeUserProperty>> coinTypeUserPropertyMap = endAssetTimeSliceDTO.getCoinTypeUserPropertyMap();
        Map<Long, Map<Integer, BillCoinUserProperty>> coinUserPropertyMap = endAssetTimeSliceDTO.getCoinUserPropertyMap();
        Map<Long, Map<Integer, BillCoinUserProperty>> calResultMap = new HashMap<>();
        coinTypeUserPropertyMap.forEach((userId, coinTypeMap) -> {
            Map<Integer, BillCoinUserProperty> calResultCoinMap = calResultMap.computeIfAbsent(userId, v -> new HashMap<>());
            coinTypeMap.forEach((k, v) -> {
                Integer coinId = BillCoinTypeUserProperty.getCoinIdFromKey(k);
                BillCoinUserProperty calBillCoinUserProperty = calResultCoinMap.computeIfAbsent(coinId, innerV -> {
                    BillCoinUserProperty billCoinUserProperty = new BillCoinUserProperty();
                    billCoinUserProperty.setUserId(userId);
                    billCoinUserProperty.setCoinId(coinId);
                    return billCoinUserProperty;
                });
                // 累加change
                calBillCoinUserProperty.addChange(v);
            });
        });
        for (Long userId : coinUserPropertyMap.keySet()) {
            Map<Integer, BillCoinUserProperty> coinMap = coinUserPropertyMap.get(userId);
            Map<Integer, BillCoinUserProperty> calCoinMap = calResultMap.get(userId);
            for (Integer coinId : coinMap.keySet()) {
                BillCoinUserProperty billCoinUserProperty = coinMap.get(coinId);
                BillCoinUserProperty calBillCoinUserProperty = calCoinMap.get(coinId);
                if (!checkBillCoinUserPropertyChange(billCoinUserProperty, calBillCoinUserProperty)) {
                    log.error("doProcessCoinTypeUserCheckForTimeSlice error checkTime single coin error change prop accountType {} , checkOkTime: {} coinId:{} billCoinUserProperty:{}, calBillCoinUserProperty:{} ,coinTypeUserPropertyMap {}"
                            , accountTypeEnum.getCode(), DateUtil.getDefaultDateStr(checkOkTime),
                            coinId, JSONObject.toJSONString(billCoinUserProperty), JSONObject.toJSONString(calBillCoinUserProperty), JSONObject.toJSONString(coinTypeUserPropertyMap.get(userId)));
                    return false;
                }
            }
        }
        return true;
    }


    public boolean checkBillCoinUserPropertyChange(BillCoinUserProperty billCoinUserProperty, BillCoinUserProperty newBillCoinUserProperty) {
        if (billCoinUserProperty != null && newBillCoinUserProperty != null) {
            return billCoinUserProperty.getChangeProp1().compareTo(newBillCoinUserProperty.getChangeProp1()) == 0
                    && billCoinUserProperty.getChangeProp2().compareTo(newBillCoinUserProperty.getChangeProp2()) == 0
                    && billCoinUserProperty.getChangeProp3().compareTo(newBillCoinUserProperty.getChangeProp3()) == 0
                    && billCoinUserProperty.getChangeProp4().compareTo(newBillCoinUserProperty.getChangeProp4()) == 0
                    && billCoinUserProperty.getChangeProp5().compareTo(newBillCoinUserProperty.getChangeProp5()) == 0
                    && billCoinUserProperty.getChangeProp6().compareTo(newBillCoinUserProperty.getChangeProp6()) == 0
                    && billCoinUserProperty.getChangeProp7().compareTo(newBillCoinUserProperty.getChangeProp7()) == 0
                    && billCoinUserProperty.getChangeProp8().compareTo(newBillCoinUserProperty.getChangeProp8()) == 0;
        }
        return false;
    }


    /**
     * 业务线出入对账
     *
     * @param accountTypeEnum
     * @param endAssetTimeSliceDTO
     * @param apolloBillConfig
     * @param checkOkTime
     * @return
     */
    protected boolean doProcessBillCheckForTimeSlice(AccountTypeEnum accountTypeEnum, BillTimeSliceDTO endAssetTimeSliceDTO, ApolloReconciliationBizConfig apolloBillConfig, Date checkOkTime) {
        // coin纬度
        Map<Integer, BillCoinProperty> endCoinPropertyDTOMap = endAssetTimeSliceDTO.getCoinPropertyMap();
        Map<String, BillCoinTypeProperty> endCoinTypePropertyMap = endAssetTimeSliceDTO.getCoinTypePropertyMap();
        BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountTypeEnum.getCode());

        // coin-type 纬度
        Map<Integer, Map<String, BigDecimal>> inMap = Maps.newHashMap();
        Map<Integer, Map<String, BigDecimal>> inChangeMap = Maps.newHashMap();
        Map<Integer, Map<String, BigDecimal>> outMap = Maps.newHashMap();
        Map<Integer, Map<String, BigDecimal>> outChangeMap = Maps.newHashMap();
        Map<Integer, Map<String, BigDecimal>> otherMap = Maps.newHashMap();
        Map<Integer, Map<String, BigDecimal>> otherChangeMap = Maps.newHashMap();
        if (MapUtils.isNotEmpty(endCoinTypePropertyMap)) {
            for (Map.Entry<String, BillCoinTypeProperty> entry : endCoinTypePropertyMap.entrySet()) {
                String bizType = BillCoinTypeProperty.getBizTypeFromKey(entry.getKey());
                Integer coinId = BillCoinTypeProperty.getCoinIdFromKey(entry.getKey());
                if (apolloBillConfig.getTransferIn().contains(bizType)) {
                    inMap.computeIfAbsent(coinId, (k) -> new HashMap()).put(bizType, billCheckService.getPropSumByProperty(entry.getValue()));
                    inChangeMap.computeIfAbsent(coinId, (k) -> new HashMap()).put(bizType, billCheckService.getChangePropSumByProperty(entry.getValue()));
                } else if (apolloBillConfig.getTransferOut().contains(bizType)) {
                    outMap.computeIfAbsent(coinId, (k) -> new HashMap()).put(bizType, billCheckService.getPropSumByProperty(entry.getValue()));
                    outChangeMap.computeIfAbsent(coinId, (k) -> new HashMap()).put(bizType, billCheckService.getChangePropSumByProperty(entry.getValue()));
                } else {
                    otherMap.computeIfAbsent(coinId, (k) -> new HashMap()).put(bizType, billCheckService.getPropSumByProperty(entry.getValue()));
                    otherChangeMap.computeIfAbsent(coinId, (k) -> new HashMap()).put(bizType, billCheckService.getChangePropSumByProperty(entry.getValue()));
                }
            }
        }

        // 对账逻辑
        CheckResult checkResult = CheckResult.success(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkOkTime);
        for (Map.Entry<Integer, BillCoinProperty> entry : endCoinPropertyDTOMap.entrySet()) {
            Integer coinId = entry.getKey();
            BigDecimal total = billCheckService.getPropSumByProperty(entry.getValue());
            BigDecimal fee = entry.getValue().getFee().negate();
            Map<String, Object> logMap = new HashMap<>();
            // 待冻账= 应收-实收
            BigDecimal unProfitTransfers = BigDecimal.ZERO;
            // 合约手续费待动账
            if (apolloBillConfig.isInoutCheckAccountTransferFeeFlag()) {
                BigDecimal profitTransferFeeProp = billCheckService.getPropSumByProperty(endCoinTypePropertyMap.get(BillCoinTypeProperty.generateKey(coinId, apolloBillConfig.getProfitTransferFeeCoinType())));
                BigDecimal profitTransferFeeRecycleProp = billCheckService.getPropSumByProperty(endCoinTypePropertyMap.get(BillCoinTypeProperty.generateKey(coinId, apolloBillConfig.getProfitTransferFeeRecycleCoinType())));
                unProfitTransfers = unProfitTransfers.add(fee.subtract(profitTransferFeeProp.add(profitTransferFeeRecycleProp)));
                logMap.put("profitTransferFeeProp", profitTransferFeeProp);
                logMap.put("profitTransferFeeRecycleProp", profitTransferFeeRecycleProp);
            }
            // 合约利息待动账
            if (apolloBillConfig.isInoutCheckAccountTransferInterestFeeFlag()) {
                // 应收
                BigDecimal interestFee = billCheckService.getPropSumByProperty(endCoinTypePropertyMap.get(BillCoinTypeProperty.generateKey(coinId, apolloBillConfig.getContractInterestDealFeeBizType()))).negate();
                // 实收+冲销
                BigDecimal profitTransferInterestFeeProp = billCheckService.getPropSumByProperty(endCoinTypePropertyMap.get(BillCoinTypeProperty.generateKey(coinId, apolloBillConfig.getProfitTransferInterestFeeCoinType())));
                BigDecimal profitTransferInterestFeeRecycleProp = billCheckService.getPropSumByProperty(endCoinTypePropertyMap.get(BillCoinTypeProperty.generateKey(coinId, apolloBillConfig.getProfitTransferInterestFeeRecycleCoinType())));
                unProfitTransfers = unProfitTransfers.add(interestFee.subtract(profitTransferInterestFeeProp.add(profitTransferInterestFeeRecycleProp)));
                logMap.put("interestFee", interestFee);
                logMap.put("profitTransferInterestFeeProp", profitTransferInterestFeeProp);
                logMap.put("profitTransferInterestFeeRecycleProp", profitTransferInterestFeeRecycleProp);
            }

            BigDecimal inAmount = inMap.computeIfAbsent(entry.getKey(), k -> new HashMap<String, BigDecimal>()).values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal outAmount = outMap.computeIfAbsent(entry.getKey(), k -> new HashMap<String, BigDecimal>()).values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);

            // 期末资产+本期手续费+待动账金额 -（入+出）-待动账期初差异
            BigDecimal result = total.subtract(inAmount.add(outAmount)).add(unProfitTransfers);
            boolean resultFlag = result.abs().compareTo(apolloBillConfig.getTotalDiffTolerateExitValue()) <= 0;
            logMap.put("result", result);
            logMap.put("total", total);
            logMap.put("inAmount", inAmount);
            logMap.put("outAmount", outAmount);
            logMap.put("unProfitTransfers", unProfitTransfers);
            logMap.put("totalDiffTolerateExitValue", apolloBillConfig.getTotalDiffTolerateExitValue());
            log.info("AbstractBillCheckService.doProcessBillCheckForTimeSlice info accountType:{} result:{} checkOkTime:{} coinId:{} logMap:{}"
                    , accountTypeEnum.getCode(), resultFlag, DateUtil.getDefaultDateStr(checkOkTime), coinId, JSON.toJSONString(logMap));
            if (!resultFlag) {
                Map<String, Object> logBizTypeDetailMap = new HashMap<>();
                logBizTypeDetailMap.put("transferInMap", inMap.get(coinId));
                logBizTypeDetailMap.put("transferOutMap", outMap.get(coinId));
                logBizTypeDetailMap.put("transferOtherMap", otherMap.get(coinId));
                logBizTypeDetailMap.put("transferInChangeMap", inChangeMap.get(coinId));
                logBizTypeDetailMap.put("transferOutChangeMap", outChangeMap.get(coinId));
                logBizTypeDetailMap.put("transferOtherChangeMap", otherChangeMap.get(coinId));
                log.info("AbstractBillCheckService.doProcessBillCheckForTimeSlice error accountType:{} result:{} checkOkTime:{} coinId:{} logMap:{} inoutDetailMap:{}"
                        , accountTypeEnum.getCode(), result.compareTo(BigDecimal.ZERO) == 0, DateUtil.getDefaultDateStr(checkOkTime), coinId, JSON.toJSONString(logMap), JSON.toJSONString(logBizTypeDetailMap));
                checkResult.addInOutProfitCheckData(entry.getKey(), total, inAmount, outAmount, unProfitTransfers, apolloBillConfig.getTotalDiffTolerateExitValue(), result, fee, otherChangeMap.get(entry.getKey()));
                checkResult.setResult(false);
            }
        }
        if (checkResult.isFail()) {
            int counter = inOutCheckResultCounterMap.computeIfAbsent(accountTypeEnum.getCode(), k -> new AtomicInteger(0)).incrementAndGet();
            checkResult.setCounter(counter);
            alarmNotifyService.alarm(accountTypeEnum.getCode(), CHECK_IN_AND_OUT_ASSETS_TEMPLATE, Map.of("checkResult", checkResult));
        } else {
            AtomicInteger counter = inOutCheckResultCounterMap.computeIfAbsent(accountTypeEnum.getCode(), k -> new AtomicInteger(0));
            if (counter.get() > 0) {
                counter.set(0);
                alarmNotifyService.alarm(accountTypeEnum.getCode(), CHECK_IN_AND_OUT_ASSETS_SUCCESS, accountTypeEnum.getCode(), DateUtil.getDefaultDateStr(checkOkTime));
            }
        }
        return checkResult.isSuccess();
    }

    protected CheckResult doBatchCheckProperty(ApolloReconciliationBizConfig apolloBizConfig, String accountUniqueId, Long userId, Integer coinId, List<CommonBillChangeData> billChangeDataList, BillCoinUserProperty billCoinUserProperty) {
        return CheckResult.DEFAULT_FAIL;
    }

    @Override
    public boolean checkCoinUserProperty(BillCoinUserProperty bizCoinUserProperty, BillCoinUserProperty memCoinUserProperty) {
        BigDecimal bizProp1 = bizCoinUserProperty != null ? getPropSumByUserProperty(bizCoinUserProperty) : BigDecimal.ZERO;
        BigDecimal memProp1 = memCoinUserProperty != null ? getPropSumByUserProperty(memCoinUserProperty) : BigDecimal.ZERO;
        return bizProp1.compareTo(memProp1) == 0;
    }

    @Override
    public boolean ignoreBillPropertyMatch(CommonBillChangeData eachBill, BillCoinUserProperty billCoinUserProperty) {
        return false;
    }

    /**
     * 根据用户资产修复流水
     *
     * @param billCoinUserProperty
     * @param billChangeDataList
     * @return
     */
    @Override
    public boolean repairBillFlowByCoinUserProperty(BillCoinUserProperty billCoinUserProperty, List<CommonBillChangeData> billChangeDataList) {
        // 修数据
        BigDecimal prop1 = billCoinUserProperty.getProp1();
        BigDecimal prop2 = billCoinUserProperty.getProp2();
        BigDecimal prop3 = billCoinUserProperty.getProp3();
        BigDecimal prop4 = billCoinUserProperty.getProp4();
        BigDecimal prop5 = billCoinUserProperty.getProp5();
        BigDecimal prop6 = billCoinUserProperty.getProp6();
        BigDecimal prop7 = billCoinUserProperty.getProp7();
        BigDecimal prop8 = billCoinUserProperty.getProp8();
        for (CommonBillChangeData commonBillChangeData : billChangeDataList) {
            prop1 = prop1.add(commonBillChangeData.getChangeProp1());
            prop2 = prop2.add(commonBillChangeData.getChangeProp2());
            prop3 = prop3.add(commonBillChangeData.getChangeProp3());
            prop4 = prop4.add(commonBillChangeData.getChangeProp4());
            prop5 = prop5.add(commonBillChangeData.getChangeProp5());
            prop6 = prop6.add(commonBillChangeData.getChangeProp6());
            prop7 = prop7.add(commonBillChangeData.getChangeProp7());
            prop8 = prop8.add(commonBillChangeData.getChangeProp8());
            commonBillChangeData.setProp1(prop1);
            commonBillChangeData.setProp2(prop2);
            commonBillChangeData.setProp3(prop3);
            commonBillChangeData.setProp4(prop4);
            commonBillChangeData.setProp5(prop5);
            commonBillChangeData.setProp6(prop6);
            commonBillChangeData.setProp7(prop7);
            commonBillChangeData.setProp8(prop8);
        }
        return true;
    }

    @Override
    public BigDecimal getExchangeDataChangeAssets(CommonBillChangeData commonBillChangeData) {
        return BigDecimal.ZERO;
    }

    @Override
    public AssetsBillCoinTypeProperty convertToAssetsBillCoinTypeProperty(Byte accountType, BillCoinTypeProperty billCoinTypeProperty) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        String assetsBizType = accountTypeEnum.getBizTypePrefix() + BillConstants.SEPARATOR + billCoinTypeProperty.getBizType();
        AssetsBillCoinTypeProperty assetsBillCoinTypeProperty = new AssetsBillCoinTypeProperty();
        assetsBillCoinTypeProperty.setCoinId(billCoinTypeProperty.getCoinId());
        assetsBillCoinTypeProperty.setBizType(assetsBizType);
        assetsBillCoinTypeProperty.setChangeProp1(billCoinTypeProperty.getChangeProp1());
        assetsBillCoinTypeProperty.setChangeProp2(billCoinTypeProperty.getChangeProp2());
        assetsBillCoinTypeProperty.setChangeProp3(billCoinTypeProperty.getChangeProp3());
        assetsBillCoinTypeProperty.setChangeProp4(billCoinTypeProperty.getChangeProp4());
        assetsBillCoinTypeProperty.setChangeProp5(billCoinTypeProperty.getChangeProp5());
        assetsBillCoinTypeProperty.setChangeProp6(billCoinTypeProperty.getChangeProp6());
        assetsBillCoinTypeProperty.setChangeProp7(billCoinTypeProperty.getChangeProp7());
        assetsBillCoinTypeProperty.setChangeProp8(billCoinTypeProperty.getChangeProp8());
        return assetsBillCoinTypeProperty;
    }

    @Override
    public BigDecimal getChangePropSumByProperty(List<? extends AbstractProperty> abstractProperty) {
        BigDecimal totalChangeProp = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(abstractProperty)) {
            for (AbstractProperty property : abstractProperty) {
                totalChangeProp = totalChangeProp.add(getChangePropSumByProperty(property));
            }
        }
        return totalChangeProp;
    }

    @Override
    public BigDecimal getQuoteTokenAssetsByProperty(AbstractProperty abstractProperty) {
        throw new RuntimeException("not support getQuoteTokenAssetsByProperty");
    }

    @Override
    public BigDecimal getQuoteTokenChangeAssets(AbstractProperty abstractProperty) {
        throw new RuntimeException("not support getQuoteTokenChangeAssets");
    }

    @Override
    public void setInitAndUnRealizedProp(AbstractProperty billSymbolProperty, BigDecimal initValue, BigDecimal unRealized) {
        throw new RuntimeException("not support setInitAndUnRealizedProp");
    }

    @Override
    public void setSCountLCountProp(AbstractProperty billSymbolProperty, BigDecimal sCount, BigDecimal lCount) {
        throw new RuntimeException("not support setSCountLCountProp");
    }

    @Override
    public BigDecimal getMarginRealizedProp(AbstractProperty abstractProperty) {
        throw new RuntimeException("not support getMarginRealizedProp");
    }

    @Override
    public BigDecimal getMarginRealizedChangeProp(AbstractProperty abstractProperty) {
        throw new RuntimeException("not support getMarginRealizedChangeProp");
    }

    @Override
    public void recalculateTransferFee(CommonBillChangeData commonBillChangeData, BillTimeSliceDTO billTimeSliceDTO) {
    }

    @Override
    public void calculateMsgTimeSliceSymbol(Byte accountType, BillTimeSliceDTO billTimeSliceDTO, CommonBillChangeData commonBillChangeData) {
        throw new RuntimeException("not support calculateMsgTimeSliceSymbol");
    }

    @Override
    public void calculateMsgTimeSliceSymbolCoin(Byte accountType, BillTimeSliceDTO billTimeSliceDTO, CommonBillChangeData commonBillChangeData) {
        throw new RuntimeException("not support calculateMsgTimeSliceSymbolCoin");
    }

    @Override
    public void setBillSymbolProperty(BillSymbolProperty billSymbolProperty, SymbolPropEnum symbolPropEnum, BigDecimal value) {
        throw new RuntimeException("not support setBillSymbolProperty");
    }

    @Override
    public void setBillSymbolCoinProperty(BillSymbolCoinProperty billSymbolCoinProperty, SymbolCoinPropEnum symbolCoinPropEnum, BigDecimal value) {
        throw new RuntimeException("not support setBillSymbolCoinProperty");
    }

    @Override
    public void setBillSymbolProperty(BillSymbolProperty billSymbolProperty, Supplier<Tuple2<SymbolPropEnum, BigDecimal>> supplier) {
        throw new RuntimeException("not support setBillSymbolProperty");
    }

    @Override
    public void setBillSymbolCoinProperty(BillSymbolCoinProperty billSymbolCoinProperty, Supplier<Tuple2<SymbolCoinPropEnum, BigDecimal>> supplier) {
        throw new RuntimeException("not support setBillSymbolCoinProperty");
    }

    @Override
    public BigDecimal getBillSymbolProperty(BillSymbolProperty billSymbolProperty, SymbolPropEnum symbolPropEnum) {
        throw new RuntimeException("not support getBillSymbolProperty");
    }

    @Override
    public BigDecimal getBillSymbolCoinProperty(BillSymbolCoinProperty billSymbolCoinProperty, SymbolCoinPropEnum symbolCoinPropEnum) {
        throw new RuntimeException("not support getBillSymbolCoinProperty");
    }
}
