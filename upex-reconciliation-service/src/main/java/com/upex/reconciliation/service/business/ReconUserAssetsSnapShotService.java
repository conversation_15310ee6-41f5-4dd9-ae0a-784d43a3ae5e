package com.upex.reconciliation.service.business;


import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.reconciliation.facade.dto.results.*;
import com.upex.reconciliation.facade.dto.results.ReconTotalAssetsDetailVo;
import com.upex.reconciliation.facade.dto.results.ReconUserOutInBalanceVo;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.params.*;
import com.upex.reconciliation.facade.params.ReconUserAssetsBySnapShotTimeParams;
import com.upex.reconciliation.facade.params.ReconUserTypeChangeParams;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.ticker.facade.dto.PriceVo;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021-01-11 10:39:18
 * @DES: 用户资产情况
 */
public interface ReconUserAssetsSnapShotService {

    ReconBillMessagePage<ReconTotalBalanceVo> selectCurrentSysAssetsSnapShotByTime(ReconUserAssetsSnapShotParams reconUserAssetsSnapShotParams);

    List<ReconUserOutInBalanceDetailsVo> selectOtherIncome(ReconUserTypeChangeParams reconUserTypeChangeParams);

    List<ReconUserOutInBalanceVo> selectOutInBalance(ReconUserTypeChangeParams reconUserTypeChangeParams);


    List<AccountAssetsInfoResult> queryUserSingleRealTimeAssets(Long userId, String subSystem,Long requestDate, GlobalBillConfig globalBillConfig);

    /**
     * 根据特定时间获取用户资产快照详情
     * @param params
     * @return
     */
    ReconTotalAssetsDetailVo getUserAssetsBySnapShotTime(ReconUserAssetsBySnapShotTimeParams params);

    List<BillCoinUserProperty> obtainDataWithDiffAccountType(AccountTypeEnum anEnum, Long userId, SysAssetsParams sysAssetsParams, Date checkTime);

    List<BillCoinUserProperty> obtainDataWithDiffAccountType(AccountTypeEnum anEnum, Long userId, SysAssetsParams sysAssetsParams, Date checkTime, Set<Integer> hasPositionCoinList);

    List<BillCoinUserProperty> obtainDataWithDiffAccountType(AccountTypeEnum anEnum, Long userId, SysAssetsParams sysAssetsParams, Date checkTime, Set<Integer> hasPositionCoinList, Boolean isMaster);


    // 按类型取时间点差值
    // 改动：查询出来的coin_type_user数据只有change
    List<BillCoinTypeUserProperty> getTimeDifferenceV2(Long userId, Long startTime, Long endTime, Integer accountType, String accountParam);

    List<BillCoinTypeUserProperty> getTimeDifferenceOnChange(Long userId, Long startTime, Long endTime, Integer accountType, String accountParam);

    ReconBillMessagePage<ReconUserOutInBalanceDetailsVo> selectDetails(ReconUserTypeChangeParams reconUserTypeChangeParams);


    String selectFinalSnapShotTime(ReconUserTypeChangeParams reconUserTypeChangeParams);

    ReconUserAssetsVo selectUserAssets(ReconUserAssetsSnapShotParams params);

    List<ReconBbTradingVo> selectBbTrading(ReconUserTypeChangeParams reconUserTypeChangeParams);

    List<ReconContractTradingVo> selectContract(ReconUserTypeChangeParams reconUserTypeChangeParams);

    ReconUserTradingVo selectUserProfitLossTotal(ReconUserTypeChangeParams reconUserTypeChangeParams);

    ReconTotalAssetsDetailVo listUserAssetsBySnapShotTime(Long userId, Long snapShotTime, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates);

    ReconTotalAssetsDetailVo listUserAssetsBySnapShotTime(Long userId, Long snapShotTime, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates, List<String> subSystemList, Set<Integer> hasPositionCoinList, Boolean isMaster);

    Queue<ReconTotalAssetsVO> createTotalAssetList(AccountTypeEnum typeEnum, List<BillCoinUserProperty> billCoinUserProperties, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates);
}
