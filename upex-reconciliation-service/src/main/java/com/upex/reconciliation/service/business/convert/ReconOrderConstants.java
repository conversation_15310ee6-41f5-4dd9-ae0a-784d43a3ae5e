package com.upex.reconciliation.service.business.convert;


import java.time.format.DateTimeFormatter;

public class ReconOrderConstants {


    // 主订单缓存前缀
    public static final String MAIN_ORDER_PREFIX = "convert:main_order:";
    // 流水订单缓存前缀
    public static final String FLOW_ORDER_PREFIX = "convert:flow_order:";
    // 主订单索引缓存前缀
    public static final String MAIN_ORDER_TIMESTAMP_PREFIX = "convert:main_order_timestamps:";
    // 默认主订单索引键
    public static final String DEFAULT_MAIN_ORDER_INDEX_KEY = "convert:main_order_index";

    public static final DateTimeFormatter SHARD_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmm");


}
