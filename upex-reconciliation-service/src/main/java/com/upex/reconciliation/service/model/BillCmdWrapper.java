package com.upex.reconciliation.service.model;

import com.upex.reconciliation.service.common.constants.ReconciliationCommandEnum;
import lombok.Data;

/**
 * 命令对象
 */
@Data
public class BillCmdWrapper {
    private final ReconciliationCommandEnum commandEnum;
    private final Object commandData;
    private Object fromCommandData;
    private Throwable ex;

    public BillCmdWrapper(ReconciliationCommandEnum commandEnum, Object commandData) {
        this.commandEnum = commandEnum;
        this.commandData = commandData;
    }

    public BillCmdWrapper(ReconciliationCommandEnum commandEnum, Object commandData, Object fromCommandData) {
        this.commandEnum = commandEnum;
        this.commandData = commandData;
        this.fromCommandData = fromCommandData;
    }
}
