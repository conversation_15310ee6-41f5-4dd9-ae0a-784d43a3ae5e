package com.upex.reconciliation.service.service.impl;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.upex.bill.dto.enums.CheckStatusEnum;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.OrderBillConfig;
import com.upex.reconciliation.service.dao.mapper.OrderBillConfigMapper;
import com.upex.reconciliation.service.model.config.ApolloBillOrderDetailConfig;
import com.upex.reconciliation.service.service.OrderBillConfigService;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class OrderBillConfigServiceImpl implements OrderBillConfigService {

    @Resource(name = "orderBillConfigMapper")
    private OrderBillConfigMapper orderBillConfigMapper;
    @Resource
    private BillDbHelper dbHelper;

    @Override
    public List<OrderBillConfig> getOrderBillConfigList(Long currentTimeMillis) {
        List<OrderBillConfig> orderBillConfigList = new ArrayList<>();
        List<ApolloBillOrderDetailConfig> billOrderCheckList = ReconciliationApolloConfigUtils.getApolloBillOrderDetailConfig();
        if (CollectionUtils.isEmpty(billOrderCheckList)) {
            log.info("getOrderBillConfigList() billOrderCheckList is empty");
            return orderBillConfigList;
        }

        for (ApolloBillOrderDetailConfig apolloBillOrderDetailConfig : billOrderCheckList) {
            if (!apolloBillOrderDetailConfig.isOpen()) {
                log.info("orderType={} orderParam={} stop bill task.....", apolloBillOrderDetailConfig.getOrderType(), apolloBillOrderDetailConfig.getOrderParam());
                continue;
            }

            OrderBillConfig orderBillConfig = this.getOrderBillConfig(apolloBillOrderDetailConfig.getOrderType(), apolloBillOrderDetailConfig.getOrderParam());
            if (orderBillConfig == null) {
                orderBillConfig = new OrderBillConfig();
                orderBillConfig.setOrderType(apolloBillOrderDetailConfig.getOrderType());
                orderBillConfig.setOrderParam(apolloBillOrderDetailConfig.getOrderParam());
                orderBillConfig.setCheckOkTime(new Date(apolloBillOrderDetailConfig.getBillBeginTime()));
                orderBillConfig.setStatus((byte) 0);
                orderBillConfig.setCreateTime(new Date());
                orderBillConfig.setUpdateTime(new Date());
                OrderBillConfig finalOrderBillConfig = orderBillConfig;
                dbHelper.doDbOpInReconMaster(() -> orderBillConfigMapper.insert(finalOrderBillConfig));
            } else {
                if (orderBillConfig.getCheckOkTime() == null) {
                    continue;
                }
            }
            if (currentTimeMillis - orderBillConfig.getCheckOkTime().getTime() < apolloBillOrderDetailConfig.getTimeInterval()) {
                continue;
            }
            orderBillConfigList.add(orderBillConfig);
        }
        return orderBillConfigList;
    }

    @Override
    public OrderBillConfig getOrderBillConfig(String orderType, String orderParam) {
        return dbHelper.doDbOpInReconMaster(() -> orderBillConfigMapper.getOrderBillConfig(orderType, orderParam));
    }

    @Override
    public void updateOrderBillConfig(OrderBillConfig orderBillConfig) {
        //更新配置表
        orderBillConfig.setUpdateTime(new Date());
        orderBillConfig.setStatus(CheckStatusEnum.OK.getCode());
        dbHelper.doDbOpInReconMaster(() -> orderBillConfigMapper.updateOrderBillConfig(orderBillConfig));
    }

    public int deleteById(Long id) {
        return dbHelper.doDbOpInReconMaster(() -> orderBillConfigMapper.deleteById(id));
    }

    @Override
    public void repairOrderBillCheck(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        log.info("repairBillBizTypeConfig data:{}", jobParam);
        String action = jsonObject.getString("action");
        JSONArray delIds = jsonObject.getJSONArray("delIds");
        JSONArray insertList = jsonObject.getJSONArray("insertList");
        JSONArray updateList = jsonObject.getJSONArray("updateList");
        Date nowDate = new Date();
        if ("delete".equals(action)) {
            if (delIds == null || delIds.size() == 0) {
                return;
            }
            for (int i = 0; i < delIds.size(); i++) {
                deleteById(delIds.getLong(i));
            }
        } else if ("insert".equals(action)) {
            if (insertList == null || insertList.size() == 0) {
                return;
            }
            List<OrderBillConfig> orderBillConfigList = Convert.toList(OrderBillConfig.class, insertList);
            if (CollectionUtils.isNotEmpty(orderBillConfigList)) {
                for (OrderBillConfig orderBillConfig : orderBillConfigList) {
                    orderBillConfig.setCreateTime(nowDate);
                    orderBillConfig.setUpdateTime(nowDate);
                    orderBillConfig.setStatus((byte) 0);
                    dbHelper.doDbOpInReconMaster(() -> orderBillConfigMapper.insert(orderBillConfig));
                }
            }
        } else if ("update".equals(action)) {
            if (updateList == null || updateList.size() == 0) {
                return;
            }
            List<OrderBillConfig> orderBillConfigList = Convert.toList(OrderBillConfig.class, updateList);
            if (CollectionUtils.isNotEmpty(orderBillConfigList)) {
                for (OrderBillConfig orderBillConfig : orderBillConfigList) {
                    updateOrderBillConfig(orderBillConfig);
                }
            }
        }
    }
}
