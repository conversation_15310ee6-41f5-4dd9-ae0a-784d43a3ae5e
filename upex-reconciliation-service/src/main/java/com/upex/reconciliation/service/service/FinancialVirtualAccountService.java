package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.dao.entity.FinancialVirtualAccount;

import java.util.List;

public interface FinancialVirtualAccountService {
    int batchSave(List<FinancialVirtualAccount> financialVirtualAccounts);

    void batchUpdateFinancialVirtualAccount(List<FinancialVirtualAccount> notCrossHourFinancialVirtualAccounts);

    List<FinancialVirtualAccount> listFinancialVirtualAccountByIds(List<FinancialVirtualAccount> financialVirtualAccounts);

    /**
     * 批量新增或者修改
     *
     * @param records
     * @return
     */
    int batchSaveOrUpdate(List<FinancialVirtualAccount> records);
}
