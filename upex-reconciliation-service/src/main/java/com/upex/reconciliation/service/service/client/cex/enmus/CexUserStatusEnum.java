package com.upex.reconciliation.service.service.client.cex.enmus;

import lombok.Getter;

@Getter
public enum CexUserStatusEnum {

    UNSYNC(0, "未同步"),
    NORMAL(1, "未启用/正常"),
    UNNORMAL(2, "异常");
    private Integer type;
    private String name;

    CexUserStatusEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static CexUserStatusEnum fromType(Integer type) {
        for (CexUserStatusEnum value : CexUserStatusEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
