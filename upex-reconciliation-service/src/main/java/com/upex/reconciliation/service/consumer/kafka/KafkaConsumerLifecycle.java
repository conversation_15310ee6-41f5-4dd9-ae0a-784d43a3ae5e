package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.otter.canal.connector.core.producer.MQMessageUtils;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.alibaba.otter.canal.protocol.Message;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;

import java.util.Iterator;
import java.util.List;

/**
 * kafka消费抽象类
 */
public interface KafkaConsumerLifecycle extends Runnable {
    void shutdown();

    boolean isRunning();

    String getThreadPrefixName();

    /**
     * canal消息解码
     *
     * @param message
     * @return
     */
    default List<FlatMessage> canalMessageDecode(Message message) {
        MQMessageUtils.EntryRowData[] datas = null;
        if (message.isRaw()) {
            List<ByteString> rawEntries = message.getRawEntries();
            datas = new MQMessageUtils.EntryRowData[rawEntries.size()];
            int i = 0;
            for (Iterator var12 = rawEntries.iterator(); var12.hasNext(); ++i) {
                ByteString byteString = (ByteString) var12.next();
                try {
                    CanalEntry.Entry entry = CanalEntry.Entry.parseFrom(byteString);
                    CanalEntry.RowChange rowChange = CanalEntry.RowChange.parseFrom(entry.getStoreValue());
                    datas[i] = new MQMessageUtils.EntryRowData();
                    datas[i].entry = entry;
                    datas[i].rowChange = rowChange;
                } catch (InvalidProtocolBufferException var5) {
                    throw new RuntimeException(var5);
                }
            }
        } else {
            datas = new MQMessageUtils.EntryRowData[message.getEntries().size()];
            int i = 0;
            for (Iterator var5 = message.getEntries().iterator(); var5.hasNext(); ++i) {
                CanalEntry.Entry entry = (CanalEntry.Entry) var5.next();
                try {
                    CanalEntry.RowChange rowChange = CanalEntry.RowChange.parseFrom(entry.getStoreValue());
                    datas[i] = new MQMessageUtils.EntryRowData();
                    datas[i].entry = entry;
                    datas[i].rowChange = rowChange;
                } catch (InvalidProtocolBufferException var4) {
                    throw new RuntimeException(var4);
                }
            }
        }
        return MQMessageUtils.messageConverter(datas, message.getId());
    }
}
