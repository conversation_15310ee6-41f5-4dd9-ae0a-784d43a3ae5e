package com.upex.reconciliation.service.common.constants.enums;

/**
 * <AUTHOR>
 *
 */
public enum ProfitStatusEnum {


    /**
     * 未执行
     */
    NOT_PERFORMED(0, "未执行"),
    /**
     * 已执行
     */
    PERFORMED(1, "已执行"),
    ;

    private final int code;
    private final String desc;

    ProfitStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 类型转枚举
     * @param code
     * @return
     */
    public static ProfitStatusEnum toEnum(int code) {
        for (ProfitStatusEnum item : ProfitStatusEnum.values()) {
            if (item.code == code) {
                return item;
            }
        }
        return null;
    }
}