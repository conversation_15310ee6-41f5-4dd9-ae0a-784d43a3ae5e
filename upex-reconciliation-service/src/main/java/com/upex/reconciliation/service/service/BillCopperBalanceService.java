package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.model.domain.BillCopperBalance;

import java.util.Date;
import java.util.List;

public interface BillCopperBalanceService {
    /**
     * 指定业务时间查询copper用户资产数据
     *
     * @param userIds userIds
     * @param bizTime bizTime
     * @return List<BillCopperBalance>
     */
    List<BillCopperBalance> selectByBizTime(List<Long> userIds, Date bizTime);

    /**
     * 批量插入copper用户余额数据
     *
     * @param balances balances
     */
    void batchInsert(List<BillCopperBalance> balances);
}
