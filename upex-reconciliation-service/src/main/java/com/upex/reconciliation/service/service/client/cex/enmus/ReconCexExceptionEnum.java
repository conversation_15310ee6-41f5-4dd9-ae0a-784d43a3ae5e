package com.upex.reconciliation.service.service.client.cex.enmus;

import com.upex.utils.framework.IErrorCode;

public enum ReconCexExceptionEnum implements IErrorCode {

    /**
     * 系统异常
     */
    UNKNOW_ERROR("90000", "系统异常"),
    /**
     * 非法参数
     */
    ILLEGAL_PARAMS("90001",  "参数错误"),
    /**
     * 交易所类型cexType不能为空/交易所类型不合法
     */
    ILLEGAL_CEXTYPE("90002",  "交易所类型错误"),

    ILLEGAL_USERTYPE("90003",  "用户类型错误"),
    ILLEGAL_TRADETYPE("90004",  "交易类型错误"),
    USER_NOT_EXISTS("90005",  "用户不存在"),
    USER_NOT_SUB_USER("90006",  "用户不是子用户"),
    USER_NO_PARENT_USER("90007",  "用户没有母用户"),
    USER_NOT_CONFIG_EEFTIVE_APIKEY("90008", "用户没有配置生效的apiKey"),
    USER_CONFIGUREED_MONITOR_APIKEY("90009",  "用户已配置监控apiKey"),
    MONITOR_APIKEY_ONLY_READ("90010", "监控apiKey只允许读权限"),
    USER_SECRET_EXPIRED("90011",  "用户密钥已过期"),
    USER_SECRET_NOT_MATCH("90012",  "用户密钥不匹配"),
    SUBUSER_MANAGER_CANNOT_MOD("90013",  "子用户管理用户不能修改"),
    EMAIL_CANNOT_MOD("90014",  "邮箱不能修改"),
    PARENT_USER_TYPE_CANNOT_MOD("90015",  "母用户类型不能修改"),
    CALSS_CAST_ERROR("90016",  "类转换错误"),
    BINANCE_API_EXCEPTION("90017",  "binance api异常"),
    APICLIENT_NOT_FOUND("90017",  "apiClient未找到"),
    CEX_TYPE_CANNOT_MOD("90018",  "交易所类型不能修改"),
    USERID_CANNOT_MOD("90019",  "用户id不能修改"),
    APIKEY_LABEL_CANNOT_BENULL("90020",  "apiKey标签不能为空"),
    APIKEY_CANNOT_BENULL("90021",  "apiKey不能为空"),
    APIKEY_PUB_SECRET_CANNOT_BENULL("90022",  "apiKey公钥不能为空"),
    IF_ASSET_MONITOR_CANNOT_BENULL("90023",  "资产监控不能为空"),
    ILLEGAL_USETYPE("90024",  "非法用途类型"),
    USER_MANAGER_STATUS_CANNOT_BENULL("90025",  "用户管理状态不能为空"),
    USER_MANAGER_ID_CANNOT_BENULL("90026",  "用户管理id不能为空"),
    PARENT_USERID_CANNOT_BENULL("90027",  "母用户id不能为空"),
    PARENT_USER_NOT_EXISTS("90028",  "母用户不存在"),
    APIPERMISSION_FAIL("90029",  "API读取权限失败"),
    APIKEY_ID_CANNOT_BENULL("90030",  "APIKEY_ID不能为空"),
    USER_APIKEY_CONFIG_NOT_EXISTS("90031",  "用户APIKEY配置不存在"),
    THIRD_ASSET_TYPE_CANNOT_BENULL("90032",  "资产类型不能为空"),
    UNSUPPORTED_ASSET_TYPE("90033",  "不支持的资产类型"),
    UNSUPPORTED_QUERY("90034",  "交易所类型或用户ID不能同时为空"),
    NOUSER_MATCH_QUERY("90034",  "没有符合查询条件的用户"),
    QUERY_SUB_USER_FAIL("90035",  "查询子用户失败"),
    ILLEGAL_CEXUSERSTATUS("90036",  "非法交易所用户状态"),
    ILLEGAL_USERMANAGERSTATUS("90037",  "非法用户管理状态"),
    UNSUPPORTED_ASSET_SYNC_TYPE("90038",  "不支持的资产记录同步类型"),
    COINNAME_CANNOT_BENULL("90039",  "币种名称不能为空"),
    APIKEY_EXISTS("90040",  "APIKEY已存在"),
    ID_CANNOT_BENULL("90041",  "id不能为空"),
    USER_EXISTS("90042",  "用户已存在"),
    SUBUSER_NOT_SUPPOERT("90042",  "子用户不支持查询"),
    USERID_CANNOT_BENULL("90043",  "用户id不能为空"),
    EMAIL_CANNOT_BENULL("90044",  "邮箱不能为空"),;

    private final String code;

    private final String msg;

    ReconCexExceptionEnum(String code,  String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    @Override
    public String getGroup() {
        return "upex-reconciliation-cex";
    }

    @Override
    public IErrorCode getSource() {
        return null;
    }

    @Override
    public String toString() {
        return getMsg();
    }

    public static String getGroupName(){
        return "upex-reconciliation-cex";
    }

    public static ReconCexExceptionEnum getByName(String name) {
        for (ReconCexExceptionEnum errorCode : values()) {
            if (errorCode.name().equals(name)) {
                return errorCode;
            }
        }
        return ReconCexExceptionEnum.ILLEGAL_PARAMS;
    }

    public static ReconCexExceptionEnum getByCode(String code) {
        for (ReconCexExceptionEnum errorCode : values()) {
            if (errorCode.getCode().equals(code)) {
                return errorCode;
            }
        }
        return ReconCexExceptionEnum.ILLEGAL_PARAMS;
    }
}
