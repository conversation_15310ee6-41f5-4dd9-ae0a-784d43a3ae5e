package com.upex.reconciliation.service.business;

import com.upex.config.coin.SpotCoinChainDTO;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.user.dto.UserLoginListDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/9/30 下午7:14
 * @Description 公共方法
 */
public interface CommonService {
    Map<Integer, String> getChainInfoMap();

    Map<Integer, String> getAllCoinsMap(AccountTypeEnum accountType);

    Map<Integer, PriceVo> getCoinIdTradePriceMap(Long snapshotTime);

    boolean isSysUser(Long userId);

    Map<Long, UserLoginListDTO> getAllSysUserMapCache(Long snapShotTime);

    Map<Long, UserLoginListDTO> getSystemUserInfoMap();

    Map<Integer, SpotCoinChainDTO> getAllWalletCoinInfoList();

    String getRealCoinName(String coinName, String chainName);

    Map<Integer,PriceVo> getRatesToUSDTCoinIdMap(AccountTypeEnum accountTypeEnum);


    /**
     * 根据币种id获取币种名称
     *
     * @param coinId 币种id
     * @return 币种名称
     */
    String getCoinName(Integer coinId);
//
//    String getAvailableCoinName(Integer coinId);
//
//    /**
//     * 根据币种名称获取对应汇率，同上
//     *
//     * @param leftCoinName
//     * @param rates
//     * @return
//     */
//    BigDecimal checkRateByCoinNameAndReturnUSDT(String leftCoinName, Map<String, PriceVo> rates);
//
//    /**
//     * 根据币种名称获取对应汇率
//     * 钱包的数据需要进行截取
//     *
//     * @param leftCoinName 币种名称
//     * @param rates 汇率map
//     * @param assetsEnum 资产类型
//     * @return
//     */
//    BigDecimal checkRateByCoinNameAndReturnUSDT(String leftCoinName, Map<String, PriceVo> rates, TotalAssetsEnum assetsEnum);
//
//    /**
//     * 根据币Id获取对应汇率，同上
//     *
//     * @param coinId
//     * @param rates
//     * @return
//     */
//    BigDecimal checkRateByCoinIdAndReturnUSDT(Integer coinId, Map<String, PriceVo> rates);
//    /**
//     * 根据币Id获取对应汇率，同上(所有的币种)
//     *
//     * @param coinId
//     * @param rates
//     * @return
//     */
//    BigDecimal checkRateByAllCoinIdAndReturnUSDT(Integer coinId, Map<String, PriceVo> rates);
//
    BigDecimal checkRateBySwapTokenIdReturnUSDT(Integer coinId, Map<Integer, PriceVo> rates);

    //
//    /**
//     * 获取系统账户，排除状态为废弃和不可用的
//     *
//     * @return 用户id集合
//     */
//    List<Long> getSystemUserIdsWithoutAbandon();
//
//    /**
//     * 获取状态为废弃和不可用的系统账户集合
//     *
//     * @return
//     */
//    List<Long> getSystemUserIdsAbandon();
//
    /**
     * 获取全部系统账户
     *
     * @return
     */
    List<Long> getSystemUserIds();
//
//    /**
//     * 获取全部系统账户信息
//     * @return
//     */
//    Map<Long,UserLoginListDTO> getSystemUserInfoMap();
//
//    Map<Long,UserLoginListDTO> getSystemUserInfoMapCache(Long snapshotTime);
//
//    /**
//     * 获取全部币种-usdt的汇率
//     *
//     * @return 汇率集合
//     */
    Map<String, PriceVo> getRatesToUSDT();
//
//    Map<String, PriceVo> getCoinNameRatesPriceMap(Long snapshotTime);
//
//    Map<String, PriceVo> getCoinNameRatesPriceMapCache(Long snapshotTime);
//
//    /**
//     * 获取指定时间的汇率
//     * @param snapshotTime  快照时间
//     * @return {@link Map< String, BigDecimal> }
//     * <AUTHOR>
//     * @date 2023/6/26 23:27
//     */
//    Map<String, BigDecimal> getCoinNameRatesMap(Long snapshotTime);
//

    /**
     * 获取指定时间的汇率
     *
     * @param snapshotTime 快照时间
     * @return {@link Map< String, BigDecimal> }
     * <AUTHOR>
     * @date 2023/6/26 23:27
     */
    Map<Integer, PriceVo> getCoinIdRatesMap(Long snapshotTime);

    //
    Map<Integer, PriceVo> getCoinIdRatesMapCache(Long snapshotTime);

    Map<Integer, PriceVo> getNewCoinIdRatesMapCache(Long snapshotTime, AccountTypeEnum accountTypeEnum);

    //
//    /**
//     * 将根据名称获取汇率转换为根据币种id获取汇率
//     * @param ratesToUSDTMap
//     * @return java.util.Map<java.lang.Integer,com.upex.ticker.facade.dto.PriceVo>
//     * @Date 2022/9/20 17:05
//     * <AUTHOR>
//     */
//    Map<Integer,PriceVo> convertToCoinIdMap(Map<String, PriceVo> ratesToUSDTMap);
//
//    /**
//     * 获取子系统列表
//     *
//     * @return
//     */
//    List<String> getSubSystem();
//
//    /**
//     * 获取全部币种（包含维护的老币种）,value取值需要大写
//     *
//     * @return
//     */
    Map<Integer, String> getAllCoinsMap();

    //
//    /**
//     * 获取全部币种（包含维护的老币种）,value取值需要大写
//     *
//     * @return
//     */
//    Map<Integer, String> getAllCoinsMap(AccountTypeEnum accountType);
//
//    /**
//     * 获取真实链名称
//     * @param coinName 币种名称
//     * @param chainName 链名称
//     * @return java.lang.String
//     * @Date 2022/8/26 15:02
//     * <AUTHOR>
//     */
//    String getRealCoinName(String coinName, String chainName);
//
//    /**
//     * 获取所有链信息
//     * @return java.util.Map<java.lang.Integer,java.lang.String>
//     * @Date 2022/8/22 10:17 AM
//     * <AUTHOR>
//     */
//    Map<Integer, String> getChainInfoMap();
//
//    /**
//     * 获取全部币种
//     *
//     * @return {@link Map< String, Integer>} 币种名称-币种id
//     * <AUTHOR>
//     * @date 下午4:23 2021/12/3
//     **/
//
    Map<String, Integer> getAllCoinId2Name();

    //    /**
//     * 获取全部币种Id-usdt的汇率
//     *
//     * @return 汇率集合
//     */
//    Map<Integer,PriceVo> getRatesToUSDTCoinIdMap(AccountTypeEnum accountTypeEnum);
//
    Map<String, Integer> reversalMap(Map<Integer, String> map);

    //
//    /**
//     * 获取所有钱包币种信息集合
//     * @return java.util.Map<java.lang.Integer,java.lang.String>
//     * @Date 2022/8/20 3:15 PM
//     * <AUTHOR>
//     */
//    Map<Integer, SpotCoinChainDTO> getAllWalletCoinInfoList();
//
//    /**
//     * 获取所有币种信息（缓存）
//     * @param snapShotTime 快照时间
//     * @return {@link Map< Integer, String> }
//     * <AUTHOR>
//     * @date 2022/12/30 23:24
//     */
    Map<Integer, String> getAllCoinsMapCache(Long snapShotTime);
//
//    /**
//     * 获取所有币种汇率（缓存）
//     * @param accountTypeEnum 账户类型
//     * @param snapShotTime 快照时间
//     * @return {@link Map< Integer, PriceVo> }
//     * <AUTHOR>
//     * @date 2022/12/30 23:24
//     */
//    Map<Integer, PriceVo> getRatesToUSDTCoinIdMapCache(AccountTypeEnum accountTypeEnum, Long snapShotTime);
//
//    /**
//     * 获取所有系统用户信息（缓存）
//     * @param snapShotTime
//     * @return
//     */
//    Map<Long, UserLoginListDTO> getAllSysUserMapCache(Long snapShotTime);
//    /**
//     * 判断是否系统用户
//     * @param userId
//     * <AUTHOR>
//     * @date 2023/3/2 21:04
//     */
//    boolean isSysUser(Long userId);

    /**
     * 获取系统用户集合
     * @param userId
     * <AUTHOR>
     * @date 2023/3/2 21:04
     */
    UserLoginListDTO getSysUserInfo(Long userId);

    /**
     * 获取系统用户展示字段
     * @param userId
     * @return
     */
    String getSysUserDisplayStr(Long userId);


    BigDecimal checkRateByCoinIdAndReturnUSDT(Integer coinId, Map<String, PriceVo> rates);

    String getAvailableCoinName(Integer coinId);

    /**
     * 根据获得对应的coinId
     *
     * @param coinName
     * @return
     */
    Integer getCoinIdByName(String coinName);
}
