package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

@Data
public class SubAccountInnerRes {
    /**
     * {
     *             "subUserId": **********,
     *             "email": "<EMAIL>",
     *             "remark": "",
     *             "isFreeze": false,
     *             "createTime": *************,
     *             "isManagedSubAccount": false,
     *             "isAssetManagementSubAccount": false
     *         }
     */
    private Long subUserId;
    private String email;
    private String remark;
    private Boolean isFreeze;
    private Long createTime;
    private Boolean isManagedSubAccount;
    private Boolean isAssetManagementSubAccount;

}
