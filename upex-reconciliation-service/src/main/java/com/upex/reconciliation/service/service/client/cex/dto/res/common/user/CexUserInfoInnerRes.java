package com.upex.reconciliation.service.service.client.cex.dto.res.common.user;


import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.ThirdCexUserInnerRes;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * CEX 用户信息数据结构
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CexUserInfoInnerRes  {

    /**
     * 后端自增ID
     */
    private Long id;

    /**
     * 交易所类型 @see CexTypeEnum
     */
    private Integer cexType;

    /**
     * 交易所注册邮箱
     */
    private String cexEmail;

    /**
     * 交易所注册用户ID
     */
    private String cexUserId;

    private String parentUserId;

    private String parentEmail;
    /**
     *  账户管理状态（1: 启用, 0: 禁用）
     */
    private Integer userManageStatus;
    /**
     * 账户状态（0: 未同步, 1: 正常, 2: 异常）
     */
    private Integer cexUserStatus;

    /**
     * 账户用途（1: 资金监控, 2: 搬砖）
     */
    private Integer useType;

    /**
     * 交易模式（1: 经典, 2: 统一）
     */
    private Integer tradeType;

    /**
     * 账户类型（1: 母账户, 2: 虚拟子账户, 3: 普通子账户, 4: 托管子账户）
     */
    private Integer userType;

    /**
     * API 状态 @See ApiKeyStatusEnum
     * 0 未配置 1 生效中 2 失效
     */
    private Integer apiKeyStatus;

    /**
     * 用户管理人邮箱
     */
    private String userManagerEmail;

    private String userManagerId;

    private String userKyb;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private List<ThirdCexUserInnerRes> subUserList;



}

