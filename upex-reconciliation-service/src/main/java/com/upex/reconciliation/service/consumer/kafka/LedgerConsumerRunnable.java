package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.RateLimiter;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.MemoryDataService;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.common.RateLimiterManager;
import com.upex.reconciliation.service.common.constants.enums.PropertySubTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.TimeUnitEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.config.AssetsKafkaConfig;
import com.upex.reconciliation.service.model.config.AssetsKafkaConsumerConfig;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.dto.AbstractLedgerMessage;
import com.upex.reconciliation.service.model.dto.BillLedgerTimeSliceDTO;
import com.upex.reconciliation.service.model.dto.PartitionOffsetDTO;
import com.upex.reconciliation.service.model.dto.SymbolCheckProperty;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.EnvUtil;
import com.upex.reconciliation.service.utils.MetricsUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.LEDGER_KAFKA_POLL_CONSUMER_ERROR;
import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.LEDGER_KAFKA_POLL_CONSUMER_ERROR_START_NODE_ISNULL;
import static com.upex.reconciliation.service.utils.MetricsUtil.HISTOGRAM_KAFKA_CONSUMER;

/**
 * @Description: kafka消费者消费消息, 手动同步提交offset
 **/

@Slf4j
public class LedgerConsumerRunnable implements KafkaConsumerLifecycle {
    private List<KafkaConsumer<String, String>> consumerList;
    private String accountType;
    private Map<String, Object> consumerConfig;
    private String topic;
    private String groupId;
    private Integer partitionNum;
    private volatile boolean running = true;
    private Map<Integer, KafkaConsumer<String, String>> partitionConsumerMap = new HashMap<>();
    private Set<Integer> closeConsumerPatition = new HashSet<>();
    private Map<Integer, Long> partitionOffsetMap = new ConcurrentHashMap<>();
    private AlarmNotifyService alarmNotifyService;
    private AssetsBillConfig assetsBillConfig;
    private MemoryDataService memoryDataService;
    private Map<Integer, BillLedgerTimeSliceDTO> partitionTimeSliceDTOMap = new ConcurrentHashMap<>();
    private Map<Integer, Long> partitionTimeSliceKeyMap = new ConcurrentHashMap<>();
    private Map<Integer, RateLimiter> partitionRateLimiterMap = new ConcurrentHashMap<>();
    private BillEngineManager billEngineManager;

    public LedgerConsumerRunnable(BillEngineManager billEngineManager,
                                  String kafkaServers,
                                  AssetsKafkaConsumerConfig kafkaConsumerConfig,
                                  AssetsBillConfig assetsBillConfig,
                                  String accountType,
                                  ReconciliationSpringContext context) {
        this.billEngineManager = billEngineManager;
        this.accountType = accountType;
        this.topic = kafkaConsumerConfig.getTopic();
        this.groupId = EnvUtil.getKafkaConsumerGroup(kafkaConsumerConfig.getGroupId());
        this.partitionNum = kafkaConsumerConfig.getPartitionNum();
        this.consumerList = new ArrayList<>();
        this.assetsBillConfig = assetsBillConfig;
        this.alarmNotifyService = context.getAlarmNotifyService();
        this.memoryDataService = context.getMemoryDataService();
        consumerConfig = new HashMap<String, Object>();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServers);
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, this.groupId);
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, kafkaConsumerConfig.getBatchSize());
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
    }

    @Override
    public void run() {
        // 初始化
        log.info("LedgerConsumerRunnable consumerRunnables.run");
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        AssetsKafkaConfig assetsKafkaConfig = globalBillConfig.getAssetsKafkaConfig();
        AssetsKafkaConsumerConfig kafkaConsumerConfig = assetsKafkaConfig.getKafkaConsumerConfig(accountType);
        KafkaConsumer<String, String> consumer = new KafkaConsumer<String, String>(consumerConfig);
        try {
            consumer.subscribe(Arrays.asList(topic));
            Set<TopicPartition> assignment = new HashSet<>();
            while (assignment.size() == 0) {
                consumer.poll(Duration.ofSeconds(3));
                assignment = consumer.assignment();
                log.info("LedgerConsumerRunnable try consumer.assignment {} {} {}", accountType, topic, groupId);
            }
            // 重置消费位点
            Map<String, List<PartitionOffsetDTO>> assetsKafkaPartitionMap = JSONObject.parseObject(assetsBillConfig.getConsumeOffset(), Map.class);
            List<PartitionOffsetDTO> partitionOffsetDTOs = assetsKafkaPartitionMap.get(accountType);
            Map<TopicPartition, OffsetAndMetadata> offsetMap = new HashMap<>();
            if (CollectionUtils.isEmpty(partitionOffsetDTOs)) {
                Long pullTime = kafkaConsumerConfig.getKafkaStartOffsetsTime() != null ? kafkaConsumerConfig.getKafkaStartOffsetsTime() : assetsBillConfig.getCheckOkTime().getTime() - kafkaConsumerConfig.getPullKafkaMessageBackTime();
                Map<TopicPartition, Long> topicPartitionLongMap = new HashMap<>();
                for (TopicPartition partitionInfo : assignment) {
                    topicPartitionLongMap.put(partitionInfo, pullTime);
                }
                Map<TopicPartition, Long> endOffsets = consumer.endOffsets(assignment);
                Map<TopicPartition, Long> beginningOffsets = consumer.beginningOffsets(assignment);
                Map<TopicPartition, OffsetAndTimestamp> offsetsForTimes = consumer.offsetsForTimes(topicPartitionLongMap);
                for (Map.Entry<TopicPartition, OffsetAndTimestamp> entry : offsetsForTimes.entrySet()) {
                    long offset = entry.getValue() != null ? entry.getValue().offset() : endOffsets.get(entry.getKey());
                    offsetMap.put(entry.getKey(), new OffsetAndMetadata(offset));
                }
                log.info("LedgerConsumerRunnable offset info beginningOffsets={} endOffsets={} offsetsForTimes={}",
                        beginningOffsets, endOffsets);
            } else {
                offsetMap = PartitionOffsetDTO.transToKafkaOffset(topic, partitionOffsetDTOs);
            }
            if (offsetMap.size() > 0) {
                for (Map.Entry<TopicPartition, OffsetAndMetadata> entry : offsetMap.entrySet()) {
                    consumer.seek(entry.getKey(), entry.getValue().offset());
                }
                consumer.commitSync();
            }
            log.info("LedgerConsumerRunnable finished start topic: {} config:{} offsetMap:{}",
                    topic, JSONObject.toJSONString(consumerConfig), offsetMap);
        } catch (Exception e) {
            log.error("LedgerConsumerRunnable.init reset offset error ", e);
        } finally {
            consumer.close();
        }
        // 创建监听器，数量和partition数量保持一致
        for (int i = 0; i < partitionNum; i++) {
            KafkaConsumer<String, String> currentConsumer = new KafkaConsumer<String, String>(consumerConfig);
            List<TopicPartition> partitions = new ArrayList<>();
            partitions.add(new TopicPartition(topic, i));
            currentConsumer.assign(partitions);
            partitionConsumerMap.put(i, currentConsumer);
            partitionRateLimiterMap.put(i, RateLimiter.create(kafkaConsumerConfig.getKafkaConsumerRateLimit()));
            partitionTimeSliceKeyMap.put(i, assetsBillConfig.getCheckOkTime().getTime());
        }

        log.info("LedgerConsumerRunnable init finished");
        for (Map.Entry<Integer, KafkaConsumer<String, String>> entry : partitionConsumerMap.entrySet()) {
            new Thread(() -> {
                try {
                    startConsume(entry.getKey(), entry.getValue());
                } catch (Exception e) {
                    log.error("LedgerConsumerRunnable.startConsume error accountType {} partition {}", accountType, entry.getKey(), e);
                }
            }, "kafka-consumer-ledger-" + accountType + "-" + entry.getKey()).start();
        }
    }

    private void startConsume(Integer partition, KafkaConsumer<String, String> consumer) {
        log.info("LedgerConsumerRunnable consumerRunnables.run accounType {} partition {}", accountType, partition);
        while (running) {
            try {
                GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
                AssetsKafkaConfig assetsKafkaConfig = globalBillConfig.getAssetsKafkaConfig();
                AssetsKafkaConsumerConfig kafkaConsumerConfig = assetsKafkaConfig.getKafkaConsumerConfig(accountType);
                ConsumerRecords<String, String> consumerRecords = consumer.poll(3000);
                consumerRecords.forEach(new java.util.function.Consumer<ConsumerRecord<String, String>>() {
                    @Override
                    public void accept(ConsumerRecord<String, String> consumerRecord) {
                        MetricsUtil.histogram(HISTOGRAM_KAFKA_CONSUMER + accountType + "_" + partition, () -> {
                            long kafkaTimestamp = consumerRecord.timestamp();
                            // 跳过指定时间段
                            List<Long> kafkaSkipOffset = kafkaConsumerConfig.getKafkaSkipOffset();
                            if (CollectionUtils.isNotEmpty(kafkaSkipOffset) && kafkaSkipOffset.size() == 2
                                    && kafkaSkipOffset.get(0) < kafkaTimestamp && kafkaTimestamp < kafkaSkipOffset.get(1)) {
                                return;
                            }
                            JSONObject jsonObject = JSON.parseObject(consumerRecord.value());
                            PropertySubTypeEnum propertySubTypeEnum = PropertySubTypeEnum.toEnum(jsonObject.getString("propertySubType"));
                            if (propertySubTypeEnum == null || propertySubTypeEnum == PropertySubTypeEnum.NONE) {
                                return;
                            }
                            boolean isStartProperty = propertySubTypeEnum.getCode().equals(PropertySubTypeEnum.COIN_START.getCode());
                            boolean isEndProperty = propertySubTypeEnum.getCode().equals(PropertySubTypeEnum.COIN_END.getCode());
                            BillLedgerTimeSliceDTO billLedgerTimeSliceDTO = partitionTimeSliceDTOMap.get(partition);
                            Date checkTime = jsonObject.getDate("checkTime");
                            checkTime = checkTime != null ? checkTime : jsonObject.getDate("checkOkTime");
                            Date internalCheckTime = billEngineManager.getInternalCheckTime();
                            if (internalCheckTime == null || checkTime.getTime() <= internalCheckTime.getTime()) {
                                return;
                            }
                            if (isStartProperty) {
                                log.info("ledger consumer recive start accountType:{} date {} partition:{} offset:{}", accountType, DateUtil.date2str(checkTime), partition, consumerRecord.offset());
                                billLedgerTimeSliceDTO = new BillLedgerTimeSliceDTO();
                                billLedgerTimeSliceDTO.setCheckTime(jsonObject.getDate("checkTime"));
                                partitionTimeSliceDTOMap.put(partition, billLedgerTimeSliceDTO);
                                memoryDataService.getLedgerTimeSliceKafkaComplete().computeIfAbsent(billLedgerTimeSliceDTO.getCheckTime().getTime(), key -> new ConcurrentHashMap<>()).put(accountType, Boolean.FALSE);
                            } else if (isEndProperty) {
                                if (billLedgerTimeSliceDTO != null) {
                                    log.info("ledger consumer recive end accountType:{} date {} partition:{} offset:{}", accountType, DateUtil.date2str(checkTime), partition, consumerRecord.offset());
                                    memoryDataService.getLedgerTimeSliceCoinPropertyMap().computeIfAbsent(billLedgerTimeSliceDTO.getCheckTime().getTime(), key -> new ConcurrentHashMap<>()).put(accountType, billLedgerTimeSliceDTO.getBillCoinPropertyMap());
                                    memoryDataService.getLedgerTimeSliceCoinTypePropertyMap().computeIfAbsent(billLedgerTimeSliceDTO.getCheckTime().getTime(), key -> new ConcurrentHashMap<>()).put(accountType, billLedgerTimeSliceDTO.getBillCoinTypePropertyMap());
                                    memoryDataService.getLedgerTimeSliceSymbolCheckPropertyMap().computeIfAbsent(billLedgerTimeSliceDTO.getCheckTime().getTime(), key -> new ConcurrentHashMap<>()).put(accountType, billLedgerTimeSliceDTO.getSymbolCheckPropertyList());
                                    memoryDataService.getLedgerTimeSliceContractProfitCoinDetailMap().computeIfAbsent(billLedgerTimeSliceDTO.getCheckTime().getTime(), key -> new ConcurrentHashMap<>()).put(accountType, billLedgerTimeSliceDTO.getContractProfitCoinDetailList());
                                    memoryDataService.getLedgerTimeSliceContractProfitSymbolDetailMap().computeIfAbsent(billLedgerTimeSliceDTO.getCheckTime().getTime(), key -> new ConcurrentHashMap<>()).put(accountType, billLedgerTimeSliceDTO.getContractProfitSymbolDetailList());
                                    memoryDataService.getLedgerTimeSliceTransferFeeCoinDetailMap().computeIfAbsent(billLedgerTimeSliceDTO.getCheckTime().getTime(), key -> new ConcurrentHashMap<>()).put(accountType, billLedgerTimeSliceDTO.getBillTransferFeeCoinDetailList());
                                    memoryDataService.getLedgerTimeSliceKafkaComplete().computeIfAbsent(billLedgerTimeSliceDTO.getCheckTime().getTime(), key -> new ConcurrentHashMap<>()).put(accountType, Boolean.TRUE);
                                    partitionTimeSliceKeyMap.put(partition, billLedgerTimeSliceDTO.getCheckTime().getTime());
                                } else {
                                    log.error("ledger consumer recive end billLedgerTimeSliceDTO is null accountType:{} date {} partition:{} billLedgerTimeSliceDTOIsNull:{} abstractProperty:{}", accountType, DateUtil.date2str(checkTime), partition, Boolean.TRUE, JSONObject.toJSONString(jsonObject));
                                    alarmNotifyService.alarm(Byte.valueOf(accountType), LEDGER_KAFKA_POLL_CONSUMER_ERROR_START_NODE_ISNULL, accountType, DateUtil.date2str(checkTime), partition, Boolean.TRUE, JSONObject.toJSONString(jsonObject));
                                }
                            } else {
                                if (billLedgerTimeSliceDTO != null) {
                                    AbstractLedgerMessage abstractProperty = jsonObject.toJavaObject(propertySubTypeEnum.getClazz());
                                    if (abstractProperty instanceof BillCoinProperty) {
                                        billLedgerTimeSliceDTO.getBillCoinPropertyList().add((BillCoinProperty) abstractProperty);
                                    } else if (abstractProperty instanceof BillCoinTypeProperty) {
                                        billLedgerTimeSliceDTO.getBillCoinTypePropertyList().add((BillCoinTypeProperty) abstractProperty);
                                    } else if (abstractProperty instanceof SymbolCheckProperty) {
                                        billLedgerTimeSliceDTO.getSymbolCheckPropertyList().add((SymbolCheckProperty) abstractProperty);
                                    } else if (abstractProperty instanceof BillContractProfitCoinDetail) {
                                        billLedgerTimeSliceDTO.getContractProfitCoinDetailList().add((BillContractProfitCoinDetail) abstractProperty);
                                    } else if (abstractProperty instanceof BillContractProfitSymbolDetail) {
                                        billLedgerTimeSliceDTO.getContractProfitSymbolDetailList().add((BillContractProfitSymbolDetail) abstractProperty);
                                    } else if (abstractProperty instanceof BillTransferFeeCoinDetail) {
                                        billLedgerTimeSliceDTO.getBillTransferFeeCoinDetailList().add((BillTransferFeeCoinDetail) abstractProperty);
                                    }
                                } else {
                                    log.error("ledger consumer recive service billLedgerTimeSliceDTO is null accountType:{} date {} partition:{} billLedgerTimeSliceDTOIsNull:{} abstractProperty:{}", accountType, DateUtil.date2str(checkTime), partition, Boolean.TRUE, JSONObject.toJSONString(jsonObject));
                                    alarmNotifyService.alarm(Byte.valueOf(accountType), LEDGER_KAFKA_POLL_CONSUMER_ERROR_START_NODE_ISNULL, accountType, DateUtil.date2str(checkTime), partition, Boolean.TRUE, JSONObject.toJSONString(jsonObject));
                                }
                                Integer coinId = jsonObject.getInteger("coinId");
                                if (coinId != null && globalBillConfig.getLedgerLogCoinSet().contains(coinId)) {
                                    boolean billLedgerTimeSliceDTOIsNull = Objects.isNull(billLedgerTimeSliceDTO);
                                    log.info("ledger consumer recive property accountType:{} date {} partition:{}, billLedgerTimeSliceDTOIsNull:{}, abstractProperty:{}", accountType, DateUtil.date2str(checkTime), partition, billLedgerTimeSliceDTOIsNull, JSONObject.toJSONString(jsonObject));
                                }
                            }
                        });
                    }
                });
                consumer.commitSync();
                int messageCount = consumerRecords.count();
                if (messageCount > 0) {
                    rateLimitCheck(partition, messageCount);
                }
            } catch (Exception e) {
                log.error("LedgerConsumerRunnable startConsume error accountType {} partition {}", accountType, partition, e);
                alarmNotifyService.alarm(Byte.valueOf(accountType), LEDGER_KAFKA_POLL_CONSUMER_ERROR, accountType, partition);
            }
        }
        consumer.close();
        closeConsumerPatition.add(partition);
        log.info("LedgerConsumerRunnable consumer.close success {} {}", accountType, partition);
    }

    /**
     * 是否限流
     *
     * @return
     */
    private void rateLimitCheck(Integer partition, Integer messageCount) {
        if (partitionRateLimiterMap.size() < partitionNum) {
            return;
        }
        partitionRateLimiterMap.get(partition).acquire(messageCount);
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        AssetsKafkaConfig assetsKafkaConfig = globalBillConfig.getAssetsKafkaConfig();
        AssetsKafkaConsumerConfig kafkaConsumerConfig = assetsKafkaConfig.getKafkaConsumerConfig(accountType);
        // 时间片队列是否限流
        Long intervalMs = TimeUnitEnum.toDuration(kafkaConsumerConfig.getTimesliceRateLimitGap()).toMillis();
        Long ledgerMinTimeSlice = memoryDataService.getLedgerMinTimeSlice();
        Long currentPartitionTimeSlice = partitionTimeSliceKeyMap.get(partition);
        Boolean isRateLimit = currentPartitionTimeSlice - ledgerMinTimeSlice > intervalMs;
        // 执行限流逻辑判断
        double rate = partitionRateLimiterMap.get(partition).getRate();
        if (isRateLimit) {
            log.info("LedgerConsumerRunnable rateLimiter accountType:{} partition:{} minTimeSlice:{} currentPartitionTimeSlice:{}", accountType, partition, DateUtil.date2str(new Date(ledgerMinTimeSlice)), DateUtil.date2str(new Date(currentPartitionTimeSlice)));
            if (rate != RateLimiterManager.ERROR_LIMIT_RATE) {
                partitionRateLimiterMap.get(partition).setRate(RateLimiterManager.ERROR_LIMIT_RATE);
            }
        } else if (rate == RateLimiterManager.ERROR_LIMIT_RATE) {
            log.info("LedgerConsumerRunnable recover rateLimiter accountType:{} partition:{} minTimeSlice:{} currentPartitionTimeSlice:{}", accountType, partition, DateUtil.date2str(new Date(ledgerMinTimeSlice)), DateUtil.date2str(new Date(currentPartitionTimeSlice)));
            partitionRateLimiterMap.get(partition).setRate(kafkaConsumerConfig.getKafkaConsumerRateLimit());
        }
    }

    @Override
    public void shutdown() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return closeConsumerPatition.size() != partitionNum;
    }

    @Override
    public String getThreadPrefixName() {
        return "kafka-consumer-thread-ledger-" + accountType;
    }
}


