package com.upex.reconciliation.service.dao.mapper.cex;

import com.upex.reconciliation.service.dao.cex.entity.ThirdCexWithdrawHistory;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.UserWithdrawHistoryListReq;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface ThirdCexWithdrawHistoryMapper {

    int insert(ThirdCexWithdrawHistory history);

    ThirdCexWithdrawHistory selectById(Long id);

    List<ThirdCexWithdrawHistory> selectByCexUserAndType(
        @Param("cexUserId") String cexUserId,
        @Param("cexType") Integer cexType);

    int checkWithdraw(Long id,Integer isLegal,String bgUserId,Integer isBgSys);

    List<ThirdCexWithdrawHistory> selectList(
        @Param("cexUserId") String cexUserId,
        @Param("cexType") Integer cexType,
        @Param("coinName") String coinName,
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime);

    int batchInsert(List<ThirdCexWithdrawHistory> records);

    List<ThirdCexWithdrawHistory> selectPageByUserIds(
        @Param("cexUserIds") List<String> cexUserIds,
        @Param("withdrawReq") UserWithdrawHistoryListReq withdrawReq);

    int countPageByUserIds(
        @Param("cexUserIds") List<String> cexUserIds,
        @Param("withdrawReq") UserWithdrawHistoryListReq withdrawReq
        );

    List<ThirdCexWithdrawHistory> selectUnCheckWithdraw(Integer cexType,  Integer isLegal);

    int deleteByWithdrawIds(@Param("withdrawIds") List<String> withdrawIds);
}
