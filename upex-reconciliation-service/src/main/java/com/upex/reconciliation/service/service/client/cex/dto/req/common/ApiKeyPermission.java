package com.upex.reconciliation.service.service.client.cex.dto.req.common;

import lombok.Data; /**
 * API Key 权限详细信息
 */
@Data
public class ApiKeyPermission {
    private Boolean ipRestrict;
    private Long createTime;
    private Boolean enableReading;
    private Boolean enableWithdrawals;
    private Boolean enableInternalTransfer;
    private Boolean enableMargin;
    private Boolean enableFutures;
    private Boolean permitsUniversalTransfer;
    //private Boolean enableVanillaOptions;
    //private Boolean enableFixApiTrade;
    //private Boolean enableFixReadOnly;
    private Boolean enableSpotAndMarginTrading;
    private Boolean enablePortfolioMarginTrading;

    public Boolean isReadOnly() {
        return enableReading &&!enableSpotAndMarginTrading&&!enablePortfolioMarginTrading&&!enableFutures&&!enableMargin&&!enableInternalTransfer&&!enableWithdrawals;
    }

    
}
