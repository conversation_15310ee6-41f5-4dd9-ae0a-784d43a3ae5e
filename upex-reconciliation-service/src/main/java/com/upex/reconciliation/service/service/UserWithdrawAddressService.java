package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.UserWithdrawAddress;
import com.upex.reconciliation.service.dao.mapper.cex.UserWithdrawAddressMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class UserWithdrawAddressService {

    @Resource
    UserWithdrawAddressMapper userWithdrawAddressMapper;

    @Resource
    BillDbHelper billDbHelper;

    public Integer insert(UserWithdrawAddress record) {
        return billDbHelper.doDbOpInReconMaster(() -> userWithdrawAddressMapper.insert(record));
    }


    public UserWithdrawAddress selectByAddress(String address,
                                               String coinName,
                                               String network
    ) {
        return billDbHelper.doDbOpInReconMaster(() -> userWithdrawAddressMapper.selectByAddress(address, coinName, network));
    }


}
