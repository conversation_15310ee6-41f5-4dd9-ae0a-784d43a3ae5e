package com.upex.reconciliation.service.dao.mapper.cex;

import com.upex.reconciliation.service.dao.cex.entity.ThirdCexTransferHistory;
import org.apache.ibatis.annotations.Param;

import javax.annotation.Resource;
import java.util.List;
@Resource
public interface ThirdCexTransferHistoryMapper {

    /**
     * 插入一条记录
     */
    int insert(ThirdCexTransferHistory record);

    /**
     * 根据 ID 查询记录
     */
    ThirdCexTransferHistory selectById(Long id);

    /**
     * 根据 CEX 用户ID 和交易所类型查询
     */
    List<ThirdCexTransferHistory> selectByCexUserAndType(@Param("cexUserId") String cexUserId, @Param("cexType") Integer cexType);

    /**
     * 更新记录
     */
    int update(ThirdCexTransferHistory record);

    /**
     * 根据交易ID删除记录
     */
    int deleteByTransferIds(List<String>  transferIds);

    int batchInsert(List<ThirdCexTransferHistory> records);
}
