package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AbstractTransferService;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.enums.ProfitTransferTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer;
import com.upex.reconciliation.service.model.config.ApolloProfitTransferConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.spot.dto.enums.SpotBillBizTypeEnum;
import com.upex.spot.dto.params.assets.UpdateAssetParams;
import com.upex.spot.facade.bill.SpotBillAssetClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.TRANSFER_SERVICE_ERROR;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SpotTransferServiceImpl extends AbstractTransferService {
    @Resource
    private SpotBillAssetClient spotBillAssetClient;
    @Resource
    private AlarmNotifyService alarmNotifyService;

    @Override
    public Boolean transfer(AccountTypeEnum anEnum, BillContractProfitTransfer item, Date transferTime, Map<Integer, String> allCoinsMap) {
        try {
            ApolloProfitTransferConfig apolloProfitTransferConfig = ReconciliationApolloConfigUtils.getApolloProfitTransferConfig();
            UpdateAssetParams params = new UpdateAssetParams();
            params.setAccountId(item.getTransferInUserId());
            params.setCoinId(item.getCoinId());
            params.setBalanceChange(item.getTransferCount());
            params.setBusinessLine(anEnum.getBusinessLineEnum());
            params.setBizType(getBillTypeEnum(item.getTransferType(), item.getTransferCount()));
            params.setBizOrderId(item.getId());
            params.setBizTime(transferTime);
            log.info("SpotTransferServiceImpl transfer params:{}", JSON.toJSONString(params));
            return spotBillAssetClient.updateAsset(params);
        } catch (Exception e) {
            alarmNotifyService.alarm(TRANSFER_SERVICE_ERROR, anEnum.getCode());
            throw e;
        }
    }


    private SpotBillBizTypeEnum getBillTypeEnum(int transferType, BigDecimal bigDecimal) {
        if (ProfitTransferTypeEnum.COIN_PROFIT.getCode() == (transferType)) {
            if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                return SpotBillBizTypeEnum.BILL_CONTRACT_EXCHANGE_SYS_OUT;
            } else {
                return SpotBillBizTypeEnum.BILL_CONTRACT_EXCHANGE_SYS_IN;
            }
        } else if (ProfitTransferTypeEnum.SYMBOL_PROFIT.getCode() == (transferType)) {
            if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                return SpotBillBizTypeEnum.BILL_CONTRACT_PROFIT_SYS_OUT;
            } else {
                return SpotBillBizTypeEnum.BILL_CONTRACT_PROFIT_SYS_IN;
            }
        } else if (ProfitTransferTypeEnum.SYMBOL_PROFIT_SYSTEM_RESET.getCode() == (transferType)) {
            if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                return SpotBillBizTypeEnum.BILL_CONTRACT_RETURN_SYS_OUT;
            } else {
                return SpotBillBizTypeEnum.BILL_CONTRACT_RETURN_SYS_IN;
            }
        } else if (ProfitTransferTypeEnum.SYSTEM_SPOT_FEE.getCode() == (transferType)) {
            if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                return SpotBillBizTypeEnum.BILL_MATCH_FEE_SYS_OUT;
            } else {
                return SpotBillBizTypeEnum.BILL_MATCH_FEE_SYS_IN;
            }
        } else if (ProfitTransferTypeEnum.SYSTEM_SPOT_FEE_RESET.getCode() == (transferType)) {
            if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                return SpotBillBizTypeEnum.BILL_MATCH_FEE_SYS_OUT;
            } else {
                return SpotBillBizTypeEnum.BILL_MATCH_FEE_SYS_IN;
            }
        }
        return null;
    }
}
