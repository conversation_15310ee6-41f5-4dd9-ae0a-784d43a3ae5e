package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.upex.mixcontract.common.literal.enums.BusinessTypeEnum;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AbstractTransferService;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.enums.ProfitTransferTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer;
import com.upex.reconciliation.service.model.config.ApolloProfitTransferConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.unified.account.dto.constants.UtaConstants;
import com.upex.unified.account.dto.enums.BizTypeEnum;
import com.upex.unified.account.dto.params.BillFeeUpdateAssetParam;
import com.upex.unified.account.facade.service.AssetRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.TRANSFER_SERVICE_ERROR;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class UtaTransferServiceImpl extends AbstractTransferService {
    @Resource
    private AssetRpcService assetRpcService;
    @Resource
    private AlarmNotifyService alarmNotifyService;

    @Override
    public Boolean transfer(AccountTypeEnum anEnum, BillContractProfitTransfer item, Date transferTime, Map<Integer, String> allCoinsMap) {
        try {
            ApolloProfitTransferConfig apolloProfitTransferConfig = ReconciliationApolloConfigUtils.getApolloProfitTransferConfig();
            BillFeeUpdateAssetParam updateAssetParam = BillFeeUpdateAssetParam.builder()
                    .accountId(item.getTransferInUserId())
                    .tokenId(item.getCoinId())
                    .balanceChange(item.getTransferCount())
                    .bizNo(item.getId())
                    .bizTime(transferTime)
                    .bizType(getBillTypeEnum(ProfitTransferTypeEnum.toEnum(item.getTransferType()), item.getTransferCount()).getCode())
                    .businessType(getBusinessType(ProfitTransferTypeEnum.toEnum(item.getTransferType())))//业务类型：0现货、杠杆  3合约
                    .build();
            log.info("UtaTransferServiceImpl transfer params:{}", JSON.toJSONString(updateAssetParam));
            return assetRpcService.billFeeUpdateAsset(updateAssetParam, UtaConstants.RPC_TIME_OUT);
        } catch (Exception e) {
            alarmNotifyService.alarm(TRANSFER_SERVICE_ERROR, anEnum.getCode());
            throw e;
        }
    }

    private BizTypeEnum getBillTypeEnum(ProfitTransferTypeEnum profitTransferTypeEnum, BigDecimal bigDecimal) {
        if (profitTransferTypeEnum == ProfitTransferTypeEnum.SYSTEM_UTA_SPOT_FEE) {
            if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                return BizTypeEnum.SPOT_BILL_MATCH_FEE_SYS_OUT;
            } else {
                return BizTypeEnum.SPOT_BILL_MATCH_FEE_SYS_IN;
            }
        } else if (profitTransferTypeEnum == ProfitTransferTypeEnum.SYSTEM_UTA_CONTRACT_FEE) {
            if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                return BizTypeEnum.CONTRACT_BILL_MATCH_FEE_SYS_OUT;
            } else {
                return BizTypeEnum.CONTRACT_BILL_MATCH_FEE_SYS_IN;
            }
        } else if (profitTransferTypeEnum == ProfitTransferTypeEnum.SYSTEM_UTA_SPOT_FEE_RESET) {
            if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                return BizTypeEnum.SPOT_BILL_MATCH_FEE_SYS_OUT;
            } else {
                return BizTypeEnum.SPOT_BILL_MATCH_FEE_SYS_IN;
            }
        } else if (profitTransferTypeEnum == ProfitTransferTypeEnum.SYSTEM_UTA_CONTRACT_FEE_RESET) {
            if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                return BizTypeEnum.CONTRACT_BILL_MATCH_FEE_SYS_OUT;
            } else {
                return BizTypeEnum.CONTRACT_BILL_MATCH_FEE_SYS_IN;
            }
        }
        throw new RuntimeException("getBillTypeEnum获取类型错误，请检查配置！" + profitTransferTypeEnum.name());
    }

    /**
     * 业务类型：0现货、杠杆  3合约
     *
     * @return
     */
    private Integer getBusinessType(ProfitTransferTypeEnum profitTransferTypeEnum) {
        if (profitTransferTypeEnum == ProfitTransferTypeEnum.SYSTEM_UTA_SPOT_FEE) {
            return BusinessTypeEnum.SPOT_TYPE.getCode();
        } else if (profitTransferTypeEnum == ProfitTransferTypeEnum.SYSTEM_UTA_CONTRACT_FEE) {
            return BusinessTypeEnum.MIX_CONTRACT_TYPE.getCode();
        } else if (profitTransferTypeEnum == ProfitTransferTypeEnum.SYSTEM_UTA_SPOT_FEE_RESET) {
            return BusinessTypeEnum.SPOT_TYPE.getCode();
        } else if (profitTransferTypeEnum == ProfitTransferTypeEnum.SYSTEM_UTA_CONTRACT_FEE_RESET) {
            return BusinessTypeEnum.MIX_CONTRACT_TYPE.getCode();
        }
        throw new RuntimeException("getBusinessType获取类型错误，请检查配置！" + profitTransferTypeEnum.name());
    }
}