//package com.upex.reconciliation.service.business.convert.remote;
//
//import com.upex.convert.facade.feign.inner.InnerConvertOrderFeignClient;
//import com.upex.kline.feign.inner.KlineInnerFeignClient;
//import org.springframework.cloud.openfeign.FeignClient;
//
//
//@FeignClient(
//        value = "${upex.feignClient.kline}",
//        contextId = "KLineInnerFeignClient",
//        path = "/inner/kline"
//)
//public interface KlineFeignService extends KlineInnerFeignClient {
//
//}
