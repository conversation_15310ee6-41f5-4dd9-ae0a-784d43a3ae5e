package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.google.common.util.concurrent.RateLimiter;
import com.alibaba.otter.canal.client.kafka.MessageDeserializer;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.alibaba.otter.canal.protocol.Message;
import com.upex.reconciliation.facade.enums.SQLTypeEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.business.convert.model.ConvertOrder;
import com.upex.reconciliation.service.business.convert.ReconOrderDataMatcher;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.model.config.ReconOrderConfig;
import com.upex.reconciliation.service.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description: kafka消费者消费消息, 手动同步提交offset
 **/

@Slf4j
public class ConvertOrderConsumerRunnable implements KafkaConsumerLifecycle {
    private List<KafkaConsumer<String, Message>> consumerList;
    private volatile boolean running = true;
    private Map<String, Object> consumerConfig;
    private String topic;
    private String groupId;
    private Integer partitionNum;
    private Map<Integer, KafkaConsumer<String, Message>> partitionConsumerMap = new HashMap<>();
    private Set<Integer> closeConsumerPatition = new HashSet<>();
    private Map<Integer, Long> partitionOffsetMap = new ConcurrentHashMap<>();
    private Map<Integer, RateLimiter> partitionRateLimiterMap = new HashMap<>();
    private ReconOrderDataMatcher orderDataMatcher;
    private ReconOrderConfig reconOrderConfig;
    private KafkaConsumerConfig kafkaConsumerConfig;

    public ConvertOrderConsumerRunnable(ReconciliationSpringContext context, String kafkaServers, KafkaConsumerConfig kafkaConsumerConfig) {
        this.topic = kafkaConsumerConfig.getTopicName();
        this.groupId =EnvUtil.getKafkaConsumerGroup(kafkaConsumerConfig.getConsumerGroupId());
        this.partitionNum = kafkaConsumerConfig.getPartitionNum();
        this.orderDataMatcher = context.getOrderDataMatcher();
        this.consumerList = new ArrayList<>();
        this.kafkaConsumerConfig = kafkaConsumerConfig;
        consumerConfig = new HashMap<String, Object>();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServers);
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MessageDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, kafkaConsumerConfig.getKafkaBatchSize());
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
        reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
    }

    @Override
    public void run() {
        // 初始化
        log.info("ConvertOrderConsumerRunnable consumerRunnables.run");
        init();
        log.info("ConvertOrderConsumerRunnable init finished");
        for (Map.Entry<Integer, KafkaConsumer<String, Message>> entry : partitionConsumerMap.entrySet()) {
            new Thread(() -> {
                try {
                    startConsume(entry.getKey(), entry.getValue());
                } catch (Exception e) {
                    log.error("ConvertOrderConsumerRunnable.startConsume error partition {}", entry.getKey(), e);
                }
            }, getThreadPrefixName() + entry.getKey()).start();
        }
    }

    private void init() {
        KafkaConsumer<String, Message> consumer = new KafkaConsumer<String, Message>(consumerConfig);
        try {
            consumer.subscribe(Arrays.asList(topic));
            Set<TopicPartition> assignment = new HashSet<>();
            while (assignment.isEmpty()) {
                consumer.poll(Duration.ofMillis(3000));
                assignment = consumer.assignment();
                log.info("ConvertOrderConsumerRunnable try consumer.assignment {} {}", topic, groupId);
            }
            // 获取当前消费组已提交的偏移量
            Map<TopicPartition, OffsetAndMetadata> committedOffsets = new HashMap<>();
            for (TopicPartition partition : assignment) {
                OffsetAndMetadata offsetAndMetadata = consumer.committed(partition);
                if (offsetAndMetadata != null) {
                    committedOffsets.put(partition, offsetAndMetadata);
                }
            }
            // 获取每个分区的最早和最新偏移量
            Map<TopicPartition, Long> beginningOffsets = consumer.beginningOffsets(assignment);
            Map<TopicPartition, Long> endOffsets = consumer.endOffsets(assignment);

            // 设置每个分区的消费位点
            for (TopicPartition partition : assignment) {
                long nextOffset;
                OffsetAndMetadata committedOffset = committedOffsets.get(partition);
                if (committedOffset != null) {
                    // 如果有已提交的偏移量，从下一个位置开始消费
                    nextOffset = committedOffset.offset();
                    log.info("ConvertOrderConsumerRunnable using committed offset for partition {}: {}",
                            partition.partition(), nextOffset);
                } else {
                    // 如果没有已提交的偏移量，从最早的位置开始消费
                    nextOffset = beginningOffsets.get(partition);
                    log.info("ConvertOrderConsumerRunnable no committed offset found for partition {}, starting from beginning: {}",
                            partition.partition(), nextOffset);
                }

                // 确保偏移量不超过最新位点
                long latestOffset = endOffsets.get(partition);
                if (nextOffset > latestOffset) {
                    nextOffset = latestOffset;
                    log.info("ConvertOrderConsumerRunnable adjusted offset for partition {} to latest: {}",
                            partition.partition(), nextOffset);
                }
                consumer.seek(partition, nextOffset);
            }
            if (reconOrderConfig.isOpenCommitSync()) {
                consumer.commitSync();
            }
            log.info("ConvertOrderConsumerRunnable finished setting offsets for topic: {} config:{} committedOffsets:{}",
                    topic, JSON.toJSONString(consumerConfig), committedOffsets);
        } catch (Exception e) {
            log.error("ConvertOrderConsumerRunnable.init set offset error ", e);
        } finally {
            consumer.close();
        }
        // 创建监听器，数量和partition数量保持一致
        for (int i = 0; i < partitionNum; i++) {
            KafkaConsumer<String, Message> currentConsumer = new KafkaConsumer<String, Message>(consumerConfig);
            List<TopicPartition> partitions = new ArrayList<>();
            partitions.add(new TopicPartition(topic, i));
            currentConsumer.assign(partitions);
            partitionRateLimiterMap.put(i, RateLimiter.create(kafkaConsumerConfig.getMsgRateLimit()));
            partitionConsumerMap.put(i, currentConsumer);
        }
    }

    private void startConsume(Integer partition, KafkaConsumer<String, Message> consumer) {
        log.info("ConvertOrderConsumerRunnable startConsume partition {}", partition);
        while (running) {
            try {
                // 从kafka集群中拉取消息
                ConsumerRecords<String, Message> consumerRecords = consumer.poll(3000);
                consumerRecords.forEach(new java.util.function.Consumer<ConsumerRecord<String, Message>>() {
                    @Override
                    public void accept(ConsumerRecord<String, Message> consumerRecord) {
                        partitionOffsetMap.put(partition, consumerRecord.offset());
                        List<FlatMessage> flatMessages = canalMessageDecode(consumerRecord.value());
                        handle(flatMessages);
                    }
                });
                consumer.commitSync();
                int messageCount = consumerRecords.count();
                if (messageCount > 0) {
                    partitionRateLimiterMap.get(partition).acquire(messageCount);
                    double rate = partitionRateLimiterMap.get(partition).getRate();
                    if (rate != kafkaConsumerConfig.getMsgRateLimit()) {
                        log.info("ConvertOrderConsumerRunnable recover rateLimiter partition:{} rate:{} newRate:{}",
                                partition, rate, kafkaConsumerConfig.getMsgRateLimit());
                        partitionRateLimiterMap.get(partition).setRate(kafkaConsumerConfig.getMsgRateLimit());
                    }
                }
            } catch (Exception e) {
                // todo 异常情况重置位点
                log.error("ConvertOrderConsumerRunnable startConsume error partition {} error:{}", partition, e.getMessage(), e);
            }
        }
        consumer.close();
        closeConsumerPatition.add(partition);
        log.info("ConvertOrderConsumerRunnable consumer.close success partition {}", partition);
    }


    public void handle(List<FlatMessage> flatMessages) {
        for (FlatMessage flatMessage : flatMessages) {
            List<ConvertOrder> convertOrders = buildConvertOrderList(flatMessage);
            convertOrders.forEach(convertOrder -> orderDataMatcher.saveMainOrderData(convertOrder));
        }
    }

    /**
     * 消息解析
     *
     * @param flatMessage
     * @return
     */
    private List<ConvertOrder> buildConvertOrderList(FlatMessage flatMessage) {

        String typeStr = flatMessage.getType();
        boolean isDdl = flatMessage.getIsDdl();
        if (isDdl) {
            return Collections.emptyList();
        }
        SQLTypeEnum sqlTypeEnum = SQLTypeEnum.convert(typeStr);
        if (null == sqlTypeEnum) {
            log.error("ConvertOrderConsumerRunnable unable to resolve sqlType:{}", typeStr);
            return Collections.emptyList();
        }
        List<Map<String, String>> dataList = flatMessage.getData();
        switch (sqlTypeEnum) {
            case DELETE:
                return Collections.emptyList();
            case UPDATE:
            case INSERT:
                return messageDecode(dataList);
        }
        return Collections.emptyList();
    }

    /**
     * 解码消息为ConvertOrder对象
     *
     * @param dataList
     * @return
     */
    private List<ConvertOrder> messageDecode(List<Map<String, String>> dataList) {
        List<ConvertOrder> convertOrders = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            log.info("ConvertOrderConsumerRunnable.buildConvertOrderList INSERT data:{}", JSON.toJSONString(dataList));
            for (Map<String, String> map : dataList) {
                // 盘口 普通用户
                String accountType = map.get("account_type");
                String quotedType = map.get("quoted_type");
                if (!"1".equals(accountType) || !"1".equals(quotedType)) {
                    continue;
                }
                List<String> filterStatus = reconOrderConfig.getOrderFilterStatus();
                if (!filterStatus.contains(map.get("status"))) {
                    continue;
                }
                // 设置ConvertOrder的属性
                ConvertOrder convertOrder = new ConvertOrder();
                convertOrder.setOrderId(map.get("id") == null ? null : Long.valueOf(map.get("id")));
                convertOrder.setClientOrderId(map.get("client_oid") == null ? null : Long.valueOf(map.get("client_oid")));
                convertOrder.setAccountId(map.get("account_id") == null ? null : Long.valueOf(map.get("account_id")));
                convertOrder.setAccountType(Integer.valueOf(accountType));
                convertOrder.setFromCoinId(map.get("from_coin_id") == null ? null : Integer.valueOf(map.get("from_coin_id")));
                convertOrder.setFromCoinCount(map.get("from_coin_count") == null ? null : new BigDecimal(map.get("from_coin_count")));
                convertOrder.setToCoinId(map.get("to_coin_id") == null ? null : Integer.valueOf(map.get("to_coin_id")));
                convertOrder.setToCoinCount(map.get("to_coin_count") == null ? null : new BigDecimal(map.get("to_coin_count")));
                convertOrder.setPc1(map.get("pc1") == null ? null : new BigDecimal(map.get("pc1")));
                convertOrder.setPt1(map.get("pt1") == null ? null : new BigDecimal(map.get("pt1")));
                convertOrder.setPc2(map.get("pc2") == null ? null : new BigDecimal(map.get("pc2")));
                convertOrder.setPt2(map.get("pt2") == null ? null : new BigDecimal(map.get("pt2")));
                convertOrder.setBaseSpread1(map.get("base_spread1") == null ? null : new BigDecimal(map.get("base_spread1")));
                convertOrder.setFloatSpread1(map.get("float_spread1") == null ? null : new BigDecimal(map.get("float_spread1")));
                convertOrder.setBaseSpread2(map.get("base_spread2") == null ? null : new BigDecimal(map.get("base_spread2")));
                convertOrder.setFloatSpread2(map.get("float_spread2") == null ? null : new BigDecimal(map.get("float_spread2")));
                convertOrder.setConvertFeeRate(map.get("convert_fee_rate") == null ? null : new BigDecimal(map.get("convert_fee_rate")));
                convertOrder.setConvertFeeRate2(map.get("convert_fee_rate2") == null ? null : new BigDecimal(map.get("convert_fee_rate2")));
                convertOrder.setQuotedType(Integer.valueOf(quotedType));
                convertOrder.setShowFlag(map.get("show_flag") == null ? null : Integer.valueOf(map.get("show_flag")));
                convertOrder.setEps(map.get("eps") == null ? null : Integer.valueOf(map.get("eps")));
                convertOrder.setStatus(map.get("status") == null ? null : Integer.valueOf(map.get("status")));
                convertOrder.setParams(map.get("params"));
                convertOrder.setCreateTime(map.get("create_time") == null ? null : DateUtil.getMillisecondDate((map.get("create_time"))));
                convertOrder.setUpdateTime(map.get("update_time") == null ? null : DateUtil.getMillisecondDate((map.get("update_time"))));
                convertOrder.setVersion(map.get("version") == null ? null : Long.valueOf(map.get("version")));
                convertOrders.add(convertOrder);
            }
        }
        return convertOrders;
    }



    @Override
    public void shutdown() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return closeConsumerPatition.size() != partitionNum;
    }

    @Override
    public String getThreadPrefixName() {
        return "kafka-consumer-profit-convert-order";
    }
}


