package com.upex.reconciliation.service.service;

import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.ProfitTypeEnum;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/9/25 14:35
 */
public interface ReconSystemAccountService {

    /**
     * 查询指定业务线手续费uid
     */
    Long querySystemFeeUserId(AccountTypeEnum accountTypeEnum, ProfitTypeEnum profitTypeEnum);

    /**
     * 查询换汇uid
     */
    Long queryExchangeUserId(AccountTypeEnum accountTypeEnum);

    /**
     * 查询指定业务线计息uid
     */
    Long queryInterestFeeUserId(AccountTypeEnum accountTypeEnum);

    /**
     * 获取合约盈亏/换汇id
     */
    Long queryHedgeAndWearIncomeUserId(AccountTypeEnum accountTypeEnum, ProfitTypeEnum profitTypeEnum, AssetsCheckConfig assetsCheckConfig);

    /**
     * 获取指定合约的ADL接收系统用户id
     *
     * @param symbolId
     * @return
     */
    Long queryContractAdlReceivedUserId(AccountTypeEnum accountTypeEnum, String symbolId);

    /**
     * 是否是合约adl接管系统账户
     *
     * @param symbolId
     * @return
     */
    Boolean isContractAdlReceivedUserId(AccountTypeEnum accountTypeEnum, String symbolId, Long bizUserId);

    /**
     * 根据系统账户类型获取系统账户id列表
     *
     * @param systemAccountType
     * @return
     */
    Set<Long> getSystemAccountIds(String systemAccountType);
}