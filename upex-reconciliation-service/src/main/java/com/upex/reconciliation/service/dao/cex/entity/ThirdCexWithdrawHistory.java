package com.upex.reconciliation.service.dao.cex.entity;

import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 提现记录实体类
 */
@Data
public class ThirdCexWithdrawHistory {

    private Long id;

    /**
     * 用户ID
     */
    private String cexUserId;

    private String cexEmail;

    /**
     * 交易所类型
     */
    private Integer cexType;

    /**
     * 提现ID
     */
    private String drawId;

    /**
     * 提现币种
     */
    private String coinName;

    /**
     * 提现数量
     */
    private BigDecimal amount;

    /**
     * 手续费1
     */
    private BigDecimal fee1;

    /**
     * 手续费2
     */
    private BigDecimal fee2;

    /**
     * 手续费1币种
     */
    private String fee1Coin;

    /**
     * 手续费2币种
     */
    private String fee2Coin;

    /**
     * 提现网络
     */
    private String network;

    /**
     * 提现地址（哈希加密）
     */
    private String address;

    /**
     * txId
     */
    private String txId;

    /**
     * bg用户ID
     */
    private String bgUserId;

    /**
     * 是否系统用户
     */
    private Integer isBgSys;

    /**
     * 提现状态
     */
    private Integer status;

    /**
     * 提现开始时间
     */
    private Date drawBeginTime;

    /**
     * 提现结束时间
     */
    private Date drawEndTime;

    /**
     * 转账类型，1站内转账，2站外转账
     */
    private Integer transferType;

    /**
     * 钱包类型
     */
    private Integer walletType;

    /**
     * 是否合规提现
     */
    private Integer isLegal;

    private Integer confirmNo;

    private String info;

    private Date checkSyncTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 版本号
     */
    private Long version;


}
