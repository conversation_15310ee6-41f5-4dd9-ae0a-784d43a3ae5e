package com.upex.reconciliation.service.business.impl;

import com.upex.bill.dto.params.BaseRequest;
import com.upex.bill.dto.results.AccountAssetsInfoResult ;
import com.upex.bill.dto.results.PageResponse;
import com.upex.bill.dto.results.UserInfoResult;
import com.upex.bill.facade.AccountAssetsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 默认实现
 */
@Slf4j
@Service
public class DefalutAccountAssetsServiceImpl implements AccountAssetsService {
    @Override
    public List<AccountAssetsInfoResult> queryUserAssets(List<Long> userIds, BaseRequest baseRequest) {
        return null;
    }

    @Override
    public List<AccountAssetsInfoResult> queryAllAssets(BaseRequest baseRequest) {
        return null;
    }

    @Override
    public PageResponse<AccountAssetsInfoResult> queryUserBillInfo(BaseRequest baseRequest, Integer pageNo, Integer pageSize) {
        return null;
    }

    @Override
    public PageResponse<UserInfoResult> queryUserInfo(BaseRequest baseRequest, Integer pageNo, Integer pageSize) {
        return null;
    }
}
