package com.upex.reconciliation.service.dao.cex.entity;

import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import lombok.Data;

import java.util.Date;

@Data
public class ThirdCexUser {

    /**
     * 后端自增ID
     */
    private Long id;

    /**
     * 交易所类型 @see CexTypeEnum
     */
    private Integer cexType;

    /**
     * 交易所注册邮箱
     */
    private String cexEmail;

    /**
     * 交易所注册用户ID
     */
    private String cexUserId;
    /*
     * 母级用户ID
     */
    private String parentUserId;

    private String userKyb;
    /**
     *  账户管理状态（1: 启用, 0: 禁用）
     */
    private Integer userManageStatus;
    /**
     * 交易所用户状态（1: 正常, 0: 禁用）
     */
    private Integer cexUserStatus;

    /**
     * 账户用途（1: 资金监控, 2: 搬砖）
     */
    private Integer useType;

    /**
     * 交易模式（1: 经典, 2: 统一）
     */
    private Integer tradeType;

    /**
     * 账户类型（0: 母账户, 1: 虚拟子账户, 2: 普通子账户, 3: 托管子账户）
     */
    private Integer userType;

    /**
     * API 状态（1: 生效中, 0: 失效）
     */
    private Integer apiKeyStatus;

    /**
     * 用户管理人邮箱
     */
    private String userManagerEmail;

    private String userManagerId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
