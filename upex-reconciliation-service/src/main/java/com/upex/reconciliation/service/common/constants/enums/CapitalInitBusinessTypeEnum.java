package com.upex.reconciliation.service.common.constants.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CapitalInitBusinessTypeEnum {
    SPOT_UN_PROFIT_TRANSFER(1, "现货手续费冻账数据期初值"),
    USD_REALIZED_TRANSFER(2, "USD币本位已实现期初值（盈亏+换汇动账已实现期初）"),
    CONTRACT_UN_PROFIT_TRANSFER(3, "合约待动账期初值"),
    CONTRACT_SYMBOL_REALIZED(4, "合约已实现期初值（盈亏对账已实现期初）"),
    ;

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CapitalInitBusinessTypeEnum toEnum(Integer code) {
        for (CapitalInitBusinessTypeEnum item : CapitalInitBusinessTypeEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }
}
