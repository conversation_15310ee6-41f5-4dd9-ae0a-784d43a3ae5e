package com.upex.reconciliation.service.business;

import com.upex.bill.dto.params.AssetsBaseRequest;
import com.upex.bill.dto.params.BasePageRequest;
import com.upex.bill.dto.results.AssetsInfoResult;
import com.upex.reconciliation.service.dao.entity.AssetsBillConfig;
import com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.param.UserAssetsCompareParam;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-11-19 14:51
 * @desc
 **/
public interface AssetsCheckBiz {


    /**
     * 获取业务系统的账单
     *
     * @param assetsBillConfig
     * @param assetsBaseRequest
     * @param basePageRequest
     * @return
     */
    List<AssetsInfoResult> getAssetsBills(AssetsCheckConfig assetsCheckConfig, AssetsBillConfig assetsBillConfig, AssetsBaseRequest assetsBaseRequest, BasePageRequest basePageRequest, GlobalBillConfig globalBillConfig);

    /**
     * 获取业务系统的账单
     *
     * @param assetsBillConfig
     * @param assetsBaseRequest
     * @param basePageRequest
     * @return
     */
    List<AssetsInfoResult> getAssetsBillsWithFee(AssetsCheckConfig assetsCheckConfig, AssetsBillConfig assetsBillConfig, AssetsBaseRequest assetsBaseRequest, BasePageRequest basePageRequest, GlobalBillConfig globalBillConfig, Map<Integer, BigDecimal> feeMaps);

    void initSyncInfoAndAssets(AssetsBillConfig assetsBillConfig, AssetsCheckConfig assetsCheckConfig,
                               AssetsBaseRequest assetsBaseRequest, BasePageRequest basePageRequest);

    boolean checkAssetsBills(List<AssetsInfoResult> list, AssetsBillConfig assetsBillConfig, AssetsCheckConfig assetsCheckConfig, AssetsBaseRequest assetsBaseRequest, Long timeInterval, GlobalBillConfig globalBillConfig, Map<Integer, BigDecimal> feeMaps);

    List<BillContractProfitTransfer> getBillContractProfitTransfers(AssetsBillConfig assetsBillConfig, AssetsCheckConfig assetsCheckConfig);

    List<BillContractProfitTransfer> compensateBillContractProfitTransferData(AssetsBillConfig assetsBillConfig, AssetsCheckConfig assetsCheckConfig);

    /**
     * 校验对账用户资产和三方用户资产
     *
     * @param param
     */
    void checkUserAssets(UserAssetsCompareParam param);
}
