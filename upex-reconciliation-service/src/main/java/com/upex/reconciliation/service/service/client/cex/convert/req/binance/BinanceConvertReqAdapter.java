package com.upex.reconciliation.service.service.client.cex.convert.req.binance;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.service.client.cex.convert.req.AbstractConvertReqAdpater;
import com.upex.reconciliation.service.service.client.cex.dto.req.binance.*;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.*;
import com.upex.reconciliation.service.service.client.cex.enmus.CexTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum;
import org.springframework.stereotype.Service;

@Service
public class BinanceConvertReqAdapter extends AbstractConvertReqAdpater<CommonReq, BinanceApiBaseReq> {

    @Override
    public BinanceApiBaseReq defaultConvertReq(CommonReq commonReq) {
        return new BinanceApiBaseReq();
    }

    @Override
    public BinanceApiBaseReq convertSubUserApiReq(CommonReq commonReq) {
        BinanceSubUserApiReq binanceSubUserApiReq = new BinanceSubUserApiReq();
        binanceSubUserApiReq.setEmail(commonReq.getCexEmail());
        return binanceSubUserApiReq;
    }

    @Override
    public BinanceApiBaseReq convertSubUserCoinContractAssetReq(CommonReq commonReq) {
        BinanceSubUserContractApiReq binanceSubUserContractApiReq = new BinanceSubUserContractApiReq();
        binanceSubUserContractApiReq.setEmail(commonReq.getCexEmail());
        binanceSubUserContractApiReq.setFuturesType(BinanceSubUserContractApiReq.FUTURES_TYPE_COIN);
        return binanceSubUserContractApiReq;
    }

    @Override
    public BinanceApiBaseReq convertSubUserUContractAssetReq(CommonReq commonReq) {
        BinanceSubUserContractApiReq binanceSubUserContractApiReq = new BinanceSubUserContractApiReq();
        binanceSubUserContractApiReq.setEmail(commonReq.getCexEmail());
        binanceSubUserContractApiReq.setFuturesType(BinanceSubUserContractApiReq.FUTURES_TYPE_U);
        return binanceSubUserContractApiReq;
    }

    @Override
    public BinanceApiBaseReq convertDepositeAddressApiReq(CommonReq commonReq) {
        if (commonReq instanceof DepositeAddressReq) {
            DepositeAddressReq depositeAddressReq = (DepositeAddressReq) commonReq;
            BinanceDepositeApiReq binanceDepositeApiReq = new BinanceDepositeApiReq();
            binanceDepositeApiReq.setCoin(depositeAddressReq.getCoinName());
            return binanceDepositeApiReq;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public BinanceApiBaseReq convertDepositeHistoryApiReq(CommonReq commonReq) {
        if (commonReq instanceof UserDepositeHistoryListReq) {
            UserDepositeHistoryListReq depositeAddressReq = (UserDepositeHistoryListReq) commonReq;
            BinanceUserDepositeHistoryReq binanceDepositeApiReq = new BinanceUserDepositeHistoryReq();
            binanceDepositeApiReq.setStartTime(depositeAddressReq.getStartTime().getTime());
            binanceDepositeApiReq.setEndTime(depositeAddressReq.getEndTime().getTime());
            return binanceDepositeApiReq;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public BinanceApiBaseReq convertWithdrawHistoryApiReq(CommonReq commonReq) {
        if (commonReq instanceof UserWithdrawHistoryListReq) {
            UserWithdrawHistoryListReq withdrawHistoryListReq = (UserWithdrawHistoryListReq) commonReq;
            BinanceUserWithdrawHistoryReq binanceWithdrawApiReq = new BinanceUserWithdrawHistoryReq();
            binanceWithdrawApiReq.setStartTime(withdrawHistoryListReq.getStartTime().getTime());
            binanceWithdrawApiReq.setEndTime(withdrawHistoryListReq.getEndTime().getTime());
            return binanceWithdrawApiReq;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public BinanceApiBaseReq convertPayTransferHistoryApiReq(CommonReq commonReq) {
        if (commonReq instanceof UserPayTransferHistoryListReq) {
            UserPayTransferHistoryListReq userPayTransferHistoryListReq = (UserPayTransferHistoryListReq) commonReq;
            BinancePayTransferHistoryReq binancePayTransferHistoryReq = new BinancePayTransferHistoryReq();
            binancePayTransferHistoryReq.setStartTime(userPayTransferHistoryListReq.getStartTime().getTime());
            binancePayTransferHistoryReq.setEndTime(userPayTransferHistoryListReq.getEndTime().getTime());
            return binancePayTransferHistoryReq;
        }else{
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public BinanceApiBaseReq convertParentSubTransferRecordApiReq(CommonReq commonReq) {
        if(commonReq instanceof ParentSubTransferHistoryReq){
            ParentSubTransferHistoryReq parentSubTransferHistoryReq = (ParentSubTransferHistoryReq) commonReq;
            BinanceParentSubTrasferReq binanceParentSubTrasferReq = new BinanceParentSubTrasferReq();
            binanceParentSubTrasferReq.setStartTime(parentSubTransferHistoryReq.getStartTime().getTime());
            binanceParentSubTrasferReq.setEndTime(parentSubTransferHistoryReq.getEndTime().getTime());
            binanceParentSubTrasferReq.setFromAccountType(parentSubTransferHistoryReq.getFromAccountType());
            binanceParentSubTrasferReq.setToAccountType(parentSubTransferHistoryReq.getToAccountType());
            return binanceParentSubTrasferReq;
        }else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public BinanceApiBaseReq convertUniversialTransferListApiReq(CommonReq commonReq) {
        if(commonReq instanceof UniversalTransferRecordReq){
            UniversalTransferRecordReq universalTransferRecordReq = (UniversalTransferRecordReq) commonReq;
            BinanceUniversalTransferReq binanceUniversalTransferReq = new BinanceUniversalTransferReq();
            binanceUniversalTransferReq.setStartTime(universalTransferRecordReq.getStartTime().getTime());
            binanceUniversalTransferReq.setEndTime(universalTransferRecordReq.getEndTime().getTime());
            binanceUniversalTransferReq.setType(universalTransferRecordReq.getType());
            return binanceUniversalTransferReq;
        }else {
            throw  new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public Integer cexType() {
        return CexTypeEnum.BINANCE.getType();
    }
}
