package com.upex.reconciliation.service.common.constants.enums;

import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.dto.AbstractLedgerMessage;
import com.upex.reconciliation.service.model.dto.SymbolCheckProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PropertySubTypeEnum {
    NONE("none", "无维度property", null),
    COIN("coin", "coin维度property", BillCoinProperty.class),
    COIN_START("coin_start", "coin维度property", null),
    COIN_END("coin_end", "coin维度property", null),
    COIN_TYPE("coin_type", "coin type维度property", BillCoinTypeProperty.class),
    SYMBOL_CHECK("symbol_check", "symbol check维度property", SymbolCheckProperty.class),
    PROFIT_COIN("profit_coin", "profit coin 盈亏换汇明细数据", BillContractProfitCoinDetail.class),
    PROFIT_SYMBOL("profit_symbol", "profit symbol 盈亏换汇明细数据", BillContractProfitSymbolDetail.class),
    TRANSFER_FEE("transfer_fee", "动账明细数据", BillTransferFeeCoinDetail.class),
    ;

    private String code;
    private String desc;
    private Class<? extends AbstractLedgerMessage> clazz;


    public static PropertySubTypeEnum toEnum(String code) {
        for (PropertySubTypeEnum item : PropertySubTypeEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }
}
