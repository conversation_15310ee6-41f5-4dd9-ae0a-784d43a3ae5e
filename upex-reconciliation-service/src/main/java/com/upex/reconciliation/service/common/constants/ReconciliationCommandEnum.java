package com.upex.reconciliation.service.common.constants;

import com.upex.mixcontract.common.framework.command.ICommandEnum;

public enum ReconciliationCommandEnum implements ICommandEnum {
    UNKNOWN(0, "未知命令"),
    BILL_CHANGE(1, "账单流水"),
    INIT_ASSET(2, "初始化资产"),
    INIT_ASSET_COMPLETE(3, "初始化资产完成"),
    TIME_SLICE(4, "时间片"),
    ;

    private final int id;
    private final String name;

    ReconciliationCommandEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }
}
