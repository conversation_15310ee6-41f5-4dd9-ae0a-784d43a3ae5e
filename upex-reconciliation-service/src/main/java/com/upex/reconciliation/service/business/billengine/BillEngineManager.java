package com.upex.reconciliation.service.business.billengine;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.FinancialVirtualAccountBizService;
import com.upex.reconciliation.service.business.RebootDataService;
import com.upex.reconciliation.service.business.ReconInnerTransferService;
import com.upex.reconciliation.service.business.module.impl.*;
import com.upex.reconciliation.service.common.RateLimiterManager;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.common.constants.enums.AssetsCheckTypeEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.consumer.kafka.*;
import com.upex.reconciliation.service.dao.entity.AssetsBillConfig;
import com.upex.reconciliation.service.dao.entity.BillConfig;
import com.upex.reconciliation.service.model.config.*;
import com.upex.reconciliation.service.model.dto.BillTimeSliceDTO;
import com.upex.reconciliation.service.utils.EnvUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Data
public class BillEngineManager {
    @Value("${upex.recon.kafka.namesrvAddr}")
    private String kafkaServerAddr;
    @Resource
    private ReconciliationSpringContext context;
    @Resource
    private RebootDataService rebootDataService;
    @Resource
    private RateLimiterManager rateLimiterManager;
    @Resource
    private AlarmNotifyService alarmNotifyService;
    @Resource
    private ReconInnerTransferService reconInnerTransferService;
    @Resource
    private FinancialVirtualAccountBizService financialVirtualAccountBizService;

    /***业务线kafka消费者***/
    private Map<String, KafkaConsumerLifecycle> consumerRunnableMap = new HashMap<>();
    /***业务线对账引擎***/
    private static final Map<Byte, BillLogicGroup> BILL_LOGIC_GROUP_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();
    /***总账业务线对账模块***/
    private static final Map<String, BillLedgerCheckModule> BILL_LEDGER_MODULE_CONCURENT_MAP = new ConcurrentHashMap<>();
    /***总账业务线对账模块***/
    private static final Map<Byte, BillUserProfitCheckModule> BILL_USER_PROFIT_MODULE_CONCURENT_MAP = new ConcurrentHashMap<>();
    /***交易订单对账模块****/
    private static final Map<String, BillOrderCheckModule> BILL_ORDER_CHECK_MODULE_MAP = new ConcurrentHashMap<>();

    public void start() {
        log.info("Starting ReconciliationApplication initialization.....");
        List<ApolloReconciliationBizConfig> apolloBizConfigs = ReconciliationApolloConfigUtils.getAllBillConfigByAccountType();
        for (ApolloReconciliationBizConfig apolloBizConfig : apolloBizConfigs) {
            if (EnvUtil.isLocalEnv()) {
                if (!EnvUtil.isLocalDebugByAccountType(apolloBizConfig.getAccountType())) {
                    continue;
                }
            } else if (!EnvUtil.isRunningAccountType(apolloBizConfig.getAccountType()) || !apolloBizConfig.isOpen()) {
                log.info("ReconKafkaOffsetResetService.start not running instance {} accountType:{}  apolloBizConfig:{}", EnvUtil.getServiceInstanceName(), apolloBizConfig.getAccountType(), JSONObject.toJSONString(apolloBizConfig));
                continue;
            }
            log.info("ReconciliationApplication.start accountType:{}  bizConfig:{}", apolloBizConfig.getAccountType(), JSONObject.toJSONString(apolloBizConfig));
            initBillEngine(apolloBizConfig);
        }

        // 初始化billUser增量同步
        ApolloKafkaConsumerConfig apolloKafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig();
        for (Map.Entry<String, KafkaConsumerConfig> entry : apolloKafkaConsumerConfig.getConsumerConfig().entrySet()) {
            if (!EnvUtil.isRunningKafkaConsumer(entry.getKey()) || !entry.getValue().getIsOpen()) {
                log.info("KafkaConsumer start not running instance {} consumerName:{}  kafkaConsumerConfig:{}", EnvUtil.getServiceInstanceName(), entry.getKey(), JSONObject.toJSONString(entry.getValue()));
                continue;
            }
            log.info("KafkaConsumer start consumerName:{}  kafkaConsumerConfig:{}", entry.getKey(), JSONObject.toJSONString(entry.getValue()));
            KafkaConsumerLifecycle runnable = null;
            if (KafkaTopicEnum.RECON_CAPITAL_ORDER_SYNC_TOPIC.getConsumerName().equals(entry.getKey())) {
                if (EnvUtil.isLocalEnv() && !EnvUtil.isLocalDebugCapitalOrder()) {
                    continue;
                }
                runnable = new CapitalOrderConsumerRunnable(context, kafkaServerAddr, entry.getValue(), reconInnerTransferService);
            } else if (KafkaTopicEnum.RECON_USER_INCREMENT_SYNC_TOPIC.getConsumerName().equals(entry.getKey())) {
                if (EnvUtil.isLocalEnv() && !EnvUtil.isLocalDebugByKafkaConsumer(entry.getKey())) {
                    continue;
                }
                runnable = new SyncBillUserConsumerRunnable(context, kafkaServerAddr, entry.getValue().getConsumerGroupId(), entry.getValue().getKafkaBatchSize());
            } else if (KafkaTopicEnum.RECON_SYNC_CENTER_USER_INFO_TOPIC.getConsumerName().equals(entry.getKey())) {
                if (EnvUtil.isLocalEnv() && !EnvUtil.isLocalDebugByKafkaConsumer(entry.getKey())) {
                    continue;
                }
                runnable = new SyncCenterUserConsumerRunnable(context, kafkaServerAddr, entry.getValue().getConsumerGroupId(), entry.getValue().getKafkaBatchSize());
            } else if (KafkaTopicEnum.RECON_SYNC_FINANCIAL_ACCOUNT_TOPIC.getConsumerName().equals(entry.getKey())) {
                if (EnvUtil.isLocalEnv() && !EnvUtil.isLocalDebugByKafkaConsumer(entry.getKey())) {
                    continue;
                }
                runnable = new FinancialAccountConsumerRunnable(context, kafkaServerAddr, entry.getValue(), financialVirtualAccountBizService);
            } else if (KafkaTopicEnum.RECON_USER_INITALL_SYNC_TOPIC.getConsumerName().equals(entry.getKey())) {
                if (EnvUtil.isLocalEnv() && !EnvUtil.isLocalDebugByKafkaConsumer(entry.getKey())) {
                    continue;
                }
                runnable = new SyncInitAllUserConsumerRunnable(context, kafkaServerAddr, entry.getValue().getConsumerGroupId(), entry.getValue().getKafkaBatchSize());
            } else if (KafkaTopicEnum.RECON_SYNC_USER_PROFIT_AMOUNT_TOPIC.getConsumerName().equals(entry.getKey())) {
                if (EnvUtil.isLocalEnv() && !EnvUtil.isLocalDebugByKafkaConsumer(entry.getKey())) {
                    continue;
                }
                runnable = new SyncDataProfitAmountConsumerRunnable(context, kafkaServerAddr, entry.getValue().getConsumerGroupId(), entry.getValue().getKafkaBatchSize());
            } else if (entry.getKey().startsWith(KafkaTopicEnum.RECON_SYNC_DEALT_RECORD_TOPIC.getConsumerName())) {
                if (EnvUtil.isLocalEnv() && !EnvUtil.isLocalDebugByKafkaConsumer(entry.getKey())) {
                    continue;
                }
                AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(Byte.valueOf(entry.getValue().getAccountType()));
                BillOrderCheckModule billOrderCheckModule = new BillOrderCheckModule(entry.getValue().getBusinessKey(), accountTypeEnum, context, this, entry.getKey());
                BILL_ORDER_CHECK_MODULE_MAP.put(entry.getValue().getBusinessKey(), billOrderCheckModule);
                runnable = new AccountDealtRecordConsumerRunnable(context, kafkaServerAddr, entry.getValue(), this, entry.getKey());
            } else if (entry.getKey().startsWith(KafkaTopicEnum.RECON_SYNC_BUSINESS_BILL_FLOW_TOPIC.getConsumerName())) {
                if (EnvUtil.isLocalEnv() && !EnvUtil.isLocalDebugByKafkaConsumer(entry.getKey())) {
                    continue;
                }
                AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(Byte.valueOf(entry.getValue().getAccountType()));
                BillUserProfitCheckModule billUserProfitCheckModule = new BillUserProfitCheckModule(accountTypeEnum, context, this);
                BILL_USER_PROFIT_MODULE_CONCURENT_MAP.put(accountTypeEnum.getCode(), billUserProfitCheckModule);
                runnable = new AccountProfitAssetsConsumerRunnable(context, kafkaServerAddr, entry.getValue(), this, entry.getKey());
            }else if (KafkaTopicEnum.RECON_ORDER_CONVERT_CONSUMER_TYPE.getConsumerName().equals(entry.getKey())) {
                // 闪兑订单
                if (EnvUtil.isLocalEnv() && !EnvUtil.isLocalDebugByKafkaConsumer(entry.getKey())) {
                    continue;
                }
                runnable = new ConvertOrderConsumerRunnable(context, kafkaServerAddr, entry.getValue());
            }
            else if (KafkaTopicEnum.RECON_ORDER_FLOW_CONSUMER_TYPE.getConsumerName().equals(entry.getKey())) {
                if (EnvUtil.isLocalEnv() && !EnvUtil.isLocalDebugByKafkaConsumer(entry.getKey())) {
                    continue;
                }
                // 现货订单流水
                runnable = new ConvertSpotConsumerRunnable(context, kafkaServerAddr, entry.getValue());
            }
            if (runnable == null) {
                log.info("KafkaConsumer start not find implementation instance {} consumerName:{}  kafkaConsumerConfig:{}", EnvUtil.getServiceInstanceName(), entry.getKey(), JSONObject.toJSONString(entry.getValue()));
                continue;
            }
            consumerRunnableMap.put(entry.getKey(), runnable);
            new Thread(runnable, runnable.getThreadPrefixName()).start();
        }

        // 初始化总账消费线程
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        List<AssetsCheckConfig> assetsCheckConfigs = Lists.newArrayList(globalBillConfig.getAssetsCheckConfig(AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getCode()));
        for (AssetsCheckConfig assetsCheckConfig : assetsCheckConfigs) {
            if (EnvUtil.isLocalEnv()) {
                if (!EnvUtil.isLocalDebugByAssetsCheckType(assetsCheckConfig.getAssetsCheckType())) {
                    continue;
                }
            } else if (!EnvUtil.isRunningLedgerAccountType(assetsCheckConfig.getAssetsCheckType(), assetsCheckConfig.getAssetsCheckParam()) || !assetsCheckConfig.isOpen()) {
                log.info("initBillLedgerEngine not running instance {} assetsCheckType:{}  assetsCheckConfig:{}", EnvUtil.getServiceInstanceName(), assetsCheckConfig.getAssetsCheckType(), JSONObject.toJSONString(assetsCheckConfig));
                continue;
            }
            log.info("initBillLedgerEngine.start assetsCheckType:{}  assetsCheckConfig:{}", assetsCheckConfig.getAssetsCheckType(), JSONObject.toJSONString(assetsCheckConfig));
            initBillLedgerEngine(globalBillConfig);
        }
        log.info("Finished ReconciliationApplication initialization.....");
    }

    /**
     * 初始化总账殷勤
     *
     * @param globalBillConfig
     */
    private void initBillLedgerEngine(GlobalBillConfig globalBillConfig) {
        // 初始化总账
        List<AssetsCheckConfig> assetsCheckList = globalBillConfig.getAssetsCheckList();
        for (AssetsCheckConfig assetsCheckConfig : assetsCheckList) {
            if (!assetsCheckConfig.isOpen()) {
                continue;
            }
            AssetsCheckTypeEnum assetsCheckTypeEnum = AssetsCheckTypeEnum.toEnum(assetsCheckConfig.getAssetsCheckType());
            BillLedgerCheckModule billLedgerCheckModule = new BillLedgerCheckModule(assetsCheckTypeEnum, context, this);
            billLedgerCheckModule.rebootLoadData();
            BILL_LEDGER_MODULE_CONCURENT_MAP.put(assetsCheckConfig.getAssetsCheckType(), billLedgerCheckModule);
        }

        // 初始化总账消费线程
        BillLedgerCheckModule billLedgerCheckModule = BILL_LEDGER_MODULE_CONCURENT_MAP.get(AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getCode());
        AssetsBillConfig assetsBillConfig = billLedgerCheckModule.getLastLedgerSaveTimeSliceDTO().getAssetsBillConfig();
        AssetsKafkaConfig assetsKafkaConfig = globalBillConfig.getAssetsKafkaConfig();
        Map<String, AssetsKafkaConsumerConfig> kafkaConsumerConfigMap = assetsKafkaConfig.getKafkaConsumerConfig();
        for (Map.Entry<String, AssetsKafkaConsumerConfig> kafkaConsumerConfigEntry : kafkaConsumerConfigMap.entrySet()) {
            AssetsKafkaConsumerConfig kafkaConsumerConfig = kafkaConsumerConfigEntry.getValue();
            LedgerConsumerRunnable ledgerConsumerRunnable = new LedgerConsumerRunnable(this, kafkaServerAddr, kafkaConsumerConfig, assetsBillConfig, kafkaConsumerConfigEntry.getKey(), context);
            consumerRunnableMap.put(kafkaConsumerConfigEntry.getKey(), ledgerConsumerRunnable);
            new Thread(ledgerConsumerRunnable, ledgerConsumerRunnable.getThreadPrefixName()).start();
        }
    }

    public void stop() {
        closeAllKafkaConsumer();
    }

    public void restartBillEngine(Byte accountType) {
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        ReconKafkaOpsConfig reconKafkaOpsConfig = apolloBizConfig.getReconKafkaOpsConfig();
        if (apolloBizConfig == null || !apolloBizConfig.isOpen() || !EnvUtil.isRunningAccountType(apolloBizConfig.getAccountType())) {
            log.info("restartByAccountType error accountType={}", accountType);
        }
        BillLogicGroup billLogicGroup = getBillLogicGroup(accountType);
        KafkaConsumerLifecycle kafkaConsumerLifecycle = consumerRunnableMap.get(accountType.toString());
        if (billLogicGroup == null || kafkaConsumerLifecycle == null) {
            log.info("restartByAccountType error billLogicGroup or consumerWithOffsetRunnable is null accountType={}", accountType);
        }
        // 关闭kafka
        kafkaConsumerLifecycle.shutdown();
        while (kafkaConsumerLifecycle.isRunning()) {
            try {
                log.info("restartByAccountType consumerWithOffsetRunnable shutdown thread sleep accountType={}...", accountType);
                Thread.sleep(1000);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        // 关闭引擎线程
        billLogicGroup.getEngine().stop();
        // 存盘任务是否完成
        BillTimeSliceCheckModule timeSliceModule = (BillTimeSliceCheckModule) billLogicGroup.getTimeSliceModule();
        while (!timeSliceModule.getSaveTimeSliceDTOMap().isEmpty()) {
            try {
                log.info("restartByAccountType  saveTimeSliceDTOMap thread sleep accountType={} {}...", accountType, timeSliceModule.getSaveTimeSliceDTOMap());
                Thread.sleep(1000);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        // 清除用户数据
        BillUserCheckModule billUserCheckModule = (BillUserCheckModule) billLogicGroup.getUserCheckModule();
        billUserCheckModule.getUserPropertyMap().invalidateAll();
        timeSliceModule.getStartTimeSliceDTOMap().clear();
        try {
            log.info("restartByAccountType start thread sleep {}... ", accountType);
            Thread.sleep(3000);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 重新初始化引擎
        initBillEngine(apolloBizConfig);
    }

    private void initBillEngine(ApolloReconciliationBizConfig apolloBizConfig) {
        AbstractBillEngine billEngine = new BillEngine(context, AccountTypeEnum.toEnum(apolloBizConfig.getAccountType()));
        billEngine.start(null, () -> {
        });
        BillLogicGroup billLogicGroup = billEngine.getMainLogicGroup();
        BILL_LOGIC_GROUP_CONCURRENT_HASH_MAP.put(apolloBizConfig.getAccountType(), billLogicGroup);
        if (apolloBizConfig.isToLoadMemoryData()) {
            log.info("ReconciliationApplication.start accountType:{} LoadMemoryData CheckOkTime:{}", apolloBizConfig.getAccountType(), apolloBizConfig.getCheckOkTime());
            rebootDataService.baseRebootDataRestore(apolloBizConfig.getCheckOkTime(), apolloBizConfig.getAccountType());
        }
        BillTimeSliceCheckModule timeSliceCheckModule = (BillTimeSliceCheckModule) billLogicGroup.getTimeSliceModule();
        BillTimeSliceDTO lastBillTimeSliceDTO = timeSliceCheckModule.getLastBillTimeSliceDTO();
        if (lastBillTimeSliceDTO == null) {
            log.error("ReconKafaOffsetResetService.start lastBillTimeSliceDTO is null accountType:{}", apolloBizConfig.getAccountType());
            return;
        }
        BillConfig billConfig = lastBillTimeSliceDTO.getBillConfig();
        AccountAssetsConsumerRunnable runnable = new AccountAssetsConsumerRunnable(apolloBizConfig.getAccountType(), apolloBizConfig.getReconKafkaOpsConfig(), context, kafkaServerAddr, billConfig, this);
        consumerRunnableMap.put(apolloBizConfig.getAccountType().toString(), runnable);
        new Thread(runnable, runnable.getThreadPrefixName()).start();
        log.info("ReconciliationApplication.initBillEngine finished accountType:{} ", apolloBizConfig.getAccountType());
    }

    public BillLedgerCheckModule getBillLedgerCheckModule(String assetsCheckType) {
        return BILL_LEDGER_MODULE_CONCURENT_MAP.get(assetsCheckType);
    }

    public BillLogicGroup getBillLogicGroup(Byte accountType) {
        return BILL_LOGIC_GROUP_CONCURRENT_HASH_MAP.get(accountType);
    }

    public BillUserCheckModule getUserCheckModule(Byte accountType) {
        BillLogicGroup billLogicGroup = BILL_LOGIC_GROUP_CONCURRENT_HASH_MAP.get(accountType);
        return (BillUserCheckModule) billLogicGroup.getUserCheckModule();
    }

    public BillUserProfitCheckModule getUserProfitCheckModule(Byte accountType) {
        return BILL_USER_PROFIT_MODULE_CONCURENT_MAP.get(accountType);
    }

    public void closeAllKafkaConsumer() {
        for (KafkaConsumerLifecycle consumer : consumerRunnableMap.values()) {
            try {
                consumer.shutdown();
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    /**
     * 获取leverSpot对账时间
     *
     * @return
     */
    public Date getLeverSpotCheckTime() {
        BillLedgerCheckModule billLedgerCheckModule = BILL_LEDGER_MODULE_CONCURENT_MAP.get(AssetsCheckTypeEnum.LEVER_SPOT_TOTAL_ASSETS.getCode());
        return billLedgerCheckModule != null ? billLedgerCheckModule.getLastCheckOkTime() : null;
    }

    /**
     * 获取leverspot开启状态
     *
     * @return
     */
    public Boolean getLeverSpotOpen() {
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        AssetsCheckConfig assetsCheckConfig = globalBillConfig.getAssetsCheckConfig(AssetsCheckTypeEnum.LEVER_SPOT_TOTAL_ASSETS.getCode());
        return assetsCheckConfig != null ? assetsCheckConfig.isOpen() : false;
    }

    /**
     * 获取大总账耗时
     *
     * @return
     */
    public Date getInternalCheckTime() {
        BillLedgerCheckModule billLedgerCheckModule = BILL_LEDGER_MODULE_CONCURENT_MAP.get(AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getCode());
        return billLedgerCheckModule != null ? billLedgerCheckModule.getLastCheckOkTime() : null;
    }

    public BillOrderCheckModule getBillOrderCheckModule(String businessKey) {
        return BILL_ORDER_CHECK_MODULE_MAP.get(businessKey);
    }
}
