package com.upex.reconciliation.service.business.cex;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.CexAssetConfig;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserAssetHistory;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.service.ThirdCexUserAssetHistoryService;
import com.upex.reconciliation.service.service.ThirdCexUserService;
import com.upex.reconciliation.service.service.client.cex.CexConstants;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.UserAssetListReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.*;
import com.upex.reconciliation.service.service.client.cex.enmus.CexAssetHistoryTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.CexUserTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ThirdAssetType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CexSubUserAssetHistoryBizService extends AbstractCexAssetSyncHistory implements ICexAssetSyncHistory {

    @Resource
    ThirdCexUserAssetHistoryService thirdCexUserAssetHistoryService;

    @Resource
    CommonService commonService;

    @Resource
    ThirdCexUserService thirdCexUserService;

    @Resource
    CexApiAggregateBizService cexApiAggregateBizService;

    @Resource
    BillDbHelper billDbHelper;


    public CommonRes querySubUserAssetAndSync(UserAssetListReq userAssetListReq) {
        CommonRes<CommonTotalAssetRes> commonRes = serialQuerySubUserCoinAssetFromApi(userAssetListReq);
        return commonRes;
    }

    CommonRes<CommonTotalAssetRes> serialQuerySubUserCoinAssetFromApi(UserAssetListReq commonReq) {
        ThirdCexUser user = thirdCexUserService.selectByCexTypeAndUserId(commonReq.getCexType(), commonReq.getCexUserId());
        if (user == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_NOT_EXISTS);
        }
        if (user.getParentUserId() == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_NO_PARENT_USER);
        }
        commonReq.setCexEmail(user.getCexEmail());
        CommonTotalAssetRes commonTotalAssetRes = new CommonTotalAssetRes();
        for (ThirdAssetType thirdAssetType : ThirdAssetType.subUserAssetTypeEnums()) {
            if (user.getCexEmail().contains(CexConstants.VIRTUAL_EMAIL) && (thirdAssetType.equals(ThirdAssetType.SUB_UCONTRACT) || thirdAssetType.equals(ThirdAssetType.SUB_COIN_CONTRACT))) {
                commonTotalAssetRes.addThirdAssetTypeList(thirdAssetType, new CommonCoinAssetRes());
                continue;
            }
            CommonRes<CommonCoinAssetRes> coinAssetRes = cexApiAggregateBizService.queryUserAssetByAssetType(commonReq, thirdAssetType);
            if (coinAssetRes.getSuccess() && coinAssetRes.getData() != null) {
                commonTotalAssetRes.addThirdAssetTypeList(thirdAssetType, coinAssetRes.getData());
            }
        }

        commonTotalAssetRes.setParentUserId(user.getParentUserId());
        return CommonRes.getSucApiBaseRes(commonTotalAssetRes);
    }

    public CommonRes<CommonSubUserTotalAssetRes> querySubUserCoinAsset(UserAssetListReq userAssetListReq) {
        ThirdCexUser user = thirdCexUserService.selectByCexTypeAndUserId(userAssetListReq.getCexType(), userAssetListReq.getCexUserId());
        if (user == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_NOT_EXISTS);
        }
        if (user.getParentUserId() == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_NOT_SUB_USER);
        }
        List<ThirdCexUserAssetHistory> totalUserAsset = thirdCexUserAssetHistoryService.selectSubUserTotalAsset(userAssetListReq.getCexUserId(), userAssetListReq.getCexType());
        CommonRes<CommonTotalAssetRes> commonRes = cexApiAggregateBizService.buildCommonTotalAssetRes(totalUserAsset, ThirdAssetType.subUserAssetTypes(), userAssetListReq);
        CommonSubUserTotalAssetRes commonSubUserTotalAssetRes = new CommonSubUserTotalAssetRes();
        commonSubUserTotalAssetRes.setTotalAmount(commonRes.getData().getTotalAmount());
        commonSubUserTotalAssetRes.setThirdAssetTypeAmounts(commonRes.getData().getThirdAssetTypeAmounts());
        commonSubUserTotalAssetRes.setParentUserId(user.getParentUserId());
        List<ThirdCexUser> subUsers = thirdCexUserService.selectSubUserByCexTypeAndParentUserId(userAssetListReq.getCexType(), user.getParentUserId());
        commonSubUserTotalAssetRes.setSubUsers(subUsers.stream()
                .map(userVo ->
                        new ThirdCexUserInnerRes(userVo.getCexEmail(), userVo.getCexUserId())
                ).collect(Collectors.toList()));
        CommonRes<CommonSubUserTotalAssetRes> subUserTotalAssetResCommonRes = CommonRes.getSucApiBaseRes(commonSubUserTotalAssetRes);
        return subUserTotalAssetResCommonRes;
    }


    @Override
    public void syncAssetHistory(CexAssetConfig cexAssetConfig, ThirdCexUserConfig userConfig, Date startTime, Date endTime, Date checkSyncTime) {
        ThirdCexUser user = thirdCexUserService.selectByCexTypeAndUserId(userConfig.getCexType(), userConfig.getCexUserId());
        if (user == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_NOT_EXISTS);
        }
        if (user.getParentUserId() == null) {
            //当前用户是母用户
            List<ThirdCexUser> subUsers = thirdCexUserService.selectSubUserByCexTypeAndParentUserId(userConfig.getCexType(), user.getCexUserId());
            List<CommonRes> resList = new ArrayList<>();
            for (ThirdCexUser subUser : subUsers) {
                UserAssetListReq userAssetListReq = new UserAssetListReq(userConfig.getCexType(), subUser.getCexUserId(), userConfig.getApiKey(), userConfig.getApiKeyPrivate(), checkSyncTime);
                CommonRes commonRes = querySubUserAssetAndSync(userAssetListReq);
                resList.add(commonRes);
            }
            billDbHelper.doDbOpInReconMasterTransaction(()->{
                if(CollectionUtils.isNotEmpty(resList)){
                    for(CommonRes commonRes:resList){
                        cexApiAggregateBizService.syncTotalAsset(commonRes, userConfig.getCexType(), userConfig.getCexUserId(), checkSyncTime);
                        log.info("SyncTotalAsset,userId:{},cexType:{},checkSyncTime:{}", user.getCexUserId(), user.getCexType(), checkSyncTime);
                    }
                    saveModAssetConfig(cexAssetConfig, CexAssetHistoryTypeEnum.SUB_BALANCE, userConfig, checkSyncTime);
                }
                return null;
            });
        } else {
            throw new ApiException(ReconCexExceptionEnum.USER_NO_PARENT_USER);
        }
    }


    @Override
    public CexAssetHistoryTypeEnum getAssetHistoryType() {
        return CexAssetHistoryTypeEnum.SUB_BALANCE;
    }


}
