package com.upex.reconciliation.service.service;


import com.upex.reconciliation.service.dao.entity.BillCoinUserAssets;
import com.upex.reconciliation.service.model.dto.BillCoinUserAssetsParam;

import java.util.List;

/**
 * 用户资产持仓参数业务处理
 * <AUTHOR>
 */
public interface BillCoinUserAssetsParamService {
    /**
     * 获取[用户资产持仓参数]列表
     *
     * @param billCoinUserAssetsList
     * @param accountType
     * @param accountParam
     */
    List<BillCoinUserAssetsParam> listAssetsParamObjects(List<BillCoinUserAssets> billCoinUserAssetsList,
                                                         Byte accountType,
                                                         String accountParam);


    /**
     * param属性进行赋值操作
     * @param assets
     */
    void setBillCoinUserAssetsParamProperty(BillCoinUserAssets assets);
    void setBillCoinUserAssetsParamProperty(List<BillCoinUserAssets> assetsList);

    /**
     * 批量保存用户资产持仓信息
     * @param list 用户资产持仓信息列表
     * @param accountType 业务类型
     * @param accountParam 业务类型参数
     */
    void batchInsert(List<BillCoinUserAssetsParam> list, Byte accountType, String accountParam);

    /**
     * 查询持仓信息
     * @param assetsIds
     * @param accountType
     * @param accountParam
     * @return
     */
    List<BillCoinUserAssetsParam> queryAssetsParamByAssetsIdList(List<Long> assetsIds,Byte accountType,String accountParam);
}
