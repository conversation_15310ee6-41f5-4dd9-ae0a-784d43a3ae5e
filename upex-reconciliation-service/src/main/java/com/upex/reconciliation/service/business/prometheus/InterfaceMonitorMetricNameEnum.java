package com.upex.reconciliation.service.business.prometheus;

public enum InterfaceMonitorMetricNameEnum {


    // 提笔检测接口
    INTERFACE_SUMMARY_SELECT_CHECK_FOR_THE_RESULTS("interface_summary_selectCheckForTheResults", "提笔检测接口总耗时", true),


    selectCheckForTheResults_verifyUserIsExist("selectCheckForTheResults_verifyUserIsExist", "检查用户是否存在", true),

    selectCheckForTheResults_getCheckWithdrawWhiteListUserIds("selectCheckForTheResults_getCheckWithdrawWhiteListUserIds", "白名单校验", true),
    selectCheckForTheResults_getCheckUserIsExistWhiteList("selectCheckForTheResults_getCheckUserIsExistWhiteList", "用户存在白名单校验", true),


    selectCheckForTheResults_getMinCheckOkBillConfig("selectCheckForTheResults_getMinCheckOkBillConfig", "最小check_ok_time校验", true),


    selectCheckForTheResults_isUserWithdrawalAlarmCheck("selectCheckForTheResults_isUserWithdrawalAlarmCheck", "用户禁止提笔校验", true),

    selectCheckForTheResults_isUserRealTimeNegativeAssetsAlarmCheck("selectCheckForTheResults_isUserRealTimeNegativeAssetsAlarmCheck", "负资产校验", true),

    selectCheckForTheResults_isUserNegativeAssetsAlarmCheck("selectCheckForTheResults_isUserNegativeAssetsAlarmCheck", "负资产禁提币校验", true),


    selectCheckForTheResults_getDifferenceTime("selectCheckForTheResults_getDifferenceTime", "最小业务线对账配置对账成功时间，允许业务线对账延迟时间差校验", true),


    selectCheckForTheResults_isGlobalAlarmCheck("selectCheckForTheResults_isGlobalAlarmCheck", "总账配置对账成功时间，允许总账延迟时间差校验", true),

    selectCheckForTheResults_isRealAssetsAlarmCheck("selectCheckForTheResults_isRealAssetsAlarmCheck", "对账差额校验", true),

    selectCheckForTheResults_isCapitalAssetsCheck("selectCheckForTheResults_isCapitalAssetsCheck", "资金对账校验", true),


    // 反算总耗时
    selectCheckForTheResults_backCalculationAllSammary("selectCheckForTheResults_backCalculationAllSammary", "单次反算校验-总耗时", true),

    selectCheckForTheResults_checkProfitAccount("selectCheckForTheResults_checkProfitAccount", "用户盈利检查", true),
    selectCheckForTheResults_queryUserSingleRealTimeAssets("selectCheckForTheResults_queryUserSingleRealTimeAssets", "查询用户实时资产", true),
    selectCheckForTheResults_obtainDataWithDiffAccountType("selectCheckForTheResults_obtainDataWithDiffAccountType", "用户负值资产查询", true),
    selectCheckForTheResults_listUserAssetsBySnapShotTime("selectCheckForTheResults_listUserAssetsBySnapShotTime", "用户快照资产查询", true),

    ;

    private String name;

    private String desc;

    private boolean independent;

    InterfaceMonitorMetricNameEnum(String name, String desc, boolean ifIndependent) {
        this.name = name;
        this.desc = desc;
        this.independent = ifIndependent;
    }


    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static InterfaceMonitorMetricNameEnum toEnum(String name) {
        for (InterfaceMonitorMetricNameEnum item : InterfaceMonitorMetricNameEnum.values()) {
            if (item.name.equals(name)) {
                return item;
            }
        }
        return null;
    }

    public boolean isIndependent() {
        return independent;
    }
}
