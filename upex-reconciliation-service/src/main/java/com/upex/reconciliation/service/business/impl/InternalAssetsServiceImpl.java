package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.upex.bill.dto.enums.AssetsCheckTypeEnum;
import com.upex.bill.dto.params.AssetsBaseRequest;
import com.upex.bill.dto.results.AssetsInfoResult;
import com.upex.bill.dto.results.PageResponse;
import com.upex.bill.dto.results.SyncInfoResult;
import com.upex.common.exception.BusinessException;
import com.upex.contract.process.dto.enums.ExceptionEnum;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AssetsService;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.CapitalInitBusinessTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.LogLevelEnum;
import com.upex.reconciliation.service.dao.entity.BillAllConfig;
import com.upex.reconciliation.service.dao.entity.BillCapitalInitProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.dto.AbstractProperty;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.utils.BeanCopierUtil;
import com.upex.reconciliation.service.utils.BizLogUtils;
import com.upex.reconciliation.service.utils.GroupByKeyUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-11-18 15:44
 * @desc
 **/
@Slf4j
@Service("internalAssetsService")
public class InternalAssetsServiceImpl implements AssetsService {
    @Resource
    private BillCoinPropertyService billCoinPropertyService;
    @Resource
    private BillConfigService billConfigService;
    @Resource
    private BillCoinTypePropertyService billCoinTypePropertyService;
    @Resource
    private BillAllConfigService billAllConfigService;
    @Resource
    private BillCapitalInitPropertyService billCapitalInitPropertyService;

    @Override
    public List<AssetsInfoResult> queryAssets(List<Long> syncIds, AssetsBaseRequest request) {
        List<AssetsInfoResult> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(syncIds)) {
            for (Long syncId : syncIds) {
                BillAllConfig billAllConfig = billAllConfigService.selectById(syncId);
                Date endTime = new Date(request.getEndTime());
                List<BillCoinProperty> billCoinPropertyList = billCoinPropertyService.selectAssetsByEndTime(endTime, billAllConfig.getAccountType(), billAllConfig.getAccountParam());
                if (CollectionUtils.isNotEmpty(billCoinPropertyList)) {
                    for (BillCoinProperty billCoinProperty : billCoinPropertyList) {
                        cleanOtherProperty(billAllConfig.getAccountType(), billCoinProperty);
                        AssetsInfoResult assetsInfoResult = BeanCopierUtil.copyProperties(billCoinProperty, AssetsInfoResult.class);
                        assetsInfoResult.setSyncId(syncId);
                        resultList.add(assetsInfoResult);
                    }
                }
            }
        }
        return resultList;
    }


    @Override
    public PageResponse<SyncInfoResult> querySyncInfo(AssetsCheckConfig assetsCheckConfig, AssetsBaseRequest request, Integer pageNo, Integer pageSize) {
        // 验数校证
        this.checkTotalAssetsParam(request);

        List<SyncInfoResult> resultList = new ArrayList<>();
        List<String> subSystemList = assetsCheckConfig.getSubSystemList();
        List<BillAllConfig> billAllConfigs = billAllConfigService.listAllBillConfigs();
        Long maxId = 0L;
        if (CollectionUtils.isNotEmpty(billAllConfigs)) {
            List<BillAllConfig> newBillConfigList = billAllConfigs.stream()
                    .filter(billConfig -> billConfig.getId() > request.getMaxId() && subSystemList.contains(billConfig.getUniqKey()))
                    .collect(Collectors.toList());
            for (BillAllConfig billAllConfig : newBillConfigList) {
                SyncInfoResult syncInfoResult = new SyncInfoResult();
                Long configId = billAllConfig.getId();
                syncInfoResult.setSyncId(configId);
                syncInfoResult.setCreateTime(billAllConfig.getCreateTime());
                resultList.add(syncInfoResult);
                if (configId > maxId) {
                    maxId = configId;
                }
            }
        }
        PageResponse pageResponse = PageResponse.makePageResponse(new Page(), resultList);
        pageResponse.setMaxId(maxId);
        return pageResponse;
    }


    @Override
    public PageResponse<AssetsInfoResult> queryBillInfo(AssetsCheckConfig assetsCheckConfig, AssetsBaseRequest request, Integer pageNo, Integer pageSize, Date nextCheckOkTime, GlobalBillConfig globalBillConfig, Map<Integer, BigDecimal> feeMaps) {

        List<AssetsInfoResult> resultList = new ArrayList<>();
        // 验数校证
        this.checkTotalAssetsParam(request);
        List<String> subSystemList = assetsCheckConfig.getSubSystemList();
        List<BillAllConfig> billAllConfigs = billAllConfigService.listAllBillConfigs();
        //将apollo获取的子系统列表和billconfig查询对比，得到一一对应的list--最终的子系统列表
        List<BillAllConfig> allAssetsBillConfig = billAllConfigs.stream().filter(billConfig -> subSystemList.contains(billConfig.getUniqKey())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(allAssetsBillConfig)) {
            resultList = queryAssetsInfoResultList(request, allAssetsBillConfig, globalBillConfig, feeMaps);
        }
        return PageResponse.makePageResponse(new Page(), resultList);
    }


    /**
     * 总账的流水查询
     *
     * @param request
     * @param allAssetsBillConfig
     * @param globalBillConfig
     * @return
     */
    private List<AssetsInfoResult> queryAssetsInfoResultList(AssetsBaseRequest request, List<BillAllConfig> allAssetsBillConfig, GlobalBillConfig globalBillConfig, Map<Integer, BigDecimal> feeMaps) {
        Map<Integer, BigDecimal> receiveFeeMaps = new HashMap<>();
        Map<Integer, BigDecimal> totalFeeMaps = new HashMap<>();
        List<AssetsInfoResult> resultList = new ArrayList<>();
        for (BillAllConfig billAllConfig : allAssetsBillConfig) {
            //获取appolo的bill config 子系统配置详情列表
            //遍历全局配置的子系统列表--包含混合合约后缀，即获取包含混合合约的总数据
            List<BillCoinTypeProperty> billCoinTypeProperties = billCoinTypePropertyService.listBillBetweenTime(new Date(request.getBeginTime()), new Date(request.getEndTime()), billAllConfig.getAccountType(), billAllConfig.getAccountParam());
            ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(billAllConfig.getAccountType());
            if (CollectionUtils.isNotEmpty(billCoinTypeProperties)) {
                for (BillCoinTypeProperty billCoinTypeProperty : billCoinTypeProperties) {
                    if (billCoinTypeProperty.getBizType().equals(apolloBillConfig.getProfitTransferFeeCoinType()) ||
                            billCoinTypeProperty.getBizType().equals(apolloBillConfig.getProfitTransferFeeRecycleCoinType())) {
                        BigDecimal receiveFee = receiveFeeMaps.getOrDefault(billCoinTypeProperty.getCoinId(), BigDecimal.ZERO);
                        receiveFeeMaps.put(billCoinTypeProperty.getCoinId(), receiveFee.add(billCoinTypeProperty.getProp1()));
                    }
                    //无任何prop 变化、continue
                    if (ifNoChange(billCoinTypeProperty)) {
                        continue;
                    }
                    AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(billAllConfig.getAccountType());
                    boolean noUse = accountTypeEnum.isContract()
                            && !(apolloBillConfig.getTransferIn().contains(billCoinTypeProperty.getBizType())
                            || apolloBillConfig.getTransferOut().contains(billCoinTypeProperty.getBizType()));
                    if (noUse) {
                        //合约未实现盈亏会导致对账不平
                        continue;
                    }
                    AssetsInfoResult assetsInfoResult = new AssetsInfoResult();
                    assetsInfoResult.setCoinId(billCoinTypeProperty.getCoinId());
                    assetsInfoResult.setSyncId(billAllConfig.getId());
                    String bizType = accountTypeEnum.getBizTypePrefix() + BillConstants.SEPARATOR + billCoinTypeProperty.getBizType();

                    assetsInfoResult.setBizType(bizType);
                    if (accountTypeEnum.isLever()) {
                        assetsInfoResult.setProp1(billCoinTypeProperty.getChangeProp1());
                        assetsInfoResult.setProp2(billCoinTypeProperty.getChangeProp2());
                    } else if (accountTypeEnum.isContract()) {
                        //混合合约只校验prop2
                        assetsInfoResult.setProp2(billCoinTypeProperty.getChangeProp2());
                        assetsInfoResult.setProp3(billCoinTypeProperty.getChangeProp3());
                    } else {
                        assetsInfoResult.setProp1(billCoinTypeProperty.getChangeProp1());
                        assetsInfoResult.setProp2(billCoinTypeProperty.getChangeProp2());
                        assetsInfoResult.setProp3(billCoinTypeProperty.getChangeProp3());
                        assetsInfoResult.setProp4(billCoinTypeProperty.getChangeProp4());
                        assetsInfoResult.setProp5(billCoinTypeProperty.getChangeProp5());
                    }
                    BizLogUtils.log(LogLevelEnum.VITAL_DEBUG, globalBillConfig, "InternalAssetsServiceImpl.queryAssetsInfoResultList with data assetsInfoResult {} ,billCoinTypeProperty {}", JSON.toJSONString(assetsInfoResult), JSONObject.toJSONString(billCoinTypeProperty));
                    resultList.add(assetsInfoResult);
                }
            }
            Map<Integer, BigDecimal> capitalInitPropertyMap = billCapitalInitPropertyService.selectRecords(String.valueOf(apolloBillConfig.getAccountType()), apolloBillConfig.getAccountParam(), CapitalInitBusinessTypeEnum.CONTRACT_UN_PROFIT_TRANSFER.getCode())
                    .stream().collect(Collectors.toMap(BillCapitalInitProperty::getCoinId, BillCapitalInitProperty::getInitValue));
            List<BillCoinProperty> billCoinProperties = billCoinPropertyService.selectCheckTimeRecord(Integer.valueOf(billAllConfig.getAccountType()), billAllConfig.getAccountParam(), new Date(request.getEndTime()));
            billCoinProperties.forEach(item -> {
                BigDecimal profitTransfersInitValue = capitalInitPropertyMap.getOrDefault(item.getCoinId().toString(), BigDecimal.ZERO);
                BigDecimal receiveFee = totalFeeMaps.getOrDefault(item.getCoinId(), BigDecimal.ZERO);
                totalFeeMaps.put(item.getCoinId(), item.getFee().add(receiveFee).subtract(profitTransfersInitValue));
            });
        }
        for (Map.Entry<Integer, BigDecimal> entry : totalFeeMaps.entrySet()) {
            BigDecimal receiveFee = receiveFeeMaps.getOrDefault(entry.getKey(), BigDecimal.ZERO);
            feeMaps.put(entry.getKey(), entry.getValue().negate().subtract(receiveFee));
        }
        return resultList;
    }

    private static boolean ifNoChange(BillCoinTypeProperty billCoinTypeProperty) {
        return billCoinTypeProperty.getChangeProp1().compareTo(BigDecimal.ZERO) == 0
                && billCoinTypeProperty.getChangeProp2().compareTo(BigDecimal.ZERO) == 0
                && billCoinTypeProperty.getChangeProp3().compareTo(BigDecimal.ZERO) == 0
                && billCoinTypeProperty.getChangeProp4().compareTo(BigDecimal.ZERO) == 0
                && billCoinTypeProperty.getChangeProp5().compareTo(BigDecimal.ZERO) == 0;
    }


    private void cleanOtherProperty(Byte accountType, AbstractProperty billCoinProperty) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        if (accountTypeEnum.isLever()) {
            billCoinProperty.setProp3(BigDecimal.ZERO);
            billCoinProperty.setProp4(BigDecimal.ZERO);
            billCoinProperty.setProp5(BigDecimal.ZERO);
        } else if (accountTypeEnum.isContract()) {
            //混合合约校验prop2,prop3
            billCoinProperty.setProp1(BigDecimal.ZERO);
            billCoinProperty.setProp4(BigDecimal.ZERO);
            billCoinProperty.setProp5(BigDecimal.ZERO);
        } else if (accountTypeEnum.isUta()) {
            billCoinProperty.setProp1(BigDecimal.ZERO);
            billCoinProperty.setProp4(BigDecimal.ZERO);
            billCoinProperty.setProp5(BigDecimal.ZERO);
            billCoinProperty.setProp6(BigDecimal.ZERO);
            billCoinProperty.setProp7(BigDecimal.ZERO);
        }
    }

    @Override
    public List<AssetsInfoResult> queryAllAssets(AssetsBaseRequest request) {
        List<AssetsInfoResult> resultList;
        Map<Integer, AssetsInfoResult> resultMap = new HashMap<>();
        // 验数校证
        this.checkTotalAssetsParam(request);
        AssetsCheckConfig assetsCheckConfig = ReconciliationApolloConfigUtils.getAssetsCheckConfig(request.getAssetsCheckType(), request.getAssetsCheckParam());
        List<String> subSystemList = assetsCheckConfig.getSubSystemList();
        for (String subSystem : subSystemList) {
            String[] strArr = subSystem.split(BillConstants.SEPARATOR);
            String accountTypeStr = strArr[0];
            String accountParam = strArr[1];
            Date endTime = new Date(request.getEndTime());
            List<BillCoinProperty> billCoinPropertyList = billCoinPropertyService.selectAssetsByEndTime(endTime, Byte.valueOf(accountTypeStr), accountParam);
            for (BillCoinProperty billCoinProperty : billCoinPropertyList) {
                cleanOtherProperty(Byte.valueOf(accountTypeStr), billCoinProperty);
                AssetsInfoResult assetsInfoResult = BeanCopierUtil.copyProperties(billCoinProperty, AssetsInfoResult.class);
//                assetsInfoResult.setSyncId(billConfig.getId());
                Integer coinId = assetsInfoResult.getCoinId();
                if (resultMap.get(coinId) == null) {
                    resultMap.put(coinId, assetsInfoResult);
                } else {
                    resultMap.computeIfPresent(coinId, (key, value) -> {
                        assetsInfoResult.mergeByCoinId(value);
                        return assetsInfoResult;
                    });
                }
            }
        }

        resultList = Lists.newArrayList(resultMap.values());
        return resultList;
    }


    @Override
    public List<AssetsInfoResult> queryAllAssetsType(AssetsBaseRequest request) {
        List<AssetsInfoResult> resultList;
        Map<String, AssetsInfoResult> resultMap = new HashMap<>();
        // 验数校证
        this.checkTotalAssetsParam(request);
        AssetsCheckConfig assetsCheckConfig = ReconciliationApolloConfigUtils.getAssetsCheckConfig(request.getAssetsCheckType(), request.getAssetsCheckParam());
        List<String> subSystemList = assetsCheckConfig.getSubSystemList();
        for (String subSystem : subSystemList) {
            String[] strArr = subSystem.split(BillConstants.SEPARATOR);
            ;
            String accountTypeStr = strArr[0];
            String accountParam = strArr[1];
            Date endTime = new Date(request.getEndTime());
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(Byte.valueOf(accountTypeStr));
            List<BillCoinTypeProperty> billCoinTypePropertyList = billCoinTypePropertyService.selectAssetsByEndTime(endTime, Byte.valueOf(accountTypeStr), accountParam);
            for (BillCoinTypeProperty billCoinTypeProperty : billCoinTypePropertyList) {
//                String bizType = accountTypeEnum.getBizTypePrefix() + BillConstants.SEPARATOR + billCoinTypeProperty.getBizType();
                // 总账初始化，抹平入出关系，所有的类型对汇总到other里
                String bizType = accountTypeEnum.getBizTypePrefix() + BillConstants.SEPARATOR + BillConstants.DEFAULT_BILL_BIZ_TYPE;
                cleanOtherProperty(Byte.valueOf(accountTypeStr), billCoinTypeProperty);
                AssetsInfoResult assetsInfoResult = BeanCopierUtil.copyProperties(billCoinTypeProperty, AssetsInfoResult.class);
                assetsInfoResult.setBizType(bizType);
                String groupKey = GroupByKeyUtil.groupByCoinIdAndTypeId(assetsInfoResult.getCoinId(), assetsInfoResult.getBizType());
//                assetsInfoResult.setSyncId(billConfig.getId());
                if (resultMap.get(groupKey) == null) {
                    resultMap.put(groupKey, assetsInfoResult);
                } else {
                    resultMap.computeIfPresent(groupKey, (key, value) -> {
                        assetsInfoResult.mergeByCoinId(value);
                        return assetsInfoResult;
                    });
                }
            }
        }

        resultList = Lists.newArrayList(resultMap.values());
        return resultList;
    }


    private void checkTotalAssetsParam(AssetsBaseRequest request) {
        if (!AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getCode().equals(request.getAssetsCheckType())
                && !AssetsCheckTypeEnum.LEVER_SPOT_TOTAL_ASSETS.getCode().equals(request.getAssetsCheckType())) {
            throw new BusinessException(ExceptionEnum.PARAM_VALIDATE_ERROR);
        }
    }
}
