package com.upex.reconciliation.service.service.client.cex.dto.req.common;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.service.client.cex.enmus.CexTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.IReconCexErrorCode;
import com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum;
import lombok.Data;
import javax.validation.constraints.NotNull;

@Data
public class GenSecretRequest {

    /**
     * 交易所类型（1: Binance, 2: OKX）
     */
    @NotNull(message = IReconCexErrorCode.ILLEGAL_CEXTYPE)
    private Integer cexType;

    /**
     * 交易所用户ID
     */
    @NotNull(message = IReconCexErrorCode.USERID_CANNOT_BENULL)
    private String cexUserId;

    @NotNull(message = IReconCexErrorCode.APIKEY_LABEL_CANNOT_BENULL)
    private  String apiKeyLabel;


    public void setCexType(Integer cexType) {
        CexTypeEnum cexTypeEnum = CexTypeEnum.fromType(cexType);
        if (cexTypeEnum == null) {
            throw new ApiException(ReconCexExceptionEnum.ILLEGAL_CEXTYPE);
        }
        this.cexType = cexType;
    }

}
