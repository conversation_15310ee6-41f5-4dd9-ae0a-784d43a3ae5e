package com.upex.reconciliation.service.business.convert.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.model.config.ReconOrderConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Getter
public class CombinedOrderData {

    // 主订单数据
    private ConvertOrder mainOrder;

    // 流水订单数据，按业务类型分类
    private final Map<String, ConvertBill> flowOrders = new HashMap<>();

    /**
     * 添加流水订单
     *
     * @param bizType   业务类型
     * @param flowOrder 流水订单数据
     */
    public void addFlowOrder(String bizType, ConvertBill flowOrder) {
        flowOrders.put(bizType, flowOrder);
    }

    /**
     * 添加主订单
     */
    public void addMainOrder(ConvertOrder mainOrder) {
        this.mainOrder = mainOrder;
    }

    /**
     * 获取订单ID
     *
     * @return 订单ID
     */
    public Long getOrderId() {
        return mainOrder != null ? mainOrder.getOrderId() : null;
    }

    public boolean isBuyDirection() {
        return this.getMainOrder().getFromCoinId().equals(2);
    }

    public boolean isUsdt() {
        return Objects.isNull(getMainOrder().getPt2()) &&
                Objects.isNull(this.getMainOrder().getPc2());
    }

    public Long getSystemUserId() {
        long uid = 0L;
        try {
            // 根据主订单获取系统账户id
            if (StringUtils.isNotBlank(mainOrder.getParams())) {
                JSONObject jsonObject = JSON.parseObject(mainOrder.getParams());
                Object cUid = jsonObject.get("cUid");
                if (Objects.nonNull(cUid)) {
                    uid = Long.parseLong(cUid.toString());
                }
            }
            return uid;
        } catch (Exception e) {
            return uid;
        }
    }

    public Long getUserId() {
        try {
            // 首先尝试从主订单获取账户ID
            Long accountId = getMainOrder().getAccountId();
            // 如果主订单中没有，则从用户流水订单中查找
            if (Objects.isNull(accountId)) {
                ReconOrderConfig reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
                List<String> userBizTypes = reconOrderConfig.getUserBizTypes();
                for (String userBizType : userBizTypes) {
                    ConvertBill bill = getFlowOrders().get(userBizType);
                    if (bill != null && bill.getAccountId() != null) {
                        accountId = bill.getAccountId();
                        break;
                    }
                }
            }
            return accountId != null ? accountId : 0L;
        } catch (Exception e) {
            return 0L;
        }
    }


}
