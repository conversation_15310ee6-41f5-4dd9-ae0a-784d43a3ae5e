package com.upex.reconciliation.service.business;

import com.upex.reconciliation.service.dao.entity.AssetsBillCoinProperty;
import com.upex.reconciliation.service.dao.entity.AssetsBillCoinTypeProperty;
import com.upex.reconciliation.service.model.dto.AbstractProperty;
import com.upex.reconciliation.service.model.dto.DataCalResultDTO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;


@Service
public class CheckAssetInAllService {


    <T extends AbstractProperty> boolean checkPropAssetsBillCoinProperty(T oldProperty, AssetsBillCoinProperty newProperty) {
        if (oldProperty == null) {
            if (newProperty == null || newProperty.getPropSum().compareTo(BigDecimal.ZERO) == 0) {
                return true;
            } else {
                return false;
            }
        }
        if (oldProperty.getPropSum().compareTo(newProperty.getPropSum()) == 0) {
            return true;
        }
        return false;
    }



    <T extends AbstractProperty> boolean checkPropAssetsBillCoinTypeProperty(T oldProperty, AssetsBillCoinTypeProperty newProperty) {
        if (oldProperty == null) {
            if (newProperty == null || newProperty.getChangePropSum().compareTo(BigDecimal.ZERO) == 0) {
                return true;
            } else {
                return false;
            }
        }
        if (oldProperty.getChangePropSum().compareTo(newProperty.getChangePropSum()) == 0) {
            return true;
        }
        return false;
    }

}
