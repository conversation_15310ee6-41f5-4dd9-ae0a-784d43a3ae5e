package com.upex.reconciliation.service.service;

import com.upex.bill.dto.enums.ProfitTransferStatusEnum;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.ProfitTransferTypeEnum;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;
import com.upex.reconciliation.service.model.dto.BillTimeSliceDTO;
import com.upex.reconciliation.service.model.dto.TransferDto;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_transfer(盈亏动账记录表)】的数据库操作Service
 * @createDate 2023-06-09 17:18:46
 */
@Service
public interface BillContractProfitTransferService {

    /**
     * 回退时刻，冲销动账记录
     *
     * @param resetCheckTime
     * @param lastCheckTime
     */
    void generateChargeRecordForResetBill(AccountTypeEnum accountTypeEnum, Date resetCheckTime, Date lastCheckTime);

    /**
     * 动账并更新结果
     *
     * @param executeTime 执行时间
     */
    void transferUseBillCheckResult(Date executeTime);

    List<BillContractProfitTransfer> queryTransferRecordsByStatusAndTime(ProfitTransferStatusEnum profitTransferStatusEnum, Date executeTime);

    /**
     * 通过时间查询延迟动账资产
     *
     * @param checkOkTime 检查时间
     * @return {@link List< BillContractProfitTransfer> }
     */
    List<BillContractProfitTransfer> listDelayedTransactionsByTime(Date checkOkTime);

    /**
     * 根据时间范围 填充 profitCoinList 和 transferList
     *
     * @param profitCoinList 币种盈亏记录
     * @param transferList   动账记录
     */
    void greaterTransferData(AssetsBillConfig assetsBillConfig, AssetsCheckConfig assetsCheckConfig, List<BillContractProfitCoinDetail> profitCoinList, List<BillContractProfitTransfer> transferList, List<BillContractProfitSymbolDetail> symbolDetailList);

    void greaterTransferData(ApolloReconciliationBizConfig apolloBizConfig, BillTimeSliceDTO billTimeSliceDTO);

    void resetCheckTransfer(List<TransferDto> transferIdList);

    /**
     * 批量保存动账数据
     *
     * @param apolloBillConfig
     * @param billSystemFeeBillList
     * <AUTHOR>
     * @date 2023/8/11 18:48
     */
    void batchSave(ApolloReconciliationBizConfig apolloBizConfig, BillConfig apolloBillConfig, List<AccountAssetsInfoResult> billSystemFeeBillList);


    /**
     * 根据条件 查询数据
     *
     * @param timeStart    对账开始时间
     * @param timeEnd      对账结束时间
     * @param accountType
     * @param sourceType   数据来源类型
     * @param accountParam
     * @return
     */
    List<BillContractProfitTransfer> selectByCheckOkTime(Date timeStart,
                                                         Date timeEnd,
                                                         Byte accountType,
                                                         int sourceType,
                                                         String accountParam);


    /**
     * 查询指定对账周期的所有数据
     *
     * @param checkOkTime
     * @return
     */
    List<BillContractProfitTransfer> selectAllByCheckOkTime(Date checkOkTime,
                                                            int pageSize);


    /**
     * 查询指定id列表数据
     *
     * @param idList
     * @return
     */
    List<BillContractProfitTransfer> selectAllByIdList(List<TransferDto> idList);


    /**
     * 根据id 更新指定字段
     *
     * @param statusNew
     * @param statusOld
     * @param version
     * @param updateTime
     * @param id
     * @return
     */

    int updateStatus(Integer statusNew,
                     Integer statusOld,
                     Long versionNew,
                     Long version,
                     Date updateTime,
                     Long id,
                     Date checkOkTime);

    int updateStatusWithTransferTime(Integer statusNew,
                                     Integer statusOld,
                                     Long versionNew,
                                     Long version,
                                     Date updateTime,
                                     Date transferTime,
                                     Long id,
                                     Date checkOkTime);

    /**
     * 批量插入
     *
     * @param list
     */
    int batchInsert(List<BillContractProfitTransfer> list);

    /**
     * 通过状态和时间查询延迟动账资产
     *
     * @param checkOkTime 检查时间
     * @return {@link List< BillContractProfitTransfer> }
     * <AUTHOR>
     * @date 2023/6/13 18:36
     */
    List<BillContractProfitTransfer> selectDelayedTransactionByStatusAndTime(Date checkOkTime);

    List<BillContractProfitTransfer> selectEachDelayedTransactionByStatusAndTime(Date startTime, Date checkOkTime, Byte accountType);

    /**
     * 通过时间查询延迟动账资产
     *
     * @param checkOkTime 检查时间
     * @return {@link List< BillContractProfitTransfer> }
     * <AUTHOR>
     * @date 2023/6/13 18:36
     */
    List<BillContractProfitTransfer> selectDelayedTransactionByTime(Date checkOkTime);

    /**
     * 根据动账表id、状态、数据来源查询动账数据
     *
     * @param idList
     * @return
     */
    List<BillContractProfitTransfer> selectTransferByIds(List<TransferDto> idList,
                                                         Integer status,
                                                         Integer sourceType);

    void transferFailureRepair();

    /**
     * 批量删除数据
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     */
    int deleteByCheckTime(Byte accountType, String accountParam, Date checkTime);

    /**
     * 批量删除
     *
     * @param minId
     * @param deleteSize
     * @param accountType
     * @param accountParam
     * @return
     */
    Boolean batchDelete(Long minId, Long deleteSize, Byte accountType, String accountParam);

    List<BillContractProfitTransfer> selectListByAccountTypeAndCheckTime(Byte accountType, String accountParam, Date checkTime);

    /**
     * 创建表
     *
     * @param mounth
     */
    void createTableForMonth(Integer mounth);


    /**
     * 根据时间获取所有业务线待动账金额
     *
     * @param checkOkTime
     * @return
     */
    Map<Integer, BigDecimal> selectAllUnProfitTransferByCheckOkTime(List<Byte> accountTypeList, Date checkOkTime);

    /**
     * 获取用户所有待动账金额
     *
     * @param userId
     * @param toAccountTypeList
     * @param transferTypes
     * @param checkOkTime
     * @param transferTime
     * @return
     */
    Map<Integer, BigDecimal> getUserUnProfitTransfer(Long userId, List<Byte> toAccountTypeList, List<Integer> transferTypes, Date checkOkTime, Date transferTime);

    /**
     * 获取未动账最小 checkOkTime
     *
     * @param tableTime
     * @return
     */
    Date getNotProfitTransferMinCheckOkTime(Date tableTime);

    /**
     * 获取动账数据
     *
     * @param accountType
     * @param accountParam
     * @param profitTypeList
     * @param checkOkTime
     * @return
     */
    List<BillContractProfitCoinDetail> getAllBillContractProfitCoinDetailList(Byte accountType,
                                                                              String accountParam,
                                                                              List<String> profitTypeList,
                                                                              Date checkOkTime);

    /**
     * 根据目标业务线进行动账
     *
     * @param currentTime    时间
     * @param toAccountTypes 参数格式 10,21,22
     */
    void transferUserBillCheckResultByAccountType(Date currentTime, List<Byte> toAccountTypes);
}