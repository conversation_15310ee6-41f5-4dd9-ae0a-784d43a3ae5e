package com.upex.reconciliation.service.dao.mapper.cex;

import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ThirdCexUserConfigMapper {

    /**
     * 根据ID查询配置
     */
    ThirdCexUserConfig selectById(Long id);


    /**
     * 根据用户ID查询API配置
     */
    List<ThirdCexUserConfig> selectByCexTypeAndUserId(Integer cexType,String cexUserId);

    /**
     * 插入新配置
     */
    int insert(ThirdCexUserConfig config);

    /**
     * 更新配置信息
     */
    int update(@Param("record") ThirdCexUserConfig config);

     int updateReadOnly(int cexType,String cexUserId,int readOnly);

    /**
     * 删除配置
     */
    int deleteById(Long id);

    List<ThirdCexUserConfig> selectAll();
}
