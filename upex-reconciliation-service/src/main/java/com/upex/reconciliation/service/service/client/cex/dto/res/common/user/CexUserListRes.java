package com.upex.reconciliation.service.service.client.cex.dto.res.common.user;


import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.PageData;
import lombok.AllArgsConstructor;
import lombok.Data;


/**
 * 查询 CEX 用户列表的响应结构
 */
@AllArgsConstructor
@Data
public class CexUserListRes {


    /**
     * 返回的数据内容（包含分页信息）
     */
    private PageData<CexUserInfoInnerRes> data;
}
