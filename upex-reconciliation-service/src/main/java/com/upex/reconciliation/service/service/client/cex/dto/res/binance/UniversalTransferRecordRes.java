package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class UniversalTransferRecordRes   implements IBinanceApiBaseRes {

    private Integer total;
    private List<UniversalTransferRecordInnerRes> rows;

    public static void main(String[] args) {
        String str="{\n" +
                "    \"total\": 2,\n" +
                "    \"rows\": [\n" +
                "        {\n" +
                "            \"timestamp\": 1747899142000,\n" +
                "            \"asset\": \"LDUSDT\",\n" +
                "            \"amount\": \"11.752223\",\n" +
                "            \"type\": \"MAIN_UMFUTURE\",\n" +
                "            \"status\": \"CONFIRMED\",\n" +
                "            \"tranId\": 264018774104\n" +
                "        },\n" +
                "        {\n" +
                "            \"timestamp\": 1747316547000,\n" +
                "            \"asset\": \"USDT\",\n" +
                "            \"amount\": \"120\",\n" +
                "            \"type\": \"MAIN_UMFUTURE\",\n" +
                "            \"status\": \"CONFIRMED\",\n" +
                "            \"tranId\": 262345139515\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        UniversalTransferRecordRes res= JSONObject.parseObject(str, UniversalTransferRecordRes.class);
        System.out.println(JSONObject.toJSONString( res));
    }

}
