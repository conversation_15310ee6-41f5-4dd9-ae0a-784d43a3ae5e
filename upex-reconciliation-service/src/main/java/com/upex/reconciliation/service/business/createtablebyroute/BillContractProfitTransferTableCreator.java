package com.upex.reconciliation.service.business.createtablebyroute;

import com.upex.reconciliation.service.model.config.ReconTableRouteConfig;
import com.upex.reconciliation.service.service.BillContractProfitTransferService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

@Slf4j
@Service
public class BillContractProfitTransferTableCreator extends AbstractTableCreator {
    @Resource
    private BillContractProfitTransferService billContractProfitTransferService;

    @Override
    public String getTableType() {
        return "billContractProfitTransfer";
    }

    @Override
    public void createTable(String accountType) {
        ReconTableRouteConfig.TableRouteRule tableRouteRule = getTableRouteRule(accountType);
        if (tableRouteRule == null) {
            return;
        }
        if (!ReconTableRouteConfig.TableRouteRuleEnum.MONTH.name().equalsIgnoreCase(tableRouteRule.getRule())) {
            throw new RuntimeException(getTableType() + "不支持创建表类型" + tableRouteRule.getRule());
        }
        billContractProfitTransferService.createTableForMonth( Objects.requireNonNullElse(tableRouteRule.getCreateDay(),3));
    }

    public String getTableSuffixName(Date time) {
        return this.getTableSuffixName(null, time);
    }


}
