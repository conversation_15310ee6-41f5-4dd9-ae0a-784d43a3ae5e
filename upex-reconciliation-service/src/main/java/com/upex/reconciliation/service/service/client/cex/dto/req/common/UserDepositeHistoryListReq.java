package com.upex.reconciliation.service.service.client.cex.dto.req.common;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.service.client.cex.enmus.CexUserStatusEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.CexUseTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class UserDepositeHistoryListReq extends CommonReq {

    private Date startTime;

    private Date endTime;

    private Integer cexUserStatus;

    private String userManagerId;

    private Integer useType;

    private Date checkSyncTime;

    @NotNull
    @Min(1)
    private int pageNum;
    // 当前页码（从1开始）
    @NotNull
    @Min(1)
    private int pageSize;

    public int getOffset() {
        return (pageNum - 1) * pageSize;
    }

    public UserDepositeHistoryListReq(Integer cexType, String cexUserId, String apiKey, String privateKey, Date startTime, Date endTime,Date checkSyncTime) {
        super(cexType, cexUserId, apiKey, privateKey);
        this.startTime = startTime;
        this.endTime = endTime;
        this.checkSyncTime = checkSyncTime;
    }

    public void setUseType(Integer useType) {
        if (useType != null) {
            CexUseTypeEnum cexUseTypeEnum = CexUseTypeEnum.fromType(useType);
            if (cexUseTypeEnum == null) {
                throw new ApiException(ReconCexExceptionEnum.ILLEGAL_USETYPE);
            }
        }
        this.useType = useType;
    }

    public void setCexUserStatus(Integer cexUserStatus) {
        if (cexUserStatus != null) {
            CexUserStatusEnum cexUserStatusEnum = CexUserStatusEnum.fromType(cexUserStatus);
            if (cexUserStatusEnum == null) {
                throw new ApiException(ReconCexExceptionEnum.ILLEGAL_CEXUSERSTATUS);
            }
        }
        this.cexUserStatus = cexUserStatus;
    }

}
