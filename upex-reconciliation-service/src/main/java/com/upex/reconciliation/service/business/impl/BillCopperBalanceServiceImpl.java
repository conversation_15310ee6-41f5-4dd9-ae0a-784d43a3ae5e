package com.upex.reconciliation.service.business.impl;

import com.upex.reconciliation.service.service.BillCopperBalanceService;
import com.upex.reconciliation.service.model.domain.BillCopperBalance;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class BillCopperBalanceServiceImpl implements BillCopperBalanceService {
//    @Resource
//    private BillCopperBalanceMapper billCopperBalanceMapper;

    /**
     * 获取指定用户的余额
     *
     * @param userIds
     * @param bizTime
     * @return
     */
    @Override
    public List<BillCopperBalance> selectByBizTime(List<Long> userIds, Date bizTime) {
        return null;
//        List<BillCopperBalance> billCopperBalanceList = billCopperBalanceMapper.selectByBizTime(userIds, bizTime);
//        if (CollectionUtils.isEmpty(billCopperBalanceList)) {
//            return new ArrayList();
//        }
//        return billCopperBalanceList;
    }


    /**
     * 批量插入copper余额信息
     *
     * @param balances
     */
    @Override
    public void batchInsert(List<BillCopperBalance> balances) {

//        billCopperBalanceMapper.batchInsert(balances);
    }


}
