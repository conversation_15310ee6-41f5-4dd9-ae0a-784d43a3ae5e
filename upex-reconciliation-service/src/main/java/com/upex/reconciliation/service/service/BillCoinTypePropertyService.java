package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty;
import com.upex.reconciliation.service.dao.mapper.BillCoinTypePropertyMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;


@Service
public class BillCoinTypePropertyService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billCoinTypePropertyMapper")
    private BillCoinTypePropertyMapper billCoinTypePropertyMapper;

    public int batchInsert(List<BillCoinTypeProperty> records, Byte accountType, String accountParam) {
        if (CollectionUtils.isNotEmpty(records)) {
            return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.batchInsert(records, accountType, accountParam));
        }
        return 0;
    }

    public Boolean updateById(Byte accountType, String accountParam, BillCoinTypeProperty billCoinTypeProperty) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.updateById(accountType, accountParam, billCoinTypeProperty));
    }

    public BillCoinTypeProperty selectById(Byte accountType, String accountParam, Long id) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.selectById(accountType, accountParam, id));
    }

    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.deleteByCheckTime(accountType, accountParam, checkTime));
    }

    public Boolean batchDelete(Long beginId, Long pageSize, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.batchDelete(beginId, pageSize, accountType, accountParam));
    }

    public List<BillCoinTypeProperty> selectCheckTimeRecordPage(Integer accountType,
                                                                String accountParam,
                                                                Date checkTime,
                                                                Long minId,
                                                                Integer pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.selectCheckTimeRecordPage(accountType, accountParam, checkTime, minId, pageSize));
    }


    public List<BillCoinTypeProperty> selectAssetsByEndTime(Date endTime, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.selectAssetsByEndTime(endTime, accountType, accountParam));
    }

    public List<BillCoinTypeProperty> selectByCoinIdCheckTimeNotInBizTypes(Integer accountType,
                                                                           String accountParam,
                                                                           Integer coinId,
                                                                           Date checkTime,
                                                                           List<String> notInBizTypes) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.selectByCoinIdCheckTimeNotInBizTypes(accountType, accountParam, coinId, checkTime, notInBizTypes));
    }


    public List<BillCoinTypeProperty> selectByCoinIdCheckTimeInBizTypes(Integer accountType,
                                                                        String accountParam,
                                                                        Integer coinId,
                                                                        Date checkTime,
                                                                        List<String> inBizTypes) {

        List<BillCoinTypeProperty> list = dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.selectByCoinIdCheckTimeInBizTypes(accountType, accountParam, coinId, checkTime, inBizTypes));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }


    public List<BillCoinTypeProperty> selectAllCoinByCheckTimeInBizTypes(Integer accountType,
                                                                         String accountParam,
                                                                         Date checkTime,
                                                                         List<String> inBizTypes) {
        if (CollectionUtils.isEmpty(inBizTypes)) {
            return Collections.emptyList();
        }
        List<BillCoinTypeProperty> list = dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.selectAllCoinByCheckTimeInBizTypes(accountType, accountParam, checkTime, inBizTypes));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }


    public List<BillCoinTypeProperty> listBillBetweenTime(Date startTime, Date endTime, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.listBillBetweenTime(startTime, endTime, accountType, accountParam));
    }

    public List<BillCoinTypeProperty> selectRangeCheckTimeRecord(Integer accountType,
                                                                 String accountParam,
                                                                 Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.selectRangeCheckTimeRecord(accountType, accountParam, checkTime));
    }


    public List<BillCoinTypeProperty> selectByCoinIdCheckTime(Integer accountType,
                                                              String accountParam,
                                                              Integer coinId,
                                                              Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.selectByCoinIdCheckTime(accountType, accountParam, coinId, checkTime));
    }


    public List<BillCoinTypeProperty> selectBizTypeCheckTimeRecord(Integer accountType,
                                                                   String accountParam,
                                                                   Date checkTime,
                                                                   String bizType) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.selectBizTypeCheckTimeRecord(accountType, accountParam, checkTime, bizType));
    }


    public List<BillCoinTypeProperty> selectBizTypeCoinId(Integer accountType,
                                                          String accountParam,
                                                          Date checkTime,
                                                          Integer coinId,
                                                          String bizType) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.selectBizTypeCoinId(accountType, accountParam, checkTime, coinId, bizType));
    }

    public Long getIdByCheckTime(Byte accountType, String accountParam, Date checkTime, String operation) {
        String operationNew = operation;
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.getIdByCheckTime(accountType, accountParam, checkTime, operationNew));
    }

    public boolean deleteByMaxId(Byte accountType, String accountParam, Long maxId, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.deleteByMaxId(accountType, accountParam, maxId, batchSize));
    }

    public Boolean deleteByLtCheckTime(Byte accountType, String accountParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypePropertyMapper.deleteByLtCheckTime(accountType, accountParam, checkTime, batchSize));
    }
}
