package com.upex.reconciliation.service.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.upex.reconciliation.service.model.dto.AbstractProperty;
import com.upex.ticker.facade.dto.PriceVo;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

public class BillCoinCalculationUtils {

    public static <T extends AbstractProperty> void addData(T coinProperty, T billCoinTypeUserProperty, T billCoinProperty) {
        //prop
        if (billCoinProperty.getProp1().compareTo(BigDecimal.ZERO) == 0) {
            coinProperty.setProp1(billCoinTypeUserProperty.getProp1());
        } else {
            coinProperty.setProp1(billCoinTypeUserProperty.getProp1().add(billCoinProperty.getProp1()));
        }
        if (billCoinProperty.getProp2().compareTo(BigDecimal.ZERO) == 0) {
            coinProperty.setProp2(billCoinTypeUserProperty.getProp2());

        } else {
            coinProperty.setProp2(billCoinTypeUserProperty.getProp2().add(billCoinProperty.getProp2()));
        }
        if (billCoinProperty.getProp3().compareTo(BigDecimal.ZERO) == 0) {
            coinProperty.setProp3(billCoinTypeUserProperty.getProp3());

        } else {
            coinProperty.setProp3(billCoinTypeUserProperty.getProp3().add(billCoinProperty.getProp3()));
        }
        if (billCoinProperty.getProp4().compareTo(BigDecimal.ZERO) == 0) {
            coinProperty.setProp4(billCoinTypeUserProperty.getProp4());

        } else {
            coinProperty.setProp4(billCoinTypeUserProperty.getProp4().add(billCoinProperty.getProp4()));
        }
        if (billCoinProperty.getProp5().compareTo(BigDecimal.ZERO) == 0) {
            coinProperty.setProp5(billCoinTypeUserProperty.getProp5());

        } else {
            coinProperty.setProp5(billCoinTypeUserProperty.getProp5().add(billCoinProperty.getProp5()));
        }

        //change
        if (billCoinProperty.getChangeProp1().compareTo(BigDecimal.ZERO) == 0) {
            coinProperty.setChangeProp1(billCoinTypeUserProperty.getProp1());
        } else {
            coinProperty.setChangeProp1(billCoinTypeUserProperty.getProp1().add(billCoinProperty.getChangeProp1()));
        }
        if (billCoinProperty.getChangeProp2().compareTo(BigDecimal.ZERO) == 0) {
            coinProperty.setChangeProp2(billCoinTypeUserProperty.getProp2());
        } else {
            coinProperty.setChangeProp2(billCoinTypeUserProperty.getProp2().add(billCoinProperty.getChangeProp2()));
        }
        if (billCoinProperty.getChangeProp3().compareTo(BigDecimal.ZERO) == 0) {
            coinProperty.setChangeProp3(billCoinTypeUserProperty.getProp3());
        } else {
            coinProperty.setChangeProp3(billCoinTypeUserProperty.getProp3().add(billCoinProperty.getChangeProp3()));
        }
        if (billCoinProperty.getChangeProp4().compareTo(BigDecimal.ZERO) == 0) {
            coinProperty.setChangeProp4(billCoinTypeUserProperty.getProp4());
        } else {
            coinProperty.setChangeProp4(billCoinTypeUserProperty.getProp4().add(billCoinProperty.getChangeProp4()));
        }
        if (billCoinProperty.getChangeProp5().compareTo(BigDecimal.ZERO) == 0) {
            coinProperty.setChangeProp5(billCoinTypeUserProperty.getProp5());
        } else {
            coinProperty.setChangeProp5(billCoinTypeUserProperty.getProp5().add(billCoinProperty.getChangeProp5()));
        }

    }

    /**
     * 币种资产合并
     *
     * @param mergeCoinMap
     * @param mergeIntoCoinMap
     */
    public static void mergeCoinAssets(Map<Integer, BigDecimal> mergeCoinMap, Map<Integer, BigDecimal> mergeIntoCoinMap) {
        if (CollectionUtil.isEmpty(mergeCoinMap)) {
            return;
        }
        mergeCoinMap.forEach((key, value) -> mergeIntoCoinMap.merge(key, value, BigDecimal::add));
    }

    /**
     * 计算币种资产折U
     *
     * @param coinMap
     * @param coinPriceMap
     * @return
     */
    public static BigDecimal calculateCoinUsdt(Map<Integer, BigDecimal> coinMap, Map<Integer, PriceVo> coinPriceMap) {
        if (CollectionUtil.isEmpty(coinMap)) {
            return BigDecimal.ZERO;
        }
        // 币种价格
        BigDecimal assetValueUsdt = BigDecimal.ZERO;
        // 计算用户usdt资产
        for (Integer coinId : coinMap.keySet()) {
            PriceVo priceVo = coinPriceMap.get(coinId);
            BigDecimal price = Objects.isNull(priceVo) ? BigDecimal.ZERO : priceVo.getPrice();
            BigDecimal coinCount = coinMap.getOrDefault(coinId, BigDecimal.ZERO);
            assetValueUsdt = NumberUtil.add(assetValueUsdt, NumberUtil.multiply(coinCount, price));
        }
        return assetValueUsdt;
    }
}
