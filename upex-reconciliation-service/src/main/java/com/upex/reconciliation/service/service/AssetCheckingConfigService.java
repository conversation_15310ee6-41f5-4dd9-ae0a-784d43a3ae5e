package com.upex.reconciliation.service.service;


import com.upex.reconciliation.service.dao.entity.AssetCheckingConfig;

/**
 * 资金对账配置记录
 * <AUTHOR>
 */
public interface AssetCheckingConfigService {
    /**
     * 查询最后一次配置
     *
     * @param businessType
     * @return
     */
    AssetCheckingConfig getLastAssetCheckingConfig(Integer businessType);


    /**
     * 获取最后成功的配置
     *
     * @param billCapitalStatus
     * @return
     */
    AssetCheckingConfig getLastAssetCheckingConfigByStatus(Integer businessType, Integer billCapitalStatus);

}
