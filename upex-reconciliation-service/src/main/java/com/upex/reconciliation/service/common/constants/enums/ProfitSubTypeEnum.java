package com.upex.reconciliation.service.common.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 利润类型枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ProfitSubTypeEnum {
    PROFIT("profit", "合约盈亏"),
    EXCHANGE("EXCHANGE", "合约换汇"),
    FEE("fee", "手续费"),
    INTEREST("interest", "合约利息归集"),
    ;
    private String code;
    private String desc;

    /**
     * 类型转枚举
     *
     * @param code
     * @return
     */
    public static ProfitSubTypeEnum toEnum(String code) {
        for (ProfitSubTypeEnum item : ProfitSubTypeEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }
}