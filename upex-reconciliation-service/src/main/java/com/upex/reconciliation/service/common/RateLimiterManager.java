package com.upex.reconciliation.service.common;

import com.google.common.util.concurrent.RateLimiter;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.module.impl.BillTimeSliceCheckModule;
import com.upex.reconciliation.service.business.module.impl.BillUserCheckModule;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.ReconKafkaOpsConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


/**
 * 限流
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RateLimiterManager {
    /***错误队列限流速率***/
    public static final Double ERROR_LIMIT_RATE = 1.0;
    /***限流map***/
    private Map<Byte, Map<Integer, RateLimiter>> rateLimiterPartitionMap = new ConcurrentHashMap<>();

    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor = new ScheduledThreadPoolExecutor(1);
    @Resource
    private BillEngineManager billEngineManager;
    @Resource
    private AlarmNotifyService alarmNotifyService;

    @PostConstruct
    public void init() {
        scheduledThreadPoolExecutor.scheduleWithFixedDelay(() -> {
            try {
                List<ApolloReconciliationBizConfig> apolloBizConfigs = ReconciliationApolloConfigUtils.getAllBillConfigByAccountType();
                for (ApolloReconciliationBizConfig apolloBizConfig : apolloBizConfigs) {
                    ReconKafkaOpsConfig apolloOpsConfig = apolloBizConfig.getReconKafkaOpsConfig();
                    BillLogicGroup billLogicGroup = billEngineManager.getBillLogicGroup(apolloBizConfig.getAccountType());
                    if (billLogicGroup == null) {
                        continue;
                    }
                    BillUserCheckModule userCheckModule = (BillUserCheckModule) billLogicGroup.getUserCheckModule();
                    Long errorBillMapSize = userCheckModule.getErrorBillMapSize();
                    Long waitLoadUserAssetsMapSize = userCheckModule.getWaitLoadUserAssetsMapSize();
                    Boolean isAllPartitionRateLimit = errorBillMapSize > apolloBizConfig.getReconKafkaOpsConfig().getErrorBillMapLimitSize() || waitLoadUserAssetsMapSize > apolloBizConfig.getReconKafkaOpsConfig().getWaitLoadUserAssetsMapLimitSize();
                    BillTimeSliceCheckModule timeSliceCheckModule = (BillTimeSliceCheckModule) billLogicGroup.getTimeSliceModule();
                    // 判断是否限流
                    Map<Integer, Long> latestPartitionBizTimeMap = timeSliceCheckModule.getLatestPartitionBizTime();
                    Long startTimeSliceFirstTime = timeSliceCheckModule.getStartTimeSliceMapFirstKey();
                    Map<Integer, RateLimiter> partitionRateLimiterMap = rateLimiterPartitionMap.computeIfAbsent(apolloBizConfig.getAccountType(), item -> new ConcurrentHashMap<>());
                    Map<Integer, Double> partitionRateMap = new ConcurrentHashMap<>();
                    for (int i = 0; i < apolloOpsConfig.getKafkaPartitionNum(); i++) {
                        RateLimiter rateLimiter = partitionRateLimiterMap.get(i);
                        if (rateLimiter == null) {
                            rateLimiter = RateLimiter.create(apolloOpsConfig.getMqConsumerRateLimit());
                        }
                        partitionRateLimiterMap.put(i, rateLimiter);
                        Long partitionLastBizTime = latestPartitionBizTimeMap.get(i);
                        Boolean isSinglePartitionRateLimit = startTimeSliceFirstTime != null && partitionLastBizTime != null && (apolloOpsConfig.getTimeSliceRateLimitBizTimeGap() < partitionLastBizTime - startTimeSliceFirstTime);
                        double currentRate = rateLimiter.getRate();
                        partitionRateMap.put(i, currentRate);
                        if (currentRate != apolloOpsConfig.getMqConsumerRateLimit() && !isAllPartitionRateLimit && !isSinglePartitionRateLimit) {
                            rateLimiter.setRate(apolloOpsConfig.getMqConsumerRateLimit());
                        }
                    }
                    log.info("RateLimiterManager.refresh rate limiter accountType={} isAllPartitionRateLimit:{} partitionRateMap={} errorBillMapSize:{} waitLoadUserAssetsMapSize:{}", apolloBizConfig.getAccountType(), isAllPartitionRateLimit, partitionRateMap, errorBillMapSize, waitLoadUserAssetsMapSize);
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }, 1, 1, TimeUnit.SECONDS);
    }


    /**
     * 限流
     *
     * @param accountType
     * @param permits
     * @return
     */
    public double tryRateLimitMqPoll(Byte accountType, Integer partition, int permits) {
        Map<Integer, RateLimiter> partitionMap = rateLimiterPartitionMap.get(accountType);
        if (partitionMap == null) {
            return -1;
        }
        RateLimiter rateLimiter = partitionMap.get(partition);
        return rateLimiter != null && permits > 0 ? rateLimiter.acquire(permits) : -1;
    }

    /**
     * 加载用户队列限流
     *
     * @param accountType
     * @param size
     */
    public void tryRateLimitLoadUserAssetsSize(Byte accountType, Integer waitLoadUserAssetsSize, Long recordSize, Integer cmdQueueSize) {
        Map<Integer, RateLimiter> partitionMap = rateLimiterPartitionMap.get(accountType);
        if (partitionMap == null) {
            return;
        }
        ReconKafkaOpsConfig apolloOpsConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType).getReconKafkaOpsConfig();
        if (recordSize > apolloOpsConfig.getWaitLoadUserAssetsMapLimitSize()) {
            log.info("RateLimiterManager.tryRateLimitLoadUserAssetsSize accountType={} waitLoadUserAssetsSize:{} recordSize={} cmdQueueSize={} ", accountType, waitLoadUserAssetsSize, recordSize, cmdQueueSize);
            partitionMap.forEach((key, rateLimiter) -> {
                rateLimiter.setRate(ERROR_LIMIT_RATE);
            });
        }
    }

    /**
     * 时间片限流
     *
     * @param startTimeSliceFirstTime
     * @param commonBillChangeData
     */
    public void tryRateLimitTimeSliceFirstTimeGap(Long startTimeSliceFirstTime, CommonBillChangeData commonBillChangeData) {
        Map<Integer, RateLimiter> partitionMap = rateLimiterPartitionMap.get(commonBillChangeData.getAccountType());
        if (MapUtils.isEmpty(partitionMap) || startTimeSliceFirstTime == null) {
            return;
        }
        ReconKafkaOpsConfig apolloOpsConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(commonBillChangeData.getAccountType()).getReconKafkaOpsConfig();
        RateLimiter rateLimiter = partitionMap.get(commonBillChangeData.getPartition());
        if (rateLimiter != null && (commonBillChangeData.getBizTime().getTime() - startTimeSliceFirstTime > apolloOpsConfig.getTimeSliceRateLimitBizTimeGap())) {
            rateLimiter.setRate(ERROR_LIMIT_RATE);
        }
    }
}
