package com.upex.reconciliation.service.service.client.cex.config.binance;

import com.binance.connector.client.common.ApiClient;
import com.binance.connector.client.common.JSON;
import com.binance.connector.client.common.SystemUtil;
import com.binance.connector.client.common.auth.BinanceAuthenticationFactory;
import com.binance.connector.client.common.auth.SignatureGeneratorFactory;
import com.binance.connector.client.common.configuration.ClientConfiguration;
import com.binance.connector.client.common.configuration.SignatureConfiguration;
import com.upex.reconciliation.service.model.config.ApolloProxyConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.internal.tls.OkHostnameVerifier;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.net.ssl.*;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.security.*;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Collection;
import java.util.concurrent.TimeUnit;

import static org.bouncycastle.crypto.tls.ConnectionEnd.client;

@Slf4j
@Component
@Configuration
public class BinanceConfiguration {

    private static final String USER_AGENT =
            String.format(
                    "binance-spot/2.0.0 (Java/%s; %s; %s)",
                    SystemUtil.getJavaVersion(), SystemUtil.getOs(), SystemUtil.getArch());

    @Bean
    SignatureGeneratorFactory signatureGeneratorFactory() {
        return new SignatureGeneratorFactory();
    }

    @Bean
    BinanceAuthenticationFactory binanceAuthenticationFactory() {
        return new BinanceAuthenticationFactory();
    }


    @Bean
    public SignatureConfiguration signatureConfiguration() {
        SignatureConfiguration signatureConfiguration = new SignatureConfiguration();
        signatureConfiguration.setApiKey("");
        signatureConfiguration.setSecretKey("");
        return signatureConfiguration;
    }

    @Bean("defaultApiClientConfig")
    public ClientConfiguration clientConfiguration(SignatureConfiguration signatureConfiguration) {
        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.setUrl(BinanceHostConfig.BINANCE_HOST);
        clientConfiguration.setSignatureConfiguration(signatureConfiguration);
        return clientConfiguration;
    }

    @Bean("fApiClientConfig")
    public ClientConfiguration fApiClientConfiguration(SignatureConfiguration signatureConfiguration) {
        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.setUrl(BinanceHostConfig.BINANCE_HOST_F);
        clientConfiguration.setSignatureConfiguration(signatureConfiguration);
        return clientConfiguration;
    }

    @Bean("pApiClientConfig")
    public ClientConfiguration pApiClientConfiguration(SignatureConfiguration signatureConfiguration) {
        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.setUrl(BinanceHostConfig.BINANCE_HOST_P);
        clientConfiguration.setSignatureConfiguration(signatureConfiguration);
        return clientConfiguration;
    }

    @Bean("sApiClientConfig")
    public ClientConfiguration sApiClientConfiguration(SignatureConfiguration signatureConfiguration) {
        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.setUrl(BinanceHostConfig.BINANCE_HOST_S);
        clientConfiguration.setSignatureConfiguration(signatureConfiguration);
        return clientConfiguration;
    }

    @Bean("dApiClientConfig")
    public ClientConfiguration dApiClientConfiguration(SignatureConfiguration signatureConfiguration) {
        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.setUrl(BinanceHostConfig.BINANCE_HOST_D);
        clientConfiguration.setSignatureConfiguration(signatureConfiguration);
        return clientConfiguration;
    }

    @Bean("defaultApiClient")
    public ApiClient apiClient(@Qualifier("defaultApiClientConfig") ClientConfiguration clientConfiguration) {
        ApiClient apiClient = new ApiClient(clientConfiguration,buildHttpClient());
        apiClient.setUserAgent(USER_AGENT);
        apiClient.setJson(JSON.getGson());
        return apiClient;
    }

    @Bean("fapiClient")
    public ApiClient fapiClient(@Qualifier("fApiClientConfig") ClientConfiguration clientConfiguration) {
        ApiClient apiClient = new ApiClient(clientConfiguration,buildHttpClient());
        apiClient.setUserAgent(USER_AGENT);
        apiClient.setJson(JSON.getGson());
        return apiClient;
    }

    @Bean("papiClient")
    public ApiClient papiClient(@Qualifier("pApiClientConfig") ClientConfiguration clientConfiguration) {
        ApiClient apiClient = new ApiClient(clientConfiguration,buildHttpClient());
        apiClient.setUserAgent(USER_AGENT);
        apiClient.setJson(JSON.getGson());
        return apiClient;
    }

    @Bean("sapiClient")
    public ApiClient sapiClient(@Qualifier("sApiClientConfig") ClientConfiguration clientConfiguration) {
        ApiClient apiClient = new ApiClient(clientConfiguration,buildHttpClient());
        apiClient.setUserAgent(USER_AGENT);
        apiClient.setJson(JSON.getGson());
        return apiClient;
    }

    @Bean("dapiClient")
    public ApiClient dapiClient(@Qualifier("dApiClientConfig") ClientConfiguration clientConfiguration) {
        ApiClient apiClient = new ApiClient(clientConfiguration,buildHttpClient());
        apiClient.setUserAgent(USER_AGENT);
        apiClient.setJson(JSON.getGson());
        return apiClient;
    }

    OkHttpClient buildHttpClient()  {
        OkHttpClient mOkHttpClient;
        ApolloProxyConfig proxyConfig = ReconciliationApolloConfigUtils.getApolloProxyConfig();
        if (proxyConfig != null && proxyConfig.isOpenProxy()) {
            InetSocketAddress socketAddress = InetSocketAddress.createUnresolved(proxyConfig.getProxyIp(), proxyConfig.getProxyPort());
            Proxy.Type type = Proxy.Type.valueOf(proxyConfig.getProxyScheme());
            mOkHttpClient = createSSLClientDefault()
                    .proxy(new Proxy(type, socketAddress))
                    .connectTimeout(proxyConfig.getConnectTimeout(), TimeUnit.MILLISECONDS)
                    .build();
            log.info("use proxy and create ssl okhttp");
        } else {
            mOkHttpClient = new OkHttpClient.Builder()
                    .connectTimeout(proxyConfig.getConnectTimeout(), TimeUnit.MILLISECONDS)
                    .build();
            log.info("create default okhttp");

        }
        if (proxyConfig != null && proxyConfig.isUseTLS() && proxyConfig.isOpenProxy()) {
            InetSocketAddress socketAddress = InetSocketAddress.createUnresolved(proxyConfig.getProxyIp(), proxyConfig.getProxyPort());
            Proxy.Type type = Proxy.Type.valueOf(proxyConfig.getProxyScheme());
            mOkHttpClient= buildTLSHttpClient().proxy(new Proxy(type, socketAddress))
                    .connectTimeout(proxyConfig.getConnectTimeout(), TimeUnit.MILLISECONDS)
                    .build();
            log.info("use proxy and create tls okhttp");
        }
        return mOkHttpClient;
    }

    private KeyStore newEmptyKeyStore(char[] password) throws GeneralSecurityException {
        try {
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null, password);
            return keyStore;
        } catch (IOException e) {
            throw new AssertionError(e);
        }
    }

    private static OkHttpClient.Builder createSSLClientDefault() {
        try {
            final TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public void checkServerTrusted(X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public X509Certificate[] getAcceptedIssuers() {
                            return new X509Certificate[]{};
                        }
                    }
            };
            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new SecureRandom());
            final SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            // 这一行JDK9以上适用，JDK8把第二个参数去掉
            builder.sslSocketFactory(sslSocketFactory, (X509TrustManager) trustAllCerts[0]);
            builder.hostnameVerifier((hostname, session) -> true);
            return builder;
        } catch (Exception e) {
            log.error("createSSLClientDefault is exception...", e);
            return new OkHttpClient().newBuilder();
        }
    }

    public OkHttpClient.Builder buildTLSHttpClient()  {
        OkHttpClient.Builder mOkHttpClient = new OkHttpClient().newBuilder();
        try {
            TrustManager[] trustManagers;
            HostnameVerifier hostnameVerifier;
            KeyManager[] keyManagers = new KeyManager[0];
            TrustManagerFactory trustManagerFactory =
                    TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            InputStream sslCaCert = null;
            if (sslCaCert == null) {
                trustManagerFactory.init((KeyStore) null);
            } else {
                char[] password = null; // Any password will work.
                CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
                Collection<? extends Certificate> certificates =
                        certificateFactory.generateCertificates(sslCaCert);
                if (certificates.isEmpty()) {
                    throw new IllegalArgumentException(
                            "expected non-empty set of trusted certificates");
                }
                KeyStore caKeyStore = newEmptyKeyStore(password);
                int index = 0;
                for (Certificate certificate : certificates) {
                    String certificateAlias = "ca" + (index++);
                    caKeyStore.setCertificateEntry(certificateAlias, certificate);
                }
                trustManagerFactory.init(caKeyStore);
            }
            trustManagers = trustManagerFactory.getTrustManagers();
            hostnameVerifier = OkHostnameVerifier.INSTANCE;

            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(keyManagers, trustManagers, new SecureRandom());
            mOkHttpClient = mOkHttpClient
                    .sslSocketFactory(
                            sslContext.getSocketFactory(),
                            (X509TrustManager) trustManagers[0])
                    .hostnameVerifier(hostnameVerifier);
        }catch (Exception e){
            log.error("buildTLSHttpClient is exception {}", e);
        }
        return mOkHttpClient;
    }


}

