package com.upex.reconciliation.service.service;


import com.upex.reconciliation.service.dao.entity.BillContractProfitCoinDetail;
import com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_coin_detail(币种维度盈亏明细表)】的数据库操作Service
 * @createDate 2023-06-09 17:18:46
 */
public interface OldBillContractProfitCoinDetailService {

    List<BillContractProfitCoinDetail> selectListByAccountTypeAndCheckTime(Byte accountType, String accountParam, Date date);
}
