package com.upex.reconciliation.service.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.AssetsContractProfitCoinDetail;
import com.upex.reconciliation.service.dao.mapper.AssetsContractProfitCoinDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 总账合约盈亏换汇数据计算
 *
 * <AUTHOR> @description
 */
@Slf4j
@Service
public class AssetsContractProfitCoinDetailService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "assetsContractProfitCoinDetailMapper")
    private AssetsContractProfitCoinDetailMapper assetsContractProfitCoinDetailMapper;

    /**
     * @param accountType
     * @param accountParam
     * @param checkOkTime
     * @param profitType
     * @return
     */
    public Map<Integer, AssetsContractProfitCoinDetail> getAssetsContractProfitCoinDetailMap(String accountType, String accountParam, Date checkOkTime, String profitType) {
        List<AssetsContractProfitCoinDetail> billContractProfitCoinDetailList =
                dbHelper.doDbOpInReconMaster(() -> assetsContractProfitCoinDetailMapper.getAssetsContractProfitCoinDetailList(accountType, accountParam, checkOkTime, profitType));
        if (CollectionUtils.isEmpty(billContractProfitCoinDetailList)) {
            billContractProfitCoinDetailList = new ArrayList<>();
        }
        return billContractProfitCoinDetailList.stream().collect(Collectors.toMap(AssetsContractProfitCoinDetail::getCoinId, Function.identity(), (key1, key2) -> key2));
    }

    public Date getLastCheckOkTime(String accountType, String accountParam, String profitType) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitCoinDetailMapper.getLastCheckOkTime(accountType, accountParam, profitType));
    }

    public int batchInsert(String accountType, String accountParam, List<AssetsContractProfitCoinDetail> billContractProfitCoinDetailList) {
        if (CollectionUtils.isNotEmpty(billContractProfitCoinDetailList)) {
            return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitCoinDetailMapper.batchInsert(accountType, accountParam, billContractProfitCoinDetailList));
        }
        return 0;
    }

    public boolean deleteByCheckTime(String accountType, String accountParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitCoinDetailMapper.deleteByCheckTime(accountType, accountParam, checkTime, batchSize));
    }

    public Boolean batchDelete(Long beginId, Long pageSize, String accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitCoinDetailMapper.batchDelete(beginId, pageSize, accountType, accountParam));
    }

    public List<AssetsContractProfitCoinDetail> selectListByAccountTypeAndCheckTime(String accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitCoinDetailMapper.selectListByAccountTypeAndCheckTime(accountType, accountParam, checkTime));
    }

    /**
     * 获取所有合约盈亏换汇数据
     *
     * @param accountType
     * @param accountParam
     * @param profitTypeList
     * @param checkOkTime
     * @return
     */
    public List<AssetsContractProfitCoinDetail> getAllAssetsContractProfitCoinDetailList(
            String accountType,
            String accountParam,
            List<String> profitTypeList,
            Date checkOkTime) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitCoinDetailMapper.getAllAssetsContractProfitCoinDetailList(accountType, accountParam, profitTypeList, checkOkTime));
    }

    /**
     * 获取所有合约盈亏换汇数据
     *
     * @param accountType
     * @param accountParam
     * @param profitTypeList
     * @param checkOkTime
     * @return
     */
    public List<AssetsContractProfitCoinDetail> getLastValidAssetsContractProfitCoinDetailList(
            String accountType,
            String accountParam,
            List<String> profitTypeList,
            Date checkOkTime) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitCoinDetailMapper.getLastValidAssetsContractProfitCoinDetailList(accountType, accountParam, profitTypeList, checkOkTime));
    }

    public List<AssetsContractProfitCoinDetail> getAllAfterRecord(
            Date resetCheckTime,
            String accountType,
            String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitCoinDetailMapper.getAllAfterRecord(resetCheckTime, accountType, accountParam));
    }

    public int deleteAfterRecord(
            Date resetCheckTime,
            String accountType,
            String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitCoinDetailMapper.deleteAfterRecord(resetCheckTime, accountType, accountParam));
    }

    public int batchInsertHis(
            String accountType,
            String accountParam,
            List<AssetsContractProfitCoinDetail> list) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitCoinDetailMapper.batchInsertHis(accountType, accountParam, list));
    }

    public Integer deleteById(String accountType, String accountParam, Long id) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitCoinDetailMapper.deleteById(accountType, accountParam, id));
    }

    public void repairAssetsContractProfitCoinDetail(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        log.info("repairAssetsContractProfitCoinDetail data:{}", jobParam);
        String action = jsonObject.getString("action");
        JSONArray delIds = jsonObject.getJSONArray("delIds");
        JSONArray insertList = jsonObject.getJSONArray("insertList");
        String accountType = AccountTypeEnum.INTERNAL.getBizTag();
        String accountParam = AccountTypeEnum.INTERNAL.getAccountParam();
        if ("delete".equals(action)) {
            if (delIds == null || delIds.size() == 0) {
                return;
            }
            for (int i = 0; i < delIds.size(); i++) {
                deleteById(accountType, accountParam, delIds.getLong(i));
            }
        } else if ("insert".equals(action)) {
            if (insertList == null || insertList.size() == 0) {
                return;
            }
            List<AssetsContractProfitCoinDetail> assetsContractProfitCoinDetailList = Convert.toList(AssetsContractProfitCoinDetail.class, insertList);
            if (CollectionUtils.isNotEmpty(assetsContractProfitCoinDetailList)) {
                Date nowDate = new Date();
                assetsContractProfitCoinDetailList.forEach(item -> {
                    item.setCreateTime(nowDate);
                    item.setUpdateTime(nowDate);
                });
                batchInsert(accountType, accountParam, assetsContractProfitCoinDetailList);
            }
        }
    }
}




