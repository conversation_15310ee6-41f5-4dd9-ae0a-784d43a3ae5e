package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.dao.bill.entity.AssetsBillConfig;
import com.upex.reconciliation.service.dao.mapper.OldAssetsBillConfigMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Slf4j
@Service
public class OldAssetsBillConfigService {

    @Resource
    private BillDbHelper dbHelper;


    @Resource(name = "oldAssetsBillConfigMapper")
    private OldAssetsBillConfigMapper oldAssetsBillConfigMapper;


    public AssetsBillConfig selectByTypeAndParam(String assetsCheckType, String assetsCheckParam) {
        return dbHelper.doDbOpInBillMaster(()->oldAssetsBillConfigMapper.selectByTypeAndParam(assetsCheckType,assetsCheckParam));
    }


}
