package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.dao.mapper.cex.ThirdCexUserMapper;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CexUserListRequest;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CexUserPageListRequest;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.PageData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class ThirdCexUserService {

    @Resource
    private BillDbHelper billDbHelper;

    @Resource
    ThirdCexUserMapper thirdCexUserMapper;


    public ThirdCexUser selectById(Long id) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserMapper.selectById(id));
    }
    public List<ThirdCexUser> selectAllParentUserByCexType(Integer cexType) {
        return billDbHelper.doDbOpInReconMaster(() ->
                thirdCexUserMapper.selectAllParentUserByCexType(cexType));
    }

    public List<ThirdCexUser> selectSubUser(CexUserListRequest request) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserMapper.selectSubUser(request));
    }

    public List<ThirdCexUser> getUsersByPage(CexUserPageListRequest request) {
        List<ThirdCexUser> users = billDbHelper.doDbOpInReconMaster(() -> thirdCexUserMapper.selectByPage(request));
//        int total = billDbHelper.doDbOpInReconMaster(() -> thirdCexUserMapper.countByPage(request));
        return users;
    }


    public ThirdCexUser selectByCexTypeAndUserId(Integer cexType, String cexUserId) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserMapper.selectByCexTypeAndUserId(cexType, cexUserId));
    }


    public List<ThirdCexUser> selectSubUserByCexTypeAndParentUserId(Integer cexType, String parentUserId) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserMapper.selectSubUserByCexTypeAndParentUserId(cexType, parentUserId));
    }


    public int add(ThirdCexUser user) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserMapper.insert(user));
    }


    public int update(ThirdCexUser user) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserMapper.update(user));
    }

    public int updateSubUserApikeyStatus(String parentCexUserId,Integer apikeyStatus) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserMapper.updateSubUserApikeyStatus(parentCexUserId,apikeyStatus));
    }


    public int batchInsertSubUser(List<ThirdCexUser> users) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserMapper.batchInsertSubUser(users));
    }

    public int batchInsert(List<ThirdCexUser> users) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserMapper.batchInsert(users));
    }

    public int deleteSubUserByCexTypeAndParentUserId(Integer cexType, String parentUserId) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserMapper.deleteSubUserByCexTypeAndParentUserId(cexType, parentUserId));
    }

    public List<ThirdCexUser> selectByCondition(CexUserListRequest request) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserMapper.selectByCondition(request));
    }


}
