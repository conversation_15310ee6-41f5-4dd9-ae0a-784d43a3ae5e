package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.math.BigDecimal;
@Data
public class MarginAccountInfoInnerRes {
    /**
     * {
     * asset：资产的符号（例如，BTC）。
     * borrowed：您以保证金借入的资产数量。
     * free：可用于交易或提取的资产数量（未锁定或借用）。
     * interest：此资产借入金额的累计利息。
     * locked：当前锁定在未结订单或仓位中的资产数量。
     * netAsset：您保证金账户中的资产净额
     *             "asset": "COTI",
     *             "free": "0",
     *             "locked": "0",
     *             "borrowed": "0",
     *             "interest": "0",
     *             "netAsset": "0"
     *         }
     */
    private String asset;
    private BigDecimal free;
    private BigDecimal locked;
    private BigDecimal borrowed;
    private BigDecimal interest;
    private BigDecimal netAsset;
}
