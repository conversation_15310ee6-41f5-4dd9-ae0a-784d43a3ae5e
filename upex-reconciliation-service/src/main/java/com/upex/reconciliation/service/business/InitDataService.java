package com.upex.reconciliation.service.business;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.upex.bill.dto.params.BasePageRequest;
import com.upex.bill.dto.params.BaseRequest;
import com.upex.bill.dto.results.PageResponse;
import com.upex.bill.dto.results.UserInfoResult;
import com.upex.commons.support.util.SiteUtil;
import com.upex.mixcontract.process.facade.dto.MixAccountAssetsExtension;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.utils.ReconPapTradingUtil;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.business.createtablebyroute.BillUserPositionTableCreator;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.common.constants.enums.QueryUserAssetsSceneEnum;
import com.upex.reconciliation.service.common.constants.negativeCheck.NegativeCheckCacheMigrateEnum;
import com.upex.reconciliation.service.common.constants.negativeCheck.NegativeCheckTypeEnum;
import com.upex.reconciliation.service.dao.bill.entity.BillUser;
import com.upex.reconciliation.service.dao.bill.entity.OldBillConfig;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.alarm.AlarmParam;
import com.upex.reconciliation.service.model.alarm.UserAssetNegativeErrorModel;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.CoinComparisonToleranceVo;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.dto.DataCalResultDTO;
import com.upex.reconciliation.service.model.dto.SyncBillUserDTO;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.service.impl.BillCoinUserPropertySnapshotService;
import com.upex.reconciliation.service.utils.*;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.user.facade.utils.SiteCodeUtils;
import com.upex.user.facade.vo.user.UserBaseInfoDTO;
import com.upex.utils.task.TaskManager;
import com.upex.utils.task.TaskVoidBatchResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.*;

/**
 * 全量数据初始化流程：
 * 1.全量查 userId
 * 2.之后分批查业务asset
 * 3.数据转换
 * 4.落库
 *
 * <AUTHOR>
 * @date 2023/11/2 20:23
 */
@Service
@Slf4j
public class InitDataService {
    @Resource
    private CommonService commonService;
    @Resource
    private BillEngineManager reconciliationApplication;
    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;
    @Resource
    private BillCoinUserPropertySnapshotService billCoinUserPropertySnapshotService;
    @Resource
    private BillCoinTypePropertyService billCoinTypePropertyService;
    @Resource
    private BillConfigService billConfigService;
    @Resource
    private BillAllConfigService billAllConfigService;
    @Resource
    private BillCoinPropertyService billCoinPropertyService;
    @Resource
    private AssetsBillCoinPropertyService assetsBillCoinPropertyService;
    @Resource
    private AssetsBillCoinTypePropertyService assetsBillCoinTypePropertyService;
    @Resource
    private OldAssetsBillCoinPropertyService oldAssetsBillCoinPropertyService;
    @Resource
    private OldAssetsBillCoinTypePropertyService oldAssetsBillCoinTypePropertyService;
    @Resource
    private BillUserPositionService billUserPositionService;
    @Resource(name = "taskManager")
    private TaskManager taskManager;
    @Resource
    private OldBillCoinUserService oldBillCoinUserService;
    @Resource
    private OldBillCoinService oldBillCoinService;
    @Resource
    private OldBillCoinTypeService oldBillCoinTypeService;
    @Resource
    private OldBillConfigService oldBillConfigService;
    @Resource
    private OldBillUserService oldBillUserService;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    @Resource
    private AlarmNotifyService alarmNotifyService;
    @Resource
    private CheckAssetInAllService checkAssetInAllService;
    @Resource
    private OldBillContractProfitTransferService oldBillContractProfitTransferService;
    @Resource
    private OldBillContractProfitCoinDetailService oldBillContractProfitCoinDetailService;
    @Resource
    private OldBillContractProfitSymbolDetailService oldBillContractProfitSymbolDetailService;
    @Resource
    private BillContractProfitTransferService billContractProfitTransferService;
    @Resource
    private BillContractProfitCoinDetailService billContractProfitCoinDetailService;
    @Resource
    private BillContractProfitSymbolDetailService billContractProfitSymbolDetailService;
    @Resource
    private BillSymbolPropertyService billSymbolPropertyService;
    @Resource
    private BillSymbolCoinPropertyService billSymbolCoinPropertyService;
    @Resource
    private OldBillSymbolPropertyService oldBillSymbolPropertyService;
    @Resource
    private OldBillSymbolCoinPropertyService oldBillSymbolCoinPropertyService;
    @Resource
    private AssetsBillConfigService assetsBillConfigService;
    @Resource
    private OldAssetsBillConfigService oldAssetsBillConfigService;
    @Resource(name = "kafkaProducer")
    private KafkaProducer<String, String> kafkaProducer;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private BillCoinUserPropertyAssetSnapshotService billCoinUserPropertyAssetSnapshotService;
    @Resource
    private BillUserPositionTableCreator billUserPositionTableCreator;

    private final static int CONCURRENT=20;
    private final static int QUERY_PAGE_SIZE=1000;

    public InitDataService() {
    }

    /**
     * 初始化逻辑：
     * 加载用户，根据用户批量加载资产，资产按照纬度做聚合，落db
     *
     * @param addTime
     * @param accountType
     * @param maxIdParam
     * @param pageSize
     * @param page
     */
    public void baseDataInitStart(Long bizTime, Long addTime, Byte accountType, Long maxIdParam, Integer pageSize, Integer page, String accountParam, Integer concurrent) {

        Long maxId = maxIdParam != null ? maxIdParam : 1L;
        Stopwatch totalStopwatch = Stopwatch.createStarted();
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setPageNo(page);
        basePageRequest.setPageSize(pageSize);

        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setBeginTime(bizTime - 1L);
        baseRequest.setEndTime(bizTime);
        baseRequest.setAccountType(accountType);
        baseRequest.setAccountParam(accountParam);
        baseRequest.setMaxId(maxId);
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        PageResponse<UserInfoResult> pageResponse = null;
        // 存放聚合纬度数据的map
        Map<Integer, BillCoinProperty> billCoinPropertyMap = new HashMap<>();
        // coin和bizType的聚合map,kv关系如下：<coinId#bizType, BillCoinTypeProperty>
        Map<String, BillCoinTypeProperty> billCoinTypePropertyMap = new HashMap<>();
        Set<Long> alreadyInitUserIds = new HashSet<Long>();
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        do {
            Integer partitionSize = apolloBizConfig.getInitPartitionSize();
            Queue<BillCoinUserProperty> billCoinUserPropertyListToDB = new ConcurrentLinkedQueue<>();
            Queue<BillCoinTypeUserProperty> billCoinTypeUserPropertyListToDB = new ConcurrentLinkedQueue<>();
            Queue<BillUserPosition> billUserPositions = new ConcurrentLinkedQueue<>();

            boolean needExtendQueryTime = (accountTypeEnum.isContract() || accountTypeEnum.equals(AccountTypeEnum.SPOT));
            BaseRequest startAssetsBaseRequest = getStartAssetsRequest(baseRequest);
            BaseRequest needQueryTimeBaseRequest = null;
            if (needExtendQueryTime) {
                //FIXME 2022-01-01 多查询一部分用户,目前有的新用户业务发生时间会比创建时间早1s,但是落在了两个时间间隔内
                needQueryTimeBaseRequest = getQueryUserInfoRequest(baseRequest);
            } else {
                needQueryTimeBaseRequest = getTimeQueryUserInfoRequest(accountTypeEnum, baseRequest);
            }
            pageResponse = accountAssetsServiceFactory.queryUserInfo(needQueryTimeBaseRequest, basePageRequest.getPageNo(), basePageRequest.getPageSize());
            if (apolloBizConfig.isInitTrackLogOpen()) {
                log.info("InitDataService queryUserInfo param is:{} pageNo:{} pageSize:{} result is:{}", JSONObject.toJSONString(needQueryTimeBaseRequest), basePageRequest.getPageNo(), basePageRequest.getPageSize(), JSONObject.toJSONString(pageResponse));
            }
            if (Objects.isNull(pageResponse)) {
                return;
            }
            List<UserInfoResult> userInfoResultList = pageResponse.getData();
            // 全局判重
            Set<Long> userIds = userInfoResultList.stream()
                    .filter(item -> {
                        // 10过滤掉demo用户
                        if (accountTypeEnum.getCode() == AccountTypeEnum.DEMO_SPOT.getCode()) {
                            if (!SiteCodeUtils.isDemo(item.getUserId())) {
                                return false;
                            }
                        } else if (accountTypeEnum.getCode() == AccountTypeEnum.P_SPOT.getCode()) {
                            if (!SiteUtil.checkPaptradingByUserId(item.getUserId())) {
                                return false;
                            }
                        }
                        // 重复判断
                        boolean exist = false;
                        if (alreadyInitUserIds.contains(item.getUserId())) {
                            exist = true;
                        } else {
                            alreadyInitUserIds.add(item.getUserId());
                        }
                        return !exist;
                    }).map(UserInfoResult::getUserId).collect(Collectors.toSet());
            List<List<Long>> partionUserIdLists = Lists.partition(new ArrayList<>(userIds), partitionSize);
            TaskVoidBatchResult queryResultIsEmpty = taskManager.forEachSubmitBatchAndWait(partionUserIdLists, (List<Long> uidList) -> {
                log.info("queryUserAssets query with accountType {} uids {} request {}", accountType, JSONObject.toJSONString(uidList), JSON.toJSONString(startAssetsBaseRequest));
                List<BillCoinUserProperty> billCoinUserPropertyList = new ArrayList<>();
                try {
                    billCoinUserPropertyList = accountAssetsServiceFactory.queryUserAssets(accountType, uidList, startAssetsBaseRequest, QueryUserAssetsSceneEnum.INIT);
                } catch (Exception e) {
                    log.error("queryUserAssets error ", e);
                    if (!EnvUtil.isLocalEnv()) {
                        throw e;
                    }
                }
                log.info("InitDataService.baseDataInitStart queryUserAssets accountType {} uids {} result {}", accountType, JSONObject.toJSONString(uidList), JSONObject.toJSONString(billCoinUserPropertyList));
                for (BillCoinUserProperty billCoinUserProperty : billCoinUserPropertyList) {
                    if (billCoinUserProperty.getCoinId() == null) {
                        log.info("queryUserAssets query result coin id is null {}", JSON.toJSONString(billCoinUserProperty));
                        continue;
                    }
                    BillCoinTypeUserProperty billCoinTypeUserProperty = BillCoinTypeUserProperty.initFromCoinUserProperty(billCoinUserProperty, new Date(bizTime));
                    billCoinTypeUserPropertyListToDB.add(billCoinTypeUserProperty);
                    billCoinUserPropertyListToDB.add(billCoinUserProperty);
                    // 仓位相关的信息在扩展字段 params里
                    if ((accountTypeEnum.haveUserPosition()) && BillBizUtil.isNotEmptyJsonStr(billCoinUserProperty.getParams())) {
                        JSONObject paramsJson = JSON.parseObject(billCoinUserProperty.getParams());
                        if (BillBizUtil.isNotEmptyJsonStr(paramsJson.getString("positionList"))) {
                            List<MixAccountAssetsExtension> extensions = JSON.parseArray(paramsJson.getString("positionList"), MixAccountAssetsExtension.class);
                            if (CollectionUtils.isNotEmpty(extensions)) {
                                for (MixAccountAssetsExtension extension : extensions) {
                                    BillUserPosition billUserPosition = new BillUserPosition();
                                    BeanUtils.copyProperties(extension, billUserPosition);
                                    billUserPosition.setSAvg(billUserPosition.getSAvg() != null ? billUserPosition.getSAvg() : BigDecimal.ZERO);
                                    billUserPosition.setLAvg(billUserPosition.getLAvg() != null ? billUserPosition.getLAvg() : BigDecimal.ZERO);
                                    billUserPosition.setSCount(billUserPosition.getSCount() != null ? billUserPosition.getSCount() : BigDecimal.ZERO);
                                    billUserPosition.setLCount(billUserPosition.getLCount() != null ? billUserPosition.getLCount() : BigDecimal.ZERO);
                                    billUserPosition.setUserId(billCoinUserProperty.getUserId());
                                    billUserPosition.setTokenId(extension.getTId());
                                    billUserPosition.setCoinId(billCoinUserProperty.getCoinId());
                                    billUserPosition.setSymbolId(extension.getSId());
                                    billUserPosition.setCheckOkTime(new Date(bizTime));
                                    billUserPosition.setBizTime(new Date(bizTime));
                                    billUserPosition.setCreateTime(new Date(bizTime));
                                    billUserPosition.setUpdateTime(new Date(bizTime));
                                    if (billUserPosition.getLCount().compareTo(BigDecimal.ZERO) != 0 || billUserPosition.getSCount().compareTo(BigDecimal.ZERO) != 0) {
                                        billUserPositions.add(billUserPosition);
                                    }
                                }
                            }
                        }
                    }
                }
            }, concurrent);

            if (queryResultIsEmpty != null && queryResultIsEmpty.getFails().size() > 0) {
                log.error("InitDataService queryResultIsEmpty.getFails().size={}", queryResultIsEmpty.getFails().size());
                return;
            }

            GroupByUtils.groupByCoinId(billCoinPropertyMap, billCoinUserPropertyListToDB, new Date(bizTime));
            GroupByUtils.groupByCoinIdType(billCoinTypePropertyMap, billCoinTypeUserPropertyListToDB, new Date(bizTime));
            // step2 批量落库，个人纬度
            billCoinUserPropertyService.batchInsert(new ArrayList<>(billCoinUserPropertyListToDB), accountType, accountParam);
            // billCoinUserPropertySnapshot快照表，也要初始化
            // step2 批量落库，个人纬度
            billCoinUserPropertySnapshotService.batchInsert(new ArrayList<>(billCoinUserPropertyListToDB), accountType, accountParam);
            // 资产快照落库
            billCoinUserPropertyAssetSnapshotService.batchInsert(new ArrayList<>(billCoinUserPropertyListToDB), accountType, accountParam);
            //  仓位初始化数据的存储(从params里取 scount和lcount)
            billUserPositionService.batchInsert(new ArrayList<>(billUserPositions), accountType, accountParam);

            log.info("InitDataService batch query user assets with maxId {}", pageResponse.getMaxId());

            if (pageResponse.isHasNextPage()) {
                baseRequest = baseRequest.clone();
                baseRequest.setMaxId(pageResponse.getMaxId());
            }
            setBasePageRequest(basePageRequest, pageSize, pageResponse);
        } while (pageResponse.isHasNextPage());


        // step3 聚合后的批量落盘逻辑
        log.info("init insert bill coin data is {}", JSONObject.toJSONString(billCoinPropertyMap.values()));
        billCoinPropertyService.batchInsert(new ArrayList<>(billCoinPropertyMap.values()), accountType, accountParam);
        billCoinTypePropertyService.batchInsert(new ArrayList<>(billCoinTypePropertyMap.values()), accountType, accountParam);

        // 更新billConfig
        BillConfig billConfig = new BillConfig();
        billConfig.setCheckOkTime(new Date(bizTime));
        billConfig.setSyncPos(0L);
        billConfig.setConsumeOffset(JSONObject.toJSONString(new ArrayList<>()));
        billConfig.setCreateTime(new Date());
        billConfig.setUpdateTime(new Date());
        billConfig.setInitTime(new Date(bizTime));
        billConfigService.batchInsert(List.of(billConfig), apolloBizConfig.getAccountType(), apolloBizConfig.getAccountParam());
        log.info("Init data service insert billConfig {}", JSONObject.toJSONString(billConfig));

        Date now = new Date();
        BillAllConfig billAllConfigOld = billAllConfigService.selectByTypeAndParam(apolloBizConfig.getAccountType(), apolloBizConfig.getAccountParam());
        if (billAllConfigOld != null) {
            billAllConfigOld.setCheckOkTime(new Date(bizTime));
            billAllConfigOld.setUpdateTime(now);
            billAllConfigService.updateByPrimaryKeySelective(billAllConfigOld);
        } else {
            BillAllConfig billAllConfig = new BillAllConfig();
            billAllConfig.setAccountType(apolloBizConfig.getAccountType());
            billAllConfig.setAccountParam(apolloBizConfig.getAccountParam());
            billAllConfig.setCheckOkTime(new Date(bizTime));
            billAllConfig.setCreateTime(now);
            billAllConfig.setUpdateTime(now);
            billAllConfig.setSyncPos(0L);
            billAllConfigService.batchInsert(List.of(billAllConfig));
        }
    }


    public boolean checkDataByAccountTypeContinuous(Long timeSlice, JSONArray accountTypes, String accountParam) {
        // 从local cache里获取对比时间，没有的情况下，才使用入参下发的checkTime
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        Set<Byte> checkLagSet = new HashSet<>();
        boolean totalResult = true;
        for (int i = 0; i < accountTypes.size(); i++) {
            boolean singleResult = checkDataByAccountType(timeSlice, accountTypes.getByte(i), accountParam);
            if (!singleResult) {
                checkLagSet.add(accountTypes.getByte(i));
            }
            totalResult = singleResult && totalResult;
        }
        // 根据步长，判定是否需要推送业务线当前对账过慢告警
        if (CollectionUtils.isNotEmpty(checkLagSet)) {
            alarmNotifyService.alarm(CHECK_DATA_CONTINUOUS, DateUtil.getDefaultDate(timeSlice), checkLagSet, DateUtil.getDefaultDate(timeSlice - globalBillConfig.getCheckStepGapThreshold() * BillConstants.FIVE_MINE_MIL_SEC));
        }
        return totalResult;
    }


    public boolean checkDataInAllContinuous(Long timeSlice, String assetAccountType, String assetAccountParam) {
        // 从local cache里获取对比时间，没有的情况下，才使用入参下发的checkTime
        return checkDataInAllV2(timeSlice, assetAccountType, assetAccountParam);
    }


    public boolean checkDataInAllV2(Long checkTime, String assetAccountType, String assetAccountParam) {
        boolean result = true;
        StringBuilder stringBuffer = new StringBuilder();
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        AssetsBillConfig assetsBillConfig = assetsBillConfigService.selectByTypeAndParam(assetAccountType, assetAccountParam);
        com.upex.reconciliation.service.dao.bill.entity.AssetsBillConfig oldAssetsBillConfig = oldAssetsBillConfigService.selectByTypeAndParam(assetAccountType, assetAccountParam);
        if (assetsBillConfig.getCheckOkTime().getTime() < checkTime || oldAssetsBillConfig.getCheckOkTime().getTime() < checkTime) {
            return false;
        }

        stringBuffer.append("总账数据对比:").append(assetAccountType).append(",时间:").append(DateUtil.getDefaultDateStr(new Date(checkTime))).append("\n");
        // 对比coin纬度期末值
        List<AssetsBillCoinProperty> assetsBillCoinProperties = assetsBillCoinPropertyService.selectAssetsByEndTime(new Date(checkTime), assetAccountType, assetAccountParam);
        List<com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinProperty> oldAssetsBillCoinProperties = oldAssetsBillCoinPropertyService.selectAssetsByEndTime(new Date(checkTime), assetAccountType, assetAccountParam);
        Map<Integer, com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinProperty> oldAssetsBillCoinMap = oldAssetsBillCoinProperties.stream().collect(Collectors.toMap(com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinProperty::getCoinId, (item) -> item));
        for (AssetsBillCoinProperty assetsBillCoinProperty : assetsBillCoinProperties) {
            boolean sendAlarm = false;
            com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinProperty oldAssetsBillCoinProperty = oldAssetsBillCoinMap.get(assetsBillCoinProperty.getCoinId());
            if (globalBillConfig.isCompareCoinPropertyNewOldSwitch() && !checkAssetInAllService.checkPropAssetsBillCoinProperty(oldAssetsBillCoinProperty, assetsBillCoinProperty)) {
                log.error("checkDataInAllV2 noMatch AssetsBillCoinProperty check info is {}", JSONObject.toJSONString(assetsBillCoinProperty));
                stringBuffer.append("总账期末对比不过 coinId:").append(assetsBillCoinProperty.getCoinId()).append(",老对账coin数据:").append(oldAssetsBillCoinProperty != null ? oldAssetsBillCoinProperty.getPropSum() : BigDecimal.ZERO).append(",新对账coin数据").append(assetsBillCoinProperty.getPropSum()).append("\n");
                sendAlarm = true;
            }
            // 对比coin_type纬度的change值
            List<AssetsBillCoinTypeProperty> assetsBillCoinTypeProperties = assetsBillCoinTypePropertyService.selectByCoinIdCheckTime(new Date(checkTime), assetsBillCoinProperty.getCoinId(), assetAccountType, assetAccountParam);
            List<com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinTypeProperty> oldAssetsBillCoinTypeProperties = oldAssetsBillCoinTypePropertyService.selectByCoinIdCheckTime(new Date(checkTime), assetsBillCoinProperty.getCoinId(), assetAccountType, assetAccountParam);
            Map<String, com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinTypeProperty> oldAssetsBillCoinTypeMap = oldAssetsBillCoinTypeProperties.stream().collect(Collectors.toMap(com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinTypeProperty::getBizType, (item) -> item));
            for (AssetsBillCoinTypeProperty assetsBillCoinTypeProperty : assetsBillCoinTypeProperties) {
                com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinTypeProperty oldAssetsBillCoinTypeProperty = oldAssetsBillCoinTypeMap.get(assetsBillCoinTypeProperty.getBizType());
                if (!checkAssetInAllService.checkPropAssetsBillCoinTypeProperty(oldAssetsBillCoinTypeProperty, assetsBillCoinTypeProperty)) {
                    log.error("checkDataInAllV2 noMatch AssetsBillCoinProperty check info is {}", JSONObject.toJSONString(assetsBillCoinProperty));
                    stringBuffer.append("总账增量change对比不过，biz_type:").append(assetsBillCoinTypeProperty.getBizType()).append(",老对账数据:").append(oldAssetsBillCoinTypeProperty != null ? oldAssetsBillCoinTypeProperty.getChangePropSum() : BigDecimal.ZERO).append(",新对账数据").append(assetsBillCoinTypeProperty.getChangePropSum()).append("\n");
                    sendAlarm = true;
                }
            }

            Set<String> newBizTypeSet = assetsBillCoinTypeProperties.stream().map(AssetsBillCoinTypeProperty::getBizType).collect(Collectors.toSet());
            List<com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinTypeProperty> onlyOldAssetsBillCoinTypeProperties = oldAssetsBillCoinTypeProperties.stream().filter(item -> !newBizTypeSet.contains(item.getBizType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(onlyOldAssetsBillCoinTypeProperties)) {
                for (com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinTypeProperty onlyOldAssetsBillCoinType : onlyOldAssetsBillCoinTypeProperties) {
                    if (onlyOldAssetsBillCoinType.getChangePropSum().compareTo(BigDecimal.ZERO) != 0) {
                        stringBuffer.append("总账增量change对比不过，biz_type:").append(onlyOldAssetsBillCoinType.getBizType()).append(",老对账biz_type数据非0: ").append(onlyOldAssetsBillCoinType.getChangePropSum()).append(",新对账biz_type数据为null").append("\n");
                        sendAlarm = true;
                    }
                }
            }
            if (sendAlarm) {
                result = false;
                alarmNotifyService.alarm(AlarmParam.builder().content(stringBuffer.toString()).build());
            }
        }

        Set<Integer> newCoinIdSet = assetsBillCoinProperties.stream().map(AssetsBillCoinProperty::getCoinId).collect(Collectors.toSet());
        List<com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinProperty> onlyOldAssetsBillCoinProperties = oldAssetsBillCoinProperties.stream().filter(item -> !newCoinIdSet.contains(item.getCoinId())).collect(Collectors.toList());
        if (globalBillConfig.isCompareCoinPropertyOnlyOldSwitch() && CollectionUtils.isNotEmpty(onlyOldAssetsBillCoinProperties)) {
            for (com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinProperty onlyOldAssetsBillCoin : onlyOldAssetsBillCoinProperties) {
                boolean sendAlarm = false;
                if (onlyOldAssetsBillCoin.getPropSum().compareTo(BigDecimal.ZERO) != 0) {
                    stringBuffer.append("总账期末对比不过 coinId:").append(onlyOldAssetsBillCoin.getCoinId()).append(",老对账coin数据非0: ").append(onlyOldAssetsBillCoin.getPropSum()).append(",新对账coin数据为null").append("\n");
                    sendAlarm = true;
                }
                if (sendAlarm) {
                    result = false;
                    alarmNotifyService.alarm(AlarmParam.builder().content(stringBuffer.toString()).build());
                }
            }
        }
        log.info("checkDataByAccountType check finished accountType:{}, checkTime {}", assetAccountType, DateUtil.getDefaultDate(checkTime));
        return result;
    }

    /**
     * 业务线纬度数据对比
     * 针对入出不平场景定位原因
     *
     * @param checkTime
     * @param accountType
     * @param accountParam
     */
    public boolean checkDataByAccountType(Long checkTime, Byte accountType, String accountParam) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("checkDataByAccountType check start accountType:{}", accountType);
        boolean result = true;
        BillConfig billConfig = billConfigService.selectByTypeAndParamAndCheckTime(accountType, accountParam, new Date(checkTime));
        if (billConfig == null) {
            return false;
        }
        OldBillConfig oldBillConfig = oldBillConfigService.selectByTypeAndParam(accountType, accountParam);
        if (oldBillConfig.getCheckOkTime().getTime() < checkTime) {
            return false;
        }
        ApolloReconciliationBizConfig apolloReconciliationBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        List<CoinComparisonToleranceVo> coinComparisonToleranceList = apolloReconciliationBizConfig.getCoinComparisonToleranceList();
        Map<Integer, BigDecimal> coinComparisonToleranceMap = coinComparisonToleranceList.stream().collect(Collectors.toMap(CoinComparisonToleranceVo::getCoinId, CoinComparisonToleranceVo::getTolerance));
        BillConfig firstBillConfig = billConfigService.selectFirstByTypeAndParam(accountType, accountParam);
        List<BillCoinProperty> newCoinList = billCoinPropertyService.selectRangeCheckTimeRecord(Integer.valueOf(accountType), accountParam, new Date(checkTime));
        List<com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty> oldCoinList = oldBillCoinService.selectByCheckTime(Byte.valueOf(accountType), accountParam, new Date(checkTime));
        for (BillCoinProperty billCoinProperty : newCoinList) {
            StringBuilder stringBuffer = new StringBuilder();
            stringBuffer.append("新老数据对比，业务线:").append(accountType).append(",时间:").append(DateUtil.getDefaultDateStr(new Date(checkTime))).append("coinId:").append(billCoinProperty.getCoinId()).append("\n");
            boolean sendAlarm = false;
            // 循环coin
            com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty oldBillCoinProperty = oldBillCoinService.selectLastByCoinId(accountType.intValue(), accountParam, billCoinProperty.getCoinId(), new Date(checkTime));
            DataCalResultDTO coinDataCalResultDTO = new DataCalResultDTO();
            if (apolloReconciliationBizConfig.isCompareCoinPropertyNewOldSwitch() && !accountAssetsServiceFactory.getBillCheckService(accountType).checkCoinAssetsByAccountType(oldBillCoinProperty, billCoinProperty, coinComparisonToleranceMap, coinDataCalResultDTO)) {
                log.error("checkDataByAccountType noMatch BillCoinProperty check oldBillCoinProperty is {} , new {} , coinDataCalResultDTO {}", JSONObject.toJSONString(oldBillCoinProperty), JSONObject.toJSONString(billCoinProperty), JSONObject.toJSONString(coinDataCalResultDTO));
                sendAlarm = true;
            }
            stringBuffer.append("老对账coin数据:").append(coinDataCalResultDTO.getFactor1()).append("\n");
            stringBuffer.append("新对账coin数据:").append(coinDataCalResultDTO.getFactor2()).append("\n");

            // 对比coin_type
            if (Objects.equals(billConfig.getId(), firstBillConfig.getId())) {
                // 是第一条，则是初始化的数据比对
                // 初始化的所有数据会被汇总到other内，则直接比较新库的other这条记录是否和老库的加总相等
                List<BillCoinTypeProperty> newCoinTypeList = billCoinTypePropertyService.selectBizTypeCoinId(Integer.valueOf(accountType), accountParam, billConfig.getCheckOkTime(), billCoinProperty.getCoinId(), BillConstants.DEFAULT_BILL_BIZ_TYPE);
                for (BillCoinTypeProperty billCoinTypeProperty : newCoinTypeList) {
                    List<com.upex.reconciliation.service.dao.bill.entity.BillCoinTypeProperty> oldBillCoinTypePropertyList = oldBillCoinTypeService.selectRangeCheckTimeRecord(billCoinTypeProperty.getCoinId(), billConfig.getCheckOkTime(), accountType, accountParam);
                    DataCalResultDTO coinTypeDataCalResultDTO = new DataCalResultDTO();
                    if (!accountAssetsServiceFactory.getBillCheckService(accountType).checkCoinTypeInitAssetsByAccountType(oldBillCoinTypePropertyList, billCoinTypeProperty, coinComparisonToleranceMap, coinTypeDataCalResultDTO)) {
                        log.error("checkDataByAccountType noMatch BillCoinTypeProperty check init info is {}", JSONObject.toJSONString(billCoinTypeProperty));
                        stringBuffer.append("初始化比对 bizType:").append(billCoinTypeProperty.getBizType()).append(",老对账coin_type数据:").append(coinDataCalResultDTO.getFactor1()).append("\n");
                        stringBuffer.append("初始化比对 bizType:").append(billCoinTypeProperty.getBizType()).append(",新对账coin_type数据:").append(coinDataCalResultDTO.getFactor2()).append("\n");
                        sendAlarm = true;
                    }
                }
            } else {
                // 如果不是第一条，则比对响应的change值
                List<BillCoinTypeProperty> newCoinTypeList = billCoinTypePropertyService.selectByCoinIdCheckTime(Integer.valueOf(accountType), accountParam, billCoinProperty.getCoinId(), billConfig.getCheckOkTime());
                List<com.upex.reconciliation.service.dao.bill.entity.BillCoinTypeProperty> oldBillCoinTypePropertyList = oldBillCoinTypeService.selectRangeCheckTimeRecord(billCoinProperty.getCoinId(), billConfig.getCheckOkTime(), accountType, accountParam);
                Map<String, com.upex.reconciliation.service.dao.bill.entity.BillCoinTypeProperty> oldBillCoinTypePropertyMap = oldBillCoinTypePropertyList.stream().collect(Collectors.toMap(com.upex.reconciliation.service.dao.bill.entity.BillCoinTypeProperty::getBizType, Function.identity(), (key1, key2) -> key2));
                for (BillCoinTypeProperty billCoinTypeProperty : newCoinTypeList) {
                    com.upex.reconciliation.service.dao.bill.entity.BillCoinTypeProperty oldBillCoinTypeProperty = oldBillCoinTypePropertyMap.get(billCoinTypeProperty.getBizType());
                    if (!accountAssetsServiceFactory.getBillCheckService(accountType).checkCoinTypeAssetsByAccountType(oldBillCoinTypeProperty, billCoinTypeProperty)) {
                        log.error("checkDataByAccountType noMatch BillCoinTypeProperty check increment oldBillCoinTypeProperty is {} , new {}", JSONObject.toJSONString(oldBillCoinTypeProperty), JSONObject.toJSONString(billCoinTypeProperty));
                        if (oldBillCoinTypeProperty != null) {
                            stringBuffer.append("增量比对 bizType:").append(billCoinTypeProperty.getBizType()).append(",老对账coin_type数据,changeProp1:").append(oldBillCoinTypeProperty.getChangeProp1()).append(",changeProp2:").append(oldBillCoinTypeProperty.getChangeProp2()).append(",changeProp3:").append(oldBillCoinTypeProperty.getChangeProp3()).append(",changeProp4:").append(oldBillCoinTypeProperty.getChangeProp4()).append(",changeProp5:").append(oldBillCoinTypeProperty.getChangeProp5()).append("\n");
                        } else {
                            stringBuffer.append("增量比对 bizType:").append(billCoinTypeProperty.getBizType()).append(",老对账coin_type数据null").append("\n");
                        }
                        stringBuffer.append("增量比对 bizType:").append(billCoinTypeProperty.getBizType()).append(",新对账coin_type数据,changeProp1:").append(billCoinTypeProperty.getChangeProp1()).append(",changeProp2:").append(billCoinTypeProperty.getChangeProp2()).append(",changeProp3:").append(billCoinTypeProperty.getChangeProp3()).append(",changeProp4:").append(billCoinTypeProperty.getChangeProp4()).append(",changeProp5:").append(billCoinTypeProperty.getChangeProp5()).append("\n");
                        sendAlarm = true;
                    }
                }

                // 取新老各自的 bizType组成的set，set如果有差集，则当做异常情况处理
                Set<String> newBizTypeSet = newCoinTypeList.stream().map(BillCoinTypeProperty::getBizType).collect(Collectors.toSet());
                List<com.upex.reconciliation.service.dao.bill.entity.BillCoinTypeProperty> onlyOldBillCoinTypePropertyList = oldBillCoinTypePropertyList.stream().filter(item -> !newBizTypeSet.contains(item.getBizType())).collect(Collectors.toList());
                // 仅老bill有的bizType，内存对账为null
                if (CollectionUtils.isNotEmpty(onlyOldBillCoinTypePropertyList)) {
                    for (com.upex.reconciliation.service.dao.bill.entity.BillCoinTypeProperty onlyOldBillCoinTypeProperty : onlyOldBillCoinTypePropertyList) {
                        if (onlyOldBillCoinTypeProperty.getChangePropSum().compareTo(BigDecimal.ZERO) != 0) {
                            stringBuffer.append("增量比对 bizType:").append(onlyOldBillCoinTypeProperty.getBizType()).append(",老对账coin_type数据非0,changeProp1:").append(onlyOldBillCoinTypeProperty.getChangeProp1()).append(",changeProp2:").append(onlyOldBillCoinTypeProperty.getChangeProp2()).append(",changeProp3:").append(onlyOldBillCoinTypeProperty.getChangeProp3()).append(",changeProp4:").append(onlyOldBillCoinTypeProperty.getChangeProp4()).append(",changeProp5:").append(onlyOldBillCoinTypeProperty.getChangeProp5()).append(",新对账为null").append("\n");
                            sendAlarm = true;
                        }
                    }
                }
            }

            if (sendAlarm) {
                result = false;
                alarmNotifyService.alarm(AlarmParam.builder().content(stringBuffer.toString()).build());
            }
        }

        Set<Integer> newCoinIdSet = newCoinList.stream().map(BillCoinProperty::getCoinId).collect(Collectors.toSet());
        List<com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty> onlyOldCoinList = oldCoinList.stream().filter(item -> !newCoinIdSet.contains(item.getCoinId())).collect(Collectors.toList());
        // 仅有老bill有的coin，内存对账的数据库没有值，此时老bill需要为0
        if (apolloReconciliationBizConfig.isCompareCoinPropertyOnlyOldSwitch() && CollectionUtils.isNotEmpty(onlyOldCoinList)) {
            for (com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty oldBillCoinProperty : onlyOldCoinList) {
                if (oldBillCoinProperty.getPropSum().compareTo(BigDecimal.ZERO) != 0) {
                    StringBuilder stringBuffer = new StringBuilder();
                    stringBuffer.append("新老数据对比，业务线:").append(accountType).append(",时间:").append(DateUtil.getDefaultDateStr(new Date(checkTime))).append("coinId:").append(oldBillCoinProperty.getCoinId()).append("\n");
                    stringBuffer.append("老对账coin数据有数据：").append(oldBillCoinProperty.getPropSum()).append("新coin为空").append("\n");
                    alarmNotifyService.alarm(AlarmParam.builder().content(stringBuffer.toString()).build());
                    result = false;
                }
            }
        }

        log.info("checkDataByAccountType check finished accountType:{}, checkTime {}, result {}, time consume {}", accountType, DateUtil.getDefaultDate(checkTime), result, stopwatch.stop());
        return result;
    }


    /**
     * 初始化数据对比job
     *
     * @param accountType
     * @param pageSize
     */
    public void checkInitData(Long bizTime, Byte accountType, Integer pageSize, String accountParam, Integer concurrent) {
        // 资产对比
        // step1 逐条对比
        // 以 bill_user 表为样本，取里边的所有userid
        // userid and check_time <= biz_time 的最后一期数据 Bill_Coin_User_property 表

        //  总coin 对比 过滤为0的数据  新比老
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("billCoinService.selectRangeCheckTimeRecord");
        List<BillCoinProperty> newCoinList = billCoinPropertyService.selectRangeCheckTimeRecord(Integer.valueOf(accountType), accountParam, new Date(bizTime));
        stopWatch.stop();
        if (CollectionUtils.isNotEmpty(newCoinList)) {
            for (BillCoinProperty billCoinProperty : newCoinList) {
                stopWatch.start("oldBillCoinService.selectLastByCoinId");
                com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty oldBillCoinProperty = oldBillCoinService.selectLastByCoinId(accountType.intValue(), accountParam, billCoinProperty.getCoinId(), new Date(bizTime));
                stopWatch.stop();
                if (oldBillCoinProperty == null) {
                    log.info("data check error new bill coin is accountType:{} : old coin is null", accountType, JSONObject.toJSONString(billCoinProperty));
                } else if (oldBillCoinProperty.isPropZero()) {
                    log.info("data check info isPropZero new bill coin is accountType:{} : old coin is {}", accountType, JSONObject.toJSONString(billCoinProperty), JSONObject.toJSONString(oldBillCoinProperty));
                } else if (!accountAssetsServiceFactory.getBillCheckService(accountType).checkInitCoinAssets(oldBillCoinProperty, billCoinProperty)) {
                    log.info("data check error difference BillCoin old prop {} :{}, new prop {}", accountType, JSONObject.toJSONString(oldBillCoinProperty), JSONObject.toJSONString(billCoinProperty));
                }
            }
        }
        log.info("data check error check coin exec time {}", stopWatch.prettyPrint());

        Long minId = 0L;
        int count = 0;
        int failCount = 0;
        while (true) {
            log.info("data check error while start {} {} {}", minId, count, failCount);
            StopWatch userCoinStopWatch = new StopWatch();
            userCoinStopWatch.start("oldBillUserService.selectUserIdList");
            List<BillUser> billUsers = oldBillUserService.selectUserIdList(accountType, accountParam, minId, pageSize);
            userCoinStopWatch.stop();
            if (CollectionUtils.isEmpty(billUsers)) {
                break;
            }
            userCoinStopWatch.start("selectAssetListByUserId");
            List<Long> uids = billUsers.stream().map(BillUser::getUserId).collect(Collectors.toList());
            Queue<com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty> billCoinUserPropertyListToDB = new ConcurrentLinkedQueue<>();
            taskManager.forEachSubmitBatchAndWait(uids, (Long uid) -> {
                try {
                    List<com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty> oldBillCoinUserResult = oldBillCoinUserService.selectAssetListByUserId(uid, new Date(bizTime), accountType, accountParam);
                    oldBillCoinUserResult = oldBillCoinUserResult.stream()
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(oldBillCoinUserResult)) {
                        oldBillCoinUserResult.forEach(item -> {
                            billCoinUserPropertyListToDB.add(item);
                        });
                    }
                    log.info("checkInitData user_id = {}", uid);
                } catch (Exception e) {
                    log.error("checkInitData", e);
                }
            }, concurrent);
            Map<Long, com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty> oldUserMap = billCoinUserPropertyListToDB.
                    stream().collect(Collectors.toMap(com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty::getUserId, Function.identity(), (key1, key2) -> key2));
            List<Long> missUids = new ArrayList<Long>();
            for (Long uid : uids) {
                if (oldUserMap.get(uid) == null) {
                    missUids.add(uid);
                }
            }
            userCoinStopWatch.stop();
            log.info("data check error while end {} {} {}", minId, count, failCount);
            Map<Integer, Collection<com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty>> oldBillCoinUserMap = new HashMap<>();
            for (com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty oldBillCoinUser : billCoinUserPropertyListToDB) {
                Collection<com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty> valueList =
                        oldBillCoinUserMap.computeIfAbsent(oldBillCoinUser.getCoinId(), item -> new ArrayList<>());
                valueList.add(oldBillCoinUser);
            }

            if (MapUtils.isNotEmpty(oldBillCoinUserMap)) {
                for (Map.Entry<Integer, Collection<com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty>> entry : oldBillCoinUserMap.entrySet()) {
                    Collection<com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty> oldBillCoinUserList = entry.getValue();
                    Map<Long, com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty> oldMap = oldBillCoinUserList.stream().collect(Collectors.toMap(com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty::getUserId, Function.identity(), (key1, key2) -> key2));
                    userCoinStopWatch.start("billCoinUserPropertyService.selectByIds");
                    List<BillCoinUserProperty> billCoinUserPropertyList = billCoinUserPropertyService.selectByIds(new Date(bizTime), entry.getKey(), new ArrayList<>(oldMap.keySet()), accountType, accountParam);
                    userCoinStopWatch.stop();
                    if (CollectionUtils.isEmpty(billCoinUserPropertyList)) {
                        log.info("data check error BillCoinUser is {} {}", accountType, JSONObject.toJSONString(oldBillCoinUserList));
                        failCount += oldBillCoinUserList.size();
                        for (com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty property : oldBillCoinUserList) {
                            if (!property.isPropZero()) {
                                log.error("data check error difference BillCoinUser accountType:{} userId:{}, coid_id:{},  OldeDate is {}, newDate is NUll", accountType, property.getUserId(), property.getCoinId(), JSONObject.toJSONString(property));
                            }
                        }

                        continue;
                    }
                    Map<Long, BillCoinUserProperty> newMap = billCoinUserPropertyList.stream().collect(Collectors.toMap(BillCoinUserProperty::getUserId, Function.identity(), (key1, key2) -> key2));
                    List<Long> usedUsers = new ArrayList<Long>();
                    for (Map.Entry<Long, com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty> oldEntry : oldMap.entrySet()) {
                        Long userId = oldEntry.getKey();
                        com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty oldEntryValue = oldEntry.getValue();
                        BillCoinUserProperty newProp = newMap.get(userId);
                        usedUsers.add(userId);
                        if (newProp == null) {
                            log.error("data check error difference BillCoinUser accountType:{} userId:{}, coid_id:{},  OldeDate is {}, newDate is NUll", accountType, oldEntryValue.getUserId(), oldEntryValue.getCoinId(), JSONObject.toJSONString(oldEntryValue));
                            failCount += 1;
                        } else if (!accountAssetsServiceFactory.getBillCheckService(accountType).checkInitAssets(oldEntryValue, newProp)) {
                            log.error("data check error difference BillCoinUser accountType:{} userId:{}, coid_id:{},  OldeDate is {}, newDate is {}",
                                    accountType, oldEntryValue.getUserId(), oldEntryValue.getCoinId(), JSONObject.toJSONString(oldEntryValue), JSONObject.toJSONString(newProp));
                            failCount += 1;
                        }
                    }

                    newMap.forEach((k, v) -> {
                        if (!usedUsers.contains(k)) {
                            log.error("data check error difference BillCoinUser accountType:{} userId:{}, coid_id:{},  OldeDate is NUll, newDate is {}",
                                    accountType, v.getUserId(), v.getCoinId(), JSONObject.toJSONString(v));
                        }
                        if (missUids.contains(k) && !v.isPropZero()) {
                            log.error("data check error difference BillCoinUser accountType:{} userId:{}, coid_id:{},  OldeDate is NUll, newDate is {}",
                                    accountType, v.getUserId(), v.getCoinId(), JSONObject.toJSONString(v));
                        }
                    });
                    if (oldMap.size() != newMap.size()) {
                        log.info("data check error old and new map size error , coin id {} {}, new size {} , old size {}", accountType, entry.getKey(), newMap.size(), oldMap.size());
                    }
                }
            }
            minId = billUsers.get(billUsers.size() - 1).getId();
            count += pageSize;
            log.info("finished checked total user count {} {} , fail check count {} stopwatch {}", accountType, count, failCount, userCoinStopWatch.prettyPrint());
        }

        log.info("checkInitData data check finished {}", accountType);
    }

    /**
     * 进行分页动作
     *
     * @param basePageRequest
     * @param pageResponse
     */
    private void setBasePageRequest(BasePageRequest basePageRequest, Integer pageSizeParam, PageResponse<UserInfoResult> pageResponse) {
        Integer pageSize = pageSizeParam;
        if (CollectionUtils.isNotEmpty(pageResponse.getData()) && Objects.nonNull(pageResponse.getData().get(0).getCreateTime()) && pageResponse.getData().get(0).getCreateTime().getTime() == pageResponse.getMaxId()) {
            pageSize = basePageRequest.getPageSize() + BillConstants.INCREASE_USERS_PAGE_SIZE_50;
        }
        basePageRequest.setPageNo(basePageRequest.getPageNo());
        basePageRequest.setPageSize(pageSize);
    }


    /**
     * 期初资产请求对象
     *
     * @param baseRequest
     * @return
     */
    private BaseRequest getStartAssetsRequest(BaseRequest baseRequest) {
        BaseRequest startAssetsRequest = new BaseRequest();
        startAssetsRequest.setMaxId(baseRequest.getMaxId());
        startAssetsRequest.setAccountParam(baseRequest.getAccountParam());
        startAssetsRequest.setAccountType(baseRequest.getAccountType());
        startAssetsRequest.setBeginTime(baseRequest.getBeginTime());
        startAssetsRequest.setEndTime(baseRequest.getEndTime());
        return startAssetsRequest;
    }

    /**
     * 获取用户请求对象
     * 使用createTime作为maxId的需要-1
     *
     * @param baseRequest
     * @return
     */
    private BaseRequest getTimeQueryUserInfoRequest(AccountTypeEnum accountTypeEnum, BaseRequest baseRequest) {
        if (!accountTypeEnum.isMinusOneAccount()) {
            return baseRequest;
        }
        BaseRequest resultRequest = new BaseRequest();
        // maxId -1
        resultRequest.setMaxId(baseRequest.getMaxId() - 1);
        resultRequest.setAccountParam(baseRequest.getAccountParam());
        resultRequest.setAccountType(baseRequest.getAccountType());
        resultRequest.setBeginTime(baseRequest.getBeginTime());
        resultRequest.setEndTime(baseRequest.getEndTime());

        return resultRequest;
    }

    /**
     * 获取用户请求对象
     *
     * @param baseRequest
     * @return
     */
    private BaseRequest getQueryUserInfoRequest(BaseRequest baseRequest) {
        BaseRequest startAssetsRequest = new BaseRequest();
        startAssetsRequest.setMaxId(baseRequest.getMaxId() - 1);
        startAssetsRequest.setAccountParam(baseRequest.getAccountParam());
        startAssetsRequest.setAccountType(baseRequest.getAccountType());
        startAssetsRequest.setBeginTime(baseRequest.getBeginTime());
        //多查一个时间间隔
        startAssetsRequest.setEndTime(baseRequest.getEndTime());
        return startAssetsRequest;
    }

    /**
     * 对比新老数据
     *
     * @param accountType
     */
    public void checkBillCoinProperty(Byte accountType, String accountParam, Long limit, Long checkTime, String checkType) {
        // 获取最后xxx个时间片数据
        List<BillConfig> billConfigList = billConfigService.selectLastByLimit(accountType, accountParam, limit, checkTime != null ? new Date(checkTime) : null);
        if (CollectionUtils.isEmpty(billConfigList)) {
            log.info("InitDataService billConfigService.selectLastByLimit is empty {}", accountType);
            return;
        }

        for (int i = 0; i < billConfigList.size(); i++) {
            BillConfig billConfig = billConfigList.get(i);
            if ("all".equalsIgnoreCase(checkType) || "coin".equalsIgnoreCase(checkType)) {
                //  总coin 对比 过滤为0的数据  新比老
                List<BillCoinProperty> newCoinList = billCoinPropertyService.selectRangeCheckTimeRecord(Integer.valueOf(accountType), accountParam, billConfig.getCheckOkTime());
                if (CollectionUtils.isNotEmpty(newCoinList)) {
                    List<Integer> coinIds = newCoinList.stream().map(BillCoinProperty::getCoinId).collect(Collectors.toList());
                    List<com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty> oldBillCoinPropertyList = oldBillCoinService.selectLastByCoinIds(accountType.intValue(), accountParam, coinIds, billConfig.getCheckOkTime());
                    if (CollectionUtils.isNotEmpty(oldBillCoinPropertyList)) {
                        Map<Integer, com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty> oldBillCoinPropertyMap = oldBillCoinPropertyList.stream().collect(Collectors.toMap(com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty::getCoinId, Function.identity()));
                        for (BillCoinProperty billCoinProperty : newCoinList) {
                            com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty oldBillCoinProperty = oldBillCoinPropertyMap.get(billCoinProperty.getCoinId());
                            if (oldBillCoinProperty == null) {
                                log.error("InitDataService.checkBillCoinProperty data check accountType {} date {} error new bill coin is {} , old coin is null", accountType, DateUtil.getMillisecondDateStr(billConfig.getCheckOkTime()), JSONObject.toJSONString(billCoinProperty));
                            } else if (oldBillCoinProperty.isPropZero()) {
                                log.info("InitDataService.checkBillCoinProperty data check accountType {} date {} info isPropZero new bill coin is {} , old coin is {}", accountType, DateUtil.getMillisecondDateStr(billConfig.getCheckOkTime()), JSONObject.toJSONString(billCoinProperty), JSONObject.toJSONString(oldBillCoinProperty));
                            } else {
                                if (!accountAssetsServiceFactory.getBillCheckService(accountType).checkInitCoinAssets(oldBillCoinProperty, billCoinProperty)) {
                                    log.error("InitDataService.checkBillCoinProperty data check accountType {} date {} error difference BillCoin old prop : {} , new prop: {}", accountType, DateUtil.getMillisecondDateStr(billConfig.getCheckOkTime()), JSONObject.toJSONString(oldBillCoinProperty), JSONObject.toJSONString(billCoinProperty));
                                }
                                if (!accountAssetsServiceFactory.getBillCheckService(accountType).checkInitCoinChangeAssets(oldBillCoinProperty, billCoinProperty)) {
                                    log.error("InitDataService.checkBillCoinProperty change data check accountType {} date {} error difference BillCoin old prop : {} , new prop: {}", accountType, DateUtil.getMillisecondDateStr(billConfig.getCheckOkTime()), JSONObject.toJSONString(oldBillCoinProperty), JSONObject.toJSONString(billCoinProperty));
                                }
                            }
                        }
                    } else {
                        log.error("InitDataService.checkBillCoinProperty data check accountType {} date {} error oldBillCoinPropertyList is empty accountType={} newCoinList {}", accountType, DateUtil.getMillisecondDateStr(billConfig.getCheckOkTime()), accountType, JSONObject.toJSONString(newCoinList));
                    }
                }
            }


            if ("all".equalsIgnoreCase(checkType) || "coin_type".equalsIgnoreCase(checkType)) {
                // 比对coin_type
                List<BillCoinTypeProperty> newCoinTypeList = billCoinTypePropertyService.selectRangeCheckTimeRecord(Integer.valueOf(accountType), accountParam, billConfig.getCheckOkTime());
                if (CollectionUtils.isNotEmpty(newCoinTypeList)) {
                    List<Integer> coinIds = newCoinTypeList.stream().map(BillCoinTypeProperty::getCoinId).collect(Collectors.toList());
                    List<com.upex.reconciliation.service.dao.bill.entity.BillCoinTypeProperty> oldBillCoinTypePropertyList = oldBillCoinTypeService.selectLastByCoinIds(accountType.intValue(), accountParam, coinIds, billConfig.getCheckOkTime());
                    if (CollectionUtils.isNotEmpty(oldBillCoinTypePropertyList)) {
                        Map<String, com.upex.reconciliation.service.dao.bill.entity.BillCoinTypeProperty> oldBillCoinTypePropertyMap = oldBillCoinTypePropertyList.stream().collect(Collectors.toMap(item -> item.getCoinId() + "#" + item.getBizType(), Function.identity()));
                        for (BillCoinTypeProperty billCoinTypeProperty : newCoinTypeList) {
                            com.upex.reconciliation.service.dao.bill.entity.BillCoinTypeProperty oldBillCoinTypeProperty = oldBillCoinTypePropertyMap.get(billCoinTypeProperty.getCoinId() + "#" + billCoinTypeProperty.getBizType());
                            if (oldBillCoinTypeProperty == null) {
                                log.error("InitDataService.checkBillCoinTypeProperty data check accountType {} date {} error new bill coin is {} : old coin is null", accountType, DateUtil.getMillisecondDateStr(billConfig.getCheckOkTime()), JSONObject.toJSONString(billCoinTypeProperty));
                            } else if (oldBillCoinTypeProperty.isPropZero()) {
                                log.info("InitDataService.checkBillCoinTypeProperty data check accountType {} date {} info isPropZero new bill coin is {} : old coin is {}", accountType, DateUtil.getMillisecondDateStr(billConfig.getCheckOkTime()), JSONObject.toJSONString(billCoinTypeProperty), JSONObject.toJSONString(oldBillCoinTypeProperty));
                            } else {
                                if (!accountAssetsServiceFactory.getBillCheckService(accountType).checkInitCoinTypeChangeAssets(oldBillCoinTypeProperty, billCoinTypeProperty)) {
                                    log.error("InitDataService.checkBillCoinTypeProperty change data check accountType {} date {} error difference BillCoin old prop: {}, new prop {}", accountType, DateUtil.getMillisecondDateStr(billConfig.getCheckOkTime()), JSONObject.toJSONString(oldBillCoinTypeProperty), JSONObject.toJSONString(billCoinTypeProperty));
                                }
                            }
                        }
                    } else {
                        log.error("InitDataService.checkBillCoinTypeProperty data check accountType {} date {} error oldBillCoinPropertyList is empty , newCoinList {}", accountType, DateUtil.getMillisecondDateStr(billConfig.getCheckOkTime()), JSONObject.toJSONString(newCoinTypeList));
                    }
                }
            }
        }
        log.info("InitDataService.checkBillCoinProperty data check error check coin accountType={} ", accountType);
    }

    /**
     * 对比内存对账用户资产是否和业务用户资产一致
     *
     * @param accountType
     * @param checkTime
     */
    public void checkUserCoinProperty(Byte accountType, Long checkTime) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        if (checkTime == null) {
            List<BillConfig> billConfigs = billConfigService.selectLastByLimit(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), 1L, null);
            if (CollectionUtils.isEmpty(billConfigs)) {
                log.info("InitDataService.checkUserCoinProperty billConfigService.selectLastByLimit is null accountType:{}", accountTypeEnum.getCode());
                return;
            }
            checkTime = billConfigs.get(0).getCheckOkTime().getTime();
        }
        Long maxId = 1L;
        Integer concurrent = 20;
        Integer pageSize = 1000;
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setPageNo(0);
        basePageRequest.setPageSize(pageSize);
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setBeginTime(checkTime - 1L);
        baseRequest.setEndTime(checkTime);
        baseRequest.setAccountType(accountType);
        baseRequest.setAccountParam(accountTypeEnum.getAccountParam());
        baseRequest.setMaxId(maxId);
        PageResponse<UserInfoResult> pageResponse = null;
        // 全部判重队列
        Set<Long> alreadyInitUserIds = new HashSet<Long>();
        Long startTime = System.currentTimeMillis();
        Integer dataCount = 0;
        Integer errorCount = 0;
        do {
            List<String> errorLogList = new ArrayList<>();
            Integer partitionSize = apolloBizConfig.getInitPartitionSize();
            Queue<BillCoinUserProperty> bizBillCoinUserPropertyList = new ConcurrentLinkedQueue<>();
            boolean needExtendQueryTime = (accountTypeEnum.isContract() || accountTypeEnum.equals(AccountTypeEnum.SPOT));
            BaseRequest startAssetsBaseRequest = getStartAssetsRequest(baseRequest);
            BaseRequest needQueryTimeBaseRequest = needExtendQueryTime ? getQueryUserInfoRequest(baseRequest) : getTimeQueryUserInfoRequest(accountTypeEnum, baseRequest);
            pageResponse = accountAssetsServiceFactory.queryUserInfo(needQueryTimeBaseRequest, basePageRequest.getPageNo(), basePageRequest.getPageSize());
            if (apolloBizConfig.isInitTrackLogOpen()) {
                log.info("InitDataService queryUserInfo param is {} result is {}", JSONObject.toJSONString(needQueryTimeBaseRequest), JSONObject.toJSONString(pageResponse));
            }
            if (Objects.isNull(pageResponse)) {
                log.info("InitDataService queryUserInfo pageResponse is null accountType:{}", accountType);
                return;
            }
            List<UserInfoResult> userInfoResultList = pageResponse.getData();
            if (CollectionUtils.isEmpty(userInfoResultList)) {
                continue;
            }
            // 全局判重
            Set<Long> userIds = userInfoResultList.stream()
                    .filter(item -> {
                        boolean exist = false;
                        if (alreadyInitUserIds.contains(item.getUserId())) {
                            exist = true;
                        } else {
                            alreadyInitUserIds.add(item.getUserId());
                        }
                        return !exist;
                    })
                    .map(UserInfoResult::getUserId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(userIds)) {
                continue;
            }
            List<List<Long>> partionUserIdLists = Lists.partition(new ArrayList<>(userIds), partitionSize);
            TaskVoidBatchResult queryResultIsEmpty = taskManager.forEachSubmitBatchAndWait(partionUserIdLists, (List<Long> uidList) -> {
                List<BillCoinUserProperty> billCoinUserPropertyList = accountAssetsServiceFactory.queryUserAssets(accountType, uidList, startAssetsBaseRequest, QueryUserAssetsSceneEnum.INIT);
                for (BillCoinUserProperty billCoinUserProperty : billCoinUserPropertyList) {
                    if (billCoinUserProperty.getCoinId() == null) {
                        log.info("queryUserAssets query result coin id is null {}", JSON.toJSONString(billCoinUserProperty));
                        continue;
                    }
                    bizBillCoinUserPropertyList.add(billCoinUserProperty);
                }
            }, concurrent);
            if (queryResultIsEmpty != null && queryResultIsEmpty.getFails().size() > 0) {
                log.info("InitDataService queryResultIsEmpty error accountType:{} size:{}", accountType, queryResultIsEmpty.getFails().size());
                return;
            }
            if (pageResponse.isHasNextPage()) {
                baseRequest = baseRequest.clone();
                baseRequest.setMaxId(pageResponse.getMaxId());
            }
            setBasePageRequest(basePageRequest, pageSize, pageResponse);
            // 数据对比
            if (CollectionUtils.isEmpty(bizBillCoinUserPropertyList)) {
                continue;
            }
            Queue<BillCoinUserProperty> memoryBillCoinUserPropertyList = new ConcurrentLinkedQueue<>();
            List<List<BillCoinUserProperty>> bizCoinUserPropertyLists = Lists.partition(new ArrayList<>(bizBillCoinUserPropertyList), partitionSize);
            TaskVoidBatchResult taskVoidBatchResult = taskManager.forEachSubmitBatchAndWait(bizCoinUserPropertyLists, (List<BillCoinUserProperty> bizCoinUserPropertyList) -> {
                bizCoinUserPropertyList.forEach(coinUserProperty -> {
                    BillCoinUserProperty billCoinUserProperty = billCoinUserPropertyService.selectUserCoinLatestRecord(
                            Integer.valueOf(accountType), accountTypeEnum.getAccountParam(), coinUserProperty.getUserId(), coinUserProperty.getCoinId(), coinUserProperty.getCheckTime());
//                    if (billCoinUserProperty == null) {
//                        billCoinUserProperty = billCoinUserPropertySnapshotService.selectCoinUserAfterCheckTimeRecord(
//                                Integer.valueOf(accountType), accountTypeEnum.getAccountParam(), coinUserProperty.getCoinId(), coinUserProperty.getUserId(), coinUserProperty.getCheckTime());
//                    }
                    if (billCoinUserProperty != null) {
                        memoryBillCoinUserPropertyList.add(billCoinUserProperty);
                    }
                });
            }, concurrent);
            if (taskVoidBatchResult != null && taskVoidBatchResult.getFails().size() > 0) {
                log.info("InitDataService queryResultIsEmpty error accountType:{} size:{}", accountType, queryResultIsEmpty.getFails().size());
                return;
            }
            BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountType);
            Map<String, BillCoinUserProperty> memoryBillCoinUserPropertyMap = memoryBillCoinUserPropertyList.stream().collect(Collectors.toMap(BillCoinUserProperty::groupByCoinUser, Function.identity()));
            for (BillCoinUserProperty billCoinUserProperty : bizBillCoinUserPropertyList) {
                dataCount += 1;
                BillCoinUserProperty memBillCoinUserProperty = memoryBillCoinUserPropertyMap.get(billCoinUserProperty.groupByCoinUser());
                if (!billCheckService.checkCoinUserProperty(billCoinUserProperty, memBillCoinUserProperty)) {
                    errorCount += 1;
                    BillCoinUserProperty billCoinUserPropertySnapshot = billCoinUserPropertySnapshotService.selectCoinUserAfterCheckTimeRecord(
                            Integer.valueOf(accountType), accountTypeEnum.getAccountParam(), memBillCoinUserProperty.getCoinId(), memBillCoinUserProperty.getUserId(), memBillCoinUserProperty.getCheckTime());
                    BigDecimal reconPropSum = memBillCoinUserProperty != null ? memBillCoinUserProperty.getPropSum() : null;
                    Long coinUserPropertyId = memBillCoinUserProperty != null ? memBillCoinUserProperty.getId() : null;
                    Long snapshotCoinUserPropertyId = billCoinUserPropertySnapshot != null ? billCoinUserPropertySnapshot.getId() : null;
                    errorLogList.add(MessageFormatter.arrayFormat("userId:{} coinId:{} bizPropSum:{} reconPropSum:{} coinUserPropertyId:{} snapshotCoinUserPropertyId:{}", new Object[]{billCoinUserProperty.getUserId(), billCoinUserProperty.getCoinId(), billCoinUserProperty.getPropSum(), reconPropSum, coinUserPropertyId, snapshotCoinUserPropertyId}).getMessage());
                }
            }
            log.info("InitDataService.checkUserCoinProperty ing accountType:{} checkOkTime:{} dataCount:{} errorCount:{} execTime:{} errorLog:{}",
                    accountType, DateUtil.date2str(new Date(checkTime)), dataCount, errorCount, (System.currentTimeMillis() - startTime), JSONObject.toJSONString(errorLogList));
        } while (pageResponse.isHasNextPage());
        log.info("InitDataService.checkUserCoinProperty end accountType:{} dataCount:{} errorCount:{} execTime:{} errorLog:{}", accountType, dataCount, errorCount, (System.currentTimeMillis() - startTime));
    }

    /**
     * 对比手续费和盈亏数据
     *
     * @param accountType
     * @param interval
     */
    public void checkContractProfit(Byte accountType, Long interval) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        if (!accountTypeEnum.isContract()) {
            return;
        }
        OldBillConfig oldBillConfig = oldBillConfigService.selectByTypeAndParam(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
        if (oldBillConfig == null) {
            log.info("InitDataService.checkContractProfit oldBillConfigService.selectByTypeAndParam is null accountType:{}", accountTypeEnum.getCode());
            return;
        }
        checkContractProfit(accountType, oldBillConfig.getCheckOkTime());
    }

    /**
     * 对比手续费和盈亏数据
     *
     * @param accountType
     * @param checkTime
     */
    private boolean checkContractProfit(Byte accountType, Date checkTime) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        // 查询老对账数据
        List<BillContractProfitTransfer> oldContractProfitTransferList = oldBillContractProfitTransferService.selectListByAccountTypeAndCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime);
        if (CollectionUtils.isEmpty(oldContractProfitTransferList)) {
            return false;
        }

        List<BillContractProfitCoinDetail> oldContractProfitCoinDetailList = oldBillContractProfitCoinDetailService.selectListByAccountTypeAndCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime);
        List<BillContractProfitSymbolDetail> oldContractProfitSymbolDetailList = oldBillContractProfitSymbolDetailService.selectListByAccountTypeAndCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime);
        // 查询内存对账数据
        List<BillContractProfitTransfer> contractProfitTransferList = billContractProfitTransferService.selectListByAccountTypeAndCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime);
        List<BillContractProfitCoinDetail> contractProfitCoinDetailList = billContractProfitCoinDetailService.selectListByAccountTypeAndCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime);
        List<BillContractProfitSymbolDetail> contractProfitSymbolDetailList = billContractProfitSymbolDetailService.selectListByAccountTypeAndCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime);
        Map<String, BillContractProfitTransfer> contractProfitTransferMap = contractProfitTransferList.stream().collect(Collectors.toMap(BillContractProfitTransfer::groupByAccountTypeCoinTransferType, Function.identity()));
        Map<String, BillContractProfitCoinDetail> contractProfitCoinDetailMap = contractProfitCoinDetailList.stream().collect(Collectors.toMap(BillContractProfitCoinDetail::getCoinProfitType, Function.identity()));
        Map<String, BillContractProfitSymbolDetail> contractProfitSymbolDetailMap = contractProfitSymbolDetailList.stream().collect(Collectors.toMap(BillContractProfitSymbolDetail::groupByAccountSymbolProfitType, Function.identity()));
        // 待账数据对比
        List<String> errorLogList = new ArrayList<>();
        for (BillContractProfitTransfer oldContractProfitTransfer : oldContractProfitTransferList) {
            BillContractProfitTransfer contractProfitTransfer = contractProfitTransferMap.get(oldContractProfitTransfer.groupByAccountTypeCoinTransferType());
            if (contractProfitTransfer != null) {
                if (oldContractProfitTransfer.getTransferCount().compareTo(contractProfitTransfer.getTransferCount()) != 0) {
                    errorLogList.add(MessageFormatter.arrayFormat("ContractProfitTransfer data key:{} oldTransferCount:{} memTransferCount:{}",
                            new Object[]{oldContractProfitTransfer.groupByAccountTypeCoinTransferType(), oldContractProfitTransfer.getTransferCount(), contractProfitTransfer.getTransferCount()}).getMessage());
                }
            } else {
                errorLogList.add(MessageFormatter.arrayFormat("memory ContractProfitTransfer data is null key:{}", new Object[]{oldContractProfitTransfer.groupByAccountTypeCoinTransferType()}).getMessage());
            }
        }
        // 盈亏coin数据
        for (BillContractProfitCoinDetail oldContractProfitCoinDetail : oldContractProfitCoinDetailList) {
            BillContractProfitCoinDetail contractProfitCoinDetail = contractProfitCoinDetailMap.get(oldContractProfitCoinDetail.groupByAccountCoinProfitType());
            if (contractProfitCoinDetail != null) {
                if (oldContractProfitCoinDetail.getProfitCountIncr().compareTo(contractProfitCoinDetail.getProfitCountIncr()) != 0
                        || oldContractProfitCoinDetail.getUnrealizedCount().compareTo(contractProfitCoinDetail.getUnrealizedCount()) != 0) {
                    errorLogList.add(MessageFormatter.arrayFormat("ContractProfitCoinDetail data key:{} oldProfitCount:{} oldProfitCountIncr:{} oldUnrealizedCount:{} memProfitCount:{} memProfitCountIncr:{} memUnrealizedCount:{}",
                            new Object[]{oldContractProfitCoinDetail.groupByAccountCoinProfitType(), oldContractProfitCoinDetail.getProfitCount(), oldContractProfitCoinDetail.getProfitCountIncr(), oldContractProfitCoinDetail.getUnrealizedCount(),
                                    contractProfitCoinDetail.getProfitCount(), contractProfitCoinDetail.getProfitCountIncr(), contractProfitCoinDetail.getUnrealizedCount()}).getMessage());
                }
            } else {
                errorLogList.add(MessageFormatter.arrayFormat("memory ContractProfitCoinDetail data is null key:{}", new Object[]{oldContractProfitCoinDetail.groupByAccountCoinProfitType()}).getMessage());
            }
        }
        // 盈亏symbol数据
        for (BillContractProfitSymbolDetail oldContractProfitSymbolDetail : oldContractProfitSymbolDetailList) {
            BillContractProfitSymbolDetail contractProfitSymbolDetail = contractProfitSymbolDetailMap.get(oldContractProfitSymbolDetail.groupByAccountSymbolProfitType());
            if (contractProfitSymbolDetail != null) {
                if (oldContractProfitSymbolDetail.getProfitCountIncr().compareTo(contractProfitSymbolDetail.getProfitCountIncr()) != 0
                        || oldContractProfitSymbolDetail.getUnrealizedCount().compareTo(contractProfitSymbolDetail.getUnrealizedCount()) != 0) {
                    errorLogList.add(MessageFormatter.arrayFormat("ContractProfitSymbolDetail data key:{} oldProfitCount:{} oldProfitCountIncr:{} oldUnrealizedCount:{} memProfitCount:{} memProfitCountIncr:{} memUnrealizedCount:{}",
                            new Object[]{oldContractProfitSymbolDetail.groupByAccountSymbolProfitType(), oldContractProfitSymbolDetail.getProfitCount(), oldContractProfitSymbolDetail.getProfitCountIncr(), oldContractProfitSymbolDetail.getUnrealizedCount(),
                                    contractProfitSymbolDetail.getProfitCount(), contractProfitSymbolDetail.getProfitCountIncr(), contractProfitSymbolDetail.getUnrealizedCount()}).getMessage());
                }
            } else {
                errorLogList.add(MessageFormatter.arrayFormat("memory ContractProfitSymbolDetail data is null key:{}", new Object[]{oldContractProfitSymbolDetail.groupByAccountSymbolProfitType()}).getMessage());
            }
        }
        log.info("InitDataService.checkContractProfit accountType:{} result:{} checkTime:{} oldContractProfitTransferListSize:{} oldContractProfitCoinDetailListSize:{} oldContractProfitSymbolDetailListSize:{} contractProfitTransferListSize:{} contractProfitCoinDetailListSize:{} contractProfitSymbolDetailListSIze:{} data:{}"
                , accountType, CollectionUtils.isEmpty(errorLogList), DateUtil.date2str(checkTime), oldContractProfitTransferList.size(), oldContractProfitCoinDetailList.size(), oldContractProfitSymbolDetailList.size(),
                contractProfitTransferList.size(), contractProfitCoinDetailList.size(), contractProfitSymbolDetailList.size(), JSONObject.toJSONString(errorLogList.stream().limit(100).collect(Collectors.toList())));
        if (CollectionUtils.isNotEmpty(errorLogList)) {
            alarmNotifyService.alarm(accountType, CHECK_CONTRACT_PROFIT_PROPERTY_ERROR, accountType);
        }
        return true;
    }

    /**
     * 对比symbol维度数据
     *
     * @param accountType
     * @param checkTime
     */
    public void checkSymbolProperty(Byte accountType, Long checkTime) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        if (!accountTypeEnum.haveUserPosition()) {
            return;
        }
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        if (checkTime == null) {
            OldBillConfig oldBillConfig = oldBillConfigService.selectByTypeAndParam(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
            if (oldBillConfig == null) {
                log.info("InitDataService.checkUserCoinProperty billConfigService.selectLastByLimit is null accountType:{}", accountTypeEnum.getCode());
                return;
            }
            checkTime = oldBillConfig.getCheckOkTime().getTime();
        }
        // 查询老对账数据
        List<BillSymbolProperty> oldBillSymbolPropertyList = oldBillSymbolPropertyService.selectListByCheckTime(accountType, accountTypeEnum.getAccountParam(), new Date(checkTime));
        List<BillSymbolCoinProperty> oldBillSymbolCoinPropertyList = oldBillSymbolCoinPropertyService.selectListByCheckTime(accountType, accountTypeEnum.getAccountParam(), new Date(checkTime));
        // 查询新对账数据
        List<BillSymbolProperty> billSymbolPropertyList = billSymbolPropertyService.selectListByCheckTime(accountType, accountTypeEnum.getAccountParam(), new Date(checkTime));
        List<BillSymbolCoinProperty> billSymbolCoinPropertyList = billSymbolCoinPropertyService.selectListByCheckTime(accountType, accountTypeEnum.getAccountParam(), new Date(checkTime));
        Map<String, BillSymbolProperty> billSymbolPropertyMap = billSymbolPropertyList.stream().collect(Collectors.toMap(BillSymbolProperty::getSymbolId, Function.identity()));
        Map<String, BillSymbolCoinProperty> billSymbolCoinPropertyMap = billSymbolCoinPropertyList.stream().collect(Collectors.toMap(BillSymbolCoinProperty::groupBySymbolCoin, Function.identity()));
        // 数据对比
        List<String> errorLogList = new ArrayList<>();
        for (BillSymbolProperty oldSymbolProperty : oldBillSymbolPropertyList) {
            BillSymbolProperty billSymbolProperty = billSymbolPropertyMap.get(oldSymbolProperty.getSymbolId());
            if (billSymbolProperty != null) {
                if (oldSymbolProperty.getChangeProp5().compareTo(billSymbolProperty.getChangeProp5()) != 0) {
                    errorLogList.add(MessageFormatter.arrayFormat("BillSymbolProperty data key:{} oldChangeProp4:{} oldChangeProp5:{} memChangeProp4:{} memChangeProp5:{}",
                            new Object[]{oldSymbolProperty.getSymbolId(), oldSymbolProperty.getChangeProp4(), oldSymbolProperty.getChangeProp5(),
                                    billSymbolProperty.getChangeProp4(), billSymbolProperty.getChangeProp5(),}).getMessage());
                }
            } else {
                if (oldSymbolProperty.getChangeProp5().compareTo(BigDecimal.ZERO) != 0) {
                    errorLogList.add(MessageFormatter.arrayFormat("memory BillSymbolProperty data is null key:{}", new Object[]{oldSymbolProperty.getSymbolId()}).getMessage());
                }
            }
        }
        for (BillSymbolCoinProperty oldSymbolCoinProperty : oldBillSymbolCoinPropertyList) {
            BillSymbolCoinProperty billSymbolCoinProperty = billSymbolCoinPropertyMap.get(oldSymbolCoinProperty.groupBySymbolCoin());
            if (billSymbolCoinProperty != null) {
                if (oldSymbolCoinProperty.getChangeProp5().compareTo(billSymbolCoinProperty.getChangeProp5()) != 0) {
                    errorLogList.add(MessageFormatter.arrayFormat("BillSymbolProperty data key:{} oldChangeProp4:{} oldChangeProp5:{} memChangeProp4:{} memChangeProp5:{}",
                            new Object[]{oldSymbolCoinProperty.groupBySymbolCoin(), oldSymbolCoinProperty.getChangeProp4(), oldSymbolCoinProperty.getChangeProp5(),
                                    billSymbolCoinProperty.getChangeProp4(), billSymbolCoinProperty.getChangeProp5(),}).getMessage());
                }
            } else {
                if (oldSymbolCoinProperty.getChangeProp5().compareTo(BigDecimal.ZERO) != 0) {
                    errorLogList.add(MessageFormatter.arrayFormat("memory BillSymbolCoinProperty data is null key:{}", new Object[]{oldSymbolCoinProperty.groupBySymbolCoin()}).getMessage());
                }
            }
        }
        log.info("InitDataService.checkSymbolProperty accountType:{} result:{} checkOkTime:{} oldBillSymbolPropertyListSize:{} oldBillSymbolCoinPropertyListSize:{} billSymbolPropertyListSize:{} billSymbolCoinPropertyListSize:{} data:{}"
                , accountType, CollectionUtils.isEmpty(errorLogList), DateUtil.date2str(new Date(checkTime)), oldBillSymbolPropertyList.size(), oldBillSymbolCoinPropertyList.size(),
                billSymbolPropertyList.size(), billSymbolCoinPropertyList.size(), JSONObject.toJSONString(errorLogList.stream().limit(100).collect(Collectors.toList())));
        if (CollectionUtils.isNotEmpty(errorLogList)) {
            alarmNotifyService.alarm(accountType, CHECK_SYMBOL_PROPERTY_ERROR, accountType);
        }
    }

    /**
     * 删除历史数据
     *
     * @param accountType
     */
    public void deleteHistoryData(Byte accountType, Long batchSize) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        Date nowDate = DateUtil.str2date(DateUtil.date2str(new Date()), "yyyy-MM-dd");
        Date checkTime = DateUtil.addMonth(nowDate, -1);
        // 删除持仓数据 保留1个月
        String tablePrefix = String.format("bill_user_position_%s_%s", accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
        List<String> userPositionServiceTables = billUserPositionService.getTables(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), tablePrefix);
        if (CollectionUtils.isNotEmpty(userPositionServiceTables)) {
            String tableSuffix = billUserPositionTableCreator.getTableSuffixName(accountType.toString(), checkTime);
            String deleteTableName = tablePrefix + "_" + tableSuffix;
            List<String> deleteTables = userPositionServiceTables.stream().filter(tableName -> tableName.compareTo(deleteTableName) <= 0).collect(Collectors.toList());
            for (String tableName : deleteTables) {
                boolean result = true;
                while (result) {
                    result = billUserPositionService.deleteByTableName(tableName, batchSize);
                }
                //todo jason 删除表 估计没权限
            }
        }

        // 删除用户资产 保留1个月
        Long maxId = billCoinUserPropertySnapshotService.getIdByCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime, "=");
        if (maxId == null) {
            maxId = billCoinUserPropertySnapshotService.getIdByCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime, "<=");
        }
        if (maxId != null) {
            boolean result = true;
            while (result) {
                result = billCoinUserPropertySnapshotService.deleteByMaxId(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), maxId, batchSize);
            }
        }

        // 删除coin 保留1个月
        maxId = billCoinPropertyService.getIdByCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime, "=");
        if (maxId == null) {
            maxId = billCoinPropertyService.getIdByCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime, "<=");
        }
        if (maxId != null) {
            boolean result = true;
            while (result) {
                result = billCoinPropertyService.deleteByMaxId(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), maxId, batchSize);
            }
        }

        // 删除coinType
        maxId = billCoinTypePropertyService.getIdByCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime, "=");
        if (maxId == null) {
            maxId = billCoinTypePropertyService.getIdByCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime, "<=");
        }
        if (maxId != null) {
            boolean result = true;
            while (result) {
                result = billCoinTypePropertyService.deleteByMaxId(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), maxId, batchSize);
            }
        }

        // 删除symbol
        maxId = billSymbolPropertyService.getIdByCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime, "=");
        if (maxId == null) {
            maxId = billSymbolPropertyService.getIdByCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime, "<=");
        }
        if (maxId != null) {
            boolean result = true;
            while (result) {
                result = billSymbolPropertyService.deleteByMaxId(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), maxId, batchSize);
            }
        }

        // 删除symbolCoin
        maxId = billSymbolCoinPropertyService.getIdByCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime, "=");
        if (maxId == null) {
            maxId = billSymbolCoinPropertyService.getIdByCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime, "<=");
        }
        if (maxId != null) {
            boolean result = true;
            while (result) {
                result = billSymbolCoinPropertyService.deleteByMaxId(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), maxId, batchSize);
            }
        }

        // 删除assets_bill_coin_property_internal_default
        List<String> ledgerList = Lists.newArrayList("internal", "leverspot");
        for (String ledger : ledgerList) {
            maxId = assetsBillCoinPropertyService.getIdByCheckTime(ledger, accountTypeEnum.getAccountParam(), checkTime, "=");
            if (maxId == null) {
                maxId = assetsBillCoinPropertyService.getIdByCheckTime(ledger, accountTypeEnum.getAccountParam(), checkTime, "<=");
            }
            if (maxId != null) {
                boolean result = true;
                while (result) {
                    result = assetsBillCoinPropertyService.deleteByMaxId(ledger, accountTypeEnum.getAccountParam(), maxId, batchSize);
                }
            }

            maxId = assetsBillCoinTypePropertyService.getIdByCheckTime(ledger, accountTypeEnum.getAccountParam(), checkTime, "=");
            if (maxId == null) {
                maxId = assetsBillCoinTypePropertyService.getIdByCheckTime(ledger, accountTypeEnum.getAccountParam(), checkTime, "<=");
            }
            if (maxId != null) {
                boolean result = true;
                while (result) {
                    result = assetsBillCoinTypePropertyService.deleteByMaxId(ledger, accountTypeEnum.getAccountParam(), maxId, batchSize);
                }
            }
        }

    }

    /**
     * 同步用户
     *
     * @param bizTime
     * @param accountType
     * @param maxIdParam
     * @param pageSize
     * @param page
     * @param accountParam
     */
    public Long initBillUser(Long bizTime, Byte accountType, Long maxIdParam, Integer pageSize, Integer page, String accountParam) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType.byteValue());
        AccountTypeEnum syncUserAccountTypeEnum = accountTypeEnum;
        if (accountTypeEnum.equals(AccountTypeEnum.UNKNOWN)) {
            accountTypeEnum = ReconPapTradingUtil.isPapTradingService() ? AccountTypeEnum.P_SPOT : AccountTypeEnum.SPOT;
        }
        Long maxId = maxIdParam != null ? maxIdParam : 1L;
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setPageNo(page);
        basePageRequest.setPageSize(pageSize);

        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setBeginTime(bizTime - 1L);
        baseRequest.setEndTime(bizTime);
        baseRequest.setAccountType(accountType);
        baseRequest.setAccountParam(accountParam);
        baseRequest.setMaxId(maxId);

        PageResponse<UserInfoResult> pageResponse = null;
        Set<Long> alreadyInitUserIds = new HashSet<Long>();
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        do {
            apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
            boolean needExtendQueryTime = (accountTypeEnum.isContract() || accountTypeEnum.equals(AccountTypeEnum.SPOT));
            BaseRequest needQueryTimeBaseRequest = null;
            if (needExtendQueryTime) {
                needQueryTimeBaseRequest = getQueryUserInfoRequest(baseRequest);
            } else {
                needQueryTimeBaseRequest = getTimeQueryUserInfoRequest(accountTypeEnum, baseRequest);
            }
            pageResponse = accountAssetsServiceFactory.queryUserInfo(needQueryTimeBaseRequest, basePageRequest.getPageNo(), basePageRequest.getPageSize());
            if (apolloBizConfig.isInitTrackLogOpen()) {
                log.info("InitDataService queryUserInfo param is {} result is {}", JSONObject.toJSONString(needQueryTimeBaseRequest), JSONObject.toJSONString(pageResponse));
            }
            if (Objects.isNull(pageResponse)) {
                return null;
            }
            List<UserInfoResult> userInfoResultList = pageResponse.getData();
            // 全局判重
            AccountTypeEnum finalAccountTypeEnum = accountTypeEnum;
            List<UserInfoResult> userInfoResults = userInfoResultList.stream()
                    .filter(item -> {
                        // 10过滤掉demo用户
                        if (finalAccountTypeEnum.getCode() == AccountTypeEnum.DEMO_SPOT.getCode()) {
                            if (!SiteCodeUtils.isDemo(item.getUserId())) {
                                return false;
                            }
                        } else if (finalAccountTypeEnum.getCode() == AccountTypeEnum.P_SPOT.getCode()) {
                            if (!SiteUtil.checkPaptradingByUserId(item.getUserId())) {
                                return false;
                            }
                        }
                        boolean exist = false;
                        if (alreadyInitUserIds.contains(item.getUserId())) {
                            exist = true;
                        } else {
                            alreadyInitUserIds.add(item.getUserId());
                        }
                        return !exist;
                    })
                    .collect(Collectors.toList());
            // 批量查询用户基本信息
            Map<Long, UserBaseInfoDTO> userBaseInfoMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(userInfoResults)) {
                List<UserBaseInfoDTO> userQueryBaseInfos = accountAssetsServiceFactory.batchUserBaseInfo(userInfoResults.stream().map(UserInfoResult::getUserId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(userQueryBaseInfos)) {
                    userBaseInfoMap.putAll(userQueryBaseInfos.stream().collect(Collectors.toMap(UserBaseInfoDTO::getId, Function.identity())));
                }
            }
            // 构建kafka对象 发送kafka
            for (UserInfoResult userInfoResult : userInfoResults) {
                UserBaseInfoDTO userBaseInfoDTO = userBaseInfoMap.get(userInfoResult.getUserId());
                SyncBillUserDTO syncBillUserDTO = new SyncBillUserDTO();
                syncBillUserDTO.setAccountType(syncUserAccountTypeEnum.getCode());
                syncBillUserDTO.setUserId(userInfoResult.getUserId());
                syncBillUserDTO.setParentId(userBaseInfoDTO != null ? userBaseInfoDTO.getParentId() : null);
                syncBillUserDTO.setRegisterTime(userInfoResult.getCreateTime());
                ProducerRecord<String, String> incrementRecord = new ProducerRecord<>(KafkaTopicEnum.RECON_USER_INITALL_SYNC_TOPIC.getCode(), syncBillUserDTO.getUserId().toString(), JSON.toJSONString(syncBillUserDTO));
                kafkaProducer.send(incrementRecord);
            }

            log.info("InitDataService batch query user assets with maxId {}", pageResponse.getMaxId());
            if (pageResponse.isHasNextPage()) {
                baseRequest = baseRequest.clone();
                baseRequest.setMaxId(pageResponse.getMaxId());
            }
            setBasePageRequest(basePageRequest, pageSize, pageResponse);
        } while (pageResponse.isHasNextPage());
        log.info("initBillUser successed ...");
        return baseRequest.getMaxId();
    }

    public void checkCoinAndUserCoinProperty(Byte accountType, Long checkTime, Long incrementPeriod) {
        Long pageSize = 1000L;
        Integer concurrent = 20;
        // 获取业务所有用户+coinId id分段查询 1000一个段 多线程查询
        final AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        Long coinUserMaxId = billCoinUserPropertyService.selectMaxId(accountType, accountTypeEnum.getAccountParam());
        log.info("checkCoinAndUserCoinProperty selectMaxId accountType:{} id:{}", accountType, coinUserMaxId);
        if (coinUserMaxId == null) {
            return;
        }
        List<Long[]> coinUserIdSegment = new ArrayList<>();
        for (int i = 0; i < (coinUserMaxId / pageSize + 1); i++) {
            coinUserIdSegment.add(new Long[]{i * pageSize, (i + 1) * pageSize});
        }
        log.info("checkCoinAndUserCoinProperty selectCoinUserByStartAndEndId start accountType:{} id:{}", accountType, coinUserMaxId);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("billCoinUserPropertyService.selectCoinUserByStartAndEndId");
        Map<Long, Queue<Integer>> userCoinIdQueueMap = new ConcurrentHashMap<>();
        TaskVoidBatchResult queryResultIsEmpty = taskManager.forEachSubmitBatchAndWait(coinUserIdSegment, (Long[] idSegment) -> {
            List<BillCoinUserProperty> billCoinUserPropertyList = billCoinUserPropertyService.selectCoinUserByStartAndEndId(accountType, accountTypeEnum.getAccountParam(), idSegment[0], idSegment[1]);
            billCoinUserPropertyList.forEach(billCoinUserProperty -> {
                userCoinIdQueueMap.computeIfAbsent(billCoinUserProperty.getUserId(), k -> new ConcurrentLinkedQueue<>()).add(billCoinUserProperty.getCoinId());
            });
        }, concurrent);
        stopWatch.stop();
        log.info("checkCoinAndUserCoinProperty selectCoinUserByStartAndEndId end accountType:{} stopWatch:{}", accountType, stopWatch.prettyPrint());
        if (queryResultIsEmpty != null && queryResultIsEmpty.getFails().size() > 0) {
            log.info("checkCoinAndUserCoinProperty selectCoinUserByStartAndEndId queryResultIsEmpty fail accountType:{}", accountType);
            return;
        }

        // 批量查询用户资产快照 边查询 边汇总
        Date lastCheckTime = new Date(checkTime);
        boolean checkResult = true;
        while (checkResult) {
            final Date snapshotTime = lastCheckTime;
            Map<Integer, BigDecimal> userCoinAssetsMap = new ConcurrentHashMap<>();
            List<Byte> accountTypeList = Lists.newArrayList(accountType);
            log.info("billCoinUserPropertyService.getUserSnapshotAssets start accountType:{} snapshotTime:{}", accountType, DateUtil.date2str(snapshotTime));
            stopWatch = new StopWatch();
            stopWatch.start("billCoinUserPropertyService.getUserSnapshotAssets");
            List<Long> userIdList = new ArrayList<>(userCoinIdQueueMap.keySet());
            queryResultIsEmpty = taskManager.forEachSubmitBatchAndWait(userIdList, (Long userId) -> {
                List<Integer> coinIdList = new ArrayList<>(userCoinIdQueueMap.get(userId));
                Map<Integer, BigDecimal> userSnapshotAssets = billCoinUserPropertyService.getUserSnapshotAssets(userId, accountTypeList, snapshotTime, coinIdList);
                userSnapshotAssets.forEach((coinId, assets) -> {
                    userCoinAssetsMap.computeIfAbsent(coinId, k -> new BigDecimal(0));
                    userCoinAssetsMap.computeIfPresent(coinId, (k, v) -> v.add(assets));
                });
            }, concurrent);
            stopWatch.stop();
            log.info("billCoinUserPropertyService.getUserSnapshotAssets end accountType:{} snapshotTime:{} stopWatch:{}", accountType, DateUtil.date2str(snapshotTime), stopWatch.prettyPrint());
            if (queryResultIsEmpty != null && queryResultIsEmpty.getFails().size() > 0) {
                log.info("checkCoinAndUserCoinProperty getUserSnapshotAssets queryResultIsEmpty fail accountType:{} snapshotTime:{}", accountType, DateUtil.date2str(snapshotTime));
                return;
            }
            // 查询coin资产和用户资产进行对比
            BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountType);
            List<BillCoinProperty> billCoinPropertyList = billCoinPropertyService.selectCheckTimeRecord(accountType.intValue(), accountTypeEnum.getAccountParam(), snapshotTime);
            if (CollectionUtils.isNotEmpty(billCoinPropertyList)) {
                for (BillCoinProperty billCoinProperty : billCoinPropertyList) {
                    BigDecimal coinAssets = billCheckService.getPropSumByProperty(billCoinProperty);
                    BigDecimal userCoinAssets = userCoinAssetsMap.getOrDefault(billCoinProperty.getCoinId(), BigDecimal.ZERO);
                    boolean result = coinAssets.compareTo(userCoinAssets) == 0;
                    log.info("checkCoinAndUserCoinProperty result:{} coinId:{} coinAssets:{} userCoinAssets:{}  snapshotTime:{}", result, billCoinProperty.getCoinId(), coinAssets, userCoinAssets, DateUtil.date2str(snapshotTime));
                    if (!result) {
                        checkResult = false;
                    }
                }
            } else {
                checkResult = false;
            }
            lastCheckTime = DateUtil.addMillisecond(lastCheckTime, incrementPeriod.intValue());
        }
    }

    /**
     * 产检查用户负值产
     *
     * @param jobParam
     */
    public void repairUserNegativeAssets(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        String action = jsonObject.getString("action");
        Byte accountType = jsonObject.getByte("accountType");
        boolean newCacheKey = jsonObject.getBooleanValue("newCacheKey");
        String deleteUserCoinKey = jsonObject.getString("deleteUserCoinKey");
        boolean deleteAccountType = jsonObject.getBooleanValue("deleteAccountType");
        Long checkTime = jsonObject.getLong("checkTime");
        Integer pageSize = jsonObject.getInteger("pageSize");
        Integer concurrent = jsonObject.getInteger("concurrent");
        if ("query".equalsIgnoreCase(action)) {
            Object oldNegativeObject = redisTemplate.opsForValue().get(RedisUtil.getUserCoinNegativeKey(accountType));
            Object negativeObjectV2 = redisTemplate.opsForValue().get(RedisUtil.getUserCoinNegativeKeyV2(accountType));
            log.info("queryUserNegativeAssets v1 accountType:{} oldNegativeObject:{}", accountType, oldNegativeObject == null ? "" : JSON.toJSONString(oldNegativeObject.toString()));
            log.info("queryUserNegativeAssets v2 accountType:{} negativeObjectV2:{}", accountType, negativeObjectV2 == null ? "" : JSON.toJSONString(negativeObjectV2.toString()));
        } else if ("deleteKey".equalsIgnoreCase(action)) {
            Map<String, UserAssetNegativeErrorModel> userCoinPropNegativeCache = new HashMap<>();
            if (!deleteAccountType) {
                //  获取redis负值用户
                String userCoinNegative = (String) redisTemplate.opsForValue().get(RedisUtil.getUserCoinNegativeKey(accountType));
                if (StringUtils.isNotEmpty(userCoinNegative) && !"[]".equals(userCoinNegative) && !"{}".equals(userCoinNegative)) {
                    userCoinPropNegativeCache = JSON.parseObject(userCoinNegative, new TypeReference<Map<String, UserAssetNegativeErrorModel>>() {
                    });
                }
                userCoinPropNegativeCache.remove(deleteUserCoinKey);
            }
            String redisKey = newCacheKey ? RedisUtil.getUserCoinNegativeKeyV2(accountType) : RedisUtil.getUserCoinNegativeKey(accountType);
            redisTemplate.opsForValue().set(redisKey, JSON.toJSONString(userCoinPropNegativeCache));
            log.info("deleteUserNegativeAssets success: {}", redisKey);
        } else if ("repairRedisKey".equalsIgnoreCase(action)) {
            doRepairRedisKey(accountType, pageSize, concurrent, checkTime);
        }
    }

    private void doRepairRedisKey(Byte accountType, Integer pageSize, Integer concurrent, Long checkTime) {
        pageSize = pageSize == null ? QUERY_PAGE_SIZE : pageSize;
        concurrent = concurrent == null ? CONCURRENT : concurrent;
        final AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);

        // 1. 获取业务所有用户+coinId id分段查询 1000一个段 多线程查询
        Optional<Map<Long, Queue<Integer>>> userCoinIdQueueMapOptional = queryUserCoinMap(pageSize, concurrent, accountTypeEnum);
        if (userCoinIdQueueMapOptional.isEmpty()) {
            return;
        }
        Map<Long, Queue<Integer>> userCoinIdQueueMap = userCoinIdQueueMapOptional.get();

        repairRedisKeyOfUsers(concurrent, checkTime, accountTypeEnum, userCoinIdQueueMap);
    }

    /**
     * 异步重跑部分负值缓存
     *
     * @param userCoinPropNegativeCache 需要重跑的缓存
     */
    public void asyncRepairRedisKeyOfUsers(AccountTypeEnum accountTypeEnum, Map<String, UserAssetNegativeErrorModel> userCoinPropNegativeCache) {
        log.info("asyncRepairRedisKeyOfUsers start, map:{}", JSON.toJSONString(userCoinPropNegativeCache));
        try {
            if (userCoinPropNegativeCache == null || userCoinPropNegativeCache.isEmpty()) {
                log.info("asyncRepairRedisKeyOfUsers empty input");
                return;
            }

            int concurrent = CONCURRENT;

            Optional<Map<Long, Queue<Integer>>> userCoinIdQueueMapOptional = generateUserCoinMap(accountTypeEnum, userCoinPropNegativeCache, QUERY_PAGE_SIZE, concurrent);
            if (userCoinIdQueueMapOptional.isEmpty()) {
                return;
            }
            Map<Long, Queue<Integer>> userCoinIdQueueMap = userCoinIdQueueMapOptional.get();

            repairRedisKeyOfUsers(concurrent, null, accountTypeEnum, userCoinIdQueueMap);
            log.info("asyncRepairRedisKeyOfUsers end");
        } catch (Exception e) {
            log.error("asyncRepairRedisKeyOfUsers error", e);
        }
    }

    private Optional<Map<Long, Queue<Integer>>> generateUserCoinMap(AccountTypeEnum accountTypeEnum, Map<String, UserAssetNegativeErrorModel> userCoinPropNegativeCache, int pageSize, int concurrent) {
        Map<Long, Queue<Integer>> userCoinIdQueueMap = new ConcurrentHashMap<>();
        List<Long> userIdList = userCoinPropNegativeCache.values().stream().map(UserAssetNegativeErrorModel::getUserId).collect(Collectors.toList());

        List<List<Long>> userIdListSegment = Lists.partition(userIdList, pageSize);
        log.info("generateUserCoinMap selectCoinUserByUserIds start accountType:{}", accountTypeEnum.getCode());
        TaskVoidBatchResult queryUserCoinAssetsResult = taskManager.forEachSubmitBatchAndWait(userIdListSegment, (List<Long> userIds) -> {
            List<BillCoinUserProperty> billCoinUserPropertyList = billCoinUserPropertyService.selectCoinUserByUserIds(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), userIds);
            billCoinUserPropertyList.forEach(billCoinUserProperty -> {
                userCoinIdQueueMap.computeIfAbsent(billCoinUserProperty.getUserId(), k -> new ConcurrentLinkedQueue<>()).add(billCoinUserProperty.getCoinId());
            });
        }, concurrent);
        log.info("generateUserCoinMap selectCoinUserByUserIds end accountType:{}", accountTypeEnum.getCode());
        if (queryUserCoinAssetsResult != null && !queryUserCoinAssetsResult.getFails().isEmpty()) {
            log.info("generateUserCoinMap selectCoinUserByUserIds queryUserCoinAssetsResult fail accountType:{}", accountTypeEnum.getCode());
            return Optional.empty();
        }

        return Optional.of(userCoinIdQueueMap);
    }

    private void repairRedisKeyOfUsers(Integer concurrent, Long checkTime, AccountTypeEnum accountTypeEnum, Map<Long, Queue<Integer>> userCoinIdQueueMap) {
        StopWatch stopWatch = new StopWatch();
        concurrent = concurrent == null ? CONCURRENT : concurrent;
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        // 上一次检测时间
        Date lastCheckTime = checkTime != null ? new Date(checkTime): billConfigService.selectLatestCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());

        // 2. 批量查询用户资产快照 边查询 边汇总
        Optional<Map<Long, Map<Integer, BigDecimal>>>  userCoinAssetsMapOptional = queryUserCoinAssets(accountTypeEnum.getCode(), lastCheckTime, userCoinIdQueueMap, concurrent, stopWatch);
        if (userCoinAssetsMapOptional.isEmpty()) {
            return;
        }
        Map<Long, Map<Integer, BigDecimal>> userCoinAssetsMap = userCoinAssetsMapOptional.get();

        // 3. 查询持仓资产(未实现)
        Map<Long, Map<Integer, BigDecimal>> userCoinUnRealizedMap = queryUnRealized(accountTypeEnum, stopWatch, lastCheckTime, apolloBizConfig);

        // 4. 组装负值
        Map<String, UserAssetNegativeErrorModel> userCoinPropNegativeCache = assemblyNegative(lastCheckTime, accountTypeEnum, userCoinAssetsMap, userCoinUnRealizedMap, apolloBizConfig);

        // 5. 组装完成，写入标记
        userCoinPropNegativeCache.put(NegativeCheckCacheMigrateEnum.complete_repair.name(), new UserAssetNegativeErrorModel());

        // 6. 更新redis
        String oldNegativeObject = (String) redisTemplate.opsForValue().get(RedisUtil.getUserCoinNegativeKey(accountTypeEnum.getCode()));
        redisTemplate.opsForValue().set(RedisUtil.getUserCoinNegativeKey(accountTypeEnum.getCode()), JSON.toJSONString(userCoinPropNegativeCache));
        log.info("repairUserNegativeAssets done accountType:{} oldNegativeObject:{}", accountTypeEnum.getCode(), oldNegativeObject);
        log.info("repairUserNegativeAssets done accountType:{} newNegativeObject:{}", accountTypeEnum.getCode(), JSON.toJSONString(userCoinPropNegativeCache));
    }

    private Optional<Map<Long, Map<Integer, BigDecimal>>> queryUserCoinAssets(Byte accountType, Date lastCheckTime, Map<Long, Queue<Integer>> userCoinIdQueueMap, Integer concurrent, StopWatch stopWatch) {
        stopWatch = new StopWatch();
        Map<Long, Map<Integer, BigDecimal>> userCoinAssetsMap = new ConcurrentHashMap<>();
        log.info("billCoinUserPropertyService.getUserSnapshotAssets start accountType:{} snapshotTime:{}", accountType, DateUtil.date2str(lastCheckTime));
        stopWatch.start("billCoinUserPropertyService.getUserSnapshotAssets");
        List<Byte> accountTypeList = Lists.newArrayList(accountType);
        List<Long> userIdList = new ArrayList<>(userCoinIdQueueMap.keySet());
        TaskVoidBatchResult queryUserCoinAssetsResult = taskManager.forEachSubmitBatchAndWait(userIdList, (Long userId) -> {
            List<Integer> coinIdList = new ArrayList<>(userCoinIdQueueMap.get(userId));
            Map<Integer, BigDecimal> userSnapshotAssets = billCoinUserPropertyService.getUserSnapshotAssets(userId, accountTypeList, lastCheckTime, coinIdList);
            userCoinAssetsMap.put(userId, userSnapshotAssets);
        }, concurrent);
        stopWatch.stop();
        log.info("billCoinUserPropertyService.getUserSnapshotAssets end accountType:{} snapshotTime:{} stopWatch:{}", accountType, DateUtil.date2str(lastCheckTime), stopWatch.prettyPrint());
        if (queryUserCoinAssetsResult != null && !queryUserCoinAssetsResult.getFails().isEmpty()) {
            log.info("checkCoinAndUserCoinProperty getUserSnapshotAssets queryUserCoinAssetsResult fail accountType:{} snapshotTime:{}", accountType, DateUtil.date2str(lastCheckTime));
            return Optional.empty();
        }
        return Optional.of(userCoinAssetsMap);
    }

    private Map<String, UserAssetNegativeErrorModel> assemblyNegative(Date lastCheckTime, AccountTypeEnum accountTypeEnum, Map<Long, Map<Integer, BigDecimal>> userCoinAssetsMap, Map<Long, Map<Integer, BigDecimal>> userCoinUnRealizedMap, ApolloReconciliationBizConfig apolloBizConfig) {
        Map<String, UserAssetNegativeErrorModel> userCoinPropNegativeCache = new HashMap<>();
        String checkOkTimeStr = DateUtil.date2str(lastCheckTime);
        Map<Integer, PriceVo> priceVos = commonService.getNewCoinIdRatesMapCache(lastCheckTime.getTime(), accountTypeEnum);
        // 按用户遍历
        for (Map.Entry<Long, Map<Integer, BigDecimal>> userAssetsEntry : userCoinAssetsMap.entrySet()) {
            Map<Integer, BigDecimal> unRealizedMap = userCoinUnRealizedMap.getOrDefault(userAssetsEntry.getKey(), new HashMap<>());
            // 统计用户总值与总负值
            BigDecimal userProp = BigDecimal.ZERO;
            BigDecimal userUnRealized = BigDecimal.ZERO;
            BigDecimal negativeProp  = BigDecimal.ZERO;
            BigDecimal negativeUnRealized = BigDecimal.ZERO;

            // 按币种遍历
            for (Map.Entry<Integer, BigDecimal> entry : userAssetsEntry.getValue().entrySet()) {
                BigDecimal unRealized = unRealizedMap.getOrDefault(entry.getKey(), BigDecimal.ZERO);
                PriceVo priceVo = priceVos.get(entry.getKey());
                if (priceVo == null) {
                    throw new RuntimeException("price not found coinId:" + entry.getKey());
                }
                // 单币种资产与未实现
                BigDecimal propValue = entry.getValue().multiply(priceVo.getPrice());
                BigDecimal unRealizedValue = unRealized.multiply(priceVo.getPrice());

                // 总值累加
                userProp = userProp.add(propValue);
                userUnRealized = userUnRealized.add(unRealizedValue);

                // 总负值累加
                if (propValue.add(unRealizedValue).compareTo(BigDecimal.ZERO) < 0) {
                    negativeProp = negativeProp.add(propValue);
                    negativeUnRealized = negativeUnRealized.add(unRealizedValue);
                }
            }


            // 用户维度负值资产检测 (error对象初始化)
            if (apolloBizConfig.isTimeSliceUserAssetsNegativeOpen()) {
                String userCoinKey = userAssetsEntry.getKey() + BillConstants.POUND_SIGN + NegativeCheckTypeEnum.TOTAL_ASSETS.getCode();
                String userCoinPropNegativeKey = accountTypeEnum.getCode() + BillConstants.POUND_SIGN + userCoinKey;
                // 用户总值（资产+未实现） < 0
                if (userProp.add(userUnRealized).compareTo(BigDecimal.ZERO) < 0) {
                    UserAssetNegativeErrorModel userAssetNegativeErrorModel = userCoinPropNegativeCache.computeIfAbsent(userCoinPropNegativeKey, key -> new UserAssetNegativeErrorModel());
                    userAssetNegativeErrorModel.setPropValue(userProp);
                    userAssetNegativeErrorModel.setUnRealized(userUnRealized);
                    userAssetNegativeErrorModel.setTotalValue(userProp.add(userUnRealized));

                    fillParamToModel(userAssetNegativeErrorModel, userAssetsEntry, NegativeCheckTypeEnum.TOTAL_ASSETS.getCode(), accountTypeEnum, userProp, userUnRealized, negativeProp, negativeUnRealized, checkOkTimeStr, false);
                }
            } else {
                // 用户币种维度负值资产检测 (error对象初始化)
                for (Map.Entry<Integer, BigDecimal> coinEntry : userAssetsEntry.getValue().entrySet()) {
                    PriceVo priceVo = priceVos.get(coinEntry.getKey());
                    BigDecimal propSum = coinEntry.getValue().multiply(priceVo.getPrice());
                    BigDecimal unRealized = unRealizedMap.getOrDefault(coinEntry.getKey(), BigDecimal.ZERO).multiply(priceVo.getPrice());
                    // 判断是否存在负值情况
                    String userCoinKey = userAssetsEntry.getKey() + BillConstants.POUND_SIGN + coinEntry.getKey();
                    String userCoinPropNegativeKey = accountTypeEnum.getCode() + BillConstants.POUND_SIGN + userCoinKey;
                    // 币种总值（币种资产+币种未实现） < 0
                    if (propSum.add(unRealized).compareTo(BigDecimal.ZERO) < 0) {
                        UserAssetNegativeErrorModel userAssetNegativeErrorModel = userCoinPropNegativeCache.computeIfAbsent(userCoinPropNegativeKey, key -> new UserAssetNegativeErrorModel());
                        userAssetNegativeErrorModel.setPropValue(propSum);
                        userAssetNegativeErrorModel.setUnRealized(unRealized);
                        userAssetNegativeErrorModel.setTotalValue(propSum.add(unRealized));

                        fillParamToModel(userAssetNegativeErrorModel, userAssetsEntry, coinEntry.getKey(), accountTypeEnum, userProp, userUnRealized, negativeProp, negativeUnRealized, checkOkTimeStr, true);
                    }
                }
            }
        }
        return userCoinPropNegativeCache;
    }

    private Optional<Map<Long, Queue<Integer>>> queryUserCoinMap(Integer pageSize, Integer concurrent, AccountTypeEnum accountTypeEnum) {
        StopWatch stopWatch = new StopWatch();
        Map<Long, Queue<Integer>> userCoinIdQueueMap = new ConcurrentHashMap<>();
        Long coinUserMinId = billCoinUserPropertyService.selectMinId(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
        Long coinUserMaxId = billCoinUserPropertyService.selectMaxId(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
        log.info("repairUserNegativeAssets selectMaxId accountType:{} minId:{}, maxId:{}", accountTypeEnum.getCode(), coinUserMinId, coinUserMaxId);
        if (coinUserMinId == null || coinUserMaxId == null) {
            return Optional.empty();
        }
        List<Long[]> coinUserIdSegment = new ArrayList<>();
        for (int i = 0; i < (coinUserMaxId / pageSize); i++) {
            long start = coinUserMinId + (long) i * pageSize;
            long end = Math.min(start + pageSize, coinUserMaxId);
            coinUserIdSegment.add(new Long[]{start, end});
        }
        log.info("repairUserNegativeAssets selectCoinUserByStartAndEndId start accountType:{} id:{}", accountTypeEnum.getCode(), coinUserMaxId);
        stopWatch.start("repairUserNegativeAssets.selectCoinUserByStartAndEndId");
        TaskVoidBatchResult queryUserCoinAssetsResult = taskManager.forEachSubmitBatchAndWait(coinUserIdSegment, (Long[] idSegment) -> {
            List<BillCoinUserProperty> billCoinUserPropertyList = billCoinUserPropertyService.selectCoinUserByStartAndEndId(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), idSegment[0], idSegment[1]);
            billCoinUserPropertyList.forEach(billCoinUserProperty -> {
                userCoinIdQueueMap.computeIfAbsent(billCoinUserProperty.getUserId(), k -> new ConcurrentLinkedQueue<>()).add(billCoinUserProperty.getCoinId());
            });
        }, concurrent);
        stopWatch.stop();
        log.info("repairUserNegativeAssets selectCoinUserByStartAndEndId end accountType:{} stopWatch:{}", accountTypeEnum.getCode(), stopWatch.prettyPrint());
        if (queryUserCoinAssetsResult != null && queryUserCoinAssetsResult.getFails().size() > 0) {
            log.info("repairUserNegativeAssets selectCoinUserByStartAndEndId queryUserCoinAssetsResult fail accountType:{}", accountTypeEnum.getCode());
            return Optional.empty();
        }
        return Optional.of(userCoinIdQueueMap);
    }

    private Map<Long, Map<Integer, BigDecimal>> queryUnRealized(AccountTypeEnum accountTypeEnum, StopWatch stopWatch, Date lastCheckTime, ApolloReconciliationBizConfig apolloBizConfig) {
        Map<Long, Map<Integer, BigDecimal>> userCoinUnRealizedMap = new HashMap<>();
        if (accountTypeEnum.haveUserPosition()) {
            stopWatch.start("billUserPositionService.selectRangeCheckTimeRecordPageQuery");
            Long minId = 0L;
            List<BillUserPosition> allBillUserPositionList = new ArrayList<>();
            // 删除billConfig
            while (true) {
                List<BillUserPosition> billUserPositionList = billUserPositionService.selectRangeCheckTimeRecordPageQuery(Integer.valueOf(accountTypeEnum.getCode()), accountTypeEnum.getAccountParam(), lastCheckTime, minId, apolloBizConfig.getSingleSqlMaxSize());
                if (CollectionUtils.isEmpty(billUserPositionList)) {
                    break;
                }
                minId = billUserPositionList.get(billUserPositionList.size() - 1).getId();
                allBillUserPositionList.addAll(billUserPositionList.stream().filter(item -> (item.getLCount().compareTo(BigDecimal.ZERO) > 0 || item.getSCount().compareTo(BigDecimal.ZERO) > 0)).collect(Collectors.toList()));
            }
            MixAccountAssetsExtension mixAccountAssetsExtension = accountAssetsServiceFactory.queryPriceByTime((int) apolloBizConfig.getAccountType(), lastCheckTime.getTime());
            Map<String, BigDecimal> mPriceMap = mixAccountAssetsExtension.getMPriceMap();
            Map<Integer, BigDecimal> sPriceMap = mixAccountAssetsExtension.getSPriceMap();
            log.info("mixAccountAssetsExtension.getMPriceMap accountType:{} checkOkTime:{} mPriceMap:{} sPriceMap:{}", accountTypeEnum.getCode(), DateUtil.date2str(lastCheckTime), JSON.toJSONString(mPriceMap), JSON.toJSONString(sPriceMap));
            Map<String, List<BillUserPosition>> billUserPositionMap = allBillUserPositionList.stream().collect(Collectors.groupingBy(BillUserPosition::groupByUserCoinKey));
            for (Map.Entry<String, List<BillUserPosition>> entry : billUserPositionMap.entrySet()) {
                BigDecimal unRealized = BillUserPosition.computeUnRealized(entry.getValue(), mPriceMap);
                Long userId = BillUserPosition.getUserIdFromKey(entry.getKey());
                Integer coinId = BillUserPosition.getCoinIdFromKey(entry.getKey());
                userCoinUnRealizedMap.computeIfAbsent(userId, key -> new HashMap<>()).put(coinId, unRealized);
                if (accountTypeEnum.isUsdContract() && unRealized.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal sPrice = sPriceMap.get(coinId);
                    if (sPrice == null) {
                        throw new RuntimeException("sPrice is null accountType:" + accountTypeEnum.getCode() + " coinId:" + coinId);
                    }
                    userCoinUnRealizedMap.computeIfAbsent(userId, key -> new HashMap<>()).put(coinId, unRealized.divide(sPrice, BillConstants.SERVER_DEFAULT_SCALE, RoundingMode.UP));
                }
            }
            stopWatch.stop();
        }
        return userCoinUnRealizedMap;
    }

    private static void fillParamToModel(UserAssetNegativeErrorModel errorModel, Map.Entry<Long, Map<Integer, BigDecimal>> userAssetsEntry,
                                         int coinId, AccountTypeEnum accountTypeEnum, BigDecimal userProp, BigDecimal userUnRealized, BigDecimal negativeProp,
                                         BigDecimal negativeUnRealized, String checkOkTimeStr, boolean isCoin) {
        errorModel.setAccountType(accountTypeEnum.getCode());
        errorModel.setUserId(userAssetsEntry.getKey());
        errorModel.setCoinId(coinId);
        errorModel.setUserCoinKey(errorModel.getUserId() + BillConstants.POUND_SIGN + errorModel.getCoinId());

        // 币种资产
        if (isCoin) {
            errorModel.getCoinAssets().setPropValue(errorModel.getTotalValue());
            errorModel.getCoinAssets().setUnRealized(errorModel.getUnRealized());
            errorModel.getCoinAssets().setTotal(errorModel.getTotalValue());
        }
        // 总资产
        errorModel.getUserTotalAssets().setPropValue(userProp);
        errorModel.getUserTotalAssets().setUnRealized(userUnRealized);
        errorModel.getUserTotalAssets().setTotal(userProp.add(userUnRealized));

        // 总负值资产
        errorModel.getUserNegativeSum().setPropValue(negativeProp);
        errorModel.getUserNegativeSum().setUnRealized(negativeUnRealized);
        errorModel.getUserNegativeSum().setTotal(negativeProp.add(negativeUnRealized));

        errorModel.setErrorTimeStr(errorModel.getErrorTimeStr() != null ? errorModel.getErrorTimeStr() : checkOkTimeStr);
        errorModel.setCheckTimeStr(checkOkTimeStr);
    }
}
