package com.upex.reconciliation.service.service.impl;

import com.upex.reconciliation.service.service.BillContractProfitSymbolDetailService;
import com.upex.reconciliation.service.dao.entity.BillContractProfitSymbolDetail;
import com.upex.reconciliation.service.dao.mapper.BillContractProfitSymbolDetailMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_coin_detail(币种维度盈亏明细表)】的数据库操作Service实现
 * @createDate 2023-06-09 17:18:46
 */
@Service
public class BillContractProfitSymbolDetailServiceImpl implements BillContractProfitSymbolDetailService {

    @Resource(name = "billContractProfitSymbolDetailMapperWrapper")
    private BillContractProfitSymbolDetailMapper billContractProfitSymbolDetailMapper;

    @Override
    public int batchInsert(Byte accountType, String accountParam, List<BillContractProfitSymbolDetail> billContractProfitCoinDetailList) {
        if (CollectionUtils.isNotEmpty(billContractProfitCoinDetailList)) {
            //MonitorSummaryUtil.record(JobMonitorMetricNameEnum.BILL_CONTRACT_PROFIT_COIN_DETAIL_INSERT_COUNT.getName(), billContractProfitCoinDetails.size());
            return billContractProfitSymbolDetailMapper.batchInsert(accountType, accountParam, billContractProfitCoinDetailList);
            //MonitorSummaryUtil.record(JobMonitorMetricNameEnum.BILL_CONTRACT_PROFIT_COIN_DETAIL_INSERT_TIME_SUMMARY.getName() ,new Date().getTime()-currentDate.getTime());
        }
        return 0;
    }

    @Override
    public int deleteByCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return billContractProfitSymbolDetailMapper.deleteByCheckTime(accountType, accountParam, checkTime);
    }

    @Override
    public Boolean batchDelete(Long beginId, Long pageSize, Byte accountType, String accountParam) {
        return billContractProfitSymbolDetailMapper.batchDelete(beginId, pageSize, accountType, accountParam);
    }

    @Override
    public List<BillContractProfitSymbolDetail> selectListByAccountTypeAndCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return billContractProfitSymbolDetailMapper.selectListByAccountTypeAndCheckTime(accountType, accountParam, checkTime);
    }
}




