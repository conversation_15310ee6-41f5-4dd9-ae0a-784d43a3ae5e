package com.upex.reconciliation.service.common.exception;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 软性校验异常,不影响后续执行
 * @ClassName: SoftnessValidataException
 * @date 2022/4/29 3:49 PM
 * <AUTHOR>
*/
@Data
@NoArgsConstructor
public class SoftnessValidataException extends RuntimeException {
    /**
     * 状态码
     */
    private Integer code;

    /**
     * 异常信息
     */
    private String msg;

    public SoftnessValidataException(Integer code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }
}
