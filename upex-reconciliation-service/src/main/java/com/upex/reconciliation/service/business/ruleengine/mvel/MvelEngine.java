package com.upex.reconciliation.service.business.ruleengine.mvel;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.common.constants.AlarmTemplateLevelEnum;
import com.upex.reconciliation.service.common.constants.AlarmTemplateQpsEnum;
import com.upex.reconciliation.service.common.constants.AlarmTemplateTypeEnum;
import com.upex.reconciliation.service.model.alarm.AlarmParam;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.EnvUtil;
import com.upex.reconciliation.service.utils.MatchUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.mvel2.MVEL;
import org.mvel2.ParserContext;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;

/**
 * 表达式引擎
 */
public class MvelEngine {
    /***默认MVEL表达式缓存***/
    protected final static Cache<String, Serializable> DEFAULT_EXPRESSION_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofHours(2))
            .build();

    /**
     * 执行表达式
     *
     * @param expression 表达式语句
     * @param ctx        被访问的参数
     * @return 执行结果
     */
    public static Object evaluate(String expression, Object ctx) {
        return evaluate(expression, ctx, null, DEFAULT_EXPRESSION_CACHE);
    }

    /**
     * 执行表达式
     *
     * @param expression      表达式语句
     * @param ctx             被访问的参数
     * @param expressionCache 表达式编译缓存
     * @return 执行结果
     */
    private static Object evaluate(String expression, Object ctx, Map vars, Cache<String, Serializable> expressionCache) {
        // 表达式为空获取参数为空，直接返回空
        if (StringUtils.isBlank(expression)) {
            return null;
        }
        // 从缓存中获取编译好的表达式，存在这执行，不存在先编译再执行
        String md5Hex = DigestUtil.md5Hex(expression);
        Serializable serializable = expressionCache.getIfPresent(md5Hex);
        if (serializable == null) {
            synchronized (expressionCache) {
                ParserContext context = new ParserContext();
                context.addImport("MVEL", MVEL.class);
                context.addImport("StringUtils", StringUtils.class);
                context.addImport("CollectionUtils", CollectionUtils.class);
                context.addImport("MatchUtils", MatchUtils.class);
                context.addImport("JSONObject", JSONObject.class);
                context.addImport("JSON", JSON.class);
                context.addImport("Date", Date.class);
                context.addImport("DateUtil", DateUtil.class);
                context.addImport("Arrays", Arrays.class);

                context.addImport("String", String.class);
                context.addImport("Integer", Integer.class);
                context.addImport("Byte", Byte.class);
                context.addImport("Long", Long.class);
                context.addImport("Boolean", Boolean.class);
                context.addImport("BigDecimal", BigDecimal.class);
                context.addImport("List", List.class);
                context.addImport("ArrayList", ArrayList.class);
                context.addImport("Set", Set.class);
                context.addImport("Map", Map.class);
                context.addImport("HashMap", HashMap.class);
                context.addImport("CommonBillChangeData", CommonBillChangeData.class);
                context.addImport("AlarmTemplateEnum", AlarmTemplateEnum.class);
                context.addImport("EnvUtil", EnvUtil.class);
                context.addImport("AlarmTemplateTypeEnum", AlarmTemplateTypeEnum.class);
                context.addImport("AlarmTemplateQpsEnum", AlarmTemplateQpsEnum.class);
                context.addImport("AlarmTemplateLevelEnum", AlarmTemplateLevelEnum.class);
                context.addImport("AlarmParam", AlarmParam.class);
                context.addImport("Lists", Lists.class);
                context.addImport("Pair", Pair.class);
                serializable = MVEL.compileExpression(expression, context);
                // 不做缓存，mvel存在bug
                expressionCache.put(md5Hex, serializable);
            }
        }
        return MVEL.executeExpression(serializable, ctx, vars);
    }
}
