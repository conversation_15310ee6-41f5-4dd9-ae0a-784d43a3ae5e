package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SpotPositionInnerRes {

    /**
     *  {
     *         "asset": "USDT",
     *         "free": "1",
     *         "locked": "0",
     *         "freeze": "0",
     *         "withdrawing": "0",
     *         "ipoable": "0",
     *         "btcValuation": "0"
     *     }
     */

    private String asset;
    private BigDecimal free;
    private BigDecimal locked;
    private BigDecimal freeze;
    private BigDecimal withdrawing;
    private BigDecimal ipoable;
    private BigDecimal btcValuation;

}
