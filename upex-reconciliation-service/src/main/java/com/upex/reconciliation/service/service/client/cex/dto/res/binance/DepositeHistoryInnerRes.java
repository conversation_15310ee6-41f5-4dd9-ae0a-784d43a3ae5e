package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.math.BigDecimal;
@Data
public class DepositeHistoryInnerRes {

    /**
     * {
     *         "id": "769800519366885376",
     *         "amount": "0.001",
     *         "coin": "BNB",
     *         "network": "BNB",
     *         "status": 1,
     *         "address": "bnb136ns6lfw4zs5hg4n85vdthaad7hq5m4gtkgf23",
     *         "addressTag": "101764890",
     *         "txId": "98A3EA560C6B3336D348B6C83F0F95ECE4F1F5919E94BD006E5BF3BF264FACFC",
     *         "insertTime": 1661493146000,
     *         "completeTime":1661493146000,
     *         "transferType": 0,
     *         "confirmTimes": "1/1",
     *         "unlockConfirm": 0,
     *         "walletType": 0
     *     }
     */
    private String id;
    private String coin;
    private String network;
    /**
     * 0(0:待确认,6:已上账待解锁,7:错误充值,8:待用户申请确认,1:成功,2:已拒绝)
     */
    private Integer status;
    private String txId;
    private String address;
    private String addressTag;
    private String asset;
    private BigDecimal amount;
    private Long insertTime;
    private Long completeTime;
    private Integer transferType;
    private String confirmTimes;
    private Integer unlockConfirm;
    private Integer walletType;


}
