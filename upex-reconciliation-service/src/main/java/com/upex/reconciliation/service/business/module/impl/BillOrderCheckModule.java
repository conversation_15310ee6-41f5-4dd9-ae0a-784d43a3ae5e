package com.upex.reconciliation.service.business.module.impl;

import com.alibaba.fastjson.JSON;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.business.UserBeginAssetsRedisService;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.common.concurrent.ConcurrentSortMap;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.LogLevelEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillAllConfig;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty;
import com.upex.reconciliation.service.dao.entity.OrderBillConfig;
import com.upex.reconciliation.service.model.BillCmdResult;
import com.upex.reconciliation.service.model.BillCmdWrapper;
import com.upex.reconciliation.service.model.config.ApolloBillOrderDetailConfig;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.model.dto.AccountDealtRecordData;
import com.upex.reconciliation.service.model.dto.BillOrderCheckTimeSliceDTO;
import com.upex.reconciliation.service.model.enums.DiffMapKeyEnum;
import com.upex.reconciliation.service.service.BillAllConfigService;
import com.upex.reconciliation.service.service.BillCoinTypeUserPropertyService;
import com.upex.reconciliation.service.service.OrderBillConfigService;
import com.upex.reconciliation.service.utils.*;
import com.upex.utils.log.AlarmUtils;
import com.upex.utils.task.TaskManager;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.*;
import static com.upex.reconciliation.service.utils.MetricsUtil.HISTOGRAM_ORDER_CHECK_COMMAND;


/**
 * 订单对账模块
 */
@Slf4j
public class BillOrderCheckModule {
    /***批量处理变化用户数***/
    private static final Integer CHANGE_USER_BATCH_SIZE = 50000;
    /***命令处理队列***/
    private BlockingQueue<BillCmdWrapper> cmdQueue = new ArrayBlockingQueue<>(3000);
    private AccountTypeEnum accountTypeEnum;
    private CommonService commonService;
    private BillEngineManager billEngineManager;
    private BillAllConfigService billAllConfigService;
    private AlarmNotifyService alarmNotifyService;
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    private SerialNoGenerator noGenerator;
    private RedisTemplate<String, Object> reconRedisTemplate;
    private TaskManager userProfitRtTaskManager;
    private TaskManager userBeginAssetsTaskManager;
    private UserBeginAssetsRedisService userBeginAssetsRedisService;
    /***盈利计算时间片窗口***/
    private final ConcurrentSortMap<Long, BillOrderCheckTimeSliceDTO> startTimeSliceDTOMap = new ConcurrentSortMap();
    /***业务key****/
    private String businessKey;
    private OrderBillConfigService orderBillConfigService;
    private BillCoinTypeUserPropertyService billCoinTypeUserPropertyService;
    /***对账通过时间***/
    private Date lastCheckTime;
    /***partition biz time***/
    private Map<Integer, Long> latestPartitionBizTime = new HashMap<>();
    /***消费key***/
    private String kafkaConsumerKey;

    public BillOrderCheckModule(String businessKey, AccountTypeEnum accountTypeEnum, ReconciliationSpringContext reconciliationSpringContext, BillEngineManager billEngineManager, String kafkaConsumerKey) {
        this.kafkaConsumerKey = kafkaConsumerKey;
        this.businessKey = businessKey;
        this.accountTypeEnum = accountTypeEnum;
        this.commonService = reconciliationSpringContext.getCommonService();
        this.billEngineManager = billEngineManager;
        this.billAllConfigService = reconciliationSpringContext.getBillAllConfigService();
        this.alarmNotifyService = reconciliationSpringContext.getAlarmNotifyService();
        this.accountAssetsServiceFactory = reconciliationSpringContext.getAccountAssetsServiceFactory();
        this.noGenerator = reconciliationSpringContext.getSerialNoGenerator();
        this.reconRedisTemplate = reconciliationSpringContext.getReconRedisTemplate();
        this.userProfitRtTaskManager = reconciliationSpringContext.getUserProfitRtTaskManager();
        this.userBeginAssetsTaskManager = reconciliationSpringContext.getUserBeginAssetsTaskManager();
        this.userBeginAssetsRedisService = reconciliationSpringContext.getUserBeginAssetsRedisService();
        this.orderBillConfigService = reconciliationSpringContext.getOrderBillConfigService();
        this.billCoinTypeUserPropertyService = reconciliationSpringContext.getBillCoinTypeUserPropertyService();
        // 启动消费线程
        new Thread(() -> {
            while (true) {
                takeCommand();
            }
        }, "order-check-timeslice-" + businessKey).start();
    }

    public void offerCommand(BillCmdWrapper cmdWrapper) {
        try {
            this.cmdQueue.put(cmdWrapper);
        } catch (InterruptedException e) {
            String errorMsg = "BillOrderCheckModule Command queue is full : " + cmdWrapper.getCommandEnum().getName()
                    + " queue size: " + this.cmdQueue.size() + " logicGroup: " + accountTypeEnum.getCode();
            AlarmUtils.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }
    }

    private BillCmdResult takeCommand() {
        BillCmdWrapper cmdWrapper = null;
        try {
            cmdWrapper = this.cmdQueue.poll(1, TimeUnit.SECONDS);
            if (cmdWrapper == null) {
                return null;
            }
            BillCmdWrapper finalCmdWrapper = cmdWrapper;
            return MetricsUtil.histogram(HISTOGRAM_ORDER_CHECK_COMMAND + accountTypeEnum.getCode(), () -> execCommand(finalCmdWrapper));
        } catch (Exception e) {
            log.error("BillOrderCheckModule check failed with error accountType {} data {} {}", accountTypeEnum.getCode(), cmdWrapper == null ? "" : JSON.toJSONString(cmdWrapper), e);
            alarmNotifyService.alarm(accountTypeEnum.getCode(), ORDER_CHECK_MODULE_TAKE_COMMAND_ERROR, accountTypeEnum.getCode());
        }
        return null;
    }

    private BillCmdResult execCommand(BillCmdWrapper cmdWrapper) {
        AccountDealtRecordData accountDealtRecordData = (AccountDealtRecordData) cmdWrapper.getCommandData();
        latestPartitionBizTime.put(accountDealtRecordData.getPartition(), accountDealtRecordData.getBizTime().getTime());
        Boolean messageDealtResult = false;
        ApolloBillOrderDetailConfig apolloBillOrderDetailConfig = ReconciliationApolloConfigUtils.getApolloBillOrderDetailConfig(this.businessKey);
        try {
            List<BillCoinTypeUserProperty> billCoinTypeUserPropertyList = accountDealtRecordData.convertToCoinTypeUserPropertyList(this.businessKey, apolloBillOrderDetailConfig, alarmNotifyService);
            if (CollectionUtils.isEmpty(billCoinTypeUserPropertyList)) {
                return null;
            }

            // 时间片计算
            for (BillCoinTypeUserProperty billCoinTypeUserProperty : billCoinTypeUserPropertyList) {
                // 计算时间片
                GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
                long timeOffset = TimeSliceCalcUtils.getTimeSlice(billCoinTypeUserProperty.getCheckTime().getTime(), globalBillConfig.getMergeTimeSliceSizeSecond());
                BillOrderCheckTimeSliceDTO billOrderCheckTimeSliceDTO = startTimeSliceDTOMap.computeIfAbsent(timeOffset, v -> new BillOrderCheckTimeSliceDTO(timeOffset));

                // 计算 用户-》币种-》交易类型
                billOrderCheckTimeSliceDTO.getUserCoinBizTypeProfitMap().computeIfAbsent(billCoinTypeUserProperty.getUserId(), v -> new ConcurrentHashMap<>()).computeIfAbsent(billCoinTypeUserProperty.getCoinId(), v -> new ConcurrentHashMap<>()).compute(billCoinTypeUserProperty.getBizType(), (k, oldValue) -> {
                    BillCoinTypeUserProperty property = null;
                    if (oldValue != null) {
                        property = oldValue;
                        property.setChangeProp1(property.getChangeProp1().add(billCoinTypeUserProperty.getChangeProp1()));
                        property.setChangeProp2(property.getChangeProp2().add(billCoinTypeUserProperty.getChangeProp2()));
                        property.setChangeProp3(property.getChangeProp3().add(billCoinTypeUserProperty.getChangeProp3()));
                        property.setChangeProp4(property.getChangeProp4().add(billCoinTypeUserProperty.getChangeProp4()));
                        property.setChangeProp5(property.getChangeProp5().add(billCoinTypeUserProperty.getChangeProp5()));
                        property.setChangeProp6(property.getChangeProp6().add(billCoinTypeUserProperty.getChangeProp6()));
                        property.setChangeProp7(property.getChangeProp7().add(billCoinTypeUserProperty.getChangeProp7()));
                        property.setChangeProp8(property.getChangeProp8().add(billCoinTypeUserProperty.getChangeProp8()));
                    } else {
                        property = BeanCopierUtil.copyProperties(billCoinTypeUserProperty, BillCoinTypeUserProperty.class);
                    }
                    return property;
                });
            }
            messageDealtResult = true;
        } finally {
            BizLogUtils.log(LogLevelEnum.KEY_RESULT, apolloBillOrderDetailConfig.getDefaultLogLeve(), "BillOrderCheckModule.execCommand end userId:{} bizId:{} bizTime:{} messageDealtResult:{}", accountDealtRecordData.getAccountId(), accountDealtRecordData.getBizId(), DateUtil.date2str(accountDealtRecordData.getBizTime()), messageDealtResult);
        }
        return null;
    }

    /**
     * 执行对账逻辑
     */
    public void billOrderCheck(ApolloBillOrderDetailConfig apolloBillOrderDetailConfig) {
        OrderBillConfig orderBillConfig = orderBillConfigService.getOrderBillConfig(apolloBillOrderDetailConfig.getOrderType(), apolloBillOrderDetailConfig.getOrderParam());
        if (orderBillConfig == null) {
            return;
        }
        this.lastCheckTime = orderBillConfig.getCheckOkTime();

        while (canExecute(orderBillConfig, apolloBillOrderDetailConfig)) {
            Date endCheckOkTime = new Date(orderBillConfig.getCheckOkTime().getTime() + apolloBillOrderDetailConfig.getTimeInterval());
            boolean billCheckResult = doBillOrderCheck(apolloBillOrderDetailConfig, orderBillConfig.getCheckOkTime(), endCheckOkTime);
            log.info("BillOrderCheckModule billOrderCheck orderType:{} beginCheckOkTime:{} endCheckTime:{} result:{}",
                    orderBillConfig.getOrderType(), DateUtil.getDefaultDateStr(orderBillConfig.getCheckOkTime()), DateUtil.getDefaultDateStr(endCheckOkTime), billCheckResult);
            if (!billCheckResult) {
                break;
            }
            // 对账通过 移除时间片
            for (Map.Entry<Long, BillOrderCheckTimeSliceDTO> entry : startTimeSliceDTOMap.entrySet()) {
                Long timeSliceKey = entry.getKey();
                if (timeSliceKey <= endCheckOkTime.getTime()) {
                    startTimeSliceDTOMap.remove(timeSliceKey);
                } else {
                    break;
                }
            }
            // 更新配置表
            this.lastCheckTime = endCheckOkTime;
            orderBillConfig.setCheckOkTime(endCheckOkTime);
            orderBillConfigService.updateOrderBillConfig(orderBillConfig);
            orderBillConfig = orderBillConfigService.getOrderBillConfig(orderBillConfig.getOrderType(), orderBillConfig.getOrderParam());
        }
    }

    /**
     * 是否可以对账
     *
     * @param orderBillConfig
     * @param apolloBillOrderDetailConfig
     * @return
     */
    private boolean canExecute(OrderBillConfig orderBillConfig, ApolloBillOrderDetailConfig apolloBillOrderDetailConfig) {
        log.info("BillOrderCheckModule canExecute orderType:{} beginCheckOkTime:{} timeInterval:{} billCheckBeforeBizTimeThreshold:{} partitionBizTime:{}", apolloBillOrderDetailConfig.getOrderType(), DateUtil.getDefaultDateStr(orderBillConfig.getCheckOkTime()), apolloBillOrderDetailConfig.getTimeInterval(), apolloBillOrderDetailConfig.getBillCheckBeforeBizTimeThreshold(), JSON.toJSONString(latestPartitionBizTime));

        // 获得对账最小配置
        BillAllConfig minCheckOkBillConfig = billAllConfigService.getMinCheckOkBillConfig(apolloBillOrderDetailConfig.getSubSystemList());
        // 最新订单对账的check_ok_time - 各子业务线中最小的check_ok_time > 5min
        if (minCheckOkBillConfig.getCheckOkTime().getTime() - orderBillConfig.getCheckOkTime().getTime() < apolloBillOrderDetailConfig.getTimeInterval()) {
            return false;
        }
        // 判断partition时间
        Long minPartitionBizTime = getMinPartitionBizTime();
        if (minPartitionBizTime == null || (minPartitionBizTime - orderBillConfig.getCheckOkTime().getTime() - apolloBillOrderDetailConfig.getTimeInterval()) < apolloBillOrderDetailConfig.getBillCheckBeforeBizTimeThreshold()) {
            return false;
        }
        return true;
    }

    /**
     * 执行对账逻辑
     *
     * @param apolloBillOrderDetailConfig
     * @param beginCheckOkTime
     * @param endCheckOkTime
     * @return
     */
    public boolean doBillOrderCheck(ApolloBillOrderDetailConfig apolloBillOrderDetailConfig, Date beginCheckOkTime, Date endCheckOkTime) {
        BillAllConfig minCheckOkBillConfig = billAllConfigService.getMinCheckOkBillConfig();

        // 获取订单数据
        List<BillCoinTypeUserProperty> sourceBillCoinTypeUserPropertyList = getSourceBillCoinTypeUserPropertyList(apolloBillOrderDetailConfig, beginCheckOkTime, endCheckOkTime);

        // 【币种 + 类型 + 个人】维度
        if (apolloBillOrderDetailConfig.isUserAssetsOpen()) {
            boolean checkPass = checkCoinTypeUserAssets(apolloBillOrderDetailConfig, sourceBillCoinTypeUserPropertyList, beginCheckOkTime, endCheckOkTime);
            log.info("BillOrderCheckModule doBillOrderCheck checkCoinTypeUserAssets orderType:{} beginCheckOkTime:{} endCheckOkTime:{} result:{}", apolloBillOrderDetailConfig.getOrderType(), DateUtil.getDefaultDateStr(beginCheckOkTime), DateUtil.getDefaultDateStr(endCheckOkTime), checkPass);
            if (!checkPass) {
                return false;
            }
        }
        return true;
    }

    /**
     * 用户维度订单对账
     *
     * @param apolloBillOrderDetailConfig
     * @param sourceCoinTypeUserPropertyList
     * @return
     */
    private boolean checkCoinTypeUserAssets(ApolloBillOrderDetailConfig apolloBillOrderDetailConfig, List<BillCoinTypeUserProperty> sourceCoinTypeUserPropertyList, Date beginCheckOkTime, Date endCheckOkTime) {
        // 获取理财虚拟账户、个人现货账户 入出业务类型
        List<String> sourceBizTypeList = apolloBillOrderDetailConfig.getBizTypeList(DiffMapKeyEnum.USER_SOURCE_LIST.getKey());
        List<String> targetBizTypeList = apolloBillOrderDetailConfig.getBizTypeList(DiffMapKeyEnum.USER_TARGET_LIST.getKey());
        log.info("BillOrderCheckModule checkCoinTypeUserAssets start orderType:{} beginCheckOkTime:{} endCheckOkTime:{} sourceCoinTypeUserPropertyListSize:{} sourceBizTypeList:{} targetBizTypeList:{}", apolloBillOrderDetailConfig.getOrderType(), DateUtil.getDefaultDateStr(beginCheckOkTime), DateUtil.getDefaultDateStr(endCheckOkTime), sourceCoinTypeUserPropertyList.size(), JSON.toJSONString(sourceBizTypeList), JSON.toJSONString(targetBizTypeList));
        if (CollectionUtils.isEmpty(sourceBizTypeList) || CollectionUtils.isEmpty(targetBizTypeList)) {
            return false;
        }

        // beginCheckOkTime 是上一次对账通过的时间  selectBetweenTime 查询是>= <= 所有时间得加一个周期
        Byte accountType = apolloBillOrderDetailConfig.getTargetAccountType(DiffMapKeyEnum.USER_TARGET_LIST.getKey());
        Long beginCheckTime = beginCheckOkTime.getTime() + BillConstants.FIVE_MINE_MIL_SEC;
        List<BillCoinTypeUserProperty> targetCoinTypeUserPropertyList = billCoinTypeUserPropertyService.selectSumByCheckTime(accountType, beginCheckTime, endCheckOkTime.getTime());

        // 根据个人 + 币种维度分组
        Map<String, BillCoinUserProperty> sourceFlowMap = mergeAndFilterCoinTypeUserProperty(sourceCoinTypeUserPropertyList, sourceBizTypeList);
        Map<String, BillCoinUserProperty> targetFlowMap = mergeAndFilterCoinTypeUserProperty(targetCoinTypeUserPropertyList, targetBizTypeList);
        if (MapUtils.isEmpty(sourceFlowMap) && MapUtils.isEmpty(targetFlowMap)) {
            log.info("BillOrderCheckModule checkCoinTypeUserAssets sourceFlowMap targetFlowMap is empty orderType:{} beginCheckOkTime:{} endCheckOkTime:{} sourceCoinTypeUserPropertyListSize:{} targetCoinTypeUserPropertyListSize:{}", apolloBillOrderDetailConfig.getOrderType(), DateUtil.getDefaultDateStr(beginCheckOkTime), DateUtil.getDefaultDateStr(endCheckOkTime), sourceCoinTypeUserPropertyList.size(), targetCoinTypeUserPropertyList.size());
            return true;
        }

        // 获取数据大小
        int sourceFlowSize = CollectionUtils.size(sourceFlowMap);
        int targetFlowSize = CollectionUtils.size(targetFlowMap);
        Collection<String> difference = CollectionUtils.disjunction(sourceFlowMap.keySet(), targetFlowMap.keySet());
        if (sourceFlowSize != targetFlowSize || CollectionUtils.size(difference) > 0) {
            alarmNotifyService.alarm(accountType, DEALT_RECORD_VS_BILL_CHECK_NUMBER_ERROR, DateUtil.longToDate(beginCheckOkTime.getTime()), DateUtil.longToDate(endCheckOkTime.getTime()), sourceFlowSize, targetFlowSize, JSON.toJSONString(difference));
            // 打印日志明细
            for (String userCoinId : difference) {
                List<BillCoinTypeUserProperty> logCoinTypeUserPropertyList = (sourceFlowMap.get(userCoinId) != null ? sourceCoinTypeUserPropertyList : targetCoinTypeUserPropertyList)
                        .stream().filter(e -> GroupByKeyUtil.groupByUserIdAndCoinId(e.getUserId(), e.getCoinId()).equals(userCoinId)).collect(Collectors.toList());
                BillCoinUserProperty logProperty = sourceFlowMap.get(userCoinId) != null ? sourceFlowMap.get(userCoinId) : targetFlowMap.get(userCoinId);
                log.info("BillOrderCheckModule checkCoinTypeUserAssets sourceFlowMap targetFlowMap size is not eq orderType:{} beginCheckOkTime:{} endCheckOkTime:{} sourceCoinTypeUserPropertyListSize:{} targetCoinTypeUserPropertyListSize:{} userCoinId:{} detail:{} logProperty:{}", apolloBillOrderDetailConfig.getOrderType(), DateUtil.getDefaultDateStr(beginCheckOkTime), DateUtil.getDefaultDateStr(endCheckOkTime), sourceCoinTypeUserPropertyList.size(), targetCoinTypeUserPropertyList.size(), userCoinId, JSON.toJSONString(logCoinTypeUserPropertyList), JSON.toJSONString(logProperty));
            }
            return false;
        }

        for (Map.Entry<String, BillCoinUserProperty> entry : sourceFlowMap.entrySet()) {
            BillCoinUserProperty sourceProperty = entry.getValue();
            BillCoinUserProperty targetProperty = targetFlowMap.getOrDefault(entry.getKey(), new BillCoinUserProperty());
            BigDecimal diffBalance = sourceProperty.getChangePropSum().subtract(targetProperty.getChangePropSum());
            if (diffBalance.compareTo(BigDecimal.ZERO) != 0) {
                String userCoinId = GroupByKeyUtil.groupByUserIdAndCoinId(sourceProperty.getUserId(), sourceProperty.getCoinId());
                List<BillCoinTypeUserProperty> sourceLogCoinTypeUserPropertyList = sourceCoinTypeUserPropertyList.stream().filter(e -> GroupByKeyUtil.groupByUserIdAndCoinId(e.getUserId(), e.getCoinId()).equals(userCoinId)).collect(Collectors.toList());
                List<BillCoinTypeUserProperty> targetLogCoinTypeUserPropertyList = targetCoinTypeUserPropertyList.stream().filter(e -> GroupByKeyUtil.groupByUserIdAndCoinId(e.getUserId(), e.getCoinId()).equals(userCoinId)).collect(Collectors.toList());
                log.info("BillOrderCheckModule checkCoinTypeUserAssets sourceFlowMap targetFlowMap sum is not eq orderType:{} beginCheckOkTime:{} endCheckOkTime:{} sourceCoinTypeUserPropertyListSize:{} targetCoinTypeUserPropertyListSize:{} diffBalance:{} sourceProperty:{} targetProperty:{} sourceDetail:{} targetDetail:{}", apolloBillOrderDetailConfig.getOrderType(), DateUtil.getDefaultDateStr(beginCheckOkTime), DateUtil.getDefaultDateStr(endCheckOkTime), sourceCoinTypeUserPropertyList.size(), targetCoinTypeUserPropertyList.size(), diffBalance, JSON.toJSONString(sourceProperty), JSON.toJSONString(targetProperty), JSON.toJSONString(sourceLogCoinTypeUserPropertyList), JSON.toJSONString(targetLogCoinTypeUserPropertyList));
                alarmNotifyService.alarm(accountType, DEALT_RECORD_VS_BILL_CHECK_AMOUNT_ERROR, apolloBillOrderDetailConfig.getOrderDesc(), sourceProperty.getUserId(), sourceProperty.getCoinId(), DateUtil.getDefaultDateStr(beginCheckOkTime), DateUtil.getDefaultDateStr(endCheckOkTime), sourceProperty.getChangePropSum().toPlainString(), targetProperty.getChangePropSum().toPlainString(), diffBalance.toPlainString());
                return false;
            }
        }

        log.info("BillOrderCheckModule checkCoinTypeUserAssets end orderType:{} beginCheckOkTime:{} endCheckOkTime:{} sourceCoinTypeUserPropertyListSize:{} targetCoinTypeUserPropertyListSize:{}", apolloBillOrderDetailConfig.getOrderType(), DateUtil.getDefaultDateStr(beginCheckOkTime), DateUtil.getDefaultDateStr(endCheckOkTime), sourceCoinTypeUserPropertyList.size(), targetCoinTypeUserPropertyList.size());
        return true;
    }

    /**
     * 获取源数据 > <=
     *
     * @param apolloBillOrderDetailConfig
     * @param beginCheckOkTime
     * @param endCheckOkTime
     * @return
     */
    private List<BillCoinTypeUserProperty> getSourceBillCoinTypeUserPropertyList(ApolloBillOrderDetailConfig apolloBillOrderDetailConfig, Date beginCheckOkTime, Date endCheckOkTime) {
        Map<Long, Map<Integer, Map<String, BillCoinTypeUserProperty>>> userCoinBizTypeProfitMap = new HashMap<>();
        for (Map.Entry<Long, BillOrderCheckTimeSliceDTO> entry : startTimeSliceDTOMap.entrySet()) {
            Long timeSliceKey = entry.getKey();
            if (timeSliceKey <= beginCheckOkTime.getTime()) {
                continue;
            }
            if (timeSliceKey > endCheckOkTime.getTime()) {
                break;
            }
            entry.getValue().getUserCoinBizTypeProfitMap().forEach((userId, coinIdBizTypeProfitMap) -> {
                coinIdBizTypeProfitMap.forEach((coinId, bizTypeProfitMap) -> {
                    bizTypeProfitMap.forEach((bizType, property) -> {
                        userCoinBizTypeProfitMap.computeIfAbsent(userId, v -> new HashMap<>()).computeIfAbsent(coinId, v -> new HashMap<>()).compute(bizType, (k, oldValue) -> {
                            BillCoinTypeUserProperty newProperty = null;
                            if (oldValue != null) {
                                newProperty = oldValue;
                                newProperty.setChangeProp1(newProperty.getChangeProp1().add(property.getChangeProp1()));
                                newProperty.setChangeProp2(newProperty.getChangeProp2().add(property.getChangeProp2()));
                                newProperty.setChangeProp3(newProperty.getChangeProp3().add(property.getChangeProp3()));
                                newProperty.setChangeProp4(newProperty.getChangeProp4().add(property.getChangeProp4()));
                                newProperty.setChangeProp5(newProperty.getChangeProp5().add(property.getChangeProp5()));
                                newProperty.setChangeProp6(newProperty.getChangeProp6().add(property.getChangeProp6()));
                                newProperty.setChangeProp7(newProperty.getChangeProp7().add(property.getChangeProp7()));
                                newProperty.setChangeProp8(newProperty.getChangeProp8().add(property.getChangeProp8()));
                            } else {
                                newProperty = BeanCopierUtil.copyProperties(property, BillCoinTypeUserProperty.class);
                            }
                            return newProperty;
                        });
                    });
                });
            });
        }
        List<BillCoinTypeUserProperty> billCoinTypeUserPropertyList = new ArrayList<>();
        userCoinBizTypeProfitMap.forEach((userId, coinIdBizTypeProfitMap) -> {
            coinIdBizTypeProfitMap.forEach((coinId, bizTypeProfitMap) -> {
                bizTypeProfitMap.forEach((bizType, property) -> {
                    billCoinTypeUserPropertyList.add(property);
                });
            });
        });
        return billCoinTypeUserPropertyList;
    }

    /**
     * 合并流水
     *
     * @param billCoinTypeUserPropertyList
     * @param inOutTypeList
     * @return
     */
    private Map<String, BillCoinUserProperty> mergeAndFilterCoinTypeUserProperty(List<BillCoinTypeUserProperty> billCoinTypeUserPropertyList, List<String> inOutTypeList) {
        Map<String, BillCoinUserProperty> result = new HashMap<>();
        if (CollectionUtils.isEmpty(billCoinTypeUserPropertyList)) {
            return result;
        }

        billCoinTypeUserPropertyList.stream().filter(e -> inOutTypeList.contains(e.getBizType())).forEach(property -> {
            result.compute(GroupByKeyUtil.groupByUserIdAndCoinId(property.getUserId(), property.getCoinId()), (k, oldValue) -> {
                BillCoinUserProperty newProperty = null;
                if (oldValue != null) {
                    newProperty = oldValue;
                    newProperty.setChangeProp1(newProperty.getChangeProp1().add(property.getChangeProp1()));
                    newProperty.setChangeProp2(newProperty.getChangeProp2().add(property.getChangeProp2()));
                    newProperty.setChangeProp3(newProperty.getChangeProp3().add(property.getChangeProp3()));
                    newProperty.setChangeProp4(newProperty.getChangeProp4().add(property.getChangeProp4()));
                    newProperty.setChangeProp5(newProperty.getChangeProp5().add(property.getChangeProp5()));
                    newProperty.setChangeProp6(newProperty.getChangeProp6().add(property.getChangeProp6()));
                    newProperty.setChangeProp7(newProperty.getChangeProp7().add(property.getChangeProp7()));
                    newProperty.setChangeProp8(newProperty.getChangeProp8().add(property.getChangeProp8()));
                } else {
                    newProperty = new BillCoinUserProperty();
                    newProperty.setUserId(property.getUserId());
                    newProperty.setCoinId(property.getCoinId());
                    newProperty.setChangeProp1(property.getChangeProp1());
                    newProperty.setChangeProp2(property.getChangeProp2());
                    newProperty.setChangeProp3(property.getChangeProp3());
                    newProperty.setChangeProp4(property.getChangeProp4());
                    newProperty.setChangeProp5(property.getChangeProp5());
                    newProperty.setChangeProp6(property.getChangeProp6());
                    newProperty.setChangeProp7(property.getChangeProp7());
                    newProperty.setChangeProp8(property.getChangeProp8());
                }
                return newProperty;
            });
        });

        // 过滤掉金额为0的流水
        result.entrySet().removeIf(entry -> entry.getValue().isChangePropSumZero());
        return result;
    }

    public void setLastCheckTime(Date lastCheckTime) {
        this.lastCheckTime = lastCheckTime;
    }

    public Date getLastCheckTime() {
        return lastCheckTime;
    }

    /**
     * 获取position时间
     *
     * @return
     */
    public Long getMinPartitionBizTime() {
        KafkaConsumerConfig kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig().getConsumerConfig().get(this.kafkaConsumerKey);
        return latestPartitionBizTime.size() < kafkaConsumerConfig.getPartitionNum() ? null :
                latestPartitionBizTime.values().stream().min(Comparator.comparing(x -> x)).orElse(null);
    }
}