package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;
import net.minidev.json.annotate.JsonIgnore;

@Data
public class ApikeyPermissionRes implements IBinanceApiBaseRes {

    /**
     * "ipRestrict": true,
     * "createTime": 1747900105000,
     * "enableVanillaOptions": false,
     * "enableReading": true,
     * "enableWithdrawals": true,
     * "enableInternalTransfer": true,
     * "enableMargin": true,
     * "enableFixApiTrade": false,
     * "enableFixReadOnly": false,
     * "enableSpotAndMarginTrading": true,
     * "permitsUniversalTransfer": false,
     * "enablePortfolioMarginTrading": false,
     * "enableFutures": true
     **/
    private Boolean ipRestrict;
    private Long createTime;
    private Boolean enableReading;
    private Boolean enableWithdrawals;
    private Boolean enableInternalTransfer;
    private Boolean enableMargin;
    private Boolean enableFutures;
    private Boolean permitsUniversalTransfer;
    @JsonIgnore
    private Boolean enableVanillaOptions;
    @JsonIgnore
    private Boolean enableFixApiTrade;
    @JsonIgnore
    private Boolean enableFixReadOnly;
    private Boolean enableSpotAndMarginTrading;
    private Boolean enablePortfolioMarginTrading;

    public Boolean isReadOnly() {
        return enableReading &&!enableSpotAndMarginTrading&&!enablePortfolioMarginTrading&&!enableFutures&&!enableMargin&&!enableInternalTransfer&&!enableWithdrawals;
    }
}
