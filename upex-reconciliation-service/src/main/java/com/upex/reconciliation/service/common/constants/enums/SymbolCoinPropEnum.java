package com.upex.reconciliation.service.common.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SymbolCoinPropEnum {
    MARGIN_UN_REALIZED("未实现保证金币金额（币本位独有）"),
    MARGIN_REALIZED("已实现右币金额"),
    REALIZED("已实现保证金币金额"),
    RE_REALIZED("重算已实现右币金额(排除重算用户)");
    private String propName;
}
