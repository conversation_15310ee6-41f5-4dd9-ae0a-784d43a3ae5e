package com.upex.reconciliation.service.common.constants.enums;

/**
 * 金额类型枚举
 * <AUTHOR>
 */
public enum BalanceTypeEnum {
    /**
     * 链上总入金
     */
    TOTALIN(1,"链上总入金"),
    /**
     * 链上总出金
     */
    TOTALOUT(2,"链上总出金"),
    /**
     * 内部转账入金
     */
    INTERNALIN(3,"内部转账入金"),
    /**
     * 内部转账出金
     */
    INTERNALOUT(4,"内部转账出金"),
    /**
     * OTC总入金
     */
    OTCIN(5,"OTC总入金"),
    /**
     * OTC总出金
     */
    OTCOUT(6,"OTC总出金"),
    /**
     * 其他收入
     */
    OTHER(7,"其他收入"),
    /**
     * 收入
     */
    INCOME(8,"收入"),
    /**
     * 支出
     */
    SPENDING(9,"支出"),
    /**
     * 手续费
     */
    POUNDAGE(10,"手续费"),
    /**
     * 已实现盈亏
     */
    PROFITANDLOSS(11,"已实现盈亏"),
    /**
     * 资金费率
     */
    FUNDSRATE(12,"资金费率"),
    /**
     * 买入
     */
    BUY(13,"买入"),
    /**
     * 卖出
     */
    SELL(14,"卖出"),
    ;

    private Integer code;
    private String desc;

    BalanceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static BalanceTypeEnum toEnum(Integer code) {
        for (BalanceTypeEnum item : BalanceTypeEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }
}
