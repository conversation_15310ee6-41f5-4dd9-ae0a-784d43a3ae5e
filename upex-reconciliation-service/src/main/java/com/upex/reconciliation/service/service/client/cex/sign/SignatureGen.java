package com.upex.reconciliation.service.service.client.cex.sign;

import com.alibaba.fastjson.JSONObject;
import com.binance.connector.client.common.ApiClient;
import com.binance.connector.client.common.ApiException;
import com.binance.connector.client.common.auth.Authentication;
import com.binance.connector.client.common.auth.BinanceAuthenticationFactory;
import com.binance.connector.client.common.auth.SignatureGeneratorFactory;
import com.binance.connector.client.common.configuration.SignatureConfiguration;
import com.binance.connector.client.common.sign.SignatureGenerator;
import com.upex.reconciliation.service.service.client.cex.dto.req.binance.BinanceApiBaseReq;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import okhttp3.internal.http.HttpMethod;
import okio.Buffer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URI;

import java.util.Map;
@Slf4j
@Component
public class SignatureGen {


    @Resource
    SignatureGeneratorFactory signatureGeneratorFactory;

    @Resource
    BinanceAuthenticationFactory binanceAuthenticationFactory;

    private static final String HEADER_API_KEY = "X-MBX-APIKEY";

    public void buildSignatureHeader(ApiClient apiClient,String apiKey, String secretKey, String method,String path,BinanceApiBaseReq binanceApiBaseReq) {
        final String url = apiClient.buildUrl(null, path, binanceApiBaseReq.getQueryParams(), binanceApiBaseReq.getCollectionQueryParams());
        Map<String,Authentication> authentications =binanceApiBaseReq.getAuthentications();
        SignatureConfiguration signatureConfiguration =buildSignatureGeneratorByPrivate(apiKey,secretKey);
        Authentication authentication =
                binanceAuthenticationFactory.getAuthentication(signatureConfiguration);
        if (authentication != null) {
            authentications.put("binanceSignature", authentication);
        }

        Authentication binanceApiKeyOnly =
                (queryParams, headerParams, cookieParams, payload, method1, uri1) -> {
                    headerParams.put(HEADER_API_KEY, signatureConfiguration.getApiKey());
                };
        authentications.put("binanceApiKeyOnly", binanceApiKeyOnly);
        for (Authentication auth : binanceApiBaseReq.getAuthentications().values()) {
            try {
                auth.applyToParams(binanceApiBaseReq.getQueryParams(), binanceApiBaseReq.getHeaderParams(), binanceApiBaseReq.getCookieParams(), requestBodyToString(buildReqBody(apiClient,binanceApiBaseReq,method)),method, URI.create(url));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }



    private RequestBody buildReqBody(ApiClient apiClient,BinanceApiBaseReq binanceApiBaseReq,String method){
        // prepare HTTP request body
        RequestBody reqBody;
        String contentType = binanceApiBaseReq.getHeaderParams().get("Content-Type");
        String contentTypePure = contentType;
        if (contentTypePure != null && contentTypePure.contains(";")) {
            contentTypePure = contentType.substring(0, contentType.indexOf(";"));
        }
        if (!HttpMethod.permitsRequestBody(method)) {
            reqBody = null;
        } else if ("application/x-www-form-urlencoded".equals(contentTypePure)) {
            reqBody = apiClient.buildRequestBodyFormEncoding(binanceApiBaseReq.getFormParams());
        } else if ("multipart/form-data".equals(contentTypePure)) {
            reqBody = apiClient.buildRequestBodyMultipart(binanceApiBaseReq.getFormParams());
        } else if (binanceApiBaseReq.getBody() == null) {
            if ("DELETE".equals(method)) {
                // allow calling DELETE without sending a request body
                reqBody = null;
            } else {
                // use an empty request body (for POST, PUT and PATCH)
                reqBody =
                        RequestBody.create(
                                "", contentType == null ? null : MediaType.parse(contentType));
            }
        } else {
            reqBody = apiClient.serialize(binanceApiBaseReq.getBody(), contentType);
        }
        return reqBody;

    }

    private String requestBodyToString(RequestBody requestBody) throws ApiException {
        if (requestBody != null) {
            try {
                final Buffer buffer = new Buffer();
                requestBody.writeTo(buffer);
                return buffer.readUtf8();
            } catch (final IOException e) {
                throw new ApiException(e);
            }
        }

        // empty http request body
        return "";
    }

    public SignatureConfiguration buildSignatureConfigurationBySecret(String apiKey, String secretKey) {
        SignatureConfiguration signatureConfiguration =new SignatureConfiguration();
        signatureConfiguration.setSecretKey(secretKey);
        signatureConfiguration.setApiKey(apiKey);
        return signatureConfiguration;
    }

    public SignatureConfiguration buildSignatureGeneratorByPrivate(String apiKey, String secretKey){
        SignatureConfiguration signatureConfiguration =new SignatureConfiguration();
        signatureConfiguration.setPrivateKey(secretKey);
        signatureConfiguration.setApiKey(apiKey);
        return signatureConfiguration;
    }

    public static void main(String[] args) {
        String privateKey = "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VwBCIEIKK6U5nCd116/Ah5d1iAxbczCi4bsMQBkXm3leWU19Dq\n-----END PRIVATE KEY-----\n";
        String publicKey = "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VwAyEAL6UzHTsVie9TP7v535Olucv5jlwJV2kdeyE2JdSo8VQ=\n-----END PUBLIC KEY-----\n";
        SignatureConfiguration signatureConfiguration=new SignatureConfiguration();
        signatureConfiguration.setPrivateKey(privateKey);
        signatureConfiguration.setApiKey("aboo");
        SignatureGeneratorFactory signatureGeneratorFactory = new SignatureGeneratorFactory();
        SignatureGenerator signatureGenerator = signatureGeneratorFactory.getSignatureGenerator(signatureConfiguration);
        System.out.println(signatureGenerator);
    }
}
