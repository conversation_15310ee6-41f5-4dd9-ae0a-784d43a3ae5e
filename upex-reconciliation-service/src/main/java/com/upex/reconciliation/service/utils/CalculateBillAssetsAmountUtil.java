package com.upex.reconciliation.service.utils;

import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.reconciliation.facade.dto.results.ReconAccountAssetsInfoResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.model.dto.AbstractProperty;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName: CalculateBillAssetsAmountUtils
 * @date 2022/10/22 16:19
 */
public class CalculateBillAssetsAmountUtil {


    /**
     * 资金统计-计算不同类型下资产
     * @param accountTypeEnum 账户类型
     * @param property 按照币维度账单数据明细
     * @return
     */
    public static BigDecimal calculateBillAssetsAmountResult(AccountTypeEnum accountTypeEnum, AbstractProperty property) {
        switch (accountTypeEnum) {
            case USD_MIX_CONTRACT_BL:
            case USDT_MIX_CONTRACT_BL:
            case USDC_MIX_CONTRACT_BL:
            case FINANCIAL:
                return property.getProp1();
            case LEVER_FULL:
            case LEVER_ONE:
            case DEMO_LEVER_ONE:
            case DEMO_LEVER_FULL:
                return property.getLeverSum();
            default:
                // SPOT OTC SWAP_CHAIN
                return property.getPropSum();
        }
    }

    /**
     * 计算不同类型下资产
     * @param accountTypeEnum 账户类型
     * @param list 按照币维度账单数据明细
     * @return
     */
    public static BigDecimal calculateBillAssetsAmountResult(AccountTypeEnum accountTypeEnum, List<? extends AbstractProperty> list) {
        if (CollectionUtils.isEmpty(list)) {
            return BigDecimal.ZERO;
        }

        switch (accountTypeEnum) {
            case USD_MIX_CONTRACT_BL:
            case USDT_MIX_CONTRACT_BL:
            case USDC_MIX_CONTRACT_BL:
            case FINANCIAL:
                return list.stream().map(AbstractProperty::getProp1).reduce(BigDecimal.ZERO, BigDecimal::add);
            case LEVER_FULL:
            case LEVER_ONE:
                return list.stream().map(AbstractProperty::getLeverSum).reduce(BigDecimal.ZERO, BigDecimal::add);
            default:
                // SPOT OTC SWAP_CHAIN
                return list.stream().map(AbstractProperty::getPropSum).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }

    /**
     * 计算不同类型下资产
     * @param accountTypeEnum 账户类型
     * @param list 按照币维度账单数据明细
     * @return
     */
    public static BigDecimal calculateBillLoanCountAmountResult(AccountTypeEnum accountTypeEnum, List<? extends AbstractProperty> list) {
        if (CollectionUtils.isEmpty(list)) {
            return BigDecimal.ZERO;
        }

        switch (accountTypeEnum) {
            case LEVER_FULL:
            case LEVER_ONE:
                return list.stream().map(AbstractProperty::getProp3).reduce(BigDecimal.ZERO, BigDecimal::add);
            default:
                // SPOT OTC SWAP_CHAIN
                return BigDecimal.ZERO;
        }
    }

    /**
     * 计算不同类型下资产
     *
     * @param accountTypeEnum         账户类型
     * @param reconAccountAssetsInfoResult 按照币维度账单数据明细
     * @return
     */
    public static BigDecimal calculateBillAssetsAmountInfoResult(AccountTypeEnum accountTypeEnum, ReconAccountAssetsInfoResult reconAccountAssetsInfoResult) {
        if (Objects.isNull(reconAccountAssetsInfoResult)) {
            return BigDecimal.ZERO;
        }

        switch (accountTypeEnum) {
            case USD_MIX_CONTRACT_BL:
            case USDT_MIX_CONTRACT_BL:
            case USDC_MIX_CONTRACT_BL:
            case FINANCIAL:
                return reconAccountAssetsInfoResult.getProp1();
            case LEVER_FULL:
            case LEVER_ONE:
                return reconAccountAssetsInfoResult.getLeverSum();
            default:
                // SPOT OTC SWAP_CHAIN
                return reconAccountAssetsInfoResult.getPropSum();
        }
    }

    /**
     * 理财求和
     * @param list
     * @return
     */
    private static BigDecimal prop1Sum(List<ReconAccountAssetsInfoResult> list){
        BigDecimal result = BigDecimal.ZERO;
        for (ReconAccountAssetsInfoResult reconAccountAssetsInfoResult : list) {
            result = result.add(reconAccountAssetsInfoResult.getProp1());
        }
        return result;
    }

    /**
     * 杠杆求和
     * @param list
     * @return
     */
    private static BigDecimal leverSum(List<ReconAccountAssetsInfoResult> list){
        BigDecimal result = BigDecimal.ZERO;
        for (ReconAccountAssetsInfoResult reconAccountAssetsInfoResult : list) {
            result = result.add(reconAccountAssetsInfoResult.getLeverSum());
        }
        return result;
    }

    /**
     * 其它业务线求和
     * @param list
     * @return
     */
    private static BigDecimal propSum(List<ReconAccountAssetsInfoResult> list){
        BigDecimal result = BigDecimal.ZERO;
        for (ReconAccountAssetsInfoResult reconAccountAssetsInfoResult : list) {
            result = result.add(reconAccountAssetsInfoResult.getPropSum());
        }
        return result;
    }



    /**
     * 计算用户入出资产
     * @param accountTypeEnum
     * @param reconAccountAssetsInfoResult
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2022/12/6 20:53
     */
    public static BigDecimal calculateUserBillInOutAssetsResult(AccountTypeEnum accountTypeEnum, ReconAccountAssetsInfoResult reconAccountAssetsInfoResult){
        if(reconAccountAssetsInfoResult == null){
            return BigDecimal.ZERO;
        }
        AbstractProperty abstractProperty = BeanCopierUtil.copyProperties(reconAccountAssetsInfoResult, BillCoinTypeUserProperty.class);
        return calculateUserBillAssetsResult(accountTypeEnum,abstractProperty);
    }

    public static BigDecimal calculateUserBillAssetsResult(AccountTypeEnum accountTypeEnum, AbstractProperty property){
        BigDecimal balance = BigDecimal.ZERO;
        if (property != null) {
            switch (accountTypeEnum) {
                case FINANCIAL:
                    balance = property.getProp1();
                    break;
                case LEVER_FULL:
                case LEVER_ONE:
                    balance = property.getLeverSum();
                    break;
                case USDT_MIX_CONTRACT_BL:
                case USD_MIX_CONTRACT_BL:
                case S_USD_MIX_CONTRACT_BL:
                case S_USDT_MIX_CONTRACT_BL:
                case USDC_MIX_CONTRACT_BL:
                case S_USDC_MIX_CONTRACT_BL:
                    balance = property.getProp2().add(property.getProp3());
                    break;
                default:
                    balance = property.getPropSum();
                    break;
            }
        }
        return balance;
    }

    public static BigDecimal calculateUserBillInOutChangeAssetsResult(AccountTypeEnum accountTypeEnum, AbstractProperty property){
        BigDecimal balance = BigDecimal.ZERO;
        if (property != null) {
            switch (accountTypeEnum) {
                case LEVER_FULL:
                case LEVER_ONE:
                    balance = property.getChangeProp1().add(property.getChangeProp2());
                    break;
                case USDT_MIX_CONTRACT_BL:
                case USD_MIX_CONTRACT_BL:
                case S_USD_MIX_CONTRACT_BL:
                case S_USDT_MIX_CONTRACT_BL:
                case USDC_MIX_CONTRACT_BL:
                case S_USDC_MIX_CONTRACT_BL:
                    balance = property.getChangeProp2().add(property.getChangeProp3());
                    break;
                default:
                    balance = property.getChangePropSum();
                    break;
            }
        }
        return balance;
    }


//    /**
//     * 获取币种总的数量
//     * @param detailProperties 资产集合
//     * @return java.math.BigDecimal
//     * @Date 2022/4/25 11:27 AM
//     * <AUTHOR>
//     */
//    public static BigDecimal calculateTotalCount(List<StatisticsDetailProperty> detailProperties) {
//        BigDecimal totalCount = BigDecimal.ZERO;
//        if (CollectionUtils.isNotEmpty(detailProperties)){
//            totalCount = detailProperties.stream().map(StatisticsDetailProperty::getCoinCount).reduce(BigDecimal.ZERO, BigDecimal::add);
//        }
//        return totalCount;
//    }

//    /**
//     * 获取币种总的数量
//     * @param detailProperties 资产集合
//     * @return java.math.BigDecimal
//     * @Date 2022/4/25 11:27 AM
//     * <AUTHOR>
//     */
//    public static BigDecimal calculateOwnTotalCount(List<BillOwnCoinSubTypeProperty> detailProperties) {
//        BigDecimal totalCount = BigDecimal.ZERO;
//        if (CollectionUtils.isNotEmpty(detailProperties)){
//            totalCount = detailProperties.stream().map(BillOwnCoinSubTypeProperty::getOwnCount).reduce(BigDecimal.ZERO, BigDecimal::add);
//        }
//        return totalCount;
//    }


//    /**
//     * 获取币种总金额（折U）
//     * @param detailProperties 资产集合
//     * @return java.math.BigDecimal
//     * @Date 2022/4/25 11:27 AM
//     * <AUTHOR>
//     */
//    public static BigDecimal calculateTotalAmount(List<StatisticsDetailProperty> detailProperties) {
//        BigDecimal total = BigDecimal.ZERO;
//        if (CollectionUtils.isNotEmpty(detailProperties)) {
//            total = detailProperties.stream().map(detail -> detail.getCoinCount().multiply(detail.getURate()))
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//        }
//        return total;
//    }

    /**
     * 是否是负值资产
     * @param accountAssetsInfoResult
     * @param accountTypeEnum
     * @return
     */
    public static boolean isUserAssetsNegative(AccountAssetsInfoResult accountAssetsInfoResult, AccountTypeEnum accountTypeEnum){
        if(Objects.isNull(accountAssetsInfoResult)){
            return false;
        }
        switch (accountTypeEnum) {
            case SPOT:
                return (accountAssetsInfoResult.getProp2() != null && accountAssetsInfoResult.getProp2().compareTo(BigDecimal.ZERO) < 0)
                       || (accountAssetsInfoResult.getProp1() != null && accountAssetsInfoResult.getProp3() != null && accountAssetsInfoResult.getProp1().add(accountAssetsInfoResult.getProp3()).compareTo(BigDecimal.ZERO) < 0);
            case OTC:
            case WALLET:
            case LEVER_FULL:
            case LEVER_ONE:
                return (accountAssetsInfoResult.getProp1() != null && accountAssetsInfoResult.getProp1().compareTo(BigDecimal.ZERO) < 0)
                        || (accountAssetsInfoResult.getProp2() != null && accountAssetsInfoResult.getProp2().compareTo(BigDecimal.ZERO) < 0)
                        || (accountAssetsInfoResult.getProp3() != null && accountAssetsInfoResult.getProp3().compareTo(BigDecimal.ZERO) < 0);
            case USDT_MIX_CONTRACT_BL:
            case USD_MIX_CONTRACT_BL:
            case USDC_MIX_CONTRACT_BL:
            case FINANCIAL:
                return (accountAssetsInfoResult.getProp1() != null && accountAssetsInfoResult.getProp1().compareTo(BigDecimal.ZERO) < 0);
            default:
                break;
        }
        return false;
    }


    /**
     * 是否是负值资产
     * @param accountAssetsInfoResult
     * @param accountTypeEnum
     * @return
     */
    public static BigDecimal calculateUserBillAssetsResult(AccountTypeEnum accountTypeEnum, AccountAssetsInfoResult accountAssetsInfoResult){
        BigDecimal balance = BigDecimal.ZERO;
        if(Objects.isNull(accountAssetsInfoResult)){
            return balance;
        }
        switch (accountTypeEnum) {
            case SPOT:
            case LEVER_FULL:
            case LEVER_ONE:
                if(accountAssetsInfoResult.getProp1() != null) {
                    balance = balance.add(accountAssetsInfoResult.getProp1());
                }
                if(accountAssetsInfoResult.getProp2() != null) {
                    balance = balance.add(accountAssetsInfoResult.getProp2());
                }
                if(accountAssetsInfoResult.getProp3() != null) {
                    balance = balance.add(accountAssetsInfoResult.getProp3());
                }
                return balance;
            case OTC:
                if(accountAssetsInfoResult.getProp1() != null) {
                    balance = balance.add(accountAssetsInfoResult.getProp1());
                }
                if(accountAssetsInfoResult.getProp2() != null) {
                    balance = balance.add(accountAssetsInfoResult.getProp2());
                }
                return balance;
            case USDT_MIX_CONTRACT_BL:
            case USD_MIX_CONTRACT_BL:
            case USDC_MIX_CONTRACT_BL:
            case FINANCIAL:
                if(accountAssetsInfoResult.getProp1() != null) {
                    balance = balance.add(accountAssetsInfoResult.getProp1());
                }
                return balance;
            default:
                return balance;
        }
    }


    /**
     *
     * @param billCoinUserProperty
     * @param accountTypeEnum
     * @return
     */
    public static boolean isUserAssetsNegative(BillCoinUserProperty billCoinUserProperty, AccountTypeEnum accountTypeEnum){
        if(billCoinUserProperty == null){
            return false;
        }
        switch (accountTypeEnum) {
            case SPOT:
                return (billCoinUserProperty.getProp2() != null && billCoinUserProperty.getProp2().compareTo(BigDecimal.ZERO) < 0)
                       || (billCoinUserProperty.getProp1() != null && billCoinUserProperty.getProp3() != null && billCoinUserProperty.getProp1().add(billCoinUserProperty.getProp3()).compareTo(BigDecimal.ZERO) < 0);
            case OTC:
            case WALLET:
            case LEVER_FULL:
            case LEVER_ONE:
                return (billCoinUserProperty.getProp1() != null && billCoinUserProperty.getProp1().compareTo(BigDecimal.ZERO) < 0)
                        || (billCoinUserProperty.getProp2() != null && billCoinUserProperty.getProp2().compareTo(BigDecimal.ZERO) < 0)
                        || (billCoinUserProperty.getProp3() != null && billCoinUserProperty.getProp3().compareTo(BigDecimal.ZERO) < 0)
                        || (billCoinUserProperty.getProp4() != null && billCoinUserProperty.getProp4().compareTo(BigDecimal.ZERO) < 0)
                        || (billCoinUserProperty.getProp5() != null && billCoinUserProperty.getProp5().compareTo(BigDecimal.ZERO) < 0);
            case USDT_MIX_CONTRACT_BL:
            case USD_MIX_CONTRACT_BL:
            case USDC_MIX_CONTRACT_BL:
            case FINANCIAL:
                return (billCoinUserProperty.getProp1() != null && billCoinUserProperty.getProp1().compareTo(BigDecimal.ZERO) < 0);
            default:
                break;
        }
        return false;
    }


//    /**
//     * 获取币种总金额（折U）
//     * @param list 资产集合
//     * @return java.math.BigDecimal
//     * @Date 2023/4/22 19:57 PM
//     * <AUTHOR>
//     */
//    public static BigDecimal calculateAmount(List<? extends BillCapitalCoinTypeProperty> list) {
//        if (CollectionUtils.isEmpty(list)) {
//            return BigDecimal.ZERO;
//        }
//
//        return list.stream().map(BillCapitalCoinTypeProperty::getCapitalCount)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//    }
}
