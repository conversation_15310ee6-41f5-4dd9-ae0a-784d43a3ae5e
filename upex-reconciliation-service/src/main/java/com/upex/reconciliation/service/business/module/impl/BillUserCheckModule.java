package com.upex.reconciliation.service.business.module.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.upex.mixcontract.common.framework.AttributeMap;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.BillCheckService;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.module.AbstractBillModule;
import com.upex.reconciliation.service.common.RateLimiterManager;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.ReconciliationCommandEnum;
import com.upex.reconciliation.service.common.constants.enums.CommonBillChangeDataMsgType;
import com.upex.reconciliation.service.common.constants.enums.LogLevelEnum;
import com.upex.reconciliation.service.common.constants.enums.QueryUserAssetsSceneEnum;
import com.upex.reconciliation.service.common.constants.enums.TimeUnitEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillSymbolCoinUserProperty;
import com.upex.reconciliation.service.model.BillCmdResult;
import com.upex.reconciliation.service.model.BillCmdWrapper;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.ReconKafkaOpsConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.model.dto.UserAssetsDTO;
import com.upex.reconciliation.service.service.ReconSystemAccountService;
import com.upex.reconciliation.service.utils.*;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.utils.log.AlarmUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.data.redis.core.RedisTemplate;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.*;
import static com.upex.reconciliation.service.utils.MetricsUtil.HISTOGRAM_USER_TAKE_COMMAND;

/**
 * 账单一阶段处理类，包含命令队列的存放
 * 主要处理用户维度的对账
 */
@Slf4j
public class BillUserCheckModule extends AbstractBillModule {
    /***命令处理队列***/
    private BlockingQueue<BillCmdWrapper> cmdQueue = new ArrayBlockingQueue<>(3000);
    /***个人维度聚合流水map<userId,map<coinId,BillCoinUserProperty>>***/
    private Cache<Long, Map<Integer, BillCoinUserProperty>> userPropertyMap = Caffeine.newBuilder()
            .expireAfterAccess(Duration.ofDays(2))
            .build();
    /***等待加载用户资产用户队列***/
    private Map<Long, List<CommonBillChangeData>> waitLoadUserAssetsMap = new ConcurrentHashMap<>();
//    private Map<Long, Map<Integer, BillCoinUserProperty>> userPropertyMap = new ConcurrentHashMap<>();
    /***userId,<coinId,List<CommonBillChangeData>>对账失败的暂存map，每次新消息处理之前，需要先check一遍过往的对账失败的内容***/
    private Map<Long, Map<String, List<CommonBillChangeData>>> errorBillMap = new ConcurrentHashMap<>();
    /***消息幂等***/
    private LoadingCache<String, Boolean> idempotentLoadingCache;
    private Cache<String, Date> orderNoBizTimeCache;
    private Cache<String, Date> orderNoBizTimeRedisCache;
    private AtomicLong errorBillMapSize = new AtomicLong(0L);
    private AtomicLong waitLoadUserAssetsMapSize = new AtomicLong(0L);
    private BillTimeSliceCheckModule billTimeSliceCheckModule;
    /**
     * 流水实时监控处理队列
     */
    private FlowMonitorDataProcessModule flowMonitorDataProcessModule;
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    private RateLimiterManager rateLimiterManager;
    private AlarmNotifyService alarmNotifyService;
    private List<String> consumerLogList = new ArrayList<>();
    private AtomicLong messageOffset = new AtomicLong(0L);
    private CommonService commonService;
    private PropertyInitModule propertyInitModule;
    private ReconSystemAccountService reconSystemAccountService;
    private RedisTemplate<String, Object> redisTemplate;

    public BillUserCheckModule(BillLogicGroup logicGroup) {
        super(logicGroup);
    }

    @Override
    public void init(AttributeMap initContext) {
        billTimeSliceCheckModule = getModule(BillTimeSliceCheckModule.class);
        flowMonitorDataProcessModule = getModule(FlowMonitorDataProcessModule.class);
        propertyInitModule = getModule(PropertyInitModule.class);
        accountTypeEnum = initContext.get(BillLogicGroup.ACCOUNT_TYPE_ENUM_KEY);
        ReconciliationSpringContext reconciliationSpringContext = initContext.get(BillLogicGroup.ENGINE_SPRING_CONTEXT_KEY);
        alarmNotifyService = reconciliationSpringContext.getAlarmNotifyService();
        accountAssetsServiceFactory = reconciliationSpringContext.getAccountAssetsServiceFactory();
        rateLimiterManager = reconciliationSpringContext.getRateLimiterManager();
        commonService = reconciliationSpringContext.getCommonService();
        accountTypeEnum = initContext.get(BillLogicGroup.ACCOUNT_TYPE_ENUM_KEY);
        this.reconSystemAccountService = reconciliationSpringContext.getReconSystemAccountService();
        this.redisTemplate = reconciliationSpringContext.getRedisTemplate();
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        ReconKafkaOpsConfig reconKafkaOpsConfig = apolloBizConfig.getReconKafkaOpsConfig();
        // 有该用户的任意一个coin触发初始化后，就要开始加载幂等表了，防止某个coin的消息慢，先调大这个缓存时间
        Long localCacheExpireMinutes = (long) (reconKafkaOpsConfig.getMqProducerSpeed() * BillConstants.IDEMPOTENT_CACHE_EXPIRE_MINUTES / reconKafkaOpsConfig.getMqConsumerRateLimit());
        // local cache map初始化
        idempotentLoadingCache = Caffeine.newBuilder()
                .expireAfterWrite(localCacheExpireMinutes, TimeUnit.MINUTES)
                .build(new CacheLoader<String, Boolean>() {
                    @Override
                    public @Nullable Boolean load(@NonNull String key) throws Exception {
                        return false;
                    }
                });
        // orderNo BizTime 修复
        Long intervalMs = TimeUnitEnum.toDuration(apolloBizConfig.getOrderNoBizTimeCacheExpireTime()).toMillis();
        orderNoBizTimeCache = Caffeine.newBuilder().expireAfterWrite(intervalMs, TimeUnit.MILLISECONDS).build();
        orderNoBizTimeRedisCache = Caffeine.newBuilder().expireAfterWrite(intervalMs * 10, TimeUnit.MILLISECONDS).build();
    }

    @Override
    public void offerCommand(BillCmdWrapper cmdWrapper) {
        try {
            this.cmdQueue.put(cmdWrapper);
        } catch (InterruptedException e) {
            String errorMsg = "BillUserCheckModule Command queue is full : " + cmdWrapper.getCommandEnum().getName()
                    + " queue size: " + this.cmdQueue.size() + " logicGroup: " + logicGroup.getName();
            AlarmUtils.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }
    }

    @Override
    public BillCmdResult takeCommand() {
        BillCmdWrapper cmdWrapper = null;
        try {
            cmdWrapper = this.cmdQueue.poll(1, TimeUnit.SECONDS);
            if (cmdWrapper == null) {
                return null;
            }
            BillCmdWrapper finalCmdWrapper = cmdWrapper;
            return MetricsUtil.histogram(HISTOGRAM_USER_TAKE_COMMAND + accountTypeEnum.getCode(), () -> execCommand(finalCmdWrapper));
        } catch (Exception e) {
            log.error("BillTimeSliceCheckModule check failed with error accountType {} data {} {}", accountTypeEnum.getCode(), cmdWrapper == null ? "" : JSON.toJSONString(cmdWrapper), e);
            alarmNotifyService.alarm(accountTypeEnum.getCode(), USER_CHECK_MODULE_TAKE_COMMAND_ERROR, accountTypeEnum.getCode());
            redisTemplate.opsForValue().set(RedisUtil.getSaveTimeSliceStatusKey(accountTypeEnum.getCode()), Boolean.FALSE.toString());
        }
        return null;
    }

    private BillCmdResult execCommand(BillCmdWrapper cmdWrapper) {
        if (ReconciliationCommandEnum.BILL_CHANGE.equals(cmdWrapper.getCommandEnum())) {
            CommonBillChangeData currentBill = (CommonBillChangeData) cmdWrapper.getCommandData();
            if (currentBill.getMsgType().getCode().equals(CommonBillChangeDataMsgType.UPDATE_PARTITION.getCode())) {
                offerCommonBillToTimeSlice(currentBill);
                return null;
            }
            ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(currentBill.getAccountType());
            Map<Integer, BillCoinUserProperty> userPropertyMap = getUserPropertyMap().getIfPresent(currentBill.getAccountId());
            if (userPropertyMap != null) {
                processUserBillCheck(currentBill);
            } else {
                currentBill.setLastCheckOkTime(billTimeSliceCheckModule.getLastBillTimeSliceDTO().getBillConfig().getCheckOkTime());
                // 同步加载用户
                if (apolloBizConfig.getReconKafkaOpsConfig().getSyncPropertyInit()) {
                    List<BillCoinUserProperty> billCoinUserPropertyList = accountAssetsServiceFactory.getBillCoinUserPropertyList(currentBill.getAccountType(), currentBill.getAccountId(), currentBill.getLastCheckOkTime().getTime(), QueryUserAssetsSceneEnum.RELOAD);
                    if (CollectionUtils.isNotEmpty(billCoinUserPropertyList)) {
                        loadUserCoinProperty(currentBill.getAccountId(), billCoinUserPropertyList);
                    }
                    processUserBillCheck(currentBill);
                } else {
                    // 异步加载用户
                    List<CommonBillChangeData> changeDataList = waitLoadUserAssetsMap.computeIfAbsent(currentBill.getAccountId(), k -> Collections.synchronizedList(new ArrayList<>()));
                    rateLimiterManager.tryRateLimitLoadUserAssetsSize(currentBill.getAccountType(), waitLoadUserAssetsMap.size(), waitLoadUserAssetsMapSize.get(), this.cmdQueue.size());
                    if (CollectionUtils.isEmpty(changeDataList)) {
                        BillCmdWrapper initAssetCmd = new BillCmdWrapper(ReconciliationCommandEnum.INIT_ASSET, currentBill);
                        propertyInitModule.offerCommand(initAssetCmd);
                    }
                    changeDataList.add(currentBill);
                    waitLoadUserAssetsMapSize.incrementAndGet();
                }
            }
        } else if (ReconciliationCommandEnum.INIT_ASSET_COMPLETE.equals(cmdWrapper.getCommandEnum())) {
            CommonBillChangeData currentBill = (CommonBillChangeData) cmdWrapper.getFromCommandData();
            List<BillCoinUserProperty> billCoinUserPropertyList = (List<BillCoinUserProperty>) cmdWrapper.getCommandData();
            log.info("BillUserCheckModule.execCommand INIT_ASSET_COMPLETE accountType:{} accountId:{} messageId:{} queueSize:{} waitRecordSize:{}", accountTypeEnum.getCode(), currentBill.getAccountId(), currentBill.getBizId(), this.cmdQueue.size(), waitLoadUserAssetsMapSize.get());
            if (CollectionUtils.isNotEmpty(billCoinUserPropertyList)) {
                loadUserCoinProperty(currentBill.getAccountId(), billCoinUserPropertyList);
            }
            List<CommonBillChangeData> billChangeDataList = waitLoadUserAssetsMap.remove(currentBill.getAccountId());
            for (CommonBillChangeData changeData : billChangeDataList) {
                waitLoadUserAssetsMapSize.decrementAndGet();
                processUserBillCheck(changeData);
            }
        } else {
            log.error("BillUserCheckModule execCommand unknown command {}", cmdWrapper.getCommandEnum().getName());
        }
        return null;
    }

    /**
     * 处理用户维度对照
     *
     * @param currentBill
     */
    private void processUserBillCheck(CommonBillChangeData currentBill) {
        Long accountId = currentBill.getAccountId();
        // 更新partition消息单独处理
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(currentBill.getAccountType());
        printMessageEntryLog(currentBill);

        // STEP 1 : 获取内存中的用户对象
        BillCoinUserProperty billCoinUserProperty = getBillCoinUserProperty(currentBill);

        // SETP 2: 幂等校验
        // 幂等放在开始的位置，结合 isInitialized 字段判断
        String dataUniqueId = BillBizUtil.getDataUniqueId(accountTypeEnum.getCode(), currentBill);
        boolean alreadyExistUniqueId = alreadyHasUniqueId(dataUniqueId);
        if (alreadyExistUniqueId) {
            BizLogUtils.log(LogLevelEnum.VITAL_DEBUG, apolloBizConfig, "BillUserCheckModule unique check fail dataUniqueId:{} ", dataUniqueId);
            return;
        }
        String uniqueId = BillBizUtil.getUniqueId(currentBill.getOffset(), currentBill.getBizId());
        String accountUniqueId = BillBizUtil.getAccountUniqueId(accountId, uniqueId);

        // 统一订单号 数据跨周期问题修复
        repairOrderNoBizTime(apolloBizConfig, currentBill);

        // SETP5: 获取错误数据 : 错误队列处理逻辑：先获取一下该用户是否有错误队列，如果有，则需要取出一起对账
        //  错误队列这块，按照原始的消息是有序得来处理
        BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(currentBill.getAccountType());
        List<CommonBillChangeData> addErrorListToBillResult = addErrorListToBill(currentBill);
        List<CommonBillChangeData> checkFileList = new ArrayList<>();
        synchronized (addErrorListToBillResult) {
            Iterator<CommonBillChangeData> iterator = addErrorListToBillResult.iterator();
            while (iterator.hasNext()) {
                errorBillMapSize.decrementAndGet();
                CommonBillChangeData eachBill = iterator.next();
                iterator.remove();
                //  情况一  bizTime < check_time && id_date < check_time  丢弃
                //  情况二  bizTime > check_time || id_date > check_time 继续判断流水期末-变动 = init期初 是设置lastBizId继续对账 否 放入缓存队列
                if (billCoinUserProperty.getLastBizId(eachBill.getAccountType(), eachBill.getSymbolId()) == null) {
                    if (eachBill.getUserCheckBizTime().compareTo(billCoinUserProperty.getCheckTime(eachBill.getAccountType(), eachBill.getSymbolId())) < 0) {
                        BizLogUtils.log(LogLevelEnum.MAIN_PROCESS, apolloBizConfig, "BllUserCheckModule check abandon accountUniqueId:{} Property:{} data is {}", accountUniqueId, JSON.toJSONString(billCoinUserProperty), JSONObject.toJSONString(eachBill));
                        alarmNotifyService.alarm(USER_CHECK_MODULE_ABANDON_MESSAGE_ERROR, accountTypeEnum.getCode(), accountUniqueId, DateUtil.date2str(eachBill.getBizTime()));
                        continue;
                    } else if (!billCheckService.checkBillPropertyMatch(eachBill, billCoinUserProperty)) {
                        // 是否忽略资产校验 true=跳过 进入对账 false 消息丢弃Ï
                        boolean result = billCheckService.ignoreBillPropertyMatch(eachBill, billCoinUserProperty);
                        BizLogUtils.log(LogLevelEnum.VITAL_DEBUG, apolloBizConfig, "BllUserCheckModule check abandon checkBillPropertyMatch accountUniqueId:{} result:{} Property:{} data is {}", accountUniqueId, result, JSON.toJSONString(billCoinUserProperty), JSONObject.toJSONString(eachBill));
                        // 兼容uta 并发乱序 卡第一条资产lastBillId 如果lastBizId=-1 则全部消息都需要
                        Long lastBizId = billCoinUserProperty.getLastBizId();
                        if (result) {
                            checkFileList.add(eachBill);
                            break;
                        } else if (lastBizId == -1 || lastBizId == 0) {
                            checkFileList.add(eachBill);
                            break;
                        } else {
                            continue;
                        }
                    } else {
                        BizLogUtils.log(LogLevelEnum.VITAL_DEBUG, apolloBizConfig, "BllUserCheckModule check setLastBizId accountUniqueId:{} Property:{} data is {}", accountUniqueId, JSON.toJSONString(billCoinUserProperty), JSONObject.toJSONString(eachBill));
                        billCoinUserProperty.setLastBizId(eachBill.getAccountType(), eachBill.getBizId(), eachBill.getSymbolId());
                    }
                } else if (eachBill.getBizId() < billCoinUserProperty.getLastBizId(eachBill.getAccountType(), eachBill.getSymbolId())) {
                    BizLogUtils.log(LogLevelEnum.VITAL_DEBUG, apolloBizConfig, "BllUserCheckModule check abandon getLastBizId accountUniqueId:{} Property:{} data is {}", accountUniqueId, JSON.toJSONString(billCoinUserProperty), JSONObject.toJSONString(eachBill));
                    if (accountTypeEnum.isUserCoinPropSubType()) {
                        eachBill.setBillCoinUserProperty(billCoinUserProperty.clone());
                    }
                    offerCommonBillToTimeSlice(eachBill);
                    continue;
                }

                List<CommonBillChangeData> eachList = new ArrayList<>();
                eachList.add(eachBill);
                boolean result = billCheckService.checkUserFlow(apolloBizConfig, currentBill.getAccountType(), accountUniqueId, currentBill.getAccountId(), currentBill.getCoinId(), eachList, billCoinUserProperty, addErrorListToBillResult);
                BizLogUtils.log(LogLevelEnum.DEBUG, apolloBizConfig, "BllUserCheckModule check checkUserFlow accountType {} result:{} accountUniqueId:{}", currentBill.getAccountType(), result, accountUniqueId);
                if (result) {
                    if (eachList.size() > 1) {
                        while (iterator.hasNext()) {
                            iterator.next();
                            iterator.remove();
                            errorBillMapSize.decrementAndGet();
                        }
                    }
                    for (CommonBillChangeData commonBillChangeData : eachList) {
                        // 1 更新资产对象
                        updateCacheProperty(commonBillChangeData);
                        // 2 推送给时间片
                        // 记录资产快照 目前是杠杆逐仓使用
                        if (accountTypeEnum.isUserCoinPropSubType()) {
                            commonBillChangeData.setBillCoinUserProperty(billCoinUserProperty.clone());
                        }
                        offerCommonBillToTimeSlice(commonBillChangeData);
                        offerCommonBillToMonitorCheck(commonBillChangeData);
                    }
                } else {
                    checkFileList.add(eachBill);
                    break;
                }
            }
        }
        // 放入失败队列
        if (CollectionUtils.isNotEmpty(checkFileList)) {
            for (CommonBillChangeData commonBillChangeData : checkFileList) {
                addErrorListToBill(commonBillChangeData);
            }
        }
    }

    private void offerCommonBillToTimeSlice(CommonBillChangeData eachBill) {
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(eachBill.getAccountType());
        BillCmdWrapper billCmdWrapper = new BillCmdWrapper(ReconciliationCommandEnum.BILL_CHANGE, eachBill);
        billTimeSliceCheckModule.offerCommand(billCmdWrapper);
        //处理合约换汇数据
        BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(eachBill.getAccountType());
        if (accountTypeEnum.cloneMessageToExchangeData() && billCheckService.getExchangeDataChangeAssets(eachBill).compareTo(BigDecimal.ZERO) != 0) {
            Long exchangeUserId = reconSystemAccountService.queryExchangeUserId(accountTypeEnum);
            CommonBillChangeData exchangeData = eachBill.buildMixContractExchangeData(billCheckService, exchangeUserId);
            BillCoinUserProperty exchangeBillCoinUserProperty = getBillCoinUserProperty(exchangeData);
            billCheckService.repairBillFlowByCoinUserProperty(exchangeBillCoinUserProperty, Lists.newArrayList(exchangeData));
            BillCmdWrapper exchangeBillCmdWrapper = new BillCmdWrapper(ReconciliationCommandEnum.BILL_CHANGE, exchangeData);
            billTimeSliceCheckModule.offerCommand(exchangeBillCmdWrapper);
        }
    }

    private void offerCommonBillToMonitorCheck(CommonBillChangeData eachBill) {
        BillCmdWrapper billCmdWrapper = new BillCmdWrapper(ReconciliationCommandEnum.BILL_CHANGE, eachBill);
        flowMonitorDataProcessModule.offerCommand(billCmdWrapper);
    }

    /**
     * 错误队列的处理，仅聚合流水，不聚合期初值
     *
     * @param currentBillChangeData
     */
    public List<CommonBillChangeData> addErrorListToBill(CommonBillChangeData currentBillChangeData) {
        errorBillMapSize.incrementAndGet();
        List<CommonBillChangeData> userErrorBillList = errorBillMap.computeIfAbsent(currentBillChangeData.getAccountId(), key -> new ConcurrentHashMap<>())
                .computeIfAbsent(currentBillChangeData.errorMapGroupByKey(), key -> Collections.synchronizedList(new ArrayList<>()));
        userErrorBillList.add(currentBillChangeData);
        userErrorBillList.sort(new Comparator<CommonBillChangeData>() {
            @Override
            public int compare(CommonBillChangeData o1, CommonBillChangeData o2) {
                return o1.getBizId().compareTo(o2.getBizId());
            }
        });
        return userErrorBillList;
    }

    private void updateCacheProperty(CommonBillChangeData commonBillChangeData) {
        // 已经时排序后的结果，直接累加覆盖即可
        BillCoinUserProperty billCoinUserProperty = getUserPropertyMap().getIfPresent(commonBillChangeData.getAccountId()).get(commonBillChangeData.getCoinId());
        billCoinUserProperty.setCheckTime(commonBillChangeData.getAccountType(), commonBillChangeData.getUserCheckBizTime(), commonBillChangeData.getSymbolId());
        billCoinUserProperty.setLastBizId(commonBillChangeData.getAccountType(), commonBillChangeData.getBizId(), commonBillChangeData.getSymbolId());
        billCoinUserProperty.updateCacheProperty(commonBillChangeData);
        billCoinUserProperty.setUpdateTime(commonBillChangeData.getUserCheckBizTime());
    }

    public boolean alreadyHasUniqueId(String uniqueId) {
        Boolean result = idempotentLoadingCache.getIfPresent(uniqueId);
        if (result == null) {
            idempotentLoadingCache.put(uniqueId, true);
        }
        return result != null;
    }


    private BillCoinUserProperty getBillCoinUserProperty(CommonBillChangeData currentBill) {
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        Map<Integer, BillCoinUserProperty> userCoinPropertyMap = userPropertyMap.get(currentBill.getAccountId(), key -> new ConcurrentHashMap<>());
        long initCheckOkTime = billTimeSliceCheckModule.getLastBillTimeSliceDTO().getBillConfig().getCheckOkTime().getTime();
        BillCoinUserProperty billCoinUserProperty = userCoinPropertyMap.computeIfAbsent(currentBill.getCoinId(), key -> {
            //TODO 10重新初始化后 改成get first
            Long initTime = userCoinPropertyMap.values().stream().map(i -> i.getInitialTime().getTime()).min(Comparator.comparing(x -> x)).orElse(null);
            BillCoinUserProperty coinUserProperty = new BillCoinUserProperty();
            coinUserProperty.setUserId(currentBill.getAccountId());
            coinUserProperty.setCoinId(currentBill.getCoinId());
            coinUserProperty.setCheckTime(new Date(initCheckOkTime));
            coinUserProperty.setInitialTime(new Date(initTime != null ? initTime : initCheckOkTime));
            coinUserProperty.setFristCreateTime(currentBill.getBizTime());
            coinUserProperty.setLastBizId(-1L);
            if (apolloBizConfig.isLoadCoinUserNoExistDefaultBillAssets()) {
                BizLogUtils.log(LogLevelEnum.KEY_RESULT, apolloBizConfig, "BillUserCheckModule.getBillCoinUserProperty loadCoinUserNoExistDefaultBillAssets accountType:{} accountId:{} messageId:{}", accountTypeEnum.getCode(), currentBill.getAccountId(), currentBill.getBizId());
                coinUserProperty.setProp1(currentBill.getProp1().subtract(currentBill.getChangeProp1()));
                coinUserProperty.setProp2(currentBill.getProp2().subtract(currentBill.getChangeProp2()));
                coinUserProperty.setProp3(currentBill.getProp3().subtract(currentBill.getChangeProp3()));
                coinUserProperty.setProp4(currentBill.getProp4().subtract(currentBill.getChangeProp4()));
                coinUserProperty.setProp5(currentBill.getProp5().subtract(currentBill.getChangeProp5()));
            }
            return coinUserProperty;
        });
        if (accountTypeEnum.isUserCoinPropSubType()) {
            billCoinUserProperty.getSymbolProperty().computeIfAbsent(currentBill.getSymbolId(), key1 -> {
                Long initTime = userCoinPropertyMap.values().stream().map(i -> i.getInitialTime().getTime()).min(Comparator.comparing(x -> x)).orElse(null);
                BillSymbolCoinUserProperty billSymbolCoinUserProperty = new BillSymbolCoinUserProperty();
                billSymbolCoinUserProperty.setUserId(currentBill.getAccountId());
                billSymbolCoinUserProperty.setCoinId(currentBill.getCoinId());
                billSymbolCoinUserProperty.setSymbolId(currentBill.getSymbolId());
                billSymbolCoinUserProperty.setCheckTime(new Date(initCheckOkTime));
                billSymbolCoinUserProperty.setInitialTime(new Date(initTime != null ? initTime : initCheckOkTime));
                billSymbolCoinUserProperty.setLastBizId(-1L);
                return billSymbolCoinUserProperty;
            });
        }
        userPropertyMap.put(currentBill.getAccountId(), userCoinPropertyMap);
        return billCoinUserProperty;
    }

    public Cache<Long, Map<Integer, BillCoinUserProperty>> getUserPropertyMap() {
        return userPropertyMap;
    }

    public Map<Integer, BillCoinUserProperty> getUserCoinPropertyMap(Long userId) {
        return userPropertyMap.getIfPresent(userId);
    }

    public Map<Long, Map<String, List<CommonBillChangeData>>> getErrorBillMap() {
        return errorBillMap;
    }

    public Long getErrorBillMapSize() {
        return errorBillMapSize.get();
    }

    public Long getWaitLoadUserAssetsMapSize() {
        return waitLoadUserAssetsMapSize.get();
    }

    public Long getMinErrorMapBizTime() {
        CommonBillChangeData minErrorMapData = getMinErrorMapData();
        return minErrorMapData != null ? minErrorMapData.getBizTime().getTime() : null;
    }

    public CommonBillChangeData getMinErrorMapData() {
        CommonBillChangeData minBizData = null;
        Long minBizTime = null;
        for (Map.Entry<Long, Map<String, List<CommonBillChangeData>>> userEntryMap : errorBillMap.entrySet()) {
            for (Map.Entry<String, List<CommonBillChangeData>> coinEntryMap : userEntryMap.getValue().entrySet()) {
                List<CommonBillChangeData> billChangeDataList = coinEntryMap.getValue();
                if (CollectionUtils.isNotEmpty(billChangeDataList)) {
                    synchronized (billChangeDataList) {
                        Iterator<CommonBillChangeData> changeDataIterator = billChangeDataList.iterator();
                        if (changeDataIterator.hasNext()) {
                            CommonBillChangeData commonBillChangeData = changeDataIterator.next();
                            if (Objects.nonNull(commonBillChangeData) && Objects.nonNull(commonBillChangeData.getBizTime())) {
                                Long bizTime = commonBillChangeData.getBizTime().getTime();
                                if (minBizTime == null || minBizTime > bizTime) {
                                    minBizTime = bizTime;
                                    minBizData = commonBillChangeData;
                                }
                            }
                        }
                    }
                }
            }
        }
        return minBizData;
    }

    /**
     * 获取待加载用户资产的最小bizTime
     *
     * @return
     */
    public Long getMinWaitLoadUserAssetsMapBizTime() {
        Long minBizTime = null;
        for (Map.Entry<Long, List<CommonBillChangeData>> entry : waitLoadUserAssetsMap.entrySet()) {
            List<CommonBillChangeData> changeDataList = entry.getValue();
            if (CollectionUtils.isNotEmpty(changeDataList)) {
                Iterator<CommonBillChangeData> changeDataIterator = changeDataList.iterator();
                while (changeDataIterator.hasNext()) {
                    CommonBillChangeData commonBillChangeData = changeDataIterator.next();
                    Long bizTime = commonBillChangeData.getBizTime().getTime();
                    minBizTime = minBizTime != null ? Math.min(minBizTime, bizTime) : bizTime;
                }
            }
        }
        return minBizTime;
    }

    /**
     * 获取对账最小时间
     *
     * @return
     */
    public Long getMinTimeSliceBillCheckTime() {
        Long minErrorMapBizTime = getMinErrorMapBizTime();
        Long minWaitLoadUserAssetsMapBizTime = getMinWaitLoadUserAssetsMapBizTime();
        if (minErrorMapBizTime != null && minWaitLoadUserAssetsMapBizTime != null) {
            return Math.min(minErrorMapBizTime, minWaitLoadUserAssetsMapBizTime);
        } else if (minErrorMapBizTime != null) {
            return minErrorMapBizTime;
        } else {
            return minWaitLoadUserAssetsMapBizTime;
        }
    }

    /**
     * 加载资产 根据lastBizId做幂等
     *
     * @param coinUserProperty
     */
    public void idempotentForCoinUserProperty(BillCoinUserProperty coinUserProperty, Long initTime) {
        if (coinUserProperty.getCheckTime().getTime() != initTime) {
            List<String> dataUniqueIds = BillBizUtil.getDataUniqueId(accountTypeEnum.getCode(), coinUserProperty);
            dataUniqueIds.forEach(id -> {
                idempotentLoadingCache.put(id, true);
            });
        }
    }

    public LoadingCache<String, Boolean> getIdempotentLoadingCache() {
        return idempotentLoadingCache;
    }

    /**
     * 获取coin维护用户资产数量
     *
     * @return
     */
    public Integer getUserPropertyMapCoinSize() {
        int userCoinPropertyMapSize = 0;
        for (Map<Integer, BillCoinUserProperty> coinMap : getUserPropertyMap().asMap().values()) {
            userCoinPropertyMapSize += coinMap.size();
        }
        return userCoinPropertyMapSize;
    }

    /**
     * 清除非活跃用户
     *
     * @param apolloBizConfig
     */
    @Deprecated
    public void cleanUpInactiveUsers(ApolloReconciliationBizConfig apolloBizConfig) {
        Date nowDate = new Date();
        Long coinUserPropertyCleanTime = apolloBizConfig.getCoinUserPropertyCleanTimeMinute() * 60 * 1000;
        Map<Long, Map<Integer, BillCoinUserProperty>> userPropertyMap = getUserPropertyMap().asMap();
        List<Long> removeUserIds = new ArrayList<>();
        for (Map.Entry<Long, Map<Integer, BillCoinUserProperty>> entry : userPropertyMap.entrySet()) {
            Date date = entry.getValue().values().stream().map(BillCoinUserProperty::getUpdateTime).max(Comparator.naturalOrder()).get();
            if (nowDate.getTime() - date.getTime() > coinUserPropertyCleanTime) {
                removeUserIds.add(entry.getKey());
            }
        }
        try {
            removeUserIds.forEach(id -> {
                userPropertyMap.remove(id);
            });
        } catch (Exception e) {
            log.info("cleanUpInactiveUsers accountType:{}", apolloBizConfig.getAccountType(), e);
        }
        log.info("cleanUpInactiveUsers accountType:{} size:{}", apolloBizConfig.getAccountType(), removeUserIds.size());
    }

    /**
     * 打印入口消息日志
     *
     * @param currentBill
     */
    public void printMessageEntryLog(CommonBillChangeData currentBill) {
        Date bizTime = currentBill.getBizTime();
        Long kafkaTimestamp = currentBill.getKafkaTimestamp();
        Long consumerTimestamp = currentBill.getConsumerTimestamp();
        Long kafkaDelayTime = kafkaTimestamp - bizTime.getTime();
        Long consumerDelayTime = consumerTimestamp - kafkaDelayTime;
        Object[] arg = new Object[]{getDelayTimeLevel(kafkaDelayTime), getDelayTimeLevel(consumerDelayTime), currentBill.getAccountId(), currentBill.getBizId(), DateUtil.date2str(currentBill.getBizTimeFromId()), DateUtil.date2str(new Date(currentBill.getKafkaTimestamp())), DateUtil.date2str(new Date(currentBill.getConsumerTimestamp())), currentBill.getPartition(), currentBill.getOffset(), messageOffset.incrementAndGet()};
        consumerLogList.add(MessageFormatter.arrayFormat("kafkaDelayLevel:{} consumerDelayLevel:{} userId:{} id:{} bizTime:{} kafkaTime:{} consumerTime:{} partition:{} offset:{} messageOffset:{}", arg).getMessage());
        if (consumerLogList.size() >= 5) {
            log.info("BllUserCheckModule.execCommand entry log accountType:{} data:{}", currentBill.getAccountType(), JSON.toJSONString(consumerLogList));
            consumerLogList.clear();
        }
    }

    /**
     * 获取延迟等级，方便日志搜索
     *
     * @param delayTime
     * @return
     */
    private Integer getDelayTimeLevel(Long delayTime) {
        if (delayTime < 30000) {
            return 0;
        } else if (delayTime < 60000) {
            return 1;
        } else if (delayTime < 120000) {
            return 2;
        } else if (delayTime < 180000) {
            return 3;
        } else if (delayTime < 240000) {
            return 4;
        } else if (delayTime < 300000) {
            return 5;
        }
        return 999;
    }

    /**
     * 加载用户数据
     */
    public void loadUserCoinProperty(Long userId, List<BillCoinUserProperty> billCoinUserPropertyList) {
        Map<Integer, BillCoinUserProperty> userCoinPropertyMap = userPropertyMap.get(userId, key -> new ConcurrentHashMap<>());
        if (MapUtils.isEmpty(userCoinPropertyMap)) {
            if (CollectionUtils.isNotEmpty(billCoinUserPropertyList)) {
                Long initTime = billCoinUserPropertyList.stream().map(i -> i.getInitialTime().getTime()).min(Comparator.comparing(x -> x)).orElse(null);
                for (BillCoinUserProperty coinUserProperty : billCoinUserPropertyList) {
                    userCoinPropertyMap.put(coinUserProperty.getCoinId(), coinUserProperty);
                    // 如果是非初始化资产 把lastbizid存入幂等 防止重置kafka位点后重复判断
                    idempotentForCoinUserProperty(coinUserProperty, initTime);
                }
            }
        }
    }

    /**
     * 获取内存用户usdt资产
     *
     * @param userId
     * @param checkTime
     * @return
     */
    public UserAssetsDTO getMemUserAssetsToUsdt(Long userId, Date checkTime, Map<Integer, BigDecimal> unRealizedMap, ApolloReconciliationBizConfig apolloBizConfig) {
        // 总资产
        BigDecimal totalUsdtValue = BigDecimal.ZERO;
        BigDecimal assetsUsdtValue = BigDecimal.ZERO;
        BigDecimal unRealizedUsdtValue = BigDecimal.ZERO;
        // 总负值
        BigDecimal negativeTotalUsdtValue = BigDecimal.ZERO;
        BigDecimal negativeAssetsUsdtValue = BigDecimal.ZERO;
        BigDecimal negativeUnRealizedUsdtValue = BigDecimal.ZERO;
        // 币种明细map
        Map<Integer, Tuple3<BigDecimal, BigDecimal, BigDecimal>> totalDetailMap = new HashMap<>();
        Map<Integer, Tuple3<BigDecimal, BigDecimal, BigDecimal>> assetsDetailMap = new HashMap<>();
        Map<Integer, Tuple3<BigDecimal, BigDecimal, BigDecimal>> unRealizedDetailMap = new HashMap<>();

        // 资产折U计算
        Map<Integer, BillCoinUserProperty> userCoinPropertyMap = getUserCoinPropertyMap(userId);
        if (MapUtil.isNotEmpty(userCoinPropertyMap)) {
            // 时间片时间汇率
            Map<Integer, PriceVo> priceVos = commonService.getNewCoinIdRatesMapCache(checkTime.getTime(), accountTypeEnum);
            BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountTypeEnum.getCode());
            // 计算折U值（按币种遍历）
            Date currentDateTime = new Date();
            for (Map.Entry<Integer, BillCoinUserProperty> entry : userCoinPropertyMap.entrySet()) {
                Integer coinId = entry.getKey();
                BillCoinUserProperty billCoinUserProperty = entry.getValue();
                PriceVo priceVo = priceVos.get(coinId);
                if (priceVo == null) {
                    long mockTime = DateUtil.addSecond(currentDateTime, -apolloBizConfig.getPriceTimeDiff()).getTime();
                    log.info("getMemUserAssetsToUsdt priceTimeCompare, userPropertyTime: {}, mockTime: {}", billCoinUserProperty.getCheckTime().getTime(), mockTime);
                    log.error("getMemUserAssetsToUsdt price not found accountType:{} coinId:{} userCoinPropertyMap:{} priceVos:{}", accountTypeEnum.getCode(), coinId, JSON.toJSONString(billCoinUserProperty), JSON.toJSONString(priceVos));
                    // 如果时间片没取到汇率，则可能拿到了相对于时间片的未来时间的币种，所以尝试取接近当前时间的汇率
                    Map<Integer, PriceVo> priceVosNew = commonService.getNewCoinIdRatesMapCache(mockTime, accountTypeEnum);
                    priceVo = priceVosNew.get(coinId);
                    // 如果还没取到，则抛异常
                    if (priceVo == null) {
                        log.error("getMemUserAssetsToUsdt newPrice not found accountType:{} coinId:{} userCoinPropertyMap:{} priceVos:{}", accountTypeEnum.getCode(), coinId, JSON.toJSONString(billCoinUserProperty), JSON.toJSONString(priceVosNew));
                        throw new RuntimeException("price not found coinId:" + coinId);
                    }
                    //continue;
                }

                // 单币种资产值
                BigDecimal propSum = billCheckService.getPropSumByUserProperty(billCoinUserProperty);
                BigDecimal propSumUsdtValue = propSum.multiply(priceVo.getPrice());
                assetsUsdtValue = assetsUsdtValue.add(propSumUsdtValue);

                // 单币种未实现值
                BigDecimal unRealized = unRealizedMap.getOrDefault(coinId, BigDecimal.ZERO);
                BigDecimal unRealizedUsdt = unRealized.multiply(priceVo.getPrice());
                unRealizedUsdtValue = unRealizedUsdtValue.add(unRealizedUsdt);

                // 单币种总值
                BigDecimal coinTotal = propSumUsdtValue.add(unRealizedUsdt);

                // 总负值累加
                if (coinTotal.compareTo(BigDecimal.ZERO) < 0) {
                    negativeAssetsUsdtValue = negativeAssetsUsdtValue.add(propSumUsdtValue);
                    negativeUnRealizedUsdtValue = negativeUnRealizedUsdtValue.add(unRealizedUsdt);
                    negativeTotalUsdtValue = negativeTotalUsdtValue.add(coinTotal);
                }

                // 总资产累加
                totalUsdtValue = totalUsdtValue.add(coinTotal);

                // 计算折U明细
                totalDetailMap.put(coinId, Tuples.of(propSum.add(unRealized), priceVo.getPrice(), coinTotal));
                assetsDetailMap.put(coinId, Tuples.of(propSum, priceVo.getPrice(), propSumUsdtValue));
                unRealizedDetailMap.put(coinId, Tuples.of(unRealized, priceVo.getPrice(), unRealizedUsdt));
            }
        }
        return UserAssetsDTO.builder()
                .userId(userId)
                .totalUsdtValue(totalUsdtValue)
                .assetsUsdtValue(assetsUsdtValue)
                .unRealizedUsdtValue(unRealizedUsdtValue)
                .negativeTotalUsdtValue(negativeTotalUsdtValue)
                .negativeAssetsUsdtValue(negativeAssetsUsdtValue)
                .negativeUnRealizedUsdtValue(negativeUnRealizedUsdtValue)
                .totalDetailMap(totalDetailMap)
                .assetsDetailMap(assetsDetailMap)
                .unRealizedDetailMap(unRealizedDetailMap)
                .build();
    }

    public Map<String, List<CommonBillChangeData>> getUserCoinErrorMap(Long userId) {
        Map<String, List<CommonBillChangeData>> userCoinErrorBillMap = errorBillMap.get(userId);
        Map<String, List<CommonBillChangeData>> newUserCoinErrorBillMap = new HashMap<>();
        if (userCoinErrorBillMap != null && userCoinErrorBillMap.size() > 0) {
            userCoinErrorBillMap.forEach((key, value) -> {
                newUserCoinErrorBillMap.put(key, value.stream().limit(5).collect(Collectors.toList()));
            });
        }
        return newUserCoinErrorBillMap;
    }

    /**
     * 修复业务订单号 bizTime
     *
     * @param commonBillChangeData
     */
    public void repairOrderNoBizTime(ApolloReconciliationBizConfig apolloBizConfig, CommonBillChangeData commonBillChangeData) {
        if (!apolloBizConfig.isRepairOrderNoBizTimeFlag()) {
            return;
        }
        // 如果redis存在修复数据返回
        Date redisOldBizTime = orderNoBizTimeRedisCache.getIfPresent(commonBillChangeData.getOrderId());
        if (redisOldBizTime != null) {
            commonBillChangeData.setBizTime(redisOldBizTime);
            BizLogUtils.log(LogLevelEnum.NON_KEY_INPUT, apolloBizConfig, "BillUserCheckModule.repairOrderNoBizTime redis info accountType:{} id:{}  bizType:{}  orderId:{} oldBizTime:{} newBizTime:{}", accountTypeEnum.getCode(), commonBillChangeData.getBizId(), commonBillChangeData.getBizType(), commonBillChangeData.getOrderId(), DateUtil.date2str(redisOldBizTime), DateUtil.date2str(commonBillChangeData.getBizTime()));
            return;
        }

        // redis 验证时间顺序
        Date oldBizTime = orderNoBizTimeCache.getIfPresent(commonBillChangeData.getOrderId());
        if (oldBizTime == null) {
            orderNoBizTimeCache.put(commonBillChangeData.getOrderId(), commonBillChangeData.getBizTime());
        } else {
            // 消息按时间先后顺序 修复bizTime
            if (oldBizTime.getTime() <= commonBillChangeData.getBizTime().getTime()) {
                commonBillChangeData.setBizTime(oldBizTime);
                BizLogUtils.log(LogLevelEnum.NON_KEY_INPUT, apolloBizConfig, "BillUserCheckModule.repairOrderNoBizTime info accountType:{} orderId:{} oldBizTime:{} newBizTime:{}", accountTypeEnum.getCode(), commonBillChangeData.getOrderId(), DateUtil.date2str(oldBizTime), DateUtil.date2str(commonBillChangeData.getBizTime()));
            } else {
                // 存储orderId正确时间 并告警
                redisTemplate.opsForHash().put(RedisUtil.getReversedOrderIdHashKey(accountTypeEnum.getCode()), commonBillChangeData.getOrderId(), String.valueOf(commonBillChangeData.getBizTime().getTime()));
                log.error("BillUserCheckModule.repairOrderNoBizTime error accountType:{} orderId:{} oldBizTime:{} newBizTime:{}", accountTypeEnum.getCode(), commonBillChangeData.getOrderId(), DateUtil.date2str(oldBizTime), DateUtil.date2str(commonBillChangeData.getBizTime()));
                alarmNotifyService.alarm(accountTypeEnum.getCode(), USER_CHECK_MODULE_ORDER_DELAY_ERROR, accountTypeEnum.getCode(), commonBillChangeData.getOrderId(), DateUtil.date2str(oldBizTime), DateUtil.date2str(commonBillChangeData.getBizTime()));
            }
        }
    }

    public Cache<String, Date> getOrderNoBizTimeRedisCache() {
        return orderNoBizTimeRedisCache;
    }
}
