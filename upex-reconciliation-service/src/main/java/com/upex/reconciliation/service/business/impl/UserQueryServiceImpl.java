package com.upex.reconciliation.service.business.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.upex.reconciliation.service.business.UserQueryService;
import com.upex.user.dto.sub.request.ParentRequestDTO;
import com.upex.user.dto.sub.response.UserParentChildInfoDTO;
import com.upex.user.facade.user.UserChildQueryFeignClient;
import com.upex.user.facade.utils.IdQueryResult;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户及子账户服务实现类
 *
 * <AUTHOR>
 * @Date 2025/4/24
 */
@Service
public class UserQueryServiceImpl implements UserQueryService {

    @Resource
    private UserChildQueryFeignClient userChildQueryFeignClient;

    @Override
    public Pair<Boolean, List<Long>> getChildListByParentId(Long parentId, List<Integer> childTypeList, Integer pageSize, Integer maxTotal) {
        List<Long> childIdList = new ArrayList<>();
        ParentRequestDTO request = new ParentRequestDTO();
        request.setParentId(parentId);
        request.setFirstId(0L);
        request.setChildTypeList(childTypeList);
        request.setLimit(pageSize);
        Integer total = 0;
        Boolean allChild = Boolean.TRUE;
        while (true) {
            IdQueryResult<UserParentChildInfoDTO> userChildResult = userChildQueryFeignClient.queryAllChildInfoListByParentRequest(request);
            if (Objects.isNull(userChildResult) || CollectionUtil.isEmpty(userChildResult.getItems())) {
                break;
            }
            List<UserParentChildInfoDTO> dataList = userChildResult.getItems();
            List<Long> childList = dataList.stream().map(UserParentChildInfoDTO::getUserId).collect(Collectors.toList());
            allChild = !userChildResult.isHasNextPage();
            List<Long> subChildList = childList;
            if (total + childList.size() > maxTotal) {
                subChildList = childList.subList(0, maxTotal - total);
                allChild = Boolean.FALSE;
            }
            childIdList.addAll(subChildList);
            total = childIdList.size();
            if (dataList.size() < pageSize || total >= maxTotal) {
                break;
            }
            request.setFirstId(dataList.get(dataList.size() - 1).getId());
        }
        return Pair.of(allChild, childIdList.stream().distinct().collect(Collectors.toList()));
    }
}
