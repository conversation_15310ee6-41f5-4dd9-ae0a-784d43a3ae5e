package com.upex.reconciliation.service.business.cex;

import com.upex.reconciliation.service.dao.cex.entity.CexAssetConfig;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.service.client.cex.enmus.CexAssetHistoryTypeEnum;

import java.util.Date;

public interface ICexAssetSyncHistory {

    void syncAssetHistory(CexAssetConfig cexAssetConfig,ThirdCexUserConfig userConfig, Date startTime, Date endTime,Date checkSyncTime);

    CexAssetHistoryTypeEnum getAssetHistoryType();

}
