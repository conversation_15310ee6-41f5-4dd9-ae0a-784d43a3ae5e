package com.upex.reconciliation.service.service.client.cex.dto.res.common;

import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum;
import com.upex.utils.framework.ApiResult;
import com.upex.utils.framework.IErrorCode;


public class CommonRes<T>  extends ApiResult<T>{


    private CommonRes(T data) {
        super(data);
        super.setGroup(ReconCexExceptionEnum.getGroupName());
    }

    private CommonRes(IErrorCode errorCode) {
        super(errorCode);
        super.setGroup(ReconCexExceptionEnum.getGroupName());
    }

    public static CommonRes getCastClassErrorRes() {
        return new CommonRes<>(ReconCexExceptionEnum.CALSS_CAST_ERROR);
    }

    public static <R> CommonRes getSucApiBaseRes(R data) {
        if(data instanceof Integer){
            if((Integer)data >= 1){
                return new CommonRes("修改成功");
            }else{
                return new CommonRes("修改失败");
            }
        }
        return new CommonRes(data);
    }



    public static CommonRes getSucApiBaseRes() {
        return new CommonRes("");
    }


    public static CommonRes getFailApiBaseRes(IErrorCode errorCode) {
        return new CommonRes(errorCode);
    }


    public static CommonRes getUnknowApiErrorBaseRes() {
        return new CommonRes(ReconCexExceptionEnum.UNKNOW_ERROR);
    }


    public Boolean getSuccess() {
        return super.getCode().equals("200");
    }

    @Override
    public T getData() {
        return (T) super.getData();
    }
}
