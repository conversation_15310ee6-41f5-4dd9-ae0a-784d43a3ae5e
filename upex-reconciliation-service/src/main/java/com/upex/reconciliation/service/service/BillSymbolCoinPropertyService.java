package com.upex.reconciliation.service.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.dao.entity.BillSymbolCoinProperty;
import com.upex.reconciliation.service.dao.mapper.BillSymbolCoinPropertyMapper;
import com.upex.utils.task.TaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

@Slf4j
@Service
public class BillSymbolCoinPropertyService {


    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "assetSnapshotTaskManager")
    private TaskManager assetSnapshotTaskManager;

    @Resource(name = "billSymbolCoinPropertyMapper")
    private BillSymbolCoinPropertyMapper billSymbolCoinPropertyMapper;

    public Boolean batchDelete(Long beginId, Long pageSize, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolCoinPropertyMapper.batchDelete(beginId, pageSize, accountType, accountParam));
    }

    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolCoinPropertyMapper.deleteByCheckTime(accountType, accountParam, checkTime));
    }

    public int batchInsert(List<BillSymbolCoinProperty> records, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolCoinPropertyMapper.batchInsert(records, accountType, accountParam));
    }

    public List<BillSymbolCoinProperty> selectListByCheckTime(Byte accountType, String accountParam, Date checkOkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolCoinPropertyMapper.selectListByCheckTime(accountType, accountParam, checkOkTime));
    }

    public Long getIdByCheckTime(Byte accountType, String accountParam, Date checkTime, String operation) {
        String operationNew = operation;
        return dbHelper.doDbOpInReconMaster(() -> billSymbolCoinPropertyMapper.getIdByCheckTime(accountType, accountParam, checkTime, operationNew));
    }

    public boolean deleteByMaxId(Byte accountType, String accountParam, Long maxId, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolCoinPropertyMapper.deleteByMaxId(accountType, accountParam, maxId, batchSize));
    }

    /**
     * 批量插入
     *
     * @param id
     * @return
     */
    public Integer deleteById(Byte accountType, String accountParam, Long id) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolCoinPropertyMapper.deleteById(accountType, accountParam, id));
    }

    public List<BillSymbolCoinProperty> queryByCheckTime(Date checkTime, List<String> subSystemList) {
        // 查询
        Queue<BillSymbolCoinProperty> billSymbolCoinPropertyList = new ConcurrentLinkedQueue<>();
        assetSnapshotTaskManager.forEachSubmitBatchAndWait(subSystemList, (String subSystem) -> {
            String[] strings = subSystem.split(BillConstants.SEPARATOR);
            byte accountType = Byte.parseByte(strings[0]);
            String accountParam = strings[1].toLowerCase();
            List<BillSymbolCoinProperty> list = billSymbolCoinPropertyMapper.queryByCheckTime(checkTime, accountType, accountParam);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
                list.forEach(element -> element.setAccountType(accountType));
            }
            billSymbolCoinPropertyList.addAll(list);
        });
        return Lists.newArrayList(billSymbolCoinPropertyList);
    }


    public void repairBillSymbolCoinProperty(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        log.info("repairBillSymbolCoinProperty data:{}", jobParam);
        String action = jsonObject.getString("action");
        Byte accountType = jsonObject.getByte("accountType");
        String accountParam = jsonObject.getString("accountParam");
        JSONArray delIds = jsonObject.getJSONArray("delIds");
        JSONArray insertList = jsonObject.getJSONArray("insertList");
        if ("delete".equals(action)) {
            if (delIds == null || delIds.size() == 0) {
                return;
            }
            for (int i = 0; i < delIds.size(); i++) {
                deleteById(accountType, accountParam, delIds.getLong(i));
            }
        } else if ("insert".equals(action)) {
            if (insertList == null || insertList.size() == 0) {
                return;
            }
            List<BillSymbolCoinProperty> billSymbolCoinProperties = Convert.toList(BillSymbolCoinProperty.class, insertList);
            if (CollectionUtils.isNotEmpty(billSymbolCoinProperties)) {
                batchInsert(billSymbolCoinProperties, accountType, accountParam);
            }
        }
    }

    public Boolean deleteByLtCheckTime(Byte accountType, String accountParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolCoinPropertyMapper.deleteByLtCheckTime(accountType, accountParam, checkTime, batchSize));
    }

    /**
     * 更新单条数据
     *
     * @param accountType
     * @param accountParam
     * @param symbolCoinProperty
     */
    public Boolean updateById(Byte accountType, String accountParam, BillSymbolCoinProperty symbolCoinProperty) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolCoinPropertyMapper.updateById(accountType, accountParam, symbolCoinProperty));
    }
}