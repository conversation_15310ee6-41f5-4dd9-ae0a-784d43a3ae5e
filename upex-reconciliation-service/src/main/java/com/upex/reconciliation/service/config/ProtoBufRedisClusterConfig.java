package com.upex.reconciliation.service.config;

import com.upex.commons.support.util.AwsManagerUtils;
import com.upex.utils.model.RedisAuth;
import com.upex.utils.util.EnvUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import redis.clients.jedis.JedisPoolConfig;

import java.time.Duration;

/**
 * <AUTHOR>
@Configuration
@ConditionalOnProperty(name = "protobuf.cluster.redis.flag", havingValue = "true", matchIfMissing = true)
@Slf4j
public class ProtoBufRedisClusterConfig {

    @Value("${protobuf.cluster.redis.host:redis}")
    private String hostName;

    @Value("${protobuf.cluster.redis.port:6379}")
    private int port;

    @Value("${protobuf.cluster.redis.password:}")
    private String password;

    @Value("${protobuf.cluster.redis.timeout:10000}")
    private int timeout;

    @Value("${protobuf.cluster.redis.pool.min-idle:10}")
    private int minIdle;

    @Value("${protobuf.cluster.redis.pool.max-idle:5}")
    private int maxIdle;

    @Value("${protobuf.cluster.redis.pool.max-wait:3000}")
    private long maxWait;

    @Value("${protobuf.cluster.redis.pool.max-active:1000}")
    private int maxActive;

    @Value("${protobuf.cluster.redis.endpoint:endpoint}")
    private String endpoint;

    @Value("${protobuf.cluster.redis.region:region}")
    private String region;

    @Value("${protobuf.cluster.redis.secret-name:secretName}")
    private String secretName;

    @Bean(name = "protobufClusterConnectionFactory")
    public RedisConnectionFactory decoupleConnectionFactory() {
        if (EnvUtils.isEnv(EnvUtils.EnvType.ONLINE) || EnvUtils.isEnv(EnvUtils.EnvType.PRE)) {
            log.info("protobuf redis is online");
            return createJedisClusterSecretManagerFactory();
        } else { //非正式环境
            log.info("protobuf redis is singleton");
            JedisConnectionFactory factory = new JedisConnectionFactory();
            factory.setHostName(hostName);
            factory.setPort(port);
            if (!StringUtils.isEmpty(password)) {
                factory.setPassword(password);
            }
            factory.setTimeout(timeout);
            factory.setPoolConfig(this.poolConfig());
            return factory;
        }
    }

    @Bean(name = "redisTemplateDataParseProtobuf")
    public StringRedisTemplate decoupleStringRedisTemplate(@Qualifier("protobufClusterConnectionFactory") final RedisConnectionFactory redisConnectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(redisConnectionFactory);
        log.info("redisTemplateDataParseProtobuf init end");
        return template;
    }

    protected JedisPoolConfig poolConfig() {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMinIdle(minIdle);
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMaxWaitMillis(maxWait);
        poolConfig.setMaxTotal(maxActive);
        poolConfig.setTestOnBorrow(false);
        return poolConfig;
    }

    protected JedisConnectionFactory createJedisClusterSecretManagerFactory() {
        JedisClientConfiguration.DefaultJedisClientConfigurationBuilder jedisClientConfiguration = (JedisClientConfiguration.DefaultJedisClientConfigurationBuilder) JedisClientConfiguration.builder();
        jedisClientConfiguration.poolConfig(poolConfig());
        jedisClientConfiguration.usePooling();
        jedisClientConfiguration.connectTimeout(Duration.ofMillis(timeout));
        jedisClientConfiguration.readTimeout(Duration.ofMillis(timeout));

        //配置信息放配置文件
        RedisClusterConfiguration clusterConfiguration = new RedisClusterConfiguration();
        clusterConfiguration.addClusterNode(new RedisNode(hostName,  port));
        if (!org.springframework.util.StringUtils.isEmpty(password)) {
            clusterConfiguration.setPassword(RedisPassword.of(password));
            jedisClientConfiguration.useSsl();
        } else {
            RedisAuth redisAuth = AwsManagerUtils.getAwsSecretManagerRedisAuth(endpoint, region, secretName);
            clusterConfiguration.setPassword(RedisPassword.of(redisAuth.getPassword()));
            if(StringUtils.isNotEmpty(redisAuth.getUsername())){
                clusterConfiguration.setUsername(redisAuth.getUsername());
            }
            jedisClientConfiguration.useSsl();
        }

        return new JedisConnectionFactory(clusterConfiguration, jedisClientConfiguration.build());
    }
}