package com.upex.reconciliation.service.business.prometheus;

public enum JobMonitorMetricNameEnum {


    // 任务耗时统计
    TOTAL_RECONCILIATION_TIME_SUMMARY("total_reconciliation_time_summary_","总耗时统计",false),
    ACCOUNT_TYPE_RECONCILIATION_TIME_SUMMARY("account_type_reconciliation_time_summary_","业务线对账耗时统计",false),
    // 增加统计：取账单时间，  对账时间，  取反算资产时间， 落库总耗时
    QUERY_BILL_TIME_SUMMARY("query_bill_time_summary_","取账单时间统计",false),
    LONG_AND_SHORT_ACCOUNTS_RECONCILIATION_TIME_SUMMARY("long_and_short_accounts_reconciliation_time_summary_","多空仓对账时间统计",false),
    INCOMING_AND_OUTGOING_ACCOUNTS_RECONCILIATION_TIME_SUMMARY("incoming_and_outgoing_accounts_reconciliation_time_summary_","出入对账时间统计",false),
    USER_RECONCILIATION_TIME_SUMMARY("user_reconciliation_time_summary_","个人维度对账时间统计",false),
    RECONCILIATION_TIME_SUMMARY("reconciliation_time_summary_","对账时间统计",false),
    BACK_CALCULATION_TIME_SUMMARY("back_calculation_time_summary_","取反算资产时间统计",false),
    ACCOUNT_EQUITY_TIME_SUMMARY("account_equity_time_summary_","计算账户权益时间统计",false),

    DB_TOTAL_TIME_SUMMARY("db_total_time_summary_","db总耗时",false),

    // 业务指标统计
    BUSINESS_USER_BILL_USER_COUNT("business_user_bill_user_count","业务系统账单涉及总用户数",true),



    // db 耗时统计
    BILL_COIN_PROPERTY_INSERT_TIME_SUMMARY("bill_coin_property_insert_time_summary","bill_coin_property 表单次插入耗时统计",true),
    BILL_COIN_TYPE_PROPERTY_INSERT_TIME_SUMMARY("bill_coin_type_property_insert_time_summary","bill_coin_type_property 表单次插入耗时统计",true),
    BILL_COIN_USER_PROPERTY_INSERT_TIME_SUMMARY("bill_coin_user_property_insert_time_summary","bill_coin_user_property 表单次插入耗时统计",true),
    BILL_COIN_TYPE_USER_PROPERTY_INSERT_TIME_SUMMARY("bill_coin_type_user_property_insert_time_summary","bill_coin_type_user_property 表单次插入耗时统计",true),
    BILL_SYMBOL_PROPERTY_INSERT_TIME_SUMMARY("bill_symbol_property_insert_time_summary","bill_symbol_property 表单次插入耗时统计",true),
    BILL_SYMBOL_BIZ_TYPE_PROPERTY_INSERT_TIME_SUMMARY("bill_symbol_biz_type_property_insert_time_summary","bill_symbol_biz_type_property 表单次插入耗时统计",true),
    BILL_SYMBOL_COIN_PROPERTY_INSERT_TIME_SUMMARY("bill_symbol_coin_property_insert_time_summary","bill_symbol_coin_property 表单次插入耗时统计",true),
    BILL_CONTRACT_PROFIT_SYMBOL_DETAIL_INSERT_TIME_SUMMARY("bill_contract_profit_symbol_detail_insert_time_summary","bill_contract_profit_symbol_detail 表单次插入耗时统计",true),
    BILL_CONTRACT_PROFIT_COIN_DETAIL_INSERT_TIME_SUMMARY("bill_contract_profit_coin_detail_insert_time_summary","bill_contract_profit_coin_detail 表单次插入耗时统计",true),
    BILL_COIN_USER_ASSETS_INSERT_TIME_SUMMARY("bill_coin_user_assets_insert_time_summary","bill_coin_user_assets 表单次插入耗时统计",true),
    BILL_COIN_USER_CONVERTER_INSERT_TIME_SUMMARY("bill_coin_user_converter_insert_time_summary","bill_coin_user_converter 表单次插入耗时统计",true),



    // db 更新耗时
    BILL_COIN_USER_PROPERTY_UPDATE_TIME_SUMMARY("bill_coin_user_property_update_time_summary","bill_coin_user_property 表单次更新耗时统计",true),



    // db 插入量统计
    BILL_COIN_PROPERTY_INSERT_COUNT("bill_coin_property_insert_count","bill_coin_property 表单次插入数量统计",true),
    BILL_COIN_TYPE_PROPERTY_INSERT_COUNT("bill_coin_type_property_insert_count","bill_coin_type_property 表单次插入数量统计",true),
    BILL_COIN_USER_PROPERTY_INSERT_COUNT("bill_coin_user_property_insert_count","bill_coin_user_property 表单次插入数量统计",true),
    BILL_COIN_TYPE_USER_PROPERTY_INSERT_COUNT("bill_coin_type_user_property_insert_count","BILL_COIN_TYPE_USER_PROPERTY 表单次插入数量统计",true),
    BILL_SYMBOL_PROPERTY_INSERT_COUNT("bill_symbol_property_insert_count","bill_symbol_property 表单次插入数量统计",true),

    BILL_SYMBOL_BIZ_TYPE_PROPERTY_INSERT_COUNT("bill_symbol_biz_type_property_insert_count","bill_symbol_biz_type_property 表单次插入数量统计",true),
    BILL_SYMBOL_COIN_PROPERTY_INSERT_COUNT("bill_symbol_coin_property_insert_count","bill_symbol_coin_property 表单次插入数量统计",true),
    BILL_CONTRACT_PROFIT_SYMBOL_DETAIL_INSERT_COUNT("bill_contract_profit_symbol_detail_insert_count","bill_contract_profit_symbol_detail 表单次插入数量统计",true),
    BILL_CONTRACT_PROFIT_COIN_DETAIL_INSERT_COUNT("bill_contract_profit_coin_detail_insert_count","bill_contract_profit_coin_detail 表单次插入数量统计",true),
    BILL_COIN_USER_ASSETS_INSERT_COUNT("bill_coin_user_assets_insert_count","bill_coin_user_assets 表单次插入数量统计",true),
    BILL_COIN_USER_CONVERTER_INSERT_COUNT("bill_coin_user_converter_insert_count","bill_coin_user_converter 表单次插入数量统计",true),



    // db 更新量统计
    BILL_COIN_USER_PROPERTY_UPDATE_COUNT("BILL_COIN_USER_PROPERTY_UPDATE_COUNT","bill_coin_user_property 表单次更新数量统计",true),


    ;

    private String name;

    private String desc;

    private boolean independent;

    JobMonitorMetricNameEnum(String name, String desc, boolean ifIndependent) {
        this.name = name;
        this.desc = desc;
        this.independent = ifIndependent;
    }


    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static JobMonitorMetricNameEnum toEnum(String name) {
        for (JobMonitorMetricNameEnum item : JobMonitorMetricNameEnum.values()) {
            if (item.name.equals(name)) {
                return item;
            }
        }
        return null;
    }

    public boolean isIndependent() {
        return independent;
    }
}
