package com.upex.reconciliation.service.business.createtablebyroute;

import com.upex.reconciliation.service.model.config.ReconTableRouteConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.reconciliation.service.utils.SplitTableUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.Map;

import static com.upex.reconciliation.service.business.createtablebyroute.ReconCreateTableFactory.DEFAULT_ACCOUNT_TYPE;

@Slf4j
public abstract class AbstractTableCreator implements ITableCreator {



    @Override
    public String getTableSuffixName(String accountType, Date time) {
        ReconTableRouteConfig.TableRouteRule tableRouteRule = getTableRouteRule(accountType);
        if (tableRouteRule == null) {
            return "";
        }
        String rule = tableRouteRule.getRule();
        if (ReconTableRouteConfig.TableRouteRuleEnum.DAY.name().equalsIgnoreCase(rule)) {
            return tableRouteRule.getEffectiveTime() == null || time.getTime() >= tableRouteRule.getEffectiveTime() ? SplitTableUtils.getTableNameSuffixForDay(time) : "";
        } else if (ReconTableRouteConfig.TableRouteRuleEnum.MONTH.name().equalsIgnoreCase(rule)) {
            return tableRouteRule.getEffectiveTime() == null || time.getTime() >= tableRouteRule.getEffectiveTime() ? SplitTableUtils.getBillContractProfitTransferTableNameSuffix(time) : "";
        } else if (ReconTableRouteConfig.TableRouteRuleEnum.NONE.name().equalsIgnoreCase(rule)) {
            return "";
        }
        throw new RuntimeException(getTableType() + "不支持创建表类型" + rule);
    }


    protected ReconTableRouteConfig.TableRouteRule getTableRouteRule(String accountType) {
        accountType = StringUtils.isNotEmpty(accountType) ? accountType : null;
        ReconTableRouteConfig reconTableRouteConfig = ReconciliationApolloConfigUtils.getReconTableRouteConfig();
        if (reconTableRouteConfig == null) {
            return null;
        }
        Map<String, ReconTableRouteConfig.TableRouteRule> routeRuleMap = reconTableRouteConfig.getRouteConfig().getOrDefault(getTableType(), Collections.emptyMap());
        // 优先获取自定义配置 没有走默认配置
        ReconTableRouteConfig.TableRouteRule tableRouteRule = routeRuleMap.get(accountType);
        if (tableRouteRule == null) {
            ReconTableRouteConfig.TableRouteRule defaultTableRouteRule = routeRuleMap.get(DEFAULT_ACCOUNT_TYPE);
            //兼容billContractProfitTransfer-accountType为空的情况
            if (tableRouteRule == null && accountType == null) {
                return defaultTableRouteRule;
            }
            if (defaultTableRouteRule != null && CollectionUtils.isNotEmpty(defaultTableRouteRule.getAccountTypes())
                    && defaultTableRouteRule.getAccountTypes().contains(accountType)) {
                tableRouteRule = defaultTableRouteRule;
            }
        }
        return tableRouteRule;
    }
}
