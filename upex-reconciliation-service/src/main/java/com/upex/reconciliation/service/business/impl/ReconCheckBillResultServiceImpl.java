package com.upex.reconciliation.service.business.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.commons.support.exception.ApiException;
import com.upex.commons.support.util.SiteUtil;
import com.upex.mixcontract.process.facade.feign.inner.InnerAccountFeignClient;
import com.upex.mixcontract.process.facade.params.query.AccountQueryParam;
import com.upex.mixcontract.process.facade.results.AccountListDTO;
import com.upex.reconciliation.facade.dto.results.ReconTotalAssetsDetailVo;
import com.upex.reconciliation.facade.enums.*;
import com.upex.reconciliation.facade.model.ReconBillUserVo;
import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import com.upex.reconciliation.facade.params.SysAssetsParams;
import com.upex.reconciliation.service.business.*;
import com.upex.reconciliation.service.business.profit.CheckProfitGrayService;
import com.upex.reconciliation.service.business.profit.CheckUserProfitService;
import com.upex.reconciliation.service.business.profit.impl.RedisCheckUserProfitServiceImpl;
import com.upex.reconciliation.service.business.profitabnormal.ProfitAbnormalService;
import com.upex.reconciliation.service.business.prometheus.InterfaceMonitorMetricNameEnum;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.common.constants.AlarmTemplateLevelEnum;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.CheckBillResultConstant;
import com.upex.reconciliation.service.common.constants.enums.AssetsCheckTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.*;
import com.upex.reconciliation.service.common.thread.BaseAsyncTaskManager;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.dao.mapper.wrapper.StatisticsPropertyService;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.CheckVo;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.config.WithdrawHostingBalanceConfig;
import com.upex.reconciliation.service.model.dto.AccountProfitDTO;
import com.upex.reconciliation.service.model.dto.BackCalculationResultDto;
import com.upex.reconciliation.service.model.dto.ProfitAlarmDto;
import com.upex.reconciliation.service.model.dto.WithdrawCheckResultDTO;
import com.upex.reconciliation.service.model.enums.BillWhiteListTypeEnum;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.service.impl.BillCoinUserPropertySnapshotService;
import com.upex.reconciliation.service.service.impl.CapitalOrderService;
import com.upex.reconciliation.service.utils.*;
import com.upex.spot.dto.enums.SourceEnum;
import com.upex.spot.dto.params.withdraw.CapitalOrderQueryParam;
import com.upex.spot.dto.result.withdraw.SpotInnerCapitalOrderInfoResult;
import com.upex.spot.facade.query.SpotCapitalQueryClient;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.user.enums.permission.UserPermissionEnum;
import com.upex.user.facade.utils.SiteCodeUtils;
import com.upex.user.protobuf.cache.UserPbCacheService;
import com.upex.utils.log.AlarmUtils;
import com.upex.utils.task.TaskManager;
import com.upex.utils.task.TaskVoidBatchResult;
import com.upex.utils.thread.MdcContext;
import com.upex.utils.util.MdcUtils;
import com.upex.utils.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.upex.reconciliation.service.business.profitbacktest.AbstractProfitBakCheck.PROFIT_BAKCHECK_FROM_XXLJOB;
import static com.upex.reconciliation.service.common.constants.AccountTypeConstant.TIME_INTERVAL;
import static com.upex.reconciliation.service.common.constants.BillConstants.CHECK_PERMISSION_ERROR_CODE_SET;
import static com.upex.reconciliation.service.common.constants.BillConstants.SEPARATOR;

/**
 * 通过用户检查对账结果
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ReconCheckBillResultServiceImpl implements ReconCheckBillResultService {
    @Resource
    private BillAllConfigService billAllConfigService;
    @Resource
    private AssetsBillConfigService assetsBillConfigService;
    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;
    @Resource
    private UserPbCacheService userPbCacheService;
    @Resource(name = "baseAsyncTaskManager")
    private BaseAsyncTaskManager baseAsyncTaskManager;
    @Resource(name = "taskManager")
    private TaskManager taskManager;
    @Resource
    private BillUserService billUserService;
    @Resource
    private ReconUserAssetsSnapShotService reconUserAssetsSnapShotService;
    @Resource
    private BillCapitalConfigService billCapitalConfigService;
    @Resource
    private CommonService commonService;
    @Resource
    private InnerAccountFeignClient innerAccountFeignClient;
    @Resource
    private StatisticsPropertyService statisticsPropertyService;
    @Resource
    private RealTimeFlowService realTimeFlowService;
    @Resource
    private BusinessService businessService;
    @Resource
    private BillDelayAccountService billDelayAccountService;
    @Resource
    private BillCoinUserPropertyAssetSnapshotService billCoinUserPropertyAssetSnapshotService;

    @Resource
    private BillCoinTypeUserPropertyService billCoinTypeUserPropertyService;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private AlarmNotifyService alarmNotifyService;
    @Resource
    private UserQueryService userQueryService;
    @Resource
    private BillCoinUserPropertySnapshotService billCoinUserPropertySnapshotService;

    @Resource
    private AssetCheckingConfigService assetCheckingConfigService;

    @Resource
    private StockService stockService;

    @Resource
    private CapitalOrderService capitalOrderService;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;


    @Resource
    private BillWhiteListConfigService billWhiteListConfigService;
    @Resource
    private SpotCapitalQueryClient spotCapitalQueryClient;

    @Resource
    private ReconWithdrawRecordService reconWithdrawRecordService;
    @Resource(name = "assetUserProfitTaskManager")
    private TaskManager assetUserProfitTaskManager;

    @Resource
    BillUserWithdrawProfitRecordService billUserWithdrawProfitRecordService;
    @Resource
    private UserBeginAssetsRedisService userBeginAssetsRedisService;

    @Resource
    ProfitAbnormalService profitAbnormalService;

    @Override
    public ReconBillUserVo selectCheckForTheResults(ReconCheckResultsParams reconCheckResultsParams) {
        Stopwatch slowLogStopwatch = Stopwatch.createStarted();
        ReconBillUserVo reconBillUserVo = new ReconBillUserVo();
        if (reconCheckResultsParams.getRequestDate() == null) {
            reconCheckResultsParams.setRequestDate(System.currentTimeMillis());
        }
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();

        if (!globalBillConfig.isSelectCheckForTheResultsOpen()) {
            reconBillUserVo.setPass(false);
            reconBillUserVo.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_OVERALL_OFF);
            return reconBillUserVo;
        }

        if (!globalBillConfig.isAlarmCheck()) {
            reconBillUserVo.setPass(true);
            return reconBillUserVo;
        }

        // 模拟盘用户禁止提币
        if (SiteUtil.checkPaptradingByUserId(reconCheckResultsParams.getUserId())) {
            reconBillUserVo.setPass(false);
            reconBillUserVo.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.USER_CHECK_DEMO_FAILED);
            this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.PAP_USER_CAN_NOT_WITHDRAW, reconCheckResultsParams.getUserId());
            return reconBillUserVo;
        }

        // 检测用户是否存在黑名单
        List<Long> checkUserBlackList = globalBillConfig.getCheckUserBlackList();
        if (CollectionUtils.isNotEmpty(checkUserBlackList) &&
                checkUserBlackList.contains(reconCheckResultsParams.getUserId())) {
            log.info("CheckBillResultServiceImpl selectCheckForTheResults 检测用户是否存在黑名单 userId:{}, checkUserBlackList:{}"
                    , reconCheckResultsParams.getUserId(), JSON.toJSONString(checkUserBlackList));
            reconBillUserVo.setPass(false);
            reconBillUserVo.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_USER_IN_BLACKLIST);
            return reconBillUserVo;
        }


        // 检测用户是否存在白名单
        List<Long> checkUserIsExistWhiteList = globalBillConfig.getCheckUserIsExistWhiteList();
        if (CollectionUtils.isNotEmpty(checkUserIsExistWhiteList) &&
                checkUserIsExistWhiteList.contains(reconCheckResultsParams.getUserId())) {
            log.info("CheckBillResultServiceImpl selectCheckForTheResults 检测用户是否存在白名单 userId:{}, whiteListUserIds:{}"
                    , reconCheckResultsParams.getUserId(), JSON.toJSONString(checkUserIsExistWhiteList));
            reconBillUserVo.setPass(true);
            return reconBillUserVo;
        }
        BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_getCheckUserIsExistWhiteList.getName(), reconCheckResultsParams.getUserId(), null);


        // 检查用户是否存在
        if (!verifyUserIsExist(reconCheckResultsParams, reconBillUserVo)) {
            reconBillUserVo.setPass(false);
            reconBillUserVo.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_USER_NOT_EXIST);
            return reconBillUserVo;
        }
        BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_verifyUserIsExist.getName(), reconCheckResultsParams.getUserId(), null);


        // 判定是否是demo用户
        if (globalBillConfig.isDemoUserBanWithdrawAlarmCheck()) {
            try {
                SiteCodeUtils.checkDefaultAccount(reconCheckResultsParams.getUserId());
            } catch (Exception e) {
                log.error("Demo account not allowed withdraw {}", reconCheckResultsParams.getUserId());
                this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.DEMO_USER_CAN_NOT_WITHDRAW, reconCheckResultsParams.getUserId());
                reconBillUserVo.setPass(false);
                reconBillUserVo.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.USER_CHECK_DEMO_FAILED);
                return reconBillUserVo;
            }
        }
        // 检查提币用户白名单
        List<Long> whiteListUserIds = globalBillConfig.getCheckWithdrawWhiteListUserIds();
        if (CollectionUtils.isNotEmpty(whiteListUserIds) &&
                whiteListUserIds.contains(reconCheckResultsParams.getUserId())) {
            log.info("CheckBillResultServiceImpl selectCheckForTheResults 检查提币用户白名单 userId:{}, whiteListUserIds:{}"
                    , reconCheckResultsParams.getUserId(), JSON.toJSONString(whiteListUserIds));
            reconBillUserVo.setPass(true);
            return reconBillUserVo;
        }
        // 检查提币订单白名单
        List<String> withdrawWhiteListOrders = globalBillConfig.getCheckWithdrawWhiteListOrders();
        if (CollectionUtils.isNotEmpty(withdrawWhiteListOrders)
                && withdrawWhiteListOrders.contains(reconCheckResultsParams.getRecordCode())) {
            log.info("CheckBillResultServiceImpl selectCheckForTheResults 检查提币订单白名单 userId:{}, orderNumber:{}, withdrawWhiteListOrders:{}"
                    , reconCheckResultsParams.getUserId(), reconCheckResultsParams.getRecordCode(), JSON.toJSONString(withdrawWhiteListOrders));
            reconBillUserVo.setPass(true);
            return reconBillUserVo;
        }
        BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_getCheckWithdrawWhiteListUserIds.getName(), reconCheckResultsParams.getUserId(), null);

        // 校验交易和账单数据存在篡改的用户
        if (globalBillConfig.isVerifyUserIllegalModificationBillData()
                && !verifyUserIllegalModificationBillData(reconCheckResultsParams)) {
            reconBillUserVo.setPass(false);
            reconBillUserVo.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_USER_ILLEGAL_MODIFICATION_BILL_DATA);
            return reconBillUserVo;
        }

        // 延迟入账check
        if (globalBillConfig.isCheckDelayAccountOpen()) {
            boolean result = checkDelayAccountByOldBill(reconCheckResultsParams, globalBillConfig);
            if (result) {
                log.info("CheckBillResultServiceImpl checkDelayAccount 检查延迟入账 userId:{}", reconCheckResultsParams.getUserId());
                this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.USER_DELAY_ACCOUNT, reconCheckResultsParams.getUserId());
                reconBillUserVo.setPass(false);
                reconBillUserVo.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_DELAY_FAILED);
                return reconBillUserVo;
            }
        } else {
            checkDelayAccountByOldBill(reconCheckResultsParams, globalBillConfig);
        }
        BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_isUserRealTimeNegativeAssetsAlarmCheck.getName(), reconCheckResultsParams.getUserId(), null);


        // 用户盈利check
        if (globalBillConfig.isCheckProfitOpen()) {
            try {
                // 可配置多个时间周期
                List<Integer> timePeriodProfitList = globalBillConfig.getTimePeriodProfitList();
                if (CollectionUtils.isNotEmpty(timePeriodProfitList)) {
                    for (Integer timePeriod : timePeriodProfitList) {
                        if (globalBillConfig.isSycCheckProfitAlarmOpen()) {
                            boolean result = checkProfitAccountByGray(reconCheckResultsParams, globalBillConfig, timePeriod);
                            log.info("CheckBillResultServiceImpl checkProfitAccount outcome userId: {}, result: {} ,timePeriod :{}", reconCheckResultsParams.getUserId(), result, timePeriod);
                            if (result && globalBillConfig.isCheckProfitAlarmOpen()) {
                                log.info("CheckBillResultServiceImpl checkProfitAccount 检查用户资产盈利 userId:{}", reconCheckResultsParams.getUserId());
                                reconBillUserVo.setPass(false);
                                reconBillUserVo.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_PROFIT_FAILED);
                                return reconBillUserVo;
                            }

                        } else {
                            try {
                                Future<Boolean> future = baseAsyncTaskManager.submit(() -> {
                                    boolean result = checkProfitAccountByGray(reconCheckResultsParams, globalBillConfig, timePeriod);
                                    log.info("CheckBillResultServiceImpl checkProfitAccount outcome userId: {}, result: {} ,timePeriod :{}", reconCheckResultsParams.getUserId(), result, timePeriod);
                                    return result;
                                });
                                Boolean result = future.get(globalBillConfig.getCheckProfitAccountTimeout(), TimeUnit.SECONDS);
                                if (result && globalBillConfig.isCheckProfitAlarmOpen()) {
                                    log.info("CheckBillResultServiceImpl checkProfitAccount 检查用户资产盈利1 userId:{}", reconCheckResultsParams.getUserId());
                                    reconBillUserVo.setPass(false);
                                    reconBillUserVo.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_PROFIT_FAILED);
                                    return reconBillUserVo;
                                }
                            } catch (Exception e) {
                                log.error("CheckBillResultServiceImpl checkProfitAccount call timeout", e);
                            }
                        }
                    }
                }
                BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_checkProfitAccount.getName(), reconCheckResultsParams.getUserId(), null);
            } catch (Exception e) {
                log.error("CheckBillResultServiceImpl checkProfitAccount Exception:", e);
            }
        }


        // 获取配置系统中bill_config最小check_ok_time
        BillAllConfig minBillConfig = billAllConfigService.getMinCheckOkBillConfig(globalBillConfig.getMinCheckOkTimeSubSystemList());
        if (minBillConfig == null) {
            this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.ACCOUNT_TYPE_CONFIG_NULL, reconCheckResultsParams.getUserId());
            log.info("CheckBillResultServiceImpl selectCheckForTheResults 最小业务线配置为空，禁止用户提币，userId:{}", reconCheckResultsParams.getUserId());
            reconBillUserVo.setPass(false);
            reconBillUserVo.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_BUSINESS_TYPE_CONFIG_FAIL);
            return reconBillUserVo;
        }
        BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_getMinCheckOkBillConfig.getName(), reconCheckResultsParams.getUserId(), null);

        Map<String, CompletableFuture<WithdrawCheckResultDTO>> asyncMap = new HashMap<>();

        if (globalBillConfig.isUserWithdrawalAlarmCheck()) {
            asyncMap.put("checkWithdrawLimit", CompletableFuture.supplyAsync(() -> checkWithdrawLimit(reconCheckResultsParams, globalBillConfig), baseAsyncTaskManager.getThreadPoolExecutor()));
        }
        if (globalBillConfig.isUserNegativeAssetsAlarmCheck()) {
            // 检查用户负值资产
            // 检查用户(实时)负值资产
            if (globalBillConfig.isUserRealTimeNegativeAssetsAlarmCheck()) {
                List<String> checkRealTimeNegativeAssetsSubSystemList = globalBillConfig.getCheckRealTimeNegativeAssetsSubSystemList();
                if (CollectionUtils.isNotEmpty(checkRealTimeNegativeAssetsSubSystemList)) {
                    Long userId = reconCheckResultsParams.getUserId();
                    for (String subSystem : checkRealTimeNegativeAssetsSubSystemList) {
                        asyncMap.put("negativeAssets:" + subSystem, CompletableFuture.supplyAsync(() -> {
                            WithdrawCheckResultDTO result = checkSingleRealTimeUserNegativeAssets(userId, subSystem, reconCheckResultsParams, reconCheckResultsParams.getRequestDate(), globalBillConfig);
                            log.info("CheckBillResultServiceImpl checkSingleRealTimeUserNegativeAssets outcome userId: {}, result: {} ,subSystem :{}", reconCheckResultsParams.getUserId(), result, subSystem);
                            return result;
                        }, baseAsyncTaskManager.getThreadPoolExecutor()));
                    }
                }
            } else {
                asyncMap.put("checkNonRealtimeNegativeAsset", CompletableFuture.supplyAsync(() -> {
                    WithdrawCheckResultDTO result = checkNonRealtimeNegativeAsset(reconCheckResultsParams, globalBillConfig);
                    log.info("CheckBillResultServiceImpl checkNonRealtimeNegativeAsset outcome userId: {}, result: {}", reconCheckResultsParams.getUserId(), result);
                    return result;

                }, baseAsyncTaskManager.getThreadPoolExecutor()));
            }
        }
        asyncMap.put("checkAccountAndBill", CompletableFuture.supplyAsync(() -> {
            WithdrawCheckResultDTO result = checkAccountAndBill(reconCheckResultsParams, globalBillConfig, minBillConfig);
            log.info("CheckBillResultServiceImpl checkAccountAndBill outcome userId: {}, result: {}", reconCheckResultsParams.getUserId(), result);
            return result;
        }, baseAsyncTaskManager.getThreadPoolExecutor()));

        if (globalBillConfig.isSelectCheckForTheResultsBackCalculation()) {
            for (String subSystem : globalBillConfig.getMinCheckOkTimeSubSystemList()) {
                asyncMap.put("checkBackCalculation" + subSystem, CompletableFuture.supplyAsync(() -> {
                    WithdrawCheckResultDTO result = checkBackCalculation(reconCheckResultsParams, subSystem);
                    log.info("CheckBillResultServiceImpl checkBackCalculation outcome userId: {}, result: {} ,subSystem :{}", reconCheckResultsParams.getUserId(), result, subSystem);
                    return result;
                }, baseAsyncTaskManager.getThreadPoolExecutor()));
            }
        }


        // 资金托管对账check
        if (globalBillConfig.isCheckHostingBalanceOpen()) {
            asyncMap.put("checkHostingBalance", CompletableFuture.supplyAsync(() -> {
                WithdrawCheckResultDTO result = checkHostingBalance(reconCheckResultsParams, globalBillConfig);
                log.info("CheckBillResultServiceImpl checkHostingBalance outcome userId: {}, result: {}", reconCheckResultsParams.getUserId(), result);
                return result;
            }, baseAsyncTaskManager.getThreadPoolExecutor()));
        }

        // OSL承兑账户单笔限额check
        if (!globalBillConfig.getOslFlashExConfig().isEmpty()) {
            asyncMap.put("checkTransactionLimit", CompletableFuture.supplyAsync(() -> {
                WithdrawCheckResultDTO result = checkTransactionLimit(reconCheckResultsParams, globalBillConfig);
                log.info("CheckBillResultServiceImpl checkTransactionLimit outcome userId: {}, result: {}", reconCheckResultsParams.getUserId(), result);
                return result;
            }, baseAsyncTaskManager.getThreadPoolExecutor()));
        }


        WithdrawCheckResultDTO asyncResult = AsyncUtils.fastFailAsyncResultV2(asyncMap);
        reconBillUserVo.setCheckOkTime(minBillConfig.getCheckOkTime().getTime());
        reconBillUserVo.setPass(asyncResult.isResult());
        reconBillUserVo.setWithdrawCheckResultEnum(asyncResult.getWithdrawCheckResultEnum());
        return reconBillUserVo;
    }

    private WithdrawCheckResultDTO checkTransactionLimit(ReconCheckResultsParams checkResultsParams, GlobalBillConfig globalBillConfig) {
        WithdrawCheckResultDTO withdrawCheckResultDTO = new WithdrawCheckResultDTO();
        Map<Long, BigDecimal> oslFlashExConfig = globalBillConfig.getOslFlashExConfig();
        Long userId = checkResultsParams.getUserId();
        Map<Integer, PriceVo> ratesMap = commonService.getCoinIdRatesMapCache(checkResultsParams.getRequestDate());
        for (Map.Entry<Long, BigDecimal> entry : oslFlashExConfig.entrySet()) {
            if (entry.getKey().equals(userId)) {
                BigDecimal amount = checkResultsParams.getAmount();
                Integer coinId = checkResultsParams.getCoinId();
                // 目前现货还在开发中，先兼容没传的情况，后续这里要抛异常
                if (amount == null || coinId == null) {
                    log.error("checkTransactionLimit params empty,amount:{}, coinId: {}", amount, coinId);
                    return withdrawCheckResultDTO;
                }
                // 汇率转换
                PriceVo priceVo = ratesMap.get(coinId);
                if (priceVo == null) {
                    log.error("checkTransactionLimit price not found coinId:{}", coinId);
                    throw new ApiException(BillExceptionEnum.RATE_NOT_EXISTS);
                }
                BigDecimal uAmount = amount.multiply(priceVo.getPrice());

                // 判断金额是否超限
                if (uAmount.compareTo(entry.getValue()) > 0) {
                    log.info("CheckBillResultServiceImpl checkTransactionLimit OSL承兑账户单笔限额超限，禁止用户提币，userId:{}, 提币额:{} (折U: {}, rate: {}), 限额:{}", checkResultsParams.getUserId(), amount, uAmount, priceVo.getPrice(), entry.getValue());
                    this.syncSendAlarmMessage(checkResultsParams, AlarmTemplateEnum.USER_TRANSACTION_LIMIT, userId);
                    withdrawCheckResultDTO.setResult(false);
                    withdrawCheckResultDTO.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECON_TRANSACTION_LIMIT_EXCEEDED);
                    return withdrawCheckResultDTO;
                }
            }
        }

        return withdrawCheckResultDTO;
    }


    private WithdrawCheckResultDTO checkHostingBalance(ReconCheckResultsParams checkResultsParams, GlobalBillConfig globalBillConfig) {
        WithdrawCheckResultDTO withdrawCheckResultDTO = new WithdrawCheckResultDTO();
        List<AssetBusinessTypeEnum> assetBusinessTypeEnumList = transToAssetBusinessTypeEnum(checkResultsParams.getBusinessSource());
        if (CollectionUtils.isEmpty(assetBusinessTypeEnumList)) {
            return withdrawCheckResultDTO;
        }
        for (AssetBusinessTypeEnum assetBusinessTypeEnum : assetBusinessTypeEnumList) {
            AssetCheckingConfig assetCheckingConfig = assetCheckingConfigService.getLastAssetCheckingConfig(assetBusinessTypeEnum.getBusinessType());
            if (assetCheckingConfig == null) {
                this.syncSendAlarmMessage(checkResultsParams, AlarmTemplateEnum.WITHDRAW_HOSTING_BALANCE_CONFIG_NULL, checkResultsParams.getUserId());
                log.info("CheckBillResultServiceImpl checkHostingBalance 资金托管配置为空，禁止用户提币，userId:{}", checkResultsParams.getUserId());
                withdrawCheckResultDTO.setResult(false);
                withdrawCheckResultDTO.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_HOSTING_BALANCE_CONFIG_NULL);
                return withdrawCheckResultDTO;
            }
            if (!assetCheckingConfig.getBillCapitalStatus().equals(BillCapitalStatusEnum.DONE.getStatusCode())) {
                List<WithdrawHostingBalanceConfig> withdrawHostingBalanceConfigs = globalBillConfig.getWithdrawHostingBalanceConfigs();
                Map<Integer, WithdrawHostingBalanceConfig> withdrawHostingBalanceConfigMap = withdrawHostingBalanceConfigs.stream().collect(Collectors.toMap(WithdrawHostingBalanceConfig::getBusinessType, Function.identity()));
                WithdrawHostingBalanceConfig withdrawHostingBalanceConfig = withdrawHostingBalanceConfigMap.getOrDefault(assetBusinessTypeEnum.getBusinessType(), new WithdrawHostingBalanceConfig());
                BigDecimal capitalValue = assetCheckingConfig.getBillCapitalValue();
                if (capitalValue.compareTo(withdrawHostingBalanceConfig.getMinValue()) < 0 || capitalValue.compareTo(withdrawHostingBalanceConfig.getMaxValue()) > 0) {
                    log.info("CheckBillResultServiceImpl checkHostingBalance 资金托管资金差异超过阈值，禁止用户提币，userId:{}，assetBusinessTypeEnum:{} ,capitalValue:{}, minValue:{}, maxValue:{}", checkResultsParams.getUserId(), assetBusinessTypeEnum.getBusinessCode(), capitalValue, withdrawHostingBalanceConfig.getMinValue(), withdrawHostingBalanceConfig.getMaxValue());
                    this.syncSendAlarmMessage(checkResultsParams, AlarmTemplateEnum.WITHDRAW_HOSTING_BALANCE_FAIL, checkResultsParams.getUserId(), assetBusinessTypeEnum.getBusinessCode(), capitalValue, withdrawHostingBalanceConfig.getMinValue(), withdrawHostingBalanceConfig.getMaxValue());
                    withdrawCheckResultDTO.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_HOSTING_BALANCE_FAIL);
                    withdrawCheckResultDTO.setResult(false);
                    return withdrawCheckResultDTO;
                }
            }
        }
        return withdrawCheckResultDTO;
    }


    public List<AssetBusinessTypeEnum> transToAssetBusinessTypeEnum(String businessSourceName) {
        if (StringUtils.isBlank(businessSourceName)) {
            return null;
        }
        List<AssetBusinessTypeEnum> assetBusinessTypeEnumList = new ArrayList<>();
        if (BusinessSourceEnum.COPPER.getCode().equals(businessSourceName)) {
            assetBusinessTypeEnumList.add(AssetBusinessTypeEnum.COPPER);
        } else if (BusinessSourceEnum.COBO.getCode().equals(businessSourceName)) {
            assetBusinessTypeEnumList.add(AssetBusinessTypeEnum.COBO);
        } else if (BusinessSourceEnum.OASIS.getCode().equals(businessSourceName)) {
            assetBusinessTypeEnumList.add(AssetBusinessTypeEnum.OASIS);
        }
        return assetBusinessTypeEnumList;
    }

    /**
     * 用户盈利检测灰度
     *
     * @param params
     * @param billConfig
     * @param timePeriod
     * @return
     */
    @Override
    public boolean checkProfitAccountByGray(ReconCheckResultsParams params, GlobalBillConfig billConfig, Integer timePeriod) {
        // 风控用户判断 如果是风控用户判断风控时间 小于1分钟返回失败 小于10分钟返回成功 否则重新走盈利检测
        GlobalBillConfig globalBillConfig= ReconciliationApolloConfigUtils.getGlobalBillConfig();
        if(globalBillConfig.isCheckProfitUserRiskCheckOpen()){
            String redisKey = String.format(BillRedisKeyEnum.RECON_WITHDRAWAL_PROFIT_RISK_RESULT.getKey(), params.getUserId());
            String redisCheckResult = (String) redisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotBlank(redisCheckResult)) {
                Pair pair = JSONObject.parseObject(redisCheckResult, Pair.class);
                long currentTimeMillis = System.currentTimeMillis();
                Long riskTime = Long.parseLong(pair.getValue().toString());
                if (currentTimeMillis - riskTime <= globalBillConfig.getCheckProfitRiskErrorTimeMs()) {
                    log.info("checkProfitAccountByGray risk false userId:{} gte period:{} redisCheckResult:{}", params.getUserId(), globalBillConfig.getCheckProfitRiskErrorTimeMs(), redisCheckResult);
                    return true;
                } else if (currentTimeMillis - riskTime <= globalBillConfig.getCheckProfitRiskSuccessTimeMs()) {
                    log.info("checkProfitAccountByGray risk true userId:{} gte period:{} redisCheckResult:{}", params.getUserId(), globalBillConfig.getCheckProfitRiskSuccessTimeMs(), redisCheckResult);
                    return false;
                } else {
                    log.info("checkProfitAccountByGray risk reProfitCheck userId:{} gte period:{} redisCheckResult:{}", params.getUserId(), redisCheckResult);
                }
            }
        }

        CheckProfitGrayService checkProfitGrayService = SpringUtil.getBean(billConfig.getCheckProfitGrayService());
        CheckUserProfitService checkUserProfitService = SpringUtil.getBean(billConfig.getCheckUserProfitService());
        boolean gray = false;
        if (Objects.nonNull(checkProfitGrayService) && Objects.nonNull(checkUserProfitService)) {
            gray = checkProfitGrayService.match(params, billConfig);
        }
        CheckUserProfitService redisCheckUserProfitService = SpringUtil.getBean(RedisCheckUserProfitServiceImpl.class);
        if (!(gray && (checkUserProfitService instanceof RedisCheckUserProfitServiceImpl))) {
            redisCheckUserProfitService.checkProfit(params, billConfig, timePeriod);
        }
        boolean result;
        if (gray) {
            result = checkUserProfitService.checkProfit(params, billConfig, timePeriod);
        } else {
            result = checkProfitAccount(params, billConfig, timePeriod);
        }
        return result;
    }

    @Override
    public boolean checkProfitAccount(ReconCheckResultsParams checkResultsParams, GlobalBillConfig globalBillConfig, Integer timePeriod) {
        Stopwatch startwatch = Stopwatch.createStarted();
        Stopwatch stopwatchRates = Stopwatch.createStarted();

        // 增加redis
        Long userId = checkResultsParams.getUserId();
        // 盈利白名单用户名单
        Set<Long> profitWhiteUsers = billWhiteListConfigService.getLongWhiteList(BillWhiteListTypeEnum.PROFIT_WHITE_USER.getCode());
        if (profitWhiteUsers.contains(userId)) {
            return false;
        }

        Map<Integer, ProfitAlarmDto> timeRangeProfitAlarmMap = globalBillConfig.getTimePeriodProfitAlarmMap();
        ProfitAlarmDto profitAlarmDto = timeRangeProfitAlarmMap.get(timePeriod);
        if (profitAlarmDto == null) {
            return false;
        }
        String redisKey = String.format(BillRedisKeyEnum.RECON_WITHDRAWAL_CHECK_PROFIT_RESULT.getKey(), userId, profitAlarmDto.getProfitAlarmAmount(), profitAlarmDto.getProfitRatio(), globalBillConfig.getTimePeriodProfitRedisTime());

        String redisCheckResult = (String) redisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotBlank(redisCheckResult)) {
            log.info("checkProfitAccount redisKey:{} redisCheckResult:{}", redisKey, redisCheckResult);
            if (profitWhiteUsers.contains(userId)) {
                return false;
            }
            return Boolean.parseBoolean(redisCheckResult);
        }

        Long requestTime = checkResultsParams.getRequestDate();
        Date requestDate = new Date(requestTime);
        Date beginDate = DateUtil.addMinute(requestDate, BillConstants.NEG_ONE * timePeriod);

        //1、查询用户期初资产
        Map<Integer, PriceVo> ratesMap = commonService.getCoinIdRatesMapCache(beginDate.getTime());
        Map<Integer, String> allCoinsMap = commonService.getAllCoinsMapCache(beginDate.getTime());
        List<String> checkProfitSubSystemList = globalBillConfig.getCheckProfitSubSystemList();
        log.info("checkProfitAccount queryRates requestTime:{}, userId:{}, timePeriod:{}, time:{}", requestTime, userId, timePeriod, stopwatchRates.stop());

        // 获取子账号 盈利
        Map<Long, AccountProfitDTO> userAccountProfitMap = new ConcurrentHashMap<>();
        userAccountProfitMap.putAll(getUserAccountProfitMap(userId, Lists.newArrayList(userId), allCoinsMap, ratesMap, checkProfitSubSystemList, timePeriod, beginDate, requestDate));
        if (globalBillConfig.isCheckProfitAccountChildAccount()) {
            org.apache.commons.lang3.tuple.Pair <Boolean, List<Long>> pair = userQueryService.getChildListByParentId(userId, globalBillConfig.getChildAccountTypeList(), globalBillConfig.getMaxChildAccountCount(), globalBillConfig.getMaxChildAccountCount());
            if (pair.getLeft()) {
                if (CollectionUtils.isNotEmpty(pair.getRight())) {
                    log.info("checkProfitAccount getChildListByParentId parentUserId:{} child size:{}", userId, pair.getRight().size());
                    userAccountProfitMap.putAll(getUserAccountProfitMap(userId, pair.getRight(), allCoinsMap, ratesMap, checkProfitSubSystemList, timePeriod, beginDate, requestDate));
                }
            } else {
                log.info("checkProfitAccount userId:{} childAccount exceeds:{}", userId, globalBillConfig.getMaxChildAccountCount());
            }
        }
        BigDecimal userAllProfitAmount = userAccountProfitMap.values().stream().map(AccountProfitDTO::getProfitAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal userAllBeginAmount = userAccountProfitMap.values().stream().map(AccountProfitDTO::getBeginBalance).reduce(BigDecimal.ZERO, BigDecimal::add);

        boolean checkResult = checkProfitAmount(userAllProfitAmount, userAllBeginAmount, profitAlarmDto);
        // 全步骤日志打印
        log.info("checkProfitAccount checkProfitAmount businessSource:{}, profitAmount:{}, beginAmount:{}, requestTime:{}, userId:{}, timePeriod:{}, " +
                        "checkResult:{}, allTime:{}, 查rates时间:{} requestTimeStr:{} allUserProfitInfo:{}",
                checkResultsParams.getBusinessSource(), userAllProfitAmount, userAllBeginAmount, requestTime, userId, timePeriod, checkResult,
                startwatch.stop(), stopwatchRates, DateUtil.getDefaultDateStr(new Date(requestTime)), JSON.toJSONString(userAccountProfitMap));
        boolean returnResult = checkResult;
        if (checkResult) {
            if (globalBillConfig.isCheckProfitAlarmOpen()) {
                this.syncSendAlarmMessage(checkResultsParams, AlarmTemplateEnum.CAPITAL_BILL_PROFIT_WITHDRAW_DETAIL, checkResultsParams.getUserId(), userAllProfitAmount, userAllBeginAmount);
                redisTemplate.opsForList().rightPush(String.format(BillRedisKeyEnum.RECON_WITHDRAWAL_PROFIT_RISK_USER_ALARM.getKey()), JSON.toJSONString(Map.of("userId", userId, "requestDate", requestTime)));
                boolean sendAbnormalProfitRiskV2Result = profitAbnormalService.sendAbnormalProfitRiskV2(requestTime, checkResultsParams.getUserId(), userAccountProfitMap);
                returnResult = !sendAbnormalProfitRiskV2Result;
            }
            // 开启风控推送，走用户放空策略 不走老策略
            if(!globalBillConfig.isCheckProfitUserRiskCheckOpen()){
                redisTemplate.opsForValue().set(redisKey, String.valueOf(returnResult), globalBillConfig.getTimePeriodProfitRedisTime(), TimeUnit.HOURS);
            }
        }
        // 入库
        saveProfit(checkResultsParams, userAccountProfitMap, globalBillConfig, beginDate);
        return returnResult;
    }

    /**
     * 入库
     *
     * @param checkResultsParams
     * @param userAccountProfitMap
     * @param billConfig
     */
    private void saveProfit(ReconCheckResultsParams checkResultsParams, Map<Long, AccountProfitDTO> userAccountProfitMap, GlobalBillConfig billConfig, Date beginDate) {
        if (CollectionUtil.isEmpty(userAccountProfitMap)) {
            return;
        }
        if (!billConfig.isSaveProfitRecord()) {
            return;
        }
        Long parentUserId = checkResultsParams.getUserId();
        try {
            Date date = new Date();
            Long requestDate = checkResultsParams.getRequestDate();
            Map<Integer, PriceVo> rates = commonService.getCoinIdTradePriceMap(requestDate);
            List<BillUserWithdrawProfitRecord> profitRecordList = new ArrayList<>();
            BigDecimal userAllProfitAmount = userAccountProfitMap.values().stream().map(AccountProfitDTO::getProfitAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            for (Map.Entry<Long, AccountProfitDTO> profitEntry : userAccountProfitMap.entrySet()) {
                for (Map.Entry<Integer, BigDecimal> profitCoinEntry : profitEntry.getValue().getProfitCoinMap().entrySet()) {
                    BillUserWithdrawProfitRecord profitRecord = new BillUserWithdrawProfitRecord();
                    profitRecord.setParentUserId(parentUserId);
                    // 母账户总盈利
                    if (Objects.equals(parentUserId, profitEntry.getKey())) {
                        profitRecord.setReconProfitTotalAmount(userAllProfitAmount);
                    } else {
                        profitRecord.setUserId(profitEntry.getKey());
                    }
                    profitRecord.setCoinId(profitCoinEntry.getKey());
                    profitRecord.setRequestId(checkResultsParams.getTraceId());
                    profitRecord.setProfitCount(profitCoinEntry.getValue());
                    BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(profitCoinEntry.getKey(), rates);
                    profitRecord.setProfitUsdtAmount(profitCoinEntry.getValue().multiply(rate));
                    profitRecord.setProfitSource(checkResultsParams.getBusinessSource().equals(PROFIT_BAKCHECK_FROM_XXLJOB) ? ProfitSourceEnum.BACKTEST.getCode() : ProfitSourceEnum.BUSINESS.getCode());
                    profitRecord.setOrderId(checkResultsParams.getOrderId());
                    profitRecord.setRecordCode(checkResultsParams.getRecordCode());
                    profitRecord.setBusinessSource(checkResultsParams.getBusinessSource());
                    profitRecord.setRequestTime(new Date(requestDate));
                    profitRecord.setCheckBeginTime(beginDate);
                    profitRecord.setCheckEndTime(new Date(requestDate));
                    profitRecord.setCheckOkTime(date);
                    profitRecord.setCreateTime(date);
                    profitRecord.setUpdateTime(date);
                    profitRecordList.add(profitRecord);
                }
            }
            List<List<BillUserWithdrawProfitRecord>> billUserWithdrawProfitRecordList = Lists.partition(profitRecordList, billConfig.getInsertSqlSize());
            assetUserProfitTaskManager.forEachSubmitBatchAndWait(billUserWithdrawProfitRecordList, (List<BillUserWithdrawProfitRecord> subRecords) -> billUserWithdrawProfitRecordService.batchInsert(subRecords), BillConstants.TEN);
        } catch (Exception e) {
            log.error("saveProfit checkResultsParams:{} error:{}", checkResultsParams, e);
        }
    }

    @Override
    public Pair<Boolean, Map<Integer, BigDecimal>> checkProfitAccountForTime(ReconCheckResultsParams checkResultsParams, GlobalBillConfig globalBillConfig, Integer timePeriod) {
        Stopwatch startwatch = Stopwatch.createStarted();
        Long userId = checkResultsParams.getUserId();
        Map<Integer, ProfitAlarmDto> timeRangeProfitAlarmMap = globalBillConfig.getTimePeriodProfitAlarmMap();
        ProfitAlarmDto profitAlarmDto = timeRangeProfitAlarmMap.get(timePeriod);

        // 查询流水和期初资产
        Map<Integer, BigDecimal> allFlowChangeSum = new ConcurrentHashMap<>();
        Map<Integer, BigDecimal> inOutFlowChangeSum = new ConcurrentHashMap<>();
        Long requestTime = checkResultsParams.getRequestDate();
        Date requestDate = new Date(requestTime);
        Date beginDate = com.upex.utils.util.DateUtil.addMinute(requestDate, BillConstants.NEG_ONE * timePeriod);
        beginDate = DateUtil.getLastFiveMin(beginDate);

        //1、查询用户期初资产
        Map<Integer, PriceVo> ratesMap = commonService.getCoinIdRatesMapCache(beginDate.getTime());
        Map<Integer, String> allCoinsMap = commonService.getAllCoinsMapCache(beginDate.getTime());
        List<String> checkProfitSubSystemList = globalBillConfig.getCheckProfitSubSystemList();
        Stopwatch stopwatch1 = Stopwatch.createStarted();
        ReconTotalAssetsDetailVo userBeginAssets = reconUserAssetsSnapShotService.listUserAssetsBySnapShotTime(userId, beginDate.getTime(), allCoinsMap, ratesMap, checkProfitSubSystemList, null, Boolean.FALSE);
        log.info("checkProfitAccountXxl checkProfitAccountForTime userAssetsSnapShotService.listUserAssetsBySnapShotTime requestTime:{}, userId:{}, timePeriod:{}, userBeginAssets:{} time:{}", requestTime, userId, timePeriod, JSON.toJSONString(userBeginAssets), stopwatch1.stop());

        //2、查询用户对账流水
        Map<Long, AccountProfitDTO> userAccountProfitMap = new ConcurrentHashMap<>();
        userAccountProfitMap.putAll(getUserAccountProfitMap(userId, Lists.newArrayList(userId), allCoinsMap, ratesMap, checkProfitSubSystemList, timePeriod, beginDate, requestDate));
        if (globalBillConfig.isCheckProfitAccountChildAccount()) {
            org.apache.commons.lang3.tuple.Pair <Boolean, List<Long>> pair = userQueryService.getChildListByParentId(userId, globalBillConfig.getChildAccountTypeList(), globalBillConfig.getMaxChildAccountCount(), globalBillConfig.getMaxChildAccountCount());
            if (pair.getLeft()) {
                if (CollectionUtils.isNotEmpty(pair.getRight())) {
                    log.info("checkProfitAccount getChildListByParentId parentUserId:{} child size:{}", userId, pair.getRight().size());
                    userAccountProfitMap.putAll(getUserAccountProfitMap(userId, pair.getRight(), allCoinsMap, ratesMap, checkProfitSubSystemList, timePeriod, beginDate, requestDate));
                }
            } else {
                log.info("checkProfitAccount userId:{} childAccount exceeds:{}", userId, globalBillConfig.getMaxChildAccountCount());
            }
        }
        BigDecimal userAllProfitAmount = userAccountProfitMap.values().stream().map(AccountProfitDTO::getProfitAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal userAllBeginAmount = userAccountProfitMap.values().stream().map(AccountProfitDTO::getBeginBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        boolean checkResult = checkProfitAmount(userAllProfitAmount, userAllBeginAmount, profitAlarmDto);
        log.info("checkProfitAccountXxl checkProfitAccountForTime checkProfitAccount checkProfitAmount userId:{} requestDate:{} timePeriod:{} userAllProfitAmount:{} userAllBeginAmount:{} checkResult:{} userAccountProfitMap:{} time:{}", userId, requestDate, timePeriod, userAllProfitAmount, userAllBeginAmount, checkResult, JSON.toJSONString(userAccountProfitMap), startwatch.stop());

        // 验证redis方式
        Stopwatch redisStartwatch = Stopwatch.createStarted();
        Map<Long, AccountProfitDTO> redisUserAccountProfitMap = new ConcurrentHashMap<>();
        RedisCheckUserProfitServiceImpl redisCheckUserProfitService = SpringUtil.getBean(RedisCheckUserProfitServiceImpl.class);
        redisUserAccountProfitMap.put(userId, redisCheckUserProfitService.getUserProfitAmount(checkResultsParams, globalBillConfig, timePeriod, allCoinsMap, ratesMap));
        // 获取子账号盈利
        if (globalBillConfig.isCheckProfitAccountChildAccount()) {
            org.apache.commons.lang3.tuple.Pair <Boolean, List<Long>> pair = userQueryService.getChildListByParentId(userId, globalBillConfig.getChildAccountTypeList(), globalBillConfig.getChildAccountPageSize(), globalBillConfig.getMaxChildProfitAccountCount());
            if (pair.getLeft()) {
                if (CollectionUtils.isNotEmpty(pair.getRight())) {
                    log.info("AbstractCheckUserProfitService checkProfitAccount getChildListByParentId parentUserId:{} child size:{}", userId, pair.getRight().size());
                    redisUserAccountProfitMap.putAll(redisCheckUserProfitService.getUserChildProfitAmount(checkResultsParams, pair.getRight(), globalBillConfig, timePeriod, allCoinsMap, ratesMap));
                }
            } else {
                log.info("AbstractCheckUserProfitService checkProfitAccount userId:{} childAccount exceeds:{}", userId, globalBillConfig.getMaxChildAccountCount());
            }
        }
        BigDecimal redisUserAllProfitAmount = redisUserAccountProfitMap.values().stream().map(AccountProfitDTO::getProfitAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal redisUserAllBeginAmount = redisUserAccountProfitMap.values().stream().map(AccountProfitDTO::getBeginBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        boolean redisCheckResult = checkProfitAmount(redisUserAllProfitAmount, redisUserAllBeginAmount, profitAlarmDto);
        log.info("checkProfitAccountXxl checkProfitAccountForTime checkProfitAccount redis checkProfitAmount userId:{} requestDate:{} timePeriod:{} userAllProfitAmount:{} userAllBeginAmount:{} checkResult:{} userAccountProfitMap:{} time:{}", userId, requestDate, timePeriod, redisUserAllProfitAmount, redisUserAllBeginAmount, redisCheckResult, JSON.toJSONString(redisUserAccountProfitMap), redisStartwatch.stop());
        return Pair.of(checkResult, allFlowChangeSum);
    }

    public AccountProfitDTO listUserBillFlowsV2(Long userId, Date beginDate, Date requestDate, Map<Integer, BigDecimal> allFlowChangeSum, Map<Integer, BigDecimal> allInOutFlowChangeSum, List<String> checkProfitSubSystemList) {
        Stopwatch startWatch = Stopwatch.createStarted();
        // 业务线并行计算队列
        Map<Byte, AccountProfitDTO> totalAccountProfitMap = new ConcurrentHashMap<>();
        Map<Integer, PriceVo> rates = commonService.getCoinIdTradePriceMap(requestDate.getTime());
        TaskVoidBatchResult queryResultIsEmpty = taskManager.forEachSubmitBatchAndWait(checkProfitSubSystemList, (String accountTypeDesc) -> {
            Stopwatch stopwatch = Stopwatch.createStarted();
            String[] systemSplit = accountTypeDesc.split(SEPARATOR);
            byte accountType = Byte.parseByte(systemSplit[0]);
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
            ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
            BillAllConfig billAllConfig = billAllConfigService.selectByTypeAndParam(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
            Date endDate = billAllConfig.getCheckOkTime();
            BillCoinUserProperty billCoinUserPropertyToCheckExist = billCoinUserPropertyService.selectBySingleUserIdLatest(userId, accountTypeEnum.getAccountParam(), Integer.valueOf(accountTypeEnum.getCode()));
            Map<Integer, BigDecimal> flowChangeSumMap = new HashMap<>();
            Map<Integer, BigDecimal> inOutFlowChangeSumMap = new HashMap<>();
            if (billCoinUserPropertyToCheckExist == null) {
                log.info("CheckBillResultServiceImpl.listUserBillFlows userId:{} beginDate:{} endDate:{} accountTypeDesc:{}, time:{}", userId, DateUtil.getFormatDate(beginDate.getTime()), DateUtil.getFormatDate(requestDate.getTime()), accountTypeDesc, stopwatch.stop());
                return;
            }

            if (beginDate.compareTo(endDate) >= 0) {
                // 如果对账延迟，指查询增量流水
                listUserIncrFlowsByAccountType(userId, beginDate, requestDate, flowChangeSumMap, inOutFlowChangeSumMap, accountTypeEnum);
                log.info("CheckBillResultServiceImpl.listUserBillFlows beginDate <= endDate userId:{} beginDate:{} endDate:{} accountTypeDesc:{}, time:{}", userId, DateUtil.getFormatDate(beginDate.getTime()), DateUtil.getFormatDate(requestDate.getTime()), accountTypeDesc, stopwatch.stop());
            }else{
                log.info("CheckBillResultServiceImpl.listUserBillFlows userId:{} beginDate:{} endDate:{} accountTypeDesc:{}, time:{}", userId, DateUtil.getFormatDate(beginDate.getTime()), DateUtil.getFormatDate(endDate.getTime()), accountTypeDesc, stopwatch.stop());
                // 获取原始数据
                listUserHistoryBillsByAccountType(userId, beginDate, accountTypeDesc, accountTypeEnum, apolloBillConfig, endDate, flowChangeSumMap, inOutFlowChangeSumMap);
                // 先查增量流水
                listUserIncrFlowsByAccountType(userId, endDate, requestDate, flowChangeSumMap, inOutFlowChangeSumMap, accountTypeEnum);
            }
            if(MapUtils.isEmpty(flowChangeSumMap) && MapUtils.isEmpty(inOutFlowChangeSumMap)){
                return;
            }

            // 合并计算入出盈利和总盈利
            AccountProfitDTO accountProfitDTO = new AccountProfitDTO();
            flowChangeSumMap.forEach((coinId, flowChangeSum) -> {
                allFlowChangeSum.compute(coinId, (k, oldValue) -> oldValue == null ? flowChangeSum : oldValue.add(flowChangeSum));
                accountProfitDTO.getProfitCoinMap().compute(coinId, (k, oldValue)->oldValue == null ? flowChangeSum : oldValue.add(flowChangeSum));
            });
            inOutFlowChangeSumMap.forEach((coinId, flowChangeSum)->{
                allInOutFlowChangeSum.compute(coinId, (k, oldValue) -> oldValue == null ? flowChangeSum : oldValue.add(flowChangeSum));
                accountProfitDTO.getProfitCoinMap().compute(coinId, (k, oldValue)->oldValue == null ? flowChangeSum.negate() : oldValue.add(flowChangeSum.negate()));
            });

            BigDecimal profitAmount = BigDecimal.ZERO;
            for (Map.Entry<Integer, BigDecimal> profitCoin : accountProfitDTO.getProfitCoinMap().entrySet()) {
                BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(profitCoin.getKey(), rates);
                profitAmount = profitAmount.add(profitCoin.getValue().multiply(rate));
            }
            accountProfitDTO.setProfitAmount(profitAmount);
            totalAccountProfitMap.put(accountType, accountProfitDTO);
        }, checkProfitSubSystemList.size());
        if (queryResultIsEmpty != null && queryResultIsEmpty.getFails().size() > 0) {
            log.error("billCoinTypeUserPropertyMapper.selectUserTypeByTime error size={}", queryResultIsEmpty.getFails().size());
           throw new ApiException(BillExceptionEnum.SYSTEM_ERROR, "billCoinTypeUserPropertyMapper.selectUserTypeByTime error");
        }
        // 汇总所有业务线盈亏
        AccountProfitDTO totalAccountProfit = new AccountProfitDTO();
        totalAccountProfit.setProfitAmount(totalAccountProfitMap.values().stream().map(AccountProfitDTO::getProfitAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        Map<Integer, BigDecimal> totalCoinProfitMap = new HashMap<>();
        totalAccountProfitMap.values().stream().forEach(accountProfit -> {
            accountProfit.getProfitCoinMap().forEach((coinId, profitAcmount) -> {
                totalCoinProfitMap.compute(coinId, (k, oldValue) -> oldValue == null ? profitAcmount : oldValue.add(profitAcmount));
            });
        });
        totalAccountProfit.setProfitCoinMap(totalCoinProfitMap);
        totalAccountProfit.setAccountProfitMap(totalAccountProfitMap);

        log.info("checkProfitAccountXxl checkProfitAccountForTime requestDate:{} userId:{} flowChangeSumMapQueue:{} inOutFlowChangeSumMapQueue:{} totalAccountProfit:{} time:{}", requestDate.getTime(), userId, JSONObject.toJSONString(allFlowChangeSum), JSONObject.toJSONString(allInOutFlowChangeSum), JSON.toJSONString(totalAccountProfit), startWatch.stop());
        return totalAccountProfit;
    }

    private void listUserHistoryBillsByAccountType(Long userId, Date beginDate, String accountTypeDesc, AccountTypeEnum accountTypeEnum, ApolloReconciliationBizConfig apolloBillConfig, Date endDate, Map<Integer, BigDecimal> flowChangeSumMap, Map<Integer, BigDecimal> inOutFlowChangeSumMap) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<BillCoinTypeUserProperty> billCoinTypeUserPropertieList = reconUserAssetsSnapShotService.getTimeDifferenceOnChange(userId, beginDate.getTime(), endDate.getTime(), Integer.valueOf(accountTypeEnum.getCode()), accountTypeEnum.getAccountParam());
        if (CollectionUtils.isEmpty(billCoinTypeUserPropertieList)) {
            log.info("billCoinTypeUserPropertyMapper.selectUserTypeByTime billCoinTypeUserPropertyList is empty userId:{} beginDate:{} endDate:{} accountTypeDesc:{} 无数据", userId, DateUtil.getFormatDate(beginDate.getTime()), DateUtil.getFormatDate(endDate.getTime()), accountTypeDesc);
            return;
        }
        Set<String> inOutFlowTypes = apolloBillConfig.getCheckProfitInOutBizType();

        // 数据合并计算
        for (BillCoinTypeUserProperty billCoinTypeUserProperty : billCoinTypeUserPropertieList) {
            try {
                if (CollectionUtils.isNotEmpty(inOutFlowTypes) && inOutFlowTypes.contains(billCoinTypeUserProperty.getBizType())) {
                    // 如果key不存在，put asset
                    inOutFlowChangeSumMap.computeIfAbsent(billCoinTypeUserProperty.getCoinId(), v -> BigDecimal.ZERO);
                    // 如果key存在，all + asset
                    inOutFlowChangeSumMap.computeIfPresent(billCoinTypeUserProperty.getCoinId(), (key, oldValue) -> oldValue.add(billCoinTypeUserProperty.getChangePropSum(accountTypeEnum.getCode())));
                }
            } catch (Exception e) {
                log.error("查询增量流水异常，accountTypeEnum:{}, userId:{}, billEndDate:{}, requestDate:{}, inOutFlowTypes:{}, billCoinTypeUserProperty:{}",
                        accountTypeEnum, userId, beginDate, endDate, JSONObject.toJSONString(inOutFlowTypes), JSONObject.toJSONString(billCoinTypeUserProperty));
                throw e;
            }
            // 如果key不存在，put asset
            flowChangeSumMap.computeIfAbsent(billCoinTypeUserProperty.getCoinId(), v -> BigDecimal.ZERO);
            // 如果key存在，all + asset
            flowChangeSumMap.computeIfPresent(billCoinTypeUserProperty.getCoinId(), (key, oldValue) -> oldValue.add(billCoinTypeUserProperty.getChangePropSum(accountTypeEnum.getCode())));
        }
        log.info("billCoinTypeUserPropertyMapper.selectUserTypeByTime prop sum userId:{} beginDate:{} endDate:{} accountTypeDesc:{} flowChangeSumMap:{} inOutFlowChangeSumMap:{} time:{}", userId, DateUtil.getFormatDate(beginDate.getTime()), DateUtil.getFormatDate(endDate.getTime()), accountTypeDesc, JSON.toJSONString(flowChangeSumMap), JSON.toJSONString(inOutFlowChangeSumMap), stopwatch.stop());
        log.info("checkProfitAccountXxl checkProfitAccountForTime billCoinTypeUserPropertyMapper.selectUserTypeByTime prop sum userId:{} beginDate:{} endDate:{} accountTypeDesc:{} allFlowChangeList:{} inOutFlowChangeList:{} profitFlowChangeList:{}",
                userId, DateUtil.getFormatDate(beginDate.getTime()), DateUtil.getFormatDate(endDate.getTime()), accountTypeDesc, JSONObject.toJSONString(convertProfitLogList(endDate, accountTypeEnum, billCoinTypeUserPropertieList.stream().filter(e -> !e.isChangePropZero()).collect(Collectors.toList()))),
                JSONObject.toJSONString(convertProfitLogList(endDate, accountTypeEnum, billCoinTypeUserPropertieList.stream().filter(e -> !e.isChangePropZero() && inOutFlowTypes.contains(e.getBizType())).collect(Collectors.toList()))),
                JSONObject.toJSONString(convertProfitLogList(endDate, accountTypeEnum, billCoinTypeUserPropertieList.stream().filter(e -> !e.isChangePropZero() && !inOutFlowTypes.contains(e.getBizType())).collect(Collectors.toList()))));
    }

    private Map<Integer, List<ProfitLogDTO>> convertProfitLogList(Date requestDate, AccountTypeEnum accountTypeEnum, List<BillCoinTypeUserProperty> profitLogList) {
        if (CollectionUtils.isEmpty(profitLogList)) {
            return new HashMap<>();
        }

        Map<Integer, PriceVo> rates = commonService.getCoinIdTradePriceMap(requestDate.getTime());

        return profitLogList.stream()
                .map(coinTypeUserProperty -> getProfitLogDTO(accountTypeEnum, rates, coinTypeUserProperty))
                .sorted(Comparator.comparing(ProfitLogDTO::getUAmount).reversed())
                .collect(Collectors.groupingBy(ProfitLogDTO::getCoinId));
    }

    private ProfitLogDTO getProfitLogDTO(AccountTypeEnum accountTypeEnum, Map<Integer, PriceVo> rates, BillCoinTypeUserProperty coinTypeUserProperty) {
        ProfitLogDTO profitLogDTO = new ProfitLogDTO();
        profitLogDTO.setUserId(coinTypeUserProperty.getUserId());
        profitLogDTO.setBizType(coinTypeUserProperty.getBizType());
        Integer coinId = coinTypeUserProperty.getCoinId();
        profitLogDTO.setCoinId(coinId);
        profitLogDTO.setCount(coinTypeUserProperty.getChangePropSum(accountTypeEnum.getCode()));
        BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(coinId, rates);
        profitLogDTO.setRate(rate);
        profitLogDTO.setUAmount(profitLogDTO.calculateUAmount());
        profitLogDTO.setAccountType(accountTypeEnum.getCode());

        return profitLogDTO;
    }


    public List<BillCoinTypeUserProperty> getBillCoinTypeUserProperties(Long userId, Date beginDate, Date endDate, AccountTypeEnum accountTypeEnum, Integer lastTableBackwardDays) {
        // todo 老bill逻辑，有任何变动，这一期这个人的全量数据做存储    内存对账，仅增量部分
        List<BillCoinTypeUserProperty> beginBillCoinTypeUserPropertieList = selectByUserBeforeTime(userId, Integer.valueOf(accountTypeEnum.getCode()), accountTypeEnum.getAccountParam(), beginDate, lastTableBackwardDays);
        List<BillCoinTypeUserProperty> endBillCoinTypeUserPropertieList = selectByUserBeforeTime(userId, Integer.valueOf(accountTypeEnum.getCode()), accountTypeEnum.getAccountParam(), endDate, lastTableBackwardDays);
        if (CollectionUtils.isEmpty(beginBillCoinTypeUserPropertieList) && CollectionUtils.isEmpty(endBillCoinTypeUserPropertieList)) {
            return new ArrayList<>();
        }

        Set<String> beginCoinTypeSet = beginBillCoinTypeUserPropertieList.stream().map(ctu -> GroupByKeyUtil.groupByCoinIdAndTypeId(ctu.getCoinId(), ctu.getBizType())).collect(Collectors.toSet());
        Set<String> endCoinTypeSet = endBillCoinTypeUserPropertieList.stream().map(ctu -> GroupByKeyUtil.groupByCoinIdAndTypeId(ctu.getCoinId(), ctu.getBizType())).collect(Collectors.toSet());
        beginCoinTypeSet.addAll(endCoinTypeSet);

        Map<String, BillCoinTypeUserProperty> beginBillCoinTypeUserPropertieMap = beginBillCoinTypeUserPropertieList.stream().collect(Collectors.toMap(ctu -> GroupByKeyUtil.groupByCoinIdAndTypeId(ctu.getCoinId(), ctu.getBizType()), Function.identity()));
        Map<String, BillCoinTypeUserProperty> endBillCoinTypeUserPropertieMap = endBillCoinTypeUserPropertieList.stream().collect(Collectors.toMap(ctu -> GroupByKeyUtil.groupByCoinIdAndTypeId(ctu.getCoinId(), ctu.getBizType()), Function.identity()));
        BillCoinTypeUserProperty defaultProp = new BillCoinTypeUserProperty();
        return beginCoinTypeSet.stream().map(coinType -> {
                    BillCoinTypeUserProperty result = new BillCoinTypeUserProperty();
                    result.setUserId(userId);
                    String[] split = coinType.split(BillConstants.POUND_SIGN);
                    result.setCoinId(Integer.valueOf(split[0]));
                    result.setBizType(split[1]);

                    BillCoinTypeUserProperty endProp = endBillCoinTypeUserPropertieMap.getOrDefault(coinType, defaultProp);
                    BillCoinTypeUserProperty startProp = beginBillCoinTypeUserPropertieMap.getOrDefault(coinType, defaultProp);

                    result.setChangeProp1(endProp.getProp1().subtract(startProp.getProp1()));
                    result.setChangeProp2(endProp.getProp2().subtract(startProp.getProp2()));
                    result.setChangeProp3(endProp.getProp3().subtract(startProp.getProp3()));
                    result.setChangeProp4(endProp.getProp4().subtract(startProp.getProp4()));
                    result.setChangeProp5(endProp.getProp5().subtract(startProp.getProp5()));

                    return result;
                }).filter(BillCoinTypeUserProperty::checkAnyNotZero)
                .collect(Collectors.toList());
    }


    private List<BillCoinTypeUserProperty> selectByUserBeforeTime(Long userId, Integer accountType, String accountParam, Date removDate, Integer maxBackDays) {
        Date removDate1 = removDate;
        Integer executeNumber = maxBackDays;
        List<BillCoinTypeUserProperty> moveList;
        do {
            moveList = billCoinTypeUserPropertyService.selectByUserBeforeTime(userId, removDate1, accountType);
            if (CollectionUtils.isNotEmpty(moveList)) {
                break;
            }
            removDate1 = DateUtil.getStartOfDay(removDate1);
        } while (--executeNumber > BillConstants.ZERO);
        return moveList;
    }

    @Override
    public void listUserIncrFlowsByAccountType(Long userId, Date billEndDate, Date requestDate, Map<Integer, BigDecimal> flowChangeSumMap, Map<Integer, BigDecimal> inOutFlowChangeSumMap, AccountTypeEnum accountTypeEnum) {
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        Boolean ifSelectTimeSlice=ifSelectTimeSlice(billEndDate,requestDate,accountTypeEnum,userId);
        if(ifSelectTimeSlice){
            TimeSliceCalcUtils.slice(billEndDate, requestDate, globalBillConfig.getIncrProfitCheckTimeMinuteInterval(), (startDate, endDate) -> {
                getUserIncrFlowsByAccountType(userId, startDate, endDate, flowChangeSumMap, inOutFlowChangeSumMap, accountTypeEnum);
            });
        }else{
            getUserIncrFlowsByAccountType(userId, billEndDate, requestDate, flowChangeSumMap, inOutFlowChangeSumMap, accountTypeEnum);
        }
    }

    boolean ifSelectTimeSlice(Date billEndDate, Date requestDate,AccountTypeEnum accountTypeEnum,Long userId){
        Boolean  selectTimeSlice=false;
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        if(globalBillConfig.getIncrProfitCheckTimeMinuteInterval()>0&&(requestDate.getTime()-billEndDate.getTime())>BillConstants.ONE_HOUR_MIL_SEC) {
            selectTimeSlice=true;
            List<Integer> incrProfitCheckTimeSliceBlackAccountTypeList=globalBillConfig.getIncrProfitCheckTimeSliceBlackAccountTypeList();
            List<Long> incrProfitCheckTimeSliceBlackUserList=globalBillConfig.getIncrProfitCheckTimeSliceBlackUserList();
            if(CollectionUtils.isNotEmpty(incrProfitCheckTimeSliceBlackUserList)&&incrProfitCheckTimeSliceBlackUserList.contains(userId)){
                selectTimeSlice=false;
            }
            if(CollectionUtils.isNotEmpty(incrProfitCheckTimeSliceBlackAccountTypeList)&&incrProfitCheckTimeSliceBlackAccountTypeList.contains(Integer.valueOf(accountTypeEnum.getCode()))){
                selectTimeSlice=false;
            }
        }
        log.info("listUserIncrFlowTimeSliceSelect userId:{} billEndDate:{} requestDate:{} accountTypeDesc:{} selectTimeSlice:{}", userId, DateUtil.getFormatDate(billEndDate.getTime()), DateUtil.getFormatDate(requestDate.getTime()), accountTypeEnum,selectTimeSlice);
        return selectTimeSlice;
    }

    public void getUserIncrFlowsByAccountType(Long userId, Date startDate, Date endDate, Map<Integer, BigDecimal> flowChangeSumMap, Map<Integer, BigDecimal> inOutFlowChangeSumMap, AccountTypeEnum accountTypeEnum) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        if (startDate != null && endDate != null && startDate.compareTo(endDate) >= 0) {
            log.info("listUserIncrFlowsByAccountType userId:{} startDate:{} endDate:{} accountTypeDesc:{} 无增量流水", userId, DateUtil.getFormatDate(startDate.getTime()), DateUtil.getFormatDate(endDate.getTime()), accountTypeEnum);
            return;
        }
        List<AccountAssetsInfoResult> resultList = realTimeFlowService.queryIncrFlows(userId, startDate, endDate, accountTypeEnum);
        if (CollectionUtils.isEmpty(resultList)) {
            log.info("listUserIncrFlowsByAccountType resultList is empty userId:{} startDate:{} endDate:{} accountTypeDesc:{} 无增量流水", userId, DateUtil.getFormatDate(startDate.getTime()), DateUtil.getFormatDate(endDate.getTime()), accountTypeEnum);
            return;
        }
        ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        Set<String> inOutFlowTypes = apolloBillConfig.getCheckProfitInOutBizType();
        for (AccountAssetsInfoResult assetsInfoResult : resultList) {
            try {
                if (CollectionUtils.isNotEmpty(inOutFlowTypes) && inOutFlowTypes.contains(assetsInfoResult.getBizType())) {
                    // 如果key不存在，put asset
                    inOutFlowChangeSumMap.computeIfAbsent(assetsInfoResult.getCoinId(), v -> BigDecimal.ZERO);
                    // 如果key存在，all + asset
                    inOutFlowChangeSumMap.computeIfPresent(assetsInfoResult.getCoinId(), (key, oldValue) -> oldValue.add(assetsInfoResult.getProp1()));
                }
            } catch (Exception e) {
                log.error("查询增量流水异常，accountTypeEnum:{}, userId:{}, startDate:{}, endDate:{}, inOutFlowTypes:{}, assetsInfoResult:{}",
                        accountTypeEnum, userId, startDate, endDate, JSONObject.toJSONString(inOutFlowTypes), JSONObject.toJSONString(assetsInfoResult));
                throw e;
            }
            // 如果key不存在，put asset
            flowChangeSumMap.computeIfAbsent(assetsInfoResult.getCoinId(), v -> BigDecimal.ZERO);
            // 如果key存在，all + asset
            flowChangeSumMap.computeIfPresent(assetsInfoResult.getCoinId(), (key, oldValue) -> oldValue.add(assetsInfoResult.getProp1()));
            log.info("checkProfitAccountXxl checkProfitAccountForTime listUserIncrFlowsByAccountType prop sum userId:{} beginDate:{} endDate:{} accountTypeDesc:{} flowChangeSumMap:{} inOutFlowChangeSumMap:{}", userId, DateUtil.getFormatDate(startDate.getTime()), DateUtil.getFormatDate(endDate.getTime()), accountTypeEnum, JSON.toJSONString(flowChangeSumMap), JSON.toJSONString(inOutFlowChangeSumMap));
        }
        log.info("listUserIncrFlowsByAccountType userId:{} beginDate:{} endDate:{} accountTypeDesc:{} spend:{}", userId, DateUtil.getFormatDate(startDate.getTime()), DateUtil.getFormatDate(endDate.getTime()), accountTypeEnum, stopwatch.stop());
    }


    private boolean checkProfitAmount(BigDecimal profitAmount, BigDecimal beginBalance, ProfitAlarmDto profitAlarmDto) {
        if (NumberUtil.isEmptyOrZero(profitAmount)) {
            return false;
        }
        BigDecimal profitAlarmAmount = profitAlarmDto.getProfitAlarmAmount();
        BigDecimal profitRatio = profitAlarmDto.getProfitRatio();
        if (NumberUtil.isEmptyOrZero(beginBalance)) {
            // 期初为0，资产增加总值(盈利) > 盈利报警总数量
            return profitAmount.compareTo(profitAlarmAmount) > 0;
        }
        BigDecimal maxProfitAlarmAmount = profitAlarmDto.getMaxProfitAlarmAmount();
        // 资产增加总值(盈利) > 盈利报警总数量 && 资产增加百分比(盈利/期初) > 盈利/期初 或 用户盈利大于等于最大盈利告警阈值
        return (profitAmount.compareTo(profitAlarmAmount) > 0 && profitAmount.divide(beginBalance, RoundingMode.HALF_UP).compareTo(profitRatio) > 0)
                || (maxProfitAlarmAmount.compareTo(BigDecimal.ZERO) > 0 && profitAmount.compareTo(maxProfitAlarmAmount) >= 0);
    }

    /**
     * 反算用户当前资产情况，看与流水是否契合
     *
     * @param reconCheckResultsParams 参数
     * @param subSystem               子系统
     * @return 是否反算通过
     */
    private WithdrawCheckResultDTO checkBackCalculation(ReconCheckResultsParams reconCheckResultsParams, String subSystem) {
        Stopwatch slowLogStopwatch = Stopwatch.createStarted();
        Stopwatch stopwatch = Stopwatch.createStarted();
        WithdrawCheckResultDTO withdrawCheckResultDTO = new WithdrawCheckResultDTO();
        BackCalculationResultDto result = doBackCalculation(reconCheckResultsParams, subSystem);
        log.info("ReconCheckBillResultServiceImpl doBackCalculation userId {} , subSystem {} , time {}", reconCheckResultsParams.getUserId(), subSystem, stopwatch.stop());
        stopwatch.reset().start();

        if (result.getBillConfig() == null || CollectionUtils.isEmpty(result.getBillUsers()) || CollectionUtils.isEmpty(result.getBillCoinUserPropertyList())) {
            return withdrawCheckResultDTO;
        }
        log.info("ReconCheckBillResultServiceImpl doBackCalculation outcome userId {} , billCoinUserPropertyList {}", reconCheckResultsParams.getUserId(), LogPrintUtils.getBillCoinUserPropertyListPropStr(result.getBillCoinUserPropertyList()));
        BillAllConfig billConfig = result.getBillConfig();
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(billConfig.getAccountType());
        // 当前系统的配置
        ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap = new HashMap<>();
        Map<Integer, PriceVo> ratesToUSDTCoinIdMap = new HashMap<>();
        List<BillCoinUserProperty> billUsers = result.getBillUsers();
        List<BillCoinUserProperty> billCoinUserPropertyList = result.getBillCoinUserPropertyList();
        com.upex.bill.dto.params.BaseRequest baseRequest = new com.upex.bill.dto.params.BaseRequest();
        baseRequest.setMaxId(billConfig.getSyncPos());
        baseRequest.setAccountParam(billConfig.getAccountParam());
        baseRequest.setAccountType(billConfig.getAccountType());
        baseRequest.setBeginTime(Math.max(billConfig.getCheckOkTime().getTime() - TIME_INTERVAL, billUsers.get(0).getCheckTime().getTime()) - TIME_INTERVAL);
        baseRequest.setEndTime(Math.max(billConfig.getCheckOkTime().getTime() - TIME_INTERVAL, billUsers.get(0).getCheckTime().getTime()));
        Map<Long, Map<Integer, AccountAssetsInfoResult>> userAssetsMap = businessService.getUserAssetsMapSingleThread(accountTypeEnum, Collections.singletonList(reconCheckResultsParams.getUserId()), baseRequest, billConfig);
        log.info("ReconCheckBillResultServiceImpl getUserAssetsMapSingleThread userId {} , subSystem {} , time {}", reconCheckResultsParams.getUserId(), subSystem, stopwatch.stop());
        stopwatch.reset().start();

        if (CollectionUtils.isEmpty(billCoinUserPropertyList)) {
            if (MapUtils.isEmpty(userAssetsMap)) {
                return withdrawCheckResultDTO;
            } else {
                if (MapUtils.isEmpty(userAssetsMap.get(reconCheckResultsParams.getUserId()))) {
                    return withdrawCheckResultDTO;
                }
                // 非空的话，资产值必须全是0
                for (AccountAssetsInfoResult accountAssetsInfoResult : userAssetsMap.get(reconCheckResultsParams.getUserId()).values()) {
                    if (accountAssetsInfoResult.haveAsset()) {
                        log.info("...CheckBillResultServiceImpl.checkBackCalculation 反算校验失败，期末资产为空，但是业务系统反算结果不为空，禁止用户提币 {}, billConfig {}", reconCheckResultsParams.getUserId(), billConfig);
                        this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.BACK_CALCULATION_BILL_USER_EMPTY_ASSET_NOT_EMPTY, reconCheckResultsParams.getUserId());
                        withdrawCheckResultDTO.setResult(false);
                        withdrawCheckResultDTO.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_BACK_CALCULATION_FAIL);
                        return withdrawCheckResultDTO;
                    }
                }
                return withdrawCheckResultDTO;
            }
        }

        for (BillCoinUserProperty billCoinTypeUserProperty : billCoinUserPropertyList) {
            stopwatch.reset().start();
            if (MapUtils.isNotEmpty(userAssetsMap) && !apolloBillConfig.getCheckAssetsWhiteList().contains(reconCheckResultsParams.getUserId())) {
                //对账 userId+coinId,基于流水推算期末资产=从业务系统反算资产
                Map<Integer, AccountAssetsInfoResult> coinAssetsMap = userAssetsMap.get(reconCheckResultsParams.getUserId());
                if (MapUtils.isNotEmpty(coinAssetsMap)) {
                    AccountAssetsInfoResult totalAccountAssetsInfoResult = coinAssetsMap.get(billCoinTypeUserProperty.getCoinId());
                    if (totalAccountAssetsInfoResult == null) {
                        totalAccountAssetsInfoResult = new AccountAssetsInfoResult();
                        totalAccountAssetsInfoResult.setUserId(reconCheckResultsParams.getUserId());
                        totalAccountAssetsInfoResult.setCoinId(billCoinTypeUserProperty.getCoinId());
                        log.info("selectCheckForTheResults financial totalAccountAssetsInfoResult is null,userId:{},coinId:{}", reconCheckResultsParams.getUserId(), billCoinTypeUserProperty.getCoinId());
                    }
                    // 对账逻辑
                    boolean checkResult = accountAssetsServiceFactory.getBillCheckService(accountTypeEnum.getCode()).checkAssets(billCoinTypeUserProperty, totalAccountAssetsInfoResult, apolloBillConfig, ratesToUSDTCoinIdMap, billWalletSupplementConfigUserCoinMap);
                    if (!checkResult) {
                        // 检测sprop是否正确
                        checkResult = accountAssetsServiceFactory.getBillCheckService(accountTypeEnum.getCode()).checkSpropAssets(billCoinTypeUserProperty, totalAccountAssetsInfoResult, apolloBillConfig, ratesToUSDTCoinIdMap, billWalletSupplementConfigUserCoinMap);
                        if (!checkResult) {
                            log.info("...CheckBillResultServiceImpl.checkBackCalculation 反算校验失败，禁止用户提币 {}, billConfig {}, billCoinTypeUserProperty {}, totalAccountAssetsInfoResult {}, billCoinUserPropertyAssetSnapshot {}", reconCheckResultsParams.getUserId(), billConfig, JSONObject.toJSONString(billCoinTypeUserProperty), JSONObject.toJSONString(totalAccountAssetsInfoResult), JSONObject.toJSONString(billCoinTypeUserProperty));
                            this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.BACK_CALCULATION_BILL_USER_NOT_MATCH_ASSET, reconCheckResultsParams.getUserId());
                            withdrawCheckResultDTO.setResult(false);
                            withdrawCheckResultDTO.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_BACK_CALCULATION_FAIL);
                            return withdrawCheckResultDTO;
                        }
                    }
                } else {
                    // property非空，但是反算 coinAssetsMap 为空的情况
                    if (billCoinTypeUserProperty.isPropNotZeroAbsSum()) {
                        log.error("...CheckBillResultServiceImpl.checkBackCalculation 用户提币，资产 coinAssetsMap 为空，对账非0 billCoinTypeUserProperty {}", JSONObject.toJSONString(billCoinTypeUserProperty));
                    }
                }
                log.info("ReconCheckBillResultServiceImpl checkAssets userId {} , subSystem {} , time {}", reconCheckResultsParams.getUserId(), subSystem, stopwatch.stop());
            } else {
                // property非空，但是反算 userAssetsMap 为空的情况
                if (billCoinTypeUserProperty.isPropNotZeroAbsSum()) {
                    log.error("...CheckBillResultServiceImpl.checkBackCalculation 用户提币，资产userAssetsMap为空，对账非0 billCoinTypeUserProperty {}", JSONObject.toJSONString(billCoinTypeUserProperty));
                }
            }
        }
        BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_backCalculationAllSammary.getName(), reconCheckResultsParams.getUserId(), billConfig.getAccountType());
        return withdrawCheckResultDTO;
    }

    private BackCalculationResultDto doBackCalculation(ReconCheckResultsParams reconCheckResultsParams, String subSystem) {
        BackCalculationResultDto backCalculationResultDto = new BackCalculationResultDto();
        List<BillAllConfig> billConfigs = billAllConfigService.getCheckOkBillConfig(Collections.singletonList(subSystem));
        if (CollectionUtils.isEmpty(billConfigs)) {
            return backCalculationResultDto;
        }

        BillAllConfig billConfig = billConfigs.get(0);
        BillCoinUserProperty billCoinUserPropertyToCheckExist = billCoinUserPropertyService.selectBySingleUserIdLatest(reconCheckResultsParams.getUserId(), billConfig.getAccountParam(), Integer.valueOf(billConfig.getAccountType()));
        if (billCoinUserPropertyToCheckExist == null) {
            // 这个人在这条业务线没有任何对账记录，直接跳出
            return backCalculationResultDto;
        }
        List<BillCoinUserProperty> billCoinUserPropertyList = billCoinUserPropertySnapshotService.selectAllCoinUserByCheckTimeAndCoinIds(Integer.valueOf(billConfig.getAccountType()), billConfig.getAccountParam(), reconCheckResultsParams.getUserId(), billCoinUserPropertyToCheckExist.getCheckTime());
        backCalculationResultDto.setBillConfig(billConfig);
        backCalculationResultDto.setBillUsers(List.of(billCoinUserPropertyToCheckExist));
        backCalculationResultDto.setBillCoinUserPropertyList(billCoinUserPropertyList);
        return backCalculationResultDto;
    }

    private Date getDifferenceTime(ReconCheckResultsParams reconCheckResultsParams, GlobalBillConfig globalBillConfig, BusinessTypeEnum businessTypeEnum) {
        CheckVo checkVo = getCheckVo(businessTypeEnum.getBusinessCode(), globalBillConfig.getBusinessConfig());
        return DateUtil.addMinute(new Date(reconCheckResultsParams.getRequestDate()), checkVo.getCheckInterval() * (-1));
    }

    private WithdrawCheckResultDTO checkAccountAndBill(ReconCheckResultsParams reconCheckResultsParams, GlobalBillConfig globalBillConfig, BillAllConfig minBillConfig) {
        WithdrawCheckResultDTO withdrawCheckResultDTO = new WithdrawCheckResultDTO();
        Stopwatch slowLogStopwatch = Stopwatch.createStarted();
        Date defaultDifferenceTime = getDifferenceTime(reconCheckResultsParams, globalBillConfig, BusinessTypeEnum.BUSINESS_NORMAL);
        if (minBillConfig.getCheckOkTime().getTime() < defaultDifferenceTime.getTime()) {
            log.info("CheckBillResultServiceImpl selectCheckForTheResults 最小业务线对账配置对账成功时间 < 允许业务线对账延迟时间，禁止用户提币，userId:{}， minBillConfig.getCheckOkTime:{}, defaultDifferenceTime:{}", reconCheckResultsParams.getUserId(), minBillConfig.getCheckOkTime().getTime(), defaultDifferenceTime.getTime());
            this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.ACCOUNT_TYPE_REC_TIMEOUT, reconCheckResultsParams.getUserId());
            withdrawCheckResultDTO.setResult(false);
            withdrawCheckResultDTO.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_BUSINESS_TYPE_TIME_FAIL);
            return withdrawCheckResultDTO;

        }
        BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_getDifferenceTime.getName(), reconCheckResultsParams.getUserId(), null);


        if (globalBillConfig.isGlobalAlarmCheck()) {
            // 获取总账对账配置，取上次对账成功的check_ok_time
            AssetsBillConfig assetsBillConfig = assetsBillConfigService.selectByTypeAndParam(AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getCode(), BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM);

            if (assetsBillConfig == null) {
                this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.INTERNAL_CONFIG_NULL, reconCheckResultsParams.getUserId());
                log.info("CheckBillResultServiceImpl selectCheckForTheResults 总账配置为空，禁止用户提币，userId:{}", reconCheckResultsParams.getUserId());
                withdrawCheckResultDTO.setResult(false);
                withdrawCheckResultDTO.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_IN_ALL_CONFIG_FAIL);
                return withdrawCheckResultDTO;
            }

            if (assetsBillConfig.getCheckOkTime().getTime() < defaultDifferenceTime.getTime()) {
                this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.INTERNAL_REC_TIMEOUT, reconCheckResultsParams.getUserId());
                log.info("CheckBillResultServiceImpl selectCheckForTheResults 总账配置对账成功时间 < 允许总账延迟时间，禁止用户提币，userId:{}， assetsBillConfig.getCheckOkTime:{}, defaultDifferenceTime:{}", reconCheckResultsParams.getUserId(), assetsBillConfig.getCheckOkTime().getTime(), defaultDifferenceTime.getTime());
                withdrawCheckResultDTO.setResult(false);
                withdrawCheckResultDTO.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_IN_ALL_TIME_FAIL);
                return withdrawCheckResultDTO;

            }
            BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_isGlobalAlarmCheck.getName(), reconCheckResultsParams.getUserId(), null);
        }

        if (globalBillConfig.isRealAssetsAlarmCheck()) {
            // 获取统计资金的配置，获取最后一次完成资金对账的配置
            StatisticsProperty assetsStatistics = statisticsPropertyService.selectLastDoneAndCorrect();
            if (assetsStatistics == null) {
                this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.CAPITAL_BILL_LAST_CONFIG_NULL, reconCheckResultsParams.getUserId());
                log.info("CheckBillResultServiceImpl selectCheckForTheResults 最后一次完成资金对账的配置为空，禁止用户提币，userId:{}", reconCheckResultsParams.getUserId());
                withdrawCheckResultDTO.setResult(false);
                withdrawCheckResultDTO.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_LAST_CAPITAL_CONFIG_FAIL);
                return withdrawCheckResultDTO;

            }

            if (!StatisticsResultEnum.ASSET_CORRECT.getResult().equals(assetsStatistics.getStatisticsResult())) {
                // 如果对账差额 >= 禁止提币报警值，不让提币
                if (assetsStatistics.getBillDiffValue().abs().compareTo(globalBillConfig.getAlarmValue()) >= 0) {
                    this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.CAPITAL_BILL_DIFF_QUOTA, reconCheckResultsParams.getUserId());
                    log.info("CheckBillResultServiceImpl selectCheckForTheResults 对账差额 >= 禁止提币报警值，禁止用户提币，userId:{}, BillDiffValue:{}, AlarmValue:{}", reconCheckResultsParams.getUserId(), assetsStatistics.getBillDiffValue(), globalBillConfig.getAlarmValue());
                    withdrawCheckResultDTO.setResult(false);
                    withdrawCheckResultDTO.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_CAPITAL_RESULT_FAIL);
                    return withdrawCheckResultDTO;
                }
            }
            BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_isRealAssetsAlarmCheck.getName(), reconCheckResultsParams.getUserId(), null);
        }

        if (globalBillConfig.isCapitalAssetsCheck()) {
            // 获取统计资金的配置，获取最后一次完成资金对账的配置
            Date executeTime = new Date();
            List<BillCapitalConfig> lastFinalConfigList = billCapitalConfigService.selectLastFinalStateConfig(globalBillConfig.getCapitalBlockedWithdrawCount());
            BillCapitalConfig lastSuccessConfig = billCapitalConfigService.selectLastConfigByTimeAndStatus(executeTime, BillCapitalStatusEnum.DONE.getStatusCode());
            if (CollectionUtils.isEmpty(lastFinalConfigList) || Objects.isNull(lastSuccessConfig)) {
                this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.CAPITAL_BILL_CONFIG_NULL, reconCheckResultsParams.getUserId());
                AlarmUtils.error("CheckBillResultServiceImpl selectCheckForTheResults 资金对账配置为空，禁止用户提币，userId:{}", reconCheckResultsParams.getUserId());
                withdrawCheckResultDTO.setResult(false);
                withdrawCheckResultDTO.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_CAPITAL_CONFIG_FAIL);
                return withdrawCheckResultDTO;
            }

            Date intervalTime = DateUtil.addMinute(executeTime, globalBillConfig.getCapitalLastSuccessTimeIntervalMin() * BillConstants.NEG_ONE);
            boolean intervalFlag = lastSuccessConfig.getCheckOkTime().compareTo(intervalTime) < 0;

            // 如果最近的配置中， 对账差额 >= 禁止提币值 的配置数量 == 资金对账阻断提币次数 ，不让提币
            if (globalBillConfig.getCapitalBlockedWithdrawCount() == lastFinalConfigList.stream().filter(lastFinalConfig -> BillCapitalAmountUtils.billAssetDiff(lastFinalConfig.getBillCapitalValue(), globalBillConfig.getCapitalBlockedWithdrawExpression())).count() && intervalFlag) {
                List<BigDecimal> billCapitalValueList = lastFinalConfigList.stream()
                        .map(BillCapitalConfig::getBillCapitalValue)
                        .filter(billCapitalValue -> BillCapitalAmountUtils.billAssetDiff(billCapitalValue, globalBillConfig.getCapitalBlockedWithdrawExpression()))
                        .collect(Collectors.toList());
                String errorMessage = String.format("CheckBillResultServiceImpl selectCheckForTheResults 资金对账差额 >= 资金对账阻断提币值，禁止用户提币，userId:%s, billCapitalValueList:%s, CapitalBlockedWithdrawExpression:%s", reconCheckResultsParams.getUserId(), JSONObject.toJSONString(billCapitalValueList), globalBillConfig.getCapitalBlockedWithdrawExpression());
                log.error(errorMessage);
                this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.CAPITAL_BILL_FORBID_WITHDRAW, reconCheckResultsParams.getUserId());

                //可以去掉
                // AlarmUtils.emergency(errorMessage);
                withdrawCheckResultDTO.setResult(false);
                withdrawCheckResultDTO.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_CAPITAL_BILL_FORBID_FAIL);
                return withdrawCheckResultDTO;
            }

            BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_isCapitalAssetsCheck.getName(), reconCheckResultsParams.getUserId(), null);
        }
        return withdrawCheckResultDTO;
    }


    /**
     * 检查用户非实时负资产校验
     *
     * @param reconCheckResultsParams
     * @param globalBillConfig
     * @return
     */
    private WithdrawCheckResultDTO checkNonRealtimeNegativeAsset(ReconCheckResultsParams reconCheckResultsParams, GlobalBillConfig globalBillConfig) {
        // 检查用户负值资产
        Stopwatch slowLogStopwatch = Stopwatch.createStarted();
        WithdrawCheckResultDTO withdrawCheckResultDTO = new WithdrawCheckResultDTO();
        Map<String, List<BillCoinUserProperty>> checkUserNegativeAssetsMap = checkUserNegativeAssetsSerial(reconCheckResultsParams, globalBillConfig);
        if (MapUtils.isNotEmpty(checkUserNegativeAssetsMap)) {
            this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.USER_NEGATIVE_ASSETS, reconCheckResultsParams.getUserId());
            log.info("...CheckBillResultServiceImpl.selectCheckForTheResults 用户资产为负值，禁止用户提币， userId:{}, checkUserNegativeAssetsMap:{}", reconCheckResultsParams.getUserId(), JSONObject.toJSONString(checkUserNegativeAssetsMap));
            withdrawCheckResultDTO.setResult(false);
            withdrawCheckResultDTO.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_NEGATIVE_FAIL);
            return withdrawCheckResultDTO;
        }
        BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_isUserNegativeAssetsAlarmCheck.getName(), reconCheckResultsParams.getUserId(), null);
        return withdrawCheckResultDTO;
    }

    /**
     * 获取负值资产（串行）
     *
     * @param reconCheckResultsParams
     * @param globalBillConfig
     * @return
     */
    private Map<String, List<BillCoinUserProperty>> checkUserNegativeAssetsSerial(ReconCheckResultsParams reconCheckResultsParams, GlobalBillConfig globalBillConfig) {
        List<String> checkNegativeAssetsSubSystemList = globalBillConfig.getCheckNegativeAssetsSubSystemList();
        if (CollectionUtils.isEmpty(checkNegativeAssetsSubSystemList)) {
            return new ConcurrentHashMap<>();
        }

        Map<String, List<BillCoinUserProperty>> negativeAssetsMap = new ConcurrentHashMap<>(10);
        checkNegativeAssetsSubSystemList.stream().forEach(subSystem -> {
            Stopwatch stopwatch = Stopwatch.createStarted();
            String[] split = subSystem.split(SEPARATOR);
            SysAssetsParams sysAssetsParams = new SysAssetsParams(Integer.valueOf(split[0]), split[1]);
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(sysAssetsParams.getAccountType().byteValue());
            try {
                List<BillCoinUserProperty> billCoinUserPropertyList = reconUserAssetsSnapShotService.obtainDataWithDiffAccountType(accountTypeEnum, reconCheckResultsParams.getUserId(), sysAssetsParams, new Date(reconCheckResultsParams.getRequestDate()));
                List<BillCoinUserProperty> negativePropertyList = CollectionUtils.emptyIfNull(billCoinUserPropertyList).stream().filter(property -> CalculateBillAssetsAmountUtil.isUserAssetsNegative((BillCoinUserProperty) property, accountTypeEnum)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(negativePropertyList)) {
                    return;
                }
                //合约类型为负值，重新查一下合约实时资产
                if (accountTypeEnum.isContract()) {
                    List<BillCoinUserProperty> negativePropertyListByFeign = collectNegativePropertyList(reconCheckResultsParams.getUserId(), (int) accountTypeEnum.getCode(), negativePropertyList);
                    if (CollectionUtils.isNotEmpty(negativePropertyListByFeign)) {
                        negativeAssetsMap.put(subSystem, negativePropertyListByFeign);
                    }
                } else {
                    negativeAssetsMap.put(subSystem, negativePropertyList);
                }
            } finally {
                BizLogUtils.printLogForSlowInterface(stopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_obtainDataWithDiffAccountType.getName(), reconCheckResultsParams.getUserId(), accountTypeEnum.getCode());
            }
        });
        return negativeAssetsMap;
    }


    private List<BillCoinUserProperty> collectNegativePropertyList(Long userId, Integer accountType, List<BillCoinUserProperty> negativePropertyList) {
        List<BillCoinUserProperty> okCoinList = new CopyOnWriteArrayList<>();
        Map<Integer, String> allCoinsMap = commonService.getAllCoinsMapCache(DateUtil.getZeroOClick(new Date()).getTime());
        negativePropertyList.stream().parallel().forEach(property -> {
            AccountQueryParam param = new AccountQueryParam();
            param.setUserId(userId);
            param.setBusinessLine(BusinessLineMappingEnum.toBusinessLine(accountType));
            param.setTokenId(allCoinsMap.get(property.getCoinId())); //coinName
            try {
                AccountListDTO accountListDTO = innerAccountFeignClient.queryAccountList(param);
                if (Objects.nonNull(accountListDTO)) {
                    String totalUSDT = accountListDTO.getContractTotalUSDT();
                    if (StrUtil.isNotBlank(totalUSDT) && new BigDecimal(totalUSDT).compareTo(BigDecimal.ZERO) >= 0) {
                        okCoinList.add(property);
                    }
                }
            } catch (Exception ex) {
                log.error("call innerAccountFeignClient queryAccountList error,param:{},msg:{}", param, ex.getMessage());
                log.error("call innerAccountFeignClient queryAccountList error", ex);
            }
        });
        List<Integer> okCoinIdList = okCoinList.stream().map(BillCoinUserProperty::getCoinId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(okCoinIdList)) {
            negativePropertyList = negativePropertyList.stream().filter(item -> !okCoinIdList.contains(item.getCoinId())).collect(Collectors.toList());
        }
        return negativePropertyList;
    }


    /**
     * 业务侧提币受限制与否校验
     *
     * @param reconCheckResultsParams
     * @param globalBillConfig
     * @return
     */
    @Override
    public WithdrawCheckResultDTO checkWithdrawLimit(ReconCheckResultsParams reconCheckResultsParams, GlobalBillConfig globalBillConfig) {
        WithdrawCheckResultDTO result = new WithdrawCheckResultDTO();
        Stopwatch slowLogStopwatch = Stopwatch.createStarted();
        // 检查是否允许用户提币 结果为空或者返回结果为No允许用户提币
        // 查询用户订单信息
        Boolean userResult;
        if (globalBillConfig.getQueryUserSourceOpen()) {
            userResult = getUserResultByBusinessSource(reconCheckResultsParams, result, globalBillConfig);
        } else {
            getUserResultByBusinessSource(reconCheckResultsParams, result, globalBillConfig);
            result.setResult(true);
            return result;
        }

        if (Objects.nonNull(userResult) && Boolean.FALSE.equals(userResult)) {
            this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.USER_SERVICES, reconCheckResultsParams.getUserId());
            log.info("CheckBillResultServiceImpl selectCheckForTheResults 用户服务禁止用户提币 userId:{}, userResult:{}", reconCheckResultsParams.getUserId(), JSON.toJSONString(userResult));
            result.setResult(false);
            result.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.USER_CHECK_FAILED);
            return result;
        }
        BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_isUserWithdrawalAlarmCheck.getName(), reconCheckResultsParams.getUserId(), null);
        return result;
    }

    // 根据订单信息获取用户结果的方法
    private Boolean getUserResultByOrderInfo(BillCapitalOrder capitalOrderInfo, ReconCheckResultsParams reconCheckResultsParams) {
        Long userId = reconCheckResultsParams.getUserId();
        Byte source = capitalOrderInfo.getSource();
        SourceEnum anEnum = SourceEnum.toEnum(Integer.valueOf(source));
        Long bizCode;
        switch (anEnum) {
            case OPEN_API:
                bizCode = UserPermissionEnum.API_WITHDRAW.getCode();
                break;
            case SYS_BATCH:
                bizCode = UserPermissionEnum.ADMIN_WITHDRAW.getCode();
                break;
            default:
                bizCode = UserPermissionEnum.WITHDRAW.getCode();
                break;
        }
        return checkUserPermission(userId, bizCode);
    }

    public Boolean getUserResultByBusinessSource(ReconCheckResultsParams reconCheckResultsParams, WithdrawCheckResultDTO result, GlobalBillConfig globalBillConfig) {
        Boolean userResult = null;
        if (BusinessSourceEnum.REFUND_OF_RECHARGE_FAILURE.getCode().equals(reconCheckResultsParams.getBusinessSource())) {
            log.info("充值未到账退回不检测提币权限");
            return userResult;
        }
        if (BusinessSourceEnum.getHaveRecordCodeBusinessSourceList().contains(reconCheckResultsParams.getBusinessSource())) {
            try {
                Long orderNumber = reconCheckResultsParams.getOrderId();
                BillCapitalOrder capitalOrderInfo = capitalOrderService.getCapitalOrderInfo(orderNumber);
                if (Objects.isNull(capitalOrderInfo)) {
                    log.error("CheckBillResultServiceImpl selectCheckForTheResults capitalOrderInfo is null 查询用户订单信息失败，orderNumber:{}, recordCode:{}", orderNumber, reconCheckResultsParams.getRecordCode());
                    CapitalOrderQueryParam capitalOrderQueryParam = new CapitalOrderQueryParam();
                    capitalOrderQueryParam.setOrderId(orderNumber);
                    SpotInnerCapitalOrderInfoResult capitalOrderDetailByOrderId = spotCapitalQueryClient.getCapitalOrderDetailByOrderId(capitalOrderQueryParam);
                    if (Objects.isNull(capitalOrderDetailByOrderId)) {
                        this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.USER_ORDER_NOT_EXIST, reconCheckResultsParams.getUserId());
                        result.setResult(false);
                        result.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_ORDER_ERROR);
                        return false;
                    }
                    capitalOrderInfo = new BillCapitalOrder();
                    capitalOrderInfo.setSource(capitalOrderDetailByOrderId.getSource());
                }
                // 根据订单信息获取用户结果
                userResult = getUserResultByOrderInfo(capitalOrderInfo, reconCheckResultsParams);
            } catch (Exception e) {
                // 处理异常并返回默认用户结果
                AlarmUtils.error("CheckBillResultServiceImpl selectCheckForTheResults 查询用户订单信息异常 userId:{}, userResult:{}", reconCheckResultsParams.getUserId(), userResult, e);
                userResult = checkUserPermission(reconCheckResultsParams.getUserId(), UserPermissionEnum.WITHDRAW.getCode());
            }
        } else if (BusinessSourceEnum.getSystemBatch().contains(reconCheckResultsParams.getBusinessSource())) {
            if (globalBillConfig.getQueryUserSourceVerificationOpen()) {
                userResult = checkUserPermission(reconCheckResultsParams.getUserId(), UserPermissionEnum.ADMIN_WITHDRAW.getCode());
            } else {
                checkUserPermission(reconCheckResultsParams.getUserId(), UserPermissionEnum.ADMIN_WITHDRAW.getCode());
                userResult = true;
            }
        } else {
            userResult = checkUserPermission(reconCheckResultsParams.getUserId(), WithdrawUtils.getUserPermissionEnum(reconCheckResultsParams.getBusinessSource()).getCode());
        }
        return userResult;
    }

    /**
     * 用户权限检查
     * 如果指定权限禁止，直接抛出异常
     * 如果没有禁止，无返回
     *
     * @param userId
     * @param bizCode
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2025/3/4 16:16
     */
    public Boolean checkUserPermission(Long userId, Long bizCode) {
        try {
            userPbCacheService.checkPermissionDefault(userId, bizCode);
            return Boolean.TRUE;
        } catch (ApiException e) {
            String code = e.getCode();
            if (CHECK_PERMISSION_ERROR_CODE_SET.contains(code)) {
                log.info("CheckBillResultServiceImpl checkUserPermission 禁止用户提币 userId:{},bizCode:{},code:{},error", userId, bizCode, code, e);
                return Boolean.FALSE;
            } else {
                log.error("CheckBillResultServiceImpl checkUserPermission error not in CHECK PERMISSION ERROR CODE SET,userId:{},bizCode:{},", userId, bizCode, e);
                throw e;
            }
        } catch (Exception e) {
            log.error("CheckBillResultServiceImpl checkUserPermission Exception userId:{},bizCode:{},", userId, bizCode, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * @param userId
     * @param subSystem
     * @return
     */
    private WithdrawCheckResultDTO checkSingleRealTimeUserNegativeAssets(Long userId, String subSystem, ReconCheckResultsParams reconCheckResultsParams, Long requestDate, GlobalBillConfig globalBillConfig) {
        Stopwatch slowLogStopwatch = Stopwatch.createStarted();
        WithdrawCheckResultDTO result = new WithdrawCheckResultDTO();
        try {
            List<AccountAssetsInfoResult> accountAssetsInfoResultList = reconUserAssetsSnapShotService.queryUserSingleRealTimeAssets(userId, subSystem, requestDate, globalBillConfig);
            if (CollectionUtils.isNotEmpty(accountAssetsInfoResultList)) {
                this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.USER_REAL_TIME_ASSETS_NEGATIVE, userId);
                log.info("...CheckBillResultServiceImpl.selectCheckForTheResults 用户(实时)资产为负值，禁止用户提币， userId:{}, checkUserRealTimeNegativeAssetsMap:{}", userId, JSONObject.toJSONString(accountAssetsInfoResultList));
                result.setResult(false);
                result.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_NEGATIVE_FAIL);
                return result;
            }
        } catch (Exception ex) {
            this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.USER_REAL_TIME_ASSETS_EXCEPTION, userId);
            log.error("...CheckBillResultServiceImpl.selectCheckForTheResults 用户(实时)资产为负值，查询用户实时资产异常，禁止用户提币", ex);
            result.setResult(false);
            result.setWithdrawCheckResultEnum(WithdrawCheckResultEnum.RECONCILIATION_CHECK_NEGATIVE_FAIL);
            return result;
        } finally {
            BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_isUserRealTimeNegativeAssetsAlarmCheck.getName(), reconCheckResultsParams.getUserId(), null);
        }
        return result;
    }


    @Override
    public boolean checkDelayAccountByOldBill(ReconCheckResultsParams reconCheckResultsParams, GlobalBillConfig globalBillConfig) {
        // 用户名白名单逻辑
        if (globalBillConfig.getDelayAccountWhiteList().contains(reconCheckResultsParams.getUserId())) {
            return false;
        }

        // 查询延迟入账的数据
        List<BillDelayAccount> delayAccountList = billDelayAccountService.selectHistoryDelayAccountList(reconCheckResultsParams.getUserId(), ActiveFlagEnum.Active.getCode());
        if (CollectionUtils.isNotEmpty(delayAccountList)) {
            log.info("CheckBillResultServiceImpl checkDelayAccount userId:{},delayAccountList:{}", reconCheckResultsParams.getUserId(), JSONObject.toJSONString(delayAccountList));
        }
        return CollectionUtils.isNotEmpty(delayAccountList);
    }


    private boolean verifyUserIllegalModificationBillData(ReconCheckResultsParams reconCheckResultsParams) {
        Long userId = reconCheckResultsParams.getUserId();
        Boolean ifMember = redisTemplate.opsForSet().isMember(RedisUtil.KAFKA_ILLEGAL_MESSAGE_USER_IDS, String.valueOf(userId));
        if (ifMember != null && ifMember) {
            this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.USER_ILLEGAL_MODIFICATION_BILL_DATA, userId);
            return false;
        }
        return true;
    }

    private boolean verifyUserIsExist(ReconCheckResultsParams reconCheckResultsParams, ReconBillUserVo reconBillUserVo) {
        reconBillUserVo.setUserId(reconCheckResultsParams.getUserId());

        if (!checkUserExist(reconCheckResultsParams.getUserId())) {
            this.syncSendAlarmMessage(reconCheckResultsParams, AlarmTemplateEnum.USER_NOT_EXIST, reconCheckResultsParams.getUserId());
            return false;
        }
        return true;
    }


    private boolean checkUserExist(Long userId) {
        if (userId == null) {
            return false;
        }
        List<BillUser> billUsers = billUserService.selectByIds(List.of(userId));
        if (CollectionUtils.isEmpty(billUsers)) {
            return false;
        }
        return true;
    }

    @Override
    public void syncSendAlarmMessage(ReconCheckResultsParams resultsParams, AlarmTemplateEnum alarmTemplateEnum, Object... args) {
        try {
            if (Objects.isNull(args)) {
                return;
            }
            // 获取报警消息
            Long userId = (Long) args[0];
            String code = alarmTemplateEnum.getLevel().getCode().equals(AlarmTemplateLevelEnum.NORMAL.getCode()) ? String.valueOf(userId) : alarmTemplateEnum.name();
            String redisKey = String.format(BillRedisKeyEnum.RECON_CACHE_WITHDRAWAL_ALARM_LOCK.getKey(), code);
            Object object = redisTemplate.opsForValue().get(redisKey);
            if (Objects.nonNull(object)) {
                log.info("syncSendAlarmMessage redisKey:[{}] is not null", redisKey);
                return;
            }
            String businessSource = resultsParams.getBusinessSource();
            if (StringUtils.isNotBlank(businessSource)) {
                String bizSourceDesc = businessSource;
                BusinessSourceEnum sourceEnum = BusinessSourceEnum.toEnum(businessSource);
                if (Objects.nonNull(sourceEnum)) {
                    bizSourceDesc = sourceEnum.getDesc();
                }
                args = Stream.concat(Arrays.stream(args), Stream.of(String.format("业务来源：%s", bizSourceDesc))).toArray(Object[]::new);
            }
            // 发送消息
            alarmNotifyService.alarm(alarmTemplateEnum, args);
            log.error("syncSendAlarmMessage redisKey:[{}] alarmMessage:{}", redisKey, alarmTemplateEnum.getText());
            // 设置Redis值
            GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
            redisTemplate.opsForValue().set(redisKey, String.valueOf(userId), globalBillConfig.getWithdrawalCheckExpire(), TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("ReconCheckBillResultServiceImpl.syncSendAlarmMessage error ", e);
        }
    }


    public boolean checkWithdrawAlarmTypeForUser(Integer level) {
        return level == 0;
    }

    @Override
    public void resultsCheck(ReconCheckResultsParams reconCheckResultsParams) {
        if (null == reconCheckResultsParams) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }

        if (reconCheckResultsParams.getUserId() == null || CheckBillResultConstant.CHECK_EMPTY == reconCheckResultsParams.getUserId().intValue()) {
            throw new ApiException(BillExceptionEnum.USER_NOT_EXISTS);
        }
    }

    @Override
    public void withdrawalBlockReasonCheck(ReconCheckResultsParams reconCheckResultsParams, ReconBillUserVo reconBillUserVo) {
        if (reconBillUserVo.isPass()) {
            return;
        }

        WithdrawCheckResultEnum withdrawCheckResultEnum = reconBillUserVo.getWithdrawCheckResultEnum();
        if (Objects.isNull(withdrawCheckResultEnum)) {
            return;
        }

        int withdrawErrorCode = withdrawCheckResultEnum.getCode();
        Set<Integer> userCodeSet = ReconciliationApolloConfigUtils.getGlobalBillConfig().getUserCodeSet();
        if (CollectionUtils.isEmpty(userCodeSet) || !userCodeSet.contains(withdrawErrorCode)) {
            return;
        }

        // 单位时间内，相同报警原因的次数超过阈值，进行lark报警
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        Integer withdrawBlockLarkAlarmTimeFixed = globalBillConfig.getWithdrawBlockLarkAlarmTimeFixed();
        Date timeKey = DateUtil.getLastMin(new Date(), withdrawBlockLarkAlarmTimeFixed);
        String redisKey = String.format(BillRedisKeyEnum.RECON_CACHE_WITHDRAWAL_LARK_ALARM_LOCK.getKey(), timeKey.getTime(), withdrawCheckResultEnum.getValue());
        String redisLock = BillRedisKeyEnum.RECON_CACHE_WITHDRAWAL_BLOCK_ALARM_THRESHOLD_LOCK.getKey();
        // 增加报警用户数量
        long count = stockService.addStock(redisKey, Long.valueOf(withdrawBlockLarkAlarmTimeFixed), BillConstants.ONE, redisLock);
        if (count >= globalBillConfig.getWithdrawBlockLarkAlarmThreshold()) {
            //lark报警  指定key的阻断次数超过预警值，进行lark报警
            alarmNotifyService.alarm(AlarmTemplateEnum.WITHDRAW_BILL_PLATFORM_WITHDRAW_BLOCKING, withdrawCheckResultEnum.getValue(), count, globalBillConfig.getWithdrawBlockLarkAlarmThreshold());
        }
    }

    @Override
    public void platformBlockReasonCheck(ReconCheckResultsParams reconCheckResultsParams, ReconBillUserVo reconBillUserVo) {
        WithdrawCheckResultEnum withdrawCheckResultEnum = reconBillUserVo.getWithdrawCheckResultEnum();
        if (Objects.isNull(withdrawCheckResultEnum)) {
            return;
        }

        String redisKey = String.format(BillRedisKeyEnum.RECON_CACHE_WITHDRAWAL_BLOCK_ALARM_THRESHOLD_VALUE.getKey(), withdrawCheckResultEnum.getValue());
        String redisOrderNoKey = String.format(BillRedisKeyEnum.RECON_CACHE_WITHDRAWAL_BLOCK_ALARM_THRESHOLD_ORDER_NO.getKey(), withdrawCheckResultEnum.getValue());
        if (reconBillUserVo.isPass()) {
            // 平台提币通过，清除缓存
            // 将订单号一起清除
            stockService.removeStock(redisKey);
            stockService.removeStock(redisOrderNoKey);
            return;
        }

        // 平台提币失败，只有小额提币走这个流程
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        Set<String> platformBusinessSourceSet = globalBillConfig.getPlatformBusinessSourceSet();
        String businessSource = reconCheckResultsParams.getBusinessSource();
        if (CollectionUtils.isEmpty(platformBusinessSourceSet) || !platformBusinessSourceSet.contains(businessSource)) {
            return;
        }

        int withdrawErrorCode = withdrawCheckResultEnum.getCode();
        Set<Integer> platformCodeSet = globalBillConfig.getPlatformCodeSet();
        if (CollectionUtils.isEmpty(platformCodeSet) || !platformCodeSet.contains(withdrawErrorCode)) {
            return;
        }

        Long orderNumber = reconCheckResultsParams.getOrderId();
        BillCapitalOrder capitalOrder = capitalOrderService.getCapitalOrderInfo(orderNumber);
        if (Objects.isNull(capitalOrder)) {
            return;
        }

        // 获取最近指定时间的缓存汇率
        Date timeKey = DateUtil.getLastMin(new Date(), BillConstants.FIVE);
        Map<Integer, PriceVo> ratesMap = commonService.getCoinIdRatesMapCache(timeKey.getTime());
        Integer coinId = capitalOrder.getCoinId();
        BigDecimal amount = capitalOrder.getAmount();
        BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(coinId, ratesMap);
        // 订单金额转换为U
        BigDecimal orderAmountUsdt = amount.multiply(rate);

        // 如果通过，并且redis有值，清空数据
        String redisLockKey = BillRedisKeyEnum.RECON_CACHE_WITHDRAWAL_BLOCK_ALARM_THRESHOLD_LOCK.getKey();
        long stock = stockService.subtractStock(redisKey, redisOrderNoKey, globalBillConfig.getPlatformBlockedWithdrawValueTimeFixed(), orderAmountUsdt, globalBillConfig.getPlatformBlockedWithdrawValue(), orderNumber, redisLockKey);
        if (stock > 0) {
            // 如果剩余库存大于0，说明允许该用户提币，重新设置提币结果
            reconBillUserVo.setPass(true);
            reconBillUserVo.setWithdrawCheckResultEnum(null);
            log.info("platformBlockReasonCheck userId:{},orderNumber:{},stock:{},orderAmountUsdt:{},rate:{}", reconCheckResultsParams.getUserId(), orderNumber, stock, orderAmountUsdt, rate);
        }
    }

    @Override
    public void saveWithdrawResult(ReconCheckResultsParams reconCheckResultsParams, ReconBillUserVo reconBillUserVo) {
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        if (!globalBillConfig.isSaveReconWithdrawResult()) {
            return;
        }
        ReconWithdrawCheckRecord reconWithdrawCheckRecord = ReconWithdrawCheckRecord.builder()
                .businessSource(reconCheckResultsParams.getBusinessSource())
                .userId(reconBillUserVo.getUserId())
                .orderId(reconCheckResultsParams.getOrderId())
                .traceId(Objects.requireNonNullElse(MdcContext.currentTraceId(), "new" + MdcUtils.getNewTraceId()))
                .recordCode(reconCheckResultsParams.getRecordCode())
                .requestTime(new Date(reconCheckResultsParams.getRequestDate()))
                .responseTime(new Date(reconBillUserVo.getResponseTime()))
                .withdrawCheckResultCode(reconBillUserVo.getWithdrawCheckResultCode())
                .withdrawCheckResultName(reconBillUserVo.getWithdrawCheckResultName())
                .isPass(Boolean.TRUE.equals(reconBillUserVo.isPass()) ? 1 : 0)
                .build();
        if (Objects.nonNull(reconBillUserVo.getCheckOkTime())) {
            reconWithdrawCheckRecord.setCheckOnTime(new Date(reconBillUserVo.getCheckOkTime()));
        }

        reconWithdrawRecordService.save(reconWithdrawCheckRecord);
    }

    @Override
    public BigDecimal getUserBeginAssets(Long userId, Long snapshotTime, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> ratesMap, GlobalBillConfig billConfig) {
        BigDecimal userBeginAssets = null;
        if (billConfig.isRedisUserBeginAssets()) {
            // 缓存请求总数
            MetricsUtil.counter(MetricsUtil.METRICS_GAUGE_USER_BEGIN_ASSETS_TOTAL);
            Map<Integer, BigDecimal> coinAssetsMap = userBeginAssetsRedisService.getUserBeginAssets(userId, snapshotTime);
            if (CollectionUtil.isNotEmpty(coinAssetsMap)) {
                userBeginAssets = BillCoinCalculationUtils.calculateCoinUsdt(coinAssetsMap, ratesMap);
                // 缓存命中总数
                MetricsUtil.counter(MetricsUtil.METRICS_GAUGE_USER_BEGIN_ASSETS_HIT);
            }
        }
        ReconTotalAssetsDetailVo userBeginAssetsDetail = reconUserAssetsSnapShotService.listUserAssetsBySnapShotTime(userId, snapshotTime, allCoinsMap, ratesMap, billConfig.getCheckProfitSubSystemList(), null, Boolean.FALSE);
        if (Objects.isNull(userBeginAssets)) {
            userBeginAssets = userBeginAssetsDetail.getTotalBalance();
            if (billConfig.isRedisUserBeginAssets()) {
                userBeginAssetsRedisService.addUserBeginAssets(userId, snapshotTime, userBeginAssetsDetail, billConfig);
            }
        } else {
            if (userBeginAssets.compareTo(userBeginAssetsDetail.getTotalBalance()) != 0) {
                log.info("getUserBeginAssets userId:{}, snapshotTime:{}, redisUsdt:{}, DBUsdt:{}", userId, snapshotTime, userBeginAssets, userBeginAssetsDetail.getTotalBalance());
                userBeginAssets = userBeginAssetsDetail.getTotalBalance();
            }
        }
        return userBeginAssets;
    }

    /**
     * 根据businessCode和businessConfig获取CheckVo
     *
     * @param businessCode
     * @param businessConfig
     * @return
     */
    private CheckVo getCheckVo(Integer businessCode, List<CheckVo> businessConfig) {
        Map<Integer, CheckVo> businessConfigMap = businessConfig.stream().collect(Collectors.toMap(CheckVo::getBusinessCode, a -> a, (k1, k2) -> k1));
        CheckVo checkVo = businessConfigMap.get(businessCode);
        if (Objects.isNull(checkVo)) {
            log.error("check alarm result, this businessCode is [{}], but it's detail config is null ", businessCode);
            checkVo = businessConfigMap.get(BusinessTypeEnum.BUSINESS_NORMAL.getBusinessCode());
        }
        return checkVo;
    }

    /**
     * 获取用户盈利
     *
     * @param userIds
     * @return
     */
    private Map<Long, AccountProfitDTO> getUserAccountProfitMap(Long parentUserId, List<Long> userIds,
                                                                Map<Integer, String> allCoinsMap,
                                                                Map<Integer, PriceVo> rates,
                                                                List<String> subSystemList,
                                                                Integer timePeriod,
                                                                Date beginDate,
                                                                Date requestDate) {
        GlobalBillConfig billConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        Map<Long, AccountProfitDTO> accountProfitMap = new ConcurrentHashMap<>();
        assetUserProfitTaskManager.forEachSubmitBatchAndWait(userIds, (Long userId) -> {
            Map<Integer, BigDecimal> allFlowChangeSum = new ConcurrentHashMap<>();
            Map<Integer, BigDecimal> inOutFlowChangeSum = new ConcurrentHashMap<>();
            BigDecimal beginBalance = getUserBeginAssets(userId, beginDate.getTime(), allCoinsMap, rates, billConfig);
            log.info("checkProfitAccount userAssetsSnapShotService.listUserAssetsBySnapShotTime requestTime:{}, parentUserId:{} userId:{}, timePeriod:{}, beginBalance:{}", beginDate.getTime(), parentUserId, userId, timePeriod, beginBalance);

            // 2&3、查询用户对账流水
            Stopwatch stopwatch2 = Stopwatch.createStarted();
            AccountProfitDTO accountProfitDTO = listUserBillFlowsV2(userId, beginDate, requestDate, allFlowChangeSum, inOutFlowChangeSum, subSystemList);
            accountProfitDTO.setBeginBalance(beginBalance);
            log.info("checkProfitAccount listUserBillFlows requestTime:{}, parentUserId:{} userId:{}, timePeriod:{}, checkProfitSubSystemList:{} accountProfitDTO:{} time:{}", requestDate.getTime(), parentUserId, userId, timePeriod, JSONObject.toJSONString(subSystemList), JSON.toJSONString(accountProfitDTO), stopwatch2.stop());
            accountProfitMap.put(userId, accountProfitDTO);
        });
        return accountProfitMap;
    }
}