package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.dao.bill.entity.BillCoinTypeProperty;
import com.upex.reconciliation.service.dao.mapper.OldBillCoinTypePropertyMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Service
public class OldBillCoinTypeService {


    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "oldBillCoinTypePropertyMapper")
    private OldBillCoinTypePropertyMapper oldBillCoinTypePropertyMapper;


    public List<BillCoinTypeProperty> selectRangeCheckTimeRecord(Integer coinId,
                                                             Date checkTime,
                                                             Byte accountType,
                                                             String accountParam) {
        return dbHelper.doDbOpInBillMaster(() -> oldBillCoinTypePropertyMapper.selectRange(coinId, checkTime, accountType, accountParam));
    }


    public BillCoinTypeProperty selectByCoinBizTypeCheckTimeRecord(Integer coinId,
                                                                 String bizType,
                                                                 Date checkTime,
                                                                 Byte accountType,
                                                                 String accountParam) {
        return dbHelper.doDbOpInBillMaster(() -> oldBillCoinTypePropertyMapper.selectByCoinBizTypeCheckTimeRecord(coinId, bizType,checkTime, accountType, accountParam));
    }


    public List<BillCoinTypeProperty> selectLastByCoinIds(Integer accountType,
                                                                   String accountParam,
                                                                   List<Integer> coinIds,
                                                                   Date checkTime) {
        return dbHelper.doDbOpInBillMaster(() -> oldBillCoinTypePropertyMapper.selectLastByCoinIds(accountType, accountParam, coinIds, checkTime));
    }



}
