package com.upex.reconciliation.service.business;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.module.impl.BillTimeSliceCheckModule;
import com.upex.reconciliation.service.business.module.impl.BillUserCheckModule;
import com.upex.reconciliation.service.common.constants.enums.CapitalInitBusinessTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.ProfitTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.QueryUserAssetsSceneEnum;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.BillTimeSliceDTO;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.service.impl.BillCoinUserPropertySnapshotService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.reconciliation.service.utils.RedisUtil;
import com.upex.reconciliation.service.utils.TimeSliceCalcUtils;
import com.upex.utils.task.TaskManager;
import com.upex.utils.task.TaskVoidBatchResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 机器重启，从db拉数据到内存的流程
 * 1、
 */
@Service
@Slf4j
public class RebootDataService {
    @Resource
    private BillEngineManager billEngineManager;
    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;
    @Resource
    private BillCoinUserPropertySnapshotService billCoinUserPropertySnapshotService;
    @Resource
    private BillCoinTypePropertyService billCoinTypePropertyService;
    @Resource
    private BillUserPositionService billUserPositionService;
    @Resource
    private BillCoinPropertyService billCoinPropertyService;
    @Resource
    private BillCoinTypeUserPropertyService billCoinTypeUserPropertyService;
    @Resource
    private BillConfigService billConfigService;
    @Resource
    private BillSymbolPropertyService billSymbolPropertyService;
    @Resource
    private BillSymbolCoinPropertyService billSymbolCoinPropertyService;
    @Resource
    private BillCapitalInitPropertyService billCapitalInitPropertyService;
    @Resource(name = "taskManager")
    private TaskManager taskManager;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    @Resource
    private BillContractProfitTransferService billContractProfitTransferService;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private BillTransferFeeCoinDetailService billTransferFeeCoinDetailService;

    /**
     * 区分不同业务线，进行reboot后的数据重置
     * 无需传入bizTime和addTime
     *
     * @param bizTime
     * @param accountType
     */
    public void baseRebootDataRestore(Long bizTime, Byte accountType) {
        //  个人的内存维护做成增量维护的模式 ：step1 查db，如果没有；  step2 查业务系统
        //  仅时间片做这个全量初始化: 直接全量捞db放进内存
        // 获取 billConfig
        // reboot逻辑和消费消息不冲突，两边可以同时做
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        String accountParam = apolloBizConfig.getAccountParam();
        BillConfig billConfig = null;
        if (bizTime != null) {
            // 有时间就按照指定时间加载
            billConfig = billConfigService.selectByTypeAndParamAndCheckTime(accountType, accountParam, new Date(bizTime));
        } else {
            billConfig = billConfigService.selectByTypeAndParam(accountType, accountParam);
        }
        if (billConfig == null) {
            throw new RuntimeException("RebootDataService.baseRebootDataRestore  billConfig is null accountType:" + accountType);
        }
        log.info("baseInitDataRestore accountType:{} billConfig:{}", accountType, JSONObject.toJSONString(billConfig));
        StopWatch stopWatch = new StopWatch();
        // 时间片module加载过程
        Date checkOkTime = billConfig.getCheckOkTime();
        BillLogicGroup billLogicGroup = billEngineManager.getBillLogicGroup(accountType);
        // 数据库里有的全量期初
        // 时间片纬度加载
        BillTimeSliceCheckModule timeSliceModule = (BillTimeSliceCheckModule) billLogicGroup.getTimeSliceModule();
        BillUserCheckModule userCheckModule = (BillUserCheckModule) billLogicGroup.getUserCheckModule();
        BillTimeSliceDTO billTimeSliceDTO = new BillTimeSliceDTO(billConfig);
        billTimeSliceDTO.setAccountTypeEnum(accountTypeEnum);
        stopWatch.start("billCoinService.selectCheckTimeRecord");
        Map<Integer, BillCoinProperty> coinPropertyMap = billTimeSliceDTO.getCoinPropertyMap();
        List<BillCoinProperty> billCoinPropertyList = billCoinPropertyService.selectCheckTimeRecord(Integer.valueOf(accountType), accountParam, checkOkTime);
        for (BillCoinProperty billCoinProperty : billCoinPropertyList) {
            if ((StringUtils.isEmpty(billCoinProperty.getParams()) || "{}".equals(billCoinProperty.getParams()))) {
                Map<String, Object> param = new HashMap<>();
                param.put("fee", BigDecimal.ZERO);
                param.put("changeFee", BigDecimal.ZERO);
                billCoinProperty.setParams(JSON.toJSONString(param));
            }
            coinPropertyMap.put(billCoinProperty.getCoinId(), billCoinProperty);
        }
        stopWatch.stop();

        stopWatch.start("billCoinTypeService.selectCheckTimeRecordPage");
        Map<String, BillCoinTypeProperty> coinTypePropertyMap = billTimeSliceDTO.getCoinTypePropertyMap();
        Long minId = 0L;
        while (true) {
            List<BillCoinTypeProperty> coinTypePropertyList = billCoinTypePropertyService.selectCheckTimeRecordPage(Integer.valueOf(accountType), accountParam, checkOkTime, minId, apolloBizConfig.getSingleSqlMaxSize());
            if (CollectionUtils.isEmpty(coinTypePropertyList)) {
                break;
            }
            for (BillCoinTypeProperty billCoinTypeProperty : coinTypePropertyList) {
                if ((StringUtils.isEmpty(billCoinTypeProperty.getParams()) || "{}".equals(billCoinTypeProperty.getParams()))) {
                    Map<String, Object> param = new HashMap<>();
                    param.put("fee", BigDecimal.ZERO);
                    param.put("changeFee", BigDecimal.ZERO);
                    billCoinTypeProperty.setParams(JSON.toJSONString(param));
                }
                coinTypePropertyMap.put(BillCoinTypeProperty.generateKey(billCoinTypeProperty.getCoinId(), billCoinTypeProperty.getBizType()), billCoinTypeProperty);
            }
            minId = coinTypePropertyList.get(coinTypePropertyList.size() - 1).getId();
        }
        stopWatch.stop();

        // 加载用户资产信息
        stopWatch.start("billCoinUserPropertyService.cursorSelectLastUserIdByCheckTime");
        Date startTime = DateUtil.addMinute(checkOkTime, apolloBizConfig.getRebootLoadUserLastMinute());
        List<Long> userIdList = billCoinUserPropertyService.cursorSelectLastUserIdByCheckTime(accountType, accountParam, startTime);
        log.info("billCoinUserPropertyService.cursorSelectLastUserIdByCheckTime accountType:{} size:{}", accountType, userIdList.size());
        stopWatch.stop();
        stopWatch.start("userCheckModule.loadUserCoinProperty");
        List<List<Long>> partionUserIdLists = Lists.partition(userIdList, 20);
        TaskVoidBatchResult queryResultIsEmpty = taskManager.forEachSubmitBatchAndWait(partionUserIdLists, (List<Long> uidList) -> {
            for (Long userId : uidList) {
                List<BillCoinUserProperty> billCoinUserPropertyList = accountAssetsServiceFactory.getBillCoinUserPropertyList(accountType, userId, checkOkTime.getTime(), QueryUserAssetsSceneEnum.INIT);
                userCheckModule.loadUserCoinProperty(userId, billCoinUserPropertyList);
            }
        }, apolloBizConfig.getSaveDataQueryUserCoinConcurrence());
        if (queryResultIsEmpty != null && queryResultIsEmpty.getFails().size() > 0) {
            log.error("baseRebootDataRestore selectLastUserIdByCheckTime.getFails() accountType:{} size={}", accountType, queryResultIsEmpty.getFails().size());
            throw new RuntimeException("baseRebootDataRestore selectLastUserIdByCheckTime.getFails() accountType:" + accountType + " size=" + queryResultIsEmpty.getFails().size());
        }
        stopWatch.stop();

        // 合约加载仓位信息
        if (accountTypeEnum.haveUserPosition()) {
            // 加载仓位信息 过滤仓位为0 数据
            stopWatch.start("billUserPositionService.selectRangeCheckTimeRecordPageQuery");
            minId = 0L;
            List<BillUserPosition> allBillUserPositionList = new ArrayList<>();
            // 删除billConfig
            while (true) {
                List<BillUserPosition> billUserPositionList = billUserPositionService.selectRangeCheckTimeRecordPageQuery(Integer.valueOf(accountType), accountParam, checkOkTime, minId, apolloBizConfig.getSingleSqlMaxSize());
                if (CollectionUtils.isEmpty(billUserPositionList)) {
                    break;
                }
                minId = billUserPositionList.get(billUserPositionList.size() - 1).getId();
                allBillUserPositionList.addAll(billUserPositionList.stream().filter(item -> (item.getLCount().compareTo(BigDecimal.ZERO) > 0 || item.getSCount().compareTo(BigDecimal.ZERO) > 0)).collect(Collectors.toList()));
            }
            billTimeSliceDTO.setBillUserPositionList(allBillUserPositionList);
            billTimeSliceDTO.setBillUserPositionMap(BillUserPosition.listToMapSumSymbolUserCoin(allBillUserPositionList, false));
            // 加载 symbol 已实现 纬度信息
            List<BillSymbolProperty> billSymbolPropertyList = billSymbolPropertyService.selectLastRecords(checkOkTime, Integer.valueOf(accountType), accountParam);
            billTimeSliceDTO.setSymbolPropertyMap(billSymbolPropertyList.stream().collect(Collectors.toMap(BillSymbolProperty::getSymbolId, Function.identity(), (key1, key2) -> key2)));
            List<BillSymbolCoinProperty> billSymbolCoinProperties = billSymbolCoinPropertyService.selectListByCheckTime(accountType, accountParam, checkOkTime);
            billTimeSliceDTO.setSymbolCoinPropertyMap(billSymbolCoinProperties.stream().collect(Collectors.toMap(BillSymbolCoinProperty::groupBySymbolCoin, Function.identity(), (key1, key2) -> key2)));
            // 加载期初已实现
            List<BillCapitalInitProperty> contractSymbolRealizedInitList = billCapitalInitPropertyService.selectRecords(String.valueOf(accountType), accountParam, CapitalInitBusinessTypeEnum.CONTRACT_SYMBOL_REALIZED.getCode());
            Map<String, BillCapitalInitProperty> contractSymbolRealizedInitMap = CollectionUtils.emptyIfNull(contractSymbolRealizedInitList).stream().collect(Collectors.toMap(BillCapitalInitProperty::getSymbolId, Function.identity(), (key1, key2) -> key2));
            billTimeSliceDTO.setSymbolrealizedInitPropertyMap(contractSymbolRealizedInitMap);
            log.info("baseRebootDataRestore setSymbolrealizedInitPropertyMap accountType:{} db data:{} map data:{}", accountTypeEnum.getCode(), JSON.toJSONString(contractSymbolRealizedInitList), JSON.toJSONString(billTimeSliceDTO.getSymbolrealizedInitPropertyMap()));
            stopWatch.stop();
            // 加载盈亏换汇数据
            List<BillContractProfitCoinDetail> lastBillAllContractProfitCoinDetail = billContractProfitTransferService.getAllBillContractProfitCoinDetailList(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), Lists.newArrayList(ProfitTypeEnum.COIN_PROFIT.getCode(), ProfitTypeEnum.SYMBOL_PROFIT.getCode()), checkOkTime);
            billTimeSliceDTO.setBillContractProfitCoinDetailList(lastBillAllContractProfitCoinDetail);
        }
        // 加载redis订单乱序数据
        Map<Object, Object> orderNoBizTimeRedisMap = redisTemplate.opsForHash().entries(RedisUtil.getReversedOrderIdHashKey(accountType.byteValue()));
        if (MapUtil.isNotEmpty(orderNoBizTimeRedisMap)) {
            for (Map.Entry<Object, Object> entry : orderNoBizTimeRedisMap.entrySet()) {
                userCheckModule.getOrderNoBizTimeRedisCache().put(entry.getKey().toString(), new Date(Long.parseLong(entry.getValue().toString())));
            }
        }
        log.info("baseRebootDataRestore load orderNoBizTimeRedisMap accountType:{} redis orderNoBizTimeRedisMap size:{}", accountType, orderNoBizTimeRedisMap.size());
        // 加载动账手续费
        if (apolloBizConfig.isTransferFeeOpen()) {
            List<BillTransferFeeCoinDetail> billTransferFeeCoinDetails = billTransferFeeCoinDetailService.selectCheckTimeRecord(accountType, accountParam, checkOkTime);
            billTransferFeeCoinDetails.forEach(item -> {
                billTimeSliceDTO.getBillTransferFeeCoinDetailMap().put(item.groupByFeedTypeCoin(), item);
            });
        }

        timeSliceModule.setLastBillTimeSliceDTO(billTimeSliceDTO);
        timeSliceModule.setLastSaveBillTimeSliceDTO(billTimeSliceDTO);
        timeSliceModule.setReboootCheckOkTime(billConfig.getCheckOkTime().getTime());
        // 业务线重启 默认加载一个时间片
        long nextTimeSlice = TimeSliceCalcUtils.getNextTimeSlice(checkOkTime.getTime(), apolloBizConfig.getTimeSliceSize());
        SortedMap<Long, BillTimeSliceDTO> startTimeSliceDTOMap = timeSliceModule.getStartTimeSliceDTOMap();
        startTimeSliceDTOMap.computeIfAbsent(nextTimeSlice, v -> {
            BillTimeSliceDTO timeSliceDTO = new BillTimeSliceDTO(BillConfig.builder()
                    .checkOkTime(new Date(nextTimeSlice))
                    .consumeOffset(billTimeSliceDTO.getBillConfig().getConsumeOffset())
                    .syncPos(billTimeSliceDTO.getBillConfig().getSyncPos())
                    .build());
            timeSliceDTO.setAccountTypeEnum(AccountTypeEnum.toEnum(apolloBizConfig.getAccountType()));
            return timeSliceDTO;
        });
        BillTimeSliceDTO nextTimeSliceDTO = startTimeSliceDTOMap.get(nextTimeSlice);
        log.info("finished load memory data processing accountType:{} nextTimeSlice:{} nextTimeSliceDTO is null:{} stopwath:{}", accountType, DateUtil.longToDate(nextTimeSlice), (nextTimeSliceDTO == null), stopWatch.prettyPrint());
    }
}
