package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import com.upex.commons.support.hmac.HmacEncryptUtil;
import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import lombok.Data;

@Data
public class DepositeAddressInnerRes  {

    /**
     *  "coin":"USDT",
     *                 "address":"xx",
     *                 "tag":"",
     *                 "isDefault":true
     */

    private String coin;
    private String address;
    private String tag;
    private Integer isDefault;


}
