package com.upex.reconciliation.service.service.client.cex.dto.req.common;

import lombok.Data;

import java.util.Date;

@Data
public class UniversalTransferRecordReq extends CommonReq{
    private Date startTime;
    private Date endTime;

    private String type;

    private Date checkSyncTime;


    public UniversalTransferRecordReq(Integer cexType, String cexUserId, String apiKey, String privateKey, String type, Date startTime, Date endTime,Date checkSyncTime) {
        super(cexType, cexUserId, apiKey, privateKey);
        this.startTime = startTime;
        this.endTime = endTime;
        this.type = type;
        this.checkSyncTime = checkSyncTime;
    }

}
