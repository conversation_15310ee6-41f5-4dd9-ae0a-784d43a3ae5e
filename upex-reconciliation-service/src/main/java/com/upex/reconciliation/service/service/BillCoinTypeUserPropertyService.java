package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.BillCheckService;
import com.upex.reconciliation.service.business.createtablebyroute.BillCoinTypeUserPropertyTableCreator;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty;
import com.upex.reconciliation.service.dao.mapper.BillCoinTypeUserPropertyMapper;
import com.upex.reconciliation.service.model.dto.MaxMinIdDTO;
import com.upex.reconciliation.service.utils.BeanCopierUtil;
import com.upex.reconciliation.service.utils.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;


@Service
public class BillCoinTypeUserPropertyService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billCoinTypeUserPropertyMapper")
    private BillCoinTypeUserPropertyMapper billCoinTypeUserPropertyMapper;
    @Resource
    private BillCoinTypeUserPropertyTableCreator billCoinTypeUserPropertyTableCreator;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;

    public List<BillCoinTypeUserProperty> listAllChange(String tableSuffix,
                                                        Integer accountType,
                                                        String accountParam,
                                                        Integer coinId,
                                                        Long userId,
                                                        Date startTime,
                                                        Date endTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.listAllChange(tableSuffix, accountType, accountParam, coinId, userId, startTime, endTime));

    }


    public List<BillCoinTypeUserProperty> selectByUserBeforeTime(Long userId,
                                                                 Date checkTime,
                                                                 Integer accountType) {
        String startTableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.selectByUserBeforeTime(userId, checkTime, String.valueOf(accountType), startTableSuffix));
    }


    public List<BillCoinTypeUserProperty> selectUserTypeByTimeAndTypeAndCoinId(Long userId,
                                                                               List<String> bizTypes,
                                                                               Integer coinId,
                                                                               Date beginTime,
                                                                               Date endTime,
                                                                               Integer accountType) {

        // 跨天问题
        String startTableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), beginTime);
        String endTableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), endTime);
        if (StringUtils.equals(startTableSuffix, endTableSuffix)) {
            return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.selectUserTypeByTimeAndTypeAndCoinId(userId, bizTypes, coinId, beginTime, endTime, accountType, endTableSuffix));
        } else {
            List<BillCoinTypeUserProperty> resultList = new ArrayList<>();
            Date everyDayDate = beginTime;
            while (DateUtil.getGapDays(everyDayDate, endTime) >= 0) {
                String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), everyDayDate);
                List<BillCoinTypeUserProperty> everyList = dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.selectUserTypeByTimeAndTypeAndCoinId(userId, bizTypes, coinId, beginTime, endTime, accountType, tableSuffix));
                resultList.addAll(everyList);
                everyDayDate = new Date(everyDayDate.getTime() + BillConstants.ONE_DAY_MIL_SEC);
            }
            return resultList;
        }
    }


    public List<BillCoinTypeUserProperty> listAllChangeGroupByCoinBizType(String tableSuffix,
                                                                          Integer accountType,
                                                                          Long userId,
                                                                          Date startTime,
                                                                          Date endTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.listAllChangeGroupByCoinBizType(tableSuffix, accountType, userId, startTime, endTime));

    }

    public BillCoinTypeUserProperty selectTime(Long userId, Date checkTime, Integer accountType) {
        String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.selectTime(userId, checkTime, accountType, tableSuffix));
    }

    public List<BillCoinTypeUserProperty> selectByUserAndTime(Long userId, Date checkTime, String accountType) {
        String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.selectByUserAndTime(userId, checkTime, accountType, tableSuffix));
    }


    public List<BillCoinTypeUserProperty> selectBetweenTime(Long userId, Date beginTime, Date endTime, Integer accountType) {
        // 跨天问题
        String startTableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), beginTime);
        String endTableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), endTime);
        if (StringUtils.equals(startTableSuffix, endTableSuffix)) {
            return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.listAllChangeGroupByCoinBizType(endTableSuffix, Integer.valueOf(accountType), userId, beginTime, endTime));
        } else {
            List<BillCoinTypeUserProperty> resultList = new ArrayList<>();
            Date everyDayDate = beginTime;
            while (DateUtil.getGapDays(everyDayDate, endTime) >= 0) {
                String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), everyDayDate);
                List<BillCoinTypeUserProperty> everyList = dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.listAllChangeGroupByCoinBizType(tableSuffix, Integer.valueOf(accountType), userId, beginTime, endTime));
                resultList.addAll(everyList);
                everyDayDate = new Date(everyDayDate.getTime() + BillConstants.ONE_DAY_MIL_SEC);
            }
            return resultList;
        }
    }

    /**
     * 跨天查询
     *
     * @param userId
     * @param accountType
     * @param bizTypeList
     * @param coinIdsList
     * @param startTime
     * @param endTime
     * @param pageSize
     * @param consumer
     */
    public void selectCrossDayByPage(Long userId, Integer accountType, List<String> bizTypeList, List<Integer> coinIdsList, Date startTime, Date endTime, Integer pageSize, Consumer<List<BillCoinTypeUserProperty>> consumer) {
        // 跨天问题
        String startTableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), startTime);
        String endTableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), endTime);
        if (StringUtils.equals(startTableSuffix, endTableSuffix)) {
            this.selectByPage(startTableSuffix, userId, accountType, bizTypeList, coinIdsList, startTime, endTime, pageSize, consumer);
        } else {
            Date everyDayDate = startTime;
            while (DateUtil.getGapDays(everyDayDate, endTime) >= 0) {
                String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), everyDayDate);
                this.selectByPage(tableSuffix, userId, accountType, bizTypeList, coinIdsList, startTime, endTime, pageSize, consumer);
                everyDayDate = new Date(everyDayDate.getTime() + BillConstants.ONE_DAY_MIL_SEC);
            }
        }
    }

    private void selectByPage(String tableSuffix, Long userId, Integer accountType, List<String> bizTypeList, List<Integer> coinIdsList, Date startTime, Date endTime, Integer pageSize, Consumer<List<BillCoinTypeUserProperty>> consumer) {
        Long minId = null;
        List<BillCoinTypeUserProperty> coinTypeUserPropertyList;
        do {
            coinTypeUserPropertyList = this.selectSinglePage(tableSuffix, userId,
                    startTime, endTime, accountType, bizTypeList, coinIdsList, minId, pageSize);
            consumer.accept(coinTypeUserPropertyList);
            if (CollectionUtils.isEmpty(coinTypeUserPropertyList) || coinTypeUserPropertyList.size() < pageSize) {
                break;
            }
            minId = coinTypeUserPropertyList.get(coinTypeUserPropertyList.size() - 1).getId();
        } while (CollectionUtils.isNotEmpty(coinTypeUserPropertyList));
    }

    private List<BillCoinTypeUserProperty> selectSinglePage(String tableSuffix, Long userId, Date beginTime, Date endTime, Integer accountType, List<String> bizTypes, List<Integer> coinIds, Long minId, Integer pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.selectByPage(tableSuffix, Integer.valueOf(accountType), bizTypes, coinIds, userId, beginTime, endTime, minId, pageSize));
    }

    /**
     * 创建表 保持不变
     *
     * @param accountType
     * @param tableSuffix
     * @return
     */
    private Integer createTable(Byte accountType,
                                String tableSuffix) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.createTable(accountType, tableSuffix));
    }


    /**
     * 创建表
     *
     * @param accountType
     * @param day
     */
    public void createTableForDay(Byte accountType,
                                  Integer day) {
        if (day != null && day > 0) {
            Date nowDate = new Date();
            for (int i = 0; i < day; i++) {
                String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), DateUtil.addDay(nowDate, i));
                createTable(accountType, tableSuffix);
            }
        }
    }

    /**
     * 删除快照
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @param batchSize
     * @return
     */
    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date checkTime, Long batchSize) {
        String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.deleteByCheckTime(accountType, accountParam, checkTime, batchSize, tableSuffix));
    }

    /**
     * 查询资产数据
     *
     * @param accountType
     * @param checkTime
     * @param startId
     * @param pageSize
     * @return
     */
    public List<BillCoinTypeUserProperty> selectByUserIdAndGtCheckTime(Byte accountType,
                                                                       Date checkTime,
                                                                       Long startId,
                                                                       Long userId,
                                                                       Integer pageSize) {
        String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.selectByUserIdAndGtCheckTime(accountType, tableSuffix, checkTime, startId, userId, pageSize));
    }

    public Boolean tableExists(Byte accountType, String accountParam, Date checkTime) {
        String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        String tablePrefix = String.format("bill_coin_type_user_property_%s_%s", accountType, tableSuffix);
        List<String> tables = dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.getTables(accountType, accountParam, tablePrefix));
        return tables.size() > 0;
    }

    /**
     * 查询资产数据
     *
     * @param accountType
     * @param beginCheckTime
     * @param endCheckTime
     * @return
     */
    public List<BillCoinTypeUserProperty> selectSumByCheckTime(Byte accountType, Long beginCheckTime, Long endCheckTime) {
        Map<Long, Map<Integer, Map<String, BillCoinTypeUserProperty>>> userCoinBizTypeProfitMap = new ConcurrentHashMap<>();
        BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountType);
        for (Long startTime = beginCheckTime; startTime <= endCheckTime; startTime += BillConstants.FIVE_MINE_MIL_SEC) {
            List<BillCoinTypeUserProperty> billCoinTypeUserProperties = this.selectByCheckTime(accountType, new Date(startTime));
            billCoinTypeUserProperties.forEach(coinTypeUserProperty -> {
                userCoinBizTypeProfitMap.computeIfAbsent(coinTypeUserProperty.getUserId(), k -> new ConcurrentHashMap<>())
                        .computeIfAbsent(coinTypeUserProperty.getCoinId(), k -> new ConcurrentHashMap<>())
                        .compute(coinTypeUserProperty.getBizType(), (k, oldValue) -> {
                            billCheckService.cleanAndSetAssetsProperty(coinTypeUserProperty, accountType);
                            BillCoinTypeUserProperty property = null;
                            if (oldValue != null) {
                                property = oldValue;
                                property.setChangeProp1(property.getChangeProp1().add(coinTypeUserProperty.getChangeProp1()));
                                property.setChangeProp2(property.getChangeProp2().add(coinTypeUserProperty.getChangeProp2()));
                                property.setChangeProp3(property.getChangeProp3().add(coinTypeUserProperty.getChangeProp3()));
                                property.setChangeProp4(property.getChangeProp4().add(coinTypeUserProperty.getChangeProp4()));
                                property.setChangeProp5(property.getChangeProp5().add(coinTypeUserProperty.getChangeProp5()));
                                property.setChangeProp6(property.getChangeProp6().add(coinTypeUserProperty.getChangeProp6()));
                                property.setChangeProp7(property.getChangeProp7().add(coinTypeUserProperty.getChangeProp7()));
                                property.setChangeProp8(property.getChangeProp8().add(coinTypeUserProperty.getChangeProp8()));
                            } else {
                                property = BeanCopierUtil.copyProperties(coinTypeUserProperty, BillCoinTypeUserProperty.class);
                            }
                            return property;
                        });
            });
        }

        // 合并
        List<BillCoinTypeUserProperty> resultList = new ArrayList<>();
        userCoinBizTypeProfitMap.forEach((userId, coinIdMap) -> {
            coinIdMap.forEach((coinId, bizTypeMap) -> {
                bizTypeMap.forEach((bizType, billCoinTypeUserProperty) -> {
                    resultList.add(billCoinTypeUserProperty);
                });
            });
        });
        return resultList;
    }

    public List<BillCoinTypeUserProperty> selectByCheckTime(Byte accountType, Date checkTime) {
        String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        MaxMinIdDTO maxMinIdDTO = selectMaxMinId(accountType, checkTime);
        if (maxMinIdDTO == null) {
            return Collections.emptyList();
        }
        List<Long[]> idSegments = maxMinIdDTO.buildIdSegments(500L);
        List<BillCoinTypeUserProperty> resultList = new ArrayList<>();
        for (Long[] idSegment : idSegments) {
            List<BillCoinTypeUserProperty> billCoinTypeUserPropertyList = dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.selectByCheckTimeAndIdSegment(accountType, checkTime, tableSuffix, idSegment[0], idSegment[1]));
            resultList.addAll(billCoinTypeUserPropertyList);
        }
        return resultList;
    }

    /**
     * 获取id区间
     *
     * @param accountType
     * @param checkTime
     * @return
     */
    public MaxMinIdDTO selectMaxMinId(Byte accountType, Date checkTime) {
        String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(String.valueOf(accountType), checkTime);
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyMapper.selectMaxMinId(accountType, tableSuffix, checkTime));
    }
}
