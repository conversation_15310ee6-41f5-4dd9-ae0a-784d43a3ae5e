package com.upex.reconciliation.service.consumer;

import cn.hutool.core.collection.ConcurrentHashSet;
import com.alibaba.otter.canal.client.kafka.MessageDeserializer;
import com.google.common.base.Stopwatch;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.consumer.kafka.KafkaConsumerLifecycle;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 对账消费者模型
 *
 * <AUTHOR>
 * @Date 2025/6/17
 */
@Slf4j
public class BillKafkaConsumer<K, V> implements KafkaConsumerLifecycle {

    protected volatile boolean running = true;
    protected boolean multiThread;
    protected KafkaConsumerConfig kafkaConsumerConfig;
    protected String topic;
    protected Integer partitionNum;
    protected Map<String, Object> consumerConfig;
    protected Set<Integer> closeConsumerPartition = new ConcurrentHashSet<>();
    protected Consumer<ConsumerRecord<K, V>> messageHandler;
    protected Map<Integer, KafkaConsumer<K, V>> partitionConsumerMap = new HashMap<>();
    private AlarmNotifyService alarmNotifyService;

    public BillKafkaConsumer(ReconciliationSpringContext context, String kafkaServerAddr, KafkaConsumerConfig kafkaConsumerConfig, boolean multiThread, Consumer<ConsumerRecord<K, V>> messageHandler) {
        this.alarmNotifyService = context.getAlarmNotifyService();
        this.multiThread = multiThread;
        this.partitionNum = Objects.isNull(kafkaConsumerConfig.getPartitionNum()) ? 1 : kafkaConsumerConfig.getPartitionNum();
        this.topic = kafkaConsumerConfig.getTopicName();
        this.kafkaConsumerConfig = kafkaConsumerConfig;
        consumerConfig = new HashMap<>();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServerAddr);
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MessageDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, kafkaConsumerConfig.getConsumerGroupId());
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, kafkaConsumerConfig.getKafkaBatchSize());
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
        this.messageHandler = messageHandler;
    }

    @Override
    public void run() {
        try {
            // 初始化
            init();
            log.info("BillKafkaConsumer init finished");
            if (multiThread) {
                partitionConsumerMap.forEach((partition, kafkaConsumer) -> new Thread(() -> startConsume(partition, kafkaConsumer), getThreadPrefixName()).start());
            } else {
                partitionConsumerMap.forEach((partition, kafkaConsumer) ->startConsume(partition, kafkaConsumer));
            }
        } catch (Exception e) {
            alarmNotifyService.alarmException(topic + "消费者异常");
            log.error("BillKafkaConsumer consumer run Exception:", e);
        }
    }

    public void init() {
        // 创建监听器，数量和partition数量保持一致
        for (int i = 0; i < partitionNum; i++) {
            KafkaConsumer<K, V> currentConsumer = new KafkaConsumer<>(consumerConfig);
            List<TopicPartition> partitions = new ArrayList<>();
            partitions.add(new TopicPartition(topic, i));
            currentConsumer.assign(partitions);
            partitionConsumerMap.put(i, currentConsumer);
        }
    }

    /**
     * 启动消费
     */
    private void startConsume(Integer partition, KafkaConsumer<K, V> consumer) {
        consumer.subscribe(Collections.singletonList(topic));
        log.info("BillKafkaConsumer startConsume partition {}", partition);
        Stopwatch stopwatch;
        TopicPartition topicPartition = new TopicPartition(topic, partition);
        Map<TopicPartition, OffsetAndMetadata> partitionOffsetMap = new HashMap<>();
        Integer retryCount = 0;
        Long offset = null;
        while (running) {
            try {
                kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig(KafkaTopicEnum.RECON_SYNC_FINANCIAL_ACCOUNT_TOPIC);
                if (!kafkaConsumerConfig.getIsOpen() || (retryCount >= kafkaConsumerConfig.getRetryCount())) {
                    TimeUnit.SECONDS.sleep(kafkaConsumerConfig.getSleepTime());
                    continue;
                }
                offset = null;
                // 从kafka集群中拉取消息df
                ConsumerRecords<K, V> consumerRecords = consumer.poll(3000);
                stopwatch = Stopwatch.createStarted();
                log.info("BillKafkaConsumer startConsume partition {} consumerRecords size {}", partition, consumerRecords.count());
                for (ConsumerRecord<K, V> consumerRecord : consumerRecords) {
                    offset = consumerRecord.offset();
                    if (kafkaConsumerConfig.getShowLogOpen()) {
                        log.info("offset:{}, 接收到消息：{}", offset, consumerRecord.value());
                    }
                    messageHandler.accept(consumerRecord);
                }
                log.info("BillKafkaConsumer startConsume partition {} consumerRecords size {} execute success, time:{}", partition, consumerRecords.count(), stopwatch.stop());
                // 成功提交offset
                consumer.commitSync();
                retryCount = 0;
            } catch (Exception e) {
                if (retryCount < kafkaConsumerConfig.getRetryCount()) {
                    alarmNotifyService.alarmException(topic + "消费消息异常");
                }
                // 异常时默认重试3次，当达到3次及以上时会sleep
                if (Objects.nonNull(offset) && (retryCount < kafkaConsumerConfig.getRetryCount())) {
                    retryCount++;
                    // 重置offset
                    consumer.seek(topicPartition, offset);
                    partitionOffsetMap.put(topicPartition, new OffsetAndMetadata(offset));
                    consumer.commitSync(partitionOffsetMap);
                }
                log.error("BillKafkaConsumer startConsume partitionOffsetMap:{}, error ", partitionOffsetMap, e);
            }
        }
        consumer.close();
        closeConsumerPartition.add(partition);
        log.info("BillKafkaConsumer consumer close success {}", partition);
    }

    @Override
    public void shutdown() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return closeConsumerPartition.size() != partitionNum;
    }

    @Override
    public String getThreadPrefixName() {
        if (StringUtils.isNotBlank(kafkaConsumerConfig.getAccountType())) {
            return "kafka-consumer-thread-" + kafkaConsumerConfig.getAccountType();
        }
        return kafkaConsumerConfig.getConsumerGroupId();
    }
}
