package com.upex.reconciliation.service.business;


import com.upex.reconciliation.facade.params.SysAssetsParams;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/6/30 下午3:45
 * @Description
 */
public interface SwapBillCoinAssetCalService {

    /**
     * 合约类型下，计算并获取用户的资产（重算后）
     *
     * @param userId
     * @param sysAssetsParams 获取参数中的coinId，accountType，accountParam
     * @param paramTime
     * @param isMaster
     * @return
     */
    List<BillCoinUserProperty> getAndCalSingleUserAssets(Long userId, SysAssetsParams sysAssetsParams, Date paramTime, Boolean isMaster);
    /**
     * 获取重算后的资产记录
     * @param userId
     * @param accountType
     * @param accountParam
     * @param snapshotTime
     * @return
     */
    List<BillCoinUserProperty> queryBillAssetsWithSwapMain(Long userId, Integer accountType, String accountParam, Date snapshotTime);
}
