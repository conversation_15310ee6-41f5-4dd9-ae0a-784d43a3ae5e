package com.upex.reconciliation.service.service.client.cex.dto.req.common;

import com.alibaba.fastjson.annotation.JSONField;
import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.service.client.cex.enmus.CexTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum;
import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import lombok.Data;

import javax.validation.constraints.NotNull;

import static com.upex.reconciliation.service.service.client.cex.enmus.IReconCexErrorCode.*;

@Data
public class CommonReq implements ICommonBaseReq {

    /**
     * 交易所类型（1: Binance, 2: OKX）
     */
    @NotNull(message = ILLEGAL_CEXTYPE)
    private Integer cexType;

    private String cexEmail;
    @NotNull(message = USERID_CANNOT_BENULL)
    private String cexUserId;
    /**
     * 非必填
     */
    private String apiKey;
    /**
     * 非必填
     */
//    @JSONField(serialize = false)
    private String privateKey;

    public CommonReq() {
    }


    public CommonReq(Integer cexType,  String cexUserId, String apiKey, String privateKey) {
        this.cexType = cexType;
        this.cexUserId = cexUserId;
        this.apiKey = apiKey;
        this.privateKey = privateKey;
    }

    public CommonReq(Integer cexType, String cexEmail, String cexUserId) {
        this.cexType = cexType;
        this.cexEmail = cexEmail;
        this.cexUserId = cexUserId;
    }

    public CommonReq(Integer cexType, String cexUserId) {
        this.cexType = cexType;
        this.cexUserId = cexUserId;
    }



    public void setCexType(Integer cexType) {
        CexTypeEnum cexTypeEnum = CexTypeEnum.fromType(cexType);
        if (cexTypeEnum == null) {
            throw new ApiException(ReconCexExceptionEnum.ILLEGAL_CEXTYPE);
        }
        this.cexType = cexType;
    }



}
