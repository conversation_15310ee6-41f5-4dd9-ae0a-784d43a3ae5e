package com.upex.reconciliation.service.utils;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Slf4jReporter;
import com.codahale.metrics.Timer;
import io.micrometer.core.instrument.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 指标监控
 *
 * <AUTHOR>
 */
@Slf4j
public class MetricsUtil {
    /***kafka消费批次TPS和耗时分布***/
    public static final String HISTOGRAM_KAFKA_CONSUMER = "METRICS_HISTOGRAM_KAFKA_CONSUMER_";
    /***解析Binlog命令总数***/
    public static final String COUNTER_BINLOG_CMD_ALL = "METRICS_COUNTER_BINLOG_CMD_ALL";
    /***解析Binlog-insert命令总数***/
    public static final String COUNTER_BINLOG_CMD_INSERT = "METRICS_COUNTER_BINLOG_CMD_INSERT";
    /***KafkaBillHandler.processBusiness消费总数量***/
    public static final String COUNTER_HANDLER_PROCESS_BUSINESS = "METRICS_COUNTER_HANDLER_PROCESS_BUSINESS";
    /***KafkaBillHandler.processBusiness TPS和耗时分布***/
    public static final String HISTOGRAM_HANDLER_PROCESS_BUSINESS = "METRICS_HISTOGRAM_HANDLER_PROCESS_BUSINESS_";
    /***BillUserCheckModule.takeCommand TPS和耗时分布***/
    public static final String HISTOGRAM_USER_TAKE_COMMAND = "METRICS_HISTOGRAM_USER_TAKE_COMMAND_";
    public static final String HISTOGRAM_ORDER_CHECK_COMMAND = "METRICS_HISTOGRAM_ORDER_CHECK_COMMAND_";
    /***批量加载用户耗时分布***/
    public static final String HISTOGRAM_BATCH_LOAD_USER_ASSETS = "METRICS_HISTOGRAM_BATCH_LOAD_USER_ASSETS_";
    /***单个加载用户耗时分布***/
    public static final String HISTOGRAM_SINGLE_LOAD_USER_ASSETS = "METRICS_HISTOGRAM_SINGLE_LOAD_USER_ASSETS_";

    public static final String HISTOGRAM_MONITOR_CHECK_TAKE_COMMAND = "METRICS_HISTOGRAM_MONITOR_CHECK_TAKE_COMMAND_";

    /***BillTimeSliceCheckModule.takeCommand TPS和耗时分布***/
    public static final String HISTOGRAM_SLICE_TAKE_COMMAND = "METRICS_HISTOGRAM_SLICE_TAKE_COMMAND_";
    /***TimeSliceBillCheckService.baseBillStart TPS和耗时分布***/
    public static final String HISTOGRAM_SLICE_BASE_BILL_START = "METRICS_HISTOGRAM_SLICE_BASE_BILL_START_";
    public static final String HISTOGRAM_SLICE_IN_ALL_BILL_START = "METRICS_HISTOGRAM_SLICE_IN_ALL_BILL_START";
    /***UserDataPersistenceService.saveTimeSliceData TPS和耗时分布***/
    public static final String HISTOGRAM_PERSISTENCE_SAVE_TIME_SLICE_DATA = "METRICS_HISTOGRAM_PERSISTENCE_SAVE_TIME_SLICE_DATA_";
    /***时间片命令队里大小***/
    public static final String GAUGE_SLICE_CMD_QUEUE = "METRICS_GAUGE_SLICE_CMD_QUEUE_";
    /***用户命令队列大小***/
    public static final String GAUGE_USER_CMD_QUEUE = "METRICS_GAUGE_USER_CMD_QUEUE_";
    /***用户命令队列大小***/
    public static final String GAUGE_USER_UNIQUE_MAP = "METRICS_GAUGE_USER_UNIQUE_MAP_";
    /***用户命令队列大小***/
    public static final String GAUGE_USER_PROPERTY_MAP = "METRICS_GAUGE_USER_PROPERTY_MAP_";
    /***用户命令队列大小***/
    public static final String GAUGE_COIN_USER_PROPERTY_MAP = "METRICS_GAUGE_COIN_USER_PROPERTY_MAP_";
    /***时间片时间***/
    public static final String GAUGE_TIME_SLICE_CHECK_OK_TIME = "METRICS_GAUGE_TIME_SLICE_CHECK_OK_TIME_";
    /***错误队列最小时间***/
    public static final String GAUGE_ERROR_MAP_MIN_TIME = "METRICS_GAUGE_ERROR_MAP_MIN_TIME_";
    public static final String GAUGE_NOW_TIME = "METRICS_GAUGE_NOW_TIME";
    public static final String GAUGE_ERROR_MAP_SIZE = "METRICS_GAUGE_ERROR_MAP_SIZE_";
    /***用户命令队列大小***/
    public static final String GAUGE_TIME_SLICE_DTO_MAP = "METRICS_GAUGE_TIME_SLICE_DTO_MAP_";
    /***用户命令队列大小***/
    public static final String GAUGE_TIME_SLICE_DTO_MAP_ALL = "METRICS_GAUGE_TIME_SLICE_DTO_MAP_ALL_";
    /***总账对账***/
    public static final String HISTOGRAM_CHECK_LEDGER = "METRICS_HISTOGRAM_CHECK_LEDGER_";
    /***总账对账***/
    public static final String GAUGE_CHECK_LEDGER = "METRICS_GAUGE_CHECK_LEDGER_";
    /***总账对账存盘***/
    public static final String GAUGE_SAVE_LEDGER = "METRICS_GAUGE_SAVE_LEDGER_";
    /***用户期初资产请求总数***/
    public static final String METRICS_GAUGE_USER_BEGIN_ASSETS_TOTAL = "METRICS_GAUGE_USER_BEGIN_ASSETS_TOTAL";
    /***用户期初资产缓存命中总数***/
    public static final String METRICS_GAUGE_USER_BEGIN_ASSETS_HIT = "METRICS_GAUGE_USER_BEGIN_ASSETS_HIT";
    /***初始化队列监控标记***/
    private static volatile boolean MONITOR_TIME_SLICE_DTO_MAP_INITED = false;
    /***prometheus map***/
    private final Map<String, Meter> prometheusMeterMap = new ConcurrentHashMap<>();
    /***指标注册***/
    private MetricRegistry metricRegistry = new MetricRegistry();
    /***指标输出***/
    private Slf4jReporter slf4jReporter = Slf4jReporter.forRegistry(metricRegistry)
            .convertRatesTo(TimeUnit.SECONDS)
            .convertDurationsTo(TimeUnit.MILLISECONDS)
            .withLoggingLevel(Slf4jReporter.LoggingLevel.WARN)
            .build();
    /***实例***/
    private static MetricsUtil INSTANCE = new MetricsUtil();

    /**
     * 构造函数 启动监听
     */
    private MetricsUtil() {
        slf4jReporter.start(30, TimeUnit.SECONDS);
    }

    public static void histogram(String metricName, Runnable runnable) {
        Timer.Context context = getTimer(metricName).time();
        long startTime = System.currentTimeMillis();
        try {
            runnable.run();
        } finally {
            stopTimer(context);
            getInstance().getDistributionSummary(metricName).record((System.currentTimeMillis() - startTime));
        }
    }

    public static <T> T histogram(String metricName, Supplier<T> supplier) {
        Timer.Context context = getTimer(metricName).time();
        long startTime = System.currentTimeMillis();
        try {
            return supplier.get();
        } finally {
            stopTimer(context);
            getInstance().getDistributionSummary(metricName).record((System.currentTimeMillis() - startTime));
        }
    }

    public static void counter(String metricName) {
        getInstance().metricRegistry.counter(metricName).inc();
        getInstance().getCounter(metricName).increment();
    }

    public static void gauge(String metricName, Supplier<Long> supplier) {
        getInstance().metricRegistry.gauge(metricName, () -> {
            return () -> {
                return supplier.get();
            };
        });
        getInstance().getGauge(metricName, supplier);
    }

    private DistributionSummary getDistributionSummary(String name) {
        Meter meter = prometheusMeterMap.get(name);
        if (meter == null) {
            synchronized (this) {
                if (meter == null) {
                    meter = DistributionSummary.builder(name)
                            .maximumExpectedValue(10000D)
                            .minimumExpectedValue(1D)
                            .publishPercentiles(0.5, 0.90, 0.99, 1)
                            .register(Metrics.globalRegistry);
                    prometheusMeterMap.put(name, meter);
                }
            }
        }
        return (DistributionSummary) meter;
    }

    private Counter getCounter(String name) {
        Meter meter = prometheusMeterMap.get(name);
        if (meter == null) {
            synchronized (this) {
                if (meter == null) {
                    meter = Counter.builder(name)
                            .register(Metrics.globalRegistry);
                    prometheusMeterMap.put(name, meter);
                }
            }
        }
        return (Counter) meter;
    }

    private Gauge getGauge(String name, Supplier<Long> supplier) {
        Meter meter = prometheusMeterMap.get(name);
        if (meter == null) {
            synchronized (this) {
                if (meter == null) {
                    meter = Gauge.builder(name, () -> {
                        return supplier.get();
                    }).register(Metrics.globalRegistry);
                    prometheusMeterMap.put(name, meter);
                }
            }
        }
        return (Gauge) meter;
    }

    /**
     * 获取实例
     *
     * @return
     */
    private static MetricsUtil getInstance() {
        return INSTANCE;
    }

    /**
     * 获取计时器
     *
     * @param metricName
     * @return
     */
    private static Timer getTimer(String metricName) {
        return getInstance().metricRegistry.timer(metricName);
    }

    /**
     * 停止计时器
     *
     * @param context
     */
    private static void stopTimer(Timer.Context context) {
        if (context != null) {
            context.stop();
        }
    }
}
