package com.upex.reconciliation.service.business.cex;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.upex.commons.support.exception.ApiException;
import com.upex.config.coin.SpotCoinChainDTO;
import com.upex.config.facade.coin.CoinConfigService;
import com.upex.newwallet.dto.AddressGetUidDTO;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.dao.cex.entity.*;
import com.upex.reconciliation.service.model.config.ApolloThirdCexAssetConfig;
import com.upex.reconciliation.service.service.CexCoinNameConfigService;
import com.upex.reconciliation.service.service.ThirdCexWithdrawHistoryService;
import com.upex.reconciliation.service.service.ThirdCexUserService;
import com.upex.reconciliation.service.service.UserWithdrawAddressService;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CexUserListRequest;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CommonReq;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.UserWithdrawHistoryListReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonWithdrawHistoryInnerRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonWithdrawHistoryRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.PageData;
import com.upex.reconciliation.service.service.client.cex.enmus.*;
import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.EnvUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.spot.facade.query.SpotAccountQueryClient;
import com.upex.utils.task.BaseTask;
import com.upex.utils.task.TaskManager;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CexUserWithdrawHistoryBizService extends AbstractCexAssetSyncHistory implements ICexAssetSyncHistory {


    @Resource
    ThirdCexUserService thirdCexUserService;

    @Resource
    ThirdCexWithdrawHistoryService thirdCexWithdrawHistoryService;

    @Resource
    CexApiService cexApiService;

    @Resource
    SpotAccountQueryClient spotAccountQueryClient;

    @Resource
    CommonService commonService;

    @Resource(name = "thirdCexTaskManager")
    TaskManager taskManager;

    @Resource
    CoinConfigService coinConfigService;

    @Resource
    UserWithdrawAddressService userWithDrawAddressService;

    @Resource
    CexCoinConfigBizService cexCoinConfigBizService;

    @Resource
    AlarmNotifyService alarmNotifyService;

    @Resource
    BillDbHelper billDbHelper;


    public PageData<ThirdCexWithdrawHistory> getUserWithdrawHistory(UserWithdrawHistoryListReq userWithdrawHistoryListReq) {
        if (userWithdrawHistoryListReq.getCexUserId() == null && userWithdrawHistoryListReq.getCexType() == null) {
            throw new ApiException(ReconCexExceptionEnum.UNSUPPORTED_QUERY);
        }
        List<ThirdCexUser> thirdCexUsers = thirdCexUserService.selectByCondition(CexUserListRequest.builder()
                .cexType(userWithdrawHistoryListReq.getCexType())
                .cexUserStatus(userWithdrawHistoryListReq.getCexUserStatus())
                .cexUserId(userWithdrawHistoryListReq.getCexUserId())
                .userManagerId(userWithdrawHistoryListReq.getUserManagerId())
                .useType(userWithdrawHistoryListReq.getUseType())
                .build());
        if (CollectionUtils.isEmpty(thirdCexUsers)) {
            return PageData.empty();
        }
        List<String> cexUserIds = thirdCexUsers.stream().map(ThirdCexUser::getCexUserId).collect(Collectors.toList());
        List<ThirdCexWithdrawHistory> thirdCexDepositeHistories = thirdCexWithdrawHistoryService.selectPageByUserIds(cexUserIds, userWithdrawHistoryListReq);
        int count = thirdCexWithdrawHistoryService.countPageByUserIds(cexUserIds, userWithdrawHistoryListReq);
        return new PageData<>(thirdCexDepositeHistories, count);
    }

    public List<ThirdCexWithdrawHistory> queryUserWithdrawHistoryAndSync(UserWithdrawHistoryListReq userWithdrawHistoryListReq) {
        List<ThirdCexWithdrawHistory> thirdCexWithdrawHistories = new ArrayList<>();
        CommonRes<CommonWithdrawHistoryRes> commonRes = cexApiService.queryUserWithdrawHistory(userWithdrawHistoryListReq);
        if (commonRes.getSuccess() && CollectionUtils.isNotEmpty(commonRes.getData())) {
            for (CommonWithdrawHistoryInnerRes withdrawHistoryInnerRes : commonRes.getData()) {
                ThirdCexWithdrawHistory history = new ThirdCexWithdrawHistory();
                history.setDrawId(withdrawHistoryInnerRes.getDrawId());
                history.setCexUserId(withdrawHistoryInnerRes.getCexUserId());
                history.setCexEmail(withdrawHistoryInnerRes.getCexEmail());
                history.setCexType(withdrawHistoryInnerRes.getCexType());
                history.setCoinName(withdrawHistoryInnerRes.getCoinName());
                history.setTxId(withdrawHistoryInnerRes.getTxId());
                history.setAddress(withdrawHistoryInnerRes.getAddress());
                history.setNetwork(withdrawHistoryInnerRes.getNetwork());
                history.setAmount(withdrawHistoryInnerRes.getAmount());
                history.setStatus(withdrawHistoryInnerRes.getStatus());
                history.setDrawBeginTime(withdrawHistoryInnerRes.getWithdrawBeginTime());
                history.setDrawEndTime(withdrawHistoryInnerRes.getWithdrawEndTime());
                history.setWalletType(withdrawHistoryInnerRes.getWalletType());
                history.setTransferType(withdrawHistoryInnerRes.getTransferType());
                history.setInfo(withdrawHistoryInnerRes.getInfo());
                history.setConfirmNo(withdrawHistoryInnerRes.getConfirmNo());
                history.setFee1(withdrawHistoryInnerRes.getFee1());
                history.setFee1Coin(withdrawHistoryInnerRes.getFee1Coin());
                history.setFee2(withdrawHistoryInnerRes.getFee2());
                history.setFee2Coin(withdrawHistoryInnerRes.getFee2Coin());
                history.setCheckSyncTime(userWithdrawHistoryListReq.getCheckSyncTime());
                history.setCreateTime(new Date());
                history.setUpdateTime(new Date());
                history.setIsLegal(WithdrawLegalEnum.UNCHECK.getValue());
                history.setVersion(SerialNoGenerator.getMinIdByTime(new Date()));
                thirdCexWithdrawHistories.add(history);
            }
            log.info("FinshQueryWithdrawHistory,size:{},userId:{},cexType:{}", commonRes.getData().size(), userWithdrawHistoryListReq.getCexUserId(), userWithdrawHistoryListReq.getCexType());
        }
        return thirdCexWithdrawHistories;
    }

    public void checkWithdrawRecords() throws InterruptedException {
        List<ThirdCexWithdrawHistory> unCheckWithdrawRecords = thirdCexWithdrawHistoryService.selectUnCheckWithdraw(CexTypeEnum.BINANCE.getType(), WithdrawLegalEnum.UNCHECK.getValue());
        log.info("unCheckWithdrawRecordSize：{}", unCheckWithdrawRecords.size());
        if (CollectionUtils.isNotEmpty(unCheckWithdrawRecords)) {
            for (ThirdCexWithdrawHistory withdrawHistory : unCheckWithdrawRecords) {
                try {
                    Long userId = null;
                    UserWithdrawAddress userWithdrawAddress = userWithDrawAddressService.selectByAddress(withdrawHistory.getAddress(), withdrawHistory.getCoinName(), withdrawHistory.getNetwork());
                    Triple triple = null;
                    if (userWithdrawAddress != null && userWithdrawAddress.getBgUid() != null) {
                        userId = userWithdrawAddress.getBgUid();
                        withdrawHistory.setIsLegal(WithdrawLegalEnum.NORMAL.getValue());
                        withdrawHistory.setIsBgSys(BgSysUserEnum
                                .SYS_USER.getValue());
                        thirdCexWithdrawHistoryService.checkWithdraw(withdrawHistory.getId(), withdrawHistory.getIsLegal(), withdrawHistory.getBgUserId(), withdrawHistory.getIsBgSys());
                        log.info("UserWithDrawAddressExist ,userId:{},drawId:{}", userId, withdrawHistory.getDrawId());
                        continue;
                    }
                    String coinName = cexCoinConfigBizService.selectBgCoinNameByCexTypeAndCexCoinName(withdrawHistory.getCexType(), withdrawHistory.getCoinName());
                    if (coinName == null) {
                        log.warn("FailToGetCoinName cexType : {}, cexCoinName : {}", withdrawHistory.getCexType(), withdrawHistory.getCoinName());
                        alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_COIN_NAME_NOT_FOUND, CexTypeEnum.fromType(withdrawHistory.getCexType()).getName(), withdrawHistory.getCoinName());
                        continue;
                    }
                    log.info("UserWithDrawAddressNotExist ,userId:{},drawId:{}", userId, withdrawHistory.getDrawId());
                    triple = getUserIdByAddress(coinName, withdrawHistory.getAddress());
                    if (triple != null) {
                        userId = (Long) triple.getLeft();
                        log.info("GetUserIdByAddress ,coinName:{} userId:{},coinId:{}", coinName, triple.getLeft(), triple.getMiddle());
                    }
                    Boolean isBgSys = Boolean.FALSE;
                    if (userId == null) {
                        withdrawHistory.setIsLegal(WithdrawLegalEnum.ILLEGAL.getValue());
                        withdrawHistory.setIsBgSys(BgSysUserEnum.NOT_SYS_USER.getValue());
                        alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_WITHDRAW_ADDRESS_ILLEGAL, withdrawHistory.getCexUserId(), CexTypeEnum.fromType(withdrawHistory.getCexType()).getName(), withdrawHistory.getDrawId());
                        log.warn("GetWithdrawUserIdFail ,coinName:{}", withdrawHistory.getCoinName());
                    } else {
                        log.info("GetWithdrawUserIdSuc ,coinName:{} userId:{}", withdrawHistory.getCoinName(), userId);
                        isBgSys = commonService.isSysUser((Long) triple.getLeft());
//                        if (EnvUtil.isOnline()) {
//                            isBgSys = commonService.isSysUser((Long) triple.getLeft());
//                        } else {
//                            isBgSys = ThreadLocalRandom.current().nextBoolean();
//                        }
                        log.info("GetWithdrawUserInfo,drawId:{},userId:{},isSysUser:{}", withdrawHistory.getDrawId(), userId, isBgSys);
                        if (isBgSys) {
                            withdrawHistory.setIsLegal(WithdrawLegalEnum.NORMAL.getValue());
                            withdrawHistory.setIsBgSys(BgSysUserEnum.SYS_USER.getValue());
                        } else {
                            withdrawHistory.setIsLegal(WithdrawLegalEnum.ILLEGAL.getValue());
                            withdrawHistory.setIsBgSys(BgSysUserEnum.NOT_SYS_USER.getValue());
                            alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_WITHDRAW_USER_NOT_SYSTEM_USER, withdrawHistory.getCexUserId(), CexTypeEnum.fromType(withdrawHistory.getCexType()).getName(), withdrawHistory.getDrawId(), triple.getLeft());
                        }
                    }
                    Triple finalTriple = triple;
                    billDbHelper.doDbOpInReconMasterTransaction(() -> {
                        UserWithdrawAddress sysuserWithdrawAddress = new UserWithdrawAddress();
                        sysuserWithdrawAddress = new UserWithdrawAddress();
                        sysuserWithdrawAddress.setCoinName(withdrawHistory.getCoinName());
                        sysuserWithdrawAddress.setNetwork(withdrawHistory.getNetwork());
                        sysuserWithdrawAddress.setAddress(withdrawHistory.getAddress());
                        sysuserWithdrawAddress.setCreateTime(new Date());
                        sysuserWithdrawAddress.setUpdateTime(new Date());
                        sysuserWithdrawAddress.setVersion(SerialNoGenerator.getMinIdByTime(new Date()));
                        sysuserWithdrawAddress.setCoinId((Integer) finalTriple.getMiddle());
                        sysuserWithdrawAddress.setBgUid(Long.valueOf((String) finalTriple.getLeft()));
                        sysuserWithdrawAddress.setChainCoinid((Integer) finalTriple.getRight());
                        userWithDrawAddressService.insert(sysuserWithdrawAddress);
                        log.info("InsertUserWithdrawAddress,coinName:{} userId:{}", withdrawHistory.getCoinName(), finalTriple.getLeft());
                        thirdCexWithdrawHistoryService.checkWithdraw(withdrawHistory.getId(), withdrawHistory.getIsLegal(), withdrawHistory.getBgUserId(), withdrawHistory.getIsBgSys());
                        log.info("checkUserWithdrawHistoryRecord,id:{}", withdrawHistory.getId());
                        return null;
                    });

                } catch (Exception e) {
                    log.error("checkUserWithdrawHistoryRecord error,id:{}", withdrawHistory.getId(), e);
                    alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_WITHDRAW_CHECK_EXCEPTION, withdrawHistory.getCexUserId(), CexTypeEnum.fromType(withdrawHistory.getCexType()).getName(), withdrawHistory.getDrawId(), e.getMessage());
                }
                log.info("checkUserWithdrawHistoryRecord end");
            }
        }
    }

    public Triple getUserIdByAddress(String coinName, String address) throws InterruptedException {
        address = HmacUtil.decrypt(address);
        log.info("getUserIdByAddress coinName:{},address:{}", coinName);
        Long userId = null;
        Integer coinId = commonService.getAllCoinId2Name().get(coinName);
        if (coinId == null) {
            log.warn("FailToGetCoinId coinName : {}", coinName);
            alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_COIN_NAME_NOT_FOUND, coinName);
            return null;
        }
        List<SpotCoinChainDTO> coinChains = coinConfigService.getCoinChains(coinId);
        if (CollectionUtils.isEmpty(coinChains)) {
            log.warn("FailToGetCoinChain coinName : {}", coinName);
            alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_GETCOINCHAIN_ERROR, coinId, coinName);
            return null;
        }
        log.info("GetCoinChains:coinId:{},coinName:{},", coinId, coinName);
        for (SpotCoinChainDTO coinChain : coinChains) {
            try {
                if (!EnvUtil.isOnline()) {
                    if (coinChain.getCoinType().equals("0")) {
                        continue;
                    }
                }
                Integer chainCoinId = coinChain.getChainCoinId();
                if (!EnvUtil.isOnline()) {
                    if (!chainCoinId.equals(70)) {
                        continue;
                    }
                }
                AddressGetUidDTO addressGetUidDTO = AddressGetUidDTO.builder().coinId(coinId).chainCoinId(chainCoinId).address(address).build();
                userId = spotAccountQueryClient.getUserIdByAddress(addressGetUidDTO);
            } catch (Exception e) {
                log.error("getUserIdByAddressError,coinId:{},chainCoinId:{}", coinId, coinChain.getChainCoinId(), e);
                alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_GETCOINADDRESS_ERROR, coinName, coinId, coinChain.getChainCoinId(), address, e.getMessage());
            }
            if (userId != null) {
                return new MutableTriple(userId, coinId, coinChain.getChainCoinId());
            }
        }
//        if (!EnvUtil.isOnline()) {
//            return new MutableTriple(123L, coinId, 70);
//        }
        return null;
    }

    @Override
    public void syncAssetHistory(CexAssetConfig cexAssetConfig, ThirdCexUserConfig userConfig, Date startTime, Date endTime, Date checkSyncTime) {
        UserWithdrawHistoryListReq userWithdrawHistoryListReq = new UserWithdrawHistoryListReq(userConfig.getCexType(), userConfig.getCexUserId(), userConfig.getApiKey(), userConfig.getApiKeyPrivate(), startTime, endTime, checkSyncTime);
        List<ThirdCexWithdrawHistory> thirdCexWithdrawHistories = queryUserWithdrawHistoryAndSync(userWithdrawHistoryListReq);
        ApolloThirdCexAssetConfig config = ReconciliationApolloConfigUtils.getThirdCexAssetConfig();
        billDbHelper.doDbOpInReconMasterTransaction(() -> {
            if (CollectionUtils.isNotEmpty(thirdCexWithdrawHistories)) {
                List<List<ThirdCexWithdrawHistory>> partisionDepositeHistories = Lists.partition(thirdCexWithdrawHistories, config.getSqlInsertSize());
                for (List<ThirdCexWithdrawHistory> partision : partisionDepositeHistories) {
                    thirdCexWithdrawHistoryService.batchInsert(partision);
                }
                log.info("insertWithdrawHistorySize:{}", partisionDepositeHistories.size());
            }
            saveModAssetConfig(cexAssetConfig, CexAssetHistoryTypeEnum.WITHDRAW, userConfig, checkSyncTime);
            return null;
        });
    }


    @Override
    public CexAssetHistoryTypeEnum getAssetHistoryType() {
        return CexAssetHistoryTypeEnum.WITHDRAW;
    }


}
