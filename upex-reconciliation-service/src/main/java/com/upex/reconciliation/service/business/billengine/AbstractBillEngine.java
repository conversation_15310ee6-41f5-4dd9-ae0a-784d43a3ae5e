package com.upex.reconciliation.service.business.billengine;

import com.upex.mixcontract.common.framework.AttributeMap;
import com.upex.mixcontract.common.framework.command.AbstractCommand;
import com.upex.mixcontract.common.framework.module.AbstractModule;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.billengine.command.impl.BillUserCheckCommand;
import com.upex.reconciliation.service.business.module.impl.*;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.utils.log.AlarmUtils;
import com.upex.utils.redisext.IRedisLock;
import com.upex.utils.task.function.VoidFunctionP0;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public abstract class AbstractBillEngine {
    private final AtomicBoolean running = new AtomicBoolean(true);
    private final List<Thread> startedThreads = new LinkedList<>();
    private ReconciliationSpringContext context;
    private IRedisLock redisLock;
    private VoidFunctionP0 stopAction;
    protected final BillLogicGroup mainLogicGroup;
    protected AccountTypeEnum accountTypeEnum;

    public BillLogicGroup getMainLogicGroup() {
        return mainLogicGroup;
    }

    public AbstractBillEngine(ReconciliationSpringContext context, AccountTypeEnum accountTypeEnum) {
        //  模块注册要注意，事件扫描在logicGroup层，如果同一个模块注册在多个logicGroup上要严格检查事件监听代码逻辑
        this.context = context;
        this.accountTypeEnum = accountTypeEnum;
        mainLogicGroup = createMainMatchLogicGroup(context);
    }

    private BillLogicGroup createMainMatchLogicGroup(ReconciliationSpringContext context) {
        AttributeMap attrMap = createLogicGroupAttrMap(context, true);
        List<Class<? extends AbstractCommand>> cmdClasses = new LinkedList<>();
        cmdClasses.add(BillUserCheckCommand.class);

        //模块注册
        List<Class<? extends AbstractModule>> moduleClasses = new LinkedList<>();
        moduleClasses.add(BillUserCheckModule.class);
        moduleClasses.add(PropertyInitModule.class);
        moduleClasses.add(BillTimeSliceCheckModule.class);
        moduleClasses.add(BillUserAssetCheckModule.class);
        moduleClasses.add(FlowMonitorDataProcessModule.class);
        BillLogicGroup logicGroup = new BillLogicGroup("start", attrMap, cmdClasses, moduleClasses, accountTypeEnum, this);
        return logicGroup;
    }

    private AttributeMap createLogicGroupAttrMap(ReconciliationSpringContext context, boolean isMainLogicGroup) {
        //属性注册
        AttributeMap attrMap = new AttributeMap();
        attrMap.set(BillLogicGroup.ENGINE_SPRING_CONTEXT_KEY, context);
        attrMap.set(BillLogicGroup.IS_MAIN_LOGIC_GROUP, isMainLogicGroup);
        attrMap.set(BillLogicGroup.ACCOUNT_TYPE_ENUM_KEY, accountTypeEnum);

        return attrMap;
    }

    protected Thread startProtectedThread(String threadPrefix, FunctionP0<Long> sleepTimeInvoker, FunctionP0<Boolean> functionP0) {
        String name = mainLogicGroup.getName();
        String threadName = threadPrefix + name;
        Thread thread = new Thread(() -> {
            boolean needSleep = false;
            while (true) {
                if (!running.get()) {
                    break;
                }
                try {
                    if (needSleep) {
                        long sleepTime = 500L;
                        try {
                            sleepTime = sleepTimeInvoker.run();
                        } catch (Exception e) {
                            log.error("sleepTimeInvoker error", e);
                            AlarmUtils.error("内存对账AbstractBillEngine线程异常，请及时关注1！ threadName：{}", threadName, e);
                        }
                        Thread.sleep(sleepTime);
                    }
                    try {
                        boolean instant = functionP0.run();
                        needSleep = !instant;
                    } catch (InterruptedException e) {
                        throw new IllegalStateException(e);
                    }
                } catch (Throwable cause) {
                    needSleep = true;
                    AlarmUtils.error("内存对账AbstractBillEngine线程异常，请及时关注2！ threadName：{}", threadName, cause);
                }
            }
            log.info("AbstractBillEngine.startProtectedThread thread stop {}", threadName);
        }, threadName);
        thread.setDaemon(true);
        thread.start();
        startedThreads.add(thread);
        return thread;
    }

    public ReconciliationSpringContext getContext() {
        return context;
    }


    interface FunctionP0<R> {
        R run() throws InterruptedException;
    }

    private void startLogic(IRedisLock redisLock, VoidFunctionP0 stopAction) {
        this.redisLock = redisLock;
        this.stopAction = stopAction;

        AttributeMap initMap = new AttributeMap();
        initMap.set(BillLogicGroup.ENGINE_SPRING_CONTEXT_KEY, context);
        initMap.set(BillLogicGroup.ACCOUNT_TYPE_ENUM_KEY, accountTypeEnum);
        mainLogicGroup.init(initMap);
        mainLogicGroup.start();
        innerStart();
    }

    protected abstract void innerStart();

    public synchronized void start(IRedisLock redisLock, VoidFunctionP0 stopAction) {
        startLogic(redisLock, stopAction);
    }

    protected Thread startProtectedThread(String threadPrefix, long sleepTime, FunctionP0<Boolean> functionP0) {
        return startProtectedThread(threadPrefix, () -> {
            return sleepTime;
        }, functionP0);
    }

    public synchronized void stop() {
        running.set(false);
    }

    public boolean isRunning() {
        return running.get();
    }
}
