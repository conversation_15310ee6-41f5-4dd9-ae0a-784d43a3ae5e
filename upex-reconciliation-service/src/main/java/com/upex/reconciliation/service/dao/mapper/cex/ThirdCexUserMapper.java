package com.upex.reconciliation.service.dao.mapper.cex;

import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CexUserListRequest;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CexUserPageListRequest;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ThirdCexUserMapper {

    /**
     * 根据ID查询用户
     */
    ThirdCexUser selectById(Long id);

    List<ThirdCexUser> selectSubUser(CexUserListRequest request);

    /**
     * 分页查询 CEX 用户列表
     */
    List<ThirdCexUser> selectByPage(CexUserPageListRequest request);


    /**
     * 查询总数（用于分页）
     */
    int countByPage(CexUserPageListRequest request);

    /**
     * 根据CEX 用户ID查询用户
     * @param cexType
     * @param cexUserId
     * @return
     */
    ThirdCexUser selectByCexTypeAndUserId(Integer cexType,String cexUserId);

    /**
     * 插入新用户
     */
    int insert(ThirdCexUser user);

    /**
     * 更新用户信息
     */
    int update(ThirdCexUser user);

    int updateSubUserApikeyStatus(@Param("parentCexUserId") String parentCexUserId,@Param("apikeyStatus") Integer apikeyStatus);
    int batchInsertSubUser(@Param("users") List<ThirdCexUser> users);


    int batchInsert(@Param("users") List<ThirdCexUser> users);

    List<ThirdCexUser> selectSubUserByCexTypeAndParentUserId(@Param("cexType") Integer cexType,@Param("parentUserId") String parentUserId);

    int deleteSubUserByCexTypeAndParentUserId(@Param("cexType")Integer cexType,@Param("parentUserId")String parentUserId);

    List<ThirdCexUser> selectByCondition(CexUserListRequest request);

    List<ThirdCexUser> selectSubUserByPartentUserIds(@Param("cexType")Integer cexType,@Param("cexUserIds")List<String> cexUserId);

    List<ThirdCexUser> selectAllParentUserByCexType(@Param("cexType")Integer cexType);
}
