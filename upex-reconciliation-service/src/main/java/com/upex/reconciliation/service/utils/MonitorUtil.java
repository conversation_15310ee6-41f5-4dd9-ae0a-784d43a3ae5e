package com.upex.reconciliation.service.utils;

import com.upex.reconciliation.service.service.AssetsBillConfigService;
import com.upex.reconciliation.service.dao.entity.AssetsBillConfig;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;
import com.upex.reconciliation.service.model.dto.BillTimeSliceDTO;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.business.module.impl.BillLedgerCheckModule;
import com.upex.reconciliation.service.business.module.impl.BillTimeSliceCheckModule;
import com.upex.reconciliation.service.business.module.impl.BillUserCheckModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import static com.upex.reconciliation.service.utils.MetricsUtil.*;

/**
 * 监控指标上报
 */
@Slf4j
@Component
public class MonitorUtil {
    @Resource
    private BillEngineManager billEngineManager;
    @Resource
    private AssetsBillConfigService assetsBillConfigService;
    /***业务线对账时间***/
    private final Map<Byte, Long> gaugeTimeSliceCheckOkTimeMap = new ConcurrentHashMap<>();
    /***业务线用户数***/
    private final Map<Byte, Long> gaugeUserPropertySizeMap = new ConcurrentHashMap<>();
    /***业务线币用户数***/
    private final Map<Byte, Long> gaugeCoinUserPropertySizeMap = new ConcurrentHashMap<>();
    /***业务线错误队列最小时间***/
    private final Map<Byte, Long> gaugeErrorMapMinTimeMap = new ConcurrentHashMap<>();
    /***业务线错误队列***/
    private final Map<Byte, Long> gaugeErrorMapSizeMap = new ConcurrentHashMap<>();
    /***总账存盘时间***/
    private final Map<String, Long> gaugeLedgerSaveOkTimeMap = new ConcurrentHashMap<>();
    /***总账对账时间***/
    private final Map<String, Long> gaugeLedgerCheckOkTimeMap = new ConcurrentHashMap<>();


    /**
     * 启动初始化
     */
    @PostConstruct
    public void init() {
        // 初始化业务线对账指标
        List<ApolloReconciliationBizConfig> apolloBizConfigs = ReconciliationApolloConfigUtils.getAllBillConfigByAccountType();
        for (ApolloReconciliationBizConfig apolloBizConfig : apolloBizConfigs) {
            if (!apolloBizConfig.isOpen() || !EnvUtil.isRunningAccountType(apolloBizConfig.getAccountType())) {
                continue;
            }
            MetricsUtil.gauge(GAUGE_USER_PROPERTY_MAP + apolloBizConfig.getAccountType(), () -> gaugeUserPropertySizeMap.computeIfAbsent(apolloBizConfig.getAccountType(), k -> 0L));
            MetricsUtil.gauge(GAUGE_COIN_USER_PROPERTY_MAP + apolloBizConfig.getAccountType(), () -> gaugeCoinUserPropertySizeMap.computeIfAbsent(apolloBizConfig.getAccountType(), k -> 0L));
            MetricsUtil.gauge(GAUGE_TIME_SLICE_CHECK_OK_TIME + apolloBizConfig.getAccountType(), () -> gaugeTimeSliceCheckOkTimeMap.computeIfAbsent(apolloBizConfig.getAccountType(), k -> 0L));
            MetricsUtil.gauge(GAUGE_ERROR_MAP_MIN_TIME + apolloBizConfig.getAccountType(), () -> gaugeErrorMapMinTimeMap.computeIfAbsent(apolloBizConfig.getAccountType(), k -> 0L));
            MetricsUtil.gauge(GAUGE_ERROR_MAP_SIZE + apolloBizConfig.getAccountType(), () -> gaugeErrorMapSizeMap.computeIfAbsent(apolloBizConfig.getAccountType(), k -> 0L));
        }
        // 初始化总账指标
        List<AssetsCheckConfig> assetsCheckList = ReconciliationApolloConfigUtils.getGlobalBillConfig().getAssetsCheckList();
        for (AssetsCheckConfig assetsCheckConfig : assetsCheckList) {
            if (assetsCheckConfig.isOpen() && EnvUtil.isRunningLedgerAccountType(assetsCheckConfig.getAssetsCheckType(), assetsCheckConfig.getAssetsCheckParam())) {
                MetricsUtil.gauge(GAUGE_CHECK_LEDGER + assetsCheckConfig.getAssetsCheckType(), () -> gaugeLedgerCheckOkTimeMap.computeIfAbsent(assetsCheckConfig.getAssetsCheckType(), k -> 0L));
                MetricsUtil.gauge(GAUGE_SAVE_LEDGER + assetsCheckConfig.getAssetsCheckType(), () -> gaugeLedgerSaveOkTimeMap.computeIfAbsent(assetsCheckConfig.getAssetsCheckType(), k -> 0L));
            }
        }
        // 上报当前时间
        MetricsUtil.gauge(GAUGE_NOW_TIME, () -> System.currentTimeMillis());

        // 启动定时器
        new java.util.Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                try {
                    // 业务线对账
                    List<ApolloReconciliationBizConfig> apolloBizConfigs = ReconciliationApolloConfigUtils.getAllBillConfigByAccountType();
                    for (ApolloReconciliationBizConfig apolloBizConfig : apolloBizConfigs) {
                        if (!apolloBizConfig.isOpen() || !EnvUtil.isRunningAccountType(apolloBizConfig.getAccountType())) {
                            continue;
                        }
                        BillLogicGroup billLogicGroup = billEngineManager.getBillLogicGroup(apolloBizConfig.getAccountType());
                        if (billLogicGroup == null) {
                            continue;
                        }
                        BillTimeSliceCheckModule timeSliceCheckModule = (BillTimeSliceCheckModule) billLogicGroup.getTimeSliceModule();
                        BillUserCheckModule userCheckModule = (BillUserCheckModule) billLogicGroup.getUserCheckModule();
                        BillTimeSliceDTO lastBillTimeSliceDTO = timeSliceCheckModule.getLastBillTimeSliceDTO();
                        if (lastBillTimeSliceDTO == null) {
                            continue;
                        }
                        gaugeUserPropertySizeMap.put(apolloBizConfig.getAccountType(), userCheckModule.getUserPropertyMap().estimatedSize());
                        gaugeCoinUserPropertySizeMap.put(apolloBizConfig.getAccountType(), userCheckModule.getUserPropertyMapCoinSize().longValue());
                        gaugeTimeSliceCheckOkTimeMap.put(apolloBizConfig.getAccountType(), lastBillTimeSliceDTO.getBillConfig().getCheckOkTime().getTime());
                        CommonBillChangeData minErrorMapData = userCheckModule.getMinErrorMapData();
                        gaugeErrorMapMinTimeMap.put(apolloBizConfig.getAccountType(), minErrorMapData != null ? minErrorMapData.getBizTime().getTime() : 0);
                        gaugeErrorMapSizeMap.put(apolloBizConfig.getAccountType(), userCheckModule.getErrorBillMapSize());
                    }

                    // 总账
                    List<AssetsBillConfig> assetsBillConfigs = assetsBillConfigService.listAssetsBillConfig();
                    for (AssetsBillConfig assetsBillConfig : assetsBillConfigs) {
                        AssetsCheckConfig assetsCheckConfig = ReconciliationApolloConfigUtils.getAssetsCheckConfig(assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam());
                        if (assetsCheckConfig.isOpen() && EnvUtil.isRunningLedgerAccountType(assetsCheckConfig.getAssetsCheckType(), assetsCheckConfig.getAssetsCheckParam())) {
                            gaugeLedgerSaveOkTimeMap.put(assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getCheckOkTime().getTime());
                            BillLedgerCheckModule billLedgerCheckModule = billEngineManager.getBillLedgerCheckModule(assetsBillConfig.getAssetsCheckType());
                            if (billLedgerCheckModule != null && billLedgerCheckModule.getLastCheckOkTime() != null) {
                                gaugeLedgerCheckOkTimeMap.put(assetsBillConfig.getAssetsCheckType(), billLedgerCheckModule.getLastCheckOkTime().getTime());
                            }

                        }
                    }
                } catch (Exception e) {
                    log.error("", e);
                }

            }
        }, 60 * 1000, 10 * 1000);
    }
}
