package com.upex.reconciliation.service.business.convert;

import com.upex.reconciliation.service.business.convert.enums.ReconOrderFailureEnum;
import lombok.Getter;

@Getter
public class ReconOrderResult {

    private final boolean success;
    private ReconOrderFailureEnum failureType;
    private String failureReason;

    private ReconOrderResult(boolean success) {
        this.success = success;
    }

    private ReconOrderResult(ReconOrderFailureEnum failureType, String failureReason) {
        this.success = false;
        this.failureType = failureType;
        this.failureReason = failureReason;
    }

    public static ReconOrderResult success() {
        return new ReconOrderResult(true);
    }

    public static ReconOrderResult failure(ReconOrderFailureEnum failureType, String failureReason) {
        return new ReconOrderResult(failureType, failureReason);
    }
}
