package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.mixcontract.common.literal.enums.HoldSideEnum;
import com.upex.mixcontract.common.utils.ContractFormulaUtils;
import com.upex.mixcontract.process.facade.dto.MixAccountAssetsExtension;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AbstractBillCheckService;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.PropEnum;
import com.upex.reconciliation.service.common.constants.enums.SymbolCoinPropEnum;
import com.upex.reconciliation.service.common.constants.enums.SymbolPropEnum;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.alarm.CheckResult;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.*;
import com.upex.reconciliation.service.service.OldBillProfitTransferService;
import com.upex.reconciliation.service.service.ReconSystemAccountService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.NumberUtil;
import com.upex.ticker.facade.dto.PriceVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.*;

@Slf4j
@Service
public class ContractBillCheckServiceImpl extends AbstractBillCheckService {
    @Resource
    private AlarmNotifyService alarmNotifyService;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    @Resource
    private OldBillProfitTransferService oldBillProfitTransferService;
    @Resource
    private ReconSystemAccountService reconSystemAccountService;
    /***多空仓对账计数器***/
    private Map<Byte, AtomicInteger> checkAssetsLCountSCountMap = new ConcurrentHashMap<>();
    /***盈亏对账计数器***/
    private Map<Byte, AtomicInteger> checkAssetsByTradingOnCounterMap = new ConcurrentHashMap<>();
    /***合约symbol维度prop***/
    private static final Map<SymbolPropEnum, PropEnum> SYMBOL_PROP_ENUM_MAP = new HashMap<>() {{
        put(SymbolPropEnum.L_COUNT, PropEnum.toEnum(PropEnum.SYMBOL_PROP_5x_1.getPropCode()));
        put(SymbolPropEnum.S_COUNT, PropEnum.toEnum(PropEnum.SYMBOL_PROP_5x_2.getPropCode()));
        put(SymbolPropEnum.UN_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_PROP_5x_3.getPropCode()));
        put(SymbolPropEnum.MARGIN_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_PROP_5x_4.getPropCode()));
        put(SymbolPropEnum.REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_PROP_5x_5.getPropCode()));
        put(SymbolPropEnum.INIT_VALUE, PropEnum.toEnum(PropEnum.SYMBOL_PROP_5x_6.getPropCode()));
        put(SymbolPropEnum.RE_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_PROP_5x_7.getPropCode()));
        put(SymbolPropEnum.RE_UN_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_PROP_5x_8.getPropCode()));
    }};
    /***合约symbolCoin维度prop***/
    private static final Map<SymbolCoinPropEnum, PropEnum> SYMBOL_COIN_PROP_ENUM_MAP = new HashMap<>() {{
        put(SymbolCoinPropEnum.MARGIN_UN_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_COIN_PROP_5x_3.getPropCode()));
        put(SymbolCoinPropEnum.MARGIN_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_COIN_PROP_5x_4.getPropCode()));
        put(SymbolCoinPropEnum.REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_COIN_PROP_5x_5.getPropCode()));
        put(SymbolCoinPropEnum.RE_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_COIN_PROP_5x_6.getPropCode()));
    }};

    @Override
    public Byte getAccountType() {
        return AccountTypeEnum.S_USDT_MIX_CONTRACT_BL.getCode();
    }

    @Override
    public CheckResult doCheckProperty(ApolloReconciliationBizConfig apolloBizConfig, String accountUniqueId, Long userId, Integer coinId, List<CommonBillChangeData> billChangeDataList, BillCoinUserProperty billCoinUserProperty) {
        if (CollectionUtils.isEmpty(billChangeDataList)) {
            return CheckResult.DEFAULT_SUCCESS;
        }
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        CommonBillChangeData lastChange = billChangeDataList.get(billChangeDataList.size() - 1);
        BigDecimal totalChangeProp1 = BigDecimal.ZERO;
        BigDecimal totalChangeProp2 = BigDecimal.ZERO;
        BigDecimal totalChangeProp3 = BigDecimal.ZERO;
        BigDecimal totalChangeProp4 = BigDecimal.ZERO;
        BigDecimal totalChangeProp5 = BigDecimal.ZERO;
        for (CommonBillChangeData bill : billChangeDataList) {
            totalChangeProp1 = totalChangeProp1.add(bill.getChangeProp1());
            totalChangeProp2 = totalChangeProp2.add(bill.getChangeProp2());
            totalChangeProp3 = totalChangeProp3.add(bill.getChangeProp3());
            totalChangeProp4 = totalChangeProp4.add(bill.getChangeProp4());
            totalChangeProp5 = totalChangeProp5.add(bill.getChangeProp5());
        }
        if (lastChange.getProp2().compareTo(billCoinUserProperty.getProp2().add(totalChangeProp2)) != 0) {
            log.error("CalculationCheckUtils.checkBillCoinProperty check error prop2 accountUniqueId:{}  lastChange:{},billCoinUserProperty:{},totalChangeProp2:{}"
                    , accountUniqueId, JSONObject.toJSONString(lastChange)
                    , JSONObject.toJSONString(billCoinUserProperty), totalChangeProp2.toPlainString());
            return CheckResult.fail(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), lastChange.getBizTime())
                    .addUserPropCheckData(lastChange.getAccountId(), lastChange.getCoinId(), billCoinUserProperty.getProp2(), totalChangeProp2, lastChange.getProp2(), lastChange.getBizId(), "prop2");
        }
        if (lastChange.getProp3().compareTo(billCoinUserProperty.getProp3().add(totalChangeProp3)) != 0) {
            log.error("CalculationCheckUtils.checkBillCoinProperty check error prop3 accountUniqueId:{}  lastChange:{},billCoinUserProperty:{},totalChangeProp3:{}"
                    , accountUniqueId, JSONObject.toJSONString(lastChange)
                    , JSONObject.toJSONString(billCoinUserProperty), totalChangeProp3.toPlainString());
            return CheckResult.fail(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), lastChange.getBizTime())
                    .addUserPropCheckData(lastChange.getAccountId(), lastChange.getCoinId(), billCoinUserProperty.getProp3(), totalChangeProp3, lastChange.getProp3(), lastChange.getBizId(), "prop3");
        }
        return CheckResult.DEFAULT_SUCCESS;
    }

    @Override
    public boolean checkBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty) {
        if (commonBillChangeData.getProp2().subtract(commonBillChangeData.getChangeProp2()).compareTo(billCoinUserProperty.getProp2()) == 0
                && commonBillChangeData.getProp3().subtract(commonBillChangeData.getChangeProp3()).compareTo(billCoinUserProperty.getProp3()) == 0) {
            return true;
        }
        return false;
    }

    @Override
    public boolean checkCurrentBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty) {
        if (commonBillChangeData.getProp2().compareTo(billCoinUserProperty.getProp2()) == 0
                && commonBillChangeData.getProp3().compareTo(billCoinUserProperty.getProp3()) == 0) {
            return true;
        }
        return false;
    }

    @Override
    public BigDecimal getPropSumByUserProperty(BillCoinUserProperty coinUserProperty) {
        return NumberUtil.add(coinUserProperty.getProp2(), coinUserProperty.getProp3());
    }

    @Override
    public boolean doTimeSliceBillCheck(BillTimeSliceDTO endSliceDTO, Date checkOkTime, ApolloReconciliationBizConfig apolloBizConfig) {
        MixAccountAssetsExtension mixAccountAssetsExtension = accountAssetsServiceFactory.queryPriceByTime((int) apolloBizConfig.getAccountType(), checkOkTime.getTime());
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        // 多空仓对账 验证仓位检查 多仓 = 空仓（全量）
        if (apolloBizConfig.isCheckLCountSCountOpen()) {
            boolean result = checkAssetsLCountSCount(accountTypeEnum, endSliceDTO);
            log.info("BillTimeSliceCheckModule.timeSliceBillCheck checkAssetsLCountSCount result accountType:{} {}, time is {}", apolloBizConfig.getAccountType(), result, DateUtil.getDefaultDateStr(checkOkTime));
            if (!result) {
                return result;
            }
        }
        // 盈亏对账 按照交易对汇总 已实现+未实现<预期值
        if (apolloBizConfig.isCheckTradingOn()) {
            boolean result = checkAssetsByTradingOn(endSliceDTO, apolloBizConfig, mixAccountAssetsExtension);
            log.info("BillTimeSliceCheckModule.timeSliceBillCheck checkAssetsByTradingOn result accountType:{} {} time is {}", apolloBizConfig.getAccountType(), result, DateUtil.getDefaultDateStr(checkOkTime));
            if (!result) {
                return result;
            }
        }
        return true;
    }

    private boolean checkAssetsLCountSCount(AccountTypeEnum accountTypeEnum, BillTimeSliceDTO endAssetTimeSliceDTO) {
        Date checkOkTime = endAssetTimeSliceDTO.getBillConfig().getCheckOkTime();
        CheckResult checkResult = CheckResult.success(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), endAssetTimeSliceDTO.getBillConfig().getCheckOkTime());
        List<SymbolCheckProperty> symbolCheckPropertyList = endAssetTimeSliceDTO.getSymbolCheckPropertyList();
        if (CollectionUtils.isEmpty(symbolCheckPropertyList)) {
            return true;
        }
        for (SymbolCheckProperty symbolCheckProperty : symbolCheckPropertyList) {
            String symbolId = symbolCheckProperty.getSymbolId();
            BigDecimal lCount = symbolCheckProperty.getProp1();
            BigDecimal sCount = symbolCheckProperty.getProp2();
            // 如果多仓和空仓不对等 验证失败
            if (lCount.compareTo(sCount) != 0) {
                checkResult.setResult(false);
                checkResult.addLScountCheckData(symbolId, sCount, lCount, sCount.subtract(lCount));
                log.error("ContractBillCheckServiceImpl.checkAssetsLCountSCount failed, {} symbolId = {},lCount={},sCount={},data={}", accountTypeEnum.getCode(), symbolId, lCount, sCount, JSON.toJSONString(symbolCheckProperty));
            }
        }
        if (checkResult.isFail()) {
            int counter = checkAssetsLCountSCountMap.computeIfAbsent(accountTypeEnum.getCode(), k -> new AtomicInteger(0)).incrementAndGet();
            checkResult.setCounter(counter);
            alarmNotifyService.alarm(accountTypeEnum.getCode(), CHECK_LCOUNT_SCOUNT_TEMPLATE, Map.of("checkResult", checkResult));
        } else {
            AtomicInteger counter = checkAssetsLCountSCountMap.computeIfAbsent(accountTypeEnum.getCode(), k -> new AtomicInteger(0));
            if (counter.get() > 0) {
                counter.set(0);
                alarmNotifyService.alarm(accountTypeEnum.getCode(), CHECK_LCOUNT_SCOUNT_SUCCESS, accountTypeEnum.getCode(), DateUtil.getDefaultDateStr(checkOkTime));
            }
        }
        return checkResult.isSuccess();
    }

    public boolean checkAssetsByTradingOn(BillTimeSliceDTO endAssetTimeSliceDTO,
                                          ApolloReconciliationBizConfig apolloBizConfig,
                                          MixAccountAssetsExtension mixAccountAssetsExtension) {
        // 解析当前标记价格
        Date checkOkTime = endAssetTimeSliceDTO.getBillConfig().getCheckOkTime();
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        CheckResult checkResult = CheckResult.success(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), endAssetTimeSliceDTO.getBillConfig().getCheckOkTime());
        List<SymbolCheckProperty> symbolCheckPropertyList = endAssetTimeSliceDTO.getSymbolCheckPropertyList();
        if (CollectionUtils.isEmpty(symbolCheckPropertyList)) {
            return true;
        }
        for (SymbolCheckProperty symbolCheckProperty : symbolCheckPropertyList) {
            if (symbolCheckProperty.getProp1().compareTo(BigDecimal.ZERO) == 0 && symbolCheckProperty.getProp2().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            String symbolId = symbolCheckProperty.getSymbolId();
            BigDecimal lCount = symbolCheckProperty.getProp1();
            BigDecimal sCount = symbolCheckProperty.getProp2();
            BigDecimal realized = symbolCheckProperty.getProp3();
            BigDecimal unRealized = symbolCheckProperty.getProp4();
            BigDecimal initValue = symbolCheckProperty.getProp5();
            BigDecimal mPrice = symbolCheckProperty.getProp6();
            // 已实现+未实现+初始值<预值
            BigDecimal result = NumberUtil.add(realized, unRealized, initValue);
            boolean currentResult = result.compareTo(apolloBizConfig.getTradingOnToleranceValue()) <= 0;
            if (!currentResult) {
                checkResult.setResult(false);
                checkResult.addAssetsProfitCheckData(symbolId, realized, unRealized, initValue, apolloBizConfig.getTradingOnToleranceValue(), result, mPrice, lCount, sCount);
                if (apolloBizConfig.getIsOpenCheckSymbolUserPosition()) {
                    Map<String, List<BillUserPosition>> symbolBillUserPositionMap = endAssetTimeSliceDTO.getBillUserPositionList().stream().collect(Collectors.groupingBy(BillUserPosition::getSymbolId));
                    checkSymbolUserPosition(apolloBizConfig.getAccountType(), checkOkTime, mPrice, symbolId, symbolBillUserPositionMap.get(symbolId));
                }
            }
            log.info("checkMixProfitsAndLossesBySymbolId info {} finalResult {} currentResult {} data={}",
                    apolloBizConfig.getAccountType(), checkResult.isSuccess(), currentResult, JSON.toJSONString(symbolCheckProperty));
        }
        if (checkResult.isFail()) {
            int counter = checkAssetsByTradingOnCounterMap.computeIfAbsent(accountTypeEnum.getCode(), k -> new AtomicInteger(0)).incrementAndGet();
            checkResult.setCounter(counter);
            alarmNotifyService.alarm(accountTypeEnum.getCode(), CHECK_ASSETS_TRADING_ON_TEMPLATE, Map.of("checkResult", checkResult));
        } else {
            AtomicInteger counter = checkAssetsByTradingOnCounterMap.computeIfAbsent(accountTypeEnum.getCode(), k -> new AtomicInteger(0));
            if (counter.get() > 0) {
                counter.set(0);
                alarmNotifyService.alarm(accountTypeEnum.getCode(), CHECK_ASSETS_TRADING_ON_SUCCESS, accountTypeEnum.getCode(), DateUtil.getDefaultDateStr(checkOkTime));
            }
        }
        return checkResult.isSuccess();
    }

    /**
     * 获取业务线用户持仓数据进行对比
     *
     * @param accountType
     * @param checkTime
     * @param oldUserPositionList
     */
    private void checkSymbolUserPosition(Byte accountType, Date checkTime, BigDecimal mPrice, String symbolId, List<BillUserPosition> oldUserPositionList) {
        if (CollectionUtils.isEmpty(oldUserPositionList)) {
            return;
        }
        Map<Long, List<BillUserPosition>> userPositionListMap = oldUserPositionList.stream().collect(Collectors.groupingBy(BillUserPosition::getUserId));
        Collection<BillUserPosition> newBillUserPositionList = accountAssetsServiceFactory.queryUserPosition(accountType, checkTime, new ArrayList<>(userPositionListMap.keySet()));
        if (CollectionUtils.isEmpty(newBillUserPositionList)) {
            return;
        }
        Map<String, BillUserPosition> oldUserPostionMap = oldUserPositionList.stream().collect(Collectors.toMap(item -> {
            return item.getSymbolId() + "#" + item.getUserId() + "#" + item.getCoinId();
        }, Function.identity()));
        Map<String, BillUserPosition> newUserPostionMap = newBillUserPositionList.stream().collect(Collectors.toMap(item -> {
            return item.getSymbolId() + "#" + item.getUserId() + "#" + item.getCoinId();
        }, Function.identity()));
        List<Map<String, Object>> checkResultMapList = new ArrayList<>();
        for (Map.Entry<String, BillUserPosition> entry : oldUserPostionMap.entrySet()) {
            Map<String, Object> checkResultMap = new HashMap<>();
            BillUserPosition oldUserPosition = entry.getValue();
            BigDecimal oLUnRealised = ContractFormulaUtils.calProfits(HoldSideEnum.LONG_POSITIONS, oldUserPosition.getLCount(), oldUserPosition.getLAvg(), mPrice, BillConstants.SERVER_DEFAULT_SCALE);
            BigDecimal oSUnRealised = ContractFormulaUtils.calProfits(HoldSideEnum.SHORT_POSITIONS, oldUserPosition.getSCount(), oldUserPosition.getSAvg(), mPrice, BillConstants.SERVER_DEFAULT_SCALE);
            BillUserPosition newUserPosition = newUserPostionMap.get(entry.getKey());
            if (newUserPosition == null) {
                checkResultMap.put("result", "false");
            } else {
                BigDecimal nLUnRealised = ContractFormulaUtils.calProfits(HoldSideEnum.LONG_POSITIONS, newUserPosition.getLCount(), newUserPosition.getLAvg(), mPrice, BillConstants.SERVER_DEFAULT_SCALE);
                BigDecimal nSUnRealised = ContractFormulaUtils.calProfits(HoldSideEnum.SHORT_POSITIONS, newUserPosition.getSCount(), newUserPosition.getSAvg(), mPrice, BillConstants.SERVER_DEFAULT_SCALE);
                if (nLUnRealised.compareTo(oLUnRealised) != 0 || oSUnRealised.compareTo(nSUnRealised) != 0) {
                    checkResultMap.put("result", "false");
                } else {
                    checkResultMap.put("result", "true");
                }
                checkResultMap.put("nLUnRealised", nLUnRealised);
                checkResultMap.put("nSUnRealised", nSUnRealised);
                checkResultMap.put("nLCount", newUserPosition.getLCount());
                checkResultMap.put("nLAvg", newUserPosition.getLAvg());
                checkResultMap.put("nSCount", newUserPosition.getSCount());
                checkResultMap.put("nSAvg", newUserPosition.getSAvg());
            }
            checkResultMap.put("oLUnRealised", oLUnRealised);
            checkResultMap.put("oSUnRealised", oSUnRealised);
            checkResultMap.put("mPrice", mPrice);
            checkResultMap.put("oLCount", oldUserPosition.getLCount());
            checkResultMap.put("oLAvg", oldUserPosition.getLAvg());
            checkResultMap.put("oSCount", oldUserPosition.getSCount());
            checkResultMap.put("oSAvg", oldUserPosition.getSAvg());
            checkResultMap.put("key", entry.getKey());
            if (checkResultMap.get("result").toString().equals("false")) {
                checkResultMapList.add(checkResultMap);
            }
        }
        log.info("ContractBillCheckServiceImpl.checkSymbolUserPosition accountType={} timeSlice={} data={}", accountType, DateUtil.date2str(checkTime), JSON.toJSONString(checkResultMapList));
    }

    @Override
    protected UserAssetsNegativeModel doCheckNegative(BillCoinUserProperty billCoinUserProperty, BigDecimal unRealized) {
        BigDecimal totalProp = NumberUtil.add(billCoinUserProperty.getProp2(), billCoinUserProperty.getProp3(), unRealized);
        if (totalProp.compareTo(BigDecimal.ZERO) < 0) {
            UserAssetsNegativeModel userAssetsNegativeModel = UserAssetsNegativeModel.builder()
                    .propValue(totalProp)
                    .build();
            return userAssetsNegativeModel;
        }
        return null;
    }

    @Override
    public <T extends AbstractProperty> boolean checkAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult, ApolloReconciliationBizConfig apolloBillConfig, Map<Integer, PriceVo> ratesToUSDTCoinIdMap, Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap) {
        if (property == null || totalAccountAssetsInfoResult == null) {
            return false;
        }
        if (property.getProp2().compareTo(totalAccountAssetsInfoResult.getProp2()) != 0) {
            return false;
        }
        if (property.getProp3().compareTo(totalAccountAssetsInfoResult.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractSProperty> boolean checkSpropAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult, ApolloReconciliationBizConfig apolloBillConfig, Map<Integer, PriceVo> ratesToUSDTCoinIdMap, Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap) {
        if (property == null || totalAccountAssetsInfoResult == null) {
            return false;
        }
        if (property.getSprop2().compareTo(totalAccountAssetsInfoResult.getProp2()) != 0) {
            return false;
        }
        if (property.getSprop3().compareTo(totalAccountAssetsInfoResult.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public BigDecimal getPropSumByProperty(AbstractProperty billCoinProperty) {
        return billCoinProperty == null ? BigDecimal.ZERO : billCoinProperty.getProp2().add(billCoinProperty.getProp3());
    }

    @Override
    public BigDecimal getPropByTransferFee(AbstractProperty abstractProperty) {
        return abstractProperty != null ? abstractProperty.getProp2() : BigDecimal.ZERO;
    }

    @Override
    public BigDecimal getChangePropSumByProperty(AbstractProperty abstractProperty) {
        return NumberUtil.add(abstractProperty.getChangeProp2(), abstractProperty.getChangeProp3());
    }

    @Override
    public BigDecimal getPropSumByBillChangeData(CommonBillChangeData currentBill) {
        return NumberUtil.add(currentBill.getProp2(), currentBill.getProp3());
    }

    @Override
    public BigDecimal getChangePropSumByBillChangeData(CommonBillChangeData currentBill) {
        return NumberUtil.add(currentBill.getChangeProp2(), currentBill.getChangeProp3());
    }

    @Override
    public BigDecimal getChangePropSumByAbstractProperty(AbstractProperty abstractProperty) {
        return NumberUtil.add(abstractProperty.getChangeProp2(), abstractProperty.getChangeProp3());
    }

//    @Override
//    public boolean ignoreBillPropertyMatch(CommonBillChangeData eachBill, BillCoinUserProperty billCoinUserProperty) {
//        BillLogicGroup billLogicGroup = billEngineManager.getBillLogicGroup(getAccountType());
//        BillTimeSliceCheckModule timeSliceModule = (BillTimeSliceCheckModule) billLogicGroup.getTimeSliceModule();
//        Date initTime = timeSliceModule.getLastBillTimeSliceDTO().getBillConfig().getInitTime();
//        if (eachBill.getUserCheckBizTime().getTime() > initTime.getTime()) {
//            if (billCoinUserProperty.getLastBizId() == null) {
//                billCoinUserProperty.setLastBizId(eachBill.getBizId() - 1);
//            }
//            return true;
//        }
//        return false;
//    }

    /**
     * 获取业务线类型
     *
     * @return
     */
    @Override
    public List<String> getBusinessType() {
        return Arrays.asList(
                AccountTypeEnum.S_USDT_MIX_CONTRACT_BL.getBizTypePrefix(),
                AccountTypeEnum.S_USD_MIX_CONTRACT_BL.getBizTypePrefix(),
                AccountTypeEnum.USDT_MIX_CONTRACT_BL.getBizTypePrefix(),
                AccountTypeEnum.USD_MIX_CONTRACT_BL.getBizTypePrefix(),
                AccountTypeEnum.USDC_MIX_CONTRACT_BL.getBizTypePrefix(),
                AccountTypeEnum.S_USDC_MIX_CONTRACT_BL.getBizTypePrefix()
        );

    }

    @Override
    public <T extends AbstractProperty> boolean checkPosition(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult) {
        if (property == null || totalAccountAssetsInfoResult == null) {
            return false;
        }
        if (property.getProp2().compareTo(totalAccountAssetsInfoResult.getProp2()) != 0) {
            return false;
        }
        if (property.getProp3().compareTo(totalAccountAssetsInfoResult.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitAssets(T oldProperty, BillCoinUserProperty newBillCoinUserProperty) {
        if (oldProperty == null || newBillCoinUserProperty == null) {
            return false;
        }
        if (oldProperty.getProp2().compareTo(newBillCoinUserProperty.getProp2()) != 0) {
            return false;
        }
        if (oldProperty.getProp3().compareTo(newBillCoinUserProperty.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinAssets(T oldProperty, BillCoinProperty billCoinProperty) {
        if (oldProperty == null || billCoinProperty == null) {
            return false;
        }
        if (oldProperty.getProp1().compareTo(billCoinProperty.getProp1()) != 0) {
            return false;
        }
        if (oldProperty.getProp2().compareTo(billCoinProperty.getProp2()) != 0) {
            return false;
        }
        if (oldProperty.getProp3().compareTo(billCoinProperty.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeAssets(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        if (oldProperty == null || newBillCoinTypeProperty == null) {
            return false;
        }
        if (oldProperty.getProp1().compareTo(newBillCoinTypeProperty.getProp1()) != 0) {
            return false;
        }
        if (oldProperty.getProp2().compareTo(newBillCoinTypeProperty.getProp2()) != 0) {
            return false;
        }
        if (oldProperty.getProp3().compareTo(newBillCoinTypeProperty.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinChangeAssets(T oldProperty, BillCoinProperty billCoinProperty) {
        if (oldProperty == null || billCoinProperty == null) {
            return false;
        }
        if (oldProperty.getChangeProp1().compareTo(billCoinProperty.getChangeProp1()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp2().compareTo(billCoinProperty.getChangeProp2()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp3().compareTo(billCoinProperty.getChangeProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeChangeAssets(T oldProperty, BillCoinTypeProperty billCoinTypeProperty) {
        if (oldProperty == null || billCoinTypeProperty == null) {
            return false;
        }
        if (oldProperty.getChangeProp1().compareTo(billCoinTypeProperty.getChangeProp1()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp2().compareTo(billCoinTypeProperty.getChangeProp2()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp3().compareTo(billCoinTypeProperty.getChangeProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinAssetsByAccountType(T oldProperty, BillCoinProperty newBillCoinUserProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        if (oldProperty != null) {
            dataCalResultDTO.setFactor1(oldProperty.getProp2().add(oldProperty.getProp3()));
        }
        if (newBillCoinUserProperty != null) {
            dataCalResultDTO.setFactor2(newBillCoinUserProperty.getProp2().add(newBillCoinUserProperty.getProp3()));
        }
        BigDecimal tolerance = coinComparisonToleranceMap.getOrDefault(newBillCoinUserProperty.getCoinId(), BigDecimal.ZERO);
        dataCalResultDTO.setResult(dataCalResultDTO.getFactor1().subtract(dataCalResultDTO.getFactor2()).abs());
        dataCalResultDTO.setPass(dataCalResultDTO.getResult().compareTo(tolerance) <= 0);
        return dataCalResultDTO.isPass();
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeInitAssetsByAccountType(List<T> oldPropertyList, BillCoinTypeProperty newBillCoinTypeProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        if (CollectionUtils.isEmpty(oldPropertyList) || newBillCoinTypeProperty == null) {
            return false;
        }
        BigDecimal tolerance = coinComparisonToleranceMap.getOrDefault(newBillCoinTypeProperty.getCoinId(), BigDecimal.ZERO);
        BigDecimal oldSum = BigDecimal.ZERO;
        for (T odlProperty : oldPropertyList) {
            oldSum = oldSum.add(odlProperty.getProp2()).add(odlProperty.getProp3());
        }
        BigDecimal newSum = newBillCoinTypeProperty.getProp2().add(newBillCoinTypeProperty.getProp3());
        dataCalResultDTO.setFactor1(oldSum);
        dataCalResultDTO.setFactor2(newSum);
        dataCalResultDTO.setResult(oldSum.subtract(newSum).abs());
        if (oldSum.subtract(newSum).abs().compareTo(tolerance) > 0) {
            log.info("CheckAssetSpotServiceImpl checkCoinTypeAssetsByAccountType failed , old sum {}, new sum {} , property {}", oldSum, newSum, JSONObject.toJSONString(newBillCoinTypeProperty));
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeAssetsByAccountType(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        if (oldProperty == null) {
            return Objects.isNull(newBillCoinTypeProperty) || newBillCoinTypeProperty.getChangePropSum().compareTo(BigDecimal.ZERO) == 0;
        }
        if (oldProperty.getChangeProp2().compareTo(newBillCoinTypeProperty.getChangeProp2()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp3().compareTo(newBillCoinTypeProperty.getChangeProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> BigDecimal sumForInAll(T property) {
        if (property == null) {
            return BigDecimal.ZERO;
        }
        return property.getProp2().add(property.getProp3());
    }

    @Override
    public BigDecimal getExchangeDataChangeAssets(CommonBillChangeData commonBillChangeData) {
        return commonBillChangeData.getChangeProp4();
    }

    @Override
    public AssetsBillCoinTypeProperty convertToAssetsBillCoinTypeProperty(Byte accountType, BillCoinTypeProperty billCoinTypeProperty) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        String assetsBizType = accountTypeEnum.getBizTypePrefix() + BillConstants.SEPARATOR + billCoinTypeProperty.getBizType();
        AssetsBillCoinTypeProperty assetsBillCoinTypeProperty = new AssetsBillCoinTypeProperty();
        assetsBillCoinTypeProperty.setCoinId(billCoinTypeProperty.getCoinId());
        assetsBillCoinTypeProperty.setBizType(assetsBizType);
        assetsBillCoinTypeProperty.setChangeProp2(billCoinTypeProperty.getChangeProp2());
        assetsBillCoinTypeProperty.setChangeProp3(billCoinTypeProperty.getChangeProp3());
        return assetsBillCoinTypeProperty;
    }

    @Override
    public BigDecimal getQuoteTokenAssetsByProperty(AbstractProperty abstractProperty) {
        return abstractProperty.getProp5();
    }

    @Override
    public void setInitAndUnRealizedProp(AbstractProperty billSymbolProperty, BigDecimal initValue, BigDecimal unRealized) {
        billSymbolProperty.setProp3(unRealized);
        billSymbolProperty.setProp6(initValue);
    }

    @Override
    public void setSCountLCountProp(AbstractProperty billSymbolProperty, BigDecimal sCount, BigDecimal lCount) {
        billSymbolProperty.setProp1(lCount);
        billSymbolProperty.setProp2(sCount);
    }

    @Override
    public BigDecimal getQuoteTokenChangeAssets(AbstractProperty abstractProperty) {
        return abstractProperty.getChangeProp5();
    }

    @Override
    public BigDecimal getMarginRealizedProp(AbstractProperty abstractProperty) {
        return abstractProperty.getProp4();
    }

    @Override
    public BigDecimal getMarginRealizedChangeProp(AbstractProperty abstractProperty) {
        return abstractProperty.getChangeProp4();
    }

    @Override
    public void calculateMsgTimeSliceSymbol(Byte accountType, BillTimeSliceDTO billTimeSliceDTO, CommonBillChangeData commonBillChangeData) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        String symbolId = commonBillChangeData.getSymbolId();
        BillSymbolProperty billSymbolProperty = billTimeSliceDTO.getSymbolPropertyMap().computeIfAbsent(symbolId, v -> {
            BillSymbolProperty newBillSymbolProperty = new BillSymbolProperty();
            newBillSymbolProperty.setSymbolId(symbolId);
            return newBillSymbolProperty;
        });
        billSymbolProperty.setChangeProp4(billSymbolProperty.getChangeProp4().add(commonBillChangeData.getChangeProp4()));
        billSymbolProperty.setChangeProp5(billSymbolProperty.getChangeProp5().add(commonBillChangeData.getChangeProp5()));
        // 累积已实现非重算用户已实现， 重算用户已实现在merge计算
        if (!reconSystemAccountService.isContractAdlReceivedUserId(accountTypeEnum, commonBillChangeData.getSymbolId(), commonBillChangeData.getAccountId())) {
            billSymbolProperty.setChangeProp7(billSymbolProperty.getChangeProp7().add(commonBillChangeData.getChangeProp5()));
        }
    }

    @Override
    public void calculateMsgTimeSliceSymbolCoin(Byte accountType, BillTimeSliceDTO billTimeSliceDTO, CommonBillChangeData commonBillChangeData) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        BillSymbolCoinProperty symbolCoinProperty = billTimeSliceDTO.getSymbolCoinPropertyMap().computeIfAbsent(BillSymbolCoinProperty.generateKey(commonBillChangeData.getSymbolId(), commonBillChangeData.getCoinId()), v -> {
            BillSymbolCoinProperty billSymbolCoinProperty = new BillSymbolCoinProperty();
            billSymbolCoinProperty.setSymbolId(commonBillChangeData.getSymbolId());
            billSymbolCoinProperty.setCoinId(commonBillChangeData.getCoinId());
            billSymbolCoinProperty.setAccountType(commonBillChangeData.getAccountType());
            return billSymbolCoinProperty;
        });
        symbolCoinProperty.setChangeProp4(symbolCoinProperty.getChangeProp4().add(commonBillChangeData.getChangeProp4()));
        symbolCoinProperty.setChangeProp5(symbolCoinProperty.getChangeProp5().add(commonBillChangeData.getChangeProp5()));
        symbolCoinProperty.setProp4(symbolCoinProperty.getProp4().add(commonBillChangeData.getChangeProp4()));
        symbolCoinProperty.setProp5(symbolCoinProperty.getProp5().add(commonBillChangeData.getChangeProp5()));
        // 累积已实现非重算用户已实现， 重算用户已实现在merge计算
        if (!reconSystemAccountService.isContractAdlReceivedUserId(accountTypeEnum, commonBillChangeData.getSymbolId(), commonBillChangeData.getAccountId())) {
            symbolCoinProperty.setChangeProp6(symbolCoinProperty.getChangeProp6().add(commonBillChangeData.getChangeProp5()));
            symbolCoinProperty.setProp6(symbolCoinProperty.getProp6().add(commonBillChangeData.getChangeProp5()));
        }
    }

    @Override
    public void setBillSymbolProperty(BillSymbolProperty billSymbolProperty, SymbolPropEnum symbolPropEnum, BigDecimal value) {
        billSymbolProperty.setPropByPropEnum(SYMBOL_PROP_ENUM_MAP.get(symbolPropEnum), value);
    }

    @Override
    public void setBillSymbolCoinProperty(BillSymbolCoinProperty billSymbolCoinProperty, SymbolCoinPropEnum symbolCoinPropEnum, BigDecimal value) {
        billSymbolCoinProperty.setPropByPropEnum(SYMBOL_COIN_PROP_ENUM_MAP.get(symbolCoinPropEnum), value);
    }

    @Override
    public void setBillSymbolProperty(BillSymbolProperty billSymbolProperty, Supplier<Tuple2<SymbolPropEnum, BigDecimal>> supplier) {
        Tuple2<SymbolPropEnum, BigDecimal> tuple2 = supplier.get();
        this.setBillSymbolProperty(billSymbolProperty, tuple2.getT1(), tuple2.getT2());
    }

    @Override
    public void setBillSymbolCoinProperty(BillSymbolCoinProperty billSymbolCoinProperty, Supplier<Tuple2<SymbolCoinPropEnum, BigDecimal>> supplier) {
        Tuple2<SymbolCoinPropEnum, BigDecimal> tuple2 = supplier.get();
        this.setBillSymbolCoinProperty(billSymbolCoinProperty, tuple2.getT1(), tuple2.getT2());
    }

    @Override
    public BigDecimal getBillSymbolProperty(BillSymbolProperty billSymbolProperty, SymbolPropEnum symbolPropEnum) {
        return billSymbolProperty.getPropByPropEnum(SYMBOL_PROP_ENUM_MAP.get(symbolPropEnum));
    }

    @Override
    public BigDecimal getBillSymbolCoinProperty(BillSymbolCoinProperty billSymbolCoinProperty, SymbolCoinPropEnum symbolCoinPropEnum) {
        return billSymbolCoinProperty.getPropByPropEnum(SYMBOL_COIN_PROP_ENUM_MAP.get(symbolCoinPropEnum));
    }

    @Override
    public void cleanAndSetAssetsProperty(AbstractProperty abstractProperty, Byte accountType) {
        BigDecimal prop2 = abstractProperty.getProp2();
        BigDecimal prop3 = abstractProperty.getProp3();
        BigDecimal changeProp2 = abstractProperty.getChangeProp2();
        BigDecimal changeProp3 = abstractProperty.getChangeProp3();
        abstractProperty.cleaPropAndChangeProp();
        abstractProperty.setProp2(prop2);
        abstractProperty.setProp3(prop3);
        abstractProperty.setChangeProp2(changeProp2);
        abstractProperty.setChangeProp3(changeProp3);
    }
}
