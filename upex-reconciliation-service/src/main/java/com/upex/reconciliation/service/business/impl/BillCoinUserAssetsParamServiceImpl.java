package com.upex.reconciliation.service.business.impl;

import com.upex.reconciliation.service.service.BillCoinUserAssetsParamService;
import com.upex.reconciliation.service.dao.entity.BillCoinUserAssets;
import com.upex.reconciliation.service.model.dto.BillCoinUserAssetsParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class BillCoinUserAssetsParamServiceImpl  implements BillCoinUserAssetsParamService {
    @Override
    public List<BillCoinUserAssetsParam> listAssetsParamObjects(List<BillCoinUserAssets> billCoinUserAssetsList, Byte accountType, String accountParam) {
        return null;
    }

    @Override
    public void setBillCoinUserAssetsParamProperty(BillCoinUserAssets assets) {

    }

    @Override
    public void setBillCoinUserAssetsParamProperty(List<BillCoinUserAssets> assetsList) {

    }

    @Override
    public void batchInsert(List<BillCoinUserAssetsParam> list, Byte accountType, String accountParam) {

    }

    @Override
    public List<BillCoinUserAssetsParam> queryAssetsParamByAssetsIdList(List<Long> assetsIds, Byte accountType, String accountParam) {
        return null;
    }
}
