package com.upex.reconciliation.service.common.constants.enums;


import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 对账公共业务类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum BillCommonBizTypeEnum {

    BILL_COIN_TYPE_USER("bill_coin_type_user", "对账币种类型用户明细配置"),
    DELAY_ACCOUNT_MONITOR("delay_account_monitor", "延迟入账监控配置"),

    ;

    private String typeCode;

    private String typeDesc;
}
