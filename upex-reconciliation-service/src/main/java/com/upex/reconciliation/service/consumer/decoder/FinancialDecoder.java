package com.upex.reconciliation.service.consumer.decoder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.service.BillBizTypeConfigService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.BUSINESS_CHECK_START_ERROR;

@Service
@Slf4j
public class FinancialDecoder extends AbstractMessageDecoder {
    @Autowired
    private BillBizTypeConfigService billBizTypeConfigService;
    private Byte accountType = AccountTypeEnum.FINANCIAL.getCode();
    /***双币理财赎回 比如：买A得A或B***/
    private String DUAL_INVEST_OUT = "503";
    /***区间猎手理财赎回 比如：买A得A或B***/
    private String RANGE_SNIPER_OUT = "902";
    /***双币理财赎回 比如：买A得A或B 虚拟流水类型***/
    private String DUAL_INVEST_SPOT_OUT = "506";
    /***区间猎手理财赎回 比如：买A得A或B 虚拟流水类型***/
    private String RANGE_SNIPER_SPOT_OUT = "905";
    /***囤币卖币理财赎回 比如：买A得A或B 虚拟流水类型***/
    private String CUSTOM_STRUCTURED_OUT = "1802";
    /***囤币卖币理财赎回 比如：买A得A或B 虚拟流水类型***/
    private String CUSTOM_STRUCTURED_SPOT_OUT = "1803";
    @Resource
    private AlarmNotifyService alarmNotifyService;

    @Override
    public List<CommonBillChangeData> doMessageDecode(List<Map<String, String>> dataList, FlatMessage flatMessage, Integer partition, Long offset, Byte accountType) {
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        List<CommonBillChangeData> commonBillChangeDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Map<String, String> map : dataList) {
                String bizType = map.get("fin_biz_type");
                if (!apolloBizConfig.getPrincipalAndSettleInterestFinBizTypeList().contains(bizType)) {
                    log.info("FinancialDecoder.doMessageDecode principalAndSettleInterestFinBizTypeList not contains data:{}", JSON.toJSONString(map));
                    continue;
                }
                CommonBillChangeData commonBillChangeData = new CommonBillChangeData();
                commonBillChangeData.setAccountType(accountType);
                commonBillChangeData.setPartition(partition);
                commonBillChangeData.setOffset(offset);
                commonBillChangeData.setBizId(Long.valueOf(map.get("id")));
                commonBillChangeData.setOrderId(map.get("biz_order_id"));
                commonBillChangeData.setAccountId(Long.parseLong(map.get("account_id")));
                commonBillChangeData.setBizTime(DateUtil.getMillisecondDate(map.get("biz_time")));
                commonBillChangeData.setCreateTime(DateUtil.getMillisecondDate(map.get("create_time")));
                commonBillChangeData.setBizTimeFromId(DateUtil.getMillisecondDate(map.get("biz_time")));
                commonBillChangeData.setSymbolId(map.get("group_type"));
                // 设置可用变动 和 冻结变动
                BigDecimal afterBalance = new BigDecimal(map.get("after_balance_change"));
                BigDecimal balanceChange = new BigDecimal(map.get("balance_change"));
                if (apolloBizConfig.getFinancialSpotSameSettleFinBizTypeList().contains(bizType)) {
                    commonBillChangeData.setBizType(bizType);
                    commonBillChangeData.setCoinId(Integer.valueOf(map.get("coin_id")));
                    commonBillChangeData.setChangeProp1(balanceChange);
                    commonBillChangeData.setProp1(afterBalance);
                    commonBillChangeData.setChangeProp2(balanceChange);
                    commonBillChangeDataList.add(commonBillChangeData);
                } else if (apolloBizConfig.getFinancialSpotDiffSettleFinBizTypeList().contains(bizType)) {
                    // 现货流水
                    CommonBillChangeData spotInfoCommonBillChangeData = commonBillChangeData.clone();
                    JSONObject remarkJson = JSON.parseObject(map.get("remark"));
                    spotInfoCommonBillChangeData.setCoinId(remarkJson.getInteger("settleCoinId"));
                    spotInfoCommonBillChangeData.setChangeProp2(remarkJson.getBigDecimal("settleAmount"));
                    if (DUAL_INVEST_OUT.equals(bizType)) {
                        spotInfoCommonBillChangeData.setBizType(String.valueOf(DUAL_INVEST_SPOT_OUT));
                    } else if (RANGE_SNIPER_OUT.equals(bizType)) {
                        spotInfoCommonBillChangeData.setBizType(String.valueOf(RANGE_SNIPER_SPOT_OUT));
                    }else if (CUSTOM_STRUCTURED_OUT.equals(bizType)) {
                        spotInfoCommonBillChangeData.setBizType(String.valueOf(CUSTOM_STRUCTURED_SPOT_OUT));
                    }
                    spotInfoCommonBillChangeData.setIgnoreUserPropCheck(true);
                    // 理财流水
                    commonBillChangeData.setBizType(bizType);
                    commonBillChangeData.setCoinId(Integer.valueOf(map.get("coin_id")));
                    commonBillChangeData.setChangeProp1(balanceChange);
                    commonBillChangeData.setProp1(afterBalance);
                    // 放入队列 注意顺序 只能先放流水
                    commonBillChangeDataList.add(commonBillChangeData);
                    // 虚拟流水
                    commonBillChangeDataList.add(spotInfoCommonBillChangeData);
                } else if (apolloBizConfig.getFinancialDeductPrincipalBizTypeList().contains(bizType)) {
                    commonBillChangeData.setBizType(bizType);
                    commonBillChangeData.setCoinId(Integer.valueOf(map.get("coin_id")));
                    commonBillChangeData.setChangeProp1(balanceChange);
                    commonBillChangeData.setProp1(afterBalance);
                    commonBillChangeDataList.add(commonBillChangeData);
                } else if (apolloBizConfig.getSettleInterestFinBizTypeList().contains(bizType)) {
                    commonBillChangeData.setBizType(bizType);
                    commonBillChangeData.setCoinId(Integer.valueOf(map.get("coin_id")));
                    commonBillChangeData.setChangeProp3(balanceChange);
                    commonBillChangeData.setIgnoreUserPropCheck(true);
                    commonBillChangeDataList.add(commonBillChangeData);
                } else {
                    log.info("FinancialDecoder.doMessageDecode error data:{}", JSON.toJSONString(map));
                    alarmNotifyService.alarm(BUSINESS_CHECK_START_ERROR, accountType, JSON.toJSONString(map));
                    continue;
                }
            }
        }
        return commonBillChangeDataList;
    }
}
