package com.upex.reconciliation.service.common.constants.enums;

import com.upex.bill.dto.enums.AccountTypeEnum;
import com.upex.mixcontract.common.literal.enums.BusinessLineEnum;

public enum BusinessLineMappingEnum {
    S_USDT_MIX_CONTRACT_BL(AccountTypeEnum.S_USDT_MIX_CONTRACT_BL.getIntegerCode()
            , BusinessLineEnum.S_USDT_MIX_CONTRACT_BL.getCode(), "USDT模拟混合合约业务线"),
    S_USD_MIX_CONTRACT_BL(AccountTypeEnum.S_USD_MIX_CONTRACT_BL.getIntegerCode()
            , BusinessLineEnum.S_USD_MIX_CONTRACT_BL.getCode(), "USD模拟混合合约业务线"),
    S_USDC_MIX_CONTRACT_BL(AccountTypeEnum.S_USDC_MIX_CONTRACT_BL.getIntegerCode()
            , BusinessLineEnum.S_USDC_MIX_CONTRACT_BL.getCode(), "USDC模拟混合合约业务线"),

    USDT_MIX_CONTRACT_BL(AccountTypeEnum.USDT_MIX_CONTRACT_BL.getIntegerCode()
            , BusinessLineEnum.USDT_MIX_CONTRACT_BL.getCode(), "USDT混合合约业务线"),

    USD_MIX_CONTRACT_BL(AccountTypeEnum.USD_MIX_CONTRACT_BL.getIntegerCode()
            , BusinessLineEnum.USD_MIX_CONTRACT_BL.getCode(), "USD混合合约业务线"),

    USDC_MIX_CONTRACT_BL(AccountTypeEnum.USDC_MIX_CONTRACT_BL.getIntegerCode()
            , BusinessLineEnum.USDC_MIX_CONTRACT_BL.getCode(), "USDC混合合约业务线");

    private Integer accountType;
    private Integer businessLine;
    private String desc;

    BusinessLineMappingEnum(Integer accountType, Integer businessLine, String desc) {
        this.accountType = accountType;
        this.businessLine = businessLine;
        this.desc = desc;
    }

    public static Integer toBusinessLine(Integer accountType) {
        for (BusinessLineMappingEnum item : BusinessLineMappingEnum.values()) {
            if (item.accountType.equals(accountType)) {
                return item.businessLine;
            }
        }
        return null;
    }

}
