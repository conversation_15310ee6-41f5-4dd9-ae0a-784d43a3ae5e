package com.upex.reconciliation.service.dao.cex.entity;

import lombok.Data;

import java.util.Date;
@Data
public class UserWithdrawAddress {

    private Long id;
    private String network;
    private String address;
    private Integer coinId;
    private String coinName;
    private Integer chainCoinid;
    private Long bgUid;
    private Date createTime;
    private Date updateTime;
    private Long version;


}
