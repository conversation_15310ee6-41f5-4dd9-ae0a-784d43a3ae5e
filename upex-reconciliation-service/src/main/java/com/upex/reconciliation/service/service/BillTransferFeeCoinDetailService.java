package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillTransferFeeCoinDetail;
import com.upex.reconciliation.service.dao.mapper.BillTransferFeeCoinDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class BillTransferFeeCoinDetailService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billTransferFeeCoinDetailMapper")
    private BillTransferFeeCoinDetailMapper billTransferFeeCoinDetailMapper;


    /**
     * 批量插入
     *
     * @param records
     * @param accountType
     * @param accountParam
     * @return
     */
    public int batchInsert(Collection<BillTransferFeeCoinDetail> records, Byte accountType, String accountParam) {
        if (CollectionUtils.isNotEmpty(records)) {
            return dbHelper.doDbOpInReconMaster(() -> billTransferFeeCoinDetailMapper.batchInsert(records, accountType, accountParam));
        }
        return 0;
    }

    /**
     * 批量插入
     *
     * @param records
     * @param accountType
     * @param accountParam
     * @return
     */
    public int batchInsertHis(List<BillTransferFeeCoinDetail> records, Byte accountType, String accountParam) {
        if (CollectionUtils.isNotEmpty(records)) {
            return dbHelper.doDbOpInReconMaster(() -> billTransferFeeCoinDetailMapper.batchInsert(records, accountType, accountParam));
        }
        return 0;
    }

    public List<BillTransferFeeCoinDetail> selectCheckTimeRecord(Byte accountType,
                                                                 String accountParam,
                                                                 Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billTransferFeeCoinDetailMapper.selectCheckTimeRecord(accountType, accountParam, checkTime));
    }

    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billTransferFeeCoinDetailMapper.deleteByCheckTime(accountType, accountParam, checkTime));
    }
}
