package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class UniversalTransferRecordInnerRes {
    /**
     * {
     *             "timestamp": 1747899142000,
     *             "asset": "LDUSDT",
     *             "amount": "11.752223",
     *             "type": "MAIN_UMFUTURE",
     *             "status": "CONFIRMED",
     *             "tranId": 264018774104
     *         }
     */
    private Long timestamp;
    private String asset;
    private BigDecimal amount;
    private String type;
    private String status;
    private Long tranId;

}
