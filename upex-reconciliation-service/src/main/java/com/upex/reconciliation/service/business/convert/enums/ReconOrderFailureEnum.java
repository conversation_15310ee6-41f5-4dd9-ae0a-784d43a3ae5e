package com.upex.reconciliation.service.business.convert.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ReconOrderFailureEnum {

    INCONSISTENT_COIN_TYPE(1001, "币种不一致"),
    INCONSISTENT_BALANCE_CHANGE(1002, "余额变更不一致"),
    INCONSISTENT_COIN_COUNT(1003, "订单金额与流水金额不一致"),
    PRICE_DATA_MISSING(1004, "价格数据缺失"),
    INCONSISTENT_PRICE_DATA(1005, "价格数据不一致"),
    TIMEOUT_MAIN(1006, "主订单延迟"),
    TIMEOUT_FLOW(1007, "流水订单延迟"),
    INCONSISTENT_QUOTA_PRICE(1008, "行情价格校验异常"),
    UNKNOWN_ERROR(9999, "未知的对账异常");

    private final int code;
    private final String message;
}
