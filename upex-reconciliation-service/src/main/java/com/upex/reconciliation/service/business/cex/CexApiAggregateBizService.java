package com.upex.reconciliation.service.business.cex;

import com.google.common.collect.Lists;
import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.dao.cex.entity.CexAssetConfig;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserAssetHistory;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.model.config.ApolloThirdCexAssetConfig;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.*;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.*;
import com.upex.reconciliation.service.service.client.cex.enmus.*;
import com.upex.reconciliation.service.service.client.cex.utils.CexUserHolder;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.reconciliation.service.utils.TimeSliceCalcUtils;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.common.constants.BillConstants.MILLIS_PER_MINUTE;
import static com.upex.reconciliation.service.service.client.cex.CexConstants.*;
import static com.upex.reconciliation.service.service.client.cex.CexConstants.USDC;

@Slf4j
@Service
public class CexApiAggregateBizService {

    @Resource
    CexApiService cexApiService;


    @Resource
    ThirdCexUserAssetHistoryService thirdCexUserAssetHistoryService;

    @Resource
    ThirdCexUserService thirdCexUserService;

    @Resource
    CexAssetconfigService cexAssetconfigService;

    @Resource
    CexUserConfigService cexUserConfigService;

    @Resource
    CexCoinConfigBizService cexCoinConfigBizService;

    @Resource
    CommonService commonService;


    // 使用 Map 缓存不同资产类型对应的方法
    private final Map<ThirdAssetType, Function<CommonReq, CommonRes<CommonCoinAssetRes>>> ASSET_QUERY_MAP = new HashMap<>();

    @Resource
    List<ICexAssetSyncHistory> cexAssetSyncHistories;

    Map<CexAssetHistoryTypeEnum, ICexAssetSyncHistory> cexAssetSyncHistoryMap = new HashMap<>();

    @Resource
    AlarmNotifyService alarmNotifyService;

    @PostConstruct
    void init() {
        // 母用户资产
        ASSET_QUERY_MAP.put(ThirdAssetType.PARENT_SPOT, cexApiService::querySpotPosition);
        ASSET_QUERY_MAP.put(ThirdAssetType.PARENT_FUNDING, cexApiService::queryFundingCoinAsset);
        ASSET_QUERY_MAP.put(ThirdAssetType.PARENT_MARGIN, cexApiService::queryMarginCoinAsset);
        ASSET_QUERY_MAP.put(ThirdAssetType.PARENT_ISOLATED_MARGIN, cexApiService::queryIsolatedMarginCoinAsset);
        ASSET_QUERY_MAP.put(ThirdAssetType.PARENT_UCONTRACT, cexApiService::queryUContractCoinAsset);
        ASSET_QUERY_MAP.put(ThirdAssetType.PARENT_COIN_CONTRACT, cexApiService::queryCoinContractCoinAsset);
        ASSET_QUERY_MAP.put(ThirdAssetType.PARENT_FLEX_EARN, cexApiService::queryFlexEarnPosition);
        ASSET_QUERY_MAP.put(ThirdAssetType.PARENT_LOCKED_EARN, cexApiService::queryLockedEarnPosition);

        // 子用户资产
        ASSET_QUERY_MAP.put(ThirdAssetType.SUB_SPOT, cexApiService::querySubUserSpotAsset);
        ASSET_QUERY_MAP.put(ThirdAssetType.SUB_MARGIN, cexApiService::querySubUserMarginAsset);
        ASSET_QUERY_MAP.put(ThirdAssetType.SUB_UCONTRACT, cexApiService::querySubUserUContractAsset);
        ASSET_QUERY_MAP.put(ThirdAssetType.SUB_COIN_CONTRACT, cexApiService::querySubUserCoinContractAsset);

        cexAssetSyncHistories.forEach(assetHistory -> {
            cexAssetSyncHistoryMap.put(assetHistory.getAssetHistoryType(), assetHistory);
        });
    }

    public CommonRes<CommonCoinAssetRes> queryUserAssetByAssetType(CommonReq commonReq, ThirdAssetType thirdAssetType) {
        Function<CommonReq, CommonRes<CommonCoinAssetRes>> queryFunction = ASSET_QUERY_MAP.get(thirdAssetType);
        if (queryFunction == null) {
            throw new ApiException(ReconCexExceptionEnum.UNSUPPORTED_ASSET_TYPE);
        }
        CommonRes<CommonCoinAssetRes> assetRes = queryFunction.apply(commonReq);
        return assetRes;
    }

    public CommonRes<CommonTotalAssetRes> queryCoinAssetDetail(UserAssetDetailReq userAssetDetailReq) {
        ThirdCexUser thirdCexUser = thirdCexUserService.selectByCexTypeAndUserId(userAssetDetailReq.getCexType(), userAssetDetailReq.getCexUserId());
        if (thirdCexUser == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_NOT_EXISTS);
        }
        if (!ThirdAssetType.isParent(userAssetDetailReq.getAssetType())) {
            userAssetDetailReq.setCexEmail(thirdCexUser.getCexEmail());
        }
        CommonRes<CommonCoinAssetRes> commonRes = queryUserAssetByAssetType(userAssetDetailReq, userAssetDetailReq.getAssetType());
        log.info("queryUserAssetByAssetType,thirdAssetType:{},cexUserId:{}", userAssetDetailReq.getAssetType(), userAssetDetailReq.getCexUserId());
        CommonTotalAssetRes totalAssetRes = new CommonTotalAssetRes();
        totalAssetRes.addThirdAssetTypeList(userAssetDetailReq.getAssetType(), commonRes.getData());
        CommonRes<CommonTotalAssetRes> commonTotalAssetRes = CommonRes.getSucApiBaseRes(totalAssetRes);
        calculateCoinAsset(commonTotalAssetRes);
        return commonTotalAssetRes;
    }

    void calculateCoinAsset(CommonRes<CommonTotalAssetRes> commonRes) {
        CommonTotalAssetRes commonTotalAssetRes = commonRes.getData();
        BigDecimal totalAmount = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(commonTotalAssetRes.getThirdAssetTypeAmounts())) {
            commonTotalAssetRes.setThirdAssetTypeAmounts(new ArrayList<>());
            commonTotalAssetRes.setTotalAmount(BigDecimal.ZERO);
            return;
        }
        log.info("BeginCaculateAssetByCoinName,coinName:{}", commonRes.getData().getCalAssetCoinName());
        for (CommonTotalAssetRes.ThirdAssetTypeAmount thirdAssetTypeAmount : commonTotalAssetRes.getThirdAssetTypeAmounts()) {
            CommonCoinAssetRes commonCoinAssetRes = thirdAssetTypeAmount.getCoinAssetRes();
            if (CollectionUtils.isNotEmpty(commonCoinAssetRes)) {
                //各账户资产余额(各币种汇总)
                BigDecimal amount = calAmountByCoinName(commonCoinAssetRes, commonTotalAssetRes.getCalAssetCoinName());
                commonTotalAssetRes.setThirdTypeAssetAmount(thirdAssetTypeAmount.getThirdAssetType(), amount);
                totalAmount = totalAmount.add(amount);
            } else {
                commonTotalAssetRes.setThirdTypeAssetAmount(thirdAssetTypeAmount.getThirdAssetType(), BigDecimal.ZERO);
            }
        }
        log.info("EndCaculateAssetByCoinName,coinName:{}", commonRes.getData().getCalAssetCoinName());
        commonTotalAssetRes.setTotalAmount(totalAmount);
    }

    BigDecimal calAmountByCoinName(CommonCoinAssetRes commonCoinAssetRes, String calAssetCoinName) {
        if (StringUtils.isEmpty(calAssetCoinName)) {
            calAssetCoinName = USDT;
        }
        BigDecimal thirdAssetTypeAmount = BigDecimal.ZERO;
        Date timeKey = DateUtil.getLastMin(new Date(), BillConstants.ONE);
        Map<Integer, PriceVo> ratesMap = commonService.getCoinIdRatesMapCache(timeKey.getTime());
        for (CommonCoinAssetInnerRes item : commonCoinAssetRes) {
            String coinName = cexCoinConfigBizService.selectBgCoinNameByCexTypeAndCexCoinName(item.getCexType(), item.getCoinName());
            if (coinName == null) {
                log.error("FailToGetCoinId cexType:{},coinName:{}", item.getCexType(), item.getCoinName());
                alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_COIN_NAME_NOT_FOUND, CexTypeEnum.fromType(item.getCexType()).getName(), item.getCoinName());
                continue;
            }
            Integer coinId = commonService.getAllCoinId2Name().get(coinName);
            if (coinId == null) {
                log.warn("FailToGetCoinId coinName : {}", coinName);
                alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_COIN_ID_NOT_FOUND, CexTypeEnum.fromType(item.getCexType()).getName(), coinName);
                continue;
            }
            BigDecimal coin2urate = commonService.checkRateBySwapTokenIdReturnUSDT(coinId, ratesMap);
            if (coin2urate == null) {
                log.warn("FailToGetCoinRate coinId : {}", coinId);
                alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_COIN_NO_RATE, CexTypeEnum.fromType(item.getCexType()).getName(), coinName, coinId);
                continue;
            }
            log.info("GETCoinRateSUC,coinName:{},coinId:{},coin2urate:{},calAssetCoinName:{}", coinName, coinId, coin2urate, calAssetCoinName);
            item.setUsdtBalance(item.getTotalBalance().multiply(coin2urate));
            if (calAssetCoinName.equalsIgnoreCase(USDT)) {
                thirdAssetTypeAmount = thirdAssetTypeAmount.add(item.getTotalBalance().multiply(coin2urate));
            } else if (calAssetCoinName.equalsIgnoreCase(BTC)) {
                Integer btcCoinId = commonService.getAllCoinId2Name().get(BTC);
                BigDecimal btc2urate = commonService.checkRateBySwapTokenIdReturnUSDT(btcCoinId, ratesMap);
                BigDecimal u2btcrate = BigDecimal.ONE.divide(btc2urate, ACCURACY, RoundingMode.UP);
                thirdAssetTypeAmount = thirdAssetTypeAmount.add(item.getUsdtBalance().multiply(u2btcrate));
                log.info("FinshBTCCalCoinAsset,coinName:{},calAssetCoinName{},btc2urate:{},u2btcrate:{},totalbalance:{},usdtbalance:{},thirdTypeAmount:{}", coinName, calAssetCoinName, btc2urate, u2btcrate, item.getTotalBalance());
            } else if (calAssetCoinName.equalsIgnoreCase(USDC)) {
                Integer usdcCoinId = commonService.getAllCoinId2Name().get(USDC);
                BigDecimal usdc2urate = commonService.checkRateBySwapTokenIdReturnUSDT(usdcCoinId, ratesMap);
                BigDecimal u2usdcrate = BigDecimal.ONE.divide(usdc2urate, ACCURACY, RoundingMode.UP);
                thirdAssetTypeAmount = thirdAssetTypeAmount.add(item.getUsdtBalance().multiply(u2usdcrate));
                log.info("FinshUSDCCalCoinAsset,coinName:{},calAssetCoinName{},usdc2urate:{},u2usdcrate:{},totalbalance:{},usdtbalance:{},thirdTypeAmount:{}", coinName, calAssetCoinName, usdc2urate, u2usdcrate, item.getTotalBalance());
            }
            log.info("FinshCalCoinAsset,coinName:{},calAssetCoinName{}", coinName, calAssetCoinName);
        }
        return thirdAssetTypeAmount;
    }


    <R extends CommonTotalAssetRes> void syncTotalAsset(CommonRes<R> commonRes, Integer cexType, String userId, Date checkSyncTime) {
        if (!commonRes.getSuccess()) {
            log.warn("查询资产失败,交易所:{},用户ID:{}", cexType, userId);
            return;
        }
        CommonTotalAssetRes commonTotalAssetRes = commonRes.getData();
        if (CollectionUtils.isEmpty(commonTotalAssetRes.getThirdAssetTypeAmounts())) {
            log.warn("查询资产为空,交易所:{},用户ID:{}", cexType, userId);
            return;
        }
        List<ThirdCexUserAssetHistory> assetHistoryList = new ArrayList<>();
        for (CommonTotalAssetRes.ThirdAssetTypeAmount thirdAssetTypeAmount : commonTotalAssetRes.getThirdAssetTypeAmounts()) {
            CommonCoinAssetRes commonCoinAssetRes = thirdAssetTypeAmount.getCoinAssetRes();
            if (CollectionUtils.isEmpty(commonCoinAssetRes)) {
                continue;
            }
            List<ThirdCexUserAssetHistory> thirdAssetHistoryList = buildThirdCexHistoryList(commonCoinAssetRes, cexType, userId, checkSyncTime, SerialNoGenerator.getMinIdByTime(new Date()));
            assetHistoryList.addAll(thirdAssetHistoryList);
        }
        log.info("buildThirdCexHistoryList cexType:{},userId:{}", cexType, userId);
        ApolloThirdCexAssetConfig apolloThirdCexAssetConfig = ReconciliationApolloConfigUtils.getThirdCexAssetConfig();
        List<List<ThirdCexUserAssetHistory>> partitionList = Lists.partition(assetHistoryList, apolloThirdCexAssetConfig.getSqlInsertSize());
        for (List<ThirdCexUserAssetHistory> partition : partitionList) {
            thirdCexUserAssetHistoryService.batchInsert(partition);
        }
        log.info("syncTotalAsset partitionList cexType:{},userId:{}", cexType, userId);
    }

    CommonRes<CommonTotalAssetRes> buildCommonTotalAssetRes(List<ThirdCexUserAssetHistory> totalUserAsset, List<Integer> thirdAssetTypeEnums, UserAssetListReq userAssetListReq) {
        if (CollectionUtils.isEmpty(totalUserAsset)) {
            log.warn("查询用户资产为空,cexUserId:{},cexType:{}", userAssetListReq.getCexUserId(), userAssetListReq.getCexType());
        }
        log.info("buildCommonTotalAssetRes cexType:{},userId:{}", userAssetListReq.getCexType(), userAssetListReq.getCexUserId());
        Map<Integer, List<ThirdCexUserAssetHistory>> assetGroupByThirdAssetType = totalUserAsset.stream()
                .collect((Collectors.groupingBy(ThirdCexUserAssetHistory::getThirdAssetType)));
        CommonTotalAssetRes commonTotalAssetRes = new CommonTotalAssetRes();
        for (Integer thirdAssetType : thirdAssetTypeEnums) {
            ThirdAssetType thirdAssetTypeEnum = ThirdAssetType.fromType(thirdAssetType);
            if (thirdAssetTypeEnum == null) {
                log.warn("cexType:{},cexUserId:{},thirdAssetType:{}", userAssetListReq.getCexType(), userAssetListReq.getCexUserId(), thirdAssetType);
                continue;
            }
            List<ThirdCexUserAssetHistory> thirdCexUserAssetHistories = assetGroupByThirdAssetType.get(thirdAssetType);
            if (CollectionUtils.isEmpty(thirdCexUserAssetHistories)) {
                commonTotalAssetRes.addThirdAssetTypeList(thirdAssetTypeEnum,
                        new CommonCoinAssetRes());
                log.warn("thirdCexUserAssetHistoriesEmpty:cexType:{},thirdAssetType:{}", userAssetListReq.getCexType(), thirdAssetType);
                continue;
            }
            CommonCoinAssetRes commonCoinAssetRes = buildCommonCoinAssetRes(thirdCexUserAssetHistories);
            commonTotalAssetRes.addThirdAssetTypeList(thirdAssetTypeEnum, commonCoinAssetRes);
        }
        CommonRes<CommonTotalAssetRes> commonRes = CommonRes.getSucApiBaseRes(commonTotalAssetRes);
        commonRes.getData().setCalAssetCoinName(userAssetListReq.getCoinName());
        calculateCoinAsset(commonRes);
        return commonRes;
    }

    CommonCoinAssetRes buildCommonCoinAssetRes(List<ThirdCexUserAssetHistory> thirdCexUserAssetHistorys) {
        List<CommonCoinAssetInnerRes> commonCoinAssetInnerResList = new ArrayList<>();
        CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
        if (CollectionUtils.isNotEmpty(thirdCexUserAssetHistorys)) {
            for (ThirdCexUserAssetHistory thirdCexUserAssetHistory : thirdCexUserAssetHistorys) {
                CommonCoinAssetInnerRes commonCoinAssetInnerRes = buildCoinAssetRes(thirdCexUserAssetHistory);
                commonCoinAssetInnerResList.add(commonCoinAssetInnerRes);
            }
        }
        commonCoinAssetRes.addAll(commonCoinAssetInnerResList);
        return commonCoinAssetRes;
    }

    CommonCoinAssetInnerRes buildCoinAssetRes(ThirdCexUserAssetHistory thirdCexUserAssetHistory) {
        return new CommonCoinAssetInnerRes(
                thirdCexUserAssetHistory.getCoinName(),
                thirdCexUserAssetHistory.getTotalBalance(),
                thirdCexUserAssetHistory.getAvaiableBalance(),
                thirdCexUserAssetHistory.getBorrowedBalance(),
                thirdCexUserAssetHistory.getMarginBalance(),
                thirdCexUserAssetHistory.getChangeTime(),
                thirdCexUserAssetHistory.getThirdAssetType(),
                thirdCexUserAssetHistory.getCreateTime(),
                thirdCexUserAssetHistory.getCexType()

        );
    }

    List<ThirdCexUserAssetHistory> buildThirdCexHistoryList(CommonCoinAssetRes commonCoinAssetRes, Integer cexType, String userId, Date checkSyncTime, Long versionId) {
        List<ThirdCexUserAssetHistory> thirdCexUserAssetHistoryList = new ArrayList<>();
        for (CommonCoinAssetInnerRes commonCoinAssetInnerRes : commonCoinAssetRes) {
            ThirdCexUserAssetHistory thirdCexUserAssetHistory = new ThirdCexUserAssetHistory();
            thirdCexUserAssetHistory.setCexType(cexType);
            thirdCexUserAssetHistory.setCexUserId(userId);
            thirdCexUserAssetHistory.setCoinName(commonCoinAssetInnerRes.getCoinName());
            thirdCexUserAssetHistory.setTotalBalance(commonCoinAssetInnerRes.getTotalBalance());
            thirdCexUserAssetHistory.setAvaiableBalance(commonCoinAssetInnerRes.getAvailableBalance());
            thirdCexUserAssetHistory.setMarginBalance(commonCoinAssetInnerRes.getMarginBalance());
            thirdCexUserAssetHistory.setBorrowedBalance(commonCoinAssetInnerRes.getBorrowedBalance());
            thirdCexUserAssetHistory.setThirdAssetType(commonCoinAssetInnerRes.getThirdAssetType());
            thirdCexUserAssetHistory.setChangeTime(commonCoinAssetInnerRes.getChangeTime() != null ? new Date(commonCoinAssetInnerRes.getChangeTime()) : null);
            thirdCexUserAssetHistory.setCheckSyncTime(checkSyncTime);
            thirdCexUserAssetHistory.setCreateTime(new Date());
            thirdCexUserAssetHistory.setUpdateTime(new Date());
            thirdCexUserAssetHistory.setVersion(versionId);
            thirdCexUserAssetHistoryList.add(thirdCexUserAssetHistory);
        }
        return thirdCexUserAssetHistoryList;
    }


    public void queryCexAssetAndSync(CexAssetHistoryTypeEnum cexAssetHistoryTypeEnum) throws InterruptedException {
        log.info("queryCexAssetAndSync cexAssetHistoryTypeEnum:{}", cexAssetHistoryTypeEnum.getName());
        List<ThirdCexUser> thirdCexUsers = thirdCexUserService.selectAllParentUserByCexType(CexTypeEnum.BINANCE.getType());
        if (CollectionUtils.isEmpty(thirdCexUsers)) {
            log.warn("NoCexUser is null,cexType:{}", CexTypeEnum.BINANCE.getType());
            return;
        }
        for (ThirdCexUser cexUser : thirdCexUsers) {
            ThirdCexUserConfig userConfig = cexUserConfigService.getEffectiveAssetMonitorConfigByUserId(cexUser.getCexType(), cexUser.getCexUserId());
            if (userConfig == null) {
                log.warn("NoCexUserAssetMonitorConfig is null,cexType:{},userId:{},cexAssetHistoryTypeEnum:{}", cexUser.getCexType(), cexUser.getCexUserId(), cexAssetHistoryTypeEnum.getName());
//                alarmNotifyService.alarm(AlarmTemplateEnum.THIRD_CEX_USER_NOT_CONFIG_ASSET_API, cexUser.getCexUserId(), CexTypeEnum.fromType(cexUser.getCexType()).getName());
                continue;
            }
            ApolloThirdCexAssetConfig apolloThirdCexAssetConfig = ReconciliationApolloConfigUtils.getThirdCexAssetConfig();
            Map<String, ApolloThirdCexAssetConfig.ApolloCexAssetSyncConfig> cexAssetSyncConfigHashMap = apolloThirdCexAssetConfig.getCexAssetSyncConfigHashMap();
            CexAssetConfig cexAssetConfig = cexAssetconfigService.selectByAssetType(userConfig.getCexType(), cexUser.getCexUserId(), cexAssetHistoryTypeEnum.getValue());
            ApolloThirdCexAssetConfig.ApolloCexAssetSyncConfig cexAssetSyncConfig = cexAssetSyncConfigHashMap.get(cexAssetHistoryTypeEnum.getName());
            syncAssetAndModConfig(userConfig, cexAssetConfig, cexAssetSyncConfig, cexAssetHistoryTypeEnum);
        }
        log.info("queryCexAssetAndSync end,cexAssetHistoryTypeEnum:{}", cexAssetHistoryTypeEnum.getName());
    }

    void syncAssetAndModConfig(ThirdCexUserConfig userConfig, CexAssetConfig cexAssetConfig,
                               ApolloThirdCexAssetConfig.ApolloCexAssetSyncConfig cexAssetSyncConfig,
                               CexAssetHistoryTypeEnum cexAssetHistoryTypeEnum) throws InterruptedException {
        ICexAssetSyncHistory cexAssetSyncHistory = cexAssetSyncHistoryMap.get(cexAssetHistoryTypeEnum);
        if (cexAssetSyncHistory == null) {
            throw new ApiException(ReconCexExceptionEnum.UNSUPPORTED_ASSET_SYNC_TYPE);
        }
        if (cexAssetSyncConfig == null) {
            log.info("NoCexAssetSyncConfig is null,cexType:{},cexAssetHistoryTypeEnum:{}", CexTypeEnum.fromType(userConfig.getCexType()).getName(), cexAssetHistoryTypeEnum.getName());
            return;
        }
        Date endTime, startTime;
        Boolean ifInited = cexAssetConfig != null && !cexAssetConfig.getStatus().equals(CexAssetSyncEnum.NOT_INIT.getValue());
        //当初始化出错错误，可通过设置isinit参数强制重新初始化，并且通过设置初始化时间小于当前时间8分钟以内，则进行初始化
        if (cexAssetSyncConfig.getInitRefersh() && System.currentTimeMillis()-cexAssetSyncConfig.getInitSetTime()>0&&System.currentTimeMillis() - cexAssetSyncConfig.getInitSetTime() < BillConstants.EIGHT * MILLIS_PER_MINUTE) {
            if(StringUtils.isEmpty(userConfig.getCexUserId())){
                ifInited = Boolean.FALSE;
                log.info("AssetRefreshAllUser ifInit:{},initSetTime:{}", ifInited, cexAssetSyncConfig.getInitSetTime());
            }
            if(StringUtils.isNotEmpty(userConfig.getCexUserId())&&userConfig.getCexUserId().equals(cexAssetSyncConfig.getUserId())) {
                ifInited = Boolean.FALSE;
                log.info("AssetRefreshUser userId:{} ifInit:{},initSetTime:{}", userConfig.getCexUserId(),ifInited, cexAssetSyncConfig.getInitSetTime());
            }
        }
        Boolean isSync = Boolean.TRUE;
        Date now = new Date();
        if (!ifInited) {
            Long initTime = cexAssetSyncConfig.getSyncRecordInitTime();
            startTime = new Date(initTime);
        } else {
            startTime = cexAssetConfig.getCheckSyncTime();
        }
        //如果当前时间不是整5(10)分钟时间，先把当前时间转换成整5(10)分钟时间 如当前时间：18:51 转换后是18:45 然后加整5（10）分钟，最终endTime=18:50
        endTime = new Date(TimeSliceCalcUtils.getTimeSlice(now.getTime(), -Long.valueOf(cexAssetSyncConfig.getSyncInterval())));
        endTime = DateUtils.addSeconds(endTime, cexAssetSyncConfig.getSyncInterval());
        Date checkSyncTime = endTime;
        log.info("BeginSyncAsset startTime:{},endTime:{},checkSyncTime:{},isInit:{},cexAssetHistoryTypeEnum:{}", startTime, endTime, checkSyncTime, ifInited, cexAssetHistoryTypeEnum.getName());
        if (ifInited) {
            cexAssetSyncHistory.syncAssetHistory(cexAssetConfig,userConfig, startTime, endTime, checkSyncTime);
        } else {
            Date queryStartTime = startTime;
            Date queryEndTime = null;
            while (queryStartTime.getTime() < endTime.getTime()) {
                try {
                    if (cexAssetHistoryTypeEnum.equals(CexAssetHistoryTypeEnum.PARENT_BALANCE) || cexAssetHistoryTypeEnum.equals(CexAssetHistoryTypeEnum.SUB_BALANCE)) {
                        cexAssetSyncHistory.syncAssetHistory(cexAssetConfig,userConfig, null, null, checkSyncTime);
                        break;
                    }
                    queryEndTime = DateUtils.addDays(queryStartTime, cexAssetSyncConfig.getMaxQueryDay());
                    if (queryEndTime.getTime() >= endTime.getTime()) {
                        queryEndTime = endTime;
                    }
                    cexAssetSyncHistory.syncAssetHistory(cexAssetConfig,userConfig, queryStartTime, queryEndTime, checkSyncTime);
                    Thread.sleep(cexAssetSyncConfig.getSleep());
                    log.info("SyncCexAssetEnd,startTime:{},endTime:{},cexAssetHistoryType:{},userId:{}", queryStartTime, queryEndTime, cexAssetHistoryTypeEnum.getName(), userConfig.getCexUserId());
                } catch (Exception e) {
                    log.error("SyncCexAssetError,cexAssetHistoryTypeEnum:{},startTime:{},endTime:{},cexAssetHistoryType:{},userId:{},e:{}", cexAssetHistoryTypeEnum.getName(), queryStartTime, queryEndTime, cexAssetHistoryTypeEnum.getName(), userConfig.getCexUserId(), e);
                    isSync = Boolean.FALSE;
                    alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_ASSET_SYNC_EXCEPTION, userConfig.getCexUserId(), CexTypeEnum.fromType(userConfig.getCexType()).getName(), cexAssetHistoryTypeEnum.getName(), e.getMessage());
                }
                queryStartTime = queryEndTime;
            }
        }
        log.info("SyncCexAssetFinishAndBeginModConfig,cexAssetHistoryTypeEnum:{},userId:{},isSync:{}", cexAssetHistoryTypeEnum.getName(), userConfig.getCexUserId(), isSync);
//        if (isSync) {
//            if (cexAssetConfig == null) {
//                cexAssetConfig = new CexAssetConfig();
//                cexAssetConfig.setAssetHistoryType(cexAssetHistoryTypeEnum.getValue());
//                cexAssetConfig.setCreateTime(new Date());
//                cexAssetConfig.setUpdateTime(new Date());
//                cexAssetConfig.setCexUserId(userConfig.getCexUserId());
//                cexAssetConfig.setCexType(userConfig.getCexType());
//                cexAssetConfig.setStatus(CexAssetSyncEnum.INIT_SYNC.getValue());
//                cexAssetConfig.setCheckSyncTime(checkSyncTime);
//                cexAssetconfigService.insert(cexAssetConfig);
//            } else {
//                cexAssetConfig.setStatus(CexAssetSyncEnum.NORMAL_SYNC.getValue());
//                cexAssetConfig.setCheckSyncTime(checkSyncTime);
//                cexAssetConfig.setUpdateTime(new Date());
//                cexAssetconfigService.update(cexAssetConfig);
//            }
//        }
        log.info("ModConfigFinish,cexAssetHistoryTypeEnum:{},userId:{},isSync:{}", cexAssetHistoryTypeEnum.getName(), userConfig.getCexUserId(), isSync);
    }

    public CommonRes<CommonCoinInfoListRes> querySupportCoinList(CommonReq commonReq) {
        CommonRes<CommonCoinInfoListRes> commonRes = cexApiService.querySupportCoinList(commonReq);
        List<CommonCoinInfoInnerRes> uniqueList = new ArrayList<>();
        if (commonRes.getSuccess()) {
            uniqueList = commonRes.getData().stream()
                    .collect(Collectors.toMap(
                            CommonCoinInfoInnerRes::getCoinName, // 按 coinName 去重
                            item -> item,
                            (existing, replacement) -> existing // 遇到重复时保留第一个
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
        }
        CommonCoinInfoListRes commonCoinInfoListRes = new CommonCoinInfoListRes();
        commonCoinInfoListRes.addAll(uniqueList);
        return CommonRes.getSucApiBaseRes(commonCoinInfoListRes);
    }

    public static void main(String[] args) {
        //2025-06-22 18:50:00
        Date endTime = new Date(TimeSliceCalcUtils.getTimeSlice(1750589460000L, -Long.valueOf(300)));
        System.out.println(endTime);
        endTime = DateUtils.addSeconds(endTime, 300);
        System.out.println(endTime);
    }

}




