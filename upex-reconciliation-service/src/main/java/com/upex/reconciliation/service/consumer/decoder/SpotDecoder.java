package com.upex.reconciliation.service.consumer.decoder;

import com.alibaba.otter.canal.protocol.FlatMessage;
import com.google.common.collect.Lists;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.model.domain.SpotBillChangeModel;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class SpotDecoder extends AbstractMessageDecoder {

    private Byte accountType = AccountTypeEnum.S_USDT_MIX_CONTRACT_BL.getCode();
    private List<Integer> feeTypeList = Lists.newArrayList(103, 104);

    @Override
    public List<CommonBillChangeData> doMessageDecode(List<Map<String, String>> dataList, FlatMessage flatMessage, Integer partition, Long offset, Byte accountType) {
        List<CommonBillChangeData> commonBillChangeDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Map<String, String> map : dataList) {
                SpotBillChangeModel spotBillChangeModel = new SpotBillChangeModel();
                if (map.get("bill_id") != null) {
                    spotBillChangeModel.setBillId(Long.valueOf(map.get("bill_id")));
                }
                if (map.get("biz_order_id") != null) {
                    spotBillChangeModel.setBizOrderId(Long.valueOf(map.get("biz_order_id")));
                }
                if (map.get("user_id") != null) {
                    spotBillChangeModel.setUserId(Long.valueOf(map.get("user_id")));
                }
                if (map.get("coin_id") != null) {
                    spotBillChangeModel.setCoinId(Integer.valueOf(map.get("coin_id")));
                }
                if (map.get("amount") != null) {
                    spotBillChangeModel.setAmount(new BigDecimal(map.get("amount")));
                }
                if (map.get("fees") != null) {
                    spotBillChangeModel.setFees(new BigDecimal(map.get("fees")));
                }
                if (map.get("group_type") != null) {
                    spotBillChangeModel.setGroupType(Byte.valueOf(map.get("group_type")));
                }
                if (map.get("biz_type") != null) {
                    spotBillChangeModel.setBizType(Integer.valueOf(map.get("biz_type")));
                }
                if (map.get("before_balance_change") != null) {
                    spotBillChangeModel.setBeforeBalanceChange(new BigDecimal(map.get("before_balance_change")));
                }
                if (map.get("balance_change") != null) {
                    spotBillChangeModel.setBalanceChange(new BigDecimal(map.get("balance_change")));
                }
                if (map.get("before_freeze_change") != null) {
                    spotBillChangeModel.setBeforeFreezeChange(new BigDecimal(map.get("before_freeze_change")));
                }
                if (map.get("freeze_change") != null) {
                    spotBillChangeModel.setFreezeChange(new BigDecimal(map.get("freeze_change")));
                }
                if (map.get("before_lock_change") != null) {
                    spotBillChangeModel.setBeforeLockChange(new BigDecimal(map.get("before_lock_change")));
                }
                if (map.get("lock_change") != null) {
                    spotBillChangeModel.setLockChange(new BigDecimal(map.get("lock_change")));
                }
                if (map.get("show_flag") != null) {
                    spotBillChangeModel.setShowFlag(Byte.valueOf(map.get("show_flag")));
                }
                spotBillChangeModel.setParams(map.get("params"));
                if (map.get("create_date") != null) {
                    spotBillChangeModel.setCreateDate(DateUtil.getMillisecondDate(map.get("create_date")));
                }
                if (map.get("update_date") != null) {
                    spotBillChangeModel.setUpdateDate(DateUtil.getMillisecondDate(map.get("update_date")));
                }
                if (map.get("deduct_coin_id") != null) {
                    spotBillChangeModel.setDeductCoinId(Integer.valueOf(map.get("deduct_coin_id")));
                }
                if (map.get("type") != null) {
                    spotBillChangeModel.setType(Integer.valueOf(map.get("type")));
                }
                if (map.get("business_type") != null) {
                    spotBillChangeModel.setBusinessType(map.get("business_type"));
                }

                CommonBillChangeData commonBillChangeData = spotBillChangeModel.getBillChangeData(accountType);
                // 填充 accountType，跟topic一一对应
                commonBillChangeData.setPartition(partition);
                commonBillChangeData.setAccountType(accountType);
                if (feeTypeList.contains(spotBillChangeModel.getBizType())) {
                    commonBillChangeData.setChangeFee(spotBillChangeModel.getFees());
                }
                // 填充offset
                commonBillChangeData.setOffset(offset);
                commonBillChangeData.setOrderId(map.get("biz_order_id"));
                commonBillChangeDataList.add(commonBillChangeData);
            }
        }
        return commonBillChangeDataList;
    }
}
