package com.upex.reconciliation.service.service.client.cex.utils;

import com.alibaba.fastjson.JSON;
import com.upex.base.system.dto.SysUserDto;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;

public class BgUserUtils {

    public static String getSysUserEmail(HttpServletRequest request) {
        String userName = "sys";
        SysUserDto sysUserDto = getSysUserDto(request);
        return null == sysUserDto ? userName : String.valueOf(sysUserDto.getEmail());
    }

    public static String getSysUserId(HttpServletRequest request) {
        String userId = "0";
        SysUserDto sysUserDto = getSysUserDto(request);
        return null == sysUserDto ? userId : String.valueOf(sysUserDto.getUserId());
    }

    public static SysUserDto getSysUserDto(HttpServletRequest request) {
        String sysUser = request.getHeader("SysUser");
        if (StringUtils.isNotEmpty(sysUser)) {
            return JSON.parseObject(sysUser, SysUserDto.class);
        }
        return null;
    }
}
