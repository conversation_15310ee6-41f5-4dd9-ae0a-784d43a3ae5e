package com.upex.reconciliation.service.service.impl;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.FinancialVirtualAccountSnapshot;
import com.upex.reconciliation.service.dao.mapper.FinancialVirtualAccountSnapshotMapper;
import com.upex.reconciliation.service.service.FinancialVirtualAccountSnapshotService;
import com.upex.reconciliation.service.utils.PartitionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FinancialVirtualAccountSnapshotServiceImpl implements FinancialVirtualAccountSnapshotService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource
    private FinancialVirtualAccountSnapshotMapper financialVirtualAccountSnapshotMapper;

    @Override
    public void batchSave(List<FinancialVirtualAccountSnapshot> financialVirtualAccountSnapshots) {
        Map<Long, List<FinancialVirtualAccountSnapshot>> groupKeyMap = financialVirtualAccountSnapshots.stream().collect(Collectors.groupingBy(financialVirtualAccountSnapshot -> PartitionUtils.getFinancialTotalPartition(financialVirtualAccountSnapshot.getUserId())));
        dbHelper.doDbOpInReconMasterTransaction(() -> {
            for (Map.Entry<Long, List<FinancialVirtualAccountSnapshot>> entry : groupKeyMap.entrySet()) {
                dbHelper.doDbOpInReconMaster(() -> financialVirtualAccountSnapshotMapper.batchInsert(entry.getValue(), entry.getKey().toString()));
            }
            return null;
        });
    }
}
