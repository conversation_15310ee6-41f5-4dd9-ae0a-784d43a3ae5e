package com.upex.reconciliation.service.business;


import cn.hutool.core.lang.Pair;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.model.ReconBillUserVo;
import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.dto.WithdrawCheckResultDTO;
import com.upex.ticker.facade.dto.PriceVo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * 通过用户检查对账结果
 *
 * <AUTHOR>
 */
public interface ReconCheckBillResultService {

    /**
     * 通过用户查询对账结果
     *
     * @param reconCheckResultsParams
     * @return
     */
    ReconBillUserVo selectCheckForTheResults(ReconCheckResultsParams reconCheckResultsParams);

    boolean checkProfitAccountByGray(ReconCheckResultsParams checkResultsParams, GlobalBillConfig globalBillConfig, Integer timePeriod);

    boolean checkProfitAccount(ReconCheckResultsParams checkResultsParams, GlobalBillConfig globalBillConfig, Integer timePeriod);

    Pair<Boolean, Map<Integer, BigDecimal>> checkProfitAccountForTime(ReconCheckResultsParams checkResultsParams, GlobalBillConfig globalBillConfig, Integer timePeriod);

    WithdrawCheckResultDTO checkWithdrawLimit(ReconCheckResultsParams reconCheckResultsParams, GlobalBillConfig globalBillConfig);

    boolean checkDelayAccountByOldBill(ReconCheckResultsParams reconCheckResultsParams, GlobalBillConfig globalBillConfig);

    void syncSendAlarmMessage(ReconCheckResultsParams reconCheckResultsParams, AlarmTemplateEnum alarmTemplateEnum, Object... args);
    void resultsCheck(ReconCheckResultsParams reconCheckResultsParams);

    void withdrawalBlockReasonCheck(ReconCheckResultsParams reconCheckResultsParams, ReconBillUserVo reconBillUserVo);

    void platformBlockReasonCheck(ReconCheckResultsParams reconCheckResultsParams, ReconBillUserVo reconBillUserVo);

    /**
     * 提币结果存盘
     * @param reconCheckResultsParams
     * @param reconBillUserVo
     */
    void saveWithdrawResult(ReconCheckResultsParams reconCheckResultsParams, ReconBillUserVo reconBillUserVo);

    void listUserIncrFlowsByAccountType(Long userId, Date billEndDate, Date requestDate, Map<Integer, BigDecimal> flowChangeSumMap, Map<Integer, BigDecimal> inOutFlowChangeSumMap, AccountTypeEnum accountTypeEnum);
    /**
     * 获取期初资产
     *
     * @param userId
     * @param snapshotTime
     * @param allCoinsMap
     * @param ratesMap
     * @param billConfig
     * @return
     */
    BigDecimal getUserBeginAssets(Long userId, Long snapshotTime, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> ratesMap, GlobalBillConfig billConfig);
}
