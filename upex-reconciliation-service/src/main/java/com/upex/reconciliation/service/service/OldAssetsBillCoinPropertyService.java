package com.upex.reconciliation.service.service;


import com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinProperty;
import com.upex.reconciliation.service.dao.mapper.OldAssetsBillCoinPropertyMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class OldAssetsBillCoinPropertyService {


    @Resource
    private BillDbHelper dbHelper;


    @Resource(name = "oldAssetsBillCoinPropertyMapper")
    private OldAssetsBillCoinPropertyMapper oldAssetsBillCoinPropertyMapper;


    public List<AssetsBillCoinProperty> selectAssetsByEndTime( Date endTime,
                                                        String billCheckType,
                                                        String billCheckParam){
        return dbHelper.doDbOpInBillMaster(() -> oldAssetsBillCoinPropertyMapper.selectAssetsByEndTime(endTime, billCheckType,billCheckParam));
    }

}
