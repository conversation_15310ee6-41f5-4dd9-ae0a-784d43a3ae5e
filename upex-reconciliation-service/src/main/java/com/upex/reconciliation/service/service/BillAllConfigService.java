package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.dao.entity.BillAllConfig;
import com.upex.reconciliation.service.dao.mapper.BillAllConfigMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class BillAllConfigService {



    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "billAllConfigMapper")
    private BillAllConfigMapper billAllConfigMapper;


    public BillAllConfig selectById(Long id) {
        return dbHelper.doDbOpInReconMaster(() -> billAllConfigMapper.selectById(id));
    }


    public List<BillAllConfig>  getCheckOkBillConfig(List<String> subSystemList){
        List<BillAllConfig> billAllConfigs=listAllBillConfigs();
        return billAllConfigs.stream()
                .filter(config -> CollectionUtils.isEmpty(subSystemList) || subSystemList.contains(config.getUniqKey()))
                .sorted(Comparator.comparing(BillAllConfig::getCheckOkTime)).collect(Collectors.toList());
    }
    public int updateByPrimaryKeySelective(BillAllConfig record) {
        return dbHelper.doDbOpInReconMaster(() -> billAllConfigMapper.updateByPrimaryKeySelective(record));
    }

    public BillAllConfig selectByTypeAndParam(Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billAllConfigMapper.selectByTypeAndParam(accountType,accountParam));
    }

    public int batchInsert(List<BillAllConfig> records) {
        return dbHelper.doDbOpInReconMaster(() -> billAllConfigMapper.batchInsert(records));
    }



    public List<BillAllConfig> listAllBillConfigs() {
        return dbHelper.doDbOpInReconMaster(() -> billAllConfigMapper.listAllBillConfigs());
    }


    public BillAllConfig getMinCheckOkBillConfig(List<String> subSystemList){
        List<BillAllConfig> billAllConfigs=listAllBillConfigs();
        return billAllConfigs.stream()
                .filter(config -> CollectionUtils.isEmpty(subSystemList) || subSystemList.contains(config.getUniqKey()))
                .sorted(Comparator.comparing(BillAllConfig::getCheckOkTime)).findFirst().orElse(null);
    }




    public BillAllConfig getMinCheckOkBillConfig(){
        List<BillAllConfig> billAllConfigs=listAllBillConfigs();
        return billAllConfigs.stream()
                .sorted(Comparator.comparing(BillAllConfig::getCheckOkTime)).findFirst().orElse(null);
    }



}
