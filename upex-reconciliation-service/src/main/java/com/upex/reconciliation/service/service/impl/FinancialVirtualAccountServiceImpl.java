package com.upex.reconciliation.service.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.FinancialVirtualAccount;
import com.upex.reconciliation.service.dao.mapper.FinancialVirtualAccountMapper;
import com.upex.reconciliation.service.service.FinancialVirtualAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FinancialVirtualAccountServiceImpl implements FinancialVirtualAccountService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource
    private FinancialVirtualAccountMapper financialVirtualAccountMapper;

    @Override
    public int batchSave(List<FinancialVirtualAccount> financialVirtualAccounts) {
        if (CollectionUtil.isEmpty(financialVirtualAccounts)) {
            return 0;
        }
        return dbHelper.doDbOpInReconMaster(() -> financialVirtualAccountMapper.batchInsert(financialVirtualAccounts));
    }

    @Override
    public void batchUpdateFinancialVirtualAccount(List<FinancialVirtualAccount> notCrossHourFinancialVirtualAccounts) {
        dbHelper.doDbOpInReconMaster(() -> {
            financialVirtualAccountMapper.batchUpdateFinancialVirtualAccount(notCrossHourFinancialVirtualAccounts);
            return null;
        });
    }

    @Override
    public List<FinancialVirtualAccount> listFinancialVirtualAccountByIds(List<FinancialVirtualAccount> financialVirtualAccounts) {
        if (CollectionUtils.isEmpty(financialVirtualAccounts)) {
            return new ArrayList<>();
        }
        List<Long> ids = financialVirtualAccounts.stream().map(FinancialVirtualAccount::getId).collect(Collectors.toList());
        List<FinancialVirtualAccount> dbFinancialVirtualAccounts = dbHelper.doDbOpInReconMaster(() -> financialVirtualAccountMapper.selectByIds(ids));
        if (CollectionUtils.isEmpty(dbFinancialVirtualAccounts)) {
            return new ArrayList<>();
        }
        return dbFinancialVirtualAccounts;
    }

    @Override
    public int batchSaveOrUpdate(List<FinancialVirtualAccount> records) {
        if (CollectionUtil.isEmpty(records)) {
            return 0;
        }
        return dbHelper.doDbOpInReconMaster(() -> financialVirtualAccountMapper.batchSaveOrUpdate(records));
    }
}
