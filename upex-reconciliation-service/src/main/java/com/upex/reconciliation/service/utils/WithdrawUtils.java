package com.upex.reconciliation.service.utils;

import com.upex.reconciliation.facade.enums.BusinessSourceEnum;
import com.upex.user.enums.permission.UserPermissionEnum;

import java.util.HashMap;
import java.util.Map;

import static com.upex.reconciliation.facade.enums.BusinessSourceEnum.*;
import static com.upex.user.enums.permission.UserPermissionEnum.*;

public class WithdrawUtils {

    private static Map<BusinessSourceEnum, UserPermissionEnum> busSourceToUserPermissionEnumMap = new HashMap<>();

    private static void initBusSourceToUserPermissionEnumMap() {
        // OTC提币来源拆分：p2p出售、p2p放币、p2p广告
        busSourceToUserPermissionEnumMap.put(P2PSALE, OTC_SELL);
        busSourceToUserPermissionEnumMap.put(P2PCOIN, OTC_CONFIRM);
        busSourceToUserPermissionEnumMap.put(P2PADVERTISE, OTC_SEND_ADV);
        // 法币提币来源拆分
        busSourceToUserPermissionEnumMap.put(FIAT, PAYMENT_LIMIT_TYPE_WITHDRAW_BANK_DEPOSIT);
        // 发卡出金
        busSourceToUserPermissionEnumMap.put(BITGET_CAR, FUND_ACCOUNT_WITHDRAW);
        // 第三方提币
        busSourceToUserPermissionEnumMap.put(COPPER, DELEGATED_WITHDRAW_LIST);
        busSourceToUserPermissionEnumMap.put(COBO, DELEGATED_WITHDRAW_LIST);
        busSourceToUserPermissionEnumMap.put(OASIS, DELEGATED_WITHDRAW_LIST);
    }

    public static UserPermissionEnum getUserPermissionEnum(String businessSource) {
        if (busSourceToUserPermissionEnumMap.isEmpty()) {
            initBusSourceToUserPermissionEnumMap();
        }
        BusinessSourceEnum businessSourceEnum = BusinessSourceEnum.toEnum(businessSource);
        return busSourceToUserPermissionEnumMap.getOrDefault(businessSourceEnum, WITHDRAW);
    }
}
