package com.upex.reconciliation.service.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fiat.fund.facade.model.enums.FundAccountBillOperateTypeEnum;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.upex.margin.constant.MarginBizTypeEnum;
import com.upex.mixcontract.common.literal.enums.FinanceBizTypeEnum;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.enums.BizInOutTypeEnum;
import com.upex.reconciliation.service.common.constants.financial.VirtualAccountBizTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillBizTypeConfig;
import com.upex.reconciliation.service.dao.mapper.BillBizTypeConfigMapper;
import com.upex.reconciliation.service.utils.RedisUtil;
import com.upex.spot.dto.enums.SpotBillBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.upex.reconciliation.facade.enums.AccountTypeEnum.*;

/**
 * 业务类型配置service
 */
@Slf4j
@Service
public class BillBizTypeConfigService {

    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billBizTypeConfigMapper")
    private BillBizTypeConfigMapper billBizTypeConfigMapper;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private ReconSystemAccountService reconSystemAccountService;

    /***业务线biztype混存配置***/
    private final LoadingCache<String, Map<String, BillBizTypeConfig>> bizTypeConfigMapCache =
            Caffeine.newBuilder()
                    .expireAfterWrite(3, TimeUnit.MINUTES)
                    .build(key -> {
                        Byte accountType = BillBizTypeConfig.getAccountTypeByKey(key);
                        String accountParam = BillBizTypeConfig.getAccountParamByKey(key);
                        List<BillBizTypeConfig> billBizTypeConfigs = selectByTypeAndParam(accountType, accountParam);
                        // 系统账户设置
                        billBizTypeConfigs.forEach(config -> setSystemUserIds(config));
                        Map<String, BillBizTypeConfig> billBizTypeConfigMap = billBizTypeConfigs.stream().collect(Collectors.toMap(BillBizTypeConfig::getBizType, item -> item));
                        log.info("BillBizTypeConfigService.load dataSize:{}", billBizTypeConfigs.size());
                        return billBizTypeConfigMap;
                    });

    /***业务线biztype混存配置***/
    private final LoadingCache<String, List<String>> bizInTypeListCache =
            Caffeine.newBuilder()
                    .expireAfterWrite(3, TimeUnit.MINUTES)
                    .build(key -> Optional.ofNullable(bizTypeConfigMapCache.get(key))
                                          .map(bizTypeConfigMap -> bizTypeConfigMap.values().stream()
                                                                                   .filter(item -> BizInOutTypeEnum.IN.getCode().equals(item.getBizInOut().intValue()))
                                                                                   .map(BillBizTypeConfig::getBizType)
                                                                                   .collect(Collectors.toList()))
                                          .orElse(List.of()));
    /***业务线biztype混存配置***/
    private final LoadingCache<String, List<String>> bizOutTypeListCache =
            Caffeine.newBuilder()
                    .expireAfterWrite(3, TimeUnit.MINUTES)
                    .build(key -> Optional.ofNullable(bizTypeConfigMapCache.get(key))
                                          .map(bizTypeConfigMap -> bizTypeConfigMap.values().stream()
                                                                                   .filter(item -> BizInOutTypeEnum.OUT.getCode().equals(item.getBizInOut().intValue()))
                                                                                   .map(BillBizTypeConfig::getBizType)
                                                                                   .collect(Collectors.toList()))
                                          .orElse(List.of()));

    public int batchInsert(List<BillBizTypeConfig> records) {
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }
        return dbHelper.doDbOpInReconMaster(() -> billBizTypeConfigMapper.batchInsert(records));
    }

    public int updateByPrimaryKeySelective(BillBizTypeConfig record) {
        return dbHelper.doDbOpInReconMaster(() -> billBizTypeConfigMapper.updateByPrimaryKeySelective(record));
    }

    public int deleteById(Long id) {
        return dbHelper.doDbOpInReconMaster(() -> billBizTypeConfigMapper.deleteById(id));
    }

    public BillBizTypeConfig selectById(Long id) {
        return dbHelper.doDbOpInReconMaster(() -> billBizTypeConfigMapper.selectById(id));
    }

    public List<BillBizTypeConfig> selectByTypeAndParam(Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billBizTypeConfigMapper.selectByTypeAndParam(accountType, accountParam));
    }

    public BillBizTypeConfig selectByTypeAndParamAndType(Byte accountType, String accountParam, String bizType) {
        return dbHelper.doDbOpInReconMaster(() -> billBizTypeConfigMapper.selectByTypeAndParamAndType(accountType, accountParam, bizType));
    }

    public List<BillBizTypeConfig> listAllBillConfigs() {
        return dbHelper.doDbOpInReconMaster(() -> billBizTypeConfigMapper.listAllBillConfigs());
    }

    /**
     * 获取业务线缓存配置
     *
     * @param accountType
     * @param accountParam
     * @return
     */
    public Map<String, BillBizTypeConfig> getBizTypeConfigByMap(Byte accountType, String accountParam) {
        return bizTypeConfigMapCache.get(BillBizTypeConfig.getUniqKey(accountType, accountParam));
    }

    /**
     * 获取业务线入出
     *
     * @param accountType
     * @param accountParam
     * @return
     */
    public List<String> getBizInTypeList(Byte accountType, String accountParam) {
        return bizInTypeListCache.get(BillBizTypeConfig.getUniqKey(accountType, accountParam));
    }

    /**
     * 获取业务线入出
     *
     * @param accountType
     * @param accountParam
     * @return
     */
    public List<String> getBizOutTypeList(Byte accountType, String accountParam) {
        return bizOutTypeListCache.get(BillBizTypeConfig.getUniqKey(accountType, accountParam));
    }

    /**
     * 修复billBizTypeConfig数据
     *
     * @param jobParam
     */
    public void repairBillBizTypeConfig(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        log.info("repairBillBizTypeConfig data:{}", jobParam);
        String action = jsonObject.getString("action");
        JSONArray delIds = jsonObject.getJSONArray("delIds");
        JSONArray insertList = jsonObject.getJSONArray("insertList");
        JSONArray updateList = jsonObject.getJSONArray("updateList");
        JSONObject batchUpdateData = jsonObject.getJSONObject("batchUpdateData");
        Date nowDate = new Date();
        if ("delete".equals(action)) {
            if (delIds == null || delIds.size() == 0) {
                return;
            }
            for (int i = 0; i < delIds.size(); i++) {
                deleteById(delIds.getLong(i));
            }
        } else if ("insert".equals(action)) {
            if (insertList == null || insertList.size() == 0) {
                return;
            }
            List<BillBizTypeConfig> billBizTypeConfigList = Convert.toList(BillBizTypeConfig.class, insertList);
            if (CollectionUtils.isNotEmpty(billBizTypeConfigList)) {
                for (BillBizTypeConfig billBizTypeConfig : billBizTypeConfigList) {
                    billBizTypeConfig.setCreateTime(nowDate);
                    billBizTypeConfig.setUpdateTime(nowDate);
                }
                batchInsert(billBizTypeConfigList);
            }
        } else if ("update".equals(action)) {
            if (updateList == null || updateList.size() == 0) {
                return;
            }
            List<BillBizTypeConfig> billBizTypeConfigList = Convert.toList(BillBizTypeConfig.class, updateList);
            if (CollectionUtils.isNotEmpty(billBizTypeConfigList)) {
                for (BillBizTypeConfig billBizTypeConfig : billBizTypeConfigList) {
                    updateByPrimaryKeySelective(billBizTypeConfig);
                }
            }
        } else if ("batchUpdate".equals(action)) {
            if (batchUpdateData == null) {
                return;
            }
            BillBizTypeConfig billBizTypeConfig = JSONObject.toJavaObject(batchUpdateData.getJSONObject("bizTypeConfig"), BillBizTypeConfig.class);
            JSONArray ids = batchUpdateData.getJSONArray("ids");
            for (int i = 0; i < ids.size(); i++) {
                billBizTypeConfig.setId(delIds.getLong(i));
                updateByPrimaryKeySelective(billBizTypeConfig);
            }
        } else if ("init".equals(action)) {
            initConfigs();
        }
    }

    private void initConfigs() {
        List<BillBizTypeConfig> billBizTypeConfigList = new ArrayList<>();
        // 现货
        Stream.of(SpotBillBizTypeEnum.values())
              .map(item -> BillBizTypeConfig.defaultBuild(AccountTypeEnum.SPOT, String.valueOf(item.getCode()), item.getDesc()))
              .forEach(billBizTypeConfigList::add);
        // 现货 DEMO
        Stream.of(SpotBillBizTypeEnum.values())
              .map(item -> BillBizTypeConfig.defaultBuild(DEMO_SPOT, String.valueOf(item.getCode()), item.getDesc()))
              .forEach(billBizTypeConfigList::add);
        // 杠杆 全仓
        Stream.of(MarginBizTypeEnum.values())
              .map(item -> BillBizTypeConfig.defaultBuild(LEVER_FULL, item.getBizCode().toString(), item.getBizName()))
              .forEach(billBizTypeConfigList::add);
        // 杠杆 全仓 DEMO
        Stream.of(MarginBizTypeEnum.values())
                .map(item -> BillBizTypeConfig.defaultBuild(DEMO_LEVER_FULL, item.getBizCode().toString(), item.getBizName()))
                .forEach(billBizTypeConfigList::add);
        // 杠杆 逐仓
        Stream.of(MarginBizTypeEnum.values())
              .map(item -> BillBizTypeConfig.defaultBuild(LEVER_ONE, item.getBizCode().toString(), item.getBizName()))
              .forEach(billBizTypeConfigList::add);
        // 杠杆 逐仓 DEMO
        Stream.of(MarginBizTypeEnum.values())
                .map(item -> BillBizTypeConfig.defaultBuild(DEMO_LEVER_ONE, item.getBizCode().toString(), item.getBizName()))
                .forEach(billBizTypeConfigList::add);
        // otc
        Stream.of(FundAccountBillOperateTypeEnum.values())
              .map(item -> BillBizTypeConfig.defaultBuild(OTC, String.valueOf(item.getOldType()), item.getDesc()))
              .forEach(billBizTypeConfigList::add);
        // 理财
        Stream.of(VirtualAccountBizTypeEnum.values())
              .map(item -> BillBizTypeConfig.defaultBuild(FINANCIAL, String.valueOf(item.getFinBizType()), item.getDesc()))
              .forEach(billBizTypeConfigList::add);
        // 合约
        contractTypes().forEach(contractType ->
                Stream.of(FinanceBizTypeEnum.values())
                      .map(item -> BillBizTypeConfig.defaultBuild(contractType, String.valueOf(item.getCode()), item.getDesc()))
                      .forEach(billBizTypeConfigList::add));
        var toInsertConfigs = resolveToInsert(billBizTypeConfigList);
        var count = batchInsert(toInsertConfigs);
        log.info("init biz type config success! init count:{}, details:{}", count, toInsertConfigs);
    }

    private List<BillBizTypeConfig> resolveToInsert(List<BillBizTypeConfig> billBizTypeConfigList) {
        Map<String, BillBizTypeConfig> dbBillBizTypeConfigMap = listAllBillConfigs().stream().collect(Collectors.toMap(BillBizTypeConfig::getUniqBizTypeKey, Function.identity()));
        return billBizTypeConfigList
                .stream()
                .collect(Collectors.toMap(BillBizTypeConfig::getUniqBizTypeKey, Function.identity(), (key1, key2) -> key2)).values()
                .stream()
                .filter(config -> dbBillBizTypeConfigMap.get(config.getUniqBizTypeKey()) == null)
                .collect(Collectors.toList());
    }

    /**
     * 修复billBizTypeConfig数据
     *
     * @param jobParam
     */
    public void repairKafkaIllegalMessageUserIds(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        String action = jsonObject.getString("action");
        JSONArray delIds = jsonObject.getJSONArray("delIds");
        JSONArray insertList = jsonObject.getJSONArray("insertList");
        JSONArray updateList = jsonObject.getJSONArray("updateList");
        Date nowDate = new Date();
        if ("delete".equals(action)) {
            if (delIds == null || delIds.size() == 0) {
                return;
            }
            for (int i = 0; i < delIds.size(); i++) {
                redisTemplate.opsForSet().remove(RedisUtil.KAFKA_ILLEGAL_MESSAGE_USER_IDS, delIds.getString(i));
            }
        } else if ("insert".equals(action)) {
            if (insertList == null || insertList.size() == 0) {
                return;
            }
            for (int i = 0; i < insertList.size(); i++) {
                redisTemplate.opsForSet().add(RedisUtil.KAFKA_ILLEGAL_MESSAGE_USER_IDS, insertList.getString(i));
            }
        } else if ("query".equals(action)) {
            Set<Object> userIds = redisTemplate.opsForSet().members(RedisUtil.KAFKA_ILLEGAL_MESSAGE_USER_IDS);
            log.info("repairKafkaIllegalMessageUserIds query data:{}", org.apache.commons.collections4.CollectionUtils.isNotEmpty(userIds) ? JSON.toJSONString(userIds) : "");
        }
    }

    /**
     * 系统账户类型获取系统用户
     *
     * @param config
     */
    private void setSystemUserIds(BillBizTypeConfig config) {
        if (Objects.equals(config.getSystemAccount(), 0) || StringUtils.isBlank(config.getSystemAccountType())) {
            return;
        }
        Set<Long> systemAccountSet = reconSystemAccountService.getSystemAccountIds(config.getSystemAccountType());
        if (CollectionUtil.isEmpty(systemAccountSet)) {
            return;
        }
        String userIds = systemAccountSet.stream().map(String::valueOf).collect(Collectors.joining(","));
        config.setUserIds(userIds);
    }
}
