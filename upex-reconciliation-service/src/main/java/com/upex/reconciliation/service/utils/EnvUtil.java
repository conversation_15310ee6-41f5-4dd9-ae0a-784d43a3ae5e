package com.upex.reconciliation.service.utils;

import com.upex.reconciliation.service.model.config.ApolloKafkaConsumerConfig;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import org.apache.commons.lang3.StringUtils;

/**
 * 环境处理工具类
 *
 * <AUTHOR>
 */
public class EnvUtil {
    private static final String SPRING_PROFILES_ACTIVE = "spring.profiles.active";
    private static final String SPRING_APPLICATION_NAME = "spring.application.name";
    private static final String SERVICE_INSTANCE_NAME = "service.instance.name";
    private static final String ENV_ONLINE = "online";
    private static final String ENV_LOCAL = "local";
    private static final String ENV_PRE = "pre";


    /**
     * 获取系统属性
     *
     * @param key
     * @return
     */
    public static String getSystemProperty(String key) {
        return System.getProperty(key);
    }

    /**
     * 获取实例名称
     *
     * @return
     */
    public static String getServiceInstanceName() {
        return getSystemProperty(SERVICE_INSTANCE_NAME);
    }

    /**
     * 是否是当前实例
     *
     * @param serviceInstanceName
     * @return
     */
    public static boolean isServiceInstance(String serviceInstanceName) {
        String instanceName = getServiceInstanceName();
        if (StringUtils.isEmpty(serviceInstanceName) || StringUtils.isEmpty(instanceName)) {
            return false;
        }
        return instanceName.startsWith(serviceInstanceName);
    }

    /**
     * 是否是生产环境
     *
     * @return
     */
    public static boolean isOnline() {
        return ENV_ONLINE.equalsIgnoreCase(getSystemProperty(SPRING_PROFILES_ACTIVE));
    }

    public static boolean isPre() {
        return ENV_PRE.equalsIgnoreCase(getSystemProperty(SPRING_PROFILES_ACTIVE));
    }

    /**
     * 业务线是否开启
     *
     * @return
     */
    public static boolean isRunningAccountType(Byte accountType) {
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        return !isOnline() || isServiceInstance(apolloBizConfig.getServiceInstanceName());
    }

    /**
     * 总账是否开启
     *
     * @return
     */
    public static boolean isRunningLedgerAccountType(String accountType, String accountParam) {
        AssetsCheckConfig assetsCheckConfig = ReconciliationApolloConfigUtils.getAssetsCheckConfig(accountType, accountParam);
        return !isOnline() || isServiceInstance(assetsCheckConfig.getServiceInstanceName());
    }

    /**
     * 是否运行kafkaconsumer
     *
     * @param consumerType
     * @return
     */
    public static boolean isRunningKafkaConsumer(String consumerType) {
        ApolloKafkaConsumerConfig apolloKafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig();
        KafkaConsumerConfig kafkaConsumerConfig = apolloKafkaConsumerConfig.getConsumerConfig().get(consumerType);
        if (kafkaConsumerConfig == null) {
            return false;
        }
        return !isOnline() || isServiceInstance(kafkaConsumerConfig.getServiceInstanceName());
    }

    /**
     * 是否本地调试
     *
     * @return
     */
    public static boolean isLocalDebugByAccountType(byte accountType) {
        String debugAccountType = getSystemProperty("debugAccountType");
        if (StringUtils.isEmpty(debugAccountType)) {
            return false;
        }
        String[] accountTypeArray = debugAccountType.split(",");
        for (int i = 0; i < accountTypeArray.length; i++) {
            if (accountType == Byte.valueOf(accountTypeArray[i])) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取开发kafka消费组
     *
     * @return
     */
    public static String getKafkaConsumerGroup(String def) {
        String systemProperty = getSystemProperty("kafkaConsumerGroup");
        return StringUtils.isNotEmpty(systemProperty) ? systemProperty : def;
    }

    /**
     * 是否本地环境
     *
     * @return
     */
    public static boolean isLocalEnv() {
        return ENV_LOCAL.equalsIgnoreCase(getSystemProperty(SPRING_PROFILES_ACTIVE));
    }

    /**
     * 是否本地调试
     *
     * @return
     */
    public static boolean isLocalDebugByKafkaConsumer(String kafkaConsumer) {
        String debugKafkaConsumer = getSystemProperty("debugKafkaConsumer");
        if (StringUtils.isEmpty(debugKafkaConsumer)) {
            return false;
        }
        String[] kafkaConsumerArray = debugKafkaConsumer.split(",");
        for (int i = 0; i < kafkaConsumerArray.length; i++) {
            if (kafkaConsumer.equals(kafkaConsumerArray[i])) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否本地调试
     *
     * @return
     */
    public static boolean isLocalDebugByAssetsCheckType(String assetsCheckType) {
        String debugAssetsCheckType = getSystemProperty("debugAssetsCheckType");
        if (StringUtils.isEmpty(debugAssetsCheckType)) {
            return false;
        }
        String[] assetsCheckTypeArray = debugAssetsCheckType.split(",");
        for (int i = 0; i < assetsCheckTypeArray.length; i++) {
            if (assetsCheckType.equals(assetsCheckTypeArray[i])) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否本地调试
     */
    public static boolean isLocalDebugCapitalOrder() {
        String debugCapitalOrder = getSystemProperty("debugCapitalOrder");
        if (StringUtils.isEmpty(debugCapitalOrder)) {
            return false;
        }
        return Boolean.parseBoolean(debugCapitalOrder);
    }

    /**
     * 是否运行实例
     *
     * @param instanceName
     * @return
     */
    public static boolean isRunningServiceInstanceName(String instanceName) {
        return !isOnline() || isServiceInstance(instanceName);
    }


}
