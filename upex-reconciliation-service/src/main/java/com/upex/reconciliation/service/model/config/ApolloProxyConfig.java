package com.upex.reconciliation.service.model.config;

import lombok.Data;

@Data
public class ApolloProxyConfig {

    private boolean openProxy = false;
    private String proxyIp;
    private Integer proxyPort;
    private int internalProxyPort = 80;
    private String proxyScheme;
    private int connectTimeout = 10000;
    private int connectionRequestTimeout = 3000;
    private int socketTimeout = 6000;
    private boolean printResponse;
    private boolean useTLS=false ;
}
