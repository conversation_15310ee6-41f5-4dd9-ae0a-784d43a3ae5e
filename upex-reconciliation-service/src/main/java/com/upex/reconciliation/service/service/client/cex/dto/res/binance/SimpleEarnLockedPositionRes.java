package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SimpleEarnLockedPositionRes implements IBinanceApiBaseRes{
    /**
     * {
     * "rows":[
     *   {
     *     "positionId":123123,  //定期订单ID
     *     "parentPositionId":123122, //父订单ID
     *     "projectId": "Axs*90",  //定期项目ID
     *     "asset":"AXS",          //锁仓资产
     *     "amount":"122.09202928",  //锁仓数量
     *     "purchaseTime": "1646182276000",  //申购时间
     *     "duration": "60",    //项目锁仓时间
     *     "accrualDays": "4",  //计息天数
     *     "rewardAsset":"AXS", //收益币种
     *     "APY":"0.2032",
     *     "rewardAmt": "5.17181528",  //收益数量
     *     "extraRewardAsset":"BNB",   //额外收益资产
     *     "extraRewardAPR":"0.0203",  //额外收益的年化收益率
     *     "estExtraRewardAmt": "5.17181528", //额外收益数量，在项目到期时发放
     *     "boostRewardAsset": "AXS",
     *     "boostApr": "0.0121",
     *     "totalBoostRewardAmt": "3.98201829",
     *     "nextPay": "1.29295383",   //下一次发放数量
     *     "nextPayDate": "1646697600000", //下一次发放日期
     *     "payPeriod": "1",  //发放周期
     *     "redeemAmountEarly": "2802.24068892", //提前赎回数量
     *     "rewardsEndDate": "1651449600000",
     *     "deliverDate": "1651536000000",       //赎回到账时间
     *     "redeemPeriod": "1",           //赎回周期
     *     "redeemingAmt":"232.2323",     //赎回中的数量
     *     “redeemTo”:"FLEXIBLE", // 赎回至活期产品或者现货钱包
     *     "partialAmtDeliverDate":"1651536000000", //部分赎回到账时间
     *    "canRedeemEarly": true,        //当为true时可以操作提前赎回
     *     "canFastRedemption": true,
     *     "autoSubscribe"：true,
     *     "type":"AUTO",   //订单类型为普通或续期类型
     *     "status": "HOLDING",
     *      "canReStake": true
     *   }
     * ]
     * "total": 1
     * }
     */
    private Integer total;
    private List<SimpleEarnLockedPositionInnerRes> rows;


    @Data
    public class SimpleEarnLockedPositionInnerRes {
        private Long positionId;
        private Long parentPositionId;
        private String projectId;
        private String asset;
        private BigDecimal amount;
        private Long purchaseTime;
        private String duration;
        private String accrualDays;
        private String rewardAsset;
        private BigDecimal APY;
        private BigDecimal rewardAmt;
        private String extraRewardAsset;
        private BigDecimal extraRewardAPR;
        private BigDecimal estExtraRewardAmt;
        private String boostRewardAsset;
        private BigDecimal boostApr;
        private BigDecimal totalBoostRewardAmt;
        private BigDecimal nextPay;
        private Long nextPayDate;
        private String payPeriod;
        private BigDecimal redeemAmountEarly;
        private Long rewardsEndDate;
        private Long deliverDate;
        private String redeemPeriod;
        private BigDecimal redeemingAmt;
        private String redeemTo;
        private Boolean canRedeemEarly;
        private Boolean canFastRedemption;
        private Boolean autoSubscribe;
        private String type;
        private String status;
        private Boolean canReStake;
    }
}
