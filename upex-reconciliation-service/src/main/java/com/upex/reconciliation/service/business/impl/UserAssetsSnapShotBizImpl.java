package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.upex.commons.support.exception.ApiException;
import com.upex.financial.dto.params.common.rsp.FinancialAssetsSnapshotRsp;
import com.upex.reconciliation.facade.dto.results.*;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.enums.ApolloKeyEnum;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;
import com.upex.reconciliation.facade.params.ReconUserAssetsParam;
import com.upex.reconciliation.facade.params.ReconUserAssetsSnapShotParams;
import com.upex.reconciliation.facade.params.ReconUserTypeChangeParams;
import com.upex.reconciliation.service.business.ReconUserAssetsSnapShotService;
import com.upex.reconciliation.service.business.UserAssetsSnapShotBiz;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.common.chain.AbstractCheckHandler;
import com.upex.reconciliation.service.common.chain.CheckBusinessForceValidataHandler;
import com.upex.reconciliation.service.common.chain.CheckEmptyForceValidataHandler;
import com.upex.reconciliation.service.common.chain.CheckSoftnessFinishValidataHandler;
import com.upex.reconciliation.service.common.exception.SoftnessValidataException;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.dao.entity.BillUser;
import com.upex.reconciliation.service.model.config.BillBaseAssetSnapshotApolloConfig;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.service.BillCoinUserPropertyService;
import com.upex.reconciliation.service.service.BillUserService;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.utils.task.TaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2021/10/27 下午4:49
 * @Description
 */
@Service
@Slf4j
public class UserAssetsSnapShotBizImpl implements UserAssetsSnapShotBiz {

    @Resource
    private ReconUserAssetsSnapShotService reconUserAssetsSnapShotService;

    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;

    @Resource
    private CommonService commonService;
    @Autowired
    private AbstractCheckHandler.Builder builder;

    @Resource
    private BillUserService billUserService;

    @Resource(name = "batchAssetSnapshotTaskManager")
    private TaskManager batchAssetSnapshotTaskManager;


    @Override
    public List<ReconUserOutInBalanceVo> selectOutInBalance(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        checkOutInBalanceParams(reconUserTypeChangeParams);
        checkUserExist(reconUserTypeChangeParams.getUserId());
        checkSysUser(reconUserTypeChangeParams.getUserId());
        checkTime(reconUserTypeChangeParams);
        // 先查A点快照、再查B点快照，基于两个时间点快照做差值
        List<ReconUserOutInBalanceVo> totalBalanceVoList = reconUserAssetsSnapShotService.selectOutInBalance(reconUserTypeChangeParams);
        return totalBalanceVoList;
    }

    @Override
    public ReconBillMessagePage<ReconUserOutInBalanceDetailsVo> selectDetails(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        checkDetailsParams(reconUserTypeChangeParams);
        checkUserExist(reconUserTypeChangeParams.getUserId());
        checkSysUser(reconUserTypeChangeParams.getUserId());
        checkTime(reconUserTypeChangeParams);
        // 先查A点快照、再查B点快照，基于两个时间点快照做差值
        ReconBillMessagePage<ReconUserOutInBalanceDetailsVo> reconBillMessagePage = reconUserAssetsSnapShotService.selectDetails(reconUserTypeChangeParams);
        return reconBillMessagePage;
    }

    @Override
    public List<ReconUserOutInBalanceDetailsVo> selectOtherIncome(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        checkOtherParams(reconUserTypeChangeParams);
        checkUserExist(reconUserTypeChangeParams.getUserId());
        checkSysUser(reconUserTypeChangeParams.getUserId());
        checkTime(reconUserTypeChangeParams);
        // 先查A点快照、再查B点快照，基于两个时间点快照做差值
        List<ReconUserOutInBalanceDetailsVo> list = reconUserAssetsSnapShotService.selectOtherIncome(reconUserTypeChangeParams);
        return list;
    }

    @Override
    public String selectFinalSnapShotTime(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        String finalSnapShotTime = reconUserAssetsSnapShotService.selectFinalSnapShotTime(reconUserTypeChangeParams);
        return finalSnapShotTime;
    }

    @Override
    public ReconBillMessagePage<ReconTotalBalanceVo> selectCurrentSysAssetsSnapShotByTime(ReconUserAssetsSnapShotParams reconUserAssetsSnapShotParams) {
        checkCurrentAssetsParams(reconUserAssetsSnapShotParams);
        checkUserExist(reconUserAssetsSnapShotParams.getUserId());
        checkSysUser(reconUserAssetsSnapShotParams.getUserId());
        ReconBillMessagePage<ReconTotalBalanceVo> totalBalanceVoList = reconUserAssetsSnapShotService.selectCurrentSysAssetsSnapShotByTime(reconUserAssetsSnapShotParams);
        return totalBalanceVoList;
    }



    @Override
    public List<ReconUserAssetsResult> getUserAssetsByCoinAndTime(ReconUserAssetsParam reconUserAssetsParam) {
        try {
            Map<Integer, List<ReconTotalAssetsVO>> symbolAssetsMap = getSymbolAssetsMap(reconUserAssetsParam);
            log.info("UserAssetsSnapShotBizImpl getSymbolAssetsMap symbolAssetsMap:{}", JSONObject.toJSONString(symbolAssetsMap));

            List<ReconUserAssetsResult> reconUserAssetsResults = symbolAssetsMap.entrySet().stream()
                    .map(asset -> buildUserAssetsResult(reconUserAssetsParam.getUserId(), asset.getKey(), asset.getValue()))
                    .collect(Collectors.toList());
            log.info("UserAssetsSnapShotBizImpl getSymbolAssetsMap userAssetsResults:{}", JSONObject.toJSONString(reconUserAssetsResults));
            return reconUserAssetsResults;
        } catch (Exception e) {
            log.error("batchUserAssetsBySnapShotTime execute error", e);
            log.error("batchUserAssetsBySnapShotTime execute error: userIds:{}, snapShotTime={}", reconUserAssetsParam.getUserId(), reconUserAssetsParam.getSnapshotTime());
            throw new ApiException(BillExceptionEnum.SYSTEM_ERROR);
        }
    }


    @Override
    public List<ReconContractTradingVo> selectContract(ReconUserTypeChangeParams userTypeChangeParams) {
        checkContractParams(userTypeChangeParams);
        checkUserExist(userTypeChangeParams.getUserId());
        checkSysUser(userTypeChangeParams.getUserId());
        checkTime(userTypeChangeParams);
        List<ReconContractTradingVo> list = reconUserAssetsSnapShotService.selectContract(userTypeChangeParams);
        return list;
    }


    @Override
    public ReconUserAssetsVo selectUserAssets(ReconUserAssetsSnapShotParams reconUserAssetsSnapShotParams) {
        checkUserAssetsParams(reconUserAssetsSnapShotParams);
        checkUserExist(reconUserAssetsSnapShotParams.getUserId());
        checkUserExist(reconUserAssetsSnapShotParams.getUserId());
        checkSysUser(reconUserAssetsSnapShotParams.getUserId());
        ReconUserAssetsVo list = reconUserAssetsSnapShotService.selectUserAssets(reconUserAssetsSnapShotParams);
        return list;
    }

    @Override
    public List<ReconBbTradingVo> selectBbTrading(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        checkOutInBalanceParams(reconUserTypeChangeParams);
        checkUserExist(reconUserTypeChangeParams.getUserId());
        checkSysUser(reconUserTypeChangeParams.getUserId());
        checkTime(reconUserTypeChangeParams);
        List<ReconBbTradingVo> list = reconUserAssetsSnapShotService.selectBbTrading(reconUserTypeChangeParams);
        return list;
    }


    @Override
    public ReconUserTradingVo selectUserProfitLossTotal(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        checkOutInBalanceParams(reconUserTypeChangeParams);
        checkUserExist(reconUserTypeChangeParams.getUserId());
        checkSysUser(reconUserTypeChangeParams.getUserId());
        checkTime(reconUserTypeChangeParams);
        ReconUserTradingVo reconUserTradingVo = reconUserAssetsSnapShotService.selectUserProfitLossTotal(reconUserTypeChangeParams);
        return reconUserTradingVo;
    }


    @Override
    public ReconTotalAssetsDetailVo listUserAssetsBySnapShotTime(Long userId, Long snapShotTime, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates) throws Exception {
        AbstractCheckHandler build = builder.addHandler(new CheckEmptyForceValidataHandler())
                .addHandler(new CheckBusinessForceValidataHandler(commonService))
                .addHandler(new CheckSoftnessFinishValidataHandler(billCoinUserPropertyService))
                .build();
        build.doHandler(userId, snapShotTime);
        return reconUserAssetsSnapShotService.listUserAssetsBySnapShotTime(userId, snapShotTime, allCoinsMap, rates);
    }

    @Override
    public ReconTotalAssetsDetailVo listUserAssetsBySnapShotTime(Long userId, Long snapShotTime, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates, List<String> subSystemList, Set<Integer> hasPositionCoinList) throws Exception {
        AbstractCheckHandler build = builder.addHandler(new CheckEmptyForceValidataHandler())
                .addHandler(new CheckBusinessForceValidataHandler(commonService))
                .addHandler(new CheckSoftnessFinishValidataHandler(billCoinUserPropertyService))
                .build();
        build.doHandler(userId, snapShotTime);
        return reconUserAssetsSnapShotService.listUserAssetsBySnapShotTime(userId, snapShotTime, allCoinsMap, rates, subSystemList, hasPositionCoinList, Boolean.TRUE);
    }


    @Override
    public ReconTotalAssetsDetailVo listUserAssetsBySnapShotTime(Long userId, Long snapShotTime, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates, List<String> subSystemList, Set<Integer> hasPositionCoinList, Boolean isMaster) throws Exception {
        AbstractCheckHandler build = builder.addHandler(new CheckEmptyForceValidataHandler())
                .addHandler(new CheckBusinessForceValidataHandler(commonService))
                .addHandler(new CheckSoftnessFinishValidataHandler(billCoinUserPropertyService))
                .build();
        build.doHandler(userId, snapShotTime);
        return reconUserAssetsSnapShotService.listUserAssetsBySnapShotTime(userId, snapShotTime, allCoinsMap, rates, subSystemList, hasPositionCoinList, isMaster);
    }


    @Override
    public Map<Long, ReconTotalAssetsDetailVo> batchUserAssetsBySnapShotTime(List<Long> userIds, Long snapShotTime, Boolean isMaster) {
        Map<Long, ReconTotalAssetsDetailVo> resultMap = new ConcurrentHashMap<>(userIds.size());
        // 增加缓存处理   定时清空缓存
        Map<Integer, String> allCoinsMap = commonService.getAllCoinsMapCache(snapShotTime);
        // 汇率改为获取指定时间的汇率
        Map<Integer, PriceVo> rates = commonService.getCoinIdTradePriceMap(snapShotTime);

        batchAssetSnapshotTaskManager.forEachSubmitBatchAndWait(userIds, userId -> {
            setResultMap(snapShotTime, resultMap, userId, allCoinsMap, rates, isMaster);
        });
        return resultMap;
    }

    private void setResultMap(Long snapShotTime, Map<Long, ReconTotalAssetsDetailVo> resultMap, Long userId, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates, Boolean isMaster) {
        try {
            List<String> subSystemList = ReconciliationApolloConfigUtils.getApolloObjectConfig(ApolloKeyEnum.BILL_BASE_ASSET_SNAPSHOT_APOLLO_CONFIG, BillBaseAssetSnapshotApolloConfig.class).getSubSystemList();
            ReconTotalAssetsDetailVo reconTotalAssetsDetailVo = listUserAssetsBySnapShotTime(userId, snapShotTime, allCoinsMap, rates, subSystemList, null, isMaster);
            resultMap.put(userId, reconTotalAssetsDetailVo);
        } catch (SoftnessValidataException e) {
            resultMap.put(userId, ReconTotalAssetsDetailVo.init());
        } catch (Exception e) {
            log.error("batchUserAssetsBySnapShotTime execute error", e);
            log.error("batchUserAssetsBySnapShotTime execute error: userId:{}, snapShotTime={}", userId, snapShotTime);
            throw new RuntimeException(e);
        }
    }


    private Map<Integer, List<ReconTotalAssetsVO>> getSymbolAssetsMap(ReconUserAssetsParam reconUserAssetsParam) throws Exception {
        // 获取用户对账的所有资产
        Map<Integer, String> allCoinsMap = commonService.getAllCoinsMap();
        Map<Integer, PriceVo> rates = commonService.getRatesToUSDTCoinIdMap(AccountTypeEnum.SPOT);
        ReconTotalAssetsDetailVo reconTotalAssetsDetailVo = listUserAssetsBySnapShotTime(reconUserAssetsParam.getUserId(), reconUserAssetsParam.getSnapshotTime(), allCoinsMap, rates);
        List<ReconTotalAssetsVO> totalAssetsDetail = reconTotalAssetsDetailVo.getTotalAssetsDetail();
        log.info("UserAssetsSnapShotBizImpl getSymbolAssetsMap totalAssetsDetail:{}", JSONObject.toJSONString(totalAssetsDetail));

        List<Integer> coinIds = reconUserAssetsParam.getCoinIds();
        boolean empty = CollectionUtils.isEmpty(coinIds);

        return totalAssetsDetail.stream()
                .filter(asset -> empty || coinIds.contains(asset.getCoinId()))
                .collect(Collectors.groupingBy(ReconTotalAssetsVO::getCoinId));
    }

    /**
     * 构建资产汇总类
     *
     * @param asset                资产
     * @param ratesToUSDTCoinIdMap
     * @return {@link ReconTotalAssetsVO }
     * <AUTHOR>
     * @date 2022/12/17 13:58
     */
    private ReconTotalAssetsVO getTotalAssetsVO(FinancialAssetsSnapshotRsp asset, Map<Integer, PriceVo> ratesToUSDTCoinIdMap) {
        ReconTotalAssetsVO reconTotalAssetsVO = new ReconTotalAssetsVO();
        reconTotalAssetsVO.setAssetsType((int) AccountTypeEnum.FINANCIAL.getCode());
        reconTotalAssetsVO.setCoinId(asset.getCoinId());
        reconTotalAssetsVO.setCoinName(asset.getCoinName());
        reconTotalAssetsVO.setCount(asset.getTotalAmount());

        BigDecimal rate = BigDecimal.ZERO;
        PriceVo priceVo = ratesToUSDTCoinIdMap.get(asset.getCoinId());
        if (priceVo != null) {
            rate = priceVo.getPrice();
        }
        reconTotalAssetsVO.setURate(rate);
        reconTotalAssetsVO.setBalance(asset.getTotalAmount().multiply(rate));
        return reconTotalAssetsVO;
    }

    private ReconUserAssetsResult buildUserAssetsResult(Long userId, Integer coinId, List<ReconTotalAssetsVO> reconTotalAssetsVOS) {
        BigDecimal countSum = reconTotalAssetsVOS.stream().map(ReconTotalAssetsVO::getCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        return new ReconUserAssetsResult(userId, coinId, countSum);
    }


    private void checkCurrentAssetsParams(ReconUserAssetsSnapShotParams reconUserAssetsSnapShotParams) {
        if (Objects.isNull(reconUserAssetsSnapShotParams)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }
        if (Objects.isNull(reconUserAssetsSnapShotParams.getUserId())) {
            throw new ApiException(BillExceptionEnum.USER_NOT_EXISTS);
        }
        if (Objects.isNull(reconUserAssetsSnapShotParams.getPageSize())) {
            throw new ApiException(BillExceptionEnum.PAGESIZE_IS_NULL);
        }
        if (Objects.isNull(reconUserAssetsSnapShotParams.getAccountType())) {
            throw new ApiException(BillExceptionEnum.ACCOUNT_PARAMS_IS_NULL);
        }
    }

    private void checkUserExist(Long userId) {
        if (userId == null) {
            throw new ApiException(BillExceptionEnum.USER_NOT_EXISTS);
        }
        List<BillUser> billUsers = billUserService.selectByIds(List.of(userId));
        if (CollectionUtils.isEmpty(billUsers)) {
            throw new ApiException(BillExceptionEnum.USER_NOT_EXISTS);
        }
    }

    private void checkSysUser(Long userId) {
        if (commonService.isSysUser(userId)) {
            throw new ApiException(BillExceptionEnum.SYSTEM_USER_ILLEGAL_PARAMS);
        }
    }

    private void checkOutInBalanceParams(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        if (Objects.isNull(reconUserTypeChangeParams)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }
        if (Objects.isNull(reconUserTypeChangeParams.getUserId())) {
            throw new ApiException(BillExceptionEnum.USER_NOT_EXISTS);
        }
    }


    private void checkDetailsParams(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        if (Objects.isNull(reconUserTypeChangeParams)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }
        if (Objects.isNull(reconUserTypeChangeParams.getUserId())) {
            throw new ApiException(BillExceptionEnum.USER_NOT_EXISTS);
        }
        if (Objects.isNull(reconUserTypeChangeParams.getCoinId())) {
            throw new ApiException(BillExceptionEnum.COIN_NOT_EXISTS);
        }
        if (Objects.isNull(reconUserTypeChangeParams.getPageSize())) {
            throw new ApiException(BillExceptionEnum.PAGESIZE_IS_NULL);
        }

    }


    private void checkOtherParams(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        if (Objects.isNull(reconUserTypeChangeParams)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }
        if (Objects.isNull(reconUserTypeChangeParams.getUserId())) {
            throw new ApiException(BillExceptionEnum.USER_NOT_EXISTS);
        }
        if (Objects.isNull(reconUserTypeChangeParams.getCoinId())) {
            throw new ApiException(BillExceptionEnum.COIN_NOT_EXISTS);
        }
    }


    private void checkUserAssetsParams(ReconUserAssetsSnapShotParams reconUserAssetsSnapShotParams) {
        if (Objects.isNull(reconUserAssetsSnapShotParams)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }
        if (Objects.isNull(reconUserAssetsSnapShotParams.getUserId())) {
            throw new ApiException(BillExceptionEnum.USER_NOT_EXISTS);
        }
    }

    private void checkContractParams(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        if (Objects.isNull(reconUserTypeChangeParams)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }
        if (Objects.isNull(reconUserTypeChangeParams.getUserId())) {
            throw new ApiException(BillExceptionEnum.USER_NOT_EXISTS);
        }
        if (Objects.isNull(reconUserTypeChangeParams.getType())) {
            throw new ApiException(BillExceptionEnum.TYPE_IS_NULL);
        }

    }

    private void checkSelectUsers(ReconUserAssetsSnapShotParams reconUserAssetsSnapShotParams) {
        if (reconUserAssetsSnapShotParams.getBalancePointed() == null) {
            throw new ApiException(BillExceptionEnum.ACCOUNT_PARAMS_IS_NULL);
        }
        if (reconUserAssetsSnapShotParams.getRequestTime() == null) {
            throw new ApiException(BillExceptionEnum.ACCOUNT_PARAMS_IS_NULL);
        }
        if (CollectionUtils.isEmpty(reconUserAssetsSnapShotParams.getSystemList())) {
            throw new ApiException(BillExceptionEnum.ACCOUNT_PARAMS_IS_NULL);
        }
        if (CollectionUtils.isEmpty(reconUserAssetsSnapShotParams.getUserIds())) {
            throw new ApiException(BillExceptionEnum.ACCOUNT_PARAMS_IS_NULL);
        }
    }

    private void checkTime(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        if (Objects.isNull(reconUserTypeChangeParams)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }
        // 设置时间范围，新逻辑分表了，不能无限制的查询，如果不传end时间，默认是当前
        if (Objects.isNull(reconUserTypeChangeParams.getEndTime())) {
            Date now = new Date();
            reconUserTypeChangeParams.setEndTime(now.getTime());
        }
        // 不传begin时间，默认是end往前推7天
        if (Objects.isNull(reconUserTypeChangeParams.getBeginTime())) {
            reconUserTypeChangeParams.setBeginTime(reconUserTypeChangeParams.getEndTime() - BillConstants.SEVEN * BillConstants.ONE_DAY_MIL_SEC);
        }
        // 校验时间范围，开始时间，结束时间，都必须是有新bill_coin_type_user表后的时间，否则报错
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        if (reconUserTypeChangeParams.getBeginTime() < globalBillConfig.getBillCoinTypeUserTableFirstUseTime() || reconUserTypeChangeParams.getEndTime() < globalBillConfig.getBillCoinTypeUserTableFirstUseTime()) {
            throw new ApiException(BillExceptionEnum.TIME_ERROR);
        }
        if (reconUserTypeChangeParams.getBeginTime() > reconUserTypeChangeParams.getEndTime()) {
            throw new ApiException(BillExceptionEnum.TIME_ERROR);
        }

    }


}
