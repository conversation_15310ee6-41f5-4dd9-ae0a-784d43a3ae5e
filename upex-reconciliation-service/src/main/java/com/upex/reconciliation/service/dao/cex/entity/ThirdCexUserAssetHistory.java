package com.upex.reconciliation.service.dao.cex.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ThirdCexUserAssetHistory {
    private Long id;
    private String cexUserId;
    private Integer cexType;
    private Integer thirdAssetType;
    private String coinName;
    private BigDecimal totalBalance;
    private BigDecimal avaiableBalance;
    private BigDecimal borrowedBalance;
    private BigDecimal marginBalance;
    private Date changeTime;
    private Date checkSyncTime;
    private Date createTime;
    private Date updateTime;
    private Long version;

    // Getters and Setters
}
