package com.upex.reconciliation.service.business.profitabnormal;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.upex.data.feature.dto.params.FeatureKeyParam;
import com.upex.data.feature.dto.params.FeatureUpdateReq;
import com.upex.data.feature.dto.results.FeatureStoreResult;
import com.upex.data.feature.facade.inner.FeatureStoreFacade;
import com.upex.reconciliation.service.common.constants.enums.BillRedisKeyEnum;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.dto.AccountProfitDTO;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ProfitAbnormalService {
    public static final String featureCode = "reconciliation_asset_abnormal";
    public static final String featureValue = "1";
    @Resource
    FeatureStoreFacade featureStoreFacade;
    @Resource(name = "redisTemplate")
    protected RedisTemplate<String, Object> redisTemplate;

    public void sendAbnormalProfitRisk(Long userId) {
        if (userId == null) {
            return;
        }
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        if (!globalBillConfig.isSendProfitAbnormalToRisk()) {
            return;
        }
        try {
            FeatureUpdateReq featureUpdateReq = new FeatureUpdateReq();
            featureUpdateReq.setFeatureCode(featureCode);
            featureUpdateReq.setFeatureValue(featureValue);
            featureUpdateReq.setFeatureKeyValues(Set.of(userId.toString()));
            FeatureStoreResult featureStoreResult = featureStoreFacade.update(featureUpdateReq);
            if (Objects.isNull(featureStoreResult) || Boolean.FALSE.equals(featureStoreResult.isSuccess())) {
                log.error("ProfitAbnormalAddFeatureFail,errmsg:{},userId:{}", JSONObject.toJSONString(featureStoreResult), userId);
            }
            String redisKey = String.format(BillRedisKeyEnum.RECON_WITHDRAWAL_PROFIT_RISK_RESULT.getKey(), userId);
            redisTemplate.opsForValue().set(redisKey, JSON.toJSONString(Pair.of(userId, new Date().getTime())), globalBillConfig.getTimePeriodProfitRedisTime(), TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("ProfitAbnormalAddFeatureError,errmsg:{},userId:{}", e, userId);
        }
    }

    /**
     * 风控指标推送
     *
     * @param userId
     * @param userAccountProfitMap
     */
    public boolean sendAbnormalProfitRiskV2(Long requestTime, Long userId, Map<Long, AccountProfitDTO> userAccountProfitMap) {
        if (userId == null) {
            return false;
        }
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        if (!globalBillConfig.isSendProfitAbnormalToRisk()) {
            return false;
        }
        try {
            // 组装业务线盈利数据 accountType——》金额
            Map<String, BigDecimal> parentUserIdProfitMap = new HashMap<>();
            Map<String, BigDecimal> subUserIdProfitMap = new HashMap<>();
            for (Map.Entry<Long, AccountProfitDTO> entry : userAccountProfitMap.entrySet()) {
                Long profitUserId = entry.getKey();
                AccountProfitDTO accountProfitDTO = entry.getValue();
                if (userId.equals(profitUserId)) {
                    accountProfitDTO.getAccountProfitMap().forEach((accountType, v) -> {
                        if (v.getProfitAmount().compareTo(BigDecimal.ZERO) != 0) {
                            parentUserIdProfitMap.put(accountType.toString(), v.getProfitAmount());
                        }
                    });
                } else {
                    accountProfitDTO.getAccountProfitMap().forEach((accountType, v) -> {
                        if (v.getProfitAmount().compareTo(BigDecimal.ZERO) != 0) {
                            subUserIdProfitMap.compute(accountType.toString(), (k, oldValue) -> oldValue == null ? v.getProfitAmount() : oldValue.add(v.getProfitAmount()));
                        }
                    });
                }
            }
            // 推送风控数据
            FeatureKeyParam featureKeyParam = new FeatureKeyParam();
            featureKeyParam.setFeatureKey(userId.toString());
            featureKeyParam.setFeatureCodeValueMap(new HashMap<>() {{
                put("reconciliation_asset_abnormal", 1);
                put("reconciliation_asset_profit", new HashMap<>() {{
                    put("profit", parentUserIdProfitMap);
                    put("subProfit", subUserIdProfitMap);
                }});
            }});
            FeatureStoreResult featureStoreResult = featureStoreFacade.batchUpdate(Lists.newArrayList(featureKeyParam));
            log.info("ProfitAbnormalService.sendAbnormalProfitRiskV2 userId:{} requestTime:{} userAccountProfitMap:{} featureKeyParam:{} featureStoreResult:{}", userId, requestTime, JSONObject.toJSONString(userAccountProfitMap), JSONObject.toJSONString(featureKeyParam), JSON.toJSONString(featureStoreResult));
            if (Objects.isNull(featureStoreResult) || Boolean.FALSE.equals(featureStoreResult.isSuccess())) {
                log.error("ProfitAbnormalAddFeatureFail,errmsg:{},userId:{}", JSONObject.toJSONString(featureStoreResult), userId);
                return false;
            }
            String redisKey = String.format(BillRedisKeyEnum.RECON_WITHDRAWAL_PROFIT_RISK_RESULT.getKey(), userId);
            redisTemplate.opsForValue().set(redisKey, JSON.toJSONString(Pair.of(userId, new Date().getTime())), globalBillConfig.getTimePeriodProfitRedisTime(), TimeUnit.HOURS);
            return true;
        } catch (Exception e) {
            log.error("ProfitAbnormalAddFeatureError errmsg:{} userId:{} requestTime:{} userAccountProfitMap:{}", e, userId, requestTime, JSONObject.toJSONString(userAccountProfitMap));
        }
        return false;
    }

}
