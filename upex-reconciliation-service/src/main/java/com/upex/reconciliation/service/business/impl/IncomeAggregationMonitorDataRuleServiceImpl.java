package com.upex.reconciliation.service.business.impl;

import com.google.common.collect.Lists;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.business.MonitorNonInputDataRuleService;
import com.upex.reconciliation.service.business.createtablebyroute.BillCoinTypeUserPropertyTableCreator;
import com.upex.reconciliation.service.business.module.processor.MonitorCheckProcessor;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.ProfitTransferTypeEnum;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.MonitorCmdWrapper;
import com.upex.reconciliation.service.model.config.IncomeCheckConfig;
import com.upex.reconciliation.service.model.config.IncomeCheckUserConfig;
import com.upex.reconciliation.service.model.config.MonitorSceneTaskConfig;
import com.upex.reconciliation.service.model.dto.AbstractProperty;
import com.upex.reconciliation.service.model.enums.MonitorCmdEnum;
import com.upex.reconciliation.service.model.param.IncomeCheckData;
import com.upex.reconciliation.service.model.param.MonitorIncomeAggregationData;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.service.impl.BillCoinUserPropertySnapshotService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.ticker.facade.dto.PriceVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class IncomeAggregationMonitorDataRuleServiceImpl implements MonitorNonInputDataRuleService {

    @Resource
    private BillCoinUserPropertySnapshotService userPropertySnapshotService;

    @Resource
    private BillCoinUserPropertyService userPropertyService;
    @Resource
    private BillCoinTypePropertyService billCoinTypePropertyService;

    @Resource
    private MonitorCheckProcessor monitorCheckProcessor;

    @Resource
    private BillConfigService billConfigService;

    @Resource
    private AlarmNotifyService alarmNotifyService;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private OldBillProfitTransferService oldBillProfitTransferService;

    @Resource
    private OldBillContractProfitTransferService oldBillContractProfitTransferService;
    @Resource
    private BillCoinTypeUserPropertyService billCoinTypeUserPropertyService;
    @Resource
    private BillCoinPropertyService billCoinPropertyService;
    @Resource
    private BillCoinTypeUserPropertyTableCreator billCoinTypeUserPropertyTableCreator;


    @Resource
    private CommonService commonService;

    private static final String ACCOUNT_TYPE_FINISHED_SIGN_KEY = "income_aggregation_finished_account_type:";

    private static final Integer ACCOUNT_TYPE_FINISHED_SIGN_EXPIRE_HOUR = 2;

    private static final int BY_FEE_TYPE = 1;
    private static final int BY_BIZ_TYPE = 2;


    @Override
    public void processScene(Long sceneId, Date bizTime, Byte accountType) {
        try {
            MonitorSceneTaskConfig monitorSceneTaskConfig = ReconciliationApolloConfigUtils.getMonitorSceneTaskConfig(sceneId);
            // 获取到业务线的所有配置
            IncomeCheckConfig incomeCheckConfig = ReconciliationApolloConfigUtils.getIncomeCheckConfig();
            // 所有业务线的内容
            List<IncomeCheckUserConfig> incomeCheckUserConfigs = incomeCheckConfig.getConfigs();
            // 前一天早上8点的时间
            Date lastDayBizTime = new Date(bizTime.getTime() - BillConstants.ONE_DAY_MIL_SEC);

            // 待动账的数据也先提前获取
            List<BillContractProfitTransfer> todayTransfer = getBillContractProfitTransfers(bizTime);
            List<BillContractProfitTransfer> lastTransfer = getBillContractProfitTransfers(lastDayBizTime);
            BillContractProfitTransfer defaultBillContractProfitTransfer = new BillContractProfitTransfer();
            defaultBillContractProfitTransfer.setTransferCount(BigDecimal.ZERO);

            // 取今日的汇率
            Map<Integer, PriceVo> rates = commonService.getCoinIdRatesMapCache(bizTime.getTime());


            for (IncomeCheckUserConfig incomeCheckUserConfig : incomeCheckUserConfigs) {
                MonitorIncomeAggregationData monitorIncomeAggregationData = new MonitorIncomeAggregationData();
                monitorIncomeAggregationData.setBizTime(bizTime);
                monitorIncomeAggregationData.setUserId(incomeCheckUserConfig.getSystemUserId());
                monitorIncomeAggregationData.setAccountType(incomeCheckUserConfig.getAccountType());
                monitorIncomeAggregationData.setSceneName(incomeCheckUserConfig.getName());
                AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(incomeCheckUserConfig.getAccountType());

                BillConfig billConfig = billConfigService.selectByTypeAndParam(incomeCheckUserConfig.getAccountType(), incomeCheckUserConfig.getAccountParam());
                if (billConfig.getCheckOkTime().compareTo(bizTime) < 0) {
                    continue;
                }
                // 获取系统账号的所有币种
                List<BillCoinUserProperty> allCoins = userPropertyService.selectUserLatestAllRecord(Integer.valueOf(incomeCheckUserConfig.getAccountType()), incomeCheckUserConfig.getAccountParam(), incomeCheckUserConfig.getSystemUserId());
                Set<Integer> allCoinIds = allCoins.stream().map(BillCoinUserProperty::getCoinId).collect(Collectors.toSet());
                Set<Integer> notFeeCoinIds = incomeCheckUserConfig.getNotFeeCoinIds();
                if (!CollectionUtils.isEmpty(notFeeCoinIds)) {
                    allCoinIds.removeAll(notFeeCoinIds);
                }
                Map<Integer, BillContractProfitTransfer> todayTransferMap = new HashMap<>();
                Map<Integer, BillContractProfitTransfer> lastDayTransferMap = new HashMap<>();
                if (incomeCheckUserConfig.getType() == BY_FEE_TYPE) {
                    todayTransferMap = todayTransfer.stream()
                            .filter(asset -> asset.getToAccountParam().equalsIgnoreCase(String.valueOf(incomeCheckUserConfig.getAccountType())))
                            .collect(Collectors.toMap(BillContractProfitTransfer::getCoinId, Function.identity(), (key1, key2) -> {
                                key2.setTransferCount(key1.getTransferCount().add(key2.getTransferCount()));
                                return key2;
                            }));
                    lastDayTransferMap = lastTransfer.stream()
                            .filter(asset -> asset.getToAccountParam().equalsIgnoreCase(String.valueOf(incomeCheckUserConfig.getAccountType())))
                            .collect(Collectors.toMap(BillContractProfitTransfer::getCoinId, Function.identity(), (key1, key2) -> {
                                key2.setTransferCount(key1.getTransferCount().add(key2.getTransferCount()));
                                return key2;
                            }));
                }

                List<BillCoinProperty> currentBillCoinPropertyList = billCoinPropertyService.selectCheckTimeRecord(Integer.valueOf(incomeCheckUserConfig.getAccountType()), incomeCheckUserConfig.getAccountParam(), bizTime);
                List<BillCoinProperty> lastDayBillCoinPropertyList = billCoinPropertyService.selectCheckTimeRecord(Integer.valueOf(incomeCheckUserConfig.getAccountType()), incomeCheckUserConfig.getAccountParam(), lastDayBizTime);
                Map<Integer, BillCoinProperty> currentBillCoinPropertyMap = currentBillCoinPropertyList.stream().collect(Collectors.toMap(BillCoinProperty::getCoinId, Function.identity()));
                Map<Integer, BillCoinProperty> lastDayBillCoinPropertyMap = lastDayBillCoinPropertyList.stream().collect(Collectors.toMap(BillCoinProperty::getCoinId, Function.identity()));

                // 获取系统账户-本期的资产情况
                Long feeSystemUserId = incomeCheckUserConfig.getSystemUserId();
                // 获取这个人24小时内，所有biz_type的变化值
                String tableSuffix = billCoinTypeUserPropertyTableCreator.getTableSuffixName(incomeCheckUserConfig.getAccountType().toString(), bizTime);
                List<BillCoinTypeUserProperty> allChanges = billCoinTypeUserPropertyService.listAllChangeGroupByCoinBizType(tableSuffix, Integer.valueOf(incomeCheckUserConfig.getAccountType()), feeSystemUserId, lastDayBizTime, bizTime);
                String tableSuffixLast = billCoinTypeUserPropertyTableCreator.getTableSuffixName(incomeCheckUserConfig.getAccountType().toString(), lastDayBizTime);
                List<BillCoinTypeUserProperty> allChangesLastDay = billCoinTypeUserPropertyService.listAllChangeGroupByCoinBizType(tableSuffixLast, Integer.valueOf(incomeCheckUserConfig.getAccountType()), feeSystemUserId, lastDayBizTime, bizTime);

                List<BillCoinUserProperty> currentBillCoinUserPropertyList = userPropertySnapshotService.selectAllCoinUserByCheckTimeAndCoinIds(Integer.valueOf(incomeCheckUserConfig.getAccountType()), incomeCheckUserConfig.getAccountParam(), feeSystemUserId, bizTime);
                List<BillCoinUserProperty> lastBillCoinUserPropertyList = userPropertySnapshotService.selectAllCoinUserByCheckTimeAndCoinIds(Integer.valueOf(incomeCheckUserConfig.getAccountType()), incomeCheckUserConfig.getAccountParam(), feeSystemUserId, lastDayBizTime);
                // 24小时前后的所有币资产，转为map
                Map<Integer, BillCoinUserProperty> currentBillCoinUserPropertyMap = currentBillCoinUserPropertyList.stream().collect(Collectors.toMap(BillCoinUserProperty::getCoinId, Function.identity()));
                Map<Integer, BillCoinUserProperty> lastBillCoinUserPropertyMap = lastBillCoinUserPropertyList.stream().collect(Collectors.toMap(BillCoinUserProperty::getCoinId, Function.identity()));
                // 按照coin进行分组
                Map<Integer, List<BillCoinTypeUserProperty>> allChangesMap = allChanges.stream()
                        .collect(Collectors.groupingBy(BillCoinTypeUserProperty::getCoinId));
                Map<Integer, List<BillCoinTypeUserProperty>> allChangesLastDayMap = allChangesLastDay.stream()
                        .collect(Collectors.groupingBy(BillCoinTypeUserProperty::getCoinId));


                List<BillCoinTypeProperty> currentAllBillCoinTypePropertyList = billCoinTypePropertyService.selectAllCoinByCheckTimeInBizTypes(Integer.valueOf(incomeCheckUserConfig.getAccountType()), incomeCheckUserConfig.getAccountParam(), bizTime, incomeCheckUserConfig.getFeeBizTypes());
                List<BillCoinTypeProperty> lastDayAllBillCoinTypePropertyList = billCoinTypePropertyService.selectAllCoinByCheckTimeInBizTypes(Integer.valueOf(incomeCheckUserConfig.getAccountType()), incomeCheckUserConfig.getAccountParam(), lastDayBizTime, incomeCheckUserConfig.getFeeBizTypes());
                Map<Integer, List<BillCoinTypeProperty>> currentAllBillCoinTypePropertyMap = currentAllBillCoinTypePropertyList.stream().collect(Collectors.groupingBy(BillCoinTypeProperty::getCoinId));
                Map<Integer, List<BillCoinTypeProperty>> lastDayAllBillCoinTypePropertyMap = lastDayAllBillCoinTypePropertyList.stream().collect(Collectors.groupingBy(BillCoinTypeProperty::getCoinId));
                Map<Integer, List<BillCoinTypeProperty>> currentAllBillCoinTypePropertyMapExtra = new HashMap<>();
                Map<Integer, List<BillCoinTypeProperty>> lastDayAllBillCoinTypePropertyMapExtra = new HashMap<>();
                if (incomeCheckUserConfig.getExtraAccountType() != null) {
                    List<BillCoinTypeProperty> currentAllBillCoinTypePropertyListExtra = billCoinTypePropertyService.selectAllCoinByCheckTimeInBizTypes(Integer.valueOf(incomeCheckUserConfig.getExtraAccountType()), incomeCheckUserConfig.getAccountParam(), bizTime, incomeCheckUserConfig.getFeeBizTypes());
                    List<BillCoinTypeProperty> lastDayAllBillCoinTypePropertyListExtra = billCoinTypePropertyService.selectAllCoinByCheckTimeInBizTypes(Integer.valueOf(incomeCheckUserConfig.getExtraAccountType()), incomeCheckUserConfig.getAccountParam(), lastDayBizTime, incomeCheckUserConfig.getFeeBizTypes());
                    currentAllBillCoinTypePropertyMapExtra = currentAllBillCoinTypePropertyListExtra.stream().collect(Collectors.groupingBy(BillCoinTypeProperty::getCoinId));
                    lastDayAllBillCoinTypePropertyMapExtra = lastDayAllBillCoinTypePropertyListExtra.stream().collect(Collectors.groupingBy(BillCoinTypeProperty::getCoinId));
                }


                for (Integer coinId : allCoinIds) {
                    IncomeCheckData incomeCheckData = new IncomeCheckData();
                    List<BillCoinTypeUserProperty> allChangesCoin = allChangesMap.get(coinId);
                    List<BillCoinTypeUserProperty> allChangesLastDayCoin = allChangesLastDayMap.get(coinId);
                    Map<String, BillCoinTypeUserProperty> allBizTypeChangeMap = CollectionUtils.isEmpty(allChangesCoin) ? new HashMap<>() : allChangesCoin.stream().collect(Collectors.toMap(BillCoinTypeUserProperty::getBizType, Function.identity()));
                    if (!CollectionUtils.isEmpty(allChangesLastDayCoin)) {
                        for (BillCoinTypeUserProperty lastDayChange : allChangesLastDayCoin) {
                            if (allBizTypeChangeMap.get(lastDayChange.getBizType()) != null) {
                                addChange(allBizTypeChangeMap.get(lastDayChange.getBizType()), lastDayChange);
                            } else {
                                allBizTypeChangeMap.put(lastDayChange.getBizType(), lastDayChange);
                            }
                        }
                    }
                    Map<String, BigDecimal> bizTypeChangeMap = incomeCheckData.getBizTypeChangeMap();
                    for (Map.Entry<String, BillCoinTypeUserProperty> entry : allBizTypeChangeMap.entrySet()) {
                        BillCoinTypeUserProperty billCoinTypeUserProperty = entry.getValue();
                        bizTypeChangeMap.put(entry.getKey(), sumChangeByAccountType(billCoinTypeUserProperty, accountTypeEnum));
                    }

                    // 不存在，则再单查一遍数据库确认确实没有
                    BillCoinUserProperty currentBillCoinUserProperty = currentBillCoinUserPropertyMap.get(coinId);
                    if (currentBillCoinUserProperty == null) {
                        currentBillCoinUserProperty = userPropertySnapshotService.selectCoinUserBeforeCheckTimeRecord(Integer.valueOf(incomeCheckUserConfig.getAccountType()), incomeCheckUserConfig.getAccountParam(), coinId, feeSystemUserId, bizTime);
                    }
                    BillCoinUserProperty lastBillCoinUserProperty = lastBillCoinUserPropertyMap.get(coinId);
                    if (lastBillCoinUserProperty == null) {
                        lastBillCoinUserProperty = userPropertySnapshotService.selectCoinUserBeforeCheckTimeRecord(Integer.valueOf(incomeCheckUserConfig.getAccountType()), incomeCheckUserConfig.getAccountParam(), coinId, feeSystemUserId, lastDayBizTime);
                    }
                    BillCoinUserProperty coinUserProp24HourChange = calculateChange(feeSystemUserId, coinId, currentBillCoinUserProperty, lastBillCoinUserProperty);
                    incomeCheckData.setCoinId(coinId);
                    String coinName = commonService.getCoinName(coinId);
                    if (StringUtils.isNotBlank(coinName)) {
                        log.error("IncomeAggregationMonitorDataRuleServiceImpl Failed to get coin Name with coinId : {}", coinId);
                    }
                    incomeCheckData.setCoinName(StringUtils.isNotBlank(coinName) ? coinName : String.valueOf(coinId));
                    incomeCheckData.setIncomeCountChange(sumChangeByAccountType(coinUserProp24HourChange, accountTypeEnum));
                    incomeCheckData.setTolerance(incomeCheckUserConfig.getToleranceMap().get(coinId) != null ? incomeCheckUserConfig.getToleranceMap().get(coinId) : BigDecimal.ZERO);
                    if (BY_FEE_TYPE == incomeCheckUserConfig.getType()) {
                        // 标准手续费处理逻辑
                        // 公式：   应收  +  昨日待动账  -  今日待动账 = 实收
                        BillCoinProperty billCoinProperty = currentBillCoinPropertyMap.get(coinId) != null ? currentBillCoinPropertyMap.get(coinId) : billCoinPropertyService.selectLastCheckTimeRecord(Integer.valueOf(incomeCheckUserConfig.getAccountType()), incomeCheckUserConfig.getAccountParam(), coinId, bizTime);
                        if (billCoinProperty == null) {
                            continue;
                        }
                        BillCoinProperty lastBillCoinProperty = lastDayBillCoinPropertyMap.get(coinId) != null ? lastDayBillCoinPropertyMap.get(coinId) : billCoinPropertyService.selectLastCheckTimeRecord(Integer.valueOf(incomeCheckUserConfig.getAccountType()), incomeCheckUserConfig.getAccountParam(), coinId, lastDayBizTime);
                        BigDecimal toCollect = billCoinProperty.getFee().subtract(lastBillCoinProperty != null ? lastBillCoinProperty.getFee() : BigDecimal.ZERO);
                        BillContractProfitTransfer billContractProfitTransfer = todayTransferMap.getOrDefault(coinId, defaultBillContractProfitTransfer);
                        BigDecimal transferCountToday = billContractProfitTransfer.getTransferCount();
                        BillContractProfitTransfer billContractProfitTransferLastDay = lastDayTransferMap.getOrDefault(coinId, defaultBillContractProfitTransfer);
                        BigDecimal transferCountLastDay = billContractProfitTransferLastDay.getTransferCount();
                        BigDecimal feeTotal = toCollect.add(transferCountLastDay).subtract(transferCountToday);
                        incomeCheckData.setFeeCountChange(feeTotal.abs());
                    } else if (BY_BIZ_TYPE == incomeCheckUserConfig.getType()) {
                        // biz_type 入出处理逻辑
                        // 公式：    应收 = 实收
                        List<BillCoinTypeProperty> currentBillCoinTypePropertyList = currentAllBillCoinTypePropertyMap.get(coinId);
                        // 前一天的费用
                        List<BillCoinTypeProperty> lastBillCoinTypePropertyList = lastDayAllBillCoinTypePropertyMap.get(coinId);
                        List<BillCoinTypeProperty> userBillAggregationMonitorDataList = buildFeeAggregationData(coinId, currentBillCoinTypePropertyList, lastBillCoinTypePropertyList);
                        BillCoinTypeProperty feeBillCoinTypeProperty = sumChangeFee(userBillAggregationMonitorDataList);
                        if (incomeCheckUserConfig.getExtraAccountType() != null) {
                            List<BillCoinTypeProperty> currentBillCoinTypePropertyListExtra = currentAllBillCoinTypePropertyMapExtra.get(coinId);
                            List<BillCoinTypeProperty> lastBillCoinTypePropertyListExtra = lastDayAllBillCoinTypePropertyMapExtra.get(coinId);
                            List<BillCoinTypeProperty> userBillAggregationMonitorDataListExtra = buildFeeAggregationData(coinId, currentBillCoinTypePropertyListExtra, lastBillCoinTypePropertyListExtra);
                            BillCoinTypeProperty extraFeeBillCoinTypeProperty = sumChangeFee(userBillAggregationMonitorDataListExtra);
                            addChange(feeBillCoinTypeProperty, extraFeeBillCoinTypeProperty);
                        }
                        incomeCheckData.setFeeCountChange(sumChangeByAccountType(feeBillCoinTypeProperty, accountTypeEnum).abs());
                    }
                    incomeCheckData.setDiffCount(incomeCheckData.getIncomeCountChange().subtract(incomeCheckData.getFeeCountChange()).abs());
                    BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(coinId, rates);
                    incomeCheckData.setDiffAmountUsdt(incomeCheckData.getDiffCount().multiply(rate));
                    monitorIncomeAggregationData.getIncomeCheckDataList().add(incomeCheckData);
                }
                MonitorCmdWrapper monitorCmdWrapper = new MonitorCmdWrapper(monitorSceneTaskConfig, MonitorCmdEnum.AGGREGATION_TYPE, null, monitorIncomeAggregationData, bizTime);
                // 处理结束，向对账cmd队列提交命令
                monitorCheckProcessor.offerCommand(monitorCmdWrapper);
                log.info("IncomeAggregationMonitorDataRuleServiceImpl finished sceneName {}", incomeCheckUserConfig.getName());
            }
        } catch (Exception e) {
            log.error("IncomeAggregationMonitorDataRuleServiceImpl error", e);
        }
    }


    private void addChange(AbstractProperty billCoinProperty, AbstractProperty toAddProperty) {
        billCoinProperty.setChangeProp1(billCoinProperty.getChangeProp1().add(toAddProperty.getChangeProp1()));
        billCoinProperty.setChangeProp2(billCoinProperty.getChangeProp2().add(toAddProperty.getChangeProp2()));
        billCoinProperty.setChangeProp3(billCoinProperty.getChangeProp3().add(toAddProperty.getChangeProp3()));
        billCoinProperty.setChangeProp4(billCoinProperty.getChangeProp4().add(toAddProperty.getChangeProp4()));
        billCoinProperty.setChangeProp5(billCoinProperty.getChangeProp5().add(toAddProperty.getChangeProp5()));

    }

    BigDecimal sumChangeByAccountType(AbstractProperty billCoinProperty, AccountTypeEnum accountType) {
        if (AccountTypeEnum.SPOT.equals(accountType)) {
            return billCoinProperty.getChangeProp1();
        }
        if (accountType.isContract()) {
            return billCoinProperty.getChangeProp2().add(billCoinProperty.getChangeProp3());
        }
        if (accountType.isLever()) {
            return billCoinProperty.getChangeProp1().add(billCoinProperty.getChangeProp2()).add(billCoinProperty.getChangeProp3());
        }
        return BigDecimal.ZERO;
    }


    BigDecimal sumChange(AbstractProperty billCoinProperty, AbstractProperty lastDayBillCoinProperty, AccountTypeEnum accountType) {
        if (AccountTypeEnum.SPOT.equals(accountType)) {
            return billCoinProperty.getProp1().subtract(lastDayBillCoinProperty.getProp1());
        }
        if (accountType.isContract()) {
            return billCoinProperty.getProp2().add(billCoinProperty.getProp3()).subtract(lastDayBillCoinProperty.getProp2()).subtract(lastDayBillCoinProperty.getProp3());
        }
        if (accountType.isLever()) {
            return billCoinProperty.getProp1().add(billCoinProperty.getProp2()).add(billCoinProperty.getProp3()).subtract(lastDayBillCoinProperty.getProp1()).subtract(lastDayBillCoinProperty.getProp2()).subtract(lastDayBillCoinProperty.getProp3());
        }
        return BigDecimal.ZERO;
    }


    public BillCoinTypeProperty sumChangeFee(List<BillCoinTypeProperty> coinUserProperties) {
        BillCoinTypeProperty billCoinTypeProperty = new BillCoinTypeProperty();
        if (!CollectionUtils.isEmpty(coinUserProperties)) {
            for (BillCoinTypeProperty item : coinUserProperties) {
                billCoinTypeProperty.setChangeProp1(billCoinTypeProperty.getChangeProp1().add(item.getChangeProp1()));
                billCoinTypeProperty.setChangeProp2(billCoinTypeProperty.getChangeProp2().add(item.getChangeProp2()));
                billCoinTypeProperty.setChangeProp3(billCoinTypeProperty.getChangeProp3().add(item.getChangeProp3()));
                billCoinTypeProperty.setChangeProp4(billCoinTypeProperty.getChangeProp4().add(item.getChangeProp4()));
                billCoinTypeProperty.setChangeProp5(billCoinTypeProperty.getChangeProp5().add(item.getChangeProp5()));
            }
        }
        return billCoinTypeProperty;
    }


    public List<BillContractProfitTransfer> getBillContractProfitTransfers(Date checkOkTime) {
        // 待动账金额
        List<ProfitTransferTypeEnum> typeEnums = Lists.newArrayList(ProfitTransferTypeEnum.SYSTEM_CONTRACT_FEE, ProfitTransferTypeEnum.SYSTEM_RESET, ProfitTransferTypeEnum.SYSTEM_SPOT_FEE, ProfitTransferTypeEnum.SYSTEM_SPOT_FEE_RESET);
        List<BillContractProfitTransfer> transfers = Lists.newArrayList();
        transfers.addAll(oldBillContractProfitTransferService.selectDelayedTransactionByTimeAndType(checkOkTime, typeEnums, null));
        transfers.addAll(oldBillContractProfitTransferService.selectDelayedTransactionByStatusAndTimeAndType(checkOkTime, typeEnums, null));
        if (DateUtil.isFirstDayOfMonth(checkOkTime)) {
            Date lastMonth = DateUtil.getLastMonthEndTime(checkOkTime);
            transfers.addAll(oldBillContractProfitTransferService.selectDelayedTransactionByTimeAndType(checkOkTime, typeEnums, lastMonth));
            transfers.addAll(oldBillContractProfitTransferService.selectDelayedTransactionByStatusAndTimeAndType(checkOkTime, typeEnums, lastMonth));
        }
        return transfers;
    }

    public BillCoinUserProperty sumChangeIncome(List<BillCoinUserProperty> coinUserProperties) {
        BillCoinUserProperty billCoinUserProperty = new BillCoinUserProperty();
        if (!CollectionUtils.isEmpty(coinUserProperties)) {
            for (BillCoinUserProperty item : coinUserProperties) {
                billCoinUserProperty.setChangeProp1(billCoinUserProperty.getChangeProp1().add(item.getChangeProp1()));
                billCoinUserProperty.setChangeProp2(billCoinUserProperty.getChangeProp2().add(item.getChangeProp2()));
                billCoinUserProperty.setChangeProp3(billCoinUserProperty.getChangeProp3().add(item.getChangeProp3()));
                billCoinUserProperty.setChangeProp4(billCoinUserProperty.getChangeProp4().add(item.getChangeProp4()));
                billCoinUserProperty.setChangeProp5(billCoinUserProperty.getChangeProp5().add(item.getChangeProp5()));
            }
        }
        return billCoinUserProperty;
    }

    public List<BillCoinTypeProperty> buildFeeAggregationData(Integer coinId, List<BillCoinTypeProperty> currentList, List<BillCoinTypeProperty> lastList) {
        if (CollectionUtils.isEmpty(currentList)) {
            return Collections.emptyList();
        }
        List<BillCoinTypeProperty> resultList = new ArrayList<>();
        Map<String, BillCoinTypeProperty> lastMap = !CollectionUtils.isEmpty(lastList) ? lastList.stream().collect(Collectors.toMap(BillCoinTypeProperty::getBizType, Function.identity())) : new HashMap<>();
        for (BillCoinTypeProperty currentProperty : currentList) {
            BillCoinTypeProperty lastBillCoinTypeProperty = lastMap.get(currentProperty.getBizType());
            BillCoinTypeProperty coinTypeProperty = calculateChange(coinId, currentProperty, lastBillCoinTypeProperty);
            resultList.add(coinTypeProperty);
        }
        return resultList;

    }


    public BillCoinTypeProperty calculateChange(Integer coinId, AbstractProperty currentProperty, AbstractProperty lastProperty) {
        BillCoinTypeProperty aggregationData = new BillCoinTypeProperty();
        aggregationData.setCoinId(coinId);
        if (currentProperty == null) {
            return aggregationData;
        }
        aggregationData.setChangeProp1(lastProperty != null ? currentProperty.getProp1().subtract(lastProperty.getProp1()) : currentProperty.getProp1());
        aggregationData.setChangeProp2(lastProperty != null ? currentProperty.getProp2().subtract(lastProperty.getProp2()) : currentProperty.getProp2());
        aggregationData.setChangeProp3(lastProperty != null ? currentProperty.getProp3().subtract(lastProperty.getProp3()) : currentProperty.getProp3());
        aggregationData.setChangeProp4(lastProperty != null ? currentProperty.getProp4().subtract(lastProperty.getProp4()) : currentProperty.getProp4());
        aggregationData.setChangeProp5(lastProperty != null ? currentProperty.getProp5().subtract(lastProperty.getProp5()) : currentProperty.getProp5());
        return aggregationData;
    }


    public BillCoinUserProperty calculateChange(Long userId, Integer coinId, AbstractProperty currentProperty, AbstractProperty lastProperty) {
        BillCoinUserProperty aggregationData = new BillCoinUserProperty();
        aggregationData.setUserId(userId);
        aggregationData.setCoinId(coinId);
        if (currentProperty == null) {
            return aggregationData;
        }
        aggregationData.setChangeProp1(lastProperty != null ? currentProperty.getProp1().subtract(lastProperty.getProp1()) : currentProperty.getProp1());
        aggregationData.setChangeProp2(lastProperty != null ? currentProperty.getProp2().subtract(lastProperty.getProp2()) : currentProperty.getProp2());
        aggregationData.setChangeProp3(lastProperty != null ? currentProperty.getProp3().subtract(lastProperty.getProp3()) : currentProperty.getProp3());
        aggregationData.setChangeProp4(lastProperty != null ? currentProperty.getProp4().subtract(lastProperty.getProp4()) : currentProperty.getProp4());
        aggregationData.setChangeProp5(lastProperty != null ? currentProperty.getProp5().subtract(lastProperty.getProp5()) : currentProperty.getProp5());
        return aggregationData;

    }


}
