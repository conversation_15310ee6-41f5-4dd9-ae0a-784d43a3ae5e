package com.upex.reconciliation.service.model.dto;

import com.alibaba.fastjson.JSON;
import com.upex.mixcontract.common.literal.enums.BusinessLineEnum;
import com.upex.mixcontract.common.literal.enums.DelegateTypeEnum;
import com.upex.mixcontract.common.literal.enums.DepthSideEnum;
import com.upex.mixcontract.common.literal.enums.SecondBusinessLineEnum;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.enums.LogLevelEnum;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty;
import com.upex.reconciliation.service.model.config.ApolloBillOrderDetailConfig;
import com.upex.reconciliation.service.utils.BizLogUtils;
import com.upex.reconciliation.service.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.DEALT_SECOND_BUSINESS_LINE_ERROR;

@Slf4j
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountDealtRecordData {
    private Long bizId;
    private Long accountId;
    private String secondBusinessLine;
    private String delegateType;
    private String depthSide;
    private String symbolId;
    private Integer baseTokenId;
    private Integer quoteTokenId;
    private BigDecimal dealtPrice;
    private BigDecimal dealtCount;
    private BigDecimal dealtAmount;
    private BigDecimal profits;
    private Date createTime;
    private Date bizTime;
    private BigDecimal fee;
    private Integer feeTokenId;
    private BigDecimal receivableFee;
    /***是否抵扣BGB***/
    private Boolean useDt;
    /***kafka信息***/
    private Long offset;
    private Integer partition;
    private Long kafkaTimestamp;
    private Long consumerTimestamp;

    /**
     * 订单转换
     *
     * @return
     */
    public List<BillCoinTypeUserProperty> convertToCoinTypeUserPropertyList(String businessKey, ApolloBillOrderDetailConfig apolloBillOrderDetailConfig, AlarmNotifyService alarmNotifyService) {
        List<BillCoinTypeUserProperty> list = new ArrayList<>();
        SecondBusinessLineEnum secondBusinessLine = SecondBusinessLineEnum.toEnumByCode(Integer.parseInt(this.secondBusinessLine));
        DelegateTypeEnum delegateType = DelegateTypeEnum.toEnum(Integer.parseInt(this.delegateType));
        DepthSideEnum depthSideEnum = delegateType.getDepthSideEnum();

        String deductBizType = String.format("%s#%s#%s", secondBusinessLine.getCode(), delegateType.getCode(), DepthSideEnum.SELL.getCode());
        if (secondBusinessLine.getCode() == BusinessLineEnum.SPOT_BL.getCode() || secondBusinessLine.getCode() == BusinessLineEnum.MARGIN_BL.getCode()) {
            String addBizType = String.format("%s#%s#%s", secondBusinessLine.getCode(), delegateType.getCode(), DepthSideEnum.BUY.getCode());
            if (depthSideEnum == DepthSideEnum.BUY) {
                // 买单 得到币
                BigDecimal addBalanceChange = dealtCount.add(useDt ? BigDecimal.ZERO : fee);
                BillCoinTypeUserProperty addBillCoinTypeUserProperty = new BillCoinTypeUserProperty();
                addBillCoinTypeUserProperty.setUserId(accountId);
                addBillCoinTypeUserProperty.setBizType(addBizType);
                addBillCoinTypeUserProperty.setCheckTime(bizTime);
                addBillCoinTypeUserProperty.setCoinId(baseTokenId);
                addBillCoinTypeUserProperty.setChangeProp1(addBalanceChange);
                list.add(addBillCoinTypeUserProperty);
                // 买单 扣除币
                BigDecimal deductBalanceChange = dealtAmount.negate();
                BillCoinTypeUserProperty deductBillCoinTypeUserProperty = new BillCoinTypeUserProperty();
                deductBillCoinTypeUserProperty.setUserId(accountId);
                deductBillCoinTypeUserProperty.setBizType(deductBizType);
                deductBillCoinTypeUserProperty.setCheckTime(bizTime);
                deductBillCoinTypeUserProperty.setCoinId(quoteTokenId);
                deductBillCoinTypeUserProperty.setChangeProp1(deductBalanceChange);
                list.add(deductBillCoinTypeUserProperty);
            } else {
                // 卖单 得到币
                BigDecimal addBalanceChange = dealtAmount.add(useDt ? BigDecimal.ZERO : fee);
                BillCoinTypeUserProperty addBillCoinTypeUserProperty = new BillCoinTypeUserProperty();
                addBillCoinTypeUserProperty.setUserId(accountId);
                addBillCoinTypeUserProperty.setBizType(addBizType);
                addBillCoinTypeUserProperty.setCheckTime(bizTime);
                addBillCoinTypeUserProperty.setCoinId(quoteTokenId);
                addBillCoinTypeUserProperty.setChangeProp1(addBalanceChange);
                list.add(addBillCoinTypeUserProperty);
                // 卖单 扣除币
                BigDecimal deductBalanceChange = dealtCount.negate();
                BillCoinTypeUserProperty deductBillCoinTypeUserProperty = new BillCoinTypeUserProperty();
                deductBillCoinTypeUserProperty.setUserId(accountId);
                deductBillCoinTypeUserProperty.setBizType(deductBizType);
                deductBillCoinTypeUserProperty.setCheckTime(bizTime);
                deductBillCoinTypeUserProperty.setCoinId(baseTokenId);
                deductBillCoinTypeUserProperty.setChangeProp1(deductBalanceChange);
                list.add(deductBillCoinTypeUserProperty);
            }
        } else if (secondBusinessLine.getCode() == BusinessLineEnum.USDT_MIX_CONTRACT_BL.getCode()
                || secondBusinessLine.getCode() == BusinessLineEnum.USD_MIX_CONTRACT_BL.getCode()
                || secondBusinessLine.getCode() == BusinessLineEnum.USDC_MIX_CONTRACT_BL.getCode()) {
            if (delegateType.getCode() == DelegateTypeEnum.SELL_IN_SINGLE_SIDE_MODE.getCode()
                    || delegateType.getCode() == DelegateTypeEnum.CLOSE_LONG.getCode()
                    || delegateType.getCode() == DelegateTypeEnum.CLOSE_SHORT.getCode()) {
                // 卖单 得到币
                BigDecimal addBalanceChange = profits.add(useDt ? BigDecimal.ZERO : fee);
                BillCoinTypeUserProperty addBillCoinTypeUserProperty = new BillCoinTypeUserProperty();
                addBillCoinTypeUserProperty.setUserId(accountId);
                addBillCoinTypeUserProperty.setBizType(deductBizType);
                addBillCoinTypeUserProperty.setCheckTime(bizTime);
                addBillCoinTypeUserProperty.setCoinId(quoteTokenId);
                addBillCoinTypeUserProperty.setChangeProp1(addBalanceChange);
                list.add(addBillCoinTypeUserProperty);
            }
        } else {
            BizLogUtils.log(LogLevelEnum.MAIN_PROCESS, apolloBillOrderDetailConfig.getDefaultLogLeve(), "AccountDealtRecordData.convertToCoinTypeUserPropertyList error, secondBusinessLine is not support userId:{} bizId:{} bizTime:{} secondBusinessLine:{}", this.getAccountId(), this.getBizId(), DateUtil.date2str(this.getBizTime()), secondBusinessLine.getCode());
            alarmNotifyService.alarm(DEALT_SECOND_BUSINESS_LINE_ERROR, businessKey, this.bizId, this.secondBusinessLine);
        }
        BizLogUtils.log(LogLevelEnum.DEBUG, apolloBillOrderDetailConfig.getDefaultLogLeve(), "AccountDealtRecordData.convertToCoinTypeUserPropertyList end userId:{} bizId:{} bizTime:{} list:{}", this.getAccountId(), this.getBizId(), DateUtil.date2str(this.getBizTime()), JSON.toJSONString(list));
        return list;
    }
}
