package com.upex.reconciliation.service.service.client.cex.dto.req.binance;

import com.binance.connector.client.common.Pair;
import lombok.Data;

@Data
public class BinanceParentSubTrasferReq extends BinanceApiBaseReq{

    private Long startTime;

    private Long endTime;

    private String fromAccountType;

    private String toAccountType;

    public void setStartTime(Long startTime) {
        queryParams.add(new Pair("startTime", String.valueOf(startTime)));
    }

    public void setEndTime(Long endTime) {
        queryParams.add(new Pair("endTime", String.valueOf(endTime)));
    }

    public void setFromAccountType(String fromAccountType) {
        queryParams.add(new Pair("fromAccountType", fromAccountType));
    }

    public void setToAccountType(String toAccountType) {
        queryParams.add(new Pair("toAccountType", toAccountType));
    }
}
