package com.upex.reconciliation.service.business.module.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.upex.mixcontract.common.framework.AttributeMap;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.BillCheckService;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.module.AbstractBillModule;
import com.upex.reconciliation.service.common.RateLimiterManager;
import com.upex.reconciliation.service.common.concurrent.ConcurrentSortMap;
import com.upex.reconciliation.service.common.constants.BillBizTypeConstant;
import com.upex.reconciliation.service.common.constants.ReconciliationCommandEnum;
import com.upex.reconciliation.service.common.constants.enums.CommonBillChangeDataMsgType;
import com.upex.reconciliation.service.common.constants.enums.LogLevelEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.entity.BillConfig;
import com.upex.reconciliation.service.model.BillCmdResult;
import com.upex.reconciliation.service.model.BillCmdWrapper;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.ReconKafkaOpsConfig;
import com.upex.reconciliation.service.model.dto.BillTimeSliceDTO;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.model.dto.PartitionOffsetDTO;
import com.upex.reconciliation.service.service.BillDelayAccountService;
import com.upex.reconciliation.service.service.ReconSystemAccountService;
import com.upex.reconciliation.service.utils.*;
import com.upex.utils.log.AlarmUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.ALARM_TIME_SLICE_DELAY_MSG;
import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.TIME_SLICE_CHECK_MODULE_TAKE_COMMAND_ERROR;
import static com.upex.reconciliation.service.utils.MetricsUtil.HISTOGRAM_SLICE_TAKE_COMMAND;

/**
 * 账单二阶段处理类，包含命令队列的存放
 * 主要处理时间片纬度的数据持续维护
 */
@Slf4j
public class BillTimeSliceCheckModule extends AbstractBillModule {
    /***命令处理队列***/
    private BlockingQueue<BillCmdWrapper> cmdQueue = new LinkedBlockingQueue<>(15000);
    /***消息时间片 key：时间戳， value 按整分钟进行聚合***/
    private final ConcurrentSortMap<Long, BillTimeSliceDTO> startTimeSliceDTOMap = new ConcurrentSortMap();
    /***存储时间片 key：时间戳， value 按整分钟进行聚合***/
    private final ConcurrentSortMap<Long, BillTimeSliceDTO> saveTimeSliceDTOMap = new ConcurrentSortMap();
    /***时间片offset***/
    private Map<Integer, Long> partitionOffsetMap = new HashMap<>();
    /***内存对账期初 reboot 加载 时间片对账通过后 赋值作为下一时间片期初***/
    private BillTimeSliceDTO lastBillTimeSliceDTO;
    private BillTimeSliceDTO lastSaveBillTimeSliceDTO;
    /***partition 最后消费时间***/
    private Map<Integer, Long> latestPartitionBizTime = new HashMap<>();
    private AlarmNotifyService alarmNotifyService;
    private RateLimiterManager rateLimiterManager;
    private RedisTemplate<String, Object> redisTemplate;
    /***重启时 reboootCheckOkTime 只加载一次 追数时过滤延迟消息***/
    private Long reboootCheckOkTime;
    private BillDelayAccountService billDelayAccountService;
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    private ReconSystemAccountService reconSystemAccountService;

    public BillTimeSliceCheckModule(BillLogicGroup logicGroup) {
        super(logicGroup);
    }

    @Override
    public void init(AttributeMap initContext) {
        accountTypeEnum = initContext.get(BillLogicGroup.ACCOUNT_TYPE_ENUM_KEY);
        ReconciliationSpringContext reconciliationSpringContext = initContext.get(BillLogicGroup.ENGINE_SPRING_CONTEXT_KEY);
        alarmNotifyService = reconciliationSpringContext.getAlarmNotifyService();
        rateLimiterManager = reconciliationSpringContext.getRateLimiterManager();
        redisTemplate = reconciliationSpringContext.getRedisTemplate();
        billDelayAccountService = reconciliationSpringContext.getBillDelayAccountService();
        accountAssetsServiceFactory = reconciliationSpringContext.getAccountAssetsServiceFactory();
        reconSystemAccountService = reconciliationSpringContext.getReconSystemAccountService();
    }

    @Override
    public void offerCommand(BillCmdWrapper cmdWrapper) {
        if (ReconciliationCommandEnum.BILL_CHANGE.equals(cmdWrapper.getCommandEnum())) {
            try {
                this.cmdQueue.put(cmdWrapper);
            } catch (InterruptedException e) {
                String errorMsg = "BillTimeSliceCheckModule Command queue is full : " + cmdWrapper.getCommandEnum().getName()
                        + " queue size: " + this.cmdQueue.size() + " logicGroup: " + logicGroup.getName();
                AlarmUtils.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }
        }
    }

    @Override
    public BillCmdResult takeCommand() {
        BillCmdWrapper cmdWrapper = null;
        try {
            cmdWrapper = this.cmdQueue.poll(1, TimeUnit.SECONDS);
            if (cmdWrapper == null) {
                return null;
            }
            BillCmdWrapper finalCmdWrapper = cmdWrapper;
            return MetricsUtil.histogram(HISTOGRAM_SLICE_TAKE_COMMAND + accountTypeEnum.getCode(), () -> execCommand(finalCmdWrapper));
        } catch (Exception e) {
            log.error("BillTimeSliceCheckModule check failed with error data:{}", cmdWrapper == null ? "" : JSON.toJSONString(cmdWrapper.getCommandData()), e);
            alarmNotifyService.alarm(accountTypeEnum.getCode(), TIME_SLICE_CHECK_MODULE_TAKE_COMMAND_ERROR, accountTypeEnum.getCode());
            redisTemplate.opsForValue().set(RedisUtil.getSaveTimeSliceStatusKey(accountTypeEnum.getCode()), Boolean.FALSE.toString());
        }
        return null;
    }

    private BillCmdResult execCommand(BillCmdWrapper cmdWrapper) {
        CommonBillChangeData commonBillChangeData = (CommonBillChangeData) cmdWrapper.getCommandData();
        // 设置Partition时间
        setMaxPartitionBizTime(commonBillChangeData);
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(commonBillChangeData.getAccountType());
        // 当前时间 - partition bizTime= 时间差
        if (commonBillChangeData.getMsgType().getCode().equals(CommonBillChangeDataMsgType.UPDATE_PARTITION.getCode())) {
            return null;
        }
        // 触发是否限流
        rateLimiterManager.tryRateLimitTimeSliceFirstTimeGap(getStartTimeSliceMapFirstKey(), commonBillChangeData);
        long timeOffset = TimeSliceCalcUtils.getTimeSlice(commonBillChangeData.getBizTime().getTime(), apolloBizConfig.getTimeSliceSize());
        Long checkOkTime = lastBillTimeSliceDTO.getBillConfig().getCheckOkTime().getTime();
        if (lastBillTimeSliceDTO != null && timeOffset <= checkOkTime) {
            if (timeOffset > reboootCheckOkTime) {
                billDelayAccountService.saveDelayAccount(commonBillChangeData, apolloBizConfig);
                log.error("BillTimeSliceCheckModule updateTimeSlice failed delay message,timeOffset:{} checkOkTime:{} bizTime:{} data: {}", timeOffset, checkOkTime, DateUtil.date2str(commonBillChangeData.getBizTime()), JSONObject.toJSONString(commonBillChangeData));
                alarmNotifyService.alarm(accountTypeEnum.getCode(), ALARM_TIME_SLICE_DELAY_MSG, accountTypeEnum.getCode(), DateUtil.date2str(new Date(timeOffset)), DateUtil.date2str(new Date(checkOkTime)), DateUtil.date2str(commonBillChangeData.getBizTime()), JSON.toJSONString(commonBillChangeData));
                // 监听到延迟消息，阻止业务线存盘
                redisTemplate.opsForValue().set(RedisUtil.getSaveTimeSliceStatusKey(accountTypeEnum.getCode()), Boolean.FALSE.toString());
            }
            return null;
        }
        BillTimeSliceDTO billTimeSliceDTO = startTimeSliceDTOMap.computeIfAbsent(timeOffset, v -> {
            BillTimeSliceDTO timeSliceDTO = new BillTimeSliceDTO(BillConfig.builder()
                    .checkOkTime(new Date(timeOffset))
                    .consumeOffset(lastBillTimeSliceDTO.getBillConfig().getConsumeOffset())
                    .syncPos(lastBillTimeSliceDTO.getBillConfig().getSyncPos())
                    .build());
            timeSliceDTO.setAccountTypeEnum(accountTypeEnum);
            return timeSliceDTO;
        });
        BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountTypeEnum.getCode());
        BillConfig billConfig = billTimeSliceDTO.getBillConfig();
        Map<Integer, Long> partitionMap = PartitionOffsetDTO.transToMapFromString(billConfig.getConsumeOffset());
        partitionMap.put(commonBillChangeData.getPartition(), commonBillChangeData.getOffset());
        billConfig.setConsumeOffset(PartitionOffsetDTO.transToStringFromMap(partitionMap));
        // 更新所有的聚合流水
        billCheckService.recalculateTransferFee(commonBillChangeData, billTimeSliceDTO);
        billTimeSliceDTO.recalculateCoin(commonBillChangeData);
        billTimeSliceDTO.recalculateCoinType(commonBillChangeData);
        billTimeSliceDTO.recalculateCoinTypeUser(commonBillChangeData);
        billTimeSliceDTO.recalculateUserCoin(commonBillChangeData);
        if (accountTypeEnum.haveUserPosition()
                && StringUtils.isNotEmpty(commonBillChangeData.getSymbolId())
                && AccountTypeEnum.isContractBySymbol(commonBillChangeData.getSymbolId())
                && !commonBillChangeData.getBizType().equals(BillBizTypeConstant.BILL_INNER_TRANS_MIDDLE_EXCHANGE)) {
            billCheckService.calculateMsgTimeSliceSymbol(accountTypeEnum.getCode(), billTimeSliceDTO, commonBillChangeData);
            billCheckService.calculateMsgTimeSliceSymbolCoin(accountTypeEnum.getCode(), billTimeSliceDTO, commonBillChangeData);
            billTimeSliceDTO.recalculateUserPosition(commonBillChangeData);
        }
        if (reconSystemAccountService.isContractAdlReceivedUserId(accountTypeEnum, commonBillChangeData.getSymbolId(), commonBillChangeData.getAccountId())) {
            billTimeSliceDTO.getCommonBillChangeDataList().add(commonBillChangeData);
        }
        BizLogUtils.log(LogLevelEnum.KEY_RESULT, apolloBizConfig, "BillTimeSliceCheckModule finished process message accountType {} accountUniqueId:{}", commonBillChangeData.getAccountType(), commonBillChangeData.getBizId());
        return null;
    }

    public void setMaxPartitionBizTime(CommonBillChangeData commonBillChangeData) {
        latestPartitionBizTime.put(commonBillChangeData.getPartition(), commonBillChangeData.getBizTime().getTime());
    }

    public Long getMinPartitionBizTime() {
        ReconKafkaOpsConfig reconKafkaOpsConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode()).getReconKafkaOpsConfig();
        return latestPartitionBizTime.size() < reconKafkaOpsConfig.getKafkaPartitionNum() ? null :
                latestPartitionBizTime.values().stream().min(Comparator.comparing(x -> x)).orElse(null);
    }

    public Long getMaxPartitionBizTime() {
        return latestPartitionBizTime.values().stream().max(Comparator.comparing(x -> x)).orElse(null);
    }

    public Map<Integer, Long> getLatestPartitionBizTime() {
        return latestPartitionBizTime;
    }

    public Map<Integer, Long> getPartitionOffsetMap() {
        return partitionOffsetMap;
    }

    public SortedMap<Long, BillTimeSliceDTO> getSaveTimeSliceDTOMap() {
        return saveTimeSliceDTOMap;
    }

    public SortedMap<Long, BillTimeSliceDTO> getStartTimeSliceDTOMap() {
        return startTimeSliceDTOMap;
    }

    public void setLastBillTimeSliceDTO(BillTimeSliceDTO lastBillTimeSliceDTO) {
        this.lastBillTimeSliceDTO = lastBillTimeSliceDTO;
    }

    public void setLastSaveBillTimeSliceDTO(BillTimeSliceDTO lastSaveBillTimeSliceDTO) {
        this.lastSaveBillTimeSliceDTO = lastSaveBillTimeSliceDTO;
    }

    public BillTimeSliceDTO getLastBillTimeSliceDTO() {
        return lastBillTimeSliceDTO;
    }

    public BillTimeSliceDTO getLastSaveBillTimeSliceDTO() {
        return lastSaveBillTimeSliceDTO;
    }

    /**
     * 获取对账时间片内存队列记录数
     *
     * @return
     */
    public Long getStartTimeSliceMapQueueSize() {
        return countBillTimeSliceDTOQueueSize(startTimeSliceDTOMap);
    }

    /**
     * 获取存储时间片内存队列记录数
     *
     * @return
     */
    public Long getSaveTimeSliceDTOMapQueueSize() {
        return countBillTimeSliceDTOQueueSize(saveTimeSliceDTOMap);
    }

    /**
     * 计算时间片队列记录数
     *
     * @param timeSliceDTOMap
     * @return
     */
    private Long countBillTimeSliceDTOQueueSize(Map<Long, BillTimeSliceDTO> timeSliceDTOMap) {
        Long count = 0L;
        for (Map.Entry<Long, BillTimeSliceDTO> entry : timeSliceDTOMap.entrySet()) {
            BillTimeSliceDTO billTimeSliceDTO = entry.getValue();
            count += billTimeSliceDTO.getUniqueMap().size() + billTimeSliceDTO.getCoinPropertyMap().size()
                    + billTimeSliceDTO.getCoinUserPropertyMap().size() + billTimeSliceDTO.getCoinTypePropertyMap().size()
                    + billTimeSliceDTO.getSymbolPropertyMap().size() + billTimeSliceDTO.getSymbolCoinPropertyMap().size()
                    + billTimeSliceDTO.getBillUserPositionList().size() + billTimeSliceDTO.getBillCoinUserPropertyErrorList().size();
        }
        return count;
    }

    /**
     * 获取对账时间片内存队列第一个key
     *
     * @return
     */
    public Long getStartTimeSliceMapFirstKey() {
        try {
            return startTimeSliceDTOMap.isEmpty() ? null : startTimeSliceDTOMap.firstKey();
        } catch (Exception e) {
        }
        return null;
    }

    public void setReboootCheckOkTime(Long reboootCheckOkTime) {
        this.reboootCheckOkTime = reboootCheckOkTime;
    }
}
