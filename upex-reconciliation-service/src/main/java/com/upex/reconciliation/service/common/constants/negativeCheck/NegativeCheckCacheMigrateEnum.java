package com.upex.reconciliation.service.common.constants.negativeCheck;

public enum NegativeCheckCacheMigrateEnum {
    complete_repair,
    complete_merge,
    ;

    public static boolean containName(String name) {
        for (NegativeCheckCacheMigrateEnum e : NegativeCheckCacheMigrateEnum.values()) {
            if (e.name().equals(name)) {
                return true;
            }
        }
        return false;
    }
}
