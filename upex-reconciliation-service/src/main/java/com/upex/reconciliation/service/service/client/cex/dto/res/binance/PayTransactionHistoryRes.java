package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import com.upex.reconciliation.service.service.client.cex.client.ICexApiClient;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@Data
public class PayTransactionHistoryRes implements IBinanceApiBaseRes {

    /**
     * {
     * "code": "000000",
     * "message": "success",
     * "data": [
     * {
     * "uid": **********,
     * "counterpartyId": *********,
     * "orderId": "366325689297084416",
     * "note": "",
     * "orderType": "C2C",
     * "transactionId": "P_A1TMKW42DES71113",
     * "transactionTime": 1************,
     * "amount": "-0.000001",
     * "currency": "USDT",
     * "walletType": 1,
     * "walletTypes": [
     * "1"
     * ],
     * "fundsDetail": [
     * {
     * "currency": "USDT",
     * "amount": "0.000001",
     * "walletAssetCost": {
     * "1": "0.000001"
     * }
     * }
     * ],
     * "payerInfo": {
     * "binanceId": **********,
     * "unmaskData": false
     * },
     * "receiverInfo": {
     * "name": "Doomaxe",
     * "type": "USER",
     * "binanceId": *********,
     * "accountId": *********,
     * "unmaskData": true,
     * "extend": {
     * "phoneOrEmailChanged": false
     * }
     * },
     * "totalPaymentFee": "0"
     * }
     * ],
     * "success": true
     */
    private String code;
    private String message;
    private List<DataItem> data;
    private boolean success;

    @Data
    public static class DataItem {
        private long uid;
        private long counterpartyId;
        private String orderId;
        private String note;
        private String orderType;
        private String transactionId;
        private long transactionTime;
        private BigDecimal amount;
        private String currency;
        private int walletType;
        private List<String> walletTypes;
        private List<FundsDetailItem> fundsDetail;
        private PayerInfo payerInfo;
        private ReceiverInfo receiverInfo;
        private String totalPaymentFee;
    }

    @Data
    public static class FundsDetailItem {
        private String currency;
        private String amount;
        private Map<String, String> walletAssetCost;
    }

    @Data
    public static class PayerInfo {
        private long binanceId;
        private boolean unmaskData;
    }

    @Data
    public static class ReceiverInfo {
        private String name;
        private String type;
        private long binanceId;
        private long accountId;
        private boolean unmaskData;
        private ExtendInfo extend;
    }

    @Data
    public static class ExtendInfo {
        private boolean phoneOrEmailChanged;
    }
}



