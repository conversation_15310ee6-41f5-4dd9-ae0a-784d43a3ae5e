package com.upex.reconciliation.service.service.client.cex.config.binance;

public class BinancePathConfig {

    public static final String querySpotAccountInfo = "/api/v3/account";

    public static final String queryFundingCoinAsset = "/sapi/v1/asset/get-funding-asset";

    public static final String queryUContractAccountInfoAccount = "/fapi/v3/account";

    public static final String queryCoinContractAccountInfoAccount= "/dapi/v1/account";

    public static final String queryMarginAccountInfo = "/sapi/v1/margin/account";

    public static final String queryIsolatedMarginAccountInfo= "/sapi/v1/margin/isolated/account";

    public static final String queryFlexEarnPosition= "/sapi/v1/simple-earn/flexible/position";

    public static final String queryLockedEarnPosition = "/sapi/v1/simple-earn/locked/position";

    public static final String queryApikeyPermission = "/sapi/v1/account/apiRestrictions";

    public static final String queryUserStatus = "/sapi/v1/account/status";

    public static final String querySubUserList = "/sapi/v1/sub-account/list";

    public static final String querySpotPosition = "/sapi/v3/asset/getUserAsset";

    public static final String querySubUserSpotCoinAsset = "/sapi/v3/sub-account/assets";

    public static final String querySubUserContractAccountInfo = "/sapi/v2/sub-account/futures/account";

    public static final String querySubUserMarginAccountInfo = "/sapi/v1/sub-account/margin/account";

    public static final String querySupportCoinList = "/sapi/v1/capital/config/getall";

    public static final String queryDepositeAddress = "/sapi/v1/capital/deposit/address/list";

    public static final String queryUserDepositeHistory = "/sapi/v1/capital/deposit/hisrec";

    public static final String queryUserWithdrawHistory = "/sapi/v1/capital/withdraw/history";

    public static final String queryPayTransferHistory = "/sapi/v1/pay/transactions";

    public static final String queryParentSubTransferRecord = "/sapi/v1/sub-account/universalTransfer";

    public static final String queryUniversialTransferList = "/sapi/v1/asset/transfer";

}
