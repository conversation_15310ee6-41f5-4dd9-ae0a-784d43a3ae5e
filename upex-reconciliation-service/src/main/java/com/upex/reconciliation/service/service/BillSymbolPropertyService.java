package com.upex.reconciliation.service.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.BillCheckService;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.enums.SymbolCoinPropEnum;
import com.upex.reconciliation.service.common.constants.enums.SymbolPropEnum;
import com.upex.reconciliation.service.dao.entity.BillSymbolCoinProperty;
import com.upex.reconciliation.service.dao.entity.BillSymbolProperty;
import com.upex.reconciliation.service.dao.mapper.BillSymbolPropertyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class BillSymbolPropertyService {
    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "billSymbolPropertyMapper")
    private BillSymbolPropertyMapper billSymbolPropertyMapper;
    @Resource
    private BillSymbolCoinPropertyService billSymbolCoinPropertyService;
    @Resource
    private BillUserPositionService billUserPositionService;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;

    /**
     * 批量插入数据
     *
     * @param records
     * @param accountType
     * @param accountParam
     * @return
     */
    public int batchInsert(List<BillSymbolProperty> records, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolPropertyMapper.batchInsert(records, accountType, accountParam));
    }

    public Boolean batchDelete(Long beginId, Long pageSize, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolPropertyMapper.batchDelete(beginId, pageSize, accountType, accountParam));
    }

    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolPropertyMapper.deleteByCheckTime(accountType, accountParam, checkTime));
    }

    /**
     * 获取上期数据
     *
     * @param checkOkTime
     * @param accountType
     * @param accountParam
     * @return
     */
    public List<BillSymbolProperty> selectLastRecords(Date checkOkTime, Integer accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolPropertyMapper.selectLastRecords(checkOkTime, accountType, accountParam));
    }

    public List<BillSymbolProperty> selectListByCheckTime(Byte accountType, String accountParam, Date checkOkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolPropertyMapper.selectListByCheckTime(accountType, accountParam, checkOkTime));
    }

    public Long getIdByCheckTime(Byte accountType, String accountParam, Date checkTime, String operation) {
        String operationNew = operation;
        return dbHelper.doDbOpInReconMaster(() -> billSymbolPropertyMapper.getIdByCheckTime(accountType, accountParam, checkTime, operationNew));
    }

    public boolean deleteByMaxId(Byte accountType, String accountParam, Long maxId, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolPropertyMapper.deleteByMaxId(accountType, accountParam, maxId, batchSize));
    }

    public Boolean deleteByLtCheckTime(Byte accountType, String accountParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolPropertyMapper.deleteByLtCheckTime(accountType, accountParam, checkTime, batchSize));
    }

    public Boolean updateById(Byte accountType, String accountParam, BillSymbolProperty billSymbolProperty) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolPropertyMapper.updateById(accountType, accountParam, billSymbolProperty));
    }

    public void repairRecalculatePositionProfit(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        log.info("repairBillSymbolCoinProperty data:{}", jobParam);
        String action = jsonObject.getString("action");
        Byte accountType = jsonObject.getByte("accountType");
        String accountParam = jsonObject.getString("accountParam");
        Long checkTime = jsonObject.getLong("checkTime");
        JSONArray userIds = jsonObject.getJSONArray("userIds");
        if ("update".equals(action)) {
            if (userIds == null || userIds.size() == 0) {
                return;
            }
            BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountType);
            // 修复symbol维度
            List<BillSymbolProperty> billSymbolPropertyList = this.selectLastRecords(new Date(checkTime), Integer.valueOf(accountType), accountParam);
            for (BillSymbolProperty symbolProperty : billSymbolPropertyList) {
                billCheckService.setBillSymbolProperty(symbolProperty, SymbolPropEnum.RE_REALIZED, BigDecimal.ZERO);
                this.updateById(accountType, accountParam, symbolProperty);
            }
            // 修复coin维度
            List<BillSymbolCoinProperty> billSymbolCoinProperties = billSymbolCoinPropertyService.selectListByCheckTime(accountType, accountParam, new Date(checkTime));
            for (BillSymbolCoinProperty symbolCoinProperty : billSymbolCoinProperties) {
                billCheckService.setBillSymbolCoinProperty(symbolCoinProperty, SymbolCoinPropEnum.RE_REALIZED, BigDecimal.ZERO);
                billSymbolCoinPropertyService.updateById(accountType, accountParam, symbolCoinProperty);
            }
            // 修复用户持仓
            for (int i = 0; i < userIds.size(); i++) {
                Long userId = userIds.getLong(i);
                billUserPositionService.updateUserPositionReLAvgAndReSAvg(accountType, accountParam, new Date(checkTime), userId, BigDecimal.ZERO, BigDecimal.ZERO);
            }
        }
    }
}