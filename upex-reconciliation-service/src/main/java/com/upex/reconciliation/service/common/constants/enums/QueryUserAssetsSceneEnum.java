package com.upex.reconciliation.service.common.constants.enums;

/**
 * 获取用户资产场景枚举，兼容三方返回未来无效资产lastBillId
 */
public enum QueryUserAssetsSceneEnum {
    INIT(0, "资产初始化场景， 不过滤无效lastBillId"),
    RELOAD(1, "对账过程中重新加载资产场景， 过滤无效lastBillId");

    private Integer code;
    private String desc;

    QueryUserAssetsSceneEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static QueryUserAssetsSceneEnum toEnum(Integer code) {
        for (QueryUserAssetsSceneEnum item : QueryUserAssetsSceneEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }
}
