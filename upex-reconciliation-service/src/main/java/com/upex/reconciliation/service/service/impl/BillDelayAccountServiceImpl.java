package com.upex.reconciliation.service.service.impl;

import com.google.common.collect.Lists;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.ActiveFlagEnum;
import com.upex.reconciliation.service.dao.entity.BillDelayAccount;
import com.upex.reconciliation.service.dao.mapper.BillDelayAccountMapper;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.service.BillDelayAccountService;
import com.upex.reconciliation.service.utils.GroupByKeyUtil;
import com.upex.reconciliation.service.utils.TimeSliceCalcUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户延迟入账数据业务
 * <AUTHOR>
 */
@Slf4j
@Service
public class BillDelayAccountServiceImpl implements BillDelayAccountService {
    @Resource(name="billDelayAccountMapperWrapper")
    private BillDelayAccountMapper billDelayAccountMapperWrapper;

    @Override
    public void batchInsert(List<BillDelayAccount> list) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }

        List<List<BillDelayAccount>> partition = Lists.partition(list, BillConstants.ASSETS_INSERT_PAGE_SIZE);
        for (List<BillDelayAccount> subList : partition) {
            // 数据数据
            List<BillDelayAccount> dbBillDelayAccountList = billDelayAccountMapperWrapper.selectDelayAccountExistsList(subList);
            if(CollectionUtils.isNotEmpty(dbBillDelayAccountList)){
                Set<String> uniqueKeySet = dbBillDelayAccountList.stream().map(element -> GroupByKeyUtil.groupByBusinessLineAndUserIdAndBizId(element.getBusinessLine(), element.getUserId(), element.getBizId())).collect(Collectors.toSet());
                subList = subList.stream().filter(element -> {
                    String uniqueKey = GroupByKeyUtil.groupByBusinessLineAndUserIdAndBizId(element.getBusinessLine(), element.getUserId(), element.getBizId());
                    return !uniqueKeySet.contains(uniqueKey);
                }).collect(Collectors.toList());
            }
            billDelayAccountMapperWrapper.batchInsert(subList);
        }
    }

    @Override
    public void saveDelayAccount(CommonBillChangeData commonBillChangeData, ApolloReconciliationBizConfig apolloBizConfig) {

        BillDelayAccount billDelayAccount = new BillDelayAccount();
        long timeOffset = TimeSliceCalcUtils.getTimeSlice(commonBillChangeData.getBizTime().getTime(), apolloBizConfig.getTimeSliceSize());

        Date createTime = new Date();
        billDelayAccount.setCheckOkTime(new Date(timeOffset));
        billDelayAccount.setBusinessLine((int) commonBillChangeData.getAccountType());
        billDelayAccount.setUserId(commonBillChangeData.getAccountId());
        billDelayAccount.setBizId(commonBillChangeData.getBizId().toString());
        billDelayAccount.setSymbol(commonBillChangeData.getCoinId().toString());
        billDelayAccount.setAmount(commonBillChangeData.getChangePropSum(commonBillChangeData.getAccountType()));
        billDelayAccount.setBizTime(commonBillChangeData.getBizTime());
        billDelayAccount.setDataExt(BillConstants.EMPTY);
        billDelayAccount.setActiveFlag(ActiveFlagEnum.Active.getCode());
        billDelayAccount.setCreateTime(createTime);
        billDelayAccount.setUpdateTime(createTime);

        List<BillDelayAccount> subList = Lists.newArrayList(billDelayAccount);

        // 查询表中是否存在相同的记录
        List<BillDelayAccount> dbBillDelayAccountList = billDelayAccountMapperWrapper.selectDelayAccountExistsList(subList);
        if(CollectionUtils.isEmpty(dbBillDelayAccountList)){
            // 插入数据
            billDelayAccountMapperWrapper.batchInsert(subList);
        }
    }

    @Override
    public List<BillDelayAccount> selectHistoryDelayAccountList(Long userId, Integer activeFlag) {
        return billDelayAccountMapperWrapper.selectHistoryDelayAccountList(userId, activeFlag);
    }

    @Override
    public int updateDelayAccount(BillDelayAccount billDelayAccount) {
        return billDelayAccountMapperWrapper.updateDelayAccount(billDelayAccount);
    }

    @Override
    public int updateDelayAccountByTime(Date checkOkDate, ActiveFlagEnum activeFlagEnum) {
        return billDelayAccountMapperWrapper.updateDelayAccountByTime(checkOkDate, activeFlagEnum.getCode(), new Date());
    }

    @Override
    public Boolean deleteByLtCheckTime(Byte accountType, String accountParam, Date checkTime, Long batchSize) {
        return billDelayAccountMapperWrapper.deleteByLtCheckTime(accountType, accountParam, checkTime, batchSize);
    }
}
