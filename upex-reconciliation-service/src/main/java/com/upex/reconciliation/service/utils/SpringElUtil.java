package com.upex.reconciliation.service.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.List;
import java.util.Map;

/**
 * spring el 工具类
 */
@Slf4j
public class SpringElUtil {
    /**
     * 获取表达式值
     *
     * @param conditions
     * @param data
     * @return
     */
    public static Boolean getBooleanValue(List<String> conditions, Map<String, String> data) {
        if (data == null || data.size() == 0) {
            return false;
        }
        String pattern = getOrPattern(conditions);
        if (StringUtils.isEmpty(pattern)) {
            return false;
        }
        Expression expression = new SpelExpressionParser().parseExpression(pattern, new TemplateParserContext());
        EvaluationContext context = new StandardEvaluationContext();
        context.setVariable("data", data);
        return expression.getValue(context, Boolean.class);
    }

    /**
     * 拼接条件
     *
     * @param conditions
     * @return
     */
    private static String getOrPattern(List<String> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            return "";
        }
        StringBuilder sb = new StringBuilder("#{");
        for (int i = 0; i < conditions.size(); i++) {
            sb.append(i == 0 ? "" : " or ").append("(").append(conditions.get(i)).append(")");
        }
        sb.append("}");
        return sb.toString();
    }
}
