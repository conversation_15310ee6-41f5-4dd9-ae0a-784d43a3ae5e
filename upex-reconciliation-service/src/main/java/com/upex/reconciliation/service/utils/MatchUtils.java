package com.upex.reconciliation.service.utils;


import com.upex.reconciliation.service.common.constants.BillConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 匹配工具类
 * <AUTHOR>
 */
public class MatchUtils {
    /**
     * 业务类型相等,或者已*号结尾的,匹配
     *
     * @param list
     * @param bizType
     * @return
     */
    public static boolean checkValidate(List<String> list, String bizType) {
        return list.stream().anyMatch(str ->
                bizType != null && (bizType.equals(str) ||
                        (str.endsWith(BillConstants.ASTERISK) && bizType.startsWith(str.substring(0, str.length() - 1))))
        );

    }

    /**
     * 判断是否非空json
     *
     * @param json
     * @return
     */
    public static boolean isNotEmptyJson(String json) {
        return StringUtils.isNotBlank(json) && ((!"{}".equals(json) && json.startsWith("{")) || (!"[]".equals(json) && json.startsWith("[")));
    }

    /**
     * 判断是否空json
     *
     * @param json
     * @return
     */
    public static boolean isEmptyJson(String json) {
        return StringUtils.isBlank(json) || "{}".equals(json)  || "[]".equals(json);
    }

    /**
     * 获取匹配的业务类型
     * @param list
     * @param bizType
     * @return
     */
    public static String getMatchBizType(List<String> list, String bizType){
        return list.stream().filter(str ->
                bizType != null && (bizType.equals(str) ||
                        (str.endsWith(BillConstants.ASTERISK) && bizType.startsWith(str.substring(0, str.length() - 1))))
        ).findFirst().orElse(null);
    }
}
