package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AbstractBillCheckService;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.module.impl.BillTimeSliceCheckModule;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.alarm.CheckResult;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.*;
import com.upex.reconciliation.service.utils.NumberUtil;
import com.upex.ticker.facade.dto.PriceVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class FinancialBillCheckServiceImpl extends AbstractBillCheckService {

    @Resource
    private AlarmNotifyService alarmNotifyService;
    @Resource
    private BillEngineManager billEngineManager;

    @Override
    public Byte getAccountType() {
        return AccountTypeEnum.FINANCIAL.getCode();
    }

    @Override
    public CheckResult doCheckProperty(ApolloReconciliationBizConfig apolloBizConfig, String accountUniqueId, Long userId, Integer coinId, List<CommonBillChangeData> billChangeDataList, BillCoinUserProperty billCoinUserProperty) {
        BillSymbolCoinUserProperty billSymbolCoinUserProperty = billCoinUserProperty.getSymbolProperty().get(billChangeDataList.get(0).getSymbolId());
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        CommonBillChangeData lastChange = billChangeDataList.get(billChangeDataList.size() - 1);
        BigDecimal totalChangeProp1 = BigDecimal.ZERO;
        for (CommonBillChangeData bill : billChangeDataList) {
            totalChangeProp1 = totalChangeProp1.add(bill.getChangeProp1());
        }
        if (!lastChange.isIgnoreUserPropCheck()) {
            if (billSymbolCoinUserProperty.getProp1().add(totalChangeProp1).compareTo(lastChange.getProp1()) != 0) {
                log.error("CalculationCheckUtils.checkBillCoinProperty getProp1 accountUniqueId:{}  lastChange:{},billCoinUserProperty:{},totalChangeProp2:{}"
                        , accountUniqueId, JSONObject.toJSONString(lastChange), totalChangeProp1.toPlainString());
                return CheckResult.fail(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), lastChange.getBizTime())
                        .addUserPropCheckData(lastChange.getAccountId(), lastChange.getCoinId(), billCoinUserProperty.getProp1(), totalChangeProp1, lastChange.getProp1(), lastChange.getBizId(), "prop1");
            }
        }
        return CheckResult.DEFAULT_SUCCESS;
    }

    @Override
    public boolean checkBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty) {
        BillSymbolCoinUserProperty billSymbolCoinUserProperty = billCoinUserProperty.getSymbolProperty().get(commonBillChangeData.getSymbolId());
        if (!commonBillChangeData.isIgnoreUserPropCheck()) {
            if (commonBillChangeData.getProp1().subtract(commonBillChangeData.getChangeProp1()).compareTo(billSymbolCoinUserProperty.getProp1()) == 0) {
                return true;
            }
        } else {
            BillLogicGroup billLogicGroup = billEngineManager.getBillLogicGroup(getAccountType());
            BillTimeSliceCheckModule timeSliceModule = (BillTimeSliceCheckModule) billLogicGroup.getTimeSliceModule();
            Date initTime = timeSliceModule.getLastBillTimeSliceDTO().getBillConfig().getInitTime();
            if (commonBillChangeData.getUserCheckBizTime().getTime() > initTime.getTime()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean checkCurrentBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty) {
        BillSymbolCoinUserProperty billSymbolCoinUserProperty = billCoinUserProperty.getSymbolProperty().get(commonBillChangeData.getSymbolId());
        if (commonBillChangeData.getProp1().compareTo(billSymbolCoinUserProperty.getProp1()) == 0) {
            return true;
        }
        return false;
    }

    @Override
    public BigDecimal getPropSumByUserProperty(BillCoinUserProperty coinUserProperty) {
        Map<String, BillSymbolCoinUserProperty> symbolProperty = coinUserProperty.getSymbolProperty();
        BigDecimal totalUserCoinPropSum = BigDecimal.ZERO;
        if (symbolProperty != null && symbolProperty.size() > 0) {
            for (Map.Entry<String, BillSymbolCoinUserProperty> entry : symbolProperty.entrySet()) {
                BillSymbolCoinUserProperty billSymbolCoinUserProperty = entry.getValue();
                totalUserCoinPropSum = NumberUtil.add(totalUserCoinPropSum, billSymbolCoinUserProperty.getProp1());
            }
        }
        return totalUserCoinPropSum;
    }

    @Override
    public <T extends AbstractProperty> boolean checkAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult, ApolloReconciliationBizConfig apolloBillConfig, Map<Integer, PriceVo> ratesToUSDTCoinIdMap, Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap) {
        // 理财没有反算，直接返回true
        return true;
    }

    @Override
    public <T extends AbstractSProperty> boolean checkSpropAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult, ApolloReconciliationBizConfig apolloBillConfig, Map<Integer, PriceVo> ratesToUSDTCoinIdMap, Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap) {
        return true;
    }

    @Override
    protected UserAssetsNegativeModel doCheckNegative(BillCoinUserProperty billCoinUserProperty, BigDecimal unRealized) {
        if (billCoinUserProperty.getProp1() != null && billCoinUserProperty.getProp1().compareTo(BigDecimal.ZERO) < 0) {
            UserAssetsNegativeModel userAssetsNegativeModel = UserAssetsNegativeModel.builder()
                    .propValue(billCoinUserProperty.getProp1())
                    .build();
            return userAssetsNegativeModel;
        }
        return null;
    }

    @Override
    public boolean ignoreBillPropertyMatch(CommonBillChangeData eachBill, BillCoinUserProperty billCoinUserProperty) {
        BillLogicGroup billLogicGroup = billEngineManager.getBillLogicGroup(getAccountType());
        BillTimeSliceCheckModule timeSliceModule = (BillTimeSliceCheckModule) billLogicGroup.getTimeSliceModule();
        Date initTime = timeSliceModule.getLastBillTimeSliceDTO().getBillConfig().getInitTime();
        if (eachBill.getUserCheckBizTime().getTime() > initTime.getTime()) {
            return true;
        }
        return false;
    }

    @Override
    public BigDecimal getPropSumByProperty(AbstractProperty billCoinProperty) {
        return billCoinProperty == null ? BigDecimal.ZERO : billCoinProperty.getProp1();
    }

    @Override
    public BigDecimal getPropByTransferFee(AbstractProperty abstractProperty) {
        return abstractProperty != null ? abstractProperty.getProp1() : BigDecimal.ZERO;
    }

    @Override
    public BigDecimal getChangePropSumByProperty(AbstractProperty abstractProperty) {
        return abstractProperty.getChangeProp1();
    }

    @Override
    public BigDecimal getPropSumByBillChangeData(CommonBillChangeData currentBill) {
        return currentBill.getProp1();
    }

    @Override
    public BigDecimal getChangePropSumByBillChangeData(CommonBillChangeData currentBill) {
        return currentBill.getChangeProp1();
    }

    @Override
    public BigDecimal getChangePropSumByAbstractProperty(AbstractProperty abstractProperty) {
        return abstractProperty.getChangeProp1();
    }

    /**
     * 获取业务线类型
     *
     * @return
     */
    @Override
    public List<String> getBusinessType() {
        return Arrays.asList(AccountTypeEnum.UNIFIED.getBizTypePrefix());
    }

    @Override
    public <T extends AbstractProperty> boolean checkPosition(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult) {
        return false;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitAssets(T oldProperty, BillCoinUserProperty newBillCoinUserProperty) {
        if (oldProperty == null || newBillCoinUserProperty == null) {
            return false;
        }
        if (oldProperty.getProp2Prop3Sum().compareTo(newBillCoinUserProperty.getProp2Prop3Sum()) != 0) {
            return false;
        }
        if (oldProperty.getProp4().compareTo(newBillCoinUserProperty.getProp4()) != 0) {
            return false;
        }
        if (oldProperty.getProp5().compareTo(newBillCoinUserProperty.getProp5()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinAssets(T oldProperty, BillCoinProperty billCoinProperty) {
        if (oldProperty == null || billCoinProperty == null) {
            return false;
        }
        if (oldProperty.getProp2Prop3Sum().compareTo(billCoinProperty.getProp2Prop3Sum()) != 0) {
            return false;
        }
        if (oldProperty.getProp4().compareTo(billCoinProperty.getProp4()) != 0) {
            return false;
        }
        if (oldProperty.getProp5().compareTo(billCoinProperty.getProp5()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeAssets(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        if (oldProperty == null || newBillCoinTypeProperty == null) {
            return false;
        }
        if (oldProperty.getProp2Prop3Sum().compareTo(newBillCoinTypeProperty.getProp2Prop3Sum()) != 0) {
            return false;
        }
        if (oldProperty.getProp4().compareTo(newBillCoinTypeProperty.getProp4()) != 0) {
            return false;
        }
        if (oldProperty.getProp5().compareTo(newBillCoinTypeProperty.getProp5()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinChangeAssets(T oldProperty, BillCoinProperty billCoinProperty) {
        if (oldProperty == null || billCoinProperty == null) {
            return false;
        }
        if (oldProperty.getChangeProp1().compareTo(billCoinProperty.getChangeProp1()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp2().compareTo(billCoinProperty.getChangeProp2()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp3().compareTo(billCoinProperty.getChangeProp3()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp4().compareTo(billCoinProperty.getChangeProp4()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp5().compareTo(billCoinProperty.getChangeProp5()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeChangeAssets(T oldProperty, BillCoinTypeProperty billCoinTypeProperty) {
        if (oldProperty == null || billCoinTypeProperty == null) {
            return false;
        }
        if (oldProperty.getProp2Prop3Sum().compareTo(billCoinTypeProperty.getProp2Prop3Sum()) != 0) {
            return false;
        }
        if (oldProperty.getProp4().compareTo(billCoinTypeProperty.getProp4()) != 0) {
            return false;
        }
        if (oldProperty.getProp5().compareTo(billCoinTypeProperty.getProp5()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinAssetsByAccountType(T oldProperty, BillCoinProperty newBillCoinUserProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        if (oldProperty != null) {
            dataCalResultDTO.setFactor1(oldProperty.getProp1().add(oldProperty.getProp2()).add(oldProperty.getProp3()).add(oldProperty.getProp4()).add(oldProperty.getProp5()));
        }
        if (newBillCoinUserProperty != null) {
            dataCalResultDTO.setFactor2(newBillCoinUserProperty.getProp1().add(newBillCoinUserProperty.getProp2()).add(newBillCoinUserProperty.getProp3()).add(newBillCoinUserProperty.getProp4()).add(newBillCoinUserProperty.getProp5()));
        }
        BigDecimal tolerance = coinComparisonToleranceMap.getOrDefault(newBillCoinUserProperty.getCoinId(), BigDecimal.ZERO);
        dataCalResultDTO.setResult(dataCalResultDTO.getFactor1().subtract(dataCalResultDTO.getFactor2()).abs());
        dataCalResultDTO.setPass(dataCalResultDTO.getResult().compareTo(tolerance) <= 0);
        return dataCalResultDTO.isPass();
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeInitAssetsByAccountType(List<T> oldPropertyList, BillCoinTypeProperty newBillCoinTypeProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        if (CollectionUtils.isEmpty(oldPropertyList) || newBillCoinTypeProperty == null) {
            return false;
        }
        BigDecimal tolerance = coinComparisonToleranceMap.getOrDefault(newBillCoinTypeProperty.getCoinId(), BigDecimal.ZERO);
        BigDecimal oldSum = BigDecimal.ZERO;
        for (T odlProperty : oldPropertyList) {
            oldSum = oldSum.add(odlProperty.getProp1()).add(odlProperty.getProp2()).add(odlProperty.getProp3()).add(odlProperty.getProp4()).add(odlProperty.getProp5());
        }
        BigDecimal newSum = newBillCoinTypeProperty.getProp1().add(newBillCoinTypeProperty.getProp2()).add(newBillCoinTypeProperty.getProp3()).add(newBillCoinTypeProperty.getProp4()).add(newBillCoinTypeProperty.getProp5());
        dataCalResultDTO.setFactor1(oldSum);
        dataCalResultDTO.setFactor2(newSum);
        dataCalResultDTO.setResult(oldSum.subtract(newSum).abs());
        if (oldSum.subtract(newSum).abs().compareTo(tolerance) > 0) {
            log.info("CheckAssetSpotServiceImpl checkCoinTypeAssetsByAccountType failed , old sum {}, new sum {} , property {}", oldSum, newSum, JSONObject.toJSONString(newBillCoinTypeProperty));
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeAssetsByAccountType(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        if (oldProperty == null) {
            return Objects.isNull(newBillCoinTypeProperty) || newBillCoinTypeProperty.getChangePropSum().compareTo(BigDecimal.ZERO) == 0;
        }
        if (oldProperty.getChangeProp1().compareTo(newBillCoinTypeProperty.getChangeProp1()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp2().compareTo(newBillCoinTypeProperty.getChangeProp2()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp3().compareTo(newBillCoinTypeProperty.getChangeProp3()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp4().compareTo(newBillCoinTypeProperty.getChangeProp4()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp5().compareTo(newBillCoinTypeProperty.getChangeProp5()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> BigDecimal sumForInAll(T property) {
        if (property == null) {
            return BigDecimal.ZERO;
        }
        return property.getProp1().add(property.getProp2()).add(property.getProp3()).add(property.getProp4()).add(property.getProp5());
    }

    @Override
    public void cleanAndSetAssetsProperty(AbstractProperty abstractProperty, Byte accountType) {
        BigDecimal prop1 = abstractProperty.getProp1();
        BigDecimal changeProp1 = abstractProperty.getChangeProp1();
        abstractProperty.cleaPropAndChangeProp();
        abstractProperty.setProp1(prop1);
        abstractProperty.setChangeProp1(changeProp1);
    }
}
