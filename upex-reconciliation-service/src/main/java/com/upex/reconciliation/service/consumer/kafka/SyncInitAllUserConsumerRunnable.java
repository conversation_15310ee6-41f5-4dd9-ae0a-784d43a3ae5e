package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.google.common.util.concurrent.RateLimiter;
import com.alibaba.otter.canal.client.kafka.MessageDeserializer;
import com.alibaba.otter.canal.protocol.Message;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.model.dto.SyncBillUserDTO;
import com.upex.reconciliation.service.service.BillUserService;
import com.upex.reconciliation.service.utils.EnvUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.*;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.KAFKA_CONSUMER_TOPIC_ERROR;

/**
 * 增量用户同步
 */
@Slf4j
public class SyncInitAllUserConsumerRunnable implements KafkaConsumerLifecycle {
    private volatile boolean running = true;
    private Map<String, Object> consumerConfig;
    private String groupId;
    private Set<Integer> closeConsumerPatition = new HashSet<>();
    private AlarmNotifyService alarmNotifyService;
    private KafkaConsumer consumer;
    private ReconciliationSpringContext context;
    private BillUserService billUserService;
    private KafkaProducer<String, String> kafkaProducer;
    private String topic = KafkaTopicEnum.RECON_USER_INITALL_SYNC_TOPIC.getCode();
    private RateLimiter rateLimiter;

    public SyncInitAllUserConsumerRunnable(ReconciliationSpringContext context, String kafkaServers, String groupId, Integer maxPollSiz) {
        this.context = context;
        this.billUserService = context.getBillUserService();
        this.groupId = EnvUtil.getKafkaConsumerGroup(groupId);
        alarmNotifyService = context.getAlarmNotifyService();
        kafkaProducer = context.getKafkaProducer();
        consumerConfig = new HashMap<String, Object>();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServers);
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, this.groupId);
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollSiz);
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
    }

    @Override
    public void shutdown() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return closeConsumerPatition.size() != 1;
    }

    @Override
    public String getThreadPrefixName() {
        return "kafka-consumer-thread-sync-initall-user-info";
    }

    @Override
    public void run() {
        consumer = new KafkaConsumer<String, Message>(consumerConfig);
        consumer.subscribe(Arrays.asList(topic));
        KafkaConsumerConfig kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig(KafkaTopicEnum.RECON_USER_INCREMENT_SYNC_TOPIC);
        rateLimiter = RateLimiter.create(kafkaConsumerConfig.getMsgRateLimit());
        log.info("SyncInitAllUserConsumerRunnable run 。。。");
        while (running) {
            try {
                // 从kafka集群中拉取消息df
                ConsumerRecords<String, String> consumerRecords = consumer.poll(3000);
                List<SyncBillUserDTO> syncBillUserDTOS = new ArrayList<>();
                consumerRecords.forEach(new java.util.function.Consumer<ConsumerRecord<String, String>>() {
                    @Override
                    public void accept(ConsumerRecord<String, String> consumerRecord) {
                        if (!consumerRecord.topic().equals(topic)) {
                            alarmNotifyService.alarm(KAFKA_CONSUMER_TOPIC_ERROR, "init-alluser", topic, consumerRecord.topic(), consumerRecord.partition());
                        }
                        // log.info("接收到消息：{}", consumerRecord.value());
                        SyncBillUserDTO syncBillUserDTO = JSON.parseObject(consumerRecord.value(), SyncBillUserDTO.class);
                        syncBillUserDTOS.add(syncBillUserDTO);
                    }
                });
                for (SyncBillUserDTO syncBillUserDTO : syncBillUserDTOS) {
                    ProducerRecord<String, String> incrementRecord = new ProducerRecord<>(KafkaTopicEnum.RECON_USER_INCREMENT_SYNC_TOPIC.getCode(), syncBillUserDTO.getUserId().toString(), JSON.toJSONString(syncBillUserDTO));
                    kafkaProducer.send(incrementRecord);
                }
                consumer.commitSync();
                acquire(syncBillUserDTOS.size());
            } catch (Exception e) {
                log.error("SyncCenterUserConsumerRunnable startConsume error ", e);
            }
        }
        consumer.close();
        closeConsumerPatition.add(0);
        log.info("SyncCenterUserConsumerRunnable consumer.close success {} {}");
    }

    void acquire(int permits) {
        if(permits<=0){
            return;
        }
        double msgLimit=ReconciliationApolloConfigUtils.getKafkaConsumerConfig(KafkaTopicEnum.RECON_USER_INCREMENT_SYNC_TOPIC).getMsgRateLimit();
        if(rateLimiter==null){
            rateLimiter=RateLimiter.create(msgLimit);
        }
        if(rateLimiter.getRate()!=msgLimit){
            rateLimiter.setRate(msgLimit);
        }
        rateLimiter.acquire(permits);
    }
}


