package com.upex.reconciliation.service.common.constants.enums;


import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillTransferFeeCoinDetail;
import com.upex.reconciliation.service.model.dto.SymbolCheckProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;


@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PropEnum {
    PROP_1("prop1", "资产值1", null),
    PROP_2("prop2", "资产值2", null),
    PROP_3("prop3", "资产值3", null),
    PROP_4("prop4", "资产值4", null),
    PROP_5("prop5", "资产值5", null),
    PROP_6("prop6", "资产值6", null),
    PROP_7("prop7", "资产值7", null),
    PROP_8("prop8", "资产值8", null),

    // 币币账户
    PROP_10_1(PROP_1.getPropCode(), "余额", BillCoinUserProperty.class),
    PROP_10_2(PROP_2.getPropCode(), "锁仓", BillCoinUserProperty.class),
    PROP_10_3(PROP_3.getPropCode(), "冻结", BillCoinUserProperty.class),

    // onchain 账户
    PROP_12_1(PROP_1.getPropCode(), "余额", BillCoinUserProperty.class),
    PROP_12_2(PROP_2.getPropCode(), "冻结", BillCoinUserProperty.class),

    // 杠杆账户(逐仓)
    PROP_21_1(PROP_1.getPropCode(), "可用", BillCoinUserProperty.class),
    PROP_21_2(PROP_2.getPropCode(), "冻结", BillCoinUserProperty.class),
    PROP_21_3(PROP_3.getPropCode(), "已借", BillCoinUserProperty.class),

    // 杠杆账户(逐仓)
    PROP_22_1(PROP_1.getPropCode(), "可用", BillCoinUserProperty.class),
    PROP_22_2(PROP_2.getPropCode(), "冻结", BillCoinUserProperty.class),
    PROP_22_3(PROP_3.getPropCode(), "已借", BillCoinUserProperty.class),

    // OTC账户
    PROP_30_1(PROP_1.getPropCode(), "可用", BillCoinUserProperty.class),
    PROP_30_2(PROP_2.getPropCode(), "冻结", BillCoinUserProperty.class),

    // 合约
    PROP_5x_1(PROP_1.getPropCode(), "账户权益", BillCoinUserProperty.class),
    PROP_5x_2(PROP_2.getPropCode(), "账户余额", BillCoinUserProperty.class),
    PROP_5x_3(PROP_3.getPropCode(), "仓位保证金", BillCoinUserProperty.class),
    PROP_5x_4(PROP_4.getPropCode(), "已实现保证金币金额", BillCoinUserProperty.class),
    PROP_5x_5(PROP_5.getPropCode(), "已实现右币金额", BillCoinUserProperty.class),

    // 理财业务线
    PROP_80_1(PROP_1.getPropCode(), "理财余额", BillCoinUserProperty.class),
    PROP_80_2(PROP_2.getPropCode(), "现货资金", BillCoinUserProperty.class),
    PROP_80_3(PROP_3.getPropCode(), "利息", BillCoinUserProperty.class),

    // UNIFIED业务线
    PROP_100_1(PROP_1.getPropCode(), "权益", BillCoinUserProperty.class),
    PROP_100_2(PROP_2.getPropCode(), "余额", BillCoinUserProperty.class),
    PROP_100_3(PROP_3.getPropCode(), "冻结", BillCoinUserProperty.class),
    PROP_100_4(PROP_4.getPropCode(), "自动借款", BillCoinUserProperty.class),
    PROP_100_5(PROP_5.getPropCode(), "手工借款", BillCoinUserProperty.class),
    PROP_100_6(PROP_6.getPropCode(), "已实现保证金币金额", BillCoinUserProperty.class),
    PROP_100_7(PROP_7.getPropCode(), "已实现右币金额", BillCoinUserProperty.class),

    // 合约BillSymbolProperty 字段描述  SymbolPropEnum
    SYMBOL_PROP_5x_1(PROP_1.getPropCode(), "多仓数量", SymbolPropEnum.class),
    SYMBOL_PROP_5x_2(PROP_2.getPropCode(), "空仓数量", SymbolPropEnum.class),
    SYMBOL_PROP_5x_3(PROP_3.getPropCode(), "未实现", SymbolPropEnum.class),
    SYMBOL_PROP_5x_4(PROP_4.getPropCode(), "已实现保证金币金额", SymbolPropEnum.class),
    SYMBOL_PROP_5x_5(PROP_5.getPropCode(), "已实现右币金额", SymbolPropEnum.class),
    SYMBOL_PROP_5x_6(PROP_6.getPropCode(), "初始值", SymbolPropEnum.class),
    SYMBOL_PROP_5x_7(PROP_7.getPropCode(), "重算已实现右币金额(排除重算用户)", SymbolPropEnum.class),
    SYMBOL_PROP_5x_8(PROP_8.getPropCode(), "重算未实现", SymbolPropEnum.class),

    // 统一账户BillSymbolProperty 字段描述
    SYMBOL_PROP_100_1(PROP_1.getPropCode(), "重算已实现右币金额(排除重算用户)", SymbolPropEnum.class),
    SYMBOL_PROP_100_2(PROP_2.getPropCode(), "重算未实现", SymbolPropEnum.class),
    SYMBOL_PROP_100_3(PROP_3.getPropCode(), "多仓数量", SymbolPropEnum.class),
    SYMBOL_PROP_100_4(PROP_4.getPropCode(), "空仓数量", SymbolPropEnum.class),
    SYMBOL_PROP_100_5(PROP_5.getPropCode(), "未实现", SymbolPropEnum.class),
    SYMBOL_PROP_100_6(PROP_6.getPropCode(), "已实现保证金币金额", SymbolPropEnum.class),
    SYMBOL_PROP_100_7(PROP_7.getPropCode(), "已实现右币金额", SymbolPropEnum.class),
    SYMBOL_PROP_100_8(PROP_8.getPropCode(), "初始值", SymbolPropEnum.class),

    // 合约BillSymbolCoinProperty 字段描述 SymbolCoinPropEnum
    SYMBOL_COIN_PROP_5x_3(PROP_4.getPropCode(), "未实现保证金币金额（币本位独有）", SymbolCoinPropEnum.class),
    SYMBOL_COIN_PROP_5x_4(PROP_4.getPropCode(), "已实现保证金币金额", SymbolCoinPropEnum.class),
    SYMBOL_COIN_PROP_5x_5(PROP_5.getPropCode(), "已实现右币金额", SymbolCoinPropEnum.class),
    SYMBOL_COIN_PROP_5x_6(PROP_6.getPropCode(), "重算已实现右币金额(排除重算用户)", SymbolCoinPropEnum.class),

    // 统一账户 BillSymbolProperty 字段描述 SymbolCoinPropEnum
    SYMBOL_COIN_PROP_100_5(PROP_5.getPropCode(), "未实现保证金币金额（币本位独有）", SymbolCoinPropEnum.class),
    SYMBOL_COIN_PROP_100_6(PROP_6.getPropCode(), "已实现保证金币金额", SymbolCoinPropEnum.class),
    SYMBOL_COIN_PROP_100_7(PROP_7.getPropCode(), "已实现右币金额", SymbolCoinPropEnum.class),
    SYMBOL_COIN_PROP_100_8(PROP_8.getPropCode(), "重算已实现右币金额(排除重算用户)", SymbolCoinPropEnum.class),

    // 统一账户 BillTransferFeeCoinDetail 字段描述
    TRANSFER_FEE_PROP_1(PROP_1.getPropCode(), "手续费应收（正值）", BillTransferFeeCoinDetail.class),
    TRANSFER_FEE_PROP_2(PROP_2.getPropCode(), "手续费实收（正值）", BillTransferFeeCoinDetail.class),
    TRANSFER_FEE_PROP_3(PROP_3.getPropCode(), "业务线入账手续费（正值）（业务上线时有值）", BillTransferFeeCoinDetail.class),
    TRANSFER_FEE_PROP_4(PROP_4.getPropCode(), "bgb抵扣应收（正值）", BillTransferFeeCoinDetail.class),

    // 统一账户 SymbolCheckProperty 字段描述
    SYMBOL_CHECK_PROP_1(PROP_1.getPropCode(), "多仓数量", SymbolCheckProperty.class),
    SYMBOL_CHECK_PROP_2(PROP_2.getPropCode(), "空仓数量", SymbolCheckProperty.class),
    SYMBOL_CHECK_PROP_3(PROP_3.getPropCode(), "已实现", SymbolCheckProperty.class),
    SYMBOL_CHECK_PROP_4(PROP_4.getPropCode(), "未实现", SymbolCheckProperty.class),
    SYMBOL_CHECK_PROP_5(PROP_5.getPropCode(), "已实现初始值", SymbolCheckProperty.class),
    SYMBOL_CHECK_PROP_6(PROP_6.getPropCode(), "mPrice", SymbolCheckProperty.class),
    TRANSFER_FEE_PROP_7(PROP_7.getPropCode(), "重算已实现", SymbolCheckProperty.class),
    TRANSFER_FEE_PROP_8(PROP_8.getPropCode(), "重算未实现", SymbolCheckProperty.class),
    ;

    private String propCode;
    private String propName;
    private Class<? extends Object> clazz;

    public static PropEnum toEnum(String code) {
        for (PropEnum item : PropEnum.values()) {
            if (item.propCode.equals(code)) {
                return item;
            }
        }
        return null;
    }
}
