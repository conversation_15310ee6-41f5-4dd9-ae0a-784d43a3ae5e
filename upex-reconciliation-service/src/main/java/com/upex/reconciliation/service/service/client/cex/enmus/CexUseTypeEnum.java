package com.upex.reconciliation.service.service.client.cex.enmus;

import com.alibaba.google.common.collect.Lists;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.EnumValueRes;
import lombok.Getter;

import java.util.List;
@Getter
public enum CexUseTypeEnum {
    Market_Making(1, "做市"),
    Brick_Moving(2, "搬砖"),
    Financial_Investment(3, "理财"),
    Trading_Hedging(4, "交易对冲"),
    OTHER(5, "其他"),;
    private Integer type;
    private String name;
    CexUseTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }
    public static CexUseTypeEnum fromType(Integer type) {
        for (CexUseTypeEnum value : CexUseTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value;
            }
        }
        return null;
    }

    public static List<EnumValueRes> getList(){
        List<EnumValueRes> enumValueResList= Lists.newArrayList();
        for(CexUseTypeEnum value: CexUseTypeEnum.values()){
            enumValueResList.add(new EnumValueRes(value.type,value.name));
        }
        return enumValueResList;
    }

}
