package com.upex.reconciliation.service.common.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/6/19 16:22
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ProfitSourceTypeEnum {
    /**
     * 币种盈亏
     */
    BILL_CHECK(0, "对账数据"),

    /**
     * 币对盈亏
     */
    BILL_RESET(1, "对账回退"),

    /**
     * 对账合约系统手续费
     */
    BILL_CONTRACT_FEE(2, "对账合约系统手续费"),
    /**
     * 对账现货系统手续费
     */
    BILL_SPOT_FEE(3, "对账现货系统手续费"),

    /**
     * 对账杠杆系统手续费
     */
    BILL_LEVER_FEE(4, "对账杠杆系统手续费"),
    /**
     * 对账合约系统利息
     */
    BILL_CONTRACT_COST(5, "对账合约系统利息"),
    BILL_UTA_SPOT_FEE(6, "统一账户现货手续费"),
    BILL_UTA_CONTRACT_FEE(7, "统一账户合约手续费"),
    ;
    private int code;
    private String desc;


    /**
     * 类型转枚举
     *
     * @param code
     * @return
     */
    public static ProfitSourceTypeEnum toEnum(int code) {
        for (ProfitSourceTypeEnum item : ProfitSourceTypeEnum.values()) {
            if (item.code == code) {
                return item;
            }
        }
        return null;
    }

    /**
     * 获取source枚举
     *
     * @param accountType
     * @param profitType
     * @return
     */
    public static ProfitSourceTypeEnum getByProfitTypeEnum(Byte accountType, String profitType) {
        ProfitTypeEnum profitTypeEnum = ProfitTypeEnum.toEnum(profitType);
        if (profitTypeEnum == ProfitTypeEnum.COIN_PROFIT) {
            return BILL_CHECK;
        } else if (profitTypeEnum == ProfitTypeEnum.SYMBOL_PROFIT) {
            return BILL_CHECK;
        } else if (profitTypeEnum == ProfitTypeEnum.MIX_CONTRACT_SYSTEM_DEAL_FEE) {
            return BILL_CONTRACT_FEE;
        } else if (profitTypeEnum == ProfitTypeEnum.SPOT_DEAL_SYSTEM_DEAL_FEE) {
            return BILL_SPOT_FEE;
        } else if (profitTypeEnum == ProfitTypeEnum.LEVER_DEAL_SYSTEM_DEAL_FEE) {
            return BILL_LEVER_FEE;
        } else if (profitTypeEnum == ProfitTypeEnum.MIX_CONTRACT_SYSTEM_DEAL_COST_INTEREST) {
            return BILL_CONTRACT_COST;
        } else if (profitTypeEnum == ProfitTypeEnum.UTA_SPOT_DEAL_FEE) {
            return BILL_UTA_SPOT_FEE;
        } else if (profitTypeEnum == ProfitTypeEnum.UTA_CONTRACT_DEAL_FEE) {
            return BILL_UTA_CONTRACT_FEE;
        }
        throw new RuntimeException("获取类型错误，请检查配置！");
    }

    /**
     * 获取回退枚举类型
     *
     * @return
     */
    public static ProfitSourceTypeEnum[] getRollBackSourceTypeEnums() {
        return new ProfitSourceTypeEnum[]{BILL_CHECK, BILL_CONTRACT_FEE, BILL_SPOT_FEE, BILL_LEVER_FEE, BILL_CONTRACT_COST, BILL_UTA_SPOT_FEE, BILL_UTA_CONTRACT_FEE};
    }
}
