package com.upex.reconciliation.service.business.cex;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.model.config.ApolloThirdCexAssetConfig;
import com.upex.reconciliation.service.service.CexUserConfigService;
import com.upex.reconciliation.service.service.ThirdCexUserService;
import com.upex.reconciliation.service.service.client.cex.config.binance.SensitiveFieldFilter;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.*;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.PageData;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.ThirdCexUserInnerRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.user.CexSubUserListRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.user.CexUserInfoInnerRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.user.CommonUserStatusRes;
import com.upex.reconciliation.service.service.client.cex.enmus.*;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CexUserBizService {

    @Resource
    CexApiService cexApiService;

    @Resource
    ThirdCexUserService thirdCexUserService;

    @Resource
    CexUserConfigService cexUserConfigService;

    @Resource
    BillDbHelper billDbHelper;


    public int updateUserApiKeyStatus(ThirdCexUser cexUser, ThirdCexUserConfig userConfig) {
        return billDbHelper.doDbOpInReconMasterTransaction(() -> {
            cexUser.setApiKeyStatus(ApiKeyStatusEnum.INVALID.getType());
            cexUser.setUpdateTime(new Date());
            thirdCexUserService.update(cexUser);
            thirdCexUserService.updateSubUserApikeyStatus(cexUser.getCexUserId(), ApiKeyStatusEnum.INVALID.getType());
            userConfig.setStatus(ApiKeyStatusEnum.INVALID.getType());
            userConfig.setUpdateTime(new Date());
//                    userConfig.setReadOnly(ReadOnlyEnum.NOT_READ_ONLY.getType());
            return cexUserConfigService.update(userConfig);
        });
    }


    public List<ThirdCexUser> querySubUserList(CexUserListRequest request) {
        List<ThirdCexUser> users = thirdCexUserService.selectSubUser(request);
        return users;
    }

    public List<ThirdCexUserInnerRes> queryParentUserList(Integer cexType) {
        List<ThirdCexUser> users = thirdCexUserService.selectAllParentUserByCexType(cexType);
        return users.stream().map(user -> {
            ThirdCexUserInnerRes res = new ThirdCexUserInnerRes(user.getCexEmail(), user.getCexUserId());
            return res;
        }).collect(Collectors.toList());

    }

    public int modUserStatus(UserManagerStatusModReq userManagerStatusModReq) {
        ThirdCexUser thirdCexUser = thirdCexUserService.selectByCexTypeAndUserId(userManagerStatusModReq.getCexType(), userManagerStatusModReq.getCexUserId());
        if (thirdCexUser == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_NOT_EXISTS);
        }
        thirdCexUser.setUserManageStatus(userManagerStatusModReq.getStatus().getType());
        thirdCexUser.setUpdateTime(new Date());
        return thirdCexUserService.update(thirdCexUser);
    }

    public CommonRes<CommonUserStatusRes> recoverUserStatus(CommonReq commonReq) {
        CommonRes commonRes = cexApiService.queryUserStatus(commonReq);
        if (commonRes.getSuccess()) {
            CommonUserStatusRes commonUserStatusRes = (CommonUserStatusRes) commonRes.getData();
            if (commonUserStatusRes.getStatus() == CexUserStatusEnum.NORMAL.getType()) {
                ThirdCexUser user = thirdCexUserService.selectByCexTypeAndUserId(commonReq.getCexType(), commonReq.getCexUserId());
                user.setCexUserStatus(CexUserStatusEnum.NORMAL.getType());
                user.setUpdateTime(new Date());
                thirdCexUserService.update(user);
            }
        }
        return commonRes;
    }


    public List<ThirdCexUser> getCexUsers(CexUserPageListRequest request) {
        // 调用服务层查询逻辑
        List<ThirdCexUser> result = thirdCexUserService.getUsersByPage(request);
//        CexUserInfoInnerRes resPageData = new PageData<>();
//        if (result != null && CollectionUtils.isNotEmpty(result.getItems())) {
//            List<CexUserInfoInnerRes> resList = new ArrayList<>();
//            result.getItems().forEach(user -> {
//                CexUserInfoInnerRes cexUserInfoInnerRes = new CexUserInfoInnerRes();
//                BeanUtils.copyProperties(user, cexUserInfoInnerRes);
//                resList.add(cexUserInfoInnerRes);
//            });
//            resPageData.setItems(resList);
//            resPageData.setTotal(result.getTotal());
//        }
        return result;
    }

    public CommonRes<CexUserInfoInnerRes> getCexUserDetail(CommonReq commonReq) {
        ThirdCexUser user = thirdCexUserService.selectByCexTypeAndUserId(commonReq.getCexType(), commonReq.getCexUserId());
        CexUserInfoInnerRes res = new CexUserInfoInnerRes();
        BeanUtils.copyProperties(user, res);
        if (user.getParentUserId() == null) {
            //当前用户是母账户
            List<ThirdCexUser> subUsers = thirdCexUserService.selectSubUserByCexTypeAndParentUserId(commonReq.getCexType(), user.getCexUserId());
            if (CollectionUtils.isNotEmpty(subUsers)) {
                res.setSubUserList(subUsers.stream().map(subUser -> {
                    ThirdCexUserInnerRes subUserRes = new ThirdCexUserInnerRes(subUser.getCexEmail(), subUser.getCexUserId());
                    return subUserRes;
                }).collect(Collectors.toList()));
            }
        } else {
            //当前用户是子账户
            ThirdCexUser parentUser = thirdCexUserService.selectByCexTypeAndUserId(commonReq.getCexType(), user.getParentUserId());
            res.setParentEmail(parentUser.getCexEmail());
        }
        return CommonRes.getSucApiBaseRes(res);
    }


    public CommonRes syncSubUser(Integer cexType, String parentCexUserId) {
        log.info("BeginSyncSubUser,cexType:{},parentUserId:{}", cexType, parentCexUserId);
        ThirdCexUser parentUser = thirdCexUserService.selectByCexTypeAndUserId(cexType, parentCexUserId);
        if (parentUser == null) {
            throw new ApiException(ReconCexExceptionEnum.PARENT_USER_NOT_EXISTS);
        }
        CommonRes<CexSubUserListRes> subUserListResCommonRes = cexApiService.querySubUserList(new CommonReq(cexType, parentCexUserId));
        if (!subUserListResCommonRes.getSuccess()) {
            log.warn("查询子用户列表失败");
            return CommonRes.getFailApiBaseRes(ReconCexExceptionEnum.QUERY_SUB_USER_FAIL);
        }
        List<CexUserInfoInnerRes> subUserList = subUserListResCommonRes.getData();
        if (CollectionUtils.isEmpty(subUserList)) {
            log.warn("查询子用户列表为空");
            return CommonRes.getSucApiBaseRes("该母账户下不存在子用户");
        }
        log.info("QuerySyncSubUserRes,cexType:{},parentUserId:{},size:{}", cexType, parentCexUserId, subUserList.size());
        //查询当已同步的子用户
        List<ThirdCexUser> syncedThirdCexUsers = thirdCexUserService.selectSubUserByCexTypeAndParentUserId(cexType, parentUser.getCexUserId());
        List<ThirdCexUser> subThirdUsers = new ArrayList<>();
        for (CexUserInfoInnerRes subUser : subUserList) {
            ThirdCexUser subThirdUser = new ThirdCexUser();
            subThirdUser.setCexType(cexType);
            subThirdUser.setCexEmail(subUser.getCexEmail());
            subThirdUser.setCexUserId(String.valueOf(subUser.getCexUserId()));
            subThirdUser.setParentUserId(parentUser.getCexUserId());
            subThirdUser.setUserManageStatus(UserManagerStatusEnum.DISABLE.getType());
            subThirdUser.setCexUserStatus(subUser.getCexUserStatus());
            subThirdUser.setApiKeyStatus(parentUser.getApiKeyStatus());
            subThirdUser.setUserManagerEmail(parentUser.getUserManagerEmail());
            subThirdUser.setUserManagerId(parentUser.getUserManagerId());
            subThirdUser.setCreateTime(new Date());
            subThirdUser.setUpdateTime(new Date());
            subThirdUsers.add(subThirdUser);
        }
        if (CollectionUtils.isNotEmpty(syncedThirdCexUsers)) {
            for (ThirdCexUser syncedThirdCexUser : syncedThirdCexUsers) {
                subThirdUsers.stream().filter(subThirdUser -> subThirdUser.getCexUserId().equals(syncedThirdCexUser.getCexUserId())).findFirst().ifPresent(subThirdUser -> {
                            subThirdUser.setUserType(syncedThirdCexUser.getUserType());
                            subThirdUser.setUseType(syncedThirdCexUser.getUseType());
                            subThirdUser.setTradeType(syncedThirdCexUser.getTradeType());
                            subThirdUser.setId(syncedThirdCexUser.getId());
                        }
                );
            }
        }
        log.info("FinshSubUserInfoSet,cexType:{},parentUserId:{},result:{}", cexType, parentUser.getCexUserId());
        ApolloThirdCexAssetConfig apolloThirdCexAssetConfig = ReconciliationApolloConfigUtils.getThirdCexAssetConfig();
        List<List<ThirdCexUser>> subThirdUsersList = Lists.partition(subThirdUsers, apolloThirdCexAssetConfig.getSqlInsertSize());
        thirdCexUserService.deleteSubUserByCexTypeAndParentUserId(cexType, parentUser.getCexUserId());
        log.info("DeleteExistSubUser,cexType:{},parentUserId:{},result:{}", cexType, parentUser.getCexUserId());
        for (List<ThirdCexUser> subThirdUsersTemp : subThirdUsersList) {
            thirdCexUserService.batchInsertSubUser(subThirdUsersTemp);
        }
        log.info("InsertSubUser,cexType:{},parentUserId:{},size:{}", cexType, parentUser.getCexUserId(), subThirdUsers.size());
        return CommonRes.getSucApiBaseRes("所有子账户均已同步");
    }

    public int addParentUser(ModCexUserRequest req) {
        ThirdCexUser user = new ThirdCexUser();
        user.setUseType(req.getUseType());
        user.setCexUserId(String.valueOf(req.getCexUserId()));
        user.setCexEmail(req.getCexEmail());
        user.setUserType(CexUserTypeEnum.PARENT_USER.getType());
        user.setCexType(req.getCexType());
        user.setApiKeyStatus(ApiKeyStatusEnum.NOT_CONFIG.getType());
        user.setTradeType(req.getTradeType());
        user.setUserManagerEmail(req.getUserManagerEmail());
        user.setUserManagerId(req.getUserManagerId());
        user.setUserManageStatus(UserManagerStatusEnum.DISABLE.getType());
        user.setCexUserStatus(CexUserStatusEnum.NORMAL.getType());
        user.setUseType(req.getUseType());
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        return thirdCexUserService.add(user);
    }

    public int modUser(ModCexUserRequest req) {
        ThirdCexUser oldUser = thirdCexUserService.selectById(req.getId());
        if (oldUser == null) {
            throw new ApiException(ReconCexExceptionEnum.USER_NOT_EXISTS);
        }
        if (!req.getCexUserId().equals(oldUser.getCexUserId())) {
            throw new ApiException(ReconCexExceptionEnum.USERID_CANNOT_MOD);
        }
        if (oldUser.getParentUserId() != null) {
            //当前用户是子用户
            ThirdCexUser parentUser = thirdCexUserService.selectByCexTypeAndUserId(oldUser.getCexType(), oldUser.getParentUserId());
            if (parentUser == null) {
                throw new ApiException(ReconCexExceptionEnum.PARENT_USER_NOT_EXISTS);
            }
            if (!req.getCexEmail().equals(oldUser.getCexEmail())) {
                throw new ApiException(ReconCexExceptionEnum.EMAIL_CANNOT_MOD);
            }
        } else {
            //当前用户是母用户
            if (!req.getCexEmail().equals(oldUser.getCexEmail())) {
                List<ThirdCexUser> parentUsers = thirdCexUserService.selectAllParentUserByCexType(req.getCexType());
                ThirdCexUser existUser = parentUsers.stream().filter(parentUserTemp -> parentUserTemp.getCexUserId().equals(req.getCexUserId()) || parentUserTemp.getCexEmail().equals(req.getCexEmail())).findFirst().orElse(null);
                if (existUser != null) {
                    throw new ApiException(ReconCexExceptionEnum.USER_EXISTS);
                }
            }
        }
        if (!req.getCexType().equals(oldUser.getCexType())) {
            throw new ApiException(ReconCexExceptionEnum.CEX_TYPE_CANNOT_MOD);
        }
        ThirdCexUser parentUser = null;
        if (req.getParentUserId() != null) {
            parentUser = thirdCexUserService.selectByCexTypeAndUserId(req.getCexType(), req.getParentUserId());
            if (parentUser == null) {
                throw new ApiException(ReconCexExceptionEnum.USER_NO_PARENT_USER);
            }
            if (!parentUser.getUserManagerId().equals(oldUser.getUserManagerId())) {
                throw new ApiException(ReconCexExceptionEnum.SUBUSER_MANAGER_CANNOT_MOD);
            }
        } else {
            if (req.getUserType() == null) {
                throw new ApiException(ReconCexExceptionEnum.ILLEGAL_USERTYPE);
            }
            if (!req.getUserType().equals(CexUserTypeEnum.PARENT_USER.getType())) {
                throw new ApiException(ReconCexExceptionEnum.PARENT_USER_TYPE_CANNOT_MOD);
            }
            if (req.getUserManagerId() == null) {
                throw new ApiException(ReconCexExceptionEnum.USER_MANAGER_ID_CANNOT_BENULL);
            }
        }
        oldUser.setId(oldUser.getId());
        oldUser.setUseType(req.getUseType());
        oldUser.setTradeType(req.getTradeType());
        oldUser.setCexUserId(req.getCexUserId());
        oldUser.setCexEmail(req.getCexEmail());
        oldUser.setUserManagerEmail(req.getUserManagerEmail());
        oldUser.setUserManagerId(req.getUserManagerId());
        oldUser.setUpdateTime(new Date());
        oldUser.setUserKyb(req.getUserKyb());
        oldUser.setUserType(req.getUserType());
        return thirdCexUserService.update(oldUser);
    }


    public void updateUser(CommonRes permissionres, ThirdCexUser user, AddApiKeyReq request) {
        if (permissionres.getSuccess()) {
            user.setApiKeyStatus(ApiKeyStatusEnum.EFFECT.getType());
        } else {
            user.setApiKeyStatus(ApiKeyStatusEnum.INVALID.getType());
        }
        CommonRes<CommonUserStatusRes> userStatusRes = cexApiService.queryUserStatus(new CommonReq(request.getCexType(), request.getCexUserId()));
        user.setCexUserStatus(userStatusRes.getData().getStatus());
        user.setUpdateTime(new Date());
        thirdCexUserService.update(user);
        log.info("UpdateUser,cexType:{},userId:{}", request.getCexType(), request.getCexUserId());
    }
}
