package com.upex.reconciliation.service.service.client.cex.dto.req.common;

import com.upex.reconciliation.service.service.client.cex.enmus.IReconCexErrorCode;
import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import lombok.Data;

import javax.validation.constraints.NotNull;

import static com.upex.reconciliation.service.service.client.cex.enmus.IReconCexErrorCode.APIKEY_PUB_SECRET_CANNOT_BENULL;

@Data
public class QueryApiKeyPermissionReq extends CommonReq {
    @NotNull (message = IReconCexErrorCode.APIKEY_LABEL_CANNOT_BENULL)
    private String apiKeyLabel;
    @NotNull(message = IReconCexErrorCode.APIKEY_CANNOT_BENULL)
    private String apiKey;

    @NotNull(message = APIKEY_PUB_SECRET_CANNOT_BENULL)
    private String apiKeyPub;

}
