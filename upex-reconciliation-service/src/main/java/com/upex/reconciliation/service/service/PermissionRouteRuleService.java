package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.model.config.UserPermissionConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import org.apache.commons.lang3.tuple.Pair;

/**
 * 权限路由服务接口
 *
 * <AUTHOR>
 * @Date 2025/1/26
 */
public interface PermissionRouteRuleService {

    /**
     * 路由选择
     *
     * @param billChangeData
     * @param userPermissionConfig
     * @return
     */
    Pair<Boolean, String> isSelect(CommonBillChangeData billChangeData, UserPermissionConfig userPermissionConfig);
}
