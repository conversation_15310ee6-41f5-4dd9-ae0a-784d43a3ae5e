package com.upex.reconciliation.service.business;

import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface TransferService {
    /**
     * 动账接口
     *
     * @param anEnum
     * @param item
     * @param transferTime
     * @param allCoinsMap
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2023/8/15 15:08
     */
    Boolean transfer(AccountTypeEnum anEnum, BillContractProfitTransfer item, Date transferTime, Map<Integer, String> allCoinsMap);
}
