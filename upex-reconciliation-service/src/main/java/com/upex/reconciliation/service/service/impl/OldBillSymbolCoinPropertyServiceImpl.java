package com.upex.reconciliation.service.service.impl;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.service.OldBillSymbolCoinPropertyService;
import com.upex.reconciliation.service.dao.entity.BillSymbolCoinProperty;
import com.upex.reconciliation.service.dao.mapper.OldBillSymbolCoinPropertyMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class OldBillSymbolCoinPropertyServiceImpl implements OldBillSymbolCoinPropertyService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "oldBillSymbolCoinPropertyMapper")
    private OldBillSymbolCoinPropertyMapper oldBillSymbolCoinPropertyMapper;


    @Override
    public List<BillSymbolCoinProperty> selectListByCheckTime(Byte accountType, String accountParam, Date checkOkTime) {
        return dbHelper.doDbOpInBillMaster(() -> oldBillSymbolCoinPropertyMapper.selectListByCheckTime(accountType, accountParam, checkOkTime));
    }
}
