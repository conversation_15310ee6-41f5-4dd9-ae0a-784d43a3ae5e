package com.upex.reconciliation.service.service.client.cex.dto.req.common;


import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.service.client.cex.enmus.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 查询 CEX 用户列表的请求参数
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CexUserListRequest {


    /**
     * 交易所类型（1: Binance, 2: OKX）
     */
    @NotNull(message = IReconCexErrorCode.ILLEGAL_CEXTYPE)
    private Integer cexType;
    /**
     * 账户用途（1: 资金监控, 2: 搬砖）
     */
    private Integer useType;

    /**
     * 用户状态（0: 未启用, 1: 启用, 2: 启用中）
     */
    private Integer userManagerStatus;

    private Integer cexUserStatus;

    /**
     * 用户管理人邮箱
     */
    private String userManagerEmail;

    private String userManagerId;

    private String cexUserId;


    public void setUseType(Integer useType) {
        if(useType!=null) {
            CexUseTypeEnum cexUseTypeEnum = CexUseTypeEnum.fromType(useType);
            if (cexUseTypeEnum == null) {
                throw new ApiException(ReconCexExceptionEnum.ILLEGAL_USETYPE);
            }
        }
        this.useType = useType;
    }



    public void setCexType(Integer cexType) {
        CexTypeEnum cexTypeEnum = CexTypeEnum.fromType(cexType);
        if (cexTypeEnum == null) {
            throw new ApiException(ReconCexExceptionEnum.ILLEGAL_CEXTYPE);
        }
        this.cexType = cexType;
    }

    public void setUserManagerStatus(Integer userManagerStatus) {
        if(userManagerStatus!=null) {
            CexUserStatusEnum cexUserStatusEnum = CexUserStatusEnum.fromType(userManagerStatus);
            if (cexUserStatusEnum == null) {
                throw new ApiException(ReconCexExceptionEnum.ILLEGAL_USERMANAGERSTATUS);
            }
        }
        this.userManagerStatus = userManagerStatus;
    }

    public void setCexUserStatus(Integer cexUserStatus) {
        if(cexUserStatus!=null) {
            CexUserStatusEnum cexUserStatusEnum = CexUserStatusEnum.fromType(cexUserStatus);
            if (cexUserStatusEnum == null) {
                throw new ApiException(ReconCexExceptionEnum.ILLEGAL_CEXUSERSTATUS);
            }
        }
        this.cexUserStatus = cexUserStatus;
    }
}
