package com.upex.reconciliation.service.business;

import com.upex.mixcontract.process.facade.dto.MixAccountAssetsExtension;
import com.upex.reconciliation.facade.params.SysAssetsParams;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/7/5 下午7:01
 * @Description 增加系统资产查询和用户资产查询中混合合约（usdt，usd），其需要单独计算，需要扩展策略重新计算账户权益
 */
public interface MixBillAssetsService {

    /**
     * 为管理系统提供并计算混合合约资产（包括平台总资产、系统总资产等）
     * @param userId
     * @param sysAssetsParams
     * @param paramTime
     * @param apolloBillConfig
     * @return
     */
    List<BillCoinUserProperty> calAndGetMixBillAssets(Long userId, SysAssetsParams sysAssetsParams , Date paramTime, ApolloReconciliationBizConfig apolloBillConfig);




    List<BillCoinUserProperty> queryBillAssetsWithMix(Long userId, Integer accountType, String accountParam, Date paramTime, MixAccountAssetsExtension extension, Set<Integer> hasPositionCoinList, Boolean isMaster);

}
