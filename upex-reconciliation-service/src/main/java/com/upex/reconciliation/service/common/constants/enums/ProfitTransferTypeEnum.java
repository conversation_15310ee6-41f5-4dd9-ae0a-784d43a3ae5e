package com.upex.reconciliation.service.common.constants.enums;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;

/**
 * 利润类型枚举
 */
public enum ProfitTransferTypeEnum {

    /**
     * 币种盈亏
     */
    COIN_PROFIT(0, "币种盈亏"),
    /**
     * 币对盈亏
     */
    SYMBOL_PROFIT(1, "币对盈亏"),
    /**
     * 系统回退
     */
    SYSTEM_RESET(2, "系统回退"),

    /**
     * 对账合约系统手续费
     */
    SYSTEM_CONTRACT_FEE(3, "对账合约系统手续费"),
    /**
     * 币对盈亏回退
     */
    SYMBOL_PROFIT_SYSTEM_RESET(4, "币对-币种盈亏回退"),
    /**
     * 对账现货系统手续费
     */
    SYSTEM_SPOT_FEE(5, "对账现货系统手续费"),
    /**
     * 对账现货系统手续费回退
     */
    SYSTEM_SPOT_FEE_RESET(6, "对账现货系统手续费回退"),
    /**
     * 对账杠杆系统手续费
     */
    SYSTEM_LEVER_FEE(7, "对账杠杆系统手续费"),

    /**
     * 对账杠杆系统手续费回退
     */
    SYSTEM_LEVER_FEE_RESET(8, "对账杠杆系统手续费回退"),
    /**
     * 对账合约系统利息
     */
    SYSTEM_CONTRACT_COST_INTEREST(9, "对账合约系统利息"),

    /**
     * 对账合约系统利息回退
     */
    SYSTEM_CONTRACT_COST_INTEREST_RESET(10, "对账合约系统利息回退"),
    SYSTEM_UTA_SPOT_FEE(11, "统一账户现货手续费入账"),
    SYSTEM_UTA_SPOT_FEE_RESET(12, "统一账户现货手续费出账"),
    SYSTEM_UTA_CONTRACT_FEE(13, "统一账户合约手续费入账"),
    SYSTEM_UTA_CONTRACT_FEE_RESET(14, "统一账户现货手续费出账"),
    ;
    private int code;
    private String desc;

    ProfitTransferTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ProfitTransferTypeEnum toEnum(int code) {
        for (ProfitTransferTypeEnum item : ProfitTransferTypeEnum.values()) {
            if (item.code == code) {
                return item;
            }
        }
        throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
    }

    /**
     * 获取回退数据类型
     *
     * @return
     */
    public ProfitTransferTypeEnum getRollbackProfitTransferType() {
        switch (this) {
            case SYSTEM_CONTRACT_FEE:
                return SYSTEM_RESET;
            case SYSTEM_SPOT_FEE:
                return SYSTEM_SPOT_FEE_RESET;
            case SYSTEM_LEVER_FEE:
                return SYSTEM_LEVER_FEE_RESET;
            case SYSTEM_CONTRACT_COST_INTEREST:
                return SYSTEM_CONTRACT_COST_INTEREST_RESET;
            case SYSTEM_UTA_SPOT_FEE:
                return SYSTEM_UTA_SPOT_FEE_RESET;
            case SYSTEM_UTA_CONTRACT_FEE:
                return SYSTEM_UTA_CONTRACT_FEE_RESET;
            default:
                return SYMBOL_PROFIT_SYSTEM_RESET;
        }
    }

    /**
     * 获取转账类型
     *
     * @param profitType
     * @return
     */
    public static ProfitTransferTypeEnum getByProfitTypeEnum(String profitType) {
        return ProfitTypeEnum.toEnum(profitType).getProfitTransferType();
    }
}