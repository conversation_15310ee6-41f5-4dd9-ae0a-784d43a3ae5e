package com.upex.reconciliation.service.service.client.cex.dto.req.common;

import lombok.Data;

import java.lang.Integer;
import java.util.Date;

@Data

public class UserAssetListReq extends CommonReq{
    /**
     * 币种名称 USDT、BTC、USDC
     */
    private String coinName;

    private Date checkSyncTime;

    public UserAssetListReq() {
    }


    public UserAssetListReq(Integer cexType,String cexUserId, String apiKey, String privateKey) {
        super(cexType, cexUserId, apiKey, privateKey);
    }

    public UserAssetListReq(Integer cexType,String cexUserId, String apiKey, String privateKey,Date checkSyncTime) {
        super(cexType, cexUserId, apiKey, privateKey);
        this.checkSyncTime = checkSyncTime;
    }



}
