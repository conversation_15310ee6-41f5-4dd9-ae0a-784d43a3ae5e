package com.upex.reconciliation.service.utils;

import com.alibaba.fastjson.JSON;
import com.upex.mixcontract.common.literal.enums.BusinessLineEnum;
import com.upex.mixcontract.common.utils.MixContractConfigUtils;
import com.upex.mixcontract.common.utils.config.Token;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 合约数据处理
 */
@Slf4j
public class MixContractUtil {
    private static Map<Byte, String> SYMBOL_SUFFIX_MAP = new HashMap<>();

    static {
        SYMBOL_SUFFIX_MAP.put(AccountTypeEnum.S_USDT_MIX_CONTRACT_BL.getCode(), "_SUMCBL");
        SYMBOL_SUFFIX_MAP.put(AccountTypeEnum.S_USDC_MIX_CONTRACT_BL.getCode(), "_SCMCBL");
        SYMBOL_SUFFIX_MAP.put(AccountTypeEnum.S_USD_MIX_CONTRACT_BL.getCode(), "_SDMCBL");
        SYMBOL_SUFFIX_MAP.put(AccountTypeEnum.USDT_MIX_CONTRACT_BL.getCode(), "_UMCBL");
        SYMBOL_SUFFIX_MAP.put(AccountTypeEnum.USDC_MIX_CONTRACT_BL.getCode(), "_CMCBL");
        SYMBOL_SUFFIX_MAP.put(AccountTypeEnum.USD_MIX_CONTRACT_BL.getCode(), "_DMCBL");
    }

    /**
     * 获取coinSymbolmap
     *
     * @param accountTypeEnum
     * @return
     */
    public static Map<Integer, String> getCoinSymbolMap(AccountTypeEnum accountTypeEnum) {
        String quoteTokenId = MixContractConfigUtils.getQuoteTokenIdByBusinessLine(BusinessLineEnum.toEnum(accountTypeEnum.getBusinessLine()));
        List<Token> tokenList = MixContractConfigUtils.getTokenList().stream().filter(item -> item.getCoinId() != null).collect(Collectors.toList());
        Map<Integer, Token> coinTokenMap = tokenList.stream().collect(Collectors.toMap(Token::getCoinId, Function.identity(), (key1, key2) -> key2));
        Map<Integer, String> coinSymbolMap = new HashMap<>();
        coinTokenMap.forEach((coinId, token) -> {
            coinSymbolMap.put(coinId, buildSymbolId(accountTypeEnum, token.getTokenId(), quoteTokenId));
        });
        if (tokenList.size() != coinSymbolMap.size()) {
            List<Token> repeatTokenList = tokenList.stream().filter(item -> {
                return !coinSymbolMap.getOrDefault(item.getCoinId(), "").equals(buildSymbolId(accountTypeEnum, item.getTokenId(), quoteTokenId));
            }).collect(Collectors.toList());
            log.info("MixContractUtil.getCoinSymbolMap repeatTokenList:{}", JSON.toJSONString(repeatTokenList));
        }
        return coinSymbolMap;
    }

    /**
     * 构建symbolid
     *
     * @param accountTypeEnum
     * @param baseTokenId
     * @param quoteTokenId
     * @return
     */
    private static String buildSymbolId(AccountTypeEnum accountTypeEnum, String baseTokenId, String quoteTokenId) {
        return baseTokenId + quoteTokenId + SYMBOL_SUFFIX_MAP.get(accountTypeEnum.getCode());
    }
}
