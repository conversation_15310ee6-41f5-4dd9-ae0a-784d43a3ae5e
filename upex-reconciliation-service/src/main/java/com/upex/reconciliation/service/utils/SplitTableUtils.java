package com.upex.reconciliation.service.utils;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;
import com.upex.reconciliation.service.common.constants.BillConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * 默克尔树分表工具
 *
 * <AUTHOR>
 * @date 2023/5/26 10:59
 */
public class SplitTableUtils {
    /**
     * 获取表后缀
     *
     * @param date
     * @return
     */
    public static String getTableNameSuffixForDay(Date date) {
        if (Objects.isNull(date)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }
        return DateUtil.get(BillConstants.SEVEN, date);
    }

    /**
     * 资金对账获取表名后缀
     *
     * @param snapshotTime 快照时间
     * @return {@link String }
     * <AUTHOR>
     * @date 2023/5/26 11:00
     */
    public static String getBillContractProfitTransferTableNameSuffix(Date snapshotTime) {
        if (Objects.isNull(snapshotTime)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }
        return getDateTableNameSuffix(snapshotTime, BillConstants.oldTBillContractProfitTransferTime);
    }

    /**
     * 资金对账获取表名后缀
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return {@link String }
     * <AUTHOR>
     * @date 2023/5/26 11:00
     */
    public static Map<String, Pair<Date, Date>> getBillContractProfitTransferIntervalTimeByMonth(Date beginTime, Date endTime) {
        Map<String, Pair<Date, Date>> resultMap = new HashMap<>();
        String endTableSuffix = getBillContractProfitTransferTableNameSuffix(endTime);
        if (StringUtils.isBlank(endTableSuffix)) {
            resultMap.put(getBillContractProfitTransferTableNameSuffix(beginTime), Pair.of(beginTime, endTime));
            return resultMap;
        }

        List<Long> dateList = getMonthEndOfMonthBeginSeconds(beginTime, endTime);
        for (int i = 0; i < dateList.size(); i += 2) {
            Date start = new Date(dateList.get(i));
            Date end = new Date(dateList.get(i + 1));
            resultMap.put(getBillContractProfitTransferTableNameSuffix(start), Pair.of(start, end));
        }
        return resultMap;
    }

    /**
     * 资金对账获取表名后缀
     *
     * @param snapshotTime    快照时间
     * @param oldSnapshotTime 老的快照时间
     * @return {@link String }
     * <AUTHOR>
     * @date 2023/5/26 11:00
     */
    private static String getDateTableNameSuffix(Date snapshotTime, Date oldSnapshotTime) {
        if (Objects.isNull(snapshotTime) || Objects.isNull(oldSnapshotTime)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }
        if (snapshotTime.compareTo(oldSnapshotTime) < 0) {
            return BillConstants.EMPTY;
        }
        return DateUtil.get(BillConstants.TWELVE, snapshotTime);
    }

    /**
     * 将时间按照月份进行拆分，保留毫秒级
     * 优化毫秒级，范围
     *
     * @param beginTime
     * @param endTime
     * @return {@link List< Long> }
     * <AUTHOR>
     * @date 2023/8/22 22:27
     */
    private static List<Long> getMonthEndOfMonthBeginSeconds(Date beginTime, Date endTime) {
        List<Long> dateList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(beginTime);
        dateList.add(calendar.getTimeInMillis());

        while (calendar.getTimeInMillis() < endTime.getTime()) {
            // 设置时间为0点整
            calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DATE), 0, 0, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            // 时间增加一个月
            calendar.add(Calendar.MONTH, 1);
            // 时间天设为1号
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            // 毫秒值-1
            calendar.add(Calendar.MILLISECOND, -1);
            if (calendar.getTimeInMillis() >= endTime.getTime()) {
                break;
            }
            // 月底
            dateList.add(calendar.getTimeInMillis());
            // 设置时间为0点整
            calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DATE), 0, 0, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            // 月初
            calendar.add(Calendar.DATE, 1);
            dateList.add(calendar.getTimeInMillis());
        }
        dateList.add(endTime.getTime());
        return dateList;
    }
}
