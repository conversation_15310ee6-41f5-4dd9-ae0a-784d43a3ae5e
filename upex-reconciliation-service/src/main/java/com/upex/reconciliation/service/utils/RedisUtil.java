package com.upex.reconciliation.service.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;

/**
 * redis 工具类
 */
@Slf4j
public class RedisUtil {
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    /***redis key前缀***/
    public static final String KEY_PREFIX = "upex-reconciliation:";
    /***负值用户redis key***/
    public static final String USER_COIN_NEGATIVE_KEY = KEY_PREFIX + "user_coin_negative_key";
    /***负值用户redis key V2***/
    public static final String USER_COIN_NEGATIVE_KEY_V2 = KEY_PREFIX + "user_coin_negative_key_v2";
    /***负值超时自动重跑redis key***/
    public static final String USER_COIN_NEGATIVE_AUTO_REPAIR_KEY = KEY_PREFIX + "user_coin_negative_auto_repair_key_";
    /***coin维度最后checkTIme***/
    public static final String CHECK_COIN_PROP_LAST_CHECK_TIME_KEY = KEY_PREFIX + "check_coin_prop_last_check_time_key";
    /***资产coin维度最后checkTIme***/
    public static final String CHECK_ASSET_COIN_PROP_LAST_CHECK_TIME_KEY = KEY_PREFIX + "check_asset_coin_prop_last_check_time_key:";
    /***资产coin维度最后checkTIme***/
    public static final String INCREMENT_INIT_BILL_USER_MAX_ID = KEY_PREFIX + "increment_init_bill_user_max_id:";
    public static final String MONITOR_SCENE_FEE_CHECK_OK_TIME = KEY_PREFIX + "fee_aggregation_finished_account_type:";
    /***kafka非法消息用户ids***/
    public static final String KAFKA_ILLEGAL_MESSAGE_USER_IDS = KEY_PREFIX + "kafka_illegal_message_user_ids";
    public static final String XXL_JOB_EXECUTE_SUCCESS_TIME = KEY_PREFIX + "xxl_job_execute_success_time";
    public static final String SAVE_TIME_SLICE_STATUS = KEY_PREFIX + "save_time_slice_status:";
    /***业务订单乱序key***/
    public static final String REVERSED_ORDER_ID_HASH_KEY = KEY_PREFIX + "reversed_order_id_hash_key:";
    /***总账最后通过对账时间***/
    public static final String BUSINESS_LAST_CHECK_OK_TIME = KEY_PREFIX + "business_last_check_ok_time:";
    public static final String BUSINESS_USER_PROFIT_INCREMENT = KEY_PREFIX + "business_user_profit_increment:";
    /***用户期初资产***/
    public static final String BUSINESS_USER_BEGIN_ASSETS = KEY_PREFIX + "business_user_begin_assets:";

    /**
     * 获取reconkey
     *
     * @param key
     * @return
     */
    public static String getRecKey(String key) {
        return KEY_PREFIX + key;
    }

    /**
     * 获取负值检测key
     *
     * @return
     */
    public static String getUserCoinNegativeKey(Byte accountType) {
        return USER_COIN_NEGATIVE_KEY + accountType;
    }

    /**
     * 获取负值检测key V2
     *
     * @return
     */
    public static String getUserCoinNegativeKeyV2(Byte accountType) {
        return USER_COIN_NEGATIVE_KEY_V2 + accountType;
    }


    /**
     * 获取负值检测自动重跑key
     *
     * @param accountType
     * @return
     */
    public static String getUserCoinNegativeAutoRepairKey(Byte accountType) {
        return USER_COIN_NEGATIVE_AUTO_REPAIR_KEY + accountType;
    }

    /**
     * 获取redis存盘状态
     *
     * @param accountType
     * @return
     */
    public static String getSaveTimeSliceStatusKey(Object accountType) {
        return SAVE_TIME_SLICE_STATUS + accountType;
    }

    /**
     * 获取业务订单乱序key
     *
     * @param accountType
     * @return
     */
    public static String getReversedOrderIdHashKey(Byte accountType) {
        return REVERSED_ORDER_ID_HASH_KEY + accountType;
    }

    /**
     * 获取总账最后通过对账时间key
     *
     * @param accountType
     * @return
     */
    public static String getBusinessLastCheckOkTime(Object accountType) {
        return BUSINESS_LAST_CHECK_OK_TIME + accountType;
    }

    /**
     * 获取用户增量盈亏rediskey
     *
     * @param accountType
     * @return
     */
    public static String getBusinessUserProfitIncrement(Byte accountType, Long userId) {
        return BUSINESS_USER_PROFIT_INCREMENT + accountType + ":" + userId;
    }

    /**
     * 获取用户期初资产key
     *
     * @param userId
     * @return
     */
    public static String getBusinessUserBeginAssets(Long userId) {
        return BUSINESS_USER_BEGIN_ASSETS + ":" + userId;
    }
}
