package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.dao.bill.entity.BillCoinProperty;
import com.upex.reconciliation.service.dao.mapper.OldBillCoinPropertyMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Service
public class OldBillCoinService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "oldBillCoinPropertyMapper")
    private OldBillCoinPropertyMapper oldBillCoinPropertyMapper;

    public List<BillCoinProperty> selectRangeCheckTimeRecord(Date checkTime,
                                                             Long minId,
                                                             Integer size,
                                                             Byte accountType,
                                                             String accountParam) {
        return dbHelper.doDbOpInBillMaster(() -> oldBillCoinPropertyMapper.selectRange(checkTime, minId, size, accountType, accountParam));
    }

    public List<BillCoinProperty> selectByCheckTime(Byte accountType,
                                                    String accountParam,
                                                    Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> oldBillCoinPropertyMapper.selectByCheckTime(checkTime,accountType, accountParam));
    }

    public BillCoinProperty selectLastByCoinId(Integer accountType,
                                               String accountParam,
                                               Integer coinId,
                                               Date checkTime) {
        return dbHelper.doDbOpInBillMaster(() -> oldBillCoinPropertyMapper.selectLastByCoinId(accountType, accountParam, coinId, checkTime));
    }

    public List<BillCoinProperty> selectLastByCoinIds(Integer accountType,
                                                      String accountParam,
                                                      List<Integer> coinIds,
                                                      Date checkTime) {
        return dbHelper.doDbOpInBillMaster(() -> oldBillCoinPropertyMapper.selectLastByCoinIds(accountType, accountParam, coinIds, checkTime));
    }
}
