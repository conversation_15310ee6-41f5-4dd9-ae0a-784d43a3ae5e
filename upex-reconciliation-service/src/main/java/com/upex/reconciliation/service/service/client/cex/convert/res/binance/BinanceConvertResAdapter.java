package com.upex.reconciliation.service.service.client.cex.convert.res.binance;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexTransferHistory;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexPayTransferHistory;
import com.upex.reconciliation.service.service.client.cex.convert.res.AbstractConvertResAdapter;
import com.upex.reconciliation.service.service.client.cex.dto.res.binance.*;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.*;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.user.CexSubUserListRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.user.CexUserInfoInnerRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.user.CommonUserStatusRes;
import com.upex.reconciliation.service.service.client.cex.enmus.*;
import com.upex.reconciliation.service.service.client.cex.utils.CexUserHolder;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

import static com.upex.reconciliation.service.utils.DateUtil.FMT_yyyy_MM_dd_HH_mm_ss;

@Service
@Slf4j
public class BinanceConvertResAdapter extends AbstractConvertResAdapter<IBinanceApiBaseRes> {

    public static final String NORMAL = "Normal";

    @Override
    public CommonRes<CommonCoinAssetRes> convertSpotCoinAssetRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof SpotAccountInfoRes) {
            SpotAccountInfoRes spotAccountCoinAssetRes = (SpotAccountInfoRes) sourceRes;
            CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
            List<CommonCoinAssetInnerRes> balances = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(spotAccountCoinAssetRes.getBalances())) {
                for (CoinAssetInnerRes balance : spotAccountCoinAssetRes.getBalances()) {
                    if (balance.getFree().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    CommonCoinAssetInnerRes commonCoinAssetInnerRes = new CommonCoinAssetInnerRes();
                    commonCoinAssetInnerRes.setCoinName(balance.getAsset());
                    commonCoinAssetInnerRes.setAvailableBalance(balance.getFree());
                    commonCoinAssetInnerRes.setLocked(balance.getLocked());
                    commonCoinAssetInnerRes.setTotalBalance(balance.getFree().add(balance.getLocked()));
                    commonCoinAssetInnerRes.setChangeTime(spotAccountCoinAssetRes.getUpdateTime());
                    commonCoinAssetInnerRes.setThirdAssetType(ThirdAssetType.PARENT_SPOT.getType());
                    commonCoinAssetInnerRes.setCexType(this.cexType());
                    balances.add(commonCoinAssetInnerRes);
                }
            }
            commonCoinAssetRes.addAll(balances);
            commonRes.setData(commonCoinAssetRes);
            log.info("binance  convertSpotCoinAssetRes:{}", commonRes.getData());
        } else {
            commonRes = CommonRes.getCastClassErrorRes();
        }
        return commonRes;
    }

    @Override
    public CommonRes<CommonCoinAssetRes> convertFundingCoinAssetRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof FundingAccountCoinAssetRes) {
            FundingAccountCoinAssetRes fundingAccountCoinAssetRes = (FundingAccountCoinAssetRes) sourceRes;
            List<CommonCoinAssetInnerRes> balances = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(fundingAccountCoinAssetRes)) {
                for (FundingAccountCoinAssetInnnerRes fundingAccountCoinAssetInnnerRes : fundingAccountCoinAssetRes) {
                    if (fundingAccountCoinAssetInnnerRes.getFree().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    CommonCoinAssetInnerRes commonCoinAssetInnerRes = new CommonCoinAssetInnerRes();
                    commonCoinAssetInnerRes.setCoinName(fundingAccountCoinAssetInnnerRes.getAsset());
                    commonCoinAssetInnerRes.setAvailableBalance(fundingAccountCoinAssetInnnerRes.getFree());
                    commonCoinAssetInnerRes.setLocked(fundingAccountCoinAssetInnnerRes.getLocked());
                    commonCoinAssetInnerRes.setThirdAssetType(ThirdAssetType.PARENT_FUNDING.getType());
                    commonCoinAssetInnerRes.setTotalBalance(fundingAccountCoinAssetInnnerRes.getFree()
                            .add(fundingAccountCoinAssetInnnerRes.getLocked()
                                    .add(fundingAccountCoinAssetInnnerRes.getFreeze()
                                            .add(fundingAccountCoinAssetInnnerRes.getWithdrawing()))));
                    commonCoinAssetInnerRes.setCexType(CexTypeEnum.BINANCE.getType());
                    balances.add(commonCoinAssetInnerRes);
                }
            }
            CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
            commonCoinAssetRes.addAll(balances);
            commonRes.setData(commonCoinAssetRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes<CommonCoinAssetRes> convertUContractCoinAssetRes(IBinanceApiBaseRes sourceRes) {
        CommonRes<CommonCoinAssetRes> commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof UContractAccountInfoRes) {
            UContractAccountInfoRes uContractAccountInfoRes = (UContractAccountInfoRes) sourceRes;
            List<CommonCoinAssetInnerRes> balances = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(uContractAccountInfoRes.getAssets())) {
                for (UContractAccountInfoInnerRes uContractAccountInfoInnerRes : uContractAccountInfoRes.getAssets()) {
                    if (uContractAccountInfoInnerRes.getWalletBalance().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    CommonCoinAssetInnerRes commonCoinAssetInnerRes = new CommonCoinAssetInnerRes();
                    commonCoinAssetInnerRes.setCoinName(uContractAccountInfoInnerRes.getAsset());
                    commonCoinAssetInnerRes.setAvailableBalance(uContractAccountInfoInnerRes.getAvailableBalance());
                    commonCoinAssetInnerRes.setTotalBalance(uContractAccountInfoInnerRes.getWalletBalance());
                    commonCoinAssetInnerRes.setMarginBalance(uContractAccountInfoInnerRes.getMarginBalance());
                    commonCoinAssetInnerRes.setCexType(CexTypeEnum.BINANCE.getType());
                    commonCoinAssetInnerRes.setChangeTime(uContractAccountInfoInnerRes.getUpdateTime());
                    commonCoinAssetInnerRes.setThirdAssetType(ThirdAssetType.PARENT_UCONTRACT.getType());
                    balances.add(commonCoinAssetInnerRes);
                }
            }
            CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
            commonCoinAssetRes.addAll(balances);
            commonRes.setData(commonCoinAssetRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes convertCoinContractCoinAssetRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof CoinContractAccountInfoRes) {
            CoinContractAccountInfoRes coinContractAccountInfoRes = (CoinContractAccountInfoRes) sourceRes;
            List<CommonCoinAssetInnerRes> balances = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(coinContractAccountInfoRes.getAssets())) {
                for (CoinContractAccountInfoInnerRes coinContractAccountInfoInnerRes : coinContractAccountInfoRes.getAssets()) {
                    if (coinContractAccountInfoInnerRes.getWalletBalance().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    CommonCoinAssetInnerRes commonCoinAssetInnerRes = new CommonCoinAssetInnerRes();
                    commonCoinAssetInnerRes.setCoinName(coinContractAccountInfoInnerRes.getAsset());
                    commonCoinAssetInnerRes.setAvailableBalance(coinContractAccountInfoInnerRes.getAvailableBalance());
                    //TODO 没有这个字段
                    //commonCoinAssetInnerRes.setLocked(coinContractAccountInfoInnerRes.getMaxWithdrawAmount());
                    commonCoinAssetInnerRes.setTotalBalance(coinContractAccountInfoInnerRes.getWalletBalance());
                    commonCoinAssetInnerRes.setMarginBalance(coinContractAccountInfoInnerRes.getMarginBalance());
                    commonCoinAssetInnerRes.setChangeTime(coinContractAccountInfoInnerRes.getUpdateTime());
                    commonCoinAssetInnerRes.setThirdAssetType(ThirdAssetType.PARENT_COIN_CONTRACT.getType());
                    commonCoinAssetInnerRes.setCexType(CexTypeEnum.BINANCE.getType());
                    balances.add(commonCoinAssetInnerRes);
                }
            }
            CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
            commonCoinAssetRes.addAll(balances);
            commonRes.setData(commonCoinAssetRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }

    }

    @Override
    public CommonRes convertMarginCoinAssetRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof MarginAccountInfoRes) {
            MarginAccountInfoRes marginAccountInfoRes = (MarginAccountInfoRes) sourceRes;
            List<CommonCoinAssetInnerRes> balances = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(marginAccountInfoRes.getUserAssets())) {
                for (MarginAccountInfoInnerRes marginAccountInfoInnerRes : marginAccountInfoRes.getUserAssets()) {
                    if (marginAccountInfoInnerRes.getFree().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    CommonCoinAssetInnerRes commonCoinAssetInnerRes = new CommonCoinAssetInnerRes();
                    commonCoinAssetInnerRes.setCoinName(marginAccountInfoInnerRes.getAsset());
                    commonCoinAssetInnerRes.setAvailableBalance(marginAccountInfoInnerRes.getFree());
                    commonCoinAssetInnerRes.setTotalBalance(marginAccountInfoInnerRes.getFree());
                    commonCoinAssetInnerRes.setLocked(marginAccountInfoInnerRes.getLocked());
                    commonCoinAssetInnerRes.setBorrowedBalance(marginAccountInfoInnerRes.getBorrowed());
                    commonCoinAssetInnerRes.setCexType(CexTypeEnum.BINANCE.getType());
                    commonCoinAssetInnerRes.setThirdAssetType(ThirdAssetType.PARENT_MARGIN.getType());
                    balances.add(commonCoinAssetInnerRes);
                }
            }
            CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
            commonCoinAssetRes.addAll(balances);
            commonRes.setData(commonCoinAssetRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes convertIsolatedMarginCoinAssetRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof IsolatedMarginAccountInfoRes) {
            IsolatedMarginAccountInfoRes isolatedMarginAccountInfoRes = (IsolatedMarginAccountInfoRes) sourceRes;
            List<CommonCoinAssetInnerRes> balances = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(isolatedMarginAccountInfoRes.getAssets())) {
                for (IsolatedMargintAccountInfoInnerRes isolatedMarginAccountInfoInnerRes : isolatedMarginAccountInfoRes.getAssets()) {
                    IsolatedMargintAccountInfoInnerRes.InnerAssetRes innerAssetRes = isolatedMarginAccountInfoInnerRes.getQuoteAsset();
                    if (innerAssetRes.getFree().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    CommonCoinAssetInnerRes commonCoinAssetInnerRes = new CommonCoinAssetInnerRes();
                    commonCoinAssetInnerRes.setCoinName(innerAssetRes.getAsset());
                    commonCoinAssetInnerRes.setAvailableBalance(innerAssetRes.getFree());
                    commonCoinAssetInnerRes.setTotalBalance(innerAssetRes.getFree().add(innerAssetRes.getLocked()));
                    commonCoinAssetInnerRes.setLocked(innerAssetRes.getLocked());
                    commonCoinAssetInnerRes.setBorrowedBalance(innerAssetRes.getBorrowed());
                    commonCoinAssetInnerRes.setCexType(CexTypeEnum.BINANCE.getType());
                    commonCoinAssetInnerRes.setThirdAssetType(ThirdAssetType.PARENT_ISOLATED_MARGIN.getType());
                    balances.add(commonCoinAssetInnerRes);
                }
            }
            CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
            commonCoinAssetRes.addAll(balances);
            commonRes.setData(commonCoinAssetRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes convertFlexEarnPositionRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof SimpleEarnFlexPositionRes) {
            SimpleEarnFlexPositionRes simpleEarnFlexPositionRes = (SimpleEarnFlexPositionRes) sourceRes;
            List<CommonCoinAssetInnerRes> balances = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(simpleEarnFlexPositionRes.getRows())) {
                for (SimpleEarnFlexPositionRes.SimpleEarnFlexPositionInnerRes simpleEarnFlexPositionInnerRes : simpleEarnFlexPositionRes.getRows()) {
                    if (simpleEarnFlexPositionInnerRes.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    CommonCoinAssetInnerRes commonCoinAssetInnerRes = new CommonCoinAssetInnerRes();
                    commonCoinAssetInnerRes.setCoinName(simpleEarnFlexPositionInnerRes.getAsset());
                    commonCoinAssetInnerRes.setAvailableBalance(simpleEarnFlexPositionInnerRes.getTotalAmount());
                    commonCoinAssetInnerRes.setTotalBalance(simpleEarnFlexPositionInnerRes.getTotalAmount());
                    commonCoinAssetInnerRes.setCexType(CexTypeEnum.BINANCE.getType());
                    commonCoinAssetInnerRes.setThirdAssetType(ThirdAssetType.PARENT_FLEX_EARN.getType());
                    balances.add(commonCoinAssetInnerRes);
                }
            }
            CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
            commonCoinAssetRes.addAll(balances);
            commonRes.setData(commonCoinAssetRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }

    }

    @Override
    public CommonRes convertLockedEarnPositionRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof SimpleEarnLockedPositionRes) {
            SimpleEarnLockedPositionRes simpleEarnLockedPositionRes = (SimpleEarnLockedPositionRes) sourceRes;
            List<CommonCoinAssetInnerRes> balances = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(simpleEarnLockedPositionRes.getRows())) {
                for (SimpleEarnLockedPositionRes.SimpleEarnLockedPositionInnerRes simpleEarnLockedPositionInnerRes : simpleEarnLockedPositionRes.getRows()) {
                    if (simpleEarnLockedPositionInnerRes.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    CommonCoinAssetInnerRes commonCoinAssetInnerRes = new CommonCoinAssetInnerRes();
                    commonCoinAssetInnerRes.setCoinName(simpleEarnLockedPositionInnerRes.getAsset());
                    commonCoinAssetInnerRes.setAvailableBalance(simpleEarnLockedPositionInnerRes.getAmount());
                    commonCoinAssetInnerRes.setTotalBalance(simpleEarnLockedPositionInnerRes.getAmount());
                    commonCoinAssetInnerRes.setCexType(CexTypeEnum.BINANCE.getType());
                    commonCoinAssetInnerRes.setThirdAssetType(ThirdAssetType.PARENT_FLEX_EARN.getType());
                    balances.add(commonCoinAssetInnerRes);
                }
            }
            CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
            commonCoinAssetRes.addAll(balances);
            commonRes.setData(commonCoinAssetRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes convertApikeyPermissionRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof ApikeyPermissionRes) {
            ApikeyPermissionRes apikeyPermissionRes = (ApikeyPermissionRes) sourceRes;
            CommonApiKeyPermissionRes commonApiKeyPermissionRes = new CommonApiKeyPermissionRes();
            commonApiKeyPermissionRes.setIpRestrict(apikeyPermissionRes.getIpRestrict());
            commonApiKeyPermissionRes.setEnableReading(apikeyPermissionRes.getEnableReading());
            commonApiKeyPermissionRes.setEnableWithdrawals(apikeyPermissionRes.getEnableWithdrawals());
            commonApiKeyPermissionRes.setEnableInternalTransfer(apikeyPermissionRes.getEnableInternalTransfer());
            commonApiKeyPermissionRes.setEnableMargin(apikeyPermissionRes.getEnableMargin());
            commonApiKeyPermissionRes.setEnableFutures(apikeyPermissionRes.getEnableFutures());
            commonApiKeyPermissionRes.setPermitsUniversalTransfer(apikeyPermissionRes.getPermitsUniversalTransfer());
//            commonApiKeyPermissionRes.setEnableVanillaOptions(apikeyPermissionRes.getEnableVanillaOptions());
//            commonApiKeyPermissionRes.setEnableFixApiTrade(apikeyPermissionRes.getEnableFixApiTrade());
//            commonApiKeyPermissionRes.setEnableFixReadOnly(apikeyPermissionRes.getEnableFixReadOnly());
            commonApiKeyPermissionRes.setEnableSpotAndMarginTrading(apikeyPermissionRes.getEnableSpotAndMarginTrading());
            commonApiKeyPermissionRes.setEnablePortfolioMarginTrading(apikeyPermissionRes.getEnablePortfolioMarginTrading());
            commonRes.setData(commonApiKeyPermissionRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes convertUserStatusRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof UserAccountStatusRes) {
            UserAccountStatusRes userAccountStatusRes = (UserAccountStatusRes) sourceRes;
            CommonUserStatusRes commonUserStatusRes = new CommonUserStatusRes();
            if (userAccountStatusRes.getData() != null) {
                commonUserStatusRes.setStatus(userAccountStatusRes.getData().equals(NORMAL) ? CexUserStatusEnum.NORMAL.getType() : CexUserStatusEnum.UNNORMAL.getType());
            } else {
                commonUserStatusRes.setStatus(CexUserStatusEnum.UNNORMAL.getType());
            }
            commonUserStatusRes.setMessage(userAccountStatusRes.getData());
            commonRes.setData(commonUserStatusRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes convertSubUserListRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof SubAccountListRes) {
            SubAccountListRes subAccountListRes = (SubAccountListRes) sourceRes;
            CexSubUserListRes cexSubUserListRes = new CexSubUserListRes();
            if (CollectionUtils.isNotEmpty(subAccountListRes.getSubAccounts())) {
                subAccountListRes.getSubAccounts().forEach(item -> {
                    CexUserInfoInnerRes cexUserInfoInnerRes = new CexUserInfoInnerRes();
                    cexUserInfoInnerRes.setCexUserId(String.valueOf(item.getSubUserId()));
                    cexUserInfoInnerRes.setCexUserStatus(item.getIsFreeze() ? CexUserStatusEnum.UNNORMAL.getType() : CexUserStatusEnum.NORMAL.getType());
                    cexUserInfoInnerRes.setCexEmail(item.getEmail());
                    cexUserInfoInnerRes.setCexType(this.cexType());
                    cexSubUserListRes.add(cexUserInfoInnerRes);
                });
            }
            commonRes.setData(cexSubUserListRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes convertSpotPositionRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof SpotPositionRes) {
            SpotPositionRes spotPositionRes = (SpotPositionRes) sourceRes;
            List<CommonCoinAssetInnerRes> balances = new ArrayList<>();
            CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
            if (CollectionUtils.isNotEmpty(spotPositionRes)) {
                for (SpotPositionInnerRes item : spotPositionRes) {
                    if (item.getFree().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    CommonCoinAssetInnerRes commonCoinAssetInnerRes = new CommonCoinAssetInnerRes();
                    commonCoinAssetInnerRes.setCoinName(item.getAsset());
                    commonCoinAssetInnerRes.setAvailableBalance(item.getFree());
                    commonCoinAssetInnerRes.setTotalBalance(item.getFree().add(item.getLocked()).add(item.getWithdrawing()).add(item.getFreeze()));
                    commonCoinAssetInnerRes.setLocked(item.getLocked());
                    commonCoinAssetInnerRes.setFreeze(item.getFreeze());
                    commonCoinAssetInnerRes.setWithdrawing(item.getWithdrawing());
                    commonCoinAssetInnerRes.setBtcValuation(item.getBtcValuation());
                    commonCoinAssetInnerRes.setCexType(this.cexType());
                    commonCoinAssetInnerRes.setThirdAssetType(ThirdAssetType.PARENT_SPOT.getType());
                    balances.add(commonCoinAssetInnerRes);
                }
            }
            commonCoinAssetRes.addAll(balances);
            commonRes.setData(commonCoinAssetRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    public CommonRes convertSubUserSpotAssetRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof SubUserSpotAssetRes) {
            SubUserSpotAssetRes subUserSpotAssetRes = (SubUserSpotAssetRes) sourceRes;
            List<CommonCoinAssetInnerRes> balances = new ArrayList<>();
            CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
            if (CollectionUtils.isNotEmpty(subUserSpotAssetRes.getBalances())) {
                for (SpotPositionInnerRes item : subUserSpotAssetRes.getBalances()) {
                    if (item.getFree().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    CommonCoinAssetInnerRes commonCoinAssetInnerRes = new CommonCoinAssetInnerRes();
                    commonCoinAssetInnerRes.setCoinName(item.getAsset());
                    commonCoinAssetInnerRes.setAvailableBalance(item.getFree());
                    commonCoinAssetInnerRes.setTotalBalance(item.getFree().add(item.getLocked()).add(item.getWithdrawing()).add(item.getFreeze()));
                    commonCoinAssetInnerRes.setLocked(item.getLocked());
                    commonCoinAssetInnerRes.setFreeze(item.getFreeze());
                    commonCoinAssetInnerRes.setWithdrawing(item.getWithdrawing());
                    commonCoinAssetInnerRes.setBtcValuation(item.getBtcValuation());
                    commonCoinAssetInnerRes.setCexType(this.cexType());
                    commonCoinAssetInnerRes.setThirdAssetType(ThirdAssetType.SUB_SPOT.getType());
                    balances.add(commonCoinAssetInnerRes);
                }
            }
            commonCoinAssetRes.addAll(balances);
            commonRes.setData(commonCoinAssetRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    public CommonRes convertSubUserMarginAssetRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof SubUserMarginAccountInfoRes) {
            SubUserMarginAccountInfoRes subUserMarginAccountInfoRes = (SubUserMarginAccountInfoRes) sourceRes;
            List<CommonCoinAssetInnerRes> balances = new ArrayList<>();
            CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
            if (CollectionUtils.isNotEmpty(subUserMarginAccountInfoRes.getMarginUserAssetVoList())) {
                for (MarginAccountInfoInnerRes item : subUserMarginAccountInfoRes.getMarginUserAssetVoList()) {
                    if (item.getFree().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    CommonCoinAssetInnerRes commonCoinAssetInnerRes = new CommonCoinAssetInnerRes();
                    commonCoinAssetInnerRes.setCoinName(item.getAsset());
                    commonCoinAssetInnerRes.setTotalBalance(item.getFree());
                    commonCoinAssetInnerRes.setAvailableBalance(item.getFree());
                    commonCoinAssetInnerRes.setBorrowedBalance(item.getBorrowed());
                    commonCoinAssetInnerRes.setCexType(this.cexType());
                    commonCoinAssetInnerRes.setThirdAssetType(ThirdAssetType.SUB_MARGIN.getType());
                    balances.add(commonCoinAssetInnerRes);
                }
            }
            commonCoinAssetRes.addAll(balances);
            commonRes.setData(commonCoinAssetRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    public CommonRes convertSubUserUContractAssetRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof SubUserContractAccountInfoRes) {
            SubUserContractAccountInfoRes subUserContractAccountInfoRes = (SubUserContractAccountInfoRes) sourceRes;
            List<CommonCoinAssetInnerRes> balances = new ArrayList<>();
            CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
            if (Objects.nonNull(subUserContractAccountInfoRes.getFutureAccountResp()) && CollectionUtils.isNotEmpty(subUserContractAccountInfoRes.getFutureAccountResp().getAssets())) {
                for (SubUserContractAccountInfoInnerRes item : subUserContractAccountInfoRes.getFutureAccountResp().getAssets()) {
                    if (item.getWalletBalance().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    CommonCoinAssetInnerRes commonCoinAssetInnerRes = new CommonCoinAssetInnerRes();
                    commonCoinAssetInnerRes.setCexType(this.cexType());
                    commonCoinAssetInnerRes.setCoinName(item.getAsset());
                    commonCoinAssetInnerRes.setThirdAssetType(ThirdAssetType.SUB_UCONTRACT.getType());
                    commonCoinAssetInnerRes.setTotalBalance(item.getWalletBalance());
                    commonCoinAssetInnerRes.setAvailableBalance(item.getAvailableBalance());
                    commonCoinAssetInnerRes.setMarginBalance(item.getMarginBalance());
                    commonCoinAssetInnerRes.setChangeTime(item.getUpdateTime());
                    balances.add(commonCoinAssetInnerRes);
                }
            }
            commonCoinAssetRes.addAll(balances);
            commonRes.setData(commonCoinAssetRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    public CommonRes convertSubUserCoinContractAssetRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof SubUserContractAccountInfoRes) {
            SubUserContractAccountInfoRes subUserContractAccountInfoRes = (SubUserContractAccountInfoRes) sourceRes;
            List<CommonCoinAssetInnerRes> balances = new ArrayList<>();
            CommonCoinAssetRes commonCoinAssetRes = new CommonCoinAssetRes();
            if (Objects.nonNull(subUserContractAccountInfoRes.getFutureAccountResp()) && CollectionUtils.isNotEmpty(subUserContractAccountInfoRes.getFutureAccountResp().getAssets())) {
                for (SubUserContractAccountInfoInnerRes item : subUserContractAccountInfoRes.getFutureAccountResp().getAssets()) {
                    if (item.getWalletBalance().compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    CommonCoinAssetInnerRes commonCoinAssetInnerRes = new CommonCoinAssetInnerRes();
                    commonCoinAssetInnerRes.setCexType(this.cexType());
                    commonCoinAssetInnerRes.setCoinName(item.getAsset());
                    commonCoinAssetInnerRes.setThirdAssetType(ThirdAssetType.SUB_COIN_CONTRACT.getType());
                    commonCoinAssetInnerRes.setTotalBalance(item.getWalletBalance());
                    commonCoinAssetInnerRes.setAvailableBalance(item.getAvailableBalance());
                    commonCoinAssetInnerRes.setMarginBalance(item.getMarginBalance());
                    commonCoinAssetInnerRes.setChangeTime(item.getUpdateTime());
                    balances.add(commonCoinAssetInnerRes);
                }
            }
            commonCoinAssetRes.addAll(balances);
            commonRes.setData(commonCoinAssetRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    public CommonRes convertSupportCoinListRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof SupportCoinListRes) {
            SupportCoinListRes supportCoinListRes = (SupportCoinListRes) sourceRes;
            CommonCoinInfoListRes commonCoinInfoListRes = new CommonCoinInfoListRes();
            List<CommonCoinInfoInnerRes> commonCoinResList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(supportCoinListRes)) {
                for (SupportCoinInfoInnerRes supportCoinInfoInnerRes : supportCoinListRes) {
                    if (CollectionUtils.isEmpty(supportCoinInfoInnerRes.getNetworkList())) {
                        continue;
                    }
                    for (SupportCoinInfoInnerRes.CoinNetwork coinNetwork : supportCoinInfoInnerRes.getNetworkList()) {
                        CommonCoinInfoInnerRes commonCoinInfoInnerRes = new CommonCoinInfoInnerRes();
                        commonCoinInfoInnerRes.setCoinName(coinNetwork.getCoin());
                        commonCoinInfoInnerRes.setNetwork(coinNetwork.getNetwork());
                        commonCoinInfoInnerRes.setIsDefault(coinNetwork.getIsDefault());
                        commonCoinInfoInnerRes.setDepositEnable(coinNetwork.getDepositEnable());
                        commonCoinInfoInnerRes.setWithdrawEnable(coinNetwork.getWithdrawEnable());
                        commonCoinResList.add(commonCoinInfoInnerRes);
                    }
                }
            }
            commonCoinInfoListRes.addAll(commonCoinResList);
            commonRes.setData(commonCoinInfoListRes);
            return commonRes;
        } else {
            throw  new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes convertDepositeAddress(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof DepositeAddressRes) {
            DepositeAddressRes depositeAddressRes = (DepositeAddressRes) sourceRes;
            List<CommonDepositeAddressInnerRes> commonDepositeAddressInnerResList = new ArrayList<>();
            CommonDepositeAddressRes commonDepositeAddressRes = new CommonDepositeAddressRes();
            if (CollectionUtils.isNotEmpty(depositeAddressRes)) {
                for (DepositeAddressInnerRes depositeAddressInnerRes : depositeAddressRes) {
                    CommonDepositeAddressInnerRes commonDepositeAddressInnerRes = new CommonDepositeAddressInnerRes();
                    commonDepositeAddressInnerRes.setCoinName(depositeAddressInnerRes.getCoin());
                    commonDepositeAddressInnerRes.setAddress(depositeAddressInnerRes.getAddress());
                    commonDepositeAddressInnerRes.setTag(depositeAddressInnerRes.getTag());
                    commonDepositeAddressInnerRes.setIsDefault(depositeAddressInnerRes.getIsDefault());
                    commonDepositeAddressInnerResList.add(commonDepositeAddressInnerRes);
                }
            }
            commonDepositeAddressRes.addAll(commonDepositeAddressInnerResList);
            commonRes.setData(commonDepositeAddressRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes convertUserDepositeHistory(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof DepositeHistoryRes) {
            DepositeHistoryRes depositeHistoryRes = (DepositeHistoryRes) sourceRes;
            CommonDepositeHistoryRes commonDepositeHistoryRes = new CommonDepositeHistoryRes();
            List<CommonDepositeHistoryInnerRes> commonDepositeHistoryInnerResList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(depositeHistoryRes)) {
                for (DepositeHistoryInnerRes depositeHistoryInnerRes : depositeHistoryRes) {
                    CommonDepositeHistoryInnerRes commonDepositeHistoryInnerRes = new CommonDepositeHistoryInnerRes();
                    commonDepositeHistoryInnerRes.setDepositeId(depositeHistoryInnerRes.getId());
                    commonDepositeHistoryInnerRes.setCexType(CexTypeEnum.BINANCE.getType());
                    commonDepositeHistoryInnerRes.setCexUserId(CexUserHolder.getCurrentUserId());
                    commonDepositeHistoryInnerRes.setCexEmail(CexUserHolder.getCexEmail());
                    commonDepositeHistoryInnerRes.setCoinName(depositeHistoryInnerRes.getCoin());
                    commonDepositeHistoryInnerRes.setNetwork(depositeHistoryInnerRes.getNetwork());
                    commonDepositeHistoryInnerRes.setStatus(depositeHistoryInnerRes.getStatus());
                    commonDepositeHistoryInnerRes.setTxId(depositeHistoryInnerRes.getTxId());
                    commonDepositeHistoryInnerRes.setAddress(depositeHistoryInnerRes.getAddress());
                    commonDepositeHistoryInnerRes.setAddressTag(depositeHistoryInnerRes.getAddressTag());
                    commonDepositeHistoryInnerRes.setAsset(depositeHistoryInnerRes.getAsset());
                    commonDepositeHistoryInnerRes.setAmount(depositeHistoryInnerRes.getAmount());
                    commonDepositeHistoryInnerRes.setConfirmTimes(depositeHistoryInnerRes.getConfirmTimes());
                    commonDepositeHistoryInnerRes.setWalletType(depositeHistoryInnerRes.getWalletType());
                    commonDepositeHistoryInnerRes.setUnlockConfirm(depositeHistoryInnerRes.getUnlockConfirm());
                    commonDepositeHistoryInnerRes.setTransferType(depositeHistoryInnerRes.getTransferType());
                    commonDepositeHistoryInnerRes.setDepositeBeginTime(new Date(depositeHistoryInnerRes.getInsertTime()));
                    commonDepositeHistoryInnerRes.setDepositeEndTime(depositeHistoryInnerRes.getCompleteTime()!=null?new Date(depositeHistoryInnerRes.getCompleteTime()):null);
                    commonDepositeHistoryInnerRes.setCreateTime(new Date());
                    commonDepositeHistoryInnerRes.setUpdateTime(new Date());
                    commonDepositeHistoryInnerRes.setVersion(SerialNoGenerator.getMinIdByTime(new Date()));
                    commonDepositeHistoryInnerResList.add(commonDepositeHistoryInnerRes);
                }
            }
            commonDepositeHistoryRes.addAll(commonDepositeHistoryInnerResList);
            commonRes.setData(commonDepositeHistoryRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes convertUserWithdrawHistory(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof WithdrawHistoryRes) {
            WithdrawHistoryRes withdrawHistoryRes = (WithdrawHistoryRes) sourceRes;
            CommonWithdrawHistoryRes commonWithdrawHistoryRes = new CommonWithdrawHistoryRes();
            List<CommonWithdrawHistoryInnerRes> commonWithdrawHistoryInnerResList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(withdrawHistoryRes)) {
                for (WithdrawHistoryInnerRes withdrawHistoryInnerRes : withdrawHistoryRes) {
                    CommonWithdrawHistoryInnerRes commonWithdrawHistoryInnerRes = new CommonWithdrawHistoryInnerRes();
                    commonWithdrawHistoryInnerRes.setDrawId(withdrawHistoryInnerRes.getId());
                    commonWithdrawHistoryInnerRes.setCexType(this.cexType());
                    commonWithdrawHistoryInnerRes.setCexUserId(CexUserHolder.getCurrentUserId());
                    commonWithdrawHistoryInnerRes.setCexEmail(CexUserHolder.getCexEmail());
                    commonWithdrawHistoryInnerRes.setCoinName(withdrawHistoryInnerRes.getCoin());
                    commonWithdrawHistoryInnerRes.setNetwork(withdrawHistoryInnerRes.getNetwork());
                    commonWithdrawHistoryInnerRes.setStatus(withdrawHistoryInnerRes.getStatus());
                    commonWithdrawHistoryInnerRes.setTxId(withdrawHistoryInnerRes.getTxId());
                    commonWithdrawHistoryInnerRes.setAddress(withdrawHistoryInnerRes.getAddress());
                    commonWithdrawHistoryInnerRes.setAddressTag(withdrawHistoryInnerRes.getAddressTag());
                    commonWithdrawHistoryInnerRes.setAsset(withdrawHistoryInnerRes.getAsset());
                    commonWithdrawHistoryInnerRes.setAmount(withdrawHistoryInnerRes.getAmount());
                    commonWithdrawHistoryInnerRes.setWalletType(withdrawHistoryInnerRes.getWalletType());
                    commonWithdrawHistoryInnerRes.setInfo(withdrawHistoryInnerRes.getInfo());
                    commonWithdrawHistoryInnerRes.setConfirmNo(withdrawHistoryInnerRes.getConfirmNo());
                    commonWithdrawHistoryInnerRes.setTransferType(withdrawHistoryInnerRes.getTransferType());
                    commonWithdrawHistoryInnerRes.setWithdrawBeginTime(DateUtil.str2dateWithLenientFalse(withdrawHistoryInnerRes.getApplyTime(), FMT_yyyy_MM_dd_HH_mm_ss));
                    commonWithdrawHistoryInnerRes.setWithdrawEndTime(withdrawHistoryInnerRes.getCompleteTime()!=null?DateUtil.str2dateWithLenientFalse(withdrawHistoryInnerRes.getCompleteTime(), FMT_yyyy_MM_dd_HH_mm_ss):null);
                    commonWithdrawHistoryInnerRes.setFee1(withdrawHistoryInnerRes.getTransactionFee());
                    commonWithdrawHistoryInnerRes.setCreateTime(new Date());
                    commonWithdrawHistoryInnerRes.setUpdateTime(new Date());
                    commonWithdrawHistoryInnerRes.setVersion(SerialNoGenerator.getMinIdByTime(new Date()));
                    commonWithdrawHistoryInnerResList.add(commonWithdrawHistoryInnerRes);
                }
            }
            commonWithdrawHistoryRes.addAll(commonWithdrawHistoryInnerResList);
            commonRes.setData(commonWithdrawHistoryRes);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes<List<ThirdCexPayTransferHistory>> convertPayTransferHistory(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof PayTransactionHistoryRes) {
            PayTransactionHistoryRes payTransactionHistoryRes = (PayTransactionHistoryRes) sourceRes;
            List<ThirdCexPayTransferHistory> cexPayTransferHistories = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(payTransactionHistoryRes.getData())) {
                for (PayTransactionHistoryRes.DataItem dataItem : payTransactionHistoryRes.getData()) {
                    ThirdCexPayTransferHistory commonPayTransferHistoryRes = new ThirdCexPayTransferHistory();
                    commonPayTransferHistoryRes.setCexType(this.cexType());
                    commonPayTransferHistoryRes.setCexUserId(CexUserHolder.getCurrentUserId());
                    commonPayTransferHistoryRes.setCexEmail(CexUserHolder.getCexEmail());
                    commonPayTransferHistoryRes.setSourceUserId(String.valueOf(dataItem.getPayerInfo().getBinanceId()));
                    commonPayTransferHistoryRes.setTargetUserId(String.valueOf(dataItem.getReceiverInfo().getBinanceId()));
                    commonPayTransferHistoryRes.setCoinName(dataItem.getCurrency());
                    commonPayTransferHistoryRes.setAmount(dataItem.getAmount());
                    commonPayTransferHistoryRes.setTransferId(dataItem.getTransactionId());
                    commonPayTransferHistoryRes.setTransferTime(new Date(dataItem.getTransactionTime()));
                    commonPayTransferHistoryRes.setCreateTime(new Date());
                    commonPayTransferHistoryRes.setUpdateTime(new Date());
                    commonPayTransferHistoryRes.setVersion(SerialNoGenerator.getMinIdByTime(new Date()));
                    cexPayTransferHistories.add(commonPayTransferHistoryRes);
                }
            }
            commonRes.setData(cexPayTransferHistories);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes<List<ThirdCexTransferHistory>> convertParentSubTransferRecordRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof ParentSubTransferRes) {
            ParentSubTransferRes parentSubTransferRes = (ParentSubTransferRes) sourceRes;
            List<ThirdCexTransferHistory> cexThirdCexTransferHistories = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(parentSubTransferRes.getResult())) {
                for (ParentSubTransferInnerRes parentSubTransferInnerRes : parentSubTransferRes.getResult()) {
                    ThirdCexTransferHistory thirdCexTransferHistory = new ThirdCexTransferHistory();
                    thirdCexTransferHistory.setCexType(this.cexType());
                    thirdCexTransferHistory.setCexUserId(CexUserHolder.getCurrentUserId());
                    thirdCexTransferHistory.setCexEmail(CexUserHolder.getCexEmail());
                    thirdCexTransferHistory.setFromAccountType(parentSubTransferInnerRes.getFromAccountType());
                    thirdCexTransferHistory.setToAccountType(parentSubTransferInnerRes.getToAccountType());
                    thirdCexTransferHistory.setCoinName(parentSubTransferInnerRes.getAsset());
                    thirdCexTransferHistory.setAmount(parentSubTransferInnerRes.getAmount());
                    thirdCexTransferHistory.setTransferId(String.valueOf(parentSubTransferInnerRes.getTranId()));
                    thirdCexTransferHistory.setTransferType(TransferType.PARENT_TO_SUB.getType());
                    thirdCexTransferHistory.setStatus(TransferRecordStatus.SUCEESS.getStatus().equals(parentSubTransferInnerRes.getStatus()) ? TransferRecordStatus.SUCEESS.getType() : TransferRecordStatus.OTHER.getType());
                    thirdCexTransferHistory.setInfo(parentSubTransferInnerRes.getStatus());
                    thirdCexTransferHistory.setFromUser(parentSubTransferInnerRes.getFromEmail());
                    thirdCexTransferHistory.setToUser(parentSubTransferInnerRes.getToEmail());
                    thirdCexTransferHistory.setChangeTime(new Date(parentSubTransferInnerRes.getCreateTimeStamp()));
                    thirdCexTransferHistory.setCreateTime(new Date());
                    thirdCexTransferHistory.setUpdateTime(new Date());
                    thirdCexTransferHistory.setVersion(SerialNoGenerator.getMinIdByTime(new Date()));
                    cexThirdCexTransferHistories.add(thirdCexTransferHistory);
                }
            }
            commonRes.setData(cexThirdCexTransferHistories);
            return commonRes;
        } else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public CommonRes<List<ThirdCexTransferHistory>> convertUniversialTransferListRes(IBinanceApiBaseRes sourceRes) {
        CommonRes commonRes = CommonRes.getSucApiBaseRes();
        if (sourceRes instanceof UniversalTransferRecordRes) {
            UniversalTransferRecordRes universalTransferRecordRes = (UniversalTransferRecordRes) sourceRes;
            List<UniversalTransferRecordInnerRes> universalTransferRecordInners = universalTransferRecordRes.getRows();
            List<ThirdCexTransferHistory> thirdCexTransferHistories = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(universalTransferRecordInners)) {
                for (UniversalTransferRecordInnerRes universalTransferRecordInner : universalTransferRecordInners) {
                    ThirdCexTransferHistory thirdCexTransferHistory = new ThirdCexTransferHistory();
                    thirdCexTransferHistory.setCexUserId(CexUserHolder.getCurrentUserId());
                    thirdCexTransferHistory.setCexEmail(CexUserHolder.getCexEmail());
                    thirdCexTransferHistory.setCexType(this.cexType());
                    thirdCexTransferHistory.setTransferId(String.valueOf(universalTransferRecordInner.getTranId()));
                    thirdCexTransferHistory.setFromUser(CexUserHolder.getCexEmail());
                    thirdCexTransferHistory.setToUser(CexUserHolder.getCexEmail());
                    thirdCexTransferHistory.setTransferType(TransferType.PARENT_UNIVERSIAL_TRANSFER.getType());
                    thirdCexTransferHistory.setCoinName(universalTransferRecordInner.getAsset());
                    thirdCexTransferHistory.setAmount(universalTransferRecordInner.getAmount());
                    thirdCexTransferHistory.setFromAccountType(UniversalTransferRecordTypeEnum.valueOf(universalTransferRecordInner.getType()) != null ?
                            UniversalTransferRecordTypeEnum.valueOf(universalTransferRecordInner.getType()).getFromAccountType() : universalTransferRecordInner.getType());
                    thirdCexTransferHistory.setToAccountType(UniversalTransferRecordTypeEnum.valueOf(universalTransferRecordInner.getType()) != null ?
                        UniversalTransferRecordTypeEnum.valueOf(universalTransferRecordInner.getType()).getToAccountType() : universalTransferRecordInner.getType());
                    thirdCexTransferHistory.setChangeTime(new Date(universalTransferRecordInner.getTimestamp()));
                    thirdCexTransferHistory.setInfo(universalTransferRecordInner.getStatus());
                    thirdCexTransferHistory.setStatus(TransferRecordStatus.CONFIRMED.getStatus().equals(universalTransferRecordInner.getStatus()) ? TransferRecordStatus.SUCEESS.getType() : TransferRecordStatus.OTHER.getType());
                    thirdCexTransferHistory.setCreateTime(new Date());
                    thirdCexTransferHistory.setUpdateTime(new Date());
                    thirdCexTransferHistory.setVersion(SerialNoGenerator.getMinIdByTime(new Date()));
                    thirdCexTransferHistories.add(thirdCexTransferHistory);
                }
            }
            return CommonRes.getSucApiBaseRes(thirdCexTransferHistories);
        }else {
            throw new ApiException(ReconCexExceptionEnum.CALSS_CAST_ERROR);
        }
    }

    @Override
    public Integer cexType() {
        return CexTypeEnum.BINANCE.getType();
    }

}
