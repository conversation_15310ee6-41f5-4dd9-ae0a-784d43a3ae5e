package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.util.List;
@Data
public class MarginAccountInfoRes implements IBinanceApiBaseRes{

    /**
     * "tradeEnabled": true,
     *     "transferEnabled": true,
     *     "transferInEnabled": true,
     *     "transferOutEnabled": true,
     *     "borrowEnabled": true,
     *     "marginLevel": "999",
     *     "totalAssetOfBtc": "0.********",
     *     "totalLiabilityOfBtc": "0",
     *     "totalNetAssetOfBtc": "0.********",
     *     "collateralMarginLevel": "999",
     *     "totalCollateralValueInUSDT": "10",
     *     "accountType": "MARGIN_1",
     *     "created": true
     *     "userAssets": [
     *         {
     *             "asset": "AGLD",
     *             "free": "0",
     *             "locked": "0",
     *             "borrowed": "0",
     *             "interest": "0",
     *             "netAsset": "0"
     *         }
     *         ]
     */
    private Boolean tradeEnabled;
    private Boolean transferEnabled;
    private Boolean transferInEnabled;
    private Boolean transferOutEnabled;
    private Boolean borrowEnabled;
    private String marginLevel;
    private String totalAssetOfBtc;
    private String totalLiabilityOfBtc;
    private String totalNetAssetOfBtc;
    private List<MarginAccountInfoInnerRes> userAssets;
    private String totalUnrealizedProfit;
    private String marginBalance;
    private String availableBalance;
    private String maxWithdrawAmount;
    private String collateralMarginLevel;
    private String totalCollateralValueInUSDT;
    private String accountType;
    private Boolean created;

}
