package com.upex.reconciliation.service.service.client.cex.enmus;

import lombok.Getter;

@Getter
public enum CexTypeEnum {
    BITGET(0,"bitget"),
    BINANCE(1,"binance"),
    OKX(2,"okx");

    private Integer type;
    private String name;
    CexTypeEnum(Integer type,String name){
        this.type = type;
        this.name = name;
    }

    public static CexTypeEnum fromType(Integer type) {
        for (CexTypeEnum e : values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return null;
    }



}
