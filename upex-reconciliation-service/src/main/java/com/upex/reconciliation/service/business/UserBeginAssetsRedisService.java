package com.upex.reconciliation.service.business;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.upex.reconciliation.facade.dto.results.ReconTotalAssetsDetailVo;
import com.upex.reconciliation.facade.dto.results.ReconTotalAssetsVO;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.reconciliation.service.utils.RedisUtil;
import com.upex.ticker.facade.dto.PriceVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.TimeoutUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/09
 */
@Slf4j
@Service
public class UserBeginAssetsRedisService {

    @Resource(name = "reconRedisTemplate")
    private RedisTemplate<String, String> reconRedisTemplate;

    @Resource
    private ReconUserAssetsSnapShotService reconUserAssetsSnapShotService;

    @Resource
    private CommonService commonService;

    /**
     * Lua脚本
     * KEYS[1]: zset key
     * ARGV[1]: score (timestamp)
     * ARGV[2]: value (map -> json string)
     * ARGV[3]: ttl seconds
     * ARGV[4]: max size
     *
     */
    private static final String beginAssetsLuaScript = "redis.call('ZADD', KEYS[1], ARGV[1], ARGV[2])\n" +
            "local count = redis.call('ZCARD', KEYS[1])\n" +
            "if count > tonumber(ARGV[4]) then\n" +
            "  redis.call('ZREMRANGEBYRANK', KEYS[1], 0, count - tonumber(ARGV[4]) - 1)\n" +
            "end\n" +
            "redis.call('EXPIRE', KEYS[1], tonumber(ARGV[3]))\n" +
            "return count";

    private String scriptSha;

    @PostConstruct
    public void init() {
        scriptSha = loadScriptSha(beginAssetsLuaScript);
    }

    private String loadScriptSha(String script) {
        return reconRedisTemplate.execute((RedisConnection connection) ->
                connection.scriptingCommands().scriptLoad(script.getBytes(StandardCharsets.UTF_8)), true);
    }

    public Long addZSet(String key, Long timestamp, String value, Long expireSeconds, int maxCount) {
        return reconRedisTemplate.execute(
                (RedisCallback<Long>) connection -> connection.scriptingCommands().evalSha(
                        scriptSha,
                        ReturnType.INTEGER,
                        1,
                        key.getBytes(StandardCharsets.UTF_8),
                        String.valueOf(timestamp).getBytes(),
                        value.getBytes(),
                        String.valueOf(expireSeconds).getBytes(),
                        String.valueOf(maxCount).getBytes()
                )
        );
    }

    /**
     * 用户期初资产
     *
     * @param userId
     * @param timestamp
     */
    public void addUserBeginAssets(Long userId, Long timestamp) {
        GlobalBillConfig billConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        List<String> subSystemList = billConfig.getCheckProfitSubSystemList();
        Map<Integer, PriceVo> ratesMap = commonService.getCoinIdRatesMapCache(timestamp);
        Map<Integer, String> allCoinsMap = commonService.getAllCoinsMapCache(timestamp);
        ReconTotalAssetsDetailVo userBeginAssets = reconUserAssetsSnapShotService.listUserAssetsBySnapShotTime(userId, timestamp, allCoinsMap, ratesMap, subSystemList, null, Boolean.FALSE);
        addUserBeginAssets(userId, timestamp, userBeginAssets, billConfig);
    }

    /**
     * 用户期初资产
     *
     * @param userId
     * @param snapshotTime
     */
    public void addUserBeginAssets(Long userId, Long snapshotTime, ReconTotalAssetsDetailVo userBeginAssets, GlobalBillConfig billConfig) {
        if (CollectionUtil.isEmpty(userBeginAssets.getTotalAssetsDetail())) {
            return;
        }
        Map<Integer, BigDecimal> coinMap = userBeginAssets.getTotalAssetsDetail().stream().collect(Collectors.toMap(ReconTotalAssetsVO::getCoinId,
                ReconTotalAssetsVO::getCount, BigDecimal::add));
        long expireHour = billConfig.getUserBeginAssetsRedisExpireHour();
        long expireSeconds = TimeoutUtils.toSeconds(expireHour, TimeUnit.HOURS);
        Long member = addZSet(RedisUtil.getBusinessUserBeginAssets(userId), snapshotTime, JSON.toJSONString(coinMap), expireSeconds, billConfig.getUserBeginAssetsMaxCount());
        log.info("addUserBeginAssets userId:{} snapshotTime:{}, coinMap:{}, member:{}", userId, snapshotTime, coinMap, member);
    }

    /**
     * 获取期初资产
     *
     * @param userId
     * @param snapshotTime
     * @return
     */
    public Map<Integer, BigDecimal> getUserBeginAssets(Long userId, Long snapshotTime) {
        Map<Integer, BigDecimal> coinAssetsMap = new HashMap<>();
        Set<String> userBeginAssetsSet = reconRedisTemplate.opsForZSet().rangeByScore(RedisUtil.getBusinessUserBeginAssets(userId), 0, snapshotTime, 0, 1);
        if (CollectionUtil.isEmpty(userBeginAssetsSet)) {
            return coinAssetsMap;
        }
        String userBeginAssetsData = userBeginAssetsSet.iterator().next();
        if (StringUtils.isNotBlank(userBeginAssetsData)) {
            log.info("getUserBeginAssets userId:{} snapshotTime:{}, userBeginAssetsData:{}", userId, snapshotTime, userBeginAssetsData);
            coinAssetsMap = JSON.parseObject(userBeginAssetsData, new TypeReference<Map<Integer, BigDecimal>>() {});
        }
        return coinAssetsMap;
    }
}
