package com.upex.reconciliation.service.utils;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.upex.common.function.FunctionP0;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 缓存工具类
 *
 * <AUTHOR>
 * @date 2022/12/30 20:19
 */
@Slf4j
public class CacheUtils {

    /**
     * 获取新的cache缓存
     *
     * @param maximumSize 最大缓存量
     * @param duration    超时时间
     * @param minutes     单位
     *                    maximumSize  设置缓存的最大容量，超出后删除不太可能再次使用的条目
     *                    expireAfterWrite  设置缓存在无引用指定时间后失效
     *                    concurrencyLevel  设置并发级别为1
     *                    recordStats  开启缓存统计
     * @return {@link Cache <K, V> }
     * <AUTHOR>
     * @date 2022/12/30 20:20
     */
    public static <K, V> Cache<K, V> getNewCache(int maximumSize, int duration, TimeUnit minutes) {
        return CacheBuilder.newBuilder()
                .maximumSize(maximumSize)
                .expireAfterAccess(duration, minutes)
                .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                .recordStats()
                .build();
    }

    /**
     * 获取value值
     *
     * @param cache  缓存
     * @param key    key值
     * @param loader 获取value的方法
     * @return {@link V }
     * <AUTHOR>
     * @date 2022/12/30 23:10
     */
    public static <K, V> V computeIfAbsent(Cache<K, V> cache, K key, FunctionP0<? extends V> loader) {
        V value = cache.getIfPresent(key);
        if (Objects.nonNull(value)) {
            return value;
        }
        synchronized (CacheUtils.class) {
            value = cache.getIfPresent(key);
            if (Objects.nonNull(value)) {
                return value;
            }
            // 写操作进行加锁
            value = loader.run();
            cache.put(key, value);
            return value;
        }
    }

    /**
     * 删除缓存中的数据
     *
     * @param cache 缓存
     * @param key   key值
     * <AUTHOR>
     * @date 2022/12/30 23:10
     */
    public static boolean invalidate(Cache cache, Object key) {
        if (Objects.isNull(cache) || Objects.isNull(key)) {
            return false;
        }
        cache.invalidate(key);
        return true;
    }

}
