package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.enums.ApolloKeyEnum;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.ReconInnerTransferService;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.BillRedisKeyEnum;
import com.upex.reconciliation.service.common.delay.DelayedQueueManager;
import com.upex.reconciliation.service.dao.entity.BillCapitalOrder;
import com.upex.reconciliation.service.dao.entity.CapitalOrder;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.ApolloReconciliationCapitalOrderConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.model.dto.InnerTransferDTO;
import com.upex.reconciliation.service.service.impl.CapitalOrderService;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.TimeoutUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReconInnerTransferServiceImpl implements ReconInnerTransferService {

    // Lua脚本
    public static final String LUA_SCRIPT = "local redisTxIdPartitionKey = KEYS[1]\n" +
            "local innerTransferDTO = ARGV[1]\n" +
            "local bizTime = tonumber(ARGV[2])\n" +
            "local expireMinutes = tonumber(ARGV[3])\n" +
            "redis.call('ZADD', redisTxIdPartitionKey, bizTime, innerTransferDTO)\n" +
            "redis.call('EXPIRE', redisTxIdPartitionKey, expireMinutes)\n" +
            "return 1";

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private CapitalOrderService capitalOrderService;

    @Resource
    private AlarmNotifyService alarmNotifyService;
    @Resource
    private DelayedQueueManager delayedQueueManager;

    @Override
    public void reportedOrderData(List<CapitalOrder> capitalOrders) {
        if (CollectionUtils.isEmpty(capitalOrders)) {
            log.info("ReconInnerTransferServiceImpl capitalOrders is empty");
            return;
        }
        StringBuffer logString = new StringBuffer();
        for (CapitalOrder capitalOrder : capitalOrders) {
            logString.append(" orderId:").append(capitalOrder.getOrderId())
                    .append(" txId:").append(capitalOrder.getTxId())
                    .append(" bizType:").append(capitalOrder.getBizType())
                    .append(" bizSubType:").append(capitalOrder.getBizSubType());
        }

        log.info("ReconInnerTransferServiceImpl reportedOrderData capitalOrders:{}", logString);

        ApolloReconciliationCapitalOrderConfig apolloConfig = ReconciliationApolloConfigUtils.getApolloObjectConfig(ApolloKeyEnum.CAPITAL_ORDER_APOLLO_CONFIG, ApolloReconciliationCapitalOrderConfig.class);
        // 订单数据 key：orderId value：txId，放入redis中
        Map<String, String> orderTxIdMap = capitalOrders.stream()
                .filter(order -> order.getTxId()!= null)
                .filter(order -> order.getBizSubType() != null && apolloConfig.getInnerOrderBizSubTypeSet().contains(order.getBizSubType()))
                .collect(Collectors.toMap(order -> order.getOrderId().toString(), CapitalOrder::getTxId));
        if (MapUtils.isEmpty(orderTxIdMap)) {
            log.info("ReconInnerTransferServiceImpl reportedOrderData orderTxIdMap is empty");
            return;
        }
        redisTemplate.opsForHash().putAll(BillRedisKeyEnum.RECON_INNER_TRANSFER_ORDER_KEY.getKey(), orderTxIdMap);
        redisTemplate.expire(BillRedisKeyEnum.RECON_INNER_TRANSFER_ORDER_KEY.getKey(), Duration.ofMinutes(apolloConfig.getRedisExpireMinutes())); // 设置过期时间为1天
    }

    @Override
    public void billHandle(CommonBillChangeData commonBillChangeData) {
        log.info("ReconInnerTransferServiceImpl billHandle commonBillChangeData:{}", JSONObject.toJSONString(commonBillChangeData));

        byte accountType = commonBillChangeData.getAccountType();
        if (!AccountTypeEnum.toEnum(accountType).isSpot()) {
            // 只处理现货账户的内部转账
            return;
        }
        //订单类型过滤前置
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        if (!apolloBizConfig.getInnerWithdrawTypeSet().contains(commonBillChangeData.getBizType())
                && !apolloBizConfig.getInnerRechargeTypeSet().contains(commonBillChangeData.getBizType())) {
            return;
        }

        Long orderId = Long.valueOf(commonBillChangeData.getOrderId());
        String redisTxId = (String) redisTemplate.opsForHash().get(BillRedisKeyEnum.RECON_INNER_TRANSFER_ORDER_KEY.getKey(), commonBillChangeData.getOrderId());
        if (Objects.isNull(redisTxId)) {
            // 如果redis中没有，查询数据库，如果数据库中没有，则报警
            BillCapitalOrder dbCapitalOrderInfo = capitalOrderService.getCapitalOrderInfo(orderId);
            if (Objects.isNull(dbCapitalOrderInfo)) {
                log.info("ReconInnerTransferServiceImpl billHandle redisTxId is null and dbCapitalOrderInfo is null, orderId:{}", orderId);
                delayedQueueManager.offer(commonBillChangeData, billChangeData -> {
                    billHandle(billChangeData);
                    return null;
                });
                return;
            }
            redisTxId = dbCapitalOrderInfo.getTxId();
        }
        Integer partition = redisTxId.hashCode() % apolloBizConfig.getInnerTransferMod();
        String redisTxIdPartitionKey = String.format(BillRedisKeyEnum.RECON_INNER_TRANSFER_TX_ID_PARTITION_KEY.getKey(), partition);
        if (apolloBizConfig.getInnerWithdrawTypeSet().contains(commonBillChangeData.getBizType())) {
            // 类型是内部提现的流水
            // 根据订单号  查询redis 获取订单号和txId关联关系
            // 放入redis：key：key_{partition}     value：Map<txId, {txId, withdrawOrderId, withdrawAmount, rechargeOrderId， rechargeOrderAmount}>      时间戳
            InnerTransferDTO innerTransferDTO = new InnerTransferDTO(redisTxId, commonBillChangeData.getCoinId(), partition, orderId, commonBillChangeData.getChangePropSum(accountType), null, BigDecimal.ZERO);

            redisTemplate.opsForValue().set(redisTxId, JSONObject.toJSONString(innerTransferDTO), Duration.ofMinutes(apolloBizConfig.getRedisExpireMinutes()));
            addZSetAndExpireWithLua(redisTxIdPartitionKey, JSONObject.toJSONString(innerTransferDTO), commonBillChangeData.getBizTime().getTime(), apolloBizConfig.getRedisExpireMinutes());
        }else if (apolloBizConfig.getInnerRechargeTypeSet().contains(commonBillChangeData.getBizType())) {
            // 类型是内部充值的流水
            // 根据txId获取交易对手信息  如果redis有值，核对一致，删除
            String redisTxIdValue = (String) redisTemplate.opsForValue().get(redisTxId);
            if (Objects.isNull(redisTxIdValue)) {
                // 如果redis中没有txId,将充值订单信息放入redis（可能是扩缩容导致）
                InnerTransferDTO innerTransferDTO = new InnerTransferDTO(redisTxId, commonBillChangeData.getCoinId(), partition, null, BigDecimal.ZERO, orderId, commonBillChangeData.getChangePropSum(accountType));
                addZSetAndExpireWithLua(redisTxIdPartitionKey, JSONObject.toJSONString(innerTransferDTO), commonBillChangeData.getBizTime().getTime(), apolloBizConfig.getRedisExpireMinutes());
                log.info("ReconInnerTransferServiceImpl billHandle redisTxIdValue is null, redisTxId:{}", redisTxId);
                return;
            }
            InnerTransferDTO innerTransferDTO = JSONObject.parseObject(redisTxIdValue, InnerTransferDTO.class);
            innerTransferDTO.setRechargeInfo(orderId, commonBillChangeData.getChangePropSum(accountType));
            if (innerTransferDTO.reconCheck()) {
                // 核对一致，删除redis
                redisTemplate.delete(redisTxId);
                redisTemplate.opsForZSet().remove(redisTxIdPartitionKey, redisTxIdValue);
                log.info("ReconInnerTransferServiceImpl billHandle reconCheck success,redisTxId:{}, redisTxIdPartitionKey:{}, innerTransferDTO:{}", redisTxId, redisTxIdPartitionKey, JSONObject.toJSONString(innerTransferDTO));
            }else {
                // 核对不一致，报警
                log.info("ReconInnerTransferServiceImpl billHandle reconCheck fail,redisTxId:{}, redisTxIdPartitionKey:{}, innerTransferDTO:{}", redisTxId, redisTxIdPartitionKey, JSONObject.toJSONString(innerTransferDTO));
                // 如果redis中没有txId,将充值订单信息放入redis（可能是扩缩容导致）
                InnerTransferDTO innerRechargeDTO = new InnerTransferDTO(redisTxId, commonBillChangeData.getCoinId(), partition, null, BigDecimal.ZERO, orderId, commonBillChangeData.getChangePropSum(accountType));
                addZSetAndExpireWithLua(redisTxIdPartitionKey, JSONObject.toJSONString(innerRechargeDTO), commonBillChangeData.getBizTime().getTime(), apolloBizConfig.getRedisExpireMinutes());
            }
        }

    }

    @Override
    public void reconCheck() {
        Set<InnerTransferDTO> allInnerTransferDTOSet = new HashSet<>();
        Byte accountType = AccountTypeEnum.SPOT.getCode();
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        Integer innerTransferMod = apolloBizConfig.getInnerTransferMod();
        // 获取十分钟前到当前时间的所有数据
        long currentTimeMillis = System.currentTimeMillis();
        long min = currentTimeMillis - BillConstants.TEN_MILLIS_PER_MINUTE;

        for (Integer mod = 0; mod < innerTransferMod; mod++) {
            String redisTxIdPartitionKey = String.format(BillRedisKeyEnum.RECON_INNER_TRANSFER_TX_ID_PARTITION_KEY.getKey(), mod);
            // 大于10min 数据
            Set<Object> innerTransferDTOList = redisTemplate.opsForZSet().rangeByScore(redisTxIdPartitionKey, BillConstants.UTC_BEGIN_TIME, min);
            if (CollectionUtils.isEmpty(innerTransferDTOList)) {
                continue;
            }
            allInnerTransferDTOSet.addAll(innerTransferDTOList.stream().map(obj -> JSONObject.parseObject((String) obj, InnerTransferDTO.class)).collect(Collectors.toList()));
        }

        Map<String, List<InnerTransferDTO>> txIdOrderMap = allInnerTransferDTOSet.stream().collect(Collectors.groupingBy(InnerTransferDTO::getTxId));
        // 获取十分钟前的所有分区的txId
        // 如果有动态扩缩容，需要考虑
        // 内部转账有手续费，充值有两笔流水，一笔用户，一笔系统手续费
        Set<InnerTransferDTO> alarmSet = allInnerTransferDTOSet.stream()
                .collect(Collectors.toMap(InnerTransferDTO::getTxId, e -> e, (a, b) -> {
                    InnerTransferDTO result = new InnerTransferDTO();
                    result.setTxId(a.getTxId());
                    result.setPartition(a.getPartition());
                    result.setRechargeAmount(a.getRechargeAmount().add(b.getRechargeAmount()));
                    result.setWithdrawalAmount(a.getWithdrawalAmount().add(b.getWithdrawalAmount()));
                    // 如果a的订单号不为空，则不更新，否则更新
                    result.setRechargeOrderId(a.getRechargeOrderId() == null ? b.getRechargeOrderId() : a.getRechargeOrderId());
                    result.setWithdrawalOrderId(a.getWithdrawalOrderId() == null ? b.getWithdrawalOrderId() : a.getWithdrawalOrderId());
                    return result;
                }))
                .values().stream().filter(e -> {
                    if (e.reconCheck()) {
                        // 校验通过，删除redis
                        removeRedisOrderData(txIdOrderMap, e);
                        return false;
                    }else {
                        return true;
                    }
                }).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(alarmSet)) {
            log.info("ReconInnerTransferServiceImpl reconCheck alarmSet is empty");
            return;
        }

        log.info("ReconInnerTransferServiceImpl reconCheck alarmSet size:{}", alarmSet.size());
        Map<String, Object> alarmList = new HashMap<>();
        alarmList.put("startTime", min);
        alarmList.put("endTime", currentTimeMillis);
        alarmList.put("resultList", alarmSet);
        alarmNotifyService.alarm(AlarmTemplateEnum.INNER_TRANSFER_ALARM, alarmList);
    }

    @Override
    public void innerTransferRemoveOldRedisData() {
        Byte accountType = AccountTypeEnum.SPOT.getCode();
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        // 获取十分钟前到当前时间的所有数据
        long currentTimeMillis = System.currentTimeMillis();

        removeInnerTransferOrderData(apolloBizConfig, currentTimeMillis);
        removeFinanceOrderData(accountType, apolloBizConfig, currentTimeMillis);
    }

    private void removeInnerTransferOrderData(ApolloReconciliationBizConfig apolloBizConfig, long currentTimeMillis) {
        Set<InnerTransferDTO> allInnerTransferSet = new HashSet<>();
        Integer innerTransferMod = apolloBizConfig.getInnerTransferMod();
        // 将分钟转换为毫秒值
        long min = currentTimeMillis - TimeUnit.MINUTES.toMillis(apolloBizConfig.getRedisExpireMinutes()) - BillConstants.TEN_MILLIS_PER_MINUTE;
        for (Integer mod = 0; mod < innerTransferMod; mod++) {
            String redisTxIdPartitionKey = String.format(BillRedisKeyEnum.RECON_INNER_TRANSFER_TX_ID_PARTITION_KEY.getKey(), mod);
            // 大于10min 数据
            Set<Object> innerTransferDTOList = redisTemplate.opsForZSet().rangeByScore(redisTxIdPartitionKey, BillConstants.UTC_BEGIN_TIME, min);
            if (CollectionUtils.isEmpty(innerTransferDTOList)) {
                continue;
            }
            allInnerTransferSet.addAll(innerTransferDTOList.stream().map(obj -> JSONObject.parseObject((String) obj, InnerTransferDTO.class)).collect(Collectors.toList()));
        }
        // 删除内部转账redis数据
        if (CollectionUtils.isNotEmpty(allInnerTransferSet)) {
            Map<String, List<InnerTransferDTO>> txIdOrderMap = allInnerTransferSet.stream().collect(Collectors.groupingBy(InnerTransferDTO::getTxId));
            for (InnerTransferDTO innerTransferDTO : allInnerTransferSet) {
                removeRedisOrderData(txIdOrderMap, innerTransferDTO);
            }
        }
    }

    @Override
    public void removeFinanceOrderData(Byte accountType, ApolloReconciliationBizConfig apolloBizConfig, long currentTimeMillis) {
        long bgSolMin = currentTimeMillis - TimeUnit.MINUTES.toMillis(apolloBizConfig.getFinanceBgsolRedisExpireMinutes()) - BillConstants.TEN_MILLIS_PER_MINUTE;
        String redisTxIdPartitionKey = String.format(BillRedisKeyEnum.RECON_FINANCE_ORDER_KEY.getKey(), accountType);
        Set<Object> innerTransferDTOList = redisTemplate.opsForZSet().rangeByScore(redisTxIdPartitionKey, BillConstants.UTC_BEGIN_TIME, bgSolMin);
        // 删除理财提现增量redis数据
        if (CollectionUtils.isNotEmpty(innerTransferDTOList)) {
            for (Object obj : innerTransferDTOList) {
                redisTemplate.opsForZSet().remove(redisTxIdPartitionKey, (String) obj);
            }
        }
    }


    private void removeRedisOrderData(Map<String, List<InnerTransferDTO>> txIdOrderMap, InnerTransferDTO transferDTO) {
        redisTemplate.delete(transferDTO.getTxId());
        List<InnerTransferDTO> innerTransferDTOList = txIdOrderMap.get(transferDTO.getTxId());
        if (CollectionUtils.isEmpty(innerTransferDTOList)) {
            return;
        }
        for (InnerTransferDTO innerTransferDTO : innerTransferDTOList) {
            String redisTxIdPartitionKey = String.format(BillRedisKeyEnum.RECON_INNER_TRANSFER_TX_ID_PARTITION_KEY.getKey(), innerTransferDTO.getPartition());
            redisTemplate.opsForHash().delete(BillRedisKeyEnum.RECON_INNER_TRANSFER_ORDER_KEY.getKey(), innerTransferDTO.getOrderIdString());
            redisTemplate.opsForZSet().remove(redisTxIdPartitionKey, JSONObject.toJSONString(innerTransferDTO));
        }
    }

    /**
     * 添加ZSet和过期时间，使用Lua脚本
     * @param redisTxIdPartitionKey
     * @param innerTransferDTOJson
     * @param score
     * @param expire 过期时间，单位：分钟
     *
     * <AUTHOR>
     * @date 2025/1/7 17:15
     */
    public void addZSetAndExpireWithLua(String redisTxIdPartitionKey, String innerTransferDTOJson, long score, long expire) {
        long expireSeconds = TimeoutUtils.toSeconds(expire, TimeUnit.MINUTES);

        // 执行Lua脚本
        redisTemplate.execute((RedisCallback<Long>) connection -> connection.eval(LUA_SCRIPT.getBytes(), ReturnType.INTEGER, 1, redisTxIdPartitionKey.getBytes(), innerTransferDTOJson.getBytes(), Long.toString(score).getBytes(), Long.toString(expireSeconds).getBytes()));
    }

}
