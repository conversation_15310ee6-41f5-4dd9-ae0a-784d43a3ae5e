package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class IsolatedMargintAccountInfoInnerRes {

    private InnerAssetRes baseAsset;

    private InnerAssetRes quoteAsset;

    /**
     *   "symbol": "BTCUSDT",
     *             "isolatedCreated": true,
     *             "enabled": true,
     *             "marginLevel": "0.********",
     *             "marginLevelStatus": "EXCESSIVE",
     *             "marginRatio": "0.********",
     *             "indexPrice": "10000.********",
     *             "liquidatePrice": "1000.********",
     *             "liquidateRate": "1.********",
     *             "tradeEnabled": true
     */
    private String symbol;
    private Boolean isolatedCreated;
    private Boolean enabled;
    private BigDecimal marginLevel;
    private String marginLevelStatus;
    private BigDecimal marginRatio;
    private BigDecimal indexPrice;
    private BigDecimal liquidatePrice;
    private BigDecimal liquidateRate;
    private Boolean tradeEnabled;


    @Data
    public class InnerAssetRes {
        /**
         * {
         *           "asset": "BTC",
         *           "borrowEnabled": true,
         *           "borrowed": "0.********",
         *           "free": "0.********",
         *           "interest": "0.********",
         *           "locked": "0.********",
         *           "netAsset": "0.********",
         *           "netAssetOfBtc": "0.********",
         *           "repayEnabled": true,
         *           "totalAsset": "0.********"
         *         }
         */
        private String asset;
        private Boolean borrowEnabled;
        private BigDecimal free;
        private BigDecimal locked;
        private BigDecimal borrowed;
        private BigDecimal interest;
        private BigDecimal netAsset;
        private BigDecimal netAssetOfBtc;
        private Boolean repayEnabled;
        private BigDecimal totalAsset;
    }


}
