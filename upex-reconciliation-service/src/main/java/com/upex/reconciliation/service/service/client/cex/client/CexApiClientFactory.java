package com.upex.reconciliation.service.service.client.cex.client;

import com.binance.connector.client.common.SystemUtil;
import com.upex.reconciliation.service.service.client.cex.config.binance.BinanceApiClientMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

@Service
public class CexApiClientFactory {

    @Autowired
    private List<ICexApiClient> cexApiClients;

    private Map<Integer, ICexApiClient> cexApiClientMap;


    @Resource
    BinanceApiClientMapping binanceApiClientMapping;


    private static final String USER_AGENT =
            String.format(
                    "binance-spot/2.0.0 (Java/%s; %s; %s)",
                    SystemUtil.getJavaVersion(), SystemUtil.getOs(), SystemUtil.getArch());


    @PostConstruct
    private void init() {
        cexApiClientMap = new HashMap<>();
        for (ICexApiClient cexApiClient : cexApiClients) {
            cexApiClientMap.put(cexApiClient.cexType(), cexApiClient);
        }

    }

    public ICexApiClient getCexApiClient(Integer type) {
        return cexApiClientMap.get(type);
    }
}
