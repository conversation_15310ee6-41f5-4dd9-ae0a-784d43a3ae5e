package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.dao.bill.entity.OldBillConfig;
import com.upex.reconciliation.service.dao.mapper.OldBillConfigMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class OldBillConfigService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "oldBillConfigMapper")
    private OldBillConfigMapper oldBillConfigMapper;

    public OldBillConfig selectByTypeAndParam(Byte accountType,
                                              String accountParam) {
        return dbHelper.doDbOpInBillMaster(() -> oldBillConfigMapper.selectByTypeAndParam(accountType, accountParam));
    }

}
