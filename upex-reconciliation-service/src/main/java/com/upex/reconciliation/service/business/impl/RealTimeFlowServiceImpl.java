package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.fiat.fund.facade.client.bill.FundAccountBillQueryFacade;
import com.fiat.fund.facade.model.request.bill.FundAccountQueryHistoryBillRequest;
import com.fiat.fund.facade.model.response.bill.FundAccountQueryBillHistoryResponse;
import com.google.common.base.Stopwatch;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.bill.dto.results.PageResponse;
import com.upex.commons.support.model.ResponseResult;
import com.upex.financial.dto.params.FinancialUserBillListQuery;
import com.upex.financial.facade.bill.FinancialBillInnerFeign;
import com.upex.margin.facade.api.ApiMarginCrossAssetsFlowFeignClient;
import com.upex.margin.facade.api.ApiMarginIsolatedAssetsFlowFeignClient;
import com.upex.margin.req.api.AssetsFlowReq;
import com.upex.margin.res.api.ApiResponse;
import com.upex.margin.res.api.AssetsFlowRes;
import com.upex.mixcontract.common.literal.enums.BusinessLineEnum;
import com.upex.mixcontract.common.literal.enums.ShowFlagEnum;
import com.upex.mixcontract.process.facade.feign.inner.InnerFinancialRecordFeignClient;
import com.upex.mixcontract.process.facade.params.query.FinancialRecordQueryParam;
import com.upex.mixcontract.process.facade.results.query.FinancialRecordDTO;
import com.upex.mixcontract.process.facade.results.query.FinancialRecordListResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.service.RealTimeFlowService;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.spot.dto.params.bill.SpotQueryBillApiParam;
import com.upex.spot.dto.result.bill.SpotBillInfoResult;
import com.upex.spot.facade.query.SpotAssetsQueryServiceClient;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;


/**
 * 实时流水服务
 *
 * <AUTHOR>
 * @date 2024/5/22 16:17
 */
@Slf4j
@Service
public class RealTimeFlowServiceImpl implements RealTimeFlowService {
    /**
     * 合约实时流水接口
     */
    @Resource
    private InnerFinancialRecordFeignClient innerFinancialRecordFeignClient;
    /**
     * 现货实时流水接口
     */
    @Resource
    private SpotAssetsQueryServiceClient spotAssetsQueryServiceClient;
    /**
     * 理财实时流水接口
     */
    @Resource
    private FinancialBillInnerFeign financialBillInnerFeign;

    /**
     * 杠杠全仓实时流水接口
     */
    @Resource
    private ApiMarginCrossAssetsFlowFeignClient apiMarginCrossAssetsFlowFeignClient;
    /**
     * 杠杆逐仓实时流水接口
     */
    @Resource
    private ApiMarginIsolatedAssetsFlowFeignClient apiMarginIsolatedAssetsFlowFeignClient;
    /**
     * otc实时流水接口
     */
    @Resource
    private FundAccountBillQueryFacade fundAccountBillQueryFacade;

    @Resource
    private CommonService commonService;


    @Override
    public List<AccountAssetsInfoResult> queryIncrFlows(Long userId, Date startDate, Date endDate, AccountTypeEnum accountTypeEnum) {
        List<AccountAssetsInfoResult> resultList;
        switch (accountTypeEnum) {
            case SPOT:
                resultList = querySpotFlows(userId, startDate, endDate, accountTypeEnum);
                break;
            case USDT_MIX_CONTRACT_BL:
            case USD_MIX_CONTRACT_BL:
            case USDC_MIX_CONTRACT_BL:
                resultList = queryMixContractFlows(userId, startDate, endDate, accountTypeEnum);
                break;
            case LEVER_ONE:
            case LEVER_FULL:
                resultList = queryLeverFlows(userId, startDate, endDate, accountTypeEnum);
                break;
            case FINANCIAL:
                resultList = queryFinancialFlows(userId, startDate, endDate, accountTypeEnum);
                break;
            case OTC:
                resultList = queryOtcFlows(userId, startDate, endDate, accountTypeEnum);
                break;
            default:
                resultList = new ArrayList<>();
        }
        return resultList;
    }


    private List<AccountAssetsInfoResult> queryOtcFlows(Long userId, Date startDate, Date endDate, AccountTypeEnum accountTypeEnum) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<AccountAssetsInfoResult> resultList = new ArrayList<>();
        Map<String, Integer> allCoinId2Name = commonService.getAllCoinId2Name();
        ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());

        FundAccountQueryHistoryBillRequest request = new FundAccountQueryHistoryBillRequest();
        request.setUserId(userId);
        request.setStartTime(startDate.getTime());
        request.setEndTime(endDate.getTime());
        request.setNextId(0L);
        request.setPageSize(100);

        ResponseResult<List<FundAccountQueryBillHistoryResponse>> listResponseResult;
        List<FundAccountQueryBillHistoryResponse> data;
        AtomicLong requestCount = new AtomicLong(0);
        do {
            try {
                listResponseResult = fundAccountBillQueryFacade.queryBillHistoryByCondition(request);
                requestCount.incrementAndGet();
                data = listResponseResult.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    resultList.addAll(data.stream()
                            .map(otc -> otcBuildAccountAssetsInfoResult(otc, allCoinId2Name)).collect(Collectors.toList()));
                    request.setNextId(data.get(data.size() - 1).getId());
                }
            } catch (Exception e) {
                log.error("RealTimeFlowServiceImpl queryOtcFlows fundAccountBillQueryFacade.queryBillHistoryByCondition Exception, ", e);
                throw e;
            }
        } while (CollectionUtils.isNotEmpty(data));

        if (apolloBillConfig.getIncrFlowsProfitShowLogOpen()) {
            log.info("RealTimeFlowServiceImpl queryOtcFlows fundAccountBillQueryFacade.queryBillHistoryByCondition userId:{}, start:{}, end:{}, otcBillInfoResults:{} requestCount:{},time consume:{}",
                    userId, startDate.getTime(), endDate.getTime(), JSONObject.toJSONString(resultList), requestCount.get(), stopwatch.stop());
        }
        return resultList;
    }

    private AccountAssetsInfoResult otcBuildAccountAssetsInfoResult(FundAccountQueryBillHistoryResponse response, Map<String, Integer> allCoinId2Name) {
        AccountAssetsInfoResult result = new AccountAssetsInfoResult();
        result.setUserId(response.getUserId());
        result.setBizType(response.getType().toString());
        result.setCoinId(allCoinId2Name.get(response.getCurrencyCode().toUpperCase()));
        result.setProp1(response.getAmount());

        return result;
    }

    private List<AccountAssetsInfoResult> queryFinancialFlows(Long userId, Date startDate, Date endDate, AccountTypeEnum accountTypeEnum) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<AccountAssetsInfoResult> resultList = new ArrayList<>();
        ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());

        FinancialUserBillListQuery queryParam = new FinancialUserBillListQuery();
        queryParam.setLastEndId(0L);
        queryParam.setAccountId(userId);
        queryParam.setBeginTime(startDate.getTime());
        queryParam.setEndTime(endDate.getTime());
        queryParam.setPageSize(100L);

        PageResponse<AccountAssetsInfoResult> pageResponse;
        List<AccountAssetsInfoResult> data;
        AtomicLong requestCount = new AtomicLong(0);
        do {
            try {
                pageResponse = financialBillInnerFeign.queryUserBillList(queryParam);
                requestCount.incrementAndGet();
                data = pageResponse.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    resultList.addAll(data.stream().map(this::financialBuildAccountAssetsInfoResult).collect(Collectors.toList()));
                    queryParam.setLastEndId(pageResponse.getMaxId());
                }
            } catch (Exception e) {
                log.error("RealTimeFlowServiceImpl queryFinancialFlows financialBillInnerFeign.queryUserBillList Exception, ", e);
                throw e;
            }
        } while (CollectionUtils.isNotEmpty(data));

        if (apolloBillConfig.getIncrFlowsProfitShowLogOpen()) {
            log.info("RealTimeFlowServiceImpl queryFinancialFlows financialBillInnerFeign.queryUserBillList userId:{}, start:{}, end:{} financialBillInfoResults:{} requestCount:{} time consume:{}",
                    userId, startDate.getTime(), endDate.getTime(), JSONObject.toJSONString(resultList), requestCount.get(), stopwatch.stop());
        }
        return resultList;
    }


    private List<AccountAssetsInfoResult> queryLeverFlows(Long userId, Date startDate, Date endDate, AccountTypeEnum accountTypeEnum) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<AccountAssetsInfoResult> resultList = new ArrayList<>();
        Map<String, Integer> allCoinId2Name = commonService.getAllCoinId2Name();
        ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());

        AssetsFlowReq req = new AssetsFlowReq();
        req.setPageSize(100);
        req.setStartTime(startDate.getTime());
        req.setEndTime(endDate.getTime());
        req.setIsPre(true);
        req.setUserId(userId);

        ApiResponse<AssetsFlowRes> response = null;
        List<AssetsFlowRes> data = null;
        AtomicLong requestCount = new AtomicLong(0);
        do {
            try {
                switch (accountTypeEnum) {
                    case LEVER_ONE:
                        response = apiMarginIsolatedAssetsFlowFeignClient.list(req);
                        break;
                    case LEVER_FULL:
                        response = apiMarginCrossAssetsFlowFeignClient.list(req);
                        break;
                    default:
                        throw new RuntimeException("accountTypeEnum error");
                }
                requestCount.incrementAndGet();
                data = response.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    resultList.addAll(data.stream()
                            .filter(levelBill -> levelBill.getCTime().compareTo(startDate.getTime()) >= 0
                                    && levelBill.getCTime().compareTo(endDate.getTime()) <= 0)
                            .map(levelFlow -> leverBuildAccountAssetsInfoResult(userId, levelFlow, allCoinId2Name)).collect(Collectors.toList()));
                    req.setMinId(response.getMaxId());
                }
            } catch (Exception e) {
                log.error("RealTimeFlowServiceImpl queryLeverFlows apiMarginCrossAssetsFlowFeignClient.list Exception, ", e);
                throw e;
            }
        } while (CollectionUtils.isNotEmpty(data));
        if (apolloBillConfig.getIncrFlowsProfitShowLogOpen()) {
            log.info("RealTimeFlowServiceImpl queryLeverFlows apiMarginCrossAssetsFlowFeignClient.list userId:{}, start:{}, end:{}, accountType:{}, marginBillInfoResults:{} requestCount:{} time consume:{}",
                    userId, startDate.getTime(), endDate.getTime(), accountTypeEnum, JSONObject.toJSONString(resultList), requestCount.get(), stopwatch.stop());
        }
        return resultList;
    }

    private List<AccountAssetsInfoResult> queryMixContractFlows(Long userId, Date startDate, Date endDate, AccountTypeEnum accountTypeEnum) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<AccountAssetsInfoResult> resultList = new ArrayList<>();
        ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());

        Map<String, Integer> allCoinId2Name = commonService.getAllCoinId2Name();

        FinancialRecordQueryParam param = new FinancialRecordQueryParam();
        BusinessLineEnum businessLineEnum = BusinessLineEnum.valueOf(accountTypeEnum.getBizTag());
        param.setBusinessLine(businessLineEnum.getCode());
        param.setSecondBusinessLine("N/A");
        param.setPageSize(100);
        param.setStartTime(startDate.getTime());
        param.setEndTime(endDate.getTime());
        param.setUserId(userId);
        param.setPre(false);
        param.setTimeLimit(true);

        FinancialRecordListResult financialResult;
        List<FinancialRecordDTO> financialRecordList;
        AtomicLong requestCount = new AtomicLong(0);
        do {
            try {
                financialResult = innerFinancialRecordFeignClient.getFinancialList(param);
                requestCount.incrementAndGet();
                financialRecordList = financialResult.getFinancialRecordList();
                if (CollectionUtils.isNotEmpty(financialRecordList)) {
                    resultList.addAll(financialRecordList.stream().map(mc -> mixBuildAccountAssetsInfoResult(mc, allCoinId2Name)).collect(Collectors.toList()));
                    param.setLastEndId(financialResult.getEndId());
                }
            } catch (Exception e) {
                log.error("RealTimeFlowServiceImpl queryMixContractFlows innerFinancialRecordFeignClient.getFinancialList Exception, ", e);
                throw e;
            }
        } while (CollectionUtils.isNotEmpty(financialRecordList));

        if (apolloBillConfig.getIncrFlowsProfitShowLogOpen()) {
            log.info("RealTimeFlowServiceImpl queryMixContractFlows innerFinancialRecordFeignClient.getFinancialList userId:{}, start:{}, end:{}, accountType:{}, financialRecordList:{}, requestCount:{}, time consume:{}",
                    userId, startDate.getTime(), endDate.getTime(), accountTypeEnum, JSONObject.toJSONString(resultList), requestCount.get(), stopwatch.stop());
        }
        return resultList;
    }

    private List<AccountAssetsInfoResult> querySpotFlows(Long userId, Date startDate, Date endDate, AccountTypeEnum accountTypeEnum) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<AccountAssetsInfoResult> resultList = new ArrayList<>();
        SpotQueryBillApiParam spotQueryBillDTO = new SpotQueryBillApiParam();
        spotQueryBillDTO.setUserId(userId);
        spotQueryBillDTO.setStartTime(startDate.getTime());
        spotQueryBillDTO.setEndTime(endDate.getTime());
        spotQueryBillDTO.setLimit(100);
        spotQueryBillDTO.setShowFlag(ShowFlagEnum.SHOW.getCode());
        ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());

        List<SpotBillInfoResult> spotBillInfoResults;
        AtomicLong requestCount = new AtomicLong(0);
        do {
            try {
                spotBillInfoResults = spotAssetsQueryServiceClient.billListV2(spotQueryBillDTO);
                requestCount.incrementAndGet();
                if (CollectionUtils.isNotEmpty(spotBillInfoResults)) {
                    resultList.addAll(spotBillInfoResults.stream()
                            .filter(spotBill -> spotBill.getCreateDate().compareTo(startDate.getTime()) >= 0
                                    && spotBill.getCreateDate().compareTo(endDate.getTime()) <=0)
                            .map(spotBill -> spotBuildAccountAssetsInfoResult(userId, spotBill))
                            .collect(Collectors.toList()));
                    spotQueryBillDTO.setIdLessThan(spotBillInfoResults.get(spotBillInfoResults.size() - 1).getBillId());
                }
            } catch (Exception e) {
                log.error("RealTimeFlowServiceImpl getSpotBillInfoResults spotBillsQueryClient.bills Exception, ", e);
                throw e;
            }
        } while (CollectionUtils.isNotEmpty(spotBillInfoResults));
        if (apolloBillConfig.getIncrFlowsProfitShowLogOpen()) {
            log.info("RealTimeFlowServiceImpl getSpotBillInfoResults spotBillsQueryClient.bills userId:{}, start:{}, end:{}, spotBillInfoResults:{} requestCount:{},time consume:{}",
                    userId, startDate.getTime(), endDate.getTime(), JSONObject.toJSONString(resultList), requestCount.get(), stopwatch.stop());
        }
        return resultList;
    }

    /**
     * 现货构建AccountAssetsInfoResult对象
     *
     * @param userId
     * @param spotBillInfoResult
     * <AUTHOR>
     * @date 2024/5/22 16:24
     */
    private AccountAssetsInfoResult spotBuildAccountAssetsInfoResult(Long userId, SpotBillInfoResult spotBillInfoResult) {
        AccountAssetsInfoResult accountAssetsInfoResult = new AccountAssetsInfoResult();
        accountAssetsInfoResult.setUserId(userId);
        accountAssetsInfoResult.setBizType(spotBillInfoResult.getBizType().toString());
        accountAssetsInfoResult.setCoinId(spotBillInfoResult.getCoinId());
        // 余额变动，锁仓变动和冻结变动没有返回
        accountAssetsInfoResult.setProp1(new BigDecimal(spotBillInfoResult.getAmount()));

        return accountAssetsInfoResult;
    }

    private AccountAssetsInfoResult mixBuildAccountAssetsInfoResult(FinancialRecordDTO mixBill, Map<String, Integer> allCoinId2Name) {
        AccountAssetsInfoResult accountAssetsInfoResult = new AccountAssetsInfoResult();
        accountAssetsInfoResult.setUserId(mixBill.getUserId());
        accountAssetsInfoResult.setBizType(mixBill.getBizType());
        accountAssetsInfoResult.setCoinId(allCoinId2Name.get(mixBill.getTokenId().toUpperCase()));
        // 余额变动,类型中区分 逐仓仓位保证金变动
        accountAssetsInfoResult.setProp1(mixBill.getBalanceChange());

        return accountAssetsInfoResult;
    }

    private AccountAssetsInfoResult leverBuildAccountAssetsInfoResult(Long userId, AssetsFlowRes levelFlow, Map<String, Integer> allCoinId2Name) {
        AccountAssetsInfoResult result = new AccountAssetsInfoResult();
        result.setUserId(userId);
        // 已借 类型区分
        result.setBizType(levelFlow.getMarginType());
        // 转换coinId
        result.setCoinId(allCoinId2Name.get(levelFlow.getCoin()));
        // 可用，冻结流水中没有
        result.setProp1(levelFlow.getAmount());

        return result;
    }

    private AccountAssetsInfoResult financialBuildAccountAssetsInfoResult(AccountAssetsInfoResult financial) {
        AccountAssetsInfoResult result = new AccountAssetsInfoResult();
        result.setUserId(financial.getUserId());
        // 已借 类型区分
        result.setBizType(financial.getBizType());
        // 转换coinId
        result.setCoinId(financial.getCoinId());
        // 余额
        result.setProp1(financial.getProp1());
        // 现货资金
//        result.setProp2(financial.getProp2());
        // 利息
//        result.setProp3(financial.getProp3());

        return result;
    }

    public static void main(String[] args) {
        System.out.println(SerialNoGenerator.getMinIdByTime(new Date(1738836300000L)));
        System.out.println(SerialNoGenerator.getMinIdByTime(new Date(1738836940980L)));
    }
}
