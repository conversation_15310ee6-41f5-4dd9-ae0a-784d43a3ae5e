package com.upex.reconciliation.service.business.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.business.ReconUserPbCacheService;
import com.upex.user.enums.UserIdentityEnum;
import com.upex.user.protobuf.cache.UserPbCacheService;
import com.upex.user.protobuf.vo.UserBaseRedisDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 用户权限服务实现类
 *
 * <AUTHOR>
 * @Date 2025/1/13
 */
@Slf4j
@Service
public class ReconUserPbCacheServiceImpl implements ReconUserPbCacheService {

    @Resource
    private UserPbCacheService userPbCacheService;

    @Override
    public Pair<Boolean, String> checkUserPermission(Long userId, Long permissionCode) {
        Pair<Boolean, String> pair = Pair.of(Boolean.TRUE, null);
        try {
            userPbCacheService.checkPermissionDefault(userId, permissionCode);
        } catch (ApiException e) {
            log.warn("checkUserPermission userId:{}, permission:{} error message:{}", userId, permissionCode, e.getMessage());
            pair = Pair.of(Boolean.FALSE, e.getMsg());
        }
        return pair;
    }

    @Override
    public Pair<Boolean, String> checkUserPermissions(Long userId, List<Long> permissionCodeList) {
        Pair<Boolean, String> pair = Pair.of(Boolean.TRUE, null);
        if (CollectionUtil.isEmpty(permissionCodeList)) {
            return pair;
        }
        for (Long code : permissionCodeList) {
            pair = checkUserPermission(userId, code);
            if (Objects.equals(Boolean.FALSE, pair.getKey())) {
                break;
            }
        }
        return pair;
    }

    @Override
    public Boolean isSubAccount(Long userId) {
        UserBaseRedisDO userBaseDO = userPbCacheService.getUserBaseDO(userId);
        return Objects.nonNull(userBaseDO) && userBaseDO.isChildFlag();
    }

    @Override
    public Boolean isBroker(Long userId) {
        UserBaseRedisDO userBaseDO = userPbCacheService.getUserBaseDO(userId);
        return Objects.nonNull(userBaseDO) && UserIdentityEnum.isBroker(userBaseDO.getUserIdentity());
    }
}
