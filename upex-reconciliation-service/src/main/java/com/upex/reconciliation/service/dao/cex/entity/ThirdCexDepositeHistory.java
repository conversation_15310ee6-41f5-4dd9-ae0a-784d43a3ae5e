package com.upex.reconciliation.service.dao.cex.entity;

import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class ThirdCexDepositeHistory {

    private Long id;
    private Integer cexType;
    private String cexUserId;
    private String cexEmail;
    private String parentUserId;
    private String depositeId;
    private String coinName;
    private BigDecimal amount;
    private String network;
    private String address;
    private String addressTag;
    private Integer status;
    private String txId;
    private Date depositeBeginTime;
    private Date depositeEndTime;
    private Integer walletType;
    private Integer transferType;
    private String confirmTimes;
    private Integer unlockConfirm;
    private BigDecimal fee1;
    private BigDecimal fee2;
    private String fee1Coin;
    private String fee2Coin;
    private Date checkSyncTime;
    private Date createTime;
    private Date updateTime;
    private Long version;


}
