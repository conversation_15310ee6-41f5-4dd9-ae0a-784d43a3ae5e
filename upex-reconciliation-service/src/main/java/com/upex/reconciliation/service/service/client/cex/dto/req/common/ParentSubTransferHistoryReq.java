package com.upex.reconciliation.service.service.client.cex.dto.req.common;

import lombok.Data;

import java.util.Date;

@Data
public class ParentSubTransferHistoryReq extends CommonReq{
    private Date startTime;
    private Date endTime;
    private Date checkSyncTime;
    private String fromAccountType;

    private String toAccountType;

    public ParentSubTransferHistoryReq(Integer cexType, String cexUserId, String apiKey, String privateKey, Date startTime, Date endTime,Date checkSyncTime) {
        super(cexType, cexUserId, apiKey, privateKey);
        this.startTime = startTime;
        this.endTime = endTime;
        this.checkSyncTime = checkSyncTime;
    }
}
