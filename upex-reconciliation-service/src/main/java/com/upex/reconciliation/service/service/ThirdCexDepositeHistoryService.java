package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexDepositeHistory;
import com.upex.reconciliation.service.dao.mapper.cex.ThirdCexDepositeHistoryMapper;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.UserDepositeHistoryListReq;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ThirdCexDepositeHistoryService {

    @Resource
    BillDbHelper billDbHelper;

    @Resource
    ThirdCexDepositeHistoryMapper thirdCexDepositeHistoryMapper;

    public Integer insert(ThirdCexDepositeHistory history) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexDepositeHistoryMapper.insert(history));
    }

    public Integer batchInsert(List<ThirdCexDepositeHistory> records) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexDepositeHistoryMapper.batchInsert(records));
    }

    public List<ThirdCexDepositeHistory> selectPageByUserIds( List<String> cexUserIds,  UserDepositeHistoryListReq userDepositeHistoryListReq){
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexDepositeHistoryMapper.selectPageByUserIds(cexUserIds, userDepositeHistoryListReq));
    }

    public int countPageByUserIds( List<String> cexUserIds,  UserDepositeHistoryListReq userDepositeHistoryListReq){
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexDepositeHistoryMapper.countPageByUserIds(cexUserIds, userDepositeHistoryListReq));
    }

    public int deleteByDepositeIds(List<String> depositeIds) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexDepositeHistoryMapper.deleteByDepositeIds(depositeIds));
    }


}
