package com.upex.reconciliation.service.service.client.cex.dto.res.common;

import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CommonWithdrawHistoryInnerRes {
    private String drawId;
    private Integer cexType;
    private String cexUserId;
    private String cexEmail;
    private String parentCexUserId;
    private String coinName;
    private String network;
    private Integer status;
    private String txId;
    private String address;
    private String addressTag;
    private String asset;
    private BigDecimal amount;
    private Date withdrawBeginTime;
    private Date withdrawEndTime;
    private Integer transferType;
    private String info;
    private Integer confirmNo;
    private Integer walletType;
    private BigDecimal fee1;
    private BigDecimal fee2;
    private String fee1Coin;
    private String fee2Coin;
    private Long version;
    private Date createTime;
    private Date updateTime;

    public String getAddress() {
        return HmacUtil.decrypt(address);
    }
}
