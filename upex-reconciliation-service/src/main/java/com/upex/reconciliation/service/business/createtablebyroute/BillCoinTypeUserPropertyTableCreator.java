package com.upex.reconciliation.service.business.createtablebyroute;

import com.upex.reconciliation.service.model.config.ReconTableRouteConfig;
import com.upex.reconciliation.service.service.BillCoinTypeUserPropertyService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
public class BillCoinTypeUserPropertyTableCreator extends AbstractTableCreator {
    @Resource
    private BillCoinTypeUserPropertyService billCoinTypeUserPropertyService;

    @Override
    public String getTableType() {
        return "billCoinTypeUserProperty";
    }

    @Override
    public void createTable(String accountType) {
        ReconTableRouteConfig.TableRouteRule tableRouteRule = getTableRouteRule(accountType);
        if (tableRouteRule == null) {
            return;
        }
        if (ReconTableRouteConfig.TableRouteRuleEnum.MONTH.name().equalsIgnoreCase(tableRouteRule.getRule())) {
            throw new RuntimeException(getTableType() + "不支持创建表类型" + tableRouteRule.getRule());
        }
        billCoinTypeUserPropertyService.createTableForDay(Byte.valueOf(accountType), Objects.requireNonNullElse(tableRouteRule.getCreateDay(), 7));
    }
}
