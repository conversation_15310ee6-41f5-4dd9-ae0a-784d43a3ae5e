package com.upex.reconciliation.service.business.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.upex.assets.dto.params.InnerTransferRecordsReqV2Param;
import com.upex.assets.dto.result.InnerTransferInfoResult;
import com.upex.assets.facade.inner.TransferServiceInnerClient;
import com.upex.commons.support.util.SiteUtil;
import com.upex.reconciliation.facade.dto.results.ReconTotalAssetsDetailVo;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.params.ReconUserAssetsBySnapShotTimeParams;
import com.upex.reconciliation.service.business.*;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.module.impl.BillUserCheckModule;
import com.upex.reconciliation.service.business.ruleengine.dto.SystemUserAssetsMonitorDto;
import com.upex.reconciliation.service.business.ruleengine.dto.SystemUserRelationshipDto;
import com.upex.reconciliation.service.business.ruleengine.dto.TimeWindowsFlows;
import com.upex.reconciliation.service.business.ruleengine.mvel.MvelEngine;
import com.upex.reconciliation.service.business.ruleengine.dto.*;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.config.UserPermissionConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.service.impl.BillCoinUserPropertySnapshotService;
import com.upex.reconciliation.service.utils.BillCoinCalculationUtils;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.NumberUtil;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.user.facade.utils.SiteCodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RuleEngineDataServiceImpl implements RuleEngineDataService {
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    @Resource
    private ReconUserAssetsSnapShotService reconUserAssetsSnapShotService;
    @Resource
    private BillBizTypeConfigService billBizTypeConfigService;
    @Resource
    private BillAllConfigService billAllConfigService;
    @Resource
    private BillCoinTypeUserPropertyService billCoinTypeUserPropertyService;
    @Resource
    private CommonService commonService;
    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;
    @Resource
    private BillCoinUserPropertySnapshotService billCoinUserPropertySnapshotService;
    @Resource
    private AssetsBillCoinPropertyService assetsBillCoinPropertyService;
    @Resource
    private AssetsBillCoinTypePropertyService assetsBillCoinTypePropertyService;
    @Resource
    private BillEngineManager billEngineManager;
    @Resource
    private BillCoinPropertyService billCoinPropertyService;
    @Resource
    private BillFlowCheckService billFlowCheckService;
    @Resource
    private TransferServiceInnerClient transferServiceInnerClient;
    @Resource
    private ReconUserPbCacheService reconUserPbCacheService;
    @Resource
    private BillContractProfitTransferService billContractProfitTransferService;

    @Override
    public BigDecimal getPropSumByBillChangeData(CommonBillChangeData currentBill) {
        BillLogicGroup billLogicGroup = billEngineManager.getBillLogicGroup(currentBill.getAccountType());
        if (billLogicGroup == null) {
            return BigDecimal.ZERO;
        }
        BillUserCheckModule billUserCheckModule = (BillUserCheckModule) billLogicGroup.getUserCheckModule();
        Map<Integer, BillCoinUserProperty> userCoinPropertyMap = billUserCheckModule.getUserCoinPropertyMap(currentBill.getAccountId());
        if (userCoinPropertyMap != null && userCoinPropertyMap.size() > 0) {
            long lastMinute = currentBill.getBizTime().getTime() / BillConstants.ONE_MINE_MIL_SEC * BillConstants.ONE_MINE_MIL_SEC;
            Map<Integer, PriceVo> priceVos = commonService.getCoinIdRatesMapCache(lastMinute);
            BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(AccountTypeEnum.toEnum(currentBill.getAccountType()).getCode());
            BigDecimal totalUsdtValue = BigDecimal.ZERO;
            // 计算折U值
            for (Map.Entry<Integer, BillCoinUserProperty> entry : userCoinPropertyMap.entrySet()) {
                Integer coinId = entry.getKey();
                BillCoinUserProperty billCoinUserProperty = entry.getValue();
                PriceVo priceVo = priceVos.getOrDefault(coinId, new PriceVo(1, BigDecimal.ZERO, System.currentTimeMillis()));
                BigDecimal propSum = billCheckService.getPropSumByUserProperty(billCoinUserProperty);
                BigDecimal usdtValue = propSum.multiply(priceVo.getPrice());
                totalUsdtValue = totalUsdtValue.add(usdtValue);
            }
            return totalUsdtValue;
        }
        return BigDecimal.ZERO;
    }

    @Override
    public Date getCheckTime(SystemUserRelationshipDto systemUserRelationshipDto) {
        String accountType = systemUserRelationshipDto.getAccountType();
        List<String> accountTypes = Lists.newArrayList(accountType);
        BillAllConfig minBillConfig = billAllConfigService.getMinCheckOkBillConfig(accountTypes);
        return minBillConfig.getCheckOkTime();
    }

    @Override
    public BigDecimal getBeginAssetsData(Long userId, String accountType, Date beginTime) {
        ReconUserAssetsBySnapShotTimeParams params = new ReconUserAssetsBySnapShotTimeParams();
        params.setUserId(userId);
        params.setSnapShotTime(beginTime.getTime());
        params.setAccountTypes(Lists.newArrayList(accountType));
        ReconTotalAssetsDetailVo userAssetsBySnapShotTime = reconUserAssetsSnapShotService.getUserAssetsBySnapShotTime(params);
        return userAssetsBySnapShotTime.getTotalBalance();
    }

    @Override
    public BigDecimal getUserAssetsData(Long userId, List<String> accountType, Date beginTime) {
        ReconUserAssetsBySnapShotTimeParams params = new ReconUserAssetsBySnapShotTimeParams();
        params.setUserId(userId);
        params.setSnapShotTime(beginTime.getTime());
        params.setAccountTypes(accountType);
        ReconTotalAssetsDetailVo userAssetsBySnapShotTime = reconUserAssetsSnapShotService.getUserAssetsBySnapShotTime(params);
        return userAssetsBySnapShotTime.getTotalBalance();
    }

    @Override
    public List<SystemUserAssetsRatioMonitorDto> calculateUserAssetsURatioMonitor(List<SystemUserAssetsRatioParam> systemUserAssetsRatioParamList) {
        List<SystemUserAssetsRatioMonitorDto> results = Lists.newArrayList();
        // 获取最小检查时间
        for (SystemUserAssetsRatioParam systemUserAssetsRatioParam : systemUserAssetsRatioParamList) {
            BillAllConfig minCheckOkBillConfig = billAllConfigService.getMinCheckOkBillConfig(systemUserAssetsRatioParam.getSubSystemList());
            Date checkOkTime = minCheckOkBillConfig.getCheckOkTime();
            Date preCheckOkTime = DateUtil.addHour(checkOkTime, BillConstants.NEG_ONE);
            BigDecimal preUserAssetsData = getUserAssetsData(systemUserAssetsRatioParam.getUserId(), systemUserAssetsRatioParam.getSubSystemList(), preCheckOkTime);
            BigDecimal userAssetsData = getUserAssetsData(systemUserAssetsRatioParam.getUserId(), systemUserAssetsRatioParam.getSubSystemList(), checkOkTime);
            SystemUserAssetsRatioMonitorDto build = SystemUserAssetsRatioMonitorDto.builder()
                    .userId(systemUserAssetsRatioParam.getUserId())
                    .startTime(DateUtil.date2str(preCheckOkTime))
                    .endTime(DateUtil.date2str(checkOkTime))
                    .beginAmount(preUserAssetsData)
                    .endAmount(userAssetsData)
                    .build();
            build.setRatio(build.calculateRatio());
            results.add(build);
        }
        return results;
    }

    @Override
    public List<SystemUserAssetsMonitorDto> calculateUserAssetsMonitorData(List<TimeWindowsFlows> timeWindowsFlowsList) {
        if (CollectionUtils.isEmpty(timeWindowsFlowsList)) {
            return null;
        }

        List<SystemUserAssetsMonitorDto> results = Lists.newArrayList();
        for (TimeWindowsFlows timeWindowsFlows : timeWindowsFlowsList) {
            Long userId = timeWindowsFlows.getUserId();
            String accountType = timeWindowsFlows.getAccountType();
            Date beginTime = timeWindowsFlows.getStartTime();
//           不用判空，为空走不到这一步
            List<BillCoinTypeUserProperty> billCoinTypeUserProperties = timeWindowsFlows.getNegativeFlows();

            BigDecimal beginAssetsData = getBeginAssetsData(userId, accountType, beginTime);

            String accountTypeNumber = accountType.split(BillConstants.SEPARATOR)[0];
            Byte accountTypeByte = Byte.valueOf(accountTypeNumber);
            // 汇率使用期初汇率，排除汇率影响
            Map<Integer, PriceVo> ratesMapCache = commonService.getCoinIdRatesMapCache(beginTime.getTime());

            // 返回所有负值流水的折u值
            BigDecimal flowAmount = billCoinTypeUserProperties.stream()
                    .filter(element -> element.getChangePropSum(accountTypeByte).compareTo(BigDecimal.ZERO) < 0)
                    .map(element -> {
                        BigDecimal changePropSum = element.getChangePropSum(accountTypeByte);
                        BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(element.getCoinId(), ratesMapCache);
                        return changePropSum.multiply(rate);
                    }).reduce(BigDecimal.ZERO, BigDecimal::add);
            results.add(SystemUserAssetsMonitorDto.builder()
                    .userId(userId)
                    .accountTypeName(AccountTypeEnum.toEnum(accountTypeByte).getDesc())
                    .startTime(DateUtil.date2str(beginTime))
                    .endTime(DateUtil.date2str(timeWindowsFlows.getEndTime()))
                    .beginAmount(beginAssetsData)
                    .payAmount(flowAmount)
                    .build());
        }

        return results;
    }

    /**
     * 获取时间窗口内用户流水数量
     */
    @Override
    public TimeWindowsFlows getTimeWindowsUserFlows(SystemUserRelationshipDto systemUserRelationshipDto, Date endTime, Integer windowIntervalTime) {
        Long userId = systemUserRelationshipDto.getUserId();
        String accountType = systemUserRelationshipDto.getAccountType();
        String accountTypeNumber = accountType.split(BillConstants.SEPARATOR)[0];

        Date beginTime = DateUtil.addMinute(endTime, windowIntervalTime * BillConstants.NEG_ONE);
        List<BillCoinTypeUserProperty> billCoinTypeUserProperties = billCoinTypeUserPropertyService.selectBetweenTime(userId, beginTime, endTime, Integer.valueOf(accountTypeNumber));
        if (CollectionUtils.isEmpty(billCoinTypeUserProperties)) {
            return null;
        }

        Byte accountTypeByte = Byte.valueOf(accountTypeNumber);
        List<BillCoinTypeUserProperty> negativeFlows = billCoinTypeUserProperties.stream()
                .filter(element -> element.getChangePropSum(accountTypeByte).compareTo(BigDecimal.ZERO) < 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(negativeFlows)) {
            return null;
        }

        return TimeWindowsFlows.builder()
                .startTime(beginTime)
                .endTime(endTime)
                .userId(userId)
                .accountType(accountType)
                .negativeFlows(negativeFlows)
                .build();
    }

    @Override
    public Map<String, BillBizTypeConfig> getBusinessBizTypeConfigMap(Byte accountType) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        return billBizTypeConfigService.getBizTypeConfigByMap(accountType, accountTypeEnum.getAccountParam());
    }

    @Override
    public Map<Integer, BigDecimal> getUserSnapshotAssets(Long userId, List<Byte> accountTypeList, Date snapshotTime, List<Integer> coinIdsList) {
        return billCoinUserPropertyService.getUserSnapshotAssets(userId, accountTypeList, snapshotTime, coinIdsList);
    }

    @Override
    public BigDecimal getUserSnapshotAssetsUsdt(Long userId, List<Byte> accountTypeList, Date snapshotTime, List<Integer> coinIdsList) {
        Map<Integer, BigDecimal> userCoinAssets = billCoinUserPropertyService.getUserSnapshotAssets(userId, accountTypeList, snapshotTime, coinIdsList);
        return calculateCoinSnapshotAssetsUsdt(userCoinAssets, snapshotTime);
    }

    @Override
    public Map<Integer, BigDecimal> getUserUnProfitTransferAssets(Long userId, List<Integer> toAccountTypeList, List<Integer> transferTypes, Date snapshotTime) {
        Map<Integer, BigDecimal> userCoinUnProfitMap = new HashMap<>();
        List<Byte> toAccountTypes = null;
        if (CollectionUtil.isNotEmpty(toAccountTypeList)) {
            toAccountTypes = toAccountTypeList.stream().map(Integer::byteValue).collect(Collectors.toList());
        }
        Date checkTime = snapshotTime;
        while (true) {
            Map<Integer, BigDecimal> unProfitTransferMap = billContractProfitTransferService.getUserUnProfitTransfer(userId, toAccountTypes, transferTypes, checkTime, snapshotTime);
            if (CollectionUtil.isEmpty(unProfitTransferMap)) {
                break;
            }
            BillCoinCalculationUtils.mergeCoinAssets(unProfitTransferMap, userCoinUnProfitMap);
            checkTime = DateUtil.addMinute(checkTime, -5);
            if (checkTime.before(DateUtil.addDay(snapshotTime, - 1))) {
                log.error("getUserUnProfitTransferAssets snapshotTime:{} query checkTime:{} exceeds one day", snapshotTime, checkTime);
                break;
            }
        }
        return userCoinUnProfitMap;
    }

    @Override
    public BigDecimal calculateCoinSnapshotAssetsUsdt(Map<Integer, BigDecimal> coinAssetsMap, Date snapshotTime) {
        if (CollectionUtil.isEmpty(coinAssetsMap)) {
            return BigDecimal.ZERO;
        }
        // 币种价格
        Map<Integer, PriceVo> coinPriceMap = getCoinIdRatesMapCache(snapshotTime);
        BigDecimal assetValueUsdt = BigDecimal.ZERO;
        // 计算用户usdt资产
        for (Integer coinId : coinAssetsMap.keySet()) {
            PriceVo priceVo = coinPriceMap.get(coinId);
            BigDecimal price = priceVo == null ? BigDecimal.ZERO : priceVo.getPrice();
            BigDecimal coinCount = coinAssetsMap.getOrDefault(coinId, BigDecimal.ZERO);
            assetValueUsdt = NumberUtil.add(assetValueUsdt, NumberUtil.multiply(coinCount, price));
        }
        return assetValueUsdt;
    }

    @Override
    public Map<Integer, BigDecimal> getCoinSnapshotAssets(String assetsType, Date snapshotTime, List<Integer> coinIdsList) {
        return assetsBillCoinPropertyService.getCoinSnapshotAssets(assetsType, snapshotTime, coinIdsList);
    }

    @Override
    public Map<Integer, BigDecimal> getCoinTypeSnapshotAssets(String assetsType, Date snapshotTime, List<Integer> coinIdsList, List<String> bizTypeList) {
        return assetsBillCoinTypePropertyService.getCoinTypeSnapshotAssets(assetsType, snapshotTime, coinIdsList, bizTypeList);
    }

    @Override
    public List<String> getBizInTypeList(Byte accountType) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        return billBizTypeConfigService.getBizInTypeList(accountType, accountTypeEnum.getAccountParam());
    }

    @Override
    public List<String> getBizOutTypeList(Byte accountType) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        return billBizTypeConfigService.getBizOutTypeList(accountType, accountTypeEnum.getAccountParam());
    }

    public BigDecimal getChangePropSumByBillChangeData(CommonBillChangeData currentBill) {
        BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(AccountTypeEnum.toEnum(currentBill.getAccountType()).getCode());
        return billCheckService.getChangePropSumByBillChangeData(currentBill);
    }

    @Override
    public Boolean isDemoUser(Long userId) {
        return SiteCodeUtils.isDemo(userId);
    }

    @Override
    public Boolean isSysUser(Long userId) {
        return commonService.isSysUser(userId);
    }

    @Override
    public String getSysUserDisplayStr(Long userId) {
        return commonService.getSysUserDisplayStr(userId);
    }

    @Override
    public Map<Integer, PriceVo> getCoinIdRatesMapCache(Date snapshotTime) {
        return commonService.getCoinIdRatesMapCache(snapshotTime.getTime());
    }

    @Override
    public Map<Integer, String> getAllCoinsMapCache(Date snapshotTime) {
        return commonService.getAllCoinsMapCache(snapshotTime.getTime());
    }

    @Override
    public Boolean isPapUser(Long userId) {
        return SiteUtil.checkPaptradingByUserId(userId);
    }

    @Override
    public Map<Integer, BigDecimal> getAccountTypeFeeTotalUsdt(List<Byte> accountTypeList, Date checkTime, Date snapshotTime, List<Integer> coinIdsList) {
        Map<Integer, BigDecimal> accountTypeFeeMap = new HashMap<>();
        for (Byte accountType : accountTypeList) {
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
            // 获取coin资产
            List<BillCoinProperty> billCoinPropertyList = billCoinPropertyService.selectCheckTimeRecord((int) accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime);
            Map<Integer, PriceVo> coinPriceMap = getCoinIdRatesMapCache(snapshotTime);
            BigDecimal feeUsdt = BigDecimal.ZERO;
            for (BillCoinProperty billCoinProperty : billCoinPropertyList) {
                Integer coinId = billCoinProperty.getCoinId();
                if (CollectionUtils.isNotEmpty(coinIdsList) && !coinIdsList.contains(coinId)) {
                    continue;
                }
                PriceVo priceVo = coinPriceMap.get(coinId);
                BigDecimal price = priceVo == null ? BigDecimal.ZERO : priceVo.getPrice();
                feeUsdt = NumberUtil.add(feeUsdt, NumberUtil.multiply(billCoinProperty.getFee().negate(), price));
            }
            accountTypeFeeMap.put((int) accountTypeEnum.getCode(), feeUsdt);
        }
        return accountTypeFeeMap;
    }

    @Override
    public Set<String> getBizTypeInOutSet(byte accountType) {
        return billFlowCheckService.getBizTypeInOutSet(accountType);
    }

    @Override
    public BigDecimal storeOrSumInOut(CommonBillChangeData commonBillChangeData) {
        return billFlowCheckService.storeOrSumInOut(commonBillChangeData);
    }

    @Override
    public Map<Byte, BigDecimal> getUserChangePropSum(Long userId, List<Byte> accountTypeList, List<String> bizTypeList, List<Integer> coinIdsList, Date startTime, Date endTime) {
        Map<Byte, BigDecimal> accountTypeChangeUsdtMap = new HashMap<>();
        Map<Integer, PriceVo> coinPriceMap = getCoinIdRatesMapCache(endTime);
        Map<Byte, Map<Integer, BigDecimal>> coinChangeMap = new HashMap<>();
        for (Byte accountType : accountTypeList) {
            billCoinTypeUserPropertyService.selectCrossDayByPage(userId, Integer.valueOf(accountType), bizTypeList, coinIdsList, startTime, endTime, 1000, coinTypeUserPropertyList -> {
                BigDecimal changePropSumUsdt;
                accountTypeChangeUsdtMap.computeIfAbsent(accountType, key -> BigDecimal.ZERO);
                Map<Integer, BigDecimal> coinMap = coinChangeMap.computeIfAbsent(accountType, key -> new HashMap<>());
                for (BillCoinTypeUserProperty billCoinProperty : coinTypeUserPropertyList) {
                    BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountType);
                    BigDecimal changePropSum = billCheckService.getChangePropSumByAbstractProperty(billCoinProperty);
                    PriceVo priceVo = coinPriceMap.get(billCoinProperty.getCoinId());
                    BigDecimal price = priceVo == null ? BigDecimal.ZERO : priceVo.getPrice();
                    changePropSumUsdt = NumberUtil.add(accountTypeChangeUsdtMap.get(accountType), NumberUtil.multiply(changePropSum, price));
                    accountTypeChangeUsdtMap.put(accountType, changePropSumUsdt);
                    coinMap.merge(billCoinProperty.getCoinId(), changePropSum, BigDecimal::add);
                }
            });
        }
        log.info("getUserChangePropSum userId:{}, coinChangeMap:{}", userId, coinChangeMap);
        return accountTypeChangeUsdtMap;
    }

    @Override
    public Map<Long, BigDecimal> getTransferAmount(Long userId, Date startTime, Date endTime) {
        Map<Long, BigDecimal> toTransferAmountMap = new HashMap<>();
        Map<Long, Map<Integer, BigDecimal>> transferCoinMap = new HashMap<>();
        InnerTransferRecordsReqV2Param queryParam = new InnerTransferRecordsReqV2Param();
        queryParam.setUserId(userId);
        queryParam.setStartTime(startTime);
        queryParam.setEndTime(endTime);
        queryParam.setEndId(0L);
        queryParam.setSize(200);
        // 币种价格
        Map<Integer, PriceVo> coinPriceMap = getCoinIdRatesMapCache(endTime);
        while (true) {
            List<InnerTransferInfoResult> resultList;
            try {
                resultList = transferServiceInnerClient.getTransferRecordsV2(queryParam);
            } catch (Exception e) {
                log.error("transferServiceInnerClient getTransferRecords params:{} error:", JSON.toJSONString(queryParam), e);
                break;
            }
            if (CollectionUtils.isEmpty(resultList)) {
                break;
            }
            resultList.stream().forEach(transferInfo -> {
                if (StringUtils.isBlank(transferInfo.getAmount())) {
                    return;
                }
                Long toUserId = transferInfo.getToUserId();
                PriceVo priceVo = coinPriceMap.get(transferInfo.getTransferCoinId());
                BigDecimal price = priceVo == null ? BigDecimal.ZERO : priceVo.getPrice();
                BigDecimal amount = toTransferAmountMap.getOrDefault(toUserId, BigDecimal.ZERO);
                BigDecimal transferAmount = new BigDecimal(transferInfo.getAmount());
                toTransferAmountMap.put(toUserId, amount.add(NumberUtil.multiply(price, transferAmount)));
                Map<Integer, BigDecimal> coinMap = transferCoinMap.computeIfAbsent(toUserId, key -> new HashMap<>());
                coinMap.merge(transferInfo.getTransferCoinId(), transferAmount, BigDecimal::add);
            });
            if (resultList.size() < queryParam.getSize()) {
                break;
            }
            queryParam.setEndId(Long.valueOf(resultList.get(resultList.size() - 1).getTransferId()));
        }
        log.info("getTransferAmount userId:{}, startTime:{}, endTime:{}, transferCoinMap:{}", userId, DateUtil.date2str(startTime), DateUtil.date2str(endTime), transferCoinMap);
        return toTransferAmountMap;
    }

    @Override
    public Pair<List<Long>, String> getPermissionCodes(CommonBillChangeData billChangeData) {
        Pair<List<Long>, String> pair = Pair.of(null, "");
        List<UserPermissionConfig> userPermissionList = billFlowCheckService.getUserPermissionCode(billChangeData.getAccountType(), billChangeData.getBizType());
        if (CollectionUtils.isEmpty(userPermissionList)) {
            return pair;
        }
        List<Long> codeList = null;
        for (UserPermissionConfig config : userPermissionList) {
            // 1、优先取mvel表达式路由规则
            if (StringUtils.isNotBlank(config.getRouteRule())) {
                Map<String, Object> contextMap = new HashMap<>();
                if (StringUtils.isNotBlank(config.getInjectServices())) {
                    String[] injectServices = config.getInjectServices().split(BillConstants.COMMA);
                    for (String serviceName : injectServices) {
                        contextMap.put(serviceName, SpringUtil.getBean(serviceName));
                    }
                }
                contextMap.put("billFlowData", billChangeData);
                contextMap.put("config", config);
                try {
                    Object result = MvelEngine.evaluate(config.getRouteRule(), contextMap);
                    if (Objects.nonNull(result) && Boolean.parseBoolean(result.toString())) {
                        codeList = config.getCodeList();
                        break;
                    }
                } catch (Exception e) {
                    log.error("getPermissionCode bizId:{} evaluate error:", billChangeData.getBizId(), e);
                }
            }
            // 2、权限路由服务路由规则
            String routeRuleService = config.getRouteRuleService();
            if (StringUtils.isNotBlank(routeRuleService)) {
                PermissionRouteRuleService permissionRouteRuleService = SpringUtil.getBean(routeRuleService, PermissionRouteRuleService.class);
                Pair<Boolean, String> selectPair = permissionRouteRuleService.isSelect(billChangeData, config);
                if (selectPair.getLeft()) {
                    codeList = config.getCodeList();
                    return Pair.of(codeList, selectPair.getRight());
                }
            }
            // 3、无路由规则，取优先级高的权限(order最小)
            if (StringUtils.isBlank(config.getRouteRule()) && StringUtils.isBlank(routeRuleService)) {
                codeList = config.getCodeList();
                break;
            }
        }
        return Pair.of(codeList, "");
    }

    @Override
    public Pair<Boolean, String> checkUserPermission(Long userId, Long permissionCode) {
        return reconUserPbCacheService.checkUserPermission(userId, permissionCode);
    }

    @Override
    public Pair<Boolean, String> checkUserPermissions(Long userId, List<Long> permissionCodeList) {
        return reconUserPbCacheService.checkUserPermissions(userId, permissionCodeList);
    }

    @Override
    public BillFlowCheckConfig getSubAccountConfig(byte accountType, String bizType) {
        return billFlowCheckService.getSubAccount(accountType, bizType);
    }

    @Override
    public Boolean isSubAccount(Long userId) {
        return reconUserPbCacheService.isSubAccount(userId);
    }

    @Override
    public Boolean isBroker(Long userId) {
        return reconUserPbCacheService.isBroker(userId);
    }
}