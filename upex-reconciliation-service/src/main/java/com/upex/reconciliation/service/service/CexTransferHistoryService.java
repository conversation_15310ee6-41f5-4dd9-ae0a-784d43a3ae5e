package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexTransferHistory;
import com.upex.reconciliation.service.dao.mapper.cex.ThirdCexTransferHistoryMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CexTransferHistoryService {

    @Resource
    BillDbHelper billDbHelper;

    @Resource
    ThirdCexTransferHistoryMapper thirdCexTransferHistoryMapper;

    public int batchInsert(List<ThirdCexTransferHistory> records)
    {
        return billDbHelper.doDbOpInReconMaster(()-> thirdCexTransferHistoryMapper.batchInsert(records));
    }

    public void deleteByTransferIds(List<String>  transferIds)
    {
        billDbHelper.doDbOpInReconMaster(()-> thirdCexTransferHistoryMapper.deleteByTransferIds(transferIds));
    }


}
