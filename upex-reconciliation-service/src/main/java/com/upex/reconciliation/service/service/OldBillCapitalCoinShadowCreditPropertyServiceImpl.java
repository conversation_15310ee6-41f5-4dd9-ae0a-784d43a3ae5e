package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.mapper.OldBillCapitalCoinShadowCreditPropertyMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class OldBillCapitalCoinShadowCreditPropertyServiceImpl implements OldBillCapitalCoinShadowCreditPropertyService {
    @Resource
    private BillDbHelper billDbHelper;

    @Resource
    private OldBillCapitalCoinShadowCreditPropertyMapper oldBillCapitalCoinShadowCreditPropertyMapper;


    @Override
    public void removeResetTimeShadowCreditList(Date resetCheckTime) {
        billDbHelper.doDbOpInSnapshotGlobalMaster(() -> {
            oldBillCapitalCoinShadowCreditPropertyMapper.deleteResetTimeShadowCreditList(resetCheckTime);
            return null;
        });
    }
}
