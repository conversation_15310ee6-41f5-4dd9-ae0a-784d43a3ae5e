package com.upex.reconciliation.service.config;

import com.upex.commons.support.util.AwsManagerUtils;
import com.upex.reconciliation.service.model.config.ApolloRedisConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class RedissonConfig {
    /**
     * 所有对Redisson的使用都是通过RedissonClient对象
     * @return {@link RedissonClient }
     * <AUTHOR>
     * @date 2024/9/21 15:57
     */
    @Bean(destroyMethod="shutdown")
    public RedissonClient redisson() {
        ApolloRedisConfig redisConfig = ReconciliationApolloConfigUtils.getBillRedisConfig();
        String host = System.getProperty("redisson.host");
        host = StringUtils.isNotBlank(host) ? host : redisConfig.getHost();
        String address = "redis://" + host + ":" + redisConfig.getPort();

        Config config = new Config();
        config.useSingleServer()
                .setAddress(address)
                .setDatabase(redisConfig.getDatabase())
                .setTimeout(redisConfig.getTimeout())
                .setIdleConnectionTimeout(redisConfig.getIdleConnectionTimeout())
                .setRetryAttempts(redisConfig.getRetryAttempts())
                .setRetryInterval(redisConfig.getRetryInterval());
        if(StringUtils.isNotBlank(redisConfig.getPassword())){
            /** 设置密码 **/
            config.useSingleServer().setPassword(redisConfig.getPassword());
        }
        if(StringUtils.isNotBlank(redisConfig.getSecretName())){
            String password = AwsManagerUtils.getAwsSecretManagerRedisAuth(redisConfig.getEndpoint(), redisConfig.getRegion(), redisConfig.getSecretName()).getPassword();
            config.useSingleServer().setPassword(password);
        }
        return Redisson.create(config);
    }
}
