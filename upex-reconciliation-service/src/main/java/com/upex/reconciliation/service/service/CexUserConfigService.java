package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.dao.mapper.cex.ThirdCexUserConfigMapper;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.SetApiKeyMonitorReq;
import com.upex.reconciliation.service.service.client.cex.enmus.ApiKeyStatusEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ReadOnlyEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CexUserConfigService {

    @Resource
    private ThirdCexUserConfigMapper thirdCexUserConfigMapper;

    @Resource
    ThirdCexUserService thirdCexUserService;

    @Resource
    BillDbHelper billDbHelper;



    public int update(ThirdCexUserConfig config) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserConfigMapper.update(config));
    }

    public int updateReadOnly(int cexType, String cexUserId, int readOnly) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserConfigMapper.updateReadOnly(cexType, cexUserId, readOnly));
    }

    public ThirdCexUserConfig selectById(Long id) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserConfigMapper.selectById(id));
    }

    public List<ThirdCexUserConfig> selectAll() {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserConfigMapper.selectAll());
    }

    public int addConfig(ThirdCexUserConfig config) {
        config.setCreateTime(new Date());
        config.setUpdateTime(new Date());
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserConfigMapper.insert(config));
    }

    public List<ThirdCexUserConfig> getConfigsByUserId(Integer cexType, String userId) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserConfigMapper.selectByCexTypeAndUserId(cexType, userId));
    }

    public ThirdCexUserConfig getEffectiveAssetMonitorConfigByUserId(Integer cexType, String userId) {
        List<ThirdCexUserConfig> configs = getConfigsByUserId(cexType, userId);
        if (CollectionUtils.isEmpty(configs)) {
            return null;
        }
        //&& config.getStatus().equals(ApiKeyStatusEnum.EFFECT.getType())
        return configs.stream().filter(config -> config.getReadOnly() == ReadOnlyEnum.READ_ONLY.getType() && config.getStatus().equals(ApiKeyStatusEnum.EFFECT.getType())).findFirst().orElse(null);
    }
}
