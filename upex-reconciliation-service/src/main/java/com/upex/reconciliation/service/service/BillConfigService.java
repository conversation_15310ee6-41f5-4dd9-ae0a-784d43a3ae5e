package com.upex.reconciliation.service.service;

import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillConfig;
import com.upex.reconciliation.service.dao.mapper.BillConfigMapper;
import com.upex.reconciliation.service.model.dto.PartitionOffsetDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Service
public class BillConfigService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billConfigMapper")
    private BillConfigMapper billConfigMapper;

    public boolean updateBillConfigOffset(Byte accountType, String accountParam, Map<Integer, Long> partitionMap) {
        BillConfig billConfig = dbHelper.doDbOpInReconMaster(() -> billConfigMapper.selectByTypeAndParam(accountType, accountParam));
        if (billConfig == null) {
            return false;
        }
        List<PartitionOffsetDTO> partitionOffsetDTOList = new ArrayList<>();
        partitionMap.forEach((k, v) -> {
            PartitionOffsetDTO partitionOffsetDTO = new PartitionOffsetDTO();
            partitionOffsetDTO.setPartition(k);
            partitionOffsetDTO.setOffset(v);
            partitionOffsetDTOList.add(partitionOffsetDTO);
        });
        billConfig.setConsumeOffset(JSONObject.toJSONString(partitionOffsetDTOList));
        dbHelper.doDbOpInReconMaster(() -> billConfigMapper.updateOffset(billConfig));
        return true;
    }

    public BillConfig selectById(Long id) {
        return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.selectById(id));
    }

    public int batchInsert(List<BillConfig> records,
                           Byte accountType,
                           String accountParam) {
        if (CollectionUtils.isNotEmpty(records)) {
            return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.batchInsert(records, accountType, accountParam));
        }
        return 0;
    }

    public Boolean batchDelete(Byte accountType, String accountParam, Long beginId, Long pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.batchDelete(beginId, pageSize, accountType, accountParam));
    }

    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.deleteByCheckTime(accountType, accountParam, checkTime));
    }

    public Boolean deleteByGtCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.deleteByGtCheckTime(accountType, accountParam, checkTime));
    }

    public int cleanData() {
        return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.cleanData());
    }

    /**
     * @param accountType
     * @param accountParam
     * @return
     */
    public BillConfig selectByTypeAndParam(Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.selectByTypeAndParam(accountType, accountParam));
    }

    /**
     * @param accountType
     * @param accountParam
     * @return
     */
    public int delBillDateForReset(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.delBillDateForReset(accountType, accountParam, checkTime));
    }


    /**
     * @param accountType
     * @param accountParam
     * @return
     */
    public BillConfig selectByTypeAndParamAndCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.selectByTypeAndParamAndCheckTime(accountType, accountParam, checkTime));
    }


    /**
     * @param accountType
     * @param accountParam
     * @return
     */
    public BillConfig selectFirstByTypeAndParam(Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.selectFirstByTypeAndParam(accountType, accountParam));
    }

    /**
     * @param accountType
     * @param accountParam
     * @return
     */
    public List<BillConfig> selectByTypeAndParamAfterCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.selectByTypeAndParamAfterCheckTime(accountType, accountParam, checkTime));
    }

    /**
     * 获取最新xxx条
     *
     * @param accountType
     * @param accountParam
     * @param limit
     * @return
     */
    public List<BillConfig> selectLastByLimit(Byte accountType, String accountParam, Long limit, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.selectLastByLimit(accountType, accountParam, limit, checkTime));
    }

    public List<BillConfig> selectByLtCheckTimeAsc(Byte accountType, String accountParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.selectByLtCheckTimeAsc(accountType, accountParam, checkTime, batchSize));
    }

    public Date selectLatestCheckTime(Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billConfigMapper.selectLatestCheckTime(accountType, accountParam));
    }
}
