package com.upex.reconciliation.service.model.domain;

import com.upex.reconciliation.service.common.constants.BillConstants;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 资产快照币种数据表
 * <AUTHOR>
 * @TableName bill_base_coin_asset_balance_snapshot
 */
@Data
public class BillBaseCoinAssetBalanceSnapshot implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户类型，系统用户，普通用户，不统计用户
     */
    private Integer userType;

    /**
     * 用户类型名称，系统用户，普通用户，不统计用户
     */
    private String userTypeName;

    /**
     * 业务用户类型，send_email
     */
    private String businessUserType;

    /**
     * 业务用户详细类型，remark
     */
    private String businessUserDetailType;

    /**
     * 仓位标识 0-无 52|53|54
     */
    private String positionFlag;

    /**
     * 币种Id
     */
    private Integer coinId;

    /**
     * 币种名称
     */
    private String coinName;

    /**
     * 快照时间
     */
    private Date snapshotTime;

    /**
     * 现货资产余额
     */
    private BigDecimal spotBalance = BigDecimal.ZERO;

    /**
     * u合约资产余额
     */
    private BigDecimal mixUBalance = BigDecimal.ZERO;

    /**
     * b合约资产余额
     */
    private BigDecimal mixBBalance = BigDecimal.ZERO;

    /**
     * c合约资产余额
     */
    private BigDecimal mixCBalance = BigDecimal.ZERO;

    /**
     * 全仓杠杆资产余额
     */
    private BigDecimal marginFullBalance = BigDecimal.ZERO;

    /**
     * 逐仓杠杆资产余额
     */
    private BigDecimal marginOneBalance = BigDecimal.ZERO;

    /**
     * 理财资产余额
     */
    private BigDecimal financialBalance = BigDecimal.ZERO;

    /**
     * P2P资产余额
     */
    private BigDecimal p2pBalance = BigDecimal.ZERO;

    /**
     * 币种对应usdt汇率
     */
    private BigDecimal rate = BigDecimal.ZERO;

    /**
     * 逐仓借贷金额
     */
    private BigDecimal marginOneLoanBalance = BigDecimal.ZERO;

    /**
     * 全仓借贷金额
     */
    private BigDecimal marginFullLoanBalance = BigDecimal.ZERO;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public BigDecimal getAllAmount(){
        return spotBalance.add(mixUBalance).add(mixBBalance).add(mixCBalance).add(marginFullBalance).add(marginOneBalance)
                .add(financialBalance).add(p2pBalance);
    }

    /**
     * 判定资产是否为0
     *
     * @return {@link boolean }
     * <AUTHOR>
     * @date 2023/7/7 23:46
     */
    public boolean isZeroAssets(){
        return spotBalance.abs()
                .add(mixUBalance.abs()).add(mixBBalance.abs()).add(mixCBalance.abs())
                .add(marginFullBalance.abs()).add(marginOneBalance.abs())
                .add(marginFullLoanBalance.abs()).add(marginOneLoanBalance.abs())
                .add(financialBalance.abs())
                .add(p2pBalance.abs()).compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 获取唯一键
     *
     * @return {@link String }
     * <AUTHOR>
     * @date 2023/7/7 23:45
     */
    public String getUniqKey() {
        return userId + BillConstants.UNDERSCORE_SEPARATOR + coinId;
    }

    /**
     * 获取唯一键
     *
     * @return {@link String }
     * <AUTHOR>
     * @date 2023/7/7 23:45
     */
    public String getTimeAndUser() {
        return snapshotTime + BillConstants.UNDERSCORE_SEPARATOR + userId;
    }

    /**
     * 非合约赋值
     * <AUTHOR>
     * @date 2023/7/7 23:47
     */
    public void setNotContractAssigned(BillBaseCoinAssetBalanceSnapshot source) {
        this.setSpotBalance(source.getSpotBalance());
        this.setMarginFullBalance(source.getMarginFullBalance());
        this.setMarginOneBalance(source.getMarginOneBalance());
        this.setMarginFullLoanBalance(source.getMarginFullLoanBalance());
        this.setMarginOneLoanBalance(source.getMarginOneLoanBalance());
        this.setFinancialBalance(source.getFinancialBalance());
        this.setP2pBalance(source.getP2pBalance());
    }
}