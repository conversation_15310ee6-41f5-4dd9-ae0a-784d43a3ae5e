package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.math.BigDecimal;
@Data
public class ParentSubTransferInnerRes {

    /**
     * {
     *             "tranId": ************,
     *             "fromEmail": "<EMAIL>",
     *             "toEmail": "<EMAIL>",
     *             "asset": "USDT",
     *             "amount": "8",
     *             "createTimeStamp": *************,
     *             "fromAccountType": "SPOT",
     *             "toAccountType": "SPOT",
     *             "status": "SUCCESS"
     *         },
     */

    private Long tranId;
    private String fromEmail;
    private String toEmail;
    private String asset;
    private BigDecimal amount;
    private Long createTimeStamp;
    private String fromAccountType;
    private String toAccountType;
    private String status;

}
