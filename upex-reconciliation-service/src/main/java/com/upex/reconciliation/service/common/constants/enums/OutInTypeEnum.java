package com.upex.reconciliation.service.common.constants.enums;

/**
 * 入出类型
 */
public enum OutInTypeEnum {

    /**
     * 用户充值
     */
    RECHARGE("1",1, "充值"),
    /**
     * 老用户充值
     */
    OLD_RECHARGE("2_1_1_3",null,"充值"),
    /**
     * 充值成功
     */
    RECHARGE_SUCCESS("10_1_SUCCESS",null,"充值"),
    //锁仓订单回收到系统，解锁锁仓资金到系统账户，ftype:30,fsubtype:1，退回
    SYSTEM_UNLOCK_RECOVERY("23",23, "锁仓订单回收到系统"),
    //司法回收
    // JUDICIAL_RECOVERY_OP("12",12, "司法回收，目前只允许为正，为负不允许"),
    //系统锁仓
    USER_LOCK_FROM_SYSTEM_OP("14",14, "系统锁仓"),
    /**
     * 用户提现
     */
    WITHDRAW("2",2, "提现"),
    /**
     * 普通提币
     */
    ORDINARY_WITHDRAW("2_2_1_7",null,"普通提币"),
    //内部转账提币账单
    INNER_TRANSFER_WITHDRAW("28",28, "内部转账入"),
    //内部地址提币
    INNER_ADDRESS_WITHDRAW("29",29, "内部链地址提币入"),
    //快速充币
    FAST_RECHARGE("2_1_2_3",null,"快速充币"),
    //老内部转账
    OLD_INNER_TRANSFER_WITHDRAW("2_1_3_3",null,"内部充币"),
    //内部提币出(内部转账/内部提币地址出账类型)
    INNER_WITHDRAW_OUT("30",30,"内部地址提币出"),
    //内部转账地址出
    INNER_TRANSFER_OUT("31",31,"内部转账提币出"),
    //内部转账出金
    INNER_TRANSFER_WITHDRAW_ONE("2_2_3_3",null,"内部转账出金"),
    //内部转账出金
    INNER_TRANSFER_WITHDRAW_TWO("2_2_2_3",null,"内部转账出金"),
    //增加合约体验金
    CASH_GIFT_IN("cash_gift_in",  18,"增加合约体验金"),
    //扣除合约体验金
    CASH_GIFT_OUT("cash_gift_out", 19,"扣除合约体验金"),
    //返佣奖励 //ftype=6 fsubtype=2
    RAKE_BACK_REWARD("20",20, "返佣奖励"),
    /**
     * otc买入
     */
    BUY("5",5,"买入"),
    /**
     * otc卖出
     */
    SELL("6",6,"卖出"),
    //空投奖励
    AIRDROP_REWARD("33",33, "空投奖励"),
    //司法回收（归入到转出，和转出区分）
    // JUDICIAL_RECOVERY_OP("6",6, "司法回收"),
    ;

    private String type;
    private Integer code;
    private String desc;

    OutInTypeEnum(String type, Integer code, String desc) {
        this.type = type;
        this.code = code;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OutInTypeEnum toEnum(Integer code) {
        for (OutInTypeEnum item : OutInTypeEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static OutInTypeEnum toEnumByType(String type) {
        for (OutInTypeEnum item : OutInTypeEnum.values()) {
            if (item.type.equals(type)) {
                return item;
            }
        }
        return null;
    }
}
