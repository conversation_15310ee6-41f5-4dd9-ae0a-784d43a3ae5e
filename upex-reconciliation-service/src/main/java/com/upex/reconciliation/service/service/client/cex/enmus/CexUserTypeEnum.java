package com.upex.reconciliation.service.service.client.cex.enmus;

import lombok.Getter;

@Getter
public enum CexUserTypeEnum {

    PARENT_USER(1, "母用户"),
    VIR_SUB_USER(2, "虚拟子用户"),
    COMMON_SUB_USER(3, "普通子用户"),
    HOSTING_SUB_USER(4, "托管子用户");

    private Integer type;
    private String name;

    CexUserTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

     public static CexUserTypeEnum fromType(Integer type) {
        for (CexUserTypeEnum value : values()) {
            if (value.type.equals(type)) {
                return value;
            }
        }
        return null;
    }
}
