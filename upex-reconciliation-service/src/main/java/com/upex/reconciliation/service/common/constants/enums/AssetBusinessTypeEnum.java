package com.upex.reconciliation.service.common.constants.enums;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 加载数据定义：业务类型
 * <AUTHOR>
 */
@AllArgsConstructor
public enum AssetBusinessTypeEnum {
    COPPER("copper", 1, "COPPER业务线", "COPPER"),
    COBO("cobo", 2, "COBO业务线", "COBO"),
    FIAT_CAPITUAL("fiat_capitual", 3, "法币渠道Capitual", "CAPITUAL"),
    FIAT_OUITRUST("fiat_ouitrust", 4, "法币渠道Ouitrust", "OUITRUST"),
    FIAT_ADVCASH("fiat_advcash", 5, "法币渠道ADVCASH", "ADVCASH"),
    FIAT_OPENPAYD("fiat_openpayd", 6, "法币渠道OPENPAYD", "OPENPAYD"),
    FIAT_ADGROUP("fiat_adgroup",9,"法币渠道ADGROUP","ADGROUP"),
    OASIS("oasis", 7, "OASIS业务线", "OASIS"),
    MAIN_TURKEY_TRANSFER("main_turkey_transfer", 8, "主站土耳其划转业务线", "MAIN_TURKEY_TRANSFER"),
    SPOT_INS_LOAN("spot_ins_loan", 10, "机构借贷业务线", "SPOT_INS_LOAN"),
    SPOT_INS_INTEREST("spot_ins_interest", 11, "机构借贷利息业务线", "SPOT_INS_INTEREST"),
    ;


    /**
     * 业务方名称
     */
    private final String businessCode;
    /**
     * 插入数据库类型
     */
    private final Integer businessType;
    /**
     * 描述
     */
    private final String businessName;
    /**
     * 简写、缩写业务方标识
     */
    private final String shortName;


    /**
     * 根据类型找枚举
     *
     * @param code 枚举值
     * @return 枚举
     */
    public static AssetBusinessTypeEnum toEnum(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
        }
        for (AssetBusinessTypeEnum item : values()) {
            if (StringUtils.equalsIgnoreCase(item.getBusinessCode(), code)) {
                return item;
            }
        }
        throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public String getBusinessName() {
        return businessName;
    }

    public String getShortName() {
        return shortName;
    }
}
