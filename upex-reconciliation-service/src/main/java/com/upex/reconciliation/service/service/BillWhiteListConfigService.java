package com.upex.reconciliation.service.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillWhiteListConfig;
import com.upex.reconciliation.service.dao.mapper.BillWhiteListConfigMapper;
import com.upex.reconciliation.service.model.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toSet;

/**
 * 对账白名单服务类
 *
 * <AUTHOR>
 * @Date 2025/2/5
 */
@Slf4j
@Service
public class BillWhiteListConfigService {

    @Resource
    private BillDbHelper dbHelper;

    @Resource
    private BillWhiteListConfigMapper billWhiteListConfigMapper;

    /**
     * 白名单缓存，本地缓存：type -> Set<whiteList>
     */
    private final LoadingCache<Integer, Set<String>> whiteListCache =
            Caffeine.newBuilder()
                    .expireAfterWrite(2, TimeUnit.MINUTES)
                    .build(type -> getWhileListConfigList(type, StatusEnum.ENABLE.getCode()).stream().map(BillWhiteListConfig::getWhiteList).collect(toSet()));

    /**
     * 根据类型查询对应有效白名单列表
     *
     * @param type
     * @return
     */
    public Set<String> getWhiteList(Integer type) {
        return whiteListCache.get(type);
    }

    /**
     * 根据类型查询对应有效白名单列表
     *
     * @param type
     * @return
     */
    public Set<Long> getLongWhiteList(Integer type) {
        return getWhiteList(type).stream().map(Long::parseLong).collect(Collectors.toSet());
    }

    /**
     * 根据类型及状态查询对应白名单列表
     *
     * @param type
     * @param status
     * @return
     */
    private List<BillWhiteListConfig> getWhileListConfigList(Integer type, Integer status) {
        return dbHelper.doDbOpInReconMaster(() -> billWhiteListConfigMapper.getWhileListConfigList(type, status));
    }

    /**
     * 维护表数据
     *
     * @param jobParam
     */
    public void repairBillWhiteListConfig(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        log.info("repairBillWhiteListConfig data:{}", jobParam);
        String action = jsonObject.getString("action");
        JSONArray dataList = jsonObject.getJSONArray("dataList");
        if (CollectionUtil.isEmpty(dataList)) {
            return;
        }
        if ("insert".equals(action)) {
            Date nowDate = new Date();
            List<BillWhiteListConfig> whiteListConfigList = Convert.toList(BillWhiteListConfig.class, dataList);
            if (CollectionUtils.isNotEmpty(whiteListConfigList)) {
                for (BillWhiteListConfig billWhiteListConfig : whiteListConfigList) {
                    billWhiteListConfig.setCreateTime(nowDate);
                    billWhiteListConfig.setUpdateTime(nowDate);
                }
                batchInsert(whiteListConfigList);
            }
        } else if ("update".equals(action)) {
            List<BillWhiteListConfig> billWhiteListConfigList = Convert.toList(BillWhiteListConfig.class, dataList);
            if (CollectionUtils.isNotEmpty(billWhiteListConfigList)) {
                for (BillWhiteListConfig billWhiteListConfig : billWhiteListConfigList) {
                    updateByPrimaryKeySelective(billWhiteListConfig);
                }
            }
        }
        refresh();
    }

    /**
     * 刷新缓存
     */
    private void refresh() {
        whiteListCache.asMap().forEach((key, value) -> whiteListCache.refresh(key));
    }

    private int updateByPrimaryKeySelective(BillWhiteListConfig whiteListConfig) {
        return dbHelper.doDbOpInReconMaster(() -> billWhiteListConfigMapper.updateByPrimaryKeySelective(whiteListConfig));
    }

    private int batchInsert(List<BillWhiteListConfig> whiteListConfigList) {
        return dbHelper.doDbOpInReconMaster(() -> billWhiteListConfigMapper.batchInsert(whiteListConfigList));
    }

    private int deleteByIds(List<Long> ids) {
        return dbHelper.doDbOpInReconMaster(() -> billWhiteListConfigMapper.deleteByIds(ids));
    }
}
