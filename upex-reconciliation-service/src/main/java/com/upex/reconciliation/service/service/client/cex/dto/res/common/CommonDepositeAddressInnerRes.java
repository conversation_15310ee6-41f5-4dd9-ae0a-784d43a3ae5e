package com.upex.reconciliation.service.service.client.cex.dto.res.common;

import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import lombok.Data;

@Data
public class CommonDepositeAddressInnerRes  {

    private String coinName;
    private String address;
    private String tag;
    private Integer isDefault;

    public String getAddress() {
        return HmacUtil.decrypt( address);
    }
}
