package com.upex.reconciliation.service.business.convert.model;

import com.upex.kline.dto.response.KLinePeriodDto;
import lombok.Data;

@Data
public class KLineDataDTO extends KLinePeriodDto {

    /**k线类型**/
    private Integer kLineType;


    public KLineDataDTO(KLinePeriodDto kLinePeriodDto,Integer kLineType) {

        if (kLinePeriodDto != null) {
            // 映射所有KLinePeriodDto的属性到当前对象
            this.setActiveBaseVolume(kLinePeriodDto.getActiveBaseVolume());
            this.setActiveQuoteVolume(kLinePeriodDto.getActiveQuoteVolume());
            this.setActiveUsdtVolume(kLinePeriodDto.getActiveUsdtVolume());
            this.setBaseVolume(kLinePeriodDto.getBaseVolume());
            this.setBi(kLinePeriodDto.getBi());
            this.setEdu(kLinePeriodDto.getEdu());
            this.setFclose(kLinePeriodDto.getFclose());
            this.setFopen(kLinePeriodDto.getFopen());
            this.setFtime(kLinePeriodDto.getFtime());
            this.setFtype(kLinePeriodDto.getFtype());
            this.setHigh(kLinePeriodDto.getHigh());
            this.setLastId(kLinePeriodDto.getLastId());
            this.setLastupdatetime(kLinePeriodDto.getLastupdatetime());
            this.setLow(kLinePeriodDto.getLow());
            this.setPcode(kLinePeriodDto.getPcode());
            this.setQuoteVolume(kLinePeriodDto.getQuoteVolume());
            this.setUsdtVolume(kLinePeriodDto.getUsdtVolume());
            this.setVol(kLinePeriodDto.getVol());
        }
        // 设置k线类型
        this.setKLineType(kLineType);

    }
}
