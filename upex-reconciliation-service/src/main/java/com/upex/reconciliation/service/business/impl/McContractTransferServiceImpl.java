package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.upex.assets.dto.params.TransferRequestDTO;
import com.upex.assets.dto.result.TransferResult;
import com.upex.commons.support.model.ResponseResult;
import com.upex.mixcontract.process.facade.enums.TransferAccountEnum;
import com.upex.mixcontract.process.facade.enums.TransferTypeEnum;
import com.upex.mixcontract.process.facade.feign.inner.InnerTransferFeignClient;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AbstractTransferService;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.ProfitTransferTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.TRANSFER_SERVICE_ERROR;

@Service
@Slf4j
public class McContractTransferServiceImpl extends AbstractTransferService {
    @Resource
    private InnerTransferFeignClient transferServiceClient;
    @Resource
    private AlarmNotifyService alarmNotifyService;

    @Override
    public Boolean transfer(AccountTypeEnum accountTypeEnum, BillContractProfitTransfer item, Date transferTime, Map<Integer, String> allCoinsMap) {
        try {
            TransferRequestDTO transferRequestDTO = TransferRequestDTO.builder()
                    .userId(item.getTransferInUserId())
                    // 所有转账金额都为正数
                    .amount(item.getTransferCount().abs())
                    .transferCoinId(item.getCoinId())
                    .type(getType(item.getTransferCount()))
                    .transferId(item.getId().toString())
                    .accountType(getAccountType(item))
                    .bizTime(transferTime)
                    .tokenId(allCoinsMap.getOrDefault(item.getCoinId(), BillConstants.EMPTY).toUpperCase())
                    .businessLine(accountTypeEnum.getBusinessLineEnum())
                    .build();
            log.info("McContractTransferServiceImpl transfer params:{}", JSON.toJSONString(transferRequestDTO));
            ResponseResult<TransferResult> transfer = transferServiceClient.billTransfer(transferRequestDTO);
            if (BillConstants.MC_CONTRACT_SUCCESS_CODE.equals(transfer.getCode())) {
                return transfer.getData().getResult();
            } else {
                log.error("BillContractProfitTransferService.transferForBillProfit McContractTransferServiceImpl failed transfer param:{}, response:{}", JSONObject.toJSONString(transferRequestDTO), JSONObject.toJSONString(transfer));
                alarmNotifyService.alarm(TRANSFER_SERVICE_ERROR, accountTypeEnum.getCode());
                return false;
            }
        } catch (Exception e) {
            alarmNotifyService.alarm(TRANSFER_SERVICE_ERROR, accountTypeEnum.getCode());
            throw e;
        }
    }

    private static Integer getType(BigDecimal transferCount) {
        return BigDecimal.ZERO.compareTo(transferCount) > 0 ? TransferTypeEnum.OUT.getCode() : TransferTypeEnum.IN.getCode();
    }

    public Integer getAccountType(BillContractProfitTransfer item) {
        if (ProfitTransferTypeEnum.SYSTEM_CONTRACT_COST_INTEREST.getCode() == item.getTransferType()
                || ProfitTransferTypeEnum.SYSTEM_CONTRACT_COST_INTEREST_RESET.getCode() == item.getTransferType()) {
            return TransferAccountEnum.UNION_INTEREST.getCode();
        } else {
            return TransferAccountEnum.BILL.getCode();
        }
    }
}
