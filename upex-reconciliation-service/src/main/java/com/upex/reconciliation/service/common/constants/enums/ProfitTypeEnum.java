package com.upex.reconciliation.service.common.constants.enums;

import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 利润类型枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ProfitTypeEnum {
    COIN_PROFIT("coin_profit", "币种对冲", ProfitTransferTypeEnum.COIN_PROFIT),
    SYMBOL_PROFIT("symbol_profit", "币对平台盈亏", ProfitTransferTypeEnum.SYMBOL_PROFIT),
    MIX_CONTRACT_SYSTEM_DEAL_FEE("mix_contract_system_deal_fee", "合约交易系统收取手续费", ProfitTransferTypeEnum.SYSTEM_CONTRACT_FEE),
    SPOT_DEAL_SYSTEM_DEAL_FEE("spot_deal_system_deal_fee", "现货交易系统收取手续费", ProfitTransferTypeEnum.SYSTEM_SPOT_FEE),
    LEVER_DEAL_SYSTEM_DEAL_FEE("lever_deal_system_deal_fee", "杠杆交易系统收取手续费", ProfitTransferTypeEnum.SYSTEM_LEVER_FEE),
    MIX_CONTRACT_SYSTEM_DEAL_COST_INTEREST("u_contract_deal_cost_interest", "合约交易系统收取利息", ProfitTransferTypeEnum.SYSTEM_CONTRACT_COST_INTEREST),
    UTA_SPOT_DEAL_FEE("uta_spot_deal_fee", "统一账户现货手续费", ProfitTransferTypeEnum.SYSTEM_UTA_SPOT_FEE),
    UTA_CONTRACT_DEAL_FEE("uta_contract_deal_fee", "统一账户合约手续费", ProfitTransferTypeEnum.SYSTEM_UTA_CONTRACT_FEE),
    ;
    private String code;
    private String desc;
    private ProfitTransferTypeEnum profitTransferType;

    /**
     * 类型转枚举
     *
     * @param code
     * @return
     */
    public static ProfitTypeEnum toEnum(String code) {
        for (ProfitTypeEnum item : ProfitTypeEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 根据业务类型获取枚举
     *
     * @return
     */
    public static ProfitTypeEnum getProfitTypeByAccountType(Byte accountType, ProfitSubTypeEnum profitSubTypeEnum, TransferFeeTypeEnum transferFeeType) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        if (profitSubTypeEnum == ProfitSubTypeEnum.PROFIT) {
            return SYMBOL_PROFIT;
        } else if (profitSubTypeEnum == ProfitSubTypeEnum.EXCHANGE) {
            return COIN_PROFIT;
        } else if (profitSubTypeEnum == ProfitSubTypeEnum.INTEREST) {
            return MIX_CONTRACT_SYSTEM_DEAL_COST_INTEREST;
        } else if (profitSubTypeEnum == ProfitSubTypeEnum.FEE) {
            if (accountTypeEnum.isContract()) {
                return MIX_CONTRACT_SYSTEM_DEAL_FEE;
            } else if (accountTypeEnum.isSpot()) {
                return SPOT_DEAL_SYSTEM_DEAL_FEE;
            } else if (accountTypeEnum.isLever()) {
                return LEVER_DEAL_SYSTEM_DEAL_FEE;
            } else if (accountTypeEnum.isUta()) {
                if (transferFeeType == TransferFeeTypeEnum.UTA_SPOT_DEAL_FEE) {
                    return UTA_SPOT_DEAL_FEE;
                } else if (transferFeeType == TransferFeeTypeEnum.UTA_CONTRACT_DEAL_FEE) {
                    return UTA_CONTRACT_DEAL_FEE;
                }
            }
        }
        throw new RuntimeException("获取类型错误，请检查配置！" + accountType + "," + profitSubTypeEnum + "," + transferFeeType);
    }

    public ProfitSubTypeEnum getProfitSubTypeEnum() {
        if (this == ProfitTypeEnum.SYMBOL_PROFIT) {
            return ProfitSubTypeEnum.PROFIT;
        } else if (this == ProfitTypeEnum.COIN_PROFIT) {
            return ProfitSubTypeEnum.EXCHANGE;
        } else if (this == ProfitTypeEnum.MIX_CONTRACT_SYSTEM_DEAL_FEE) {
            return ProfitSubTypeEnum.FEE;
        } else if (this == ProfitTypeEnum.SPOT_DEAL_SYSTEM_DEAL_FEE) {
            return ProfitSubTypeEnum.FEE;
        } else if (this == ProfitTypeEnum.LEVER_DEAL_SYSTEM_DEAL_FEE) {
            return ProfitSubTypeEnum.FEE;
        } else if (this == ProfitTypeEnum.MIX_CONTRACT_SYSTEM_DEAL_COST_INTEREST) {
            return ProfitSubTypeEnum.INTEREST;
        }
        throw new RuntimeException("获取类型错误，请检查配置！");
    }
}