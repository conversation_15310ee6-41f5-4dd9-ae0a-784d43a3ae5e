package com.upex.reconciliation.service.common.constants;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.Duration;

@Getter
@AllArgsConstructor
public enum AlarmTemplateQpsEnum {
    NONE("none", "none", 0L),
    ONE_SECOND("one_second", "one_second", Duration.ofSeconds(1).toMillis()),
    ONE_MINUTE("one_minute", "one_minute", Duration.ofMinutes(1).toMillis()),
    FIVE_MINUTE("five_minute", "five_minute", Duration.ofMinutes(5).toMillis()),
    ;

    private String code;
    private String desc;
    private Long time;

    public static AlarmTemplateQpsEnum toEnum(String code) {
        for (AlarmTemplateQpsEnum alarmTemplateEnum : AlarmTemplateQpsEnum.values()) {
            if (alarmTemplateEnum.getCode().equals(code)) {
                return alarmTemplateEnum;
            }
        }
        return null;
    }

}
