package com.upex.reconciliation.service.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.dao.entity.BillUser;
import com.upex.reconciliation.service.dao.mapper.BillCoinUserPropertyMapper;
import com.upex.reconciliation.service.dao.mapper.BillUserMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.model.dto.SyncBillUserDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


@Service
public class BillUserService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billUserMapper")
    private BillUserMapper billUserMapper;
    @Resource(name = "kafkaProducer")
    private KafkaProducer<String, String> kafkaProducer;

    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;

    /**
     * 批量插入
     *
     * @param records
     * @return
     */
    public Integer batchInsert(List<BillUser> records) {
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }
        return dbHelper.doDbOpInReconMaster(() -> billUserMapper.batchInsert(records));
    }

    /**
     * 批量更新
     *
     * @param records
     * @return
     */
    public Integer batchUpdate(List<BillUser> records) {
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }
        return dbHelper.doDbOpInReconMaster(() -> billUserMapper.batchUpdate(records));
    }

    /**
     * 批量查询
     *
     * @param ids
     * @return
     */
    public List<BillUser> selectByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return dbHelper.doDbOpInReconMaster(() -> billUserMapper.selectByIds(ids));
    }

    public void repairBillUser(Integer pageSize, Byte accountType, String accountParam) {
        pageSize = Objects.requireNonNullElse(pageSize, 500);
        Map<Long, Date> existUserCreateTimeMap = billCoinUserPropertyService.selectUserMinCreateTimeBatch(accountType,accountParam,pageSize);
        if (!existUserCreateTimeMap.isEmpty()) {
            existUserCreateTimeMap.forEach((userId,createTime) -> {
                SyncBillUserDTO syncBillUserDTO = new SyncBillUserDTO();
                syncBillUserDTO.setAccountType(accountType);
                syncBillUserDTO.setUserId(userId);
                syncBillUserDTO.setRegisterTime(createTime);
                ProducerRecord<String, String> incrementRecord = new ProducerRecord<>(KafkaTopicEnum.RECON_USER_INITALL_SYNC_TOPIC.getCode(), syncBillUserDTO.getUserId().toString(), JSON.toJSONString(syncBillUserDTO));
                kafkaProducer.send(incrementRecord);
            });
        }
    }
}
