package com.upex.reconciliation.service.business.profit.impl;

import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import com.upex.reconciliation.service.business.profit.CheckProfitGrayService;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 白名单灰度
 *
 * <AUTHOR>
 * @Date 2025/4/25
 */
@Service
public class WhiteListCheckProfitGrayServiceImpl implements CheckProfitGrayService {

    @Override
    public boolean match(ReconCheckResultsParams checkResultsParams, GlobalBillConfig billConfig) {
        List<Long> profitGrayWhileList = billConfig.getProfitGrayWhileList();
        for (Long userId : profitGrayWhileList) {
            if (Objects.equals(userId, checkResultsParams.getUserId()) || matchPrefix(checkResultsParams.getUserId(), userId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 前缀匹配
     *
     * @param userId
     * @param prefix
     * @return
     */
    public static boolean matchPrefix(Long userId, Long prefix) {
        return String.valueOf(userId).startsWith(String.valueOf(prefix));
    }
}
