package com.upex.reconciliation.service.model;

import com.upex.mixcontract.common.repo.RepositorySaveItem;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/9/11
 * @Tag d6434b1e0747ecf88c83c31088a82249
 */

@Getter
public class CmdRunEvent {
    private final BillCmdWrapper billCmdWrapper;
    private final RepositorySaveItem saveItem;

    public CmdRunEvent(BillCmdWrapper billCmdWrapper, RepositorySaveItem saveItem) {
        this.billCmdWrapper = billCmdWrapper;
        this.saveItem = saveItem;
    }
}
