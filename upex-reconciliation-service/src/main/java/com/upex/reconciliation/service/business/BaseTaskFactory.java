package com.upex.reconciliation.service.business;

import com.upex.bill.dto.params.ResetCheckTimeParam;
import com.upex.data.risk.dto.params.EventCheckParam;
import com.upex.utils.task.BaseTask;
import com.upex.utils.task.function.*;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @ClassName: BaseTaskFactory
 * @date 2022/10/20 14:38
 */
@Slf4j
public class BaseTaskFactory {

    /**
     * 创建baseTask任务
     *
     * @param func 执行方法
     * @return {@link BaseTask }
     * <AUTHOR>
     * @date 2023/4/23 23:33
     */
    public static BaseTask createBaseTask(VoidFunctionP0 func) {
        return new BaseTask() {
            @Override
            protected void baseRun() {
                func.run();
            }
        };
    }

    /**
     * 创建baseTask任务
     *
     * @param accountType  账户类型
     * @param accountParam 账户参数
     * @param func         方法
     * @return com.upex.common.task.BaseTask
     * @throws
     * @Date 2022/10/20 15:07
     * <AUTHOR>
     */
    public static BaseTask createBaseTask(String accountType, String accountParam, VoidFunctionP2<String, String> func) {
        return new BaseTask() {
            @Override
            protected void baseRun() {
                func.run(accountType, accountParam);
            }
        };
    }


    public static BaseTask createBaseTask(ResetCheckTimeParam param, VoidFunctionP1<ResetCheckTimeParam> func) {
        return new BaseTask() {
            @Override
            protected void baseRun() {
                func.run(param);
            }
        };
    }

    public static BaseTask createBaseTask(EventCheckParam eventCheckParam, VoidFunctionP1<EventCheckParam> func) {
        return new BaseTask() {
            @Override
            protected void baseRun() {
                func.run(eventCheckParam);
            }
        };
    }


    /**
     * 创建baseTask任务
     *
     * @param title        报警消息标题
     * @param message      报警消息
     * @param notifyOption 通知选项
     * @param func         方法
     * @return com.upex.common.task.BaseTask
     * @throws
     * @Date 2022/10/24 15:12
     * <AUTHOR>
     */
    public static BaseTask createBaseTask(String title,
                                          String message,
                                          String notifyOption,
                                          VoidFunctionP3<String, String, String> func) {
        return new BaseTask() {
            @Override
            protected void baseRun() {
                try {
                    func.run(title, message, notifyOption);
                } catch (Exception e) {
                    log.error("JobAssetStatisticsBizImpl createBaseTask error!!!", e);
                }
            }
        };
    }


    public static BaseTask createBaseTask(String title,
                                          String message,
                                          String notifyOption,
                                          String templateEnumName,
                                          VoidFunctionP4<String, String, String,String> func) {
        return new BaseTask() {
            @Override
            protected void baseRun() {
                try {
                    func.run(title, message, notifyOption,templateEnumName);
                } catch (Exception e) {
                    log.error("JobAssetStatisticsBizImpl createBaseTask error!!!", e);
                }
            }
        };
    }

}
