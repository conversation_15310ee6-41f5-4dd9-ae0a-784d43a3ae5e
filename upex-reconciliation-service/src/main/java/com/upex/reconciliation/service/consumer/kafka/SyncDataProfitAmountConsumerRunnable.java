package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.google.common.util.concurrent.RateLimiter;
import com.alibaba.otter.canal.protocol.Message;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.profitbacktest.AbstractProfitBakCheck;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.common.constants.enums.ProfitSourceEnum;
import com.upex.reconciliation.service.common.constants.enums.UserTypeEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.entity.BillUserWithdrawProfitRecord;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.model.dto.ProfitBakCheckReq;
import com.upex.reconciliation.service.service.BillUserService;
import com.upex.reconciliation.service.service.BillUserWithdrawProfitRecordService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.EnvUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.KAFKA_CONSUMER_TOPIC_ERROR;

@Slf4j
public class SyncDataProfitAmountConsumerRunnable implements KafkaConsumerLifecycle {

    private volatile boolean running = true;
    private Map<String, Object> consumerConfig;
    private String groupId;
    private Set<Integer> closeConsumerPatition = new HashSet<>();
    private AlarmNotifyService alarmNotifyService;
    private KafkaConsumer consumer;
    private ReconciliationSpringContext context;
    private KafkaProducer<String, String> kafkaProducer;
    private String topic = KafkaTopicEnum.RECON_SYNC_USER_PROFIT_AMOUNT_TOPIC.getCode();
    private RateLimiter rateLimiter;

    BillUserWithdrawProfitRecordService billUserWithdrawProfitRecordService;

    public SyncDataProfitAmountConsumerRunnable(ReconciliationSpringContext context, String kafkaServers, String groupId, Integer maxPollSiz) {
        this.context = context;
        this.billUserWithdrawProfitRecordService = context.getBillUserWithdrawProfitRecordService();
        this.groupId = EnvUtil.getKafkaConsumerGroup(groupId);
        alarmNotifyService = context.getAlarmNotifyService();
        kafkaProducer = context.getKafkaProducer();
        consumerConfig = new HashMap<String, Object>();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServers);
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, this.groupId);
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollSiz);
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
    }

    @Override
    public void shutdown() {

    }

    @Override
    public boolean isRunning() {
        return closeConsumerPatition.size() != 1;
    }

    @Override
    public String getThreadPrefixName() {
        return "sync-user-profit-amount-from-data";
    }

    @Override
    public void run() {
        consumer = new KafkaConsumer<String, Message>(consumerConfig);
        consumer.subscribe(Arrays.asList(topic));
        KafkaConsumerConfig kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig(KafkaTopicEnum.RECON_SYNC_USER_PROFIT_AMOUNT_TOPIC);
        rateLimiter = RateLimiter.create(kafkaConsumerConfig.getMsgRateLimit());
        log.info("SyncDataProfitAmountConsumerRunnable run 。。。");
        GlobalBillConfig globalBillConfig=ReconciliationApolloConfigUtils.getGlobalBillConfig();
        while (running) {
            try {
                List<BillUserWithdrawProfitRecord> records= Lists.newArrayList();
                // 从kafka集群中拉取消息df
                ConsumerRecords<String, String> consumerRecords = consumer.poll(3000);
                consumerRecords.forEach(new java.util.function.Consumer<ConsumerRecord<String, String>>() {
                    @Override
                    public void accept(ConsumerRecord<String, String> consumerRecord) {
                        String msg = consumerRecord.value();
                        if(!globalBillConfig.isSaveProfitRecordFromData()){
                            return;
                        }
                        if(kafkaConsumerConfig.getShowLogOpen()){
                            log.info("SyncDataProfitAmountConsumerRunnableMsg:{},{}",msg,JSONObject.toJSONString(consumerRecord));
                        }
                        if (!consumerRecord.topic().equals(topic)) {
                            alarmNotifyService.alarm(KAFKA_CONSUMER_TOPIC_ERROR, "sync-user-profit-amount", topic, consumerRecord.topic(), consumerRecord.partition());
                        }
                        JSONObject msgProfitAmount=JSONObject.parseObject(msg);
                        JSONObject afterMsg=msgProfitAmount.getJSONObject("after");
                        if(afterMsg==null){
                            return;
                        }
                        BillUserWithdrawProfitRecord billUserWithdrawProfitRecord=buildProfitRecord(afterMsg);
                        if(billUserWithdrawProfitRecord==null){
                            return;
                        }
                        records.add(billUserWithdrawProfitRecord);
                    }
                });
                billUserWithdrawProfitRecordService.batchSaveWithdrawProfitResult(records);
                // 手动提交位点
                consumer.commitSync();
            } catch (Exception e) {
                log.error("SyncDataProfitAmountConsumerRunnable startConsume error ", e);
            }
        }
        consumer.close();
        closeConsumerPatition.add(0);
        log.info("SyncDataProfitAmountConsumerRunnable consumer.close success {} {}", topic, closeConsumerPatition);

    }

    void acquire(int permits) {
        if(permits<=0){
            return;
        }
        double msgLimit=ReconciliationApolloConfigUtils.getKafkaConsumerConfig(KafkaTopicEnum.RECON_SYNC_USER_PROFIT_AMOUNT_TOPIC).getMsgRateLimit();
        if(rateLimiter==null){
            rateLimiter=RateLimiter.create(msgLimit);
        }
        if(rateLimiter.getRate()!=msgLimit){
            rateLimiter.setRate(msgLimit);
        }
        rateLimiter.acquire(permits);
    }

    static BillUserWithdrawProfitRecord buildProfitRecord(JSONObject afterMsg){
        try {
            int userType = afterMsg.getInteger("user_type");
            Long userId = afterMsg.getLong("user_id");
            Date snapshotEndTime = afterMsg.getDate("snapshot_time_right");
            Date snapshotBeginTime = afterMsg.getDate("snapshot_time_left");
            Date requestTime = afterMsg.getDate("create_time");
            BigDecimal profitAmount = afterMsg.getBigDecimal("asset_change_amount");
            BillUserWithdrawProfitRecord billUserWithdrawProfitRecord = BillUserWithdrawProfitRecord.builder()
                    .checkBeginTime(Objects.requireNonNullElse(snapshotBeginTime, DateUtil.addHour(snapshotEndTime,-24)))
                    .checkEndTime(snapshotEndTime)
                    .dataProfitTotalAmount(profitAmount)
                    .requestTime(new Date())
                    .profitSource(ProfitSourceEnum.DATA.getCode())
                    .requestId(userId+ BillConstants.UNDERSCORE_SEPARATOR+snapshotEndTime+BillConstants.UNDERSCORE_SEPARATOR+snapshotBeginTime)
                    .requestTime(requestTime)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .build();
            if (UserTypeEnum.PARENT.getCode().equals(userType)) {
                billUserWithdrawProfitRecord.setParentUserId(userId);
            } else {
                billUserWithdrawProfitRecord.setUserId(userId);
            }
            return billUserWithdrawProfitRecord;
        }catch (Exception e){
            log.error("SyncDataProfitAmountConsumerRunnable buildProfitRecord error:{},msg:{} ", e,JSONObject.toJSONString(afterMsg));
            return null;
        }
    }



}
