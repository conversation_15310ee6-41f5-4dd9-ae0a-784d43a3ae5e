package com.upex.reconciliation.service.service.client.cex.dto.req.common;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.service.client.cex.enmus.*;
import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ModCexUserRequest {

    private Long  id;

    /**
     * 交易所类型（1: Binance, 2: OKX）
     */
    @NotNull(message = IReconCexErrorCode.ILLEGAL_CEXTYPE)
    private Integer cexType;

    /**
     * 交易所注册邮箱
     */
    @NotBlank(message = IReconCexErrorCode.EMAIL_CANNOT_BENULL)
    private String cexEmail;

    /**
     * 母用户ID，子用户修改时必填
     */
    private String parentUserId;
    /**
     * 交易所用户ID
     */
    @NotNull(message = IReconCexErrorCode.USERID_CANNOT_BENULL)
    private String cexUserId;

    private String userKyb;

    /**
     * 账户用途（1: 资金监控, 2: 提现）
     */
    @NotNull(message = IReconCexErrorCode.ILLEGAL_USETYPE)
    private Integer useType;

    /**
     * 交易模式（1: 经典账户, 2: 统一账户）
     */
    @NotNull(message = IReconCexErrorCode.ILLEGAL_TRADETYPE)
    private Integer tradeType;
    /**
     * 用户管理人邮箱
     * 母用户修改时，必填
     */
//    @NotNull(message = "用户管理人邮箱")
    private String userManagerEmail;
    /**
     * 用户管理人ID
     * 母用户修改时，必填
     */
//    @NotNull(message = "用户管理人ID")
    private String userManagerId;
    /**
     *  用户类型（1: 母用户, 2: 虚拟子用户, 3: 普通子用户, 4: 托管子用户）
     *  添加母用户时，必填
     */
    private  Integer userType;


    public void setUseType(Integer useType) {
        CexUseTypeEnum cexUseTypeEnum = CexUseTypeEnum.fromType(useType);
        if (cexUseTypeEnum == null) {
            throw new ApiException(ReconCexExceptionEnum.ILLEGAL_USETYPE);
        }
        this.useType = useType;
    }

    public void setTradeType(Integer tradeType) {
        TradeTypeEnum tradeTypeEnum = TradeTypeEnum.fromType(tradeType);
        if (tradeTypeEnum == null) {
            throw new ApiException(ReconCexExceptionEnum.ILLEGAL_TRADETYPE);
        }
        this.tradeType = tradeType;
    }

    public void setUserType(Integer userType) {
        if(userType!=null) {
            CexUserTypeEnum cexUserTypeEnum = CexUserTypeEnum.fromType(userType);
            if (cexUserTypeEnum == null) {
                throw new ApiException(ReconCexExceptionEnum.ILLEGAL_USERTYPE);
            }
        }
        this.userType = userType;
    }

    public void setCexType(Integer cexType) {
        CexTypeEnum cexTypeEnum = CexTypeEnum.fromType(cexType);
        if (cexTypeEnum == null) {
            throw new ApiException(ReconCexExceptionEnum.ILLEGAL_CEXTYPE);
        }
        this.cexType = cexType;
    }

}
