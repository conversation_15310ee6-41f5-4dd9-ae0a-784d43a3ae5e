package com.upex.reconciliation.service.consumer.decoder;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.google.common.collect.Lists;
import com.upex.config.coin.SpotCoinDTO;
import com.upex.config.facade.coin.CoinConfigService;
import com.upex.mixcontract.common.literal.enums.FinanceBizTypeEnum;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.LogLevelEnum;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.domain.ContractBillChangeModel;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.utils.BizLogUtils;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ContractDecoder extends AbstractMessageDecoder {
    @Resource
    private CoinConfigService coinConfigService;
    private Byte accountType = AccountTypeEnum.S_USDT_MIX_CONTRACT_BL.getCode();

    private static final List<FinanceBizTypeEnum> noReconciliationList = Lists.newArrayList(
            FinanceBizTypeEnum.RISK_MARGIN_INFLOW_MERGE_QUERY, FinanceBizTypeEnum.BURST_LOSS_QUERY,
            FinanceBizTypeEnum.BURST_LONG_LOSS_QUERY, FinanceBizTypeEnum.BURST_SHORT_LOSS_QUERY,
            FinanceBizTypeEnum.BURST_BUY_LOSS_QUERY, FinanceBizTypeEnum.BURST_SELL_LOSS_QUERY,
            FinanceBizTypeEnum.OPEN_LONG_LOCK, FinanceBizTypeEnum.OPEN_SHORT_LOCK,
            FinanceBizTypeEnum.CLOSE_LONG_LOCK, FinanceBizTypeEnum.CLOSE_SHORT_LOCK,
            FinanceBizTypeEnum.BUY_LOCK, FinanceBizTypeEnum.SELL_LOCK,
            FinanceBizTypeEnum.FORCE_BUY_LOCK_SSM, FinanceBizTypeEnum.FORCE_SELL_LOCK_SSM,
            FinanceBizTypeEnum.BURST_BUY_LOCK_SSM, FinanceBizTypeEnum.BURST_SELL_LOCK_SSM,
            FinanceBizTypeEnum.CANCEL_CLOSE_LONG, FinanceBizTypeEnum.CANCEL_CLOSE_SHORT,
            FinanceBizTypeEnum.CANCEL_OPEN_LONG, FinanceBizTypeEnum.CANCEL_OPEN_SHORT,
            FinanceBizTypeEnum.MODIFY_AUTO_APPEND_MARGIN,
            FinanceBizTypeEnum.BURST_CLOSE_SHORT_LOCK, FinanceBizTypeEnum.BURST_CLOSE_LONG_LOCK,
            FinanceBizTypeEnum.FORCE_CLOSE_LONG_LOCK, FinanceBizTypeEnum.FORCE_CLOSE_SHORT_LOCK,
            FinanceBizTypeEnum.OFFSET_REDUCE_CLOSE_LONG_LOCK, FinanceBizTypeEnum.OFFSET_REDUCE_CLOSE_SHORT_LOCK,
            FinanceBizTypeEnum.FORCE_CLOSE_LONG_LOCK, FinanceBizTypeEnum.FORCE_SELL_LOCK_SSM
    );

    @Override
    public List<CommonBillChangeData> doMessageDecode(List<Map<String, String>> dataList, FlatMessage flatMessage, Integer partition, Long offset, Byte accountType) {
        List<CommonBillChangeData> commonBillChangeDataList = new ArrayList<>();
        ApolloReconciliationBizConfig apolloReconciliationBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Map<String, String> map : dataList) {
                if (map.get("biz_type") != null) {
                    FinanceBizTypeEnum financeBizTypeEnum = FinanceBizTypeEnum.toEnum(map.get("biz_type"));
                    if (noReconciliationList.contains(financeBizTypeEnum)) {
                        continue;
                    }
                }
                ContractBillChangeModel contractBillChangeModel = new ContractBillChangeModel();
                contractBillChangeModel.setId(Long.valueOf(map.get("id")));
                contractBillChangeModel.setAccountId(Long.valueOf(map.get("account_id")));
                contractBillChangeModel.setSecondBusinessLine(map.get("second_business_line"));
                contractBillChangeModel.setTokenId(map.get("token_id"));
                contractBillChangeModel.setSymbolId(map.get("symbol_id"));
                contractBillChangeModel.setMarginMode(map.get("margin_mode") != null ? Integer.valueOf(map.get("margin_mode")) : null);
                contractBillChangeModel.setHoldMode(map.get("hold_mode") != null ? Integer.valueOf(map.get("hold_mode")) : null);
                contractBillChangeModel.setHoldSide(map.get("hold_side") != null ? Integer.valueOf(map.get("hold_side")) : null);
                contractBillChangeModel.setBizNo(map.get("biz_no"));
                if (map.get("before_balance_available") != null) {
                    contractBillChangeModel.setBeforeBalanceAvailable(new BigDecimal(map.get("before_balance_available")));
                }
                if (map.get("balance_available_change") != null) {
                    contractBillChangeModel.setBalanceAvailableChange(new BigDecimal(map.get("balance_available_change")));
                }
                if (map.get("before_balance_locked") != null) {
                    contractBillChangeModel.setBeforeBalanceLocked(new BigDecimal(map.get("before_balance_locked")));
                }
                if (map.get("balance_locked_change") != null) {
                    contractBillChangeModel.setBalanceLockedChange(new BigDecimal(map.get("balance_locked_change")));
                }
                if (map.get("before_position_locked_margin") != null) {
                    contractBillChangeModel.setBeforePositionLockedMargin(new BigDecimal(map.get("before_position_locked_margin")));
                }
                if (map.get("position_locked_margin_change") != null) {
                    contractBillChangeModel.setPositionLockedMarginChange(new BigDecimal(map.get("position_locked_margin_change")));
                }
                if (map.get("before_position_margin") != null) {
                    contractBillChangeModel.setBeforePositionMargin(new BigDecimal(map.get("before_position_margin")));
                }
                if (map.get("position_margin_change") != null) {
                    contractBillChangeModel.setPositionMarginChange(new BigDecimal(map.get("position_margin_change")));
                }
                if (map.get("before_position_available") != null) {
                    contractBillChangeModel.setBeforePositionAvailable(new BigDecimal(map.get("before_position_available")));
                }
                if (map.get("position_available_change") != null) {
                    contractBillChangeModel.setPositionAvailableChange(new BigDecimal(map.get("position_available_change")));
                }
                if (map.get("before_position_locked") != null) {
                    contractBillChangeModel.setBeforePositionLocked(new BigDecimal(map.get("before_position_locked")));
                }
                if (map.get("position_locked_change") != null) {
                    contractBillChangeModel.setPositionLockedChange(new BigDecimal(map.get("position_locked_change")));
                }
                if (map.get("balance_change") != null) {
                    contractBillChangeModel.setBalanceChange(new BigDecimal(map.get("balance_change")));
                }
                if (map.get("quote_token_balance_change") != null) {
                    contractBillChangeModel.setQuoteTokenBalanceChange(new BigDecimal(map.get("quote_token_balance_change")));
                }
                if (map.get("count_change") != null) {
                    contractBillChangeModel.setCountChange(new BigDecimal(map.get("count_change")));
                }
                if (map.get("fee") != null) {
                    contractBillChangeModel.setFee(new BigDecimal(map.get("fee")));
                }
                if (map.get("quote_token_fee") != null) {
                    contractBillChangeModel.setQuoteTokenFee(new BigDecimal(map.get("quote_token_fee")));
                }
                if (map.get("show_type") != null) {
                    contractBillChangeModel.setShowType(Byte.valueOf(map.get("show_type")));
                }
                if (map.get("show_flag") != null) {
                    contractBillChangeModel.setShowFlag(Byte.valueOf(map.get("show_flag")));
                }
                if (map.get("biz_time") != null) {
                    contractBillChangeModel.setBizTime(DateUtil.getMillisecondDate(map.get("biz_time")));
                }
                if (map.get("create_time") != null) {
                    contractBillChangeModel.setCreateTime(DateUtil.getMillisecondDate(map.get("create_time")));
                }
                contractBillChangeModel.setParams(map.get("params"));
                if (map.get("biz_type") != null) {
                    contractBillChangeModel.setBizType(map.get("biz_type"));
                }
                if (map.get("version") != null) {
                    contractBillChangeModel.setVersion(Long.valueOf(map.get("version")));
                }

                CommonBillChangeData commonBillChangeData = contractBillChangeModel.getBillChangeData(accountType);
                // 填充 accountType，跟topic一一对应
                commonBillChangeData.setPartition(partition);
                commonBillChangeData.setAccountType(accountType);
                SpotCoinDTO spotCoinDTO = coinConfigService.getCoinBaseInfoByCoinName(map.get("token_id")).get();
                commonBillChangeData.setCoinId(spotCoinDTO.getCoinId());
                // 填充offset
                commonBillChangeData.setOffset(offset);
                BizLogUtils.log(LogLevelEnum.FULL, apolloReconciliationBizConfig, "ContractDecoder decode detail bizTime: {} partition:{} offset:{} dataMAP is {} , contractBillChangeModel is {} , commonBillChangeData is {} ",
                        DateUtil.getDefaultDateStr(commonBillChangeData.getBizTime()),
                        partition, offset,
                        JSONObject.toJSONString(map), JSONObject.toJSONString(contractBillChangeModel), JSONObject.toJSONString(commonBillChangeData));
                commonBillChangeData.setOrderId(map.get("biz_no"));
                commonBillChangeDataList.add(commonBillChangeData);
            }
        }
        return commonBillChangeDataList;
    }
}
