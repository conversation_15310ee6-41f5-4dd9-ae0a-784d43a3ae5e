package com.upex.reconciliation.service.business.profit.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.facade.dto.results.ReconTotalAssetsDetailVo;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;
import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.dao.entity.BillAllConfig;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.ProfitLogDTO;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.dto.AccountProfitDTO;
import com.upex.reconciliation.service.service.BillAllConfigService;
import com.upex.reconciliation.service.service.BillCoinUserPropertyService;
import com.upex.reconciliation.service.service.RealTimeFlowService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.utils.task.TaskManager;
import com.upex.utils.task.TaskVoidBatchResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.common.constants.BillConstants.SEPARATOR;

/**
 * 实时计算用户及子账户盈利检测服务
 *
 * <AUTHOR>
 * @Date 2025/4/25
 */
@Slf4j
@Service
public class ComputeCheckUserProfitServiceImpl extends AbstractCheckUserProfitService {

    @Resource
    private BillAllConfigService billAllConfigService;

    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;

    @Resource
    private RealTimeFlowService realTimeFlowService;

    @Resource(name = "taskManager")
    private TaskManager taskManager;

    @Override
    public AccountProfitDTO getUserProfitAmount(ReconCheckResultsParams params, GlobalBillConfig globalBillConfig, Integer timePeriod, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates) {
        return getUserAccountProfit(params.getUserId(), params.getRequestDate(), globalBillConfig, timePeriod, allCoinsMap, rates);
    }

    /**
     * 获取指定用户盈利数据
     *
     * @param userId
     * @param requestTime
     * @param globalBillConfig
     * @param timePeriod
     * @param allCoinsMap
     * @param rates
     * @return
     */
    private AccountProfitDTO getUserAccountProfit(Long userId, Long requestTime, GlobalBillConfig globalBillConfig, Integer timePeriod, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates) {
        Date requestDate = new Date(requestTime);
        Date beginDate = DateUtil.addMinute(requestDate, BillConstants.NEG_ONE * timePeriod);
        List<String> subSystemList = globalBillConfig.getCheckNegativeAssetsSubSystemList();
        //1、查询用户期初资产
        Stopwatch stopwatch = Stopwatch.createStarted();
        BigDecimal beginBalance = reconCheckBillResultService.getUserBeginAssets(userId, beginDate.getTime(), allCoinsMap, rates, globalBillConfig);
        log.info("checkProfitAccount userAssetsSnapShotService.listUserAssetsBySnapShotTime requestTime:{}, userId:{}, timePeriod:{}, time:{}", requestTime, userId, timePeriod, stopwatch.stop());
        Map<Integer, BigDecimal> allFlowChangeSum = new ConcurrentHashMap<>();
        Map<Integer, BigDecimal> inOutFlowChangeSum = new ConcurrentHashMap<>();
        // 2&3、查询用户对账流水
        Stopwatch stopwatch2 = Stopwatch.createStarted();
        AccountProfitDTO accountProfitDTO = listUserBillFlowsV2(userId, beginDate, requestDate, allFlowChangeSum, inOutFlowChangeSum, subSystemList);
        accountProfitDTO.setBeginBalance(beginBalance);
        log.info("checkProfitAccount listUserBillFlows requestTime:{}, userId:{}, timePeriod:{}, checkProfitSubSystemList:{}, time:{}", requestTime, userId, timePeriod, JSONObject.toJSONString(subSystemList), stopwatch2.stop());
        return accountProfitDTO;
    }

    @Override
    public Map<Long, AccountProfitDTO> getUserChildProfitAmount(ReconCheckResultsParams params, List<Long> childUserIdList, GlobalBillConfig globalBillConfig, Integer timePeriod, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates) {
        Map<Long, AccountProfitDTO> accountProfitMap = new ConcurrentHashMap<>();
        assetUserProfitTaskManager.forEachSubmitBatchAndWait(childUserIdList, (Long userId) -> {
            AccountProfitDTO userAccountProfit = getUserAccountProfit(userId, params.getRequestDate(), globalBillConfig, timePeriod, allCoinsMap, rates);
            log.info("checkProfitAccount accountProfitMap:{} requestTime:{}, parentUserId:{} userId:{}, timePeriod:{}",
                    JSONObject.toJSONString(accountProfitMap), params.getRequestDate(), params.getUserId(), userId, timePeriod);
            accountProfitMap.put(userId, userAccountProfit);
        });
        return accountProfitMap;
    }

    public AccountProfitDTO listUserBillFlowsV2(Long userId, Date beginDate, Date requestDate, Map<Integer, BigDecimal> allFlowChangeSum, Map<Integer, BigDecimal> allInOutFlowChangeSum, List<String> checkProfitSubSystemList) {
        // 业务线并行计算队列
        Map<Byte, AccountProfitDTO> totalAccountProfitMap = new ConcurrentHashMap<>();
        Map<Integer, PriceVo> rates = commonService.getCoinIdTradePriceMap(requestDate.getTime());
        TaskVoidBatchResult queryResultIsEmpty = taskManager.forEachSubmitBatchAndWait(checkProfitSubSystemList, (String accountTypeDesc) -> {
            String[] systemSplit = accountTypeDesc.split(SEPARATOR);
            byte accountType = Byte.parseByte(systemSplit[0]);
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
            ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
            BillAllConfig billAllConfig = billAllConfigService.selectByTypeAndParam(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
            Date endDate = billAllConfig.getCheckOkTime();
            BillCoinUserProperty billCoinUserPropertyToCheckExist = billCoinUserPropertyService.selectBySingleUserIdLatest(userId, accountTypeEnum.getAccountParam(), Integer.valueOf(accountTypeEnum.getCode()));
            Map<Integer, BigDecimal> flowChangeSumMap = new HashMap<>();
            Map<Integer, BigDecimal> inOutFlowChangeSumMap = new HashMap<>();
            if (billCoinUserPropertyToCheckExist == null) {
                return;
            }

            if (beginDate.compareTo(endDate) >= 0) {
                // 如果对账延迟，指查询增量流水
                reconCheckBillResultService.listUserIncrFlowsByAccountType(userId, beginDate, requestDate, flowChangeSumMap, inOutFlowChangeSumMap, accountTypeEnum);
                log.info("CheckBillResultServiceImpl.listUserBillFlows beginDate <= endDate userId:{} beginDate:{} endDate:{} accountTypeDesc:{}", userId, DateUtil.getFormatDate(beginDate.getTime()), DateUtil.getFormatDate(requestDate.getTime()), accountTypeDesc);
            }else{
                log.info("CheckBillResultServiceImpl.listUserBillFlows userId:{} beginDate:{} endDate:{} accountTypeDesc:{}", userId, DateUtil.getFormatDate(beginDate.getTime()), DateUtil.getFormatDate(endDate.getTime()), accountTypeDesc);

                // 获取原始数据
                listUserHistoryBillsByAccountType(userId, beginDate, accountTypeDesc, accountTypeEnum, apolloBillConfig, endDate, flowChangeSumMap, inOutFlowChangeSumMap);

                // 先查增量流水
                reconCheckBillResultService.listUserIncrFlowsByAccountType(userId, endDate, requestDate, flowChangeSumMap, inOutFlowChangeSumMap, accountTypeEnum);
            }
            if(MapUtils.isEmpty(flowChangeSumMap) && MapUtils.isEmpty(inOutFlowChangeSumMap)){
                return;
            }

            // 合并计算入出盈利和总盈利
            AccountProfitDTO accountProfitDTO = new AccountProfitDTO();
            flowChangeSumMap.forEach((coinId, flowChangeSum) -> {
                allFlowChangeSum.compute(coinId, (k, oldValue) -> oldValue == null ? flowChangeSum : oldValue.add(flowChangeSum));
                accountProfitDTO.getProfitCoinMap().compute(coinId, (k, oldValue)->oldValue == null ? flowChangeSum : oldValue.add(flowChangeSum));
            });
            inOutFlowChangeSumMap.forEach((coinId, flowChangeSum)->{
                allInOutFlowChangeSum.compute(coinId, (k, oldValue) -> oldValue == null ? flowChangeSum : oldValue.add(flowChangeSum));
                accountProfitDTO.getProfitCoinMap().compute(coinId, (k, oldValue)->oldValue == null ? flowChangeSum.negate() : oldValue.add(flowChangeSum.negate()));
            });

            BigDecimal profitAmount = BigDecimal.ZERO;
            for (Map.Entry<Integer, BigDecimal> profitCoin : accountProfitDTO.getProfitCoinMap().entrySet()) {
                BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(profitCoin.getKey(), rates);
                profitAmount = profitAmount.add(profitCoin.getValue().multiply(rate));
            }
            accountProfitDTO.setProfitAmount(profitAmount);
            totalAccountProfitMap.put(accountType, accountProfitDTO);
        }, checkProfitSubSystemList.size());

        if (queryResultIsEmpty != null && queryResultIsEmpty.getFails().size() > 0) {
            log.error("billCoinTypeUserPropertyMapper.selectUserTypeByTime error size={}", queryResultIsEmpty.getFails().size());
            throw new ApiException(BillExceptionEnum.SYSTEM_ERROR, "billCoinTypeUserPropertyMapper.selectUserTypeByTime error");
        }

        // 汇总所有业务线盈亏
        AccountProfitDTO totalAccountProfit = new AccountProfitDTO();
        totalAccountProfit.setProfitAmount(totalAccountProfitMap.values().stream().map(AccountProfitDTO::getProfitAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        Map<Integer, BigDecimal> totalCoinProfitMap = new HashMap<>();
        totalAccountProfitMap.values().stream().forEach(accountProfit -> {
            accountProfit.getProfitCoinMap().forEach((coinId, profitAmount) -> {
                totalCoinProfitMap.compute(coinId, (k, oldValue) -> oldValue == null ? profitAmount : oldValue.add(profitAmount));
            });
        });
        totalAccountProfit.setProfitCoinMap(totalCoinProfitMap);
        totalAccountProfit.setAccountProfitMap(totalAccountProfitMap);
        log.info("checkProfitAccountXxl checkProfitAccountForTime requestDate:{} userId:{} totalAccountProfit:{}", requestDate.getTime(), userId, JSONObject.toJSONString(totalAccountProfit));
        return totalAccountProfit;
    }


    private void listUserHistoryBillsByAccountType(Long userId, Date beginDate, String accountTypeDesc, AccountTypeEnum accountTypeEnum, ApolloReconciliationBizConfig apolloBillConfig, Date endDate, Map<Integer, BigDecimal> flowChangeSumMap, Map<Integer, BigDecimal> inOutFlowChangeSumMap) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<BillCoinTypeUserProperty> billCoinTypeUserPropertieList = reconUserAssetsSnapShotService.getTimeDifferenceOnChange(userId, beginDate.getTime(), endDate.getTime(), Integer.valueOf(accountTypeEnum.getCode()), accountTypeEnum.getAccountParam());
        if (CollectionUtils.isEmpty(billCoinTypeUserPropertieList)) {
            log.info("billCoinTypeUserPropertyMapper.selectUserTypeByTime billCoinTypeUserPropertyList is empty userId:{} beginDate:{} endDate:{} accountTypeDesc:{} 无数据", userId, DateUtil.getFormatDate(beginDate.getTime()), DateUtil.getFormatDate(endDate.getTime()), accountTypeDesc);
            return;
        }
        Set<String> inOutFlowTypes = apolloBillConfig.getCheckProfitInOutBizType();

        // 数据合并计算
        for (BillCoinTypeUserProperty billCoinTypeUserProperty : billCoinTypeUserPropertieList) {
            try {
                if (CollectionUtils.isNotEmpty(inOutFlowTypes) && inOutFlowTypes.contains(billCoinTypeUserProperty.getBizType())) {
                    // 如果key不存在，put asset
                    inOutFlowChangeSumMap.computeIfAbsent(billCoinTypeUserProperty.getCoinId(), v -> BigDecimal.ZERO);
                    // 如果key存在，all + asset
                    inOutFlowChangeSumMap.computeIfPresent(billCoinTypeUserProperty.getCoinId(), (key, oldValue) -> oldValue.add(billCoinTypeUserProperty.getChangePropSum(accountTypeEnum.getCode())));
                }
            } catch (Exception e) {
                log.error("查询增量流水异常，accountTypeEnum:{}, userId:{}, billEndDate:{}, requestDate:{}, inOutFlowTypes:{}, billCoinTypeUserProperty:{}",
                        accountTypeEnum, userId, beginDate, endDate, JSONObject.toJSONString(inOutFlowTypes), JSONObject.toJSONString(billCoinTypeUserProperty));
                throw e;
            }
            // 如果key不存在，put asset
            flowChangeSumMap.computeIfAbsent(billCoinTypeUserProperty.getCoinId(), v -> BigDecimal.ZERO);
            // 如果key存在，all + asset
            flowChangeSumMap.computeIfPresent(billCoinTypeUserProperty.getCoinId(), (key, oldValue) -> oldValue.add(billCoinTypeUserProperty.getChangePropSum(accountTypeEnum.getCode())));
        }
        log.info("billCoinTypeUserPropertyMapper.selectUserTypeByTime prop sum userId:{} beginDate:{} endDate:{} accountTypeDesc:{} flowChangeSumMap:{} inOutFlowChangeSumMap:{} time:{}", userId, DateUtil.getFormatDate(beginDate.getTime()), DateUtil.getFormatDate(endDate.getTime()), accountTypeDesc, JSON.toJSONString(flowChangeSumMap), JSON.toJSONString(inOutFlowChangeSumMap), stopwatch.stop());
        log.info("checkProfitAccountXxl checkProfitAccountForTime billCoinTypeUserPropertyMapper.selectUserTypeByTime prop sum userId:{} beginDate:{} endDate:{} accountTypeDesc:{} allFlowChangeList:{} inOutFlowChangeList:{} profitFlowChangeList:{}",
                userId, DateUtil.getFormatDate(beginDate.getTime()), DateUtil.getFormatDate(endDate.getTime()), accountTypeDesc, JSONObject.toJSONString(convertProfitLogList(endDate, accountTypeEnum, billCoinTypeUserPropertieList.stream().filter(e -> !e.isChangePropZero()).collect(Collectors.toList()))),
                JSONObject.toJSONString(convertProfitLogList(endDate, accountTypeEnum, billCoinTypeUserPropertieList.stream().filter(e -> !e.isChangePropZero() && inOutFlowTypes.contains(e.getBizType())).collect(Collectors.toList()))),
                JSONObject.toJSONString(convertProfitLogList(endDate, accountTypeEnum, billCoinTypeUserPropertieList.stream().filter(e -> !e.isChangePropZero() && !inOutFlowTypes.contains(e.getBizType())).collect(Collectors.toList()))));
    }

    private Map<Integer, List<ProfitLogDTO>> convertProfitLogList(Date requestDate, AccountTypeEnum accountTypeEnum, List<BillCoinTypeUserProperty> profitLogList) {
        if (CollectionUtils.isEmpty(profitLogList)) {
            return new HashMap<>();
        }

        Map<Integer, PriceVo> rates = commonService.getCoinIdTradePriceMap(requestDate.getTime());

        return profitLogList.stream()
                .map(coinTypeUserProperty -> getProfitLogDTO(accountTypeEnum, rates, coinTypeUserProperty))
                .sorted(Comparator.comparing(ProfitLogDTO::getUAmount).reversed())
                .collect(Collectors.groupingBy(ProfitLogDTO::getCoinId));
    }

    private ProfitLogDTO getProfitLogDTO(AccountTypeEnum accountTypeEnum, Map<Integer, PriceVo> rates, BillCoinTypeUserProperty coinTypeUserProperty) {
        ProfitLogDTO profitLogDTO = new ProfitLogDTO();
        profitLogDTO.setUserId(coinTypeUserProperty.getUserId());
        profitLogDTO.setBizType(coinTypeUserProperty.getBizType());
        Integer coinId = coinTypeUserProperty.getCoinId();
        profitLogDTO.setCoinId(coinId);
        profitLogDTO.setCount(coinTypeUserProperty.getChangePropSum(accountTypeEnum.getCode()));
        BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(coinId, rates);
        profitLogDTO.setRate(rate);
        profitLogDTO.setUAmount(profitLogDTO.calculateUAmount());
        profitLogDTO.setAccountType(accountTypeEnum.getCode());

        return profitLogDTO;
    }

    private BigDecimal mergeFlows(Map<Integer, BigDecimal> allFlowChangeSum, Map<Integer, BigDecimal> inOutFlowChangeSum, Long requestDate, Long userId) {
        if (allFlowChangeSum.isEmpty() && inOutFlowChangeSum.isEmpty()) {
            return BigDecimal.ZERO;
        }
        log.info("checkProfitAccount mergeFlows requestDate:{} userId:{}, allFlowChangeSum:{}, inOutFlowChangeSum:{}", requestDate, userId, JSONObject.toJSONString(allFlowChangeSum), JSONObject.toJSONString(inOutFlowChangeSum));

        // Fetch rates map once
        Map<Integer, PriceVo> rates = commonService.getCoinIdTradePriceMap(requestDate);

        for (Map.Entry<Integer, BigDecimal> coinFlow : inOutFlowChangeSum.entrySet()) {
            // 如果key存在，则 all - inOut,不可能不存在
            allFlowChangeSum.computeIfPresent(coinFlow.getKey(), (key, oldValue) -> oldValue.subtract(coinFlow.getValue()));
        }

        BigDecimal result = BigDecimal.ZERO;

        // Calculate result using rates map
        for (Map.Entry<Integer, BigDecimal> profitCoin : allFlowChangeSum.entrySet()) {
            Integer coinId = profitCoin.getKey();
            BigDecimal profitCoinValue = profitCoin.getValue();
            BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(coinId, rates);
            BigDecimal amountToU = profitCoinValue.multiply(rate);
            log.info("checkProfitAccount mergeFlows requestDate:{} userId:{}, coinId:{}, rates:{}, profitCoinValue:{}, amountToU:{}", requestDate, userId, coinId, rate, profitCoinValue, amountToU);
            result = result.add(profitCoinValue.multiply(rate));
        }

        return result;
    }
}
