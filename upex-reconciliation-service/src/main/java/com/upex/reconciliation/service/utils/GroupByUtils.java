package com.upex.reconciliation.service.utils;

import com.alibaba.fastjson.JSON;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.dao.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class GroupByUtils {


    /**
     * 获取分组数据
     * 按照coin纬度聚合后，更新到 coinPropertyMap中
     *
     * @param list
     * @return
     */
    public static void groupByCoinId(Map<Integer, BillCoinProperty> coinPropertyMap, Collection<BillCoinUserProperty> list, Date checkOkTime) {
        if (CollectionUtils.isNotEmpty(list)) {
            Map<Integer, List<BillCoinUserProperty>> map = list.stream().collect(Collectors.groupingBy(BillCoinUserProperty::getCoinId));
            map.forEach((key, value) -> {
                BillCoinProperty billCoinProperty = coinPropertyMap.computeIfAbsent(key, k -> new BillCoinProperty());
                billCoinProperty.setCoinId(key);
                billCoinProperty.setCheckTime(checkOkTime);
                for (BillCoinUserProperty billCoinUserProperty : value) {
                    add(billCoinProperty, billCoinUserProperty);
                }
            });
        }
    }


    /**
     * 获取分组数据
     * 按照  coin + bizType纬度进行聚合
     *
     * @param list
     * @return
     */
    public static void groupByCoinIdType(Map<String, BillCoinTypeProperty> coinTypePropertyMap, Collection<BillCoinTypeUserProperty> list, Date checkOkTime) {
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, List<BillCoinTypeUserProperty>> map = list.stream().collect(Collectors.groupingBy(billCoinTypeUserProperty -> GroupByKeyUtils.getKey(GroupByKeyUtils.COINID_TYPEID, 0L, billCoinTypeUserProperty.getCoinId(), billCoinTypeUserProperty.getBizType())));
            map.forEach((key, value) -> {
                BillCoinTypeProperty billCoinTypeProperty = coinTypePropertyMap.computeIfAbsent(key, k -> new BillCoinTypeProperty());
                String[] strs = StringUtils.split(key, "#");
                billCoinTypeProperty.setCoinId(Integer.valueOf(strs[0]));
                billCoinTypeProperty.setBizType(strs[1]);
                billCoinTypeProperty.setCheckTime(checkOkTime);
                for (BillCoinTypeUserProperty billCoinTypeUserProperty : value) {
                    add(billCoinTypeProperty, billCoinTypeUserProperty);
                }
            });
        }
    }


    public static void add(BillCoinTypeProperty billCoinProperty, BillCoinTypeUserProperty billCoinUserProperty) {
        billCoinProperty.setProp1(billCoinProperty.getProp1().add(billCoinUserProperty.getProp1()));
        billCoinProperty.setProp2(billCoinProperty.getProp2().add(billCoinUserProperty.getProp2()));
        billCoinProperty.setProp3(billCoinProperty.getProp3().add(billCoinUserProperty.getProp3()));
        billCoinProperty.setProp4(billCoinProperty.getProp4().add(billCoinUserProperty.getProp4()));
        billCoinProperty.setProp5(billCoinProperty.getProp5().add(billCoinUserProperty.getProp5()));

    }

    public static void add(BillCoinProperty billCoinProperty, BillCoinUserProperty billCoinUserProperty) {
        billCoinProperty.setProp1(billCoinProperty.getProp1().add(billCoinUserProperty.getProp1()));
        billCoinProperty.setProp2(billCoinProperty.getProp2().add(billCoinUserProperty.getProp2()));
        billCoinProperty.setProp3(billCoinProperty.getProp3().add(billCoinUserProperty.getProp3()));
        billCoinProperty.setProp4(billCoinProperty.getProp4().add(billCoinUserProperty.getProp4()));
        billCoinProperty.setProp5(billCoinProperty.getProp5().add(billCoinUserProperty.getProp5()));

    }
}
