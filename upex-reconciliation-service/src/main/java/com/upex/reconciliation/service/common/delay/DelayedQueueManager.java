package com.upex.reconciliation.service.common.delay;

import com.upex.reconciliation.service.common.thread.BaseAsyncTaskManager;
import com.upex.reconciliation.service.model.config.DelayRetryConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 延迟重试队列管理类
 *
 * <AUTHOR>
 * @Date 2024/12/16
 */
@Slf4j
@Component
public class DelayedQueueManager {

    @Resource(name = "billBusinessTaskManager")
    private BaseAsyncTaskManager billBusinessTaskManager;

    /**
     * 延迟队列
     */
    private static final DelayQueue<DelayedRetryWrapper> DELAY_RETRY_QUEUE = new DelayQueue<>();

    /**
     * 入队：默认延迟1分钟，重试3次
     *
     * @param data
     * @param function
     * @param <T>
     * @param <R>
     */
    public <T, R> void offer(T data, Function<T, R> function) {
        this.offer(null, data, function);
    }

    /**
     * 入队：指定延迟时间，默认重试3次
     *
     * @param delayTime
     * @param data
     * @param function
     * @param <T>
     * @param <R>
     */
    public <T, R> void offer(Long delayTime, T data, Function<T, R> function) {
        this.offer(delayTime, null, data, function);
    }

    /**
     * 入队：指定延迟时间、重试次数
     *
     * @param delayTime
     * @param maxRetryCount
     * @param data
     * @param function
     * @param <T>
     * @param <R>
     */
    public <T, R> void offer(Long delayTime, Integer maxRetryCount, T data, Function<T, R> function) {
        DelayRetryConfig delayRetryConfig = ReconciliationApolloConfigUtils.getDelayRetryConfig();
        if (Objects.isNull(delayTime)) {
            delayTime = delayRetryConfig.getDelayTime();
        }
        if (Objects.isNull(maxRetryCount)) {
            maxRetryCount = delayRetryConfig.getMaxRetryCount();
        }
        Integer retryCount = RetryCountThreadLocal.get();
        retryCount = Objects.equals(retryCount, -1) ? maxRetryCount : retryCount;
        DELAY_RETRY_QUEUE.offer(new DelayedRetryWrapper(delayTime, retryCount, data, function));
    }

    @PostConstruct
    public void init() {
        // 启动拉取线程
        Thread thread = new Thread(() -> {
            while (true) {
                try {
                    DelayedRetryWrapper delayedRetryWrapper = DELAY_RETRY_QUEUE.poll(10, TimeUnit.MILLISECONDS);
                    if (Objects.nonNull(delayedRetryWrapper)) {
                        handleTask(delayedRetryWrapper);
                    }
                } catch (Throwable ex) {
                    log.error("DelayedQueueManager failed", ex);
                    throw new RuntimeException(ex);
                }
            }
        }, "DelayedQueueManager");
        thread.start();
    }

    /**
     * 延迟重试任务处理
     *
     * @param delayedRetryWrapper
     */
    private void handleTask(DelayedRetryWrapper delayedRetryWrapper) {
        // 判断重试次数是否<=0
        if (delayedRetryWrapper.getMaxRetryCount() <= 0) {
            return;
        }
        billBusinessTaskManager.submit(() -> {
            RetryCountThreadLocal.set(delayedRetryWrapper.getMaxRetryCount() - 1);
            try {
                delayedRetryWrapper.getFunction().apply(delayedRetryWrapper.getData());
            } finally {
                RetryCountThreadLocal.remove();
            }
        });
    }
}
