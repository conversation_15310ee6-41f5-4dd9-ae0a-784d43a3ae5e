package com.upex.reconciliation.service.service.client.cex.dto.req.binance;

import com.binance.connector.client.common.Pair;
import lombok.Data;

@Data
public class BinanceSubUserContractApiReq extends BinanceSubUserApiReq{
    /**
     * 1 U合约 2  币本位合约
     */
    private Integer futuresType;

    public static final Integer FUTURES_TYPE_U = 1;
    public static final Integer FUTURES_TYPE_COIN = 2;

    public void setFuturesType(Integer futuresType) {
        queryParams.add(new Pair("futuresType", String.valueOf(futuresType)));
    }
}
