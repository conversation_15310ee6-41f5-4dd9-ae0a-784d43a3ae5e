package com.upex.reconciliation.service.dao.mapper.cex;

import com.upex.reconciliation.service.dao.cex.entity.CoinNameConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CoinNameConfigMapper {

    /**
     * 插入币种名称配置
     *
     * @param record 配置对象
     * @return 影响行数
     */
    int insert(CoinNameConfig record);

    /**
     * 批量插入币种名称配置
     *
     * @param list 配置列表
     * @return 影响行数
     */
    int batchInsert(List<CoinNameConfig> list);

    /**
     * 根据交易所类型和币种名称查询对应的 BG 币种名称
     *
     * @param cexType 交易所类型
     * @param cexCoinName 交易所币种名称
     * @return BG 币种名称
     */
    String selectBgCoinNameByCexTypeAndCexCoinName(@Param("cexType") Integer cexType,
                                                  @Param("cexCoinName") String cexCoinName);

    /**
     * 根据交易所类型和币种名称查询完整配置信息
     *
     * @param cexType 交易所类型
     * @param cexCoinName 交易所币种名称
     * @return CoinNameConfig 对象
     */
    CoinNameConfig selectByCexTypeAndCexCoinName(@Param("cexType") Integer cexType,
                                                 @Param("cexCoinName") String cexCoinName);

    List<CoinNameConfig> selectAll();
}
