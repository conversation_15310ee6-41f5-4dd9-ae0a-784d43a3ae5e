package com.upex.reconciliation.service.business.profitbacktest;

import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class ProfitBakCheckFactory {

    @Resource
    List<IProfitBakCheck> profitBakCheckList;

    public void profitBakCheck() {
        profitBakCheckList.forEach((IProfitBakCheck profitBakCheck) -> {
            if (profitBakCheck.isSupport()) {
                log.info("{} start check profit", profitBakCheck.getClass().getName());
                profitBakCheck.checkProfit();
                return;
            }
        });
    }

}
