package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.AssetsBillConfigSnapshot;
import com.upex.reconciliation.service.dao.mapper.AssetsBillConfigSnapshotMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 总账快照
 */
@Service
public class AssetsBillConfigSnapshotService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "assetsBillConfigSnapshotMapper")
    private AssetsBillConfigSnapshotMapper assetsBillConfigSnapshotMapper;

    public AssetsBillConfigSnapshot selectLastByTypeAndParam(String assetsCheckType, String assetsCheckParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillConfigSnapshotMapper.selectLastByTypeAndParam(assetsCheckType, assetsCheckParam));
    }

    public int batchInsert(List<AssetsBillConfigSnapshot> records) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillConfigSnapshotMapper.batchInsert(records));
    }

    public int deleteAll(String assetsCheckType, String assetsCheckParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillConfigSnapshotMapper.deleteAll(assetsCheckType, assetsCheckParam));
    }

    public boolean deleteByCheckTime(String assetAccountType, String assetAccountParam, Date inAllRollbackTime) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillConfigSnapshotMapper.deleteByCheckTime(assetAccountType, assetAccountParam, inAllRollbackTime));
    }

    public boolean deleteByLtCheckTime(String assetAccountType, String assetAccountParam, Date inAllRollbackTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillConfigSnapshotMapper.deleteByLtCheckTime(assetAccountType, assetAccountParam, inAllRollbackTime, batchSize));
    }
}
