package com.upex.reconciliation.service.common.constants.enums;

/**
 * redis配置key枚举
 *
 * <AUTHOR>
 */
public enum BillRedisKeyEnum {

    RECON_WITHDRAWAL_CHECK_PROFIT_RESULT("recon:withdrawal:check:profit:result:%s:%s:%s:%s", "提币盈利检查结果"),

    RECON_WITHDRAWAL_CHECK_TOTAL_RESULT("recon:withdrawal:check:total:result:%s:%s", "提币检测接口维度，总的结果缓存"),

    RECON_WITHDRAWAL_CHECK_FINAL_RESULT("recon:withdrawal:check:result:%s:%s", "提币检测接口存盘"),

    RECON_CACHE_WITHDRAWAL_ALARM_LOCK("recon:cache:withdrawal:alarm:lock:%s", "提币检查报警锁"),
    /*** 时间+报警原因 ***/
    RECON_CACHE_WITHDRAWAL_LARK_ALARM_LOCK("recon:cache:withdrawal:lark:alarm:lock:%s:%s", "提币检查lark报警锁"),
    /*** 对账平台允许提币值 ***/
    RECON_CACHE_WITHDRAWAL_BLOCK_ALARM_THRESHOLD_VALUE("recon:cache:withdrawal:block:alarm:threshold:value:%s", "对账平台允许提币值"),
    /*** 对账平台允许提币值订单号 ***/
    RECON_CACHE_WITHDRAWAL_BLOCK_ALARM_THRESHOLD_ORDER_NO("recon:cache:withdrawal:block:alarm:threshold:order:no:%s", "对账平台允许提币值订单号"),
    /*** 对账平台允许提币值锁 ***/
    RECON_CACHE_WITHDRAWAL_BLOCK_ALARM_THRESHOLD_LOCK("recon:cache:withdrawal:block:alarm:threshold:lock", "对账平台允许提币值锁"),
    /*** 内部转账订单key ***/
    RECON_INNER_TRANSFER_ORDER_KEY("recon:inner:transfer:order:key", "内部转账订单key"),
    /*** 内部转账TxId分区key ***/
    RECON_INNER_TRANSFER_TX_ID_PARTITION_KEY("recon:inner:transfer:tx:id:partition:key:%s", "内部转账TxId分区key"),
    /*** 理财订单key ***/
    RECON_FINANCE_ORDER_KEY("recon:finance:order:key:%s", "理财订单key"),
    RECON_WITHDRAWAL_PROFIT_RISK_RESULT("recon:withdrawal:profit:risk:result:%s", "提币盈利检查推送风控结果"),
    RECON_WITHDRAWAL_PROFIT_RISK_USER_ALARM("recon:withdrawal:profit:risk:user:alarm", "提币盈利检查用户报警-盈利检测任务扫描推送告警"),
    /*** 第三方资产密钥key  ***/
    THIRD_CEX_ASSET_SECRET_KEY("third:cex:asset:%s:%s:%s:%s", "第三方资产密钥key"),

    THIRD_CEX_ASSET_CONFIG_KEY("third:cex:config:%s:%s", "第三方资产用户配置");
    ;
    private String key;
    private String desc;
    private String object;

    BillRedisKeyEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    BillRedisKeyEnum(String key, String desc, String object) {
        this.key = key;
        this.desc = desc;
        this.object = object;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public String getObject() {
        return object;
    }


}