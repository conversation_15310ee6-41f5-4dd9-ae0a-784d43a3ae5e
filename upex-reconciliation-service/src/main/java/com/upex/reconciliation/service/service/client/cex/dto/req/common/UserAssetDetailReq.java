package com.upex.reconciliation.service.service.client.cex.dto.req.common;

import com.upex.reconciliation.service.service.client.cex.enmus.IReconCexErrorCode;
import com.upex.reconciliation.service.service.client.cex.enmus.ThirdAssetType;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class UserAssetDetailReq extends CommonReq{
    @NotNull(message = IReconCexErrorCode.THIRD_ASSET_TYPE_CANNOT_BENULL)
    private ThirdAssetType assetType;
}
