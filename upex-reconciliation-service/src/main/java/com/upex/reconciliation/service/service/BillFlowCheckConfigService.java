package com.upex.reconciliation.service.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.Functions;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillFlowCheckConfig;
import com.upex.reconciliation.service.dao.mapper.BillFlowCheckConfigMapper;
import com.upex.reconciliation.service.model.config.UserPermissionConfig;
import com.upex.reconciliation.service.model.dto.BillFlowBizTypeInOutDTO;
import com.upex.reconciliation.service.model.enums.BillFlowCheckEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * 流水检测配置服务
 *
 * <AUTHOR>
 * @Date 2024/12/26
 */
@Slf4j
@Service
public class BillFlowCheckConfigService {

    @Resource
    private BillDbHelper dbHelper;

    @Resource
    private BillFlowCheckConfigMapper billFlowCheckConfigMapper;

    /**
     * 订单出入金额检测规则，本地缓存：accountType -> Set<bizType>
     */
    private final LoadingCache<String, Set<String>> bizTypeInOutCache =
            Caffeine.newBuilder()
                    .expireAfterWrite(10, TimeUnit.MINUTES)
                    .build(accountType -> {
                        List<BillFlowCheckConfig> billFlowConfigs = getBillConfigList(BillFlowCheckEnum.BILL_FLOW_BIZ_TYPE_IN_OUT.getCode(), accountType);
                        if (CollectionUtils.isEmpty(billFlowConfigs)) {
                            return Set.of();
                        }
                        Set<String> intOutSet = new HashSet<>();
                        for (BillFlowCheckConfig config : billFlowConfigs) {
                            if (StringUtils.isBlank(config.getParam())) {
                                continue;
                            }
                            BillFlowBizTypeInOutDTO bizTypeInOut = JSONObject.parseObject(config.getParam(), BillFlowBizTypeInOutDTO.class);
                            if (Objects.isNull(bizTypeInOut) || CollectionUtils.isEmpty(bizTypeInOut.getIn()) || CollectionUtils.isEmpty(bizTypeInOut.getOut())) {
                                continue;
                            }
                            intOutSet.addAll(bizTypeInOut.getIn());
                            intOutSet.addAll(bizTypeInOut.getOut());
                        }
                        return intOutSet;
                    });

    /**
     * 本地缓存：accountType->Map<bizType, List<BillFlowCheckConfig>>
     */
    private final LoadingCache<String, Map<String,List<UserPermissionConfig>>> userPermissionCache =
            Caffeine.newBuilder()
                    .expireAfterWrite(10, TimeUnit.MINUTES)
                    .build(accountType -> {
                        List<BillFlowCheckConfig> billFlowConfigs = getBillConfigList(BillFlowCheckEnum.BILL_FLOW_USER_PERMISSION.getCode(), accountType);
                        return billFlowConfigs.stream()
                                .filter(checkConfig -> StringUtils.isNotBlank(checkConfig.getCheckType()) && StringUtils.isNotBlank(checkConfig.getParam()))
                                .collect(groupingBy(BillFlowCheckConfig::getCheckType,
                                        Collectors.mapping(config -> JSONObject.parseObject(config.getParam(), UserPermissionConfig.class),
                                                collectingAndThen(
                                                        toList(),
                                                        list -> list.stream()
                                                                .sorted(Comparator.comparing(UserPermissionConfig::getOrder))
                                                                .collect(toList())))));
                    });


    /**
     * 本地缓存：accountType->Map<bizType, BillFlowCheckConfig>
     */
    private final LoadingCache<String, Map<String, BillFlowCheckConfig>> subAccountCache =
            Caffeine.newBuilder()
                    .expireAfterWrite(10, TimeUnit.MINUTES)
                    .build(accountType -> {
                        List<BillFlowCheckConfig> billFlowConfigs = getBillConfigList(BillFlowCheckEnum.BILL_FLOW_SUB_ACCOUNT.getCode(), accountType);
                        return billFlowConfigs.stream()
                                .filter(checkConfig -> StringUtils.isNotBlank(checkConfig.getCheckType()))
                                .collect(toMap(BillFlowCheckConfig::getCheckType, Functions.identity()));
                    });

    /**
     * 根据业务线查询订单出入配置
     *
     * @param accountType
     * @return
     */
    public Set<String> getBizTypeInOutSet(String accountType) {
        return bizTypeInOutCache.get(accountType);
    }

    /**
     * 根据业务线及业务类型查询权限code
     *
     * @param accountType
     * @return
     */
    public List<UserPermissionConfig> getUserPermissionCode(String accountType, String bizType) {
        Map<String, List<UserPermissionConfig>> bizTypeMap = userPermissionCache.get(accountType);
        if (CollectionUtils.isEmpty(bizTypeMap)) {
            return null;
        }
        return bizTypeMap.get(bizType);
    }

    /**
     * 根据业务线及业务类型获取子账户配置
     *
     * @param accountType
     * @return
     */
    public BillFlowCheckConfig getSubAccount(String accountType, String bizType) {
        Map<String, BillFlowCheckConfig> bizTypeMap = subAccountCache.get(accountType);
        if (CollectionUtils.isEmpty(bizTypeMap)) {
            return null;
        }
        return bizTypeMap.get(bizType);
    }

    public List<BillFlowCheckConfig> getBillConfigList(Integer type, String accountType) {
        return dbHelper.doDbOpInReconMaster(() -> billFlowCheckConfigMapper.getBillConfigList(type, accountType));
    }

    /**
     * 维护表数据
     *
     * @param jobParam
     */
    public void repairBillFlowCheckConfig(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        log.info("repairBillFlowCheckConfig data:{}", jobParam);
        String action = jsonObject.getString("action");
        JSONArray dataList = jsonObject.getJSONArray("dataList");
        if (CollectionUtil.isEmpty(dataList)) {
            return;
        }
        Date nowDate = new Date();
        if ("insert".equals(action)) {
            List<BillFlowCheckConfig> checkConfigList = Convert.toList(BillFlowCheckConfig.class, dataList);
            if (CollectionUtil.isNotEmpty(checkConfigList)) {
                for (BillFlowCheckConfig checkConfig : checkConfigList) {
                    checkConfig.setCreateTime(nowDate);
                    checkConfig.setUpdateTime(nowDate);
                }
                batchInsert(checkConfigList);
            }
        } else if ("update".equals(action)) {
            List<BillFlowCheckConfig> checkConfigList = Convert.toList(BillFlowCheckConfig.class, dataList);
            if (CollectionUtil.isNotEmpty(checkConfigList)) {
                for (BillFlowCheckConfig checkConfig : checkConfigList) {
                    checkConfig.setUpdateTime(nowDate);
                    updateByPrimaryKeySelective(checkConfig);
                }
            }
        }
    }

    private int updateByPrimaryKeySelective(BillFlowCheckConfig checkConfig) {
        return dbHelper.doDbOpInReconMaster(() -> billFlowCheckConfigMapper.updateByPrimaryKeySelective(checkConfig));
    }

    private int batchInsert(List<BillFlowCheckConfig> checkConfigList) {
        return dbHelper.doDbOpInReconMaster(() -> billFlowCheckConfigMapper.batchInsert(checkConfigList));
    }

    public static void main(String[] args) {
        BillFlowCheckConfig checkConfig = new BillFlowCheckConfig();
        checkConfig.setType(2);
        checkConfig.setCheckType("232");

        UserPermissionConfig config = new UserPermissionConfig();
        config.setCodeList(List.of(90L, 93L));
        config.setRouteRule("\\nimport com.upex.reconciliation.service.model.config.UserPermissionConfig;\\nString notes = billFlowData.params;\\nreturn notes !=null && config.params!=null && config.params.contains(notes);");
        config.setParams("placeSellOrder,cancelOrder,completeOrder");
        String json = JSONObject.toJSONString(config);
        System.out.println(json);
        checkConfig.setParam(json);
        String checkJson = JSONObject.toJSONString(checkConfig);
        System.out.println(checkJson);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("action", "insert");
        jsonObject.put("dataList", List.of(checkConfig));
        String jsonString = JSONObject.toJSONString(jsonObject);
        System.out.println(jsonString);
        JSONObject check = JSONObject.parseObject(jsonString);
        System.out.println(check);
    }
}