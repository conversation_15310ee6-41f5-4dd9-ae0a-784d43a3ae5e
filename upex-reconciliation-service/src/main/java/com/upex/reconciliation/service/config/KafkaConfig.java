package com.upex.reconciliation.service.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
@Configuration
public class KafkaConfig implements DisposableBean {
    @Value("${upex.recon.kafka.namesrvAddr}")
    private String kafkaServerAddr;
    private KafkaProducer<String, String> kafkaProducer;

    @Bean
    public KafkaProducer<String, String> kafkaProducer() {
        Map<String, Object> config = new HashMap<String, Object>();
        config.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServerAddr);
        config.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        config.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        config.put(ProducerConfig.ACKS_CONFIG, "-1");
        config.put(ProducerConfig.RETRIES_CONFIG, "10");
        config.put(ProducerConfig.LINGER_MS_CONFIG, "5");
        kafkaProducer = new KafkaProducer<>(config);
        return kafkaProducer;
    }

    @Override
    public void destroy() throws Exception {
        log.info("KafkaConfig.destroy KafkaProducer ...");
        if (kafkaProducer != null) {
            try {
                kafkaProducer.close();
            } catch (Exception e) {
                log.error("KafkaConfig.destroy KafkaProducer error 关闭失败 ", e);
            }
        }
    }
}
