package com.upex.reconciliation.service.service.client.cex.client.binance;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.google.common.reflect.TypeToken;
import com.binance.connector.client.common.ApiClient;
import com.binance.connector.client.common.ApiException;
import com.binance.connector.client.common.ApiResponse;

import com.google.common.collect.Lists;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.model.config.ApolloThirdCexAssetConfig;
import com.upex.reconciliation.service.service.ThirdCexUserService;
import com.upex.reconciliation.service.service.client.cex.client.AbstractCexApiClient;
import com.upex.reconciliation.service.service.client.cex.config.binance.BinanceApiClientMapping;
import com.upex.reconciliation.service.service.client.cex.config.binance.BinancePathConfig;
import com.upex.reconciliation.service.service.client.cex.config.binance.SensitiveFieldFilter;
import com.upex.reconciliation.service.service.client.cex.convert.req.binance.BinanceConvertReqAdapter;
import com.upex.reconciliation.service.service.client.cex.convert.res.binance.BinanceConvertResAdapter;
import com.upex.reconciliation.service.service.client.cex.dto.req.binance.BinanceApiBaseReq;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CommonReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.binance.*;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonCoinAssetRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import com.upex.reconciliation.service.service.client.cex.enmus.CexTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum;
import com.upex.reconciliation.service.service.client.cex.sign.SignatureGen;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import static com.upex.reconciliation.service.service.client.cex.CexConstants.*;


@Service
@Slf4j
public class BinanceApiClient extends AbstractCexApiClient {

    @Resource
    private BinanceConvertReqAdapter binanceConvertReqAdapter;

    @Resource
    private BinanceConvertResAdapter binanceConvertResAdapter;

    @Resource
    SensitiveFieldFilter sensitiveFieldFilter;


    @Resource
    BinanceApiClientMapping binanceApiClientMapping;

    @Resource
    SignatureGen signatureGen;

    @Resource
    AlarmNotifyService alarmNotifyService;


    @Override
    public CommonRes querySpotCoinAsset(CommonReq commonReq) {
        return executeApi(
                commonReq,
                binanceConvertReqAdapter::defaultConvertReq,
                BinancePathConfig.querySpotAccountInfo,
                (data) -> binanceConvertResAdapter.convertSpotCoinAssetRes((SpotAccountInfoRes) data),
                GET,
                new TypeToken<SpotAccountInfoRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes<CommonCoinAssetRes> queryFundingCoinAsset(CommonReq commonReq) {

        return executeApi(
                commonReq,
                binanceConvertReqAdapter::defaultConvertReq,
                BinancePathConfig.queryFundingCoinAsset,
                (data) -> binanceConvertResAdapter.convertFundingCoinAssetRes((FundingAccountCoinAssetRes) data),
                POST,
                new TypeToken<FundingAccountCoinAssetRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryUContractCoinAsset(CommonReq commonReq) {
        return executeApi(
                commonReq,
                binanceConvertReqAdapter::defaultConvertReq,
                BinancePathConfig.queryUContractAccountInfoAccount,
                (data) -> binanceConvertResAdapter.convertUContractCoinAssetRes((UContractAccountInfoRes) data),
                GET,
                new TypeToken<UContractAccountInfoRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryCoinContractCoinAsset(CommonReq commonReq) {
        return executeApi(
                commonReq,
                binanceConvertReqAdapter::defaultConvertReq,
                BinancePathConfig.queryCoinContractAccountInfoAccount,
                (data) -> binanceConvertResAdapter.convertCoinContractCoinAssetRes((CoinContractAccountInfoRes) data),
                GET,
                new TypeToken<CoinContractAccountInfoRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryMarginCoinAsset(CommonReq commonReq) {
        return executeApi(
                commonReq,
                binanceConvertReqAdapter::defaultConvertReq,
                BinancePathConfig.queryMarginAccountInfo,
                (data) -> binanceConvertResAdapter.convertMarginCoinAssetRes((MarginAccountInfoRes) data),
                GET,
                new TypeToken<MarginAccountInfoRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryIsolatedMarginCoinAsset(CommonReq commonReq) {
        return executeApi(
                commonReq,
                binanceConvertReqAdapter::defaultConvertReq,
                BinancePathConfig.queryIsolatedMarginAccountInfo,
                (data) -> binanceConvertResAdapter.convertIsolatedMarginCoinAssetRes((IsolatedMarginAccountInfoRes) data),
                GET,
                new TypeToken<IsolatedMarginAccountInfoRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryFlexEarnPosition(CommonReq commonReq) {
        return executeApi(
                commonReq,
                binanceConvertReqAdapter::defaultConvertReq,
                BinancePathConfig.queryFlexEarnPosition,
                (data) -> binanceConvertResAdapter.convertFlexEarnPositionRes((SimpleEarnFlexPositionRes) data),
                GET,
                new TypeToken<SimpleEarnFlexPositionRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryLockedEarnPosition(CommonReq commonReq) {
        return executeApi(
                commonReq,
                binanceConvertReqAdapter::defaultConvertReq,
                BinancePathConfig.queryLockedEarnPosition,
                (data) -> binanceConvertResAdapter.convertLockedEarnPositionRes((SimpleEarnLockedPositionRes) data),
                GET,
                new TypeToken<SimpleEarnLockedPositionRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryApikeyPermission(CommonReq commonReq) {
        return executeApi(commonReq,
                binanceConvertReqAdapter::defaultConvertReq,
                BinancePathConfig.queryApikeyPermission,
                (data) -> binanceConvertResAdapter.convertApikeyPermissionRes((ApikeyPermissionRes) data),
                GET,
                new TypeToken<ApikeyPermissionRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryUserStatus(CommonReq commonReq) {
        return executeApi(
                commonReq,
                (req) -> binanceConvertReqAdapter.defaultConvertReq(req),
                BinancePathConfig.queryUserStatus,
                (data) -> binanceConvertResAdapter.convertUserStatusRes((UserAccountStatusRes) data),
                GET,
                new TypeToken<UserAccountStatusRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes querySubUserList(CommonReq commonReq) {
        return executeApi(
                commonReq,
                (req) -> binanceConvertReqAdapter.defaultConvertReq(req),
                BinancePathConfig.querySubUserList,
                (data) -> binanceConvertResAdapter.convertSubUserListRes((SubAccountListRes) data),
                GET,
                new TypeToken<SubAccountListRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes querySpotPosition(CommonReq commonReq) {
        return executeApi(
                commonReq,
                binanceConvertReqAdapter::defaultConvertReq,
                BinancePathConfig.querySpotPosition,
                (data) -> binanceConvertResAdapter.convertSpotPositionRes((SpotPositionRes) data),
                POST,
                new TypeToken<SpotPositionRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes querySubUserSpotAsset(CommonReq commonReq) {
        return executeApi(
                commonReq,
                binanceConvertReqAdapter::convertSubUserApiReq,
                BinancePathConfig.querySubUserSpotCoinAsset,
                (data) -> binanceConvertResAdapter.convertSubUserSpotAssetRes((SubUserSpotAssetRes) data),
                GET,
                new TypeToken<SubUserSpotAssetRes>(getClass()) {
                });
    }

    @Override
    public CommonRes querySubUserMarginAsset(CommonReq commonReq) {
        return executeApi(
                commonReq,
                binanceConvertReqAdapter::convertSubUserApiReq,
                BinancePathConfig.querySubUserMarginAccountInfo,
                (data) -> binanceConvertResAdapter.convertSubUserMarginAssetRes((SubUserMarginAccountInfoRes) data),
                GET,
                new TypeToken<SubUserMarginAccountInfoRes>(getClass()) {
                });
    }

    @Override
    public CommonRes querySubUserUContractAsset(CommonReq commonReq) {
        return executeApi(
                commonReq,
                (req) -> binanceConvertReqAdapter.convertSubUserUContractAssetReq(req),
                BinancePathConfig.querySubUserContractAccountInfo,
                (data) -> binanceConvertResAdapter.convertSubUserUContractAssetRes((SubUserContractAccountInfoRes) data),
                GET,
                new TypeToken<SubUserContractAccountInfoRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes querySubUserCoinContractAsset(CommonReq commonReq) {
        return executeApi(
                commonReq,
                (req) -> binanceConvertReqAdapter.convertSubUserCoinContractAssetReq(req),
                BinancePathConfig.querySubUserContractAccountInfo,
                (data) -> binanceConvertResAdapter.convertSubUserCoinContractAssetRes((SubUserContractAccountInfoRes) data),
                GET,
                new TypeToken<SubUserContractAccountInfoRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes querySupportCoinList(CommonReq commonReq) {
        return executeApi(
                commonReq,
                binanceConvertReqAdapter::defaultConvertReq,
                BinancePathConfig.querySupportCoinList,
                (data) -> binanceConvertResAdapter.convertSupportCoinListRes((SupportCoinListRes) data),
                GET,
                new TypeToken<SupportCoinListRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryDepositeAddress(CommonReq commonReq) {
        return executeApi(
                commonReq,
                (req) -> binanceConvertReqAdapter.convertDepositeAddressApiReq(req),
                BinancePathConfig.queryDepositeAddress,
                (data) -> binanceConvertResAdapter.convertDepositeAddress((DepositeAddressRes) data),
                GET,
                new TypeToken<DepositeAddressRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryUserDepositeHistory(CommonReq commonReq) {
        return executeApi(
                commonReq,
                (req) -> binanceConvertReqAdapter.convertDepositeHistoryApiReq(req),
                BinancePathConfig.queryUserDepositeHistory,
                (data) -> binanceConvertResAdapter.convertUserDepositeHistory((DepositeHistoryRes) data),
                GET,
                new TypeToken<DepositeHistoryRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryUserWithdrawHistory(CommonReq commonReq) {
        return executeApi(
                commonReq,
                (req) -> binanceConvertReqAdapter.convertWithdrawHistoryApiReq(req),
                BinancePathConfig.queryUserWithdrawHistory,
                (data) -> binanceConvertResAdapter.convertUserWithdrawHistory((WithdrawHistoryRes) data),
                GET,
                new TypeToken<WithdrawHistoryRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryPayTransferHistory(CommonReq commonReq) {
        return executeApi(
                commonReq,
                (req) -> binanceConvertReqAdapter.convertPayTransferHistoryApiReq(req),
                BinancePathConfig.queryPayTransferHistory,
                (data) -> binanceConvertResAdapter.convertPayTransferHistory((PayTransactionHistoryRes) data),
                GET,
                new TypeToken<PayTransactionHistoryRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryParentSubTransferRecord(CommonReq commonReq) {
        return executeApi(
                commonReq,
                (req) -> binanceConvertReqAdapter.convertParentSubTransferRecordApiReq(req),
                BinancePathConfig.queryParentSubTransferRecord,
                (data) -> binanceConvertResAdapter.convertParentSubTransferRecordRes((ParentSubTransferRes) data),
                GET,
                new TypeToken<ParentSubTransferRes>(getClass()) {
                }
        );
    }

    @Override
    public CommonRes queryUniversialTransferList(CommonReq commonReq) {
        return executeApi(
                commonReq,
                (req) -> binanceConvertReqAdapter.convertUniversialTransferListApiReq(req),
                BinancePathConfig.queryUniversialTransferList,
                (data) -> binanceConvertResAdapter.convertUniversialTransferListRes((UniversalTransferRecordRes) data),
                GET,
                new TypeToken<UniversalTransferRecordRes>(getClass()) {
                }
        );
    }

    protected <T, R> CommonRes<R> executeApi(
            CommonReq commonReq,
            Function<CommonReq, BinanceApiBaseReq> reqConverter,
            String apiPath,
            Function<T, CommonRes<R>> resConverter,
            String httpMethod,
            TypeToken<T> typeToken
    ) {
        try {
            // 1. 转换请求
            BinanceApiBaseReq convertedReq = reqConverter.apply(commonReq);

            // 2. 获取客户端
            ApiClient apiClient = binanceApiClientMapping.getApiClient(apiPath);

            // 3. 构建签名
            signatureGen.buildSignatureHeader(apiClient, commonReq.getApiKey(), commonReq.getPrivateKey(), httpMethod, apiPath, convertedReq);

            // 4. 构建调用
            Call call = buildCall(apiClient, convertedReq, apiPath, httpMethod);
            // 5. 执行请求
            ApiResponse<T> response = apiClient.execute(call, typeToken.getType());

            ApolloThirdCexAssetConfig apolloThirdCexAssetConfig = ReconciliationApolloConfigUtils.getThirdCexAssetConfig();
            if (apolloThirdCexAssetConfig.getApiLogConfigs() != null) {
                if (apolloThirdCexAssetConfig.getApiLogConfigs().containsKey(apiPath) && apolloThirdCexAssetConfig.getApiLogConfigs().get(apiPath)) {
                    log.info("BinanceApi params:{} req:{} res:{}", apiPath, JSONObject.toJSONString(convertedReq, sensitiveFieldFilter), JSONObject.toJSONString(response.getData(), sensitiveFieldFilter));
                }
            }
            CommonRes<R> apires = CommonRes.getSucApiBaseRes(this.cexType());
            // 6. 处理错误
            if (!SUC.equals(response.getStatusCode())) {
                apires.setCode(String.valueOf(response.getStatusCode()));
                apires.setMsg(JSONObject.toJSONString(response.getData()));
                alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_API_STATUS_ERROR, commonReq.getCexUserId(), CexTypeEnum.fromType(this.cexType()).getName(), response.getStatusCode(), apiPath, JSONObject.toJSONString(response.getData()));
                return apires;
            }
            return resConverter.apply(response.getData());
        } catch (com.binance.connector.client.common.ApiException e) {
            log.error("捕获到 Binance ApiException: {}", e);
            alarmNotifyService.cexApipathAlarm(AlarmTemplateEnum.THIRD_CEX_API_EXCEPTION, commonReq.getCexUserId(),CexTypeEnum.fromType(commonReq.getCexType()).getName(), apiPath,commonReq.getApiKey(), e.getMessage());
            return CommonRes.getFailApiBaseRes(ReconCexExceptionEnum.BINANCE_API_EXCEPTION);
        } catch (com.upex.commons.support.exception.ApiException e) {
            log.info("捕获到 upex-ApiException: {}", e);
            return CommonRes.getFailApiBaseRes(ReconCexExceptionEnum.getByCode(e.getCode()));
        } catch (Exception e) {
            log.error("未知异常: ", e);
            alarmNotifyService.alarm(AlarmTemplateEnum.THIRD_CEX_API_UNKONW_ERROR, commonReq.getCexUserId(), CexTypeEnum.fromType(commonReq.getCexType()).getName(), apiPath, e.getMessage());
            return CommonRes.getUnknowApiErrorBaseRes();
        }
    }

    private Call buildCall(ApiClient apiClient, BinanceApiBaseReq req, String path, String httpMethod) {
        return apiClient.buildCall(
                null,
                path,
                httpMethod,
                req.getQueryParams(),
                req.getCollectionQueryParams(),
                req.getBody(),
                req.getHeaderParams(),
                req.getCookieParams(),
                req.getFormParams(),
                req.getAuthNames()
        );
    }

    @Override
    public Integer cexType() {
        return CexTypeEnum.BINANCE.getType();
    }
}
