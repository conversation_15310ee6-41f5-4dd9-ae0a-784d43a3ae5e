package com.upex.reconciliation.service.business.impl;


import com.upex.reconciliation.facade.dto.results.ReconAccountAssetsInfoResult;
import com.upex.reconciliation.service.business.MixAccountAssetsCalExtensionBillService;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserAssets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class MixAccountAssetsCalExtensionBillServiceImpl implements MixAccountAssetsCalExtensionBillService {
    @Override
    public BillCoinTypeUserProperty calEndAccountAssets(BillCoinTypeUserProperty startAccountAssets, List<ReconAccountAssetsInfoResult> billList, String accountParam, BigDecimal useFixPrice) {
        return null;
    }

    @Override
    public ReconAccountAssetsInfoResult calAccountEquity(ReconAccountAssetsInfoResult accountAssets, String accountParam, BigDecimal useFixPrice) {
        return null;
    }

    @Override
    public ReconAccountAssetsInfoResult calAccountEquity(BillCoinUserAssets billCoinUserAssets, Byte accountType, String accountParam, Map<String, BigDecimal> markPriceMap, Map<Integer, BigDecimal> rateMap) {
        return null;
    }
}
