package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.constants.enums.ProfitTransferTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface OldBillContractProfitTransferService {
    
    List<BillContractProfitTransfer> selectDelayedTransactionByTimeAndType(Date checkOkTime, List<ProfitTransferTypeEnum> profitTransferTypeEnum, Date splitDate);

    List<BillContractProfitTransfer> selectDelayedTransactionByStatusAndTimeAndType(Date checkOkTime, List<ProfitTransferTypeEnum> profitTransferTypeEnum, Date splitDate);


    List<BillContractProfitTransfer> selectEachDelayedTransactionByTimeAndType(Date checkOkTime, List<ProfitTransferTypeEnum> profitTransferTypeEnumList, Date splitDate);

    List<BillContractProfitTransfer> selectEachDelayedTransactionByStatusAndTimeAndType(Date checkOkTime, List<ProfitTransferTypeEnum> profitTransferTypeEnumList, Date splitDate);

    List<BillContractProfitTransfer> selectListByAccountTypeAndCheckTime(Byte accountType, String accountParam, Date checkTime);

    /**
     * 查询待冻账初始值
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @return
     */
    Map<Integer, BigDecimal> sumInitUnProfitTransfers(Byte accountType, String accountParam, Date checkTime);
}
