package com.upex.reconciliation.service.utils;

import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillSymbolCoinUserProperty;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/27 11:49
 */
public class BillBizUtil {

    public static boolean ifContract(byte accountType) {
        return true;
    }

    public static String getUniqueId(Long offset, Long bizId) {
        return offset + "#" + bizId;
    }

    public static String getDataUniqueId(Byte accountType, CommonBillChangeData currentBill) {
        if (AccountTypeEnum.toEnum(accountType).isUserCoinPropSubType()) {
            return currentBill.getAccountId() + "#" + currentBill.getBizId() + "#" + currentBill.isIgnoreUserPropCheck();
        }
        return currentBill.getAccountId() + "#" + currentBill.getBizId();
    }

    public static List<String> getDataUniqueId(Byte accountType, BillCoinUserProperty coinUserProperty) {
        List<String> ids = new ArrayList<>();
        if (AccountTypeEnum.toEnum(accountType).isUserCoinPropSubType()) {
            Map<String, BillSymbolCoinUserProperty> symbolProperty = coinUserProperty.getSymbolProperty();
            if (symbolProperty != null && symbolProperty.size() > 0) {
                for (Map.Entry<String, BillSymbolCoinUserProperty> entry : symbolProperty.entrySet()) {
                    Long userId = entry.getValue().getUserId();
                    Long lastBizId = entry.getValue().getLastBizId();
                    String symbolId = entry.getValue().getSymbolId();
                    if (lastBizId != null) {
                        ids.add(userId + "#" + lastBizId + "#" + true);
                        ids.add(userId + "#" + lastBizId + "#" + false);
                    }
                }
            }
        } else {
            if (coinUserProperty.getLastBizId() != null) {
                ids.add(coinUserProperty.getUserId() + "#" + coinUserProperty.getLastBizId());
            }
        }
        return ids;
    }

    public static String getAccountUniqueId(Long offset, Long bizId, Long accountId) {
        return accountId + "#" + offset + "#" + bizId;
    }

    public static Long parseAccountId(String accountUniqueId) {
        String[] strs = accountUniqueId.split("#");
        return Long.valueOf(strs[0]);
    }


    public static String getAccountUniqueId(Long accountId, String uniqueId) {
        return accountId + "#" + uniqueId;
    }

    public static Long getNewBizId(Long bizId) {
        return bizId + *********;
    }

    public static Long getNewBizIdV2(Long bizId) {
        return bizId + *********;
    }

    /**
     * 有效jsonstr
     *
     * @param jsonStr
     * @return
     */
    public static boolean isNotEmptyJsonStr(String jsonStr) {
        return StringUtils.isNotBlank(jsonStr) && !BillConstants.BRACKETS.equals(jsonStr) && !BillConstants.EMPTY_JSON.equals(jsonStr);
    }
}
