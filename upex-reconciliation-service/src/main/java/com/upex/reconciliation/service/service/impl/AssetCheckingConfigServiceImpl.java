package com.upex.reconciliation.service.service.impl;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.service.AssetCheckingConfigService;
import com.upex.reconciliation.service.dao.entity.AssetCheckingConfig;
import com.upex.reconciliation.service.dao.mapper.AssetCheckingConfigMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 资金对账配置记录表
 * <AUTHOR>
 */
@Service
public class AssetCheckingConfigServiceImpl implements AssetCheckingConfigService {
    @Resource
    private BillDbHelper dbHelper;


    @Resource
    private AssetCheckingConfigMapper assetCheckingConfigMapper;
    @Override
    public AssetCheckingConfig getLastAssetCheckingConfig(Integer businessType) {
        return dbHelper.doDbOpInSnapshotGlobalMaster(() -> assetCheckingConfigMapper.getLastAssetCheckingConfig(businessType));
    }

    @Override
    public AssetCheckingConfig getLastAssetCheckingConfigByStatus(Integer businessType, Integer billCapitalStatus) {
        return dbHelper.doDbOpInSnapshotGlobalMaster(() -> assetCheckingConfigMapper.getLastAssetCheckingConfigByStatus(businessType,billCapitalStatus));
    }

}
