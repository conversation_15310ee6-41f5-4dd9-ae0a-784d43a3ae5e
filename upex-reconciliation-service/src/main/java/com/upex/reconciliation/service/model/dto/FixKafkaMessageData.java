package com.upex.reconciliation.service.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FixKafkaMessageData {
    private Long bizId;
    private BigDecimal changeProp1;
    private BigDecimal prop1;
    private BigDecimal changeProp2;
    private BigDecimal prop2;
    private BigDecimal changeProp3;
    private BigDecimal prop3;
    private BigDecimal changeProp4;
    private BigDecimal prop4;
    private BigDecimal changeProp5;
    private BigDecimal prop5;
    private Date bizTime;
    private Date createTime;
    private String bizType;
}
