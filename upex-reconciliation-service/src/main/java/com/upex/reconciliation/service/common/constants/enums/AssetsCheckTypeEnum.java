package com.upex.reconciliation.service.common.constants.enums;


import com.upex.utils.annotation.EnumExtend;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@EnumExtend(dbs = {
        @EnumExtend.Db(dbName = "upex_reconciliation", tableName = "assets_bill_config", fieldName = "assets_check_type")
})
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AssetsCheckTypeEnum {
    @EnumExtend.Code(code = "internal", desc = "内部总资产")
    INTERNAL_TOTAL_ASSETS("internal", "default", "内部总资产"),
    @EnumExtend.Code(code = "leverspot", desc = "杠杠现货总资产")
    LEVER_SPOT_TOTAL_ASSETS("leverspot", "default", "杠杠现货总资产"),
    ;


    private String code;
    /***业务类型参数***/
    private String assetsParam = "default";
    private String desc;

    public static AssetsCheckTypeEnum toEnum(String code) {
        for (AssetsCheckTypeEnum item : AssetsCheckTypeEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 是否是总账
     *
     * @return
     */
    public static boolean isInternalTotalAssets(String code) {
        if (StringUtils.isBlank(code)) {
            return false;
        }
        return code.equals(INTERNAL_TOTAL_ASSETS.getCode());
    }

}
