package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillAlarmLog;
import com.upex.reconciliation.service.dao.mapper.BillAlarmLogMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/11
 */
@Service
public class BillAlarmLogService {

    @Resource
    private BillDbHelper dbHelper;

    @Resource
    private BillAlarmLogMapper billAlarmLogMapper;

    public int insert(BillAlarmLog billAlarmLog) {
        return dbHelper.doDbOpInReconMaster(() -> billAlarmLogMapper.insert(billAlarmLog));
    }

    public int batchInsert(List<BillAlarmLog> records) {
        return dbHelper.doDbOpInReconMaster(() -> billAlarmLogMapper.batchInsert(records));
    }

    public int deleteByTime(Date time) {
        return dbHelper.doDbOpInReconMaster(() -> billAlarmLogMapper.deleteByTime(time));
    }
}
