package com.upex.reconciliation.service.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fiat.fund.facade.client.bill.FundAccountBillQueryFacade;
import com.google.common.collect.Lists;
import com.upex.bill.dto.params.BaseRequest;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.bill.dto.results.PageResponse;
import com.upex.bill.dto.results.UserInfoResult;
import com.upex.bill.facade.AccountAssetsService;
import com.upex.bill.facade.AccountAssetsServiceWithBizId;
import com.upex.financial.facade.bill.FinancialAssetsInnerFeign;
import com.upex.margin.facade.inner.*;
import com.upex.mixcontract.process.facade.dto.MixAccountAssetsExtension;
import com.upex.mixcontract.process.facade.feign.inner.InnerBillFeignClient;
import com.upex.mixcontract.process.facade.feign.inner.InnerQueryPriceFeignClient;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.impl.*;
import com.upex.reconciliation.service.common.constants.enums.QueryUserAssetsSceneEnum;
import com.upex.reconciliation.service.consumer.decoder.*;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserPropertyError;
import com.upex.reconciliation.service.dao.entity.BillSymbolCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillUserPosition;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.service.BillCoinUserPropertyErrorService;
import com.upex.reconciliation.service.service.BillCoinUserPropertyService;
import com.upex.reconciliation.service.service.BillConfigService;
import com.upex.reconciliation.service.service.impl.OtcBillCheckServiceImpl;
import com.upex.reconciliation.service.utils.BillBizUtil;
import com.upex.reconciliation.service.utils.GroupByKeyUtils;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.spot.facade.bill.SpotBillsService;
import com.upex.spot.facade.query.SpotDemoBillsQueryClient;
import com.upex.spot.facade.query.SpotNewBillsQueryClient;
import com.upex.unified.account.facade.feign.UnifiedInnerBill2FeignClient;
import com.upex.unified.account.facade.feign.UnifiedInnerBillFeignClient;
import com.upex.user.facade.user.UserQueryFeignClient;
import com.upex.user.facade.vo.user.UserBaseInfoDTO;
import com.upex.utils.task.TaskManager;
import com.upex.utils.task.TaskVoidBatchResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AccountAssetsServiceFactory {
    @Resource
    private SpotBillsService spotBillsService;
    @Resource
    private InnerBillFeignClient innerBillFeignClient;
    @Resource
    private FundAccountBillQueryFacade otcBillsService;
    @Resource
    private InnerQueryPriceFeignClient innerQueryPriceFeignClient;
    @Resource
    private InnerMarginCrossAssetsCheckFeignClient innerMarginCrossAssetsCheckFeignClient;
    @Resource
    private InnerMarginIsolatedAssetsCheckFeignClient innerMarginIsolatedAssetsCheckFeignClient;
    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;
    @Resource
    private InnerNewMarginIsolatedAssetsCheckFeignClient innerNewMarginIsolatedAssetsCheckFeignClient;
    @Resource
    private SpotNewBillsQueryClient spotNewBillsQueryClient;
    @Resource
    private BillCoinUserPropertyErrorService billCoinUserPropertyErrorService;
    @Resource
    private FinancialAssetsInnerFeign financialAssetsInnerFeign;
    @Resource
    private SpotDemoBillsQueryClient spotDemoBillsQueryClient;
    @Resource
    private InnerMarginDemoCrossAssetsCheckFeignClient demoCrossAssetsCheckFeignClient;
    @Resource
    private InnerMarginDemoIsolatedAssetsCheckFeignClient demoIsolatedAssetsCheckFeignClient;
    @Resource
    private UnifiedInnerBillFeignClient unifiedInnerBillFeignClient;
    @Resource
    private UnifiedInnerBill2FeignClient unifiedInnerBill2FeignClient;
    private Map<Byte, AccountAssetsService> accountAssetsServiceMap = new ConcurrentHashMap<>();
    private Map<Byte, AccountAssetsServiceWithBizId> accountAssetsServiceWithBizIdMap = new ConcurrentHashMap<>();
    @Resource
    private OtcBillCheckServiceImpl otcBillCheckServiceImpl;
    @Resource
    private LeverOneBillCheckServiceImpl leverOneBillCheckService;
    @Resource
    private LeverFullBillCheckServiceImpl leverFullBillCheckService;
    @Resource
    private InnerNewMarginDemoIsolatedAssetsCheckFeignClient innerNewMarginDemoIsolatedAssetsCheckFeignClient;
    @Resource
    private SpotBillCheckServiceImpl spotBillCheckServiceImpl;
    @Resource
    private ContractBillCheckServiceImpl contractBillCheckServiceImpl;
    @Resource
    private FinancialBillCheckServiceImpl financialBillCheckServiceImpl;
    @Resource
    private UtaBillCheckServiceImpl utaBillCheckServiceImpl;
    private Map<Byte, BillCheckService> billCheckServiceMap = new ConcurrentHashMap<>();
    @Resource
    private ContractDecoder contractDecoder;
    @Resource
    private BillConfigService billConfigService;
    @Resource
    private SpotDecoder spotDecoder;
    @Resource
    private LeverDecoder leverDecoder;
    @Resource
    private OtcDecoder otcDecoder;
    @Resource
    private FinancialDecoder financialDecoder;
    @Resource
    private UtaDecoder utaDecoder;
    private Map<Byte, MessageDecoder> messageDecoderMap = new ConcurrentHashMap<>();
    @Resource
    private LeverTransferServiceImpl leverTransferService;
    @Resource
    private McContractTransferServiceImpl mcContractTransferServiceImpl;
    @Resource
    private UtaTransferServiceImpl utaTransferService;
    @Resource
    private SpotTransferServiceImpl spotTransferServiceImpl;
    private Map<Byte, TransferService> transferServiceMap = new ConcurrentHashMap<>();
    @Resource(name = "taskManager")
    private TaskManager taskManager;
    @Resource
    private UserQueryFeignClient userQueryFeignClient;

    @Resource
    private OnchainBillCheckServiceImpl onchainBillCheckService;
    @Resource
    private OnchainDecoder onchainDecoder;

    @PostConstruct
    private void init() {
        this.initAccountAssetsServiceFeignClient();
        this.initBillCheckService();
        this.initMessageDecoder();
        this.initTransferService();
    }

    /**
     * 初始化三方用户和资产接口
     */
    private void initAccountAssetsServiceFeignClient() {
        accountAssetsServiceMap.put(AccountTypeEnum.SPOT.getCode(), spotBillsService);
        accountAssetsServiceMap.put(AccountTypeEnum.OTC.getCode(), otcBillsService);
        accountAssetsServiceMap.put(AccountTypeEnum.LEVER_FULL.getCode(), innerMarginCrossAssetsCheckFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.LEVER_ONE.getCode(), innerMarginIsolatedAssetsCheckFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.USDT_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.USD_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.USDC_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.FINANCIAL.getCode(), financialAssetsInnerFeign);
        accountAssetsServiceMap.put(AccountTypeEnum.UNIFIED.getCode(), unifiedInnerBillFeignClient);

        accountAssetsServiceMap.put(AccountTypeEnum.S_USDC_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.S_USDT_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.S_USD_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);

        accountAssetsServiceMap.put(AccountTypeEnum.DEMO_SPOT.getCode(), spotDemoBillsQueryClient);
        accountAssetsServiceMap.put(AccountTypeEnum.DEMO_LEVER_FULL.getCode(), demoCrossAssetsCheckFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.DEMO_LEVER_ONE.getCode(), demoIsolatedAssetsCheckFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.DEMO_S_USDT_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.DEMO_S_USDC_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.DEMO_S_USD_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.DEMO_USDT_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.DEMO_USDC_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.DEMO_USD_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);

        accountAssetsServiceMap.put(AccountTypeEnum.P_SPOT.getCode(), spotBillsService);
        accountAssetsServiceMap.put(AccountTypeEnum.P_LEVER_FULL.getCode(), innerMarginCrossAssetsCheckFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.P_LEVER_ONE.getCode(), innerMarginIsolatedAssetsCheckFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.P_USDT_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.P_USD_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.P_USDC_MIX_CONTRACT_BL.getCode(), innerBillFeignClient);
        accountAssetsServiceMap.put(AccountTypeEnum.P_UNIFIED.getCode(), unifiedInnerBillFeignClient);

        // 业务线返回lastBillId
        accountAssetsServiceWithBizIdMap.put(AccountTypeEnum.SPOT.getCode(), spotNewBillsQueryClient);
        accountAssetsServiceWithBizIdMap.put(AccountTypeEnum.LEVER_ONE.getCode(), innerNewMarginIsolatedAssetsCheckFeignClient);
        accountAssetsServiceWithBizIdMap.put(AccountTypeEnum.UNIFIED.getCode(), unifiedInnerBill2FeignClient);

        accountAssetsServiceWithBizIdMap.put(AccountTypeEnum.DEMO_LEVER_ONE.getCode(), innerNewMarginDemoIsolatedAssetsCheckFeignClient);

        accountAssetsServiceWithBizIdMap.put(AccountTypeEnum.P_SPOT.getCode(), spotNewBillsQueryClient);
        accountAssetsServiceWithBizIdMap.put(AccountTypeEnum.P_LEVER_ONE.getCode(), innerNewMarginIsolatedAssetsCheckFeignClient);
        accountAssetsServiceWithBizIdMap.put(AccountTypeEnum.P_UNIFIED.getCode(), unifiedInnerBill2FeignClient);

    }

    /**
     * 初始化业务线数据处理接口
     */
    private void initBillCheckService() {
        billCheckServiceMap.put(AccountTypeEnum.OTC.getCode(), otcBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.ONCHAIN.getCode(), onchainBillCheckService);
        billCheckServiceMap.put(AccountTypeEnum.LEVER_FULL.getCode(), leverFullBillCheckService);
        billCheckServiceMap.put(AccountTypeEnum.LEVER_ONE.getCode(), leverOneBillCheckService);
        billCheckServiceMap.put(AccountTypeEnum.SPOT.getCode(), spotBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.USDT_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.USD_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.USDC_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.FINANCIAL.getCode(), financialBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.UNIFIED.getCode(), utaBillCheckServiceImpl);

        billCheckServiceMap.put(AccountTypeEnum.S_USDT_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.S_USD_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.S_USDC_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);

        billCheckServiceMap.put(AccountTypeEnum.DEMO_SPOT.getCode(), spotBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.DEMO_LEVER_FULL.getCode(), leverFullBillCheckService);
        billCheckServiceMap.put(AccountTypeEnum.DEMO_LEVER_ONE.getCode(), leverOneBillCheckService);
        billCheckServiceMap.put(AccountTypeEnum.DEMO_S_USDT_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.DEMO_S_USDC_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.DEMO_S_USD_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.DEMO_USDT_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.DEMO_USDC_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.DEMO_USD_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);

        billCheckServiceMap.put(AccountTypeEnum.P_SPOT.getCode(), spotBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.P_LEVER_FULL.getCode(), leverFullBillCheckService);
        billCheckServiceMap.put(AccountTypeEnum.P_LEVER_ONE.getCode(), leverOneBillCheckService);
        billCheckServiceMap.put(AccountTypeEnum.P_USDT_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.P_USD_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.P_USDC_MIX_CONTRACT_BL.getCode(), contractBillCheckServiceImpl);
        billCheckServiceMap.put(AccountTypeEnum.P_UNIFIED.getCode(), utaBillCheckServiceImpl);

    }

    private void initMessageDecoder() {
        messageDecoderMap.put(AccountTypeEnum.SPOT.getCode(), spotDecoder);
        messageDecoderMap.put(AccountTypeEnum.USDT_MIX_CONTRACT_BL.getCode(), contractDecoder);
        messageDecoderMap.put(AccountTypeEnum.USD_MIX_CONTRACT_BL.getCode(), contractDecoder);
        messageDecoderMap.put(AccountTypeEnum.USDC_MIX_CONTRACT_BL.getCode(), contractDecoder);
        messageDecoderMap.put(AccountTypeEnum.LEVER_FULL.getCode(), leverDecoder);
        messageDecoderMap.put(AccountTypeEnum.LEVER_ONE.getCode(), leverDecoder);
        messageDecoderMap.put(AccountTypeEnum.OTC.getCode(), otcDecoder);
        messageDecoderMap.put(AccountTypeEnum.FINANCIAL.getCode(), financialDecoder);
        messageDecoderMap.put(AccountTypeEnum.UNIFIED.getCode(), utaDecoder);

        messageDecoderMap.put(AccountTypeEnum.S_USDT_MIX_CONTRACT_BL.getCode(), contractDecoder);
        messageDecoderMap.put(AccountTypeEnum.S_USD_MIX_CONTRACT_BL.getCode(), contractDecoder);
        messageDecoderMap.put(AccountTypeEnum.S_USDC_MIX_CONTRACT_BL.getCode(), contractDecoder);

        messageDecoderMap.put(AccountTypeEnum.DEMO_SPOT.getCode(), spotDecoder);
        messageDecoderMap.put(AccountTypeEnum.DEMO_LEVER_FULL.getCode(), leverDecoder);
        messageDecoderMap.put(AccountTypeEnum.DEMO_LEVER_ONE.getCode(), leverDecoder);
        messageDecoderMap.put(AccountTypeEnum.DEMO_S_USDT_MIX_CONTRACT_BL.getCode(), contractDecoder);
        messageDecoderMap.put(AccountTypeEnum.DEMO_S_USDC_MIX_CONTRACT_BL.getCode(), contractDecoder);
        messageDecoderMap.put(AccountTypeEnum.DEMO_S_USD_MIX_CONTRACT_BL.getCode(), contractDecoder);
        messageDecoderMap.put(AccountTypeEnum.DEMO_USDT_MIX_CONTRACT_BL.getCode(), contractDecoder);
        messageDecoderMap.put(AccountTypeEnum.DEMO_USDC_MIX_CONTRACT_BL.getCode(), contractDecoder);
        messageDecoderMap.put(AccountTypeEnum.DEMO_USD_MIX_CONTRACT_BL.getCode(), contractDecoder);

        messageDecoderMap.put(AccountTypeEnum.P_SPOT.getCode(), spotDecoder);
        messageDecoderMap.put(AccountTypeEnum.P_LEVER_FULL.getCode(), leverDecoder);
        messageDecoderMap.put(AccountTypeEnum.P_LEVER_ONE.getCode(), leverDecoder);
        messageDecoderMap.put(AccountTypeEnum.P_USDT_MIX_CONTRACT_BL.getCode(), contractDecoder);
        messageDecoderMap.put(AccountTypeEnum.P_USD_MIX_CONTRACT_BL.getCode(), contractDecoder);
        messageDecoderMap.put(AccountTypeEnum.P_USDC_MIX_CONTRACT_BL.getCode(), contractDecoder);
        messageDecoderMap.put(AccountTypeEnum.P_UNIFIED.getCode(), utaDecoder);
        messageDecoderMap.put(AccountTypeEnum.ONCHAIN.getCode(), onchainDecoder);
    }

    private void initTransferService() {
        transferServiceMap.put(AccountTypeEnum.SPOT.getCode(), spotTransferServiceImpl);
        transferServiceMap.put(AccountTypeEnum.LEVER_ONE.getCode(), leverTransferService);
        transferServiceMap.put(AccountTypeEnum.LEVER_FULL.getCode(), leverTransferService);
        transferServiceMap.put(AccountTypeEnum.USDT_MIX_CONTRACT_BL.getCode(), mcContractTransferServiceImpl);
        transferServiceMap.put(AccountTypeEnum.USD_MIX_CONTRACT_BL.getCode(), mcContractTransferServiceImpl);
        transferServiceMap.put(AccountTypeEnum.USDC_MIX_CONTRACT_BL.getCode(), mcContractTransferServiceImpl);
        transferServiceMap.put(AccountTypeEnum.UNIFIED.getCode(), utaTransferService);

        transferServiceMap.put(AccountTypeEnum.S_USDT_MIX_CONTRACT_BL.getCode(), mcContractTransferServiceImpl);
        transferServiceMap.put(AccountTypeEnum.S_USD_MIX_CONTRACT_BL.getCode(), mcContractTransferServiceImpl);
        transferServiceMap.put(AccountTypeEnum.S_USDC_MIX_CONTRACT_BL.getCode(), mcContractTransferServiceImpl);

        transferServiceMap.put(AccountTypeEnum.P_SPOT.getCode(), spotTransferServiceImpl);
        transferServiceMap.put(AccountTypeEnum.P_LEVER_ONE.getCode(), leverTransferService);
        transferServiceMap.put(AccountTypeEnum.P_LEVER_FULL.getCode(), leverTransferService);
        transferServiceMap.put(AccountTypeEnum.P_USDT_MIX_CONTRACT_BL.getCode(), mcContractTransferServiceImpl);
        transferServiceMap.put(AccountTypeEnum.P_USD_MIX_CONTRACT_BL.getCode(), mcContractTransferServiceImpl);
        transferServiceMap.put(AccountTypeEnum.P_USDC_MIX_CONTRACT_BL.getCode(), mcContractTransferServiceImpl);
        transferServiceMap.put(AccountTypeEnum.P_UNIFIED.getCode(), utaTransferService);
    }

    public MessageDecoder getMessageDecoder(Byte accountType) {
        return messageDecoderMap.get(accountType);
    }

    public BillCheckService getBillCheckService(Byte accountType) {
        return billCheckServiceMap.get(accountType);
    }

    public TransferService getTransferService(Byte accountType) {
        return transferServiceMap.get(accountType);
    }

    public MixAccountAssetsExtension queryPriceByTime(Integer accountType, Long time) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType.byteValue());
        if (accountTypeEnum.isUta()) {
            MixAccountAssetsExtension usdt = innerQueryPriceFeignClient.queryPriceByTime((int) AccountTypeEnum.USDT_MIX_CONTRACT_BL.getCode(), time);
            MixAccountAssetsExtension usd = innerQueryPriceFeignClient.queryPriceByTime((int) AccountTypeEnum.USD_MIX_CONTRACT_BL.getCode(), time);
            MixAccountAssetsExtension usdc = innerQueryPriceFeignClient.queryPriceByTime((int) AccountTypeEnum.USDC_MIX_CONTRACT_BL.getCode(), time);
            usdt.getMPriceMap().putAll(usd.getMPriceMap());
            usdt.getMPriceMap().putAll(usdc.getMPriceMap());
            usdt.getSPriceMap().putAll(usd.getSPriceMap());
            usdt.getSPriceMap().putAll(usdc.getSPriceMap());
            return usdt;
        } else {
            return innerQueryPriceFeignClient.queryPriceByTime(accountType, time);
        }
    }

    /**
     * 查询用户资产信息
     *
     * @param accountType
     * @param userId
     * @param initCheckOkTime
     * @param queryUserAssetsSceneEnum
     * @return
     */
    public List<BillCoinUserProperty> getBillCoinUserPropertyList(Byte accountType, Long userId, Long initCheckOkTime, QueryUserAssetsSceneEnum queryUserAssetsSceneEnum) {
        // 首先查询 本地库
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        List<BillCoinUserProperty> billCoinUserPropertyList = billCoinUserPropertyService.selectUserLatestAllRecord(
                (int) accountTypeEnum.getCode(), "default", userId);
        if (CollectionUtils.isEmpty(billCoinUserPropertyList)) {
            BaseRequest baseRequest = new BaseRequest();
            Long bizTime = initCheckOkTime;
            baseRequest.setBeginTime(bizTime);
            baseRequest.setEndTime(bizTime);
            baseRequest.setAccountType(accountTypeEnum.getCode());
            baseRequest.setAccountParam(accountTypeEnum.getAccountParam());
            baseRequest.setMaxId(1L);
            List<Long> accountIds = new ArrayList<>();
            accountIds.add(userId);
            if (ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType).isAsyncLoadUserQueryUserAssets()) {
                billCoinUserPropertyList = queryUserAssets(accountTypeEnum.getCode(), accountIds, baseRequest, queryUserAssetsSceneEnum);
            }
        }

        // 兼容资产变动的id顺序和业务时间顺序不一致并且跨时间片的问题
        List<BillCoinUserProperty> newBillCoinUserPropertyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(billCoinUserPropertyList)) {
            for (BillCoinUserProperty billCoinUserProperty : billCoinUserPropertyList) {
                // params 参数转换
                billCoinUserProperty.reloadDataParamsConvert(accountTypeEnum);
                if (billCoinUserProperty.getLastBizId() != null && billCoinUserProperty.getLastBizId() > 0) {
                    BillCoinUserPropertyError billCoinUserPropertyError = billCoinUserPropertyErrorService.selectByUserCoinAndBizId(
                            billCoinUserProperty.getUserId(), billCoinUserProperty.getCoinId(), billCoinUserProperty.getLastBizId(), accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
                    if (billCoinUserPropertyError != null) {
                        newBillCoinUserPropertyList.add(JSON.parseObject(billCoinUserPropertyError.getParams(), BillCoinUserProperty.class));
                        continue;
                    }
                }
                newBillCoinUserPropertyList.add(billCoinUserProperty);
            }
        }
        return newBillCoinUserPropertyList;
    }

    /**
     * 查询用户资产信息
     *
     * @param accountType
     * @param userIds
     * @param baseRequest
     * @param queryUserAssetsSceneEnum
     * @return
     */
    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 2000, multiplier = 2))
    public List<BillCoinUserProperty> queryUserAssets(Byte accountType, List<Long> userIds, BaseRequest baseRequest, QueryUserAssetsSceneEnum queryUserAssetsSceneEnum) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        List<BillCoinUserProperty> billCoinUserPropertyList = new ArrayList<>();
        List<? extends AccountAssetsInfoResult> list = null;
        try {
            list = accountAssetsServiceWithBizIdMap.containsKey(accountType) ? accountAssetsServiceWithBizIdMap.get(accountType).queryUserAssets(userIds, baseRequest)
                    : accountAssetsServiceMap.get(accountType).queryUserAssets(userIds, baseRequest);
        } catch (Exception e) {
            log.error("AccountAssetsServiceFactory.queryUserAssets error accountType={} userIds={}   baseRequest={} error={}", accountType, JSON.toJSONString(userIds), JSON.toJSONString(baseRequest), e.getMessage());
            throw e;
        }
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        log.info("AccountAssetsServiceFactory.queryUserAssets accountType {} , userIds {} , baseRequest={} , query result is {}", accountType, userIds, JSON.toJSONString(baseRequest), JSONObject.toJSONString(list));
        if (CollectionUtils.isEmpty(list)) {
            return billCoinUserPropertyList;
        }
        List<BillSymbolCoinUserProperty> billSymbolCoinUserPropertyList = new ArrayList<>();
        for (AccountAssetsInfoResult assetsInfo : list) {
            if (assetsInfo.getCoinId() == null) {
                log.info("queryUserAssets query result coin id is null {}", JSON.toJSONString(assetsInfo));
                continue;
            }
            BillSymbolCoinUserProperty billSymbolCoinUserProperty = BillSymbolCoinUserProperty.generateFromAsset(assetsInfo, baseRequest.getEndTime());
            // 如果资产为空 并且返回lastBillId 过滤
            if (queryUserAssetsSceneEnum != null && queryUserAssetsSceneEnum.equals(QueryUserAssetsSceneEnum.RELOAD)
                    && billSymbolCoinUserProperty.isPropZero() && billSymbolCoinUserProperty.getLastBizId() != null && billSymbolCoinUserProperty.getLastBizId() > 0) {
                continue;
            }
            billSymbolCoinUserPropertyList.add(billSymbolCoinUserProperty);
        }
        Map<String, List<BillSymbolCoinUserProperty>> billUserCoinMap = billSymbolCoinUserPropertyList.stream()
                .collect(Collectors.groupingBy(billSymbolCoinUserProperty ->
                        GroupByKeyUtils.getKey(GroupByKeyUtils.USERID_COINID, billSymbolCoinUserProperty.getUserId(), billSymbolCoinUserProperty.getCoinId(), null)));
        billUserCoinMap.forEach((key, value) -> {
            BillCoinUserProperty billCoinUserProperty = new BillCoinUserProperty();
            String[] strs = StringUtils.split(key, "#");
            billCoinUserProperty.setUserId(Long.valueOf(strs[0]));
            billCoinUserProperty.setCoinId(Integer.valueOf(strs[1]));
            billCoinUserProperty.setCheckTime(value.get(0).getCheckTime());
            billCoinUserProperty.setInitialTime(value.get(0).getInitialTime());
            billCoinUserProperty.setCreateTime(value.get(0).getCreateTime());
            billCoinUserProperty.setUpdateTime(value.get(0).getUpdateTime());
            billCoinUserProperty.setLastBizId(value.get(0).getLastBizId());
            billCoinUserProperty.setFristCreateTime(value.get(0).getCheckTime());
            billCoinUserProperty.setParams(value.get(0).getParams());
            for (BillSymbolCoinUserProperty billSymbolCoinUserProperty : value) {
                billCoinUserProperty.setProp1(billCoinUserProperty.getProp1().add(billSymbolCoinUserProperty.getProp1()));
                billCoinUserProperty.setProp2(billCoinUserProperty.getProp2().add(billSymbolCoinUserProperty.getProp2()));
                billCoinUserProperty.setProp3(billCoinUserProperty.getProp3().add(billSymbolCoinUserProperty.getProp3()));
                billCoinUserProperty.setProp4(billCoinUserProperty.getProp4().add(billSymbolCoinUserProperty.getProp4()));
                billCoinUserProperty.setProp5(billCoinUserProperty.getProp5().add(billSymbolCoinUserProperty.getProp5()));
            }
            billCoinUserProperty.setSprop1(billCoinUserProperty.getProp1());
            billCoinUserProperty.setSprop2(billCoinUserProperty.getProp2());
            billCoinUserProperty.setSprop3(billCoinUserProperty.getProp3());
            billCoinUserProperty.setSprop4(billCoinUserProperty.getProp4());
            billCoinUserProperty.setSprop5(billCoinUserProperty.getProp5());
            // params 参数处理
            billCoinUserProperty.initDataParamsConvert(accountTypeEnum, value);
            billCoinUserPropertyList.add(billCoinUserProperty);
        });
        return billCoinUserPropertyList;
    }

    /**
     * 查询用户持仓信息
     *
     * @param accountType
     * @param checkTime
     * @param userIds
     * @return
     */
    public Collection<BillUserPosition> queryUserPosition(Byte accountType, Date checkTime, List<Long> userIds) {
        Integer concurrent = 10;
        Integer partitionSize = 10;
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        Long bizTime = checkTime.getTime();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setBeginTime(bizTime - 1L);
        baseRequest.setEndTime(bizTime);
        baseRequest.setAccountType(accountType);
        baseRequest.setAccountParam(accountTypeEnum.getAccountParam());
        baseRequest.setMaxId(1L);

        List<List<Long>> partionUserIdLists = Lists.partition(new ArrayList<>(userIds), partitionSize);
        Queue<BillUserPosition> billUserPositions = new ConcurrentLinkedQueue<>();
        TaskVoidBatchResult queryResultIsEmpty = taskManager.forEachSubmitBatchAndWait(partionUserIdLists, (List<Long> uidList) -> {
            log.info("queryUserAssets query with uids {}", JSONObject.toJSONString(uidList));
            List<BillCoinUserProperty> billCoinUserPropertyList = queryUserAssets(accountType, uidList, baseRequest, QueryUserAssetsSceneEnum.INIT);
            for (BillCoinUserProperty billCoinUserProperty : billCoinUserPropertyList) {
                if (billCoinUserProperty.getCoinId() == null) {
                    log.info("queryUserAssets query result coin id is null {}", JSON.toJSONString(billCoinUserProperty));
                    continue;
                }
                // 仓位相关的信息在扩展字段 params里
                if ((accountTypeEnum.haveUserPosition()) && BillBizUtil.isNotEmptyJsonStr(billCoinUserProperty.getParams())) {
                    JSONObject paramsJson = JSON.parseObject(billCoinUserProperty.getParams());
                    if (BillBizUtil.isNotEmptyJsonStr(paramsJson.getString("positionList"))) {
                        List<MixAccountAssetsExtension> extensions = JSON.parseArray(paramsJson.getString("positionList"), MixAccountAssetsExtension.class);
                        if (CollectionUtils.isNotEmpty(extensions)) {
                            for (MixAccountAssetsExtension extension : extensions) {
                                BillUserPosition billUserPosition = new BillUserPosition();
                                BeanUtils.copyProperties(extension, billUserPosition);
                                billUserPosition.setSAvg(billUserPosition.getSAvg() != null ? billUserPosition.getSAvg() : BigDecimal.ZERO);
                                billUserPosition.setLAvg(billUserPosition.getLAvg() != null ? billUserPosition.getLAvg() : BigDecimal.ZERO);
                                billUserPosition.setSCount(billUserPosition.getSCount() != null ? billUserPosition.getSCount() : BigDecimal.ZERO);
                                billUserPosition.setLCount(billUserPosition.getLCount() != null ? billUserPosition.getLCount() : BigDecimal.ZERO);
                                billUserPosition.setUserId(billCoinUserProperty.getUserId());
                                billUserPosition.setTokenId(extension.getTId());
                                billUserPosition.setCoinId(billCoinUserProperty.getCoinId());
                                billUserPosition.setSymbolId(extension.getSId());
                                billUserPosition.setCheckOkTime(new Date(bizTime));
                                billUserPosition.setBizTime(new Date(bizTime));
                                billUserPosition.setCreateTime(new Date(bizTime));
                                billUserPosition.setUpdateTime(new Date(bizTime));
                                if (billUserPosition.getLCount().compareTo(BigDecimal.ZERO) > 0 || billUserPosition.getSCount().compareTo(BigDecimal.ZERO) > 0) {
                                    billUserPositions.add(billUserPosition);
                                }
                            }
                        }
                    }
                }
            }
        }, concurrent);

        if (queryResultIsEmpty != null && queryResultIsEmpty.getFails().size() > 0) {
            log.error("InitDataService queryResultIsEmpty.getFails().size={}", queryResultIsEmpty.getFails().size());
            return null;
        }
        return billUserPositions;
    }

    /**
     * 查询用户信息
     *
     * @param baseRequest
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageResponse<UserInfoResult> queryUserInfo(BaseRequest baseRequest, Integer pageNo, Integer pageSize) {
        try {
            return accountAssetsServiceMap.get(baseRequest.getAccountType()).queryUserInfo(baseRequest, pageNo, pageSize);
        } catch (Exception e) {
            log.error("AccountAssetsServiceFactory.queryUserInfo error accountType={} baseRequest={} pageNo={} pageSize={} error={}", baseRequest.getAccountType(), JSON.toJSONString(baseRequest), pageNo, pageSize, e.getMessage());
            throw e;
        }
    }

    /**
     * 批量查询用户中心用户基础信息
     *
     * @param userIds
     * @return
     */
    public List<UserBaseInfoDTO> batchUserBaseInfo(List<Long> userIds) {
        return userQueryFeignClient.batchUserBaseInfo(userIds);
    }
}
