package com.upex.reconciliation.service.common.constants.enums;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.upex.reconciliation.service.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.Duration;
import java.util.Date;

/**
 * 时间工具类
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TimeUnitEnum {
    MILLIS("MILLI", Duration.ofNanos(1000_000)),
    SECONDS("SECOND", Duration.ofSeconds(1)),
    MINUTES("MINUTE", Duration.ofSeconds(60)),
    HOURS("HOUR", Duration.ofSeconds(3600)),
    HALF_DAYS("HALFDAY", Duration.ofSeconds(43200)),
    DAYS("DAY", Duration.ofSeconds(86400)),
    WEEKS("WEEK", Duration.ofSeconds(7 * 86400L)),
    MONTHS("MONTH", Duration.ofSeconds(31556952L / 12)),
    YEARS("YEAR", Duration.ofSeconds(31556952L)),
    ;
    private String code;
    private Duration duration;

    public static TimeUnitEnum toEnum(String code) {
        for (TimeUnitEnum item : TimeUnitEnum.values()) {
            if (item.code.equalsIgnoreCase(code)) {
                return item;
            }
        }
        return null;
    }


    /**
     * 时间格式转换 Hour:5= 5小时 Minute:1 = 1分钟
     *
     * @param format
     * @return
     */
    public static Duration toDuration(String format) {
        if (StringUtil.isEmpty(format)) {
            return null;
        }
        String[] arr = format.split(":");
        if (arr.length == 2) {
            TimeUnitEnum timeUnitEnum = toEnum(arr[0]);
            int num = Math.abs(Integer.parseInt(arr[1]));
            return timeUnitEnum.duration.multipliedBy(num);
        }
        return null;
    }

    public static Date toDate(String format) {
        return toDate(new Date(), format);
    }

    public static Date toDate(Date date, String format) {
        if (StringUtil.isEmpty(format)) {
            return null;
        }
        String[] arr = format.split(":");
        if (arr.length == 2) {
            TimeUnitEnum timeUnitEnum = toEnum(arr[0]);
            int num = Integer.parseInt(arr[1]);
            Date toDate = null;
            if (timeUnitEnum == MONTHS) {
                toDate = DateUtil.addMonth(date, num);
            } else if (timeUnitEnum == DAYS) {
                toDate = DateUtil.addDay(date, num);
            } else if (timeUnitEnum == HOURS) {
                toDate = DateUtil.addHour(date, num);
            } else if (timeUnitEnum == MINUTES) {
                toDate = DateUtil.addMinute(date, num);
            } else if (timeUnitEnum == SECONDS) {
                toDate = DateUtil.addSecond(date, num);
            }
            return toDate;
        }
        return null;
    }
}
