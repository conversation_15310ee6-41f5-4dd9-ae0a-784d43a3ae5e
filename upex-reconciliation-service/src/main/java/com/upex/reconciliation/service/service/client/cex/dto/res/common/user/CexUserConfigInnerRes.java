package com.upex.reconciliation.service.service.client.cex.dto.res.common.user;

import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.ApiKeyPermission;
import lombok.Data;

import java.util.Date;
@Data
public class CexUserConfigInnerRes {

    private Long id;
    private String cexEmail;
    private Long cexUserId;
    private Integer cexType;
    private Long bgUserId;
    private String bgUserEmail;
    private String apiKeyLabel;
    private String apiKeyPub;
   // private String apiKeyPrivate; // 已加密存储
    private String apiKey;
    private Integer status;
    private Integer readOnly;
    private String apiPermit;
    private ApiKeyPermission  apiKeyPermission;
    private Date createTime;
    private Date updateTime;

    public void setApiPermit(String apiPermit) {
        this.apiPermit = apiPermit;
        this.apiKeyPermission = JSONObject.parseObject(apiPermit, ApiKeyPermission.class);
    }
}
