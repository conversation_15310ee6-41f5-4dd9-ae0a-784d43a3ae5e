package com.upex.reconciliation.service.business;

import com.upex.bill.dto.results.AccountAssetsInfoResult ;
import com.upex.reconciliation.service.dao.entity.AssetsBillCoinProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.model.dto.AbstractProperty;
import com.upex.reconciliation.service.model.dto.DataCalResultDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 对账检查资产
 */
public interface CheckAssetService {
    /**
     * 获取业务线类型
     *
     * @return
     */
    List<String> getBusinessType();

    /**
     * 对账检查逻辑方法
     *
     * @param <T>
     * @param property                     合并流水后返回本期期末资产
     * @param totalAccountAssetsInfoResult 获取业务系统的资产
     * @return
     */
    <T extends AbstractProperty> boolean checkAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult);


    <T extends AbstractProperty> boolean checkPosition(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult);


    <T extends AbstractProperty> boolean checkInitAssets(T oldProperty,BillCoinUserProperty newBillCoinUserProperty);

    <T extends AbstractProperty> boolean checkInitCoinAssets(T oldProperty, BillCoinProperty newBillCoinUserProperty);

    <T extends AbstractProperty> boolean checkInitCoinChangeAssets(T oldProperty, BillCoinProperty newBillCoinUserProperty);

    <T extends AbstractProperty> boolean checkInitCoinTypeAssets(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty);

    <T extends AbstractProperty> boolean checkInitCoinTypeChangeAssets(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty);


    <T extends AbstractProperty> boolean checkCoinAssetsByAccountType(T oldProperty, BillCoinProperty newBillCoinUserProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO);

    <T extends AbstractProperty> boolean checkCoinTypeInitAssetsByAccountType(List<T> oldPropertyList, BillCoinTypeProperty newBillCoinTypeProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO);

    <T extends AbstractProperty> boolean checkCoinTypeAssetsByAccountType(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty);

    <T extends AbstractProperty> BigDecimal sumForInAll(T property);


}
