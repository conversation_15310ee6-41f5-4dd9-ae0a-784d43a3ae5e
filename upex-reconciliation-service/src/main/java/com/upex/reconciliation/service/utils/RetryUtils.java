package com.upex.reconciliation.service.utils;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.base.Predicates;
import com.google.common.cache.Cache;
import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.common.listener.AssetsRetryerListener;
import com.upex.reconciliation.service.common.constants.BillConstants;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * 重试工具类
 *
 * <AUTHOR>
 * @date 2023/2/27 23:46
 */
@Slf4j
public class RetryUtils {

    /**
     * 获取新的cache缓存
     *
     * @param sleepTime     最大缓存量
     * @param attemptNumber 超时时间
     * @param timeUnit      单位
     * @return {@link Cache <K, V> }
     * <AUTHOR>
     * @date 2023/2/27 23:46
     */
    public static <K> Retryer<K> getRetryer(int sleepTime, int attemptNumber, TimeUnit timeUnit) {
        return RetryerBuilder.<K>newBuilder()
                //retryIf 重试条件
                .retryIfException()
                //等待策略：每次请求间隔1s
                .withWaitStrategy(WaitStrategies.fixedWait(sleepTime, timeUnit))
                //停止策略 : 尝试请求6次
                .withStopStrategy(StopStrategies.stopAfterAttempt(attemptNumber))
                .build();
    }

    /**
     * 获取新的cache缓存
     *
     * @param sleepTime     休眠时间
     * @param attemptNumber 请求尝试次数
     * @param timeUnit      单位
     * @param listener      监听器
     * @return {@link Cache <K, V> }
     * <AUTHOR>
     * @date 2023/2/27 23:46
     */
    public static <K> Retryer<K> getRetryer(int sleepTime, int attemptNumber, TimeUnit timeUnit, AssetsRetryerListener listener) {
        return getRetryer(sleepTime, attemptNumber, timeUnit, true, listener);
    }

    /**
     * 获取新的cache缓存
     *
     * @param sleepTime     休眠时间
     * @param attemptNumber 请求尝试次数
     * @param timeUnit      单位
     * @param listener      监听器
     * @return {@link Cache <K, V> }
     * <AUTHOR>
     * @date 2023/2/27 23:46
     */
    public static <K> Retryer<K> getRetryer(int sleepTime, int attemptNumber, TimeUnit timeUnit, String preConfigKey, AssetsRetryerListener listener) {
        return getRetryer(sleepTime, attemptNumber, timeUnit, preConfigKey, true, listener);
    }

    public static <K> Retryer<K> getRetryer(int sleepTime, int attemptNumber, TimeUnit timeUnit, boolean errorOpen, AssetsRetryerListener listener) {
        return getRetryer(sleepTime, attemptNumber, timeUnit, BillConstants.EMPTY, BillConstants.EMPTY, errorOpen, listener);
    }

    public static <K> Retryer<K> getRetryer(int sleepTime, int attemptNumber, TimeUnit timeUnit, String preConfigKey, boolean errorOpen, AssetsRetryerListener listener) {
        return getRetryer(sleepTime, attemptNumber, timeUnit, BillConstants.EMPTY, preConfigKey, errorOpen, listener);
    }

    /**
     * 获取新的cache缓存
     *
     * @param sleepTime     休眠时间
     * @param attemptNumber 请求尝试次数
     * @param timeUnit      单位
     * @param errorOpen     发送报警开关
     * @param listener      监听器
     * @return {@link Cache <K, V> }
     * <AUTHOR>
     * @date 2023/2/27 23:46
     */
    public static <K> Retryer<K> getRetryer(int sleepTime, int attemptNumber, TimeUnit timeUnit, String params, String preConfigKey, Boolean errorOpen, AssetsRetryerListener listener) {
        listener.setRetryNumber(attemptNumber);
        listener.setErrorOpen(errorOpen);
        listener.setParams(params);
        listener.setPreConfigKey(preConfigKey);
        return RetryerBuilder.<K>newBuilder()
                //retryIf 重试条件
                .retryIfException(Predicates.not(ex -> ex.getClass().equals(ApiException.class)))
                //等待策略：每次请求间隔1s
                .withWaitStrategy(WaitStrategies.fixedWait(sleepTime, timeUnit))
                //停止策略 : 尝试请求6次
                .withStopStrategy(StopStrategies.stopAfterAttempt(attemptNumber))
                // 注册监听器，每次调用重试方法都会触发监听器
                .withRetryListener(listener)
                .build();
    }


}
