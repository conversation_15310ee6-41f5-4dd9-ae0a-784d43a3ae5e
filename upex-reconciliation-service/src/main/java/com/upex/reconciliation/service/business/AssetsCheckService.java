package com.upex.reconciliation.service.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.upex.bill.dto.enums.CheckStatusEnum;
import com.upex.bill.dto.params.AssetsBaseRequest;
import com.upex.bill.dto.results.AssetsInfoResult;
import com.upex.bill.dto.results.PageResponse;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.AccountTypeConstant;
import com.upex.reconciliation.service.common.constants.BillBizTypeConstant;
import com.upex.reconciliation.service.common.constants.enums.LogLevelEnum;
import com.upex.reconciliation.service.common.constants.enums.ProfitStatusEnum;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.dao.mapper.AssetsBillCoinTypePropertyMapper;
import com.upex.reconciliation.service.dao.mapper.BillContractProfitCoinDetailMapper;
import com.upex.reconciliation.service.dao.mapper.BillContractProfitSymbolDetailMapper;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.dto.AbstractProperty;
import com.upex.reconciliation.service.service.AssetsBillCoinPropertyService;
import com.upex.reconciliation.service.service.AssetsBillCoinTypePropertyService;
import com.upex.reconciliation.service.service.AssetsBillConfigService;
import com.upex.reconciliation.service.service.BillContractProfitTransferService;
import com.upex.reconciliation.service.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.utils.GroupByKeyUtil.COINID;

@Service
@Slf4j
public class AssetsCheckService {


    @Resource
    private BillDbHelper dbHelper;

    @Resource
    private AssetsBillCoinPropertyService assetsBillCoinPropertyService;

    @Resource
    private AssetsBillConfigService assetsBillConfigService;


    @Resource
    private AssetsBillCoinTypePropertyService assetsBillCoinTypePropertyService;

    @Resource
    private AssetsBillCoinTypePropertyMapper assetsBillCoinTypePropertyMapper;
    @Resource
    private BillContractProfitTransferService billContractProfitTransferService;

    @Resource
    private BillContractProfitCoinDetailMapper billContractProfitCoinDetailMapper;
    @Resource
    private BillContractProfitSymbolDetailMapper billContractProfitSymbolDetailMapper;

    public AssetsCheckService() {
    }


    public void initSyncInfoAndAssets(List<AssetsInfoResult> assetsInfoResults,
                                      AssetsBillConfig assetsBillConfig, AssetsCheckConfig assetsCheckConfig, AssetsBaseRequest baseRequest, PageResponse pageResponse) {
        dbHelper.doDbOpInReconMaster(() -> {

            //syncPos 初始化标识
            Date currentTime = new Date();
            if (assetsBillConfig.getSyncPos() == 0) {
                //回写配置
                assetsBillConfig.setCheckOkTime(new Date(assetsCheckConfig.getBillBeginTime()));
                assetsBillConfig.setSyncPos(pageResponse.getMaxId());
                assetsBillConfig.setStatus(CheckStatusEnum.OK.getCode());
                assetsBillConfig.setPriorityLevel(assetsCheckConfig.getPriorityLevel());
                assetsBillConfigService.insertSelective(assetsBillConfig);

                //重新重置checkOkTime
                assetsBillConfig.setCheckOkTime(new Date(assetsCheckConfig.getBillBeginTime() + AccountTypeConstant.TIME_INTERVAL));
            } else {
                //变更基础配置
                assetsBillConfig.setSyncPos(pageResponse.getMaxId());
                assetsBillConfig.setStatus(CheckStatusEnum.OK.getCode());
                assetsBillConfig.setUpdateTime(currentTime);
                assetsBillConfig.setFlag(false);
                assetsBillConfigService.updateByPrimaryKeySelective(assetsBillConfig);
            }
            Date checkOkTime = new Date(baseRequest.getBeginTime());
            //bill coin
            List<AssetsBillCoinProperty> assetsBillCoinPropertyList = initCollectBillCoinProperty(checkOkTime, assetsInfoResults, assetsBillConfig);
            log.info("AssetsCheckService asset check initCollectBillCoinProperty checkOkTime={},assetsInfoResults={},assetsBillConfig={}",
                    checkOkTime, JSONObject.toJSONString(assetsInfoResults), JSONObject.toJSONString(assetsBillConfig));
            if (CollectionUtils.isNotEmpty(assetsBillCoinPropertyList)) {
                List<AssetsBillCoinProperty> toInsertList = new ArrayList<>();
                List<AssetsBillCoinProperty> toUpdateList = new ArrayList<>();
                for (AssetsBillCoinProperty assetsBillCoinProperty : assetsBillCoinPropertyList) {
                    if (assetsBillCoinProperty.getId() != null) {
                        toUpdateList.add(assetsBillCoinProperty);
                    } else {
                        toInsertList.add(assetsBillCoinProperty);
                    }
                }
                if (CollectionUtils.isNotEmpty(toUpdateList)) {
                    assetsBillCoinPropertyService.batchUpdate(toUpdateList, assetsCheckConfig.getAssetsCheckType(), assetsCheckConfig.getAssetsCheckParam());
                }
                if (CollectionUtils.isNotEmpty(toInsertList)) {
                    assetsBillCoinPropertyService.batchInsert(toInsertList, assetsCheckConfig.getAssetsCheckType(), assetsCheckConfig.getAssetsCheckParam());
                }
            }

            //bill coin type
            List<AssetsBillCoinTypeProperty> assetsBillCoinTypePropertyList = initCollectBillCoinTypeProperty(baseRequest, checkOkTime, assetsInfoResults, assetsBillConfig);
            if (CollectionUtils.isNotEmpty(assetsBillCoinTypePropertyList)) {
                List<AssetsBillCoinTypeProperty> toInsertList = new ArrayList<>();
                List<AssetsBillCoinTypeProperty> toUpdateList = new ArrayList<>();
                for (AssetsBillCoinTypeProperty assetsBillCoinTypeProperty : assetsBillCoinTypePropertyList) {
                    if (assetsBillCoinTypeProperty.getId() != null) {
                        toUpdateList.add(assetsBillCoinTypeProperty);
                    } else {
                        toInsertList.add(assetsBillCoinTypeProperty);
                    }
                }
                if (CollectionUtils.isNotEmpty(toUpdateList)) {
                    assetsBillCoinTypePropertyService.batchUpdate(toUpdateList, assetsCheckConfig.getAssetsCheckType(), assetsCheckConfig.getAssetsCheckParam());
                }
                if (CollectionUtils.isNotEmpty(toInsertList)) {
                    assetsBillCoinTypePropertyService.batchInsert(toInsertList, assetsCheckConfig.getAssetsCheckType(), assetsCheckConfig.getAssetsCheckParam());
                }
            }
            return null;
        });
    }


    private List<AssetsBillCoinProperty> initCollectBillCoinProperty(Date lastOkTime, List<AssetsInfoResult> assetsInfoResults, AssetsBillConfig assetsBillConfig) {
        //查询最后一次对账成功数据
        List<AssetsBillCoinProperty> list = assetsBillCoinPropertyService.selectAssetsByEndTime(lastOkTime, assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam());
        Map<Integer, AssetsBillCoinProperty> billCoinPropertyMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(list)) {
            billCoinPropertyMap = list.stream().collect(Collectors.toMap(AssetsBillCoinProperty::getCoinId, Function.identity(), (key1, key2) -> key2));
        }
        //汇总流水
        List<AssetsBillCoinTypeProperty> assetsBillCoinTypeProperties = collectFlowGroup(assetsInfoResults, GroupByKeyUtil.COINID);
        log.info("asset check in initCollectBillCoinProperty method assetsBillCoinTypeProperties={}", JSONObject.toJSONString(assetsBillCoinTypeProperties));
        if (CollectionUtils.isNotEmpty(assetsBillCoinTypeProperties)) {
            List<AssetsBillCoinProperty> assetsBillCoinProperties = BeanCopierUtil.copyPropertiesList(assetsBillCoinTypeProperties, AssetsBillCoinProperty.class);
            List<AssetsBillCoinProperty> billCoinPropertyList = buildBillCoinProperty(assetsBillCoinProperties, lastOkTime, billCoinPropertyMap);

            return billCoinPropertyList;
        }
        return null;
    }

    private List<AssetsBillCoinProperty> buildBillCoinProperty(List<AssetsBillCoinProperty> assetsBillCoinPropertyList,
                                                               Date checkOkTime, Map<Integer, AssetsBillCoinProperty> dbBillCoinPropertyMap) {
        List<AssetsBillCoinProperty> list = new ArrayList<>();
        for (AssetsBillCoinProperty assetsBillCoinProperty : assetsBillCoinPropertyList) {
            AssetsBillCoinProperty dbEntityProperty = dbBillCoinPropertyMap.get(assetsBillCoinProperty.getCoinId());
            AssetsBillCoinProperty coinProperty = new AssetsBillCoinProperty();
            coinProperty.setCoinId(assetsBillCoinProperty.getCoinId());
            coinProperty.setProp1(assetsBillCoinProperty.getProp1());
            coinProperty.setProp2(assetsBillCoinProperty.getProp2());
            coinProperty.setProp3(assetsBillCoinProperty.getProp3());
            coinProperty.setProp4(assetsBillCoinProperty.getProp4());
            coinProperty.setProp5(assetsBillCoinProperty.getProp5());
            coinProperty.setChangeProp5(assetsBillCoinProperty.getProp5());
            coinProperty.setChangeProp4(assetsBillCoinProperty.getProp4());
            coinProperty.setChangeProp3(assetsBillCoinProperty.getProp3());
            coinProperty.setChangeProp2(assetsBillCoinProperty.getProp2());
            coinProperty.setChangeProp1(assetsBillCoinProperty.getProp1());
            coinProperty.setCheckTime(checkOkTime);
            coinProperty.setCreateTime(new Date());
            coinProperty.setUpdateTime(new Date());
            if (dbEntityProperty != null) {
                //add data
                BillCoinCalculationUtils.addData(coinProperty, assetsBillCoinProperty, dbEntityProperty);
                coinProperty.setId(dbEntityProperty.getId());
            }
            dbBillCoinPropertyMap.remove(assetsBillCoinProperty.getCoinId());
            list.add(coinProperty);
        }
        if (dbBillCoinPropertyMap.size() > 0) {
            for (Map.Entry<Integer, AssetsBillCoinProperty> noChangePropertyEntry : dbBillCoinPropertyMap.entrySet()) {
                AssetsBillCoinProperty noChangeProperty = noChangePropertyEntry.getValue();
                noChangeProperty.cleanChangeProp();
                noChangeProperty.setCheckTime(checkOkTime);
                noChangeProperty.setCreateTime(new Date());
                noChangeProperty.setUpdateTime(new Date());
                list.add(noChangeProperty);
            }
        }
        return list;
    }

    private List<AssetsBillCoinTypeProperty> initCollectBillCoinTypeProperty(AssetsBaseRequest assetsBaseRequest, Date lastOkTime,
                                                                             List<AssetsInfoResult> assetsInfoResults, AssetsBillConfig assetsBillConfig) {
        List<AssetsBillCoinTypeProperty> list = assetsBillCoinTypePropertyService.selectByCheckTime(lastOkTime, BillBizTypeConstant.BILL_BALANCE_TYPE, assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam());
        Map<Integer, Map<String, AssetsBillCoinTypeProperty>> billCoinTypePropertyMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(list)) {
            for (AssetsBillCoinTypeProperty billCoinTypeProperty : list) {
                billCoinTypePropertyMap.computeIfAbsent(billCoinTypeProperty.getCoinId(), key -> {
                    return new HashMap<>(16);
                }).put(BillBizTypeConstant.BILL_BALANCE_TYPE, billCoinTypeProperty);
            }
        }
        //汇总流水
        List<AssetsBillCoinTypeProperty> billCoinTypeProperties = collectFlowGroup(assetsInfoResults, GroupByKeyUtil.COINID_TYPEID);

        if (CollectionUtils.isNotEmpty(billCoinTypeProperties)) {
            List<AssetsBillCoinTypeProperty> billCoinTypePropertyList =
                    buildBillCoinTypeUserProperty(billCoinTypeProperties, assetsBaseRequest, BillBizTypeConstant.BILL_BALANCE_TYPE, billCoinTypePropertyMap);
            return billCoinTypePropertyList;
        }
        return null;
    }


    private List<AssetsBillCoinTypeProperty> buildBillCoinTypeUserProperty(List<AssetsBillCoinTypeProperty> billCoinTypeProperties, AssetsBaseRequest baseRequest,
                                                                           String bizType, Map<Integer, Map<String, AssetsBillCoinTypeProperty>> dbBillCoinTypePropertyMap) {

        List<AssetsBillCoinTypeProperty> list = new ArrayList<>();
        for (AssetsBillCoinTypeProperty billProperty : billCoinTypeProperties) {
            Map<String, AssetsBillCoinTypeProperty> propertyMap = dbBillCoinTypePropertyMap.get(billProperty.getCoinId());

            AssetsBillCoinTypeProperty newProperty = new AssetsBillCoinTypeProperty();
            newProperty.setProp1(billProperty.getProp1());
            newProperty.setProp2(billProperty.getProp2());
            newProperty.setProp3(billProperty.getProp3());
            newProperty.setProp4(billProperty.getProp4());
            newProperty.setProp5(billProperty.getProp5());
            newProperty.setCoinId(billProperty.getCoinId());
            // bizType =other
            newProperty.setBizType(bizType);
            //add change data
            newProperty.setChangeProp1(billProperty.getProp1());
            newProperty.setChangeProp2(billProperty.getProp2());
            newProperty.setChangeProp3(billProperty.getProp3());
            newProperty.setChangeProp4(billProperty.getProp4());
            newProperty.setChangeProp5(billProperty.getProp5());
            newProperty.setCheckTime(new Date(baseRequest.getBeginTime()));
            newProperty.setCreateTime(new Date());
            newProperty.setUpdateTime(new Date());
            if (propertyMap != null) {
                AssetsBillCoinTypeProperty existsEntity = propertyMap.get(bizType);
                if (existsEntity != null) {
                    //add data
                    BillCoinCalculationUtils.addData(newProperty, billProperty, existsEntity);
                    newProperty.setId(existsEntity.getId());
                    propertyMap.remove(bizType);
                }
            }
            list.add(newProperty);
        }
        if (dbBillCoinTypePropertyMap.size() > 0) {
            for (Map.Entry<Integer, Map<String, AssetsBillCoinTypeProperty>> mapEntry : dbBillCoinTypePropertyMap.entrySet()) {
                Map<String, AssetsBillCoinTypeProperty> valueMap = mapEntry.getValue();
                if (valueMap != null && valueMap.size() > 0) {
                    for (Map.Entry<String, AssetsBillCoinTypeProperty> propertyEntry : valueMap.entrySet()) {
                        AssetsBillCoinTypeProperty value = propertyEntry.getValue();
                        value.cleanChangeProp();
                        value.setCheckTime(new Date(baseRequest.getBeginTime()));
                        value.setCreateTime(new Date());
                        value.setUpdateTime(new Date());
                        list.add(value);
                    }
                }
            }
        }
        return list;
    }


    public List<AssetsBillCoinProperty> collectBillCoinProperty(Date lastOkTime, List<AssetsInfoResult> assetsInfoResults, AssetsBillConfig assetsBillConfig, GlobalBillConfig globalBillConfig) {
        //查询最后一次对账成功数据
        BizLogUtils.log(LogLevelEnum.NON_KEY_INPUT, globalBillConfig, "AssetsCheckService.collectBillCoinProperty started lastOkTime {},assetsBillConfig {} ", lastOkTime, JSONObject.toJSONString(assetsBillConfig));
        List<AssetsBillCoinProperty> list = assetsBillCoinPropertyService.selectAssetsByEndTime(lastOkTime, assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam());
        Map<Integer, AssetsBillCoinProperty> billCoinPropertyMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(list)) {
            billCoinPropertyMap = list.stream().collect(Collectors.toMap(AssetsBillCoinProperty::getCoinId, Function.identity(), (key1, key2) -> key2));
        }
        //流水按照需要的维度进行分组、合并
        List<AssetsBillCoinTypeProperty> billList = collectFlowGroup(assetsInfoResults, COINID);
        //和数据库中的数据合并
        for (AssetsBillCoinTypeProperty assetsBillCoinTypeProperty : billList) {
            Integer coinId = assetsBillCoinTypeProperty.getCoinId();
            AssetsBillCoinProperty dbEntity = billCoinPropertyMap.get(coinId);
            if (dbEntity != null) {
                //合并数据
                addData(dbEntity, assetsBillCoinTypeProperty, false);
            } else {
                list.add(BeanCopierUtil.copyProperties(assetsBillCoinTypeProperty, AssetsBillCoinProperty.class));
            }
        }
        for (AssetsBillCoinProperty assetsBillCoinProperty : list) {
            assetsBillCoinProperty.setCheckTime(assetsBillConfig.getCheckOkTime());
            assetsBillCoinProperty.setCreateTime(new Date());
            assetsBillCoinProperty.setUpdateTime(new Date());
            //未匹配的数据，变化值置为0
            if (!assetsBillCoinProperty.getUpdateFlag()) {
                setChangePropZero(assetsBillCoinProperty);
            }
        }
        return list;
    }

    private <T extends AbstractProperty> void setChangePropZero(T property) {
        property.setChangeProp1(BigDecimal.ZERO);
        property.setChangeProp2(BigDecimal.ZERO);
        property.setChangeProp3(BigDecimal.ZERO);
        property.setChangeProp4(BigDecimal.ZERO);
        property.setChangeProp5(BigDecimal.ZERO);
    }


    private List<AssetsBillCoinTypeProperty> collectFlowGroup(List<AssetsInfoResult> assetsInfoResults, Integer groupKey) {
        List<AssetsBillCoinTypeProperty> list = new ArrayList<>();
        Map<String, List<AssetsInfoResult>> listMap = assetsInfoResults.stream().collect(Collectors.groupingBy(assetsInfoResult -> getKey(assetsInfoResult, groupKey)));
        Map<String, AssetsBillCoinTypeProperty> infoResultMap = new HashMap<>(16);
        for (Map.Entry<String, List<AssetsInfoResult>> entry : listMap.entrySet()) {
            AssetsBillCoinTypeProperty assetsBillCoinTypeProperty = null;

            for (AssetsInfoResult result : entry.getValue()) {
                String key = getKey(result, groupKey);
                assetsBillCoinTypeProperty = BeanCopierUtil.copyProperties(result, AssetsBillCoinTypeProperty.class);
                if (infoResultMap.get(key) == null) {
                    //init change data
                    setChangePropZero(assetsBillCoinTypeProperty);
                } else {
                    AssetsBillCoinTypeProperty billCoinProperty = infoResultMap.get(key);
                    //数据累加
                    addData(assetsBillCoinTypeProperty, billCoinProperty, true);
                }
                infoResultMap.put(key, assetsBillCoinTypeProperty);
            }
            list.add(assetsBillCoinTypeProperty);
        }
        return list;
    }


    public <T extends AbstractProperty> void addData(T newProperty, T oldProperty, boolean flag) {

        if (oldProperty.getProp1().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setProp1(newProperty.getProp1());
        } else {
            newProperty.setProp1(newProperty.getProp1().add(oldProperty.getProp1()));
        }
        if (oldProperty.getProp2().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setProp2(newProperty.getProp2());
        } else {
            newProperty.setProp2(newProperty.getProp2().add(oldProperty.getProp2()));
        }
        if (oldProperty.getProp3().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setProp3(newProperty.getProp3());
        } else {
            newProperty.setProp3(newProperty.getProp3().add(oldProperty.getProp3()));
        }
        if (oldProperty.getProp4().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setProp4(newProperty.getProp4());
        } else {
            newProperty.setProp4(newProperty.getProp4().add(oldProperty.getProp4()));
        }
        if (oldProperty.getProp5().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setProp5(newProperty.getProp5());
        } else {
            newProperty.setProp5(newProperty.getProp5().add(oldProperty.getProp5()));
        }
        if (flag) {
            if (oldProperty.getChangeProp1().compareTo(BigDecimal.ZERO) == 0) {
                newProperty.setChangeProp1(newProperty.getProp1());
            } else {
                newProperty.setChangeProp1(newProperty.getProp1().add(oldProperty.getChangeProp1()));
            }
            if (oldProperty.getChangeProp2().compareTo(BigDecimal.ZERO) == 0) {
                newProperty.setChangeProp2(newProperty.getProp2());
            } else {
                newProperty.setChangeProp2(newProperty.getProp2().add(oldProperty.getChangeProp2()));
            }
            if (oldProperty.getChangeProp3().compareTo(BigDecimal.ZERO) == 0) {
                newProperty.setChangeProp3(newProperty.getProp3());
            } else {
                newProperty.setChangeProp3(newProperty.getProp3().add(oldProperty.getChangeProp3()));
            }
            if (oldProperty.getChangeProp4().compareTo(BigDecimal.ZERO) == 0) {
                newProperty.setChangeProp4(newProperty.getProp4());
            } else {
                newProperty.setChangeProp4(newProperty.getProp4().add(oldProperty.getChangeProp4()));
            }
            if (oldProperty.getChangeProp5().compareTo(BigDecimal.ZERO) == 0) {
                newProperty.setChangeProp5(newProperty.getProp5());
            } else {
                newProperty.setChangeProp5(newProperty.getProp5().add(oldProperty.getChangeProp5()));
            }
        } else {
            newProperty.setChangeProp1(oldProperty.getProp1());
            newProperty.setChangeProp2(oldProperty.getProp2());
            newProperty.setChangeProp3(oldProperty.getProp3());
            newProperty.setChangeProp4(oldProperty.getProp4());
            newProperty.setChangeProp5(oldProperty.getProp5());
        }
        newProperty.setUpdateFlag(true);

    }

    private String getKey(AssetsInfoResult assetsInfoResult, Integer groupKey) {
        String key = null;
        switch (groupKey) {
            case COINID:
                key = GroupByKeyUtil.groupByCoinId(assetsInfoResult.getCoinId());
                break;
            case GroupByKeyUtil.COINID_TYPEID:
                key = GroupByKeyUtil.groupByCoinIdAndTypeId(assetsInfoResult.getCoinId(), assetsInfoResult.getBizType());
            default:
                break;
        }
        return key;
    }

    public List<AssetsBillCoinTypeProperty> collectBillCoinTypeProperty(AssetsBaseRequest assetsBaseRequest, Date lastOkTime, List<AssetsInfoResult> assetsInfoResults, AssetsBillConfig assetsBillConfig, GlobalBillConfig globalBillConfig) {
        Date lastCheckOkTime = assetsBillCoinTypePropertyService.selectLastCheckOkTime(lastOkTime, assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam());
        List<AssetsBillCoinTypeProperty> list = assetsBillCoinTypePropertyService.selectByCheckTime(lastCheckOkTime, null, assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam());
        BizLogUtils.log(LogLevelEnum.VITAL_DEBUG, globalBillConfig, "AssetsCheckService.collectBillCoinTypeProperty query AssetsBillCoinTypeProperty lastOkTime {} list: {}", DateUtil.getMillisecondDateStr(lastCheckOkTime), JSONObject.toJSONString(list));

        Map<String, AssetsBillCoinTypeProperty> billCoinTypePropertyMap = new HashMap<>(16);

        if (CollectionUtils.isNotEmpty(list)) {
            for (AssetsBillCoinTypeProperty billCoinTypeProperty : list) {
                String key = GroupByKeyUtil.groupByCoinIdAndTypeId(billCoinTypeProperty.getCoinId(), billCoinTypeProperty.getBizType());
                billCoinTypePropertyMap.put(key, billCoinTypeProperty);
            }
        }
        //汇总流水
        List<AssetsBillCoinTypeProperty> billList = collectFlowGroup(assetsInfoResults, GroupByKeyUtil.COINID_TYPEID);

        //和数据库中的数据合并
        for (AssetsBillCoinTypeProperty assetsBillCoinTypeProperty : billList) {
            String key = GroupByKeyUtil.groupByCoinIdAndTypeId(assetsBillCoinTypeProperty.getCoinId(), assetsBillCoinTypeProperty.getBizType());
            AssetsBillCoinTypeProperty dbEntity = billCoinTypePropertyMap.get(key);
            if (dbEntity != null) {
                dbEntity.setCheckTime(assetsBillConfig.getCheckOkTime());
                dbEntity.setCreateTime(new Date());
                dbEntity.setUpdateTime(new Date());
                addData(dbEntity, assetsBillCoinTypeProperty, false);
            } else {
                assetsBillCoinTypeProperty.setCheckTime(assetsBillConfig.getCheckOkTime());
                assetsBillCoinTypeProperty.setCreateTime(new Date());
                assetsBillCoinTypeProperty.setUpdateTime(new Date());
                //第一次初始化
                assetsBillCoinTypeProperty.setChangeProp1(assetsBillCoinTypeProperty.getProp1());
                assetsBillCoinTypeProperty.setChangeProp2(assetsBillCoinTypeProperty.getProp2());
                assetsBillCoinTypeProperty.setChangeProp3(assetsBillCoinTypeProperty.getProp3());
                assetsBillCoinTypeProperty.setChangeProp4(assetsBillCoinTypeProperty.getProp4());
                assetsBillCoinTypeProperty.setChangeProp5(assetsBillCoinTypeProperty.getProp5());
                assetsBillCoinTypeProperty.setUpdateFlag(true);
                list.add(assetsBillCoinTypeProperty);
            }
        }
        for (AssetsBillCoinTypeProperty assetsBillCoinTypeProperty : list) {
            assetsBillCoinTypeProperty.setCheckTime(assetsBillConfig.getCheckOkTime());
            assetsBillCoinTypeProperty.setCreateTime(new Date());
            assetsBillCoinTypeProperty.setUpdateTime(new Date());
            //未匹配的数据，变化值置为0
            if (!assetsBillCoinTypeProperty.getUpdateFlag()) {
                setChangePropZero(assetsBillCoinTypeProperty);
            }
            BizLogUtils.log(LogLevelEnum.VITAL_DEBUG, globalBillConfig, "AssetsCheckService.collectBillCoinTypeProperty final data : {}", JSONObject.toJSONString(assetsBillCoinTypeProperty));
        }
        return list;
    }

    public void batchInsertAndUpdate(List<AssetsBillCoinProperty> newBillCoinPropertyList,
                                     List<AssetsBillCoinTypeProperty> newBillCoinTypePropertyList,
                                     AssetsBillConfig assetsBillConfig,
                                     List<BillContractProfitTransfer> transferList,
                                     List<BillContractProfitCoinDetail> coinDetailList,
                                     List<BillContractProfitSymbolDetail> symbolDetailList) {

        dbHelper.doDbOpInReconMasterTransaction(() -> {
            //AssetsBillCoinProperty
            List<AssetsBillCoinProperty> billCoinPropertyList = BeanCopierUtil.copyPropertiesList(newBillCoinPropertyList, AssetsBillCoinProperty.class);

            //billCoinTypeProperty
            List<AssetsBillCoinTypeProperty> billCoinTypePropertyList = BeanCopierUtil.copyPropertiesList(newBillCoinTypePropertyList, AssetsBillCoinTypeProperty.class);

            //批量写入
            if (CollectionUtils.isNotEmpty(billCoinPropertyList)) {
                assetsBillCoinPropertyService.batchInsert(billCoinPropertyList, assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam());
            }
            if (CollectionUtils.isNotEmpty(billCoinTypePropertyList)) {
                assetsBillCoinTypePropertyService.batchInsert(billCoinTypePropertyList, assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam());
            }

            // 处理盈亏数据
            if (CollectionUtils.isNotEmpty(coinDetailList) && CollectionUtils.isNotEmpty(transferList)) {
                batchInsertAndUpdateProfit(assetsBillConfig, coinDetailList, transferList, symbolDetailList);
            }

            //更新配置表
            assetsBillConfig.setUpdateTime(new Date());
            assetsBillConfig.setFlag(true);
            assetsBillConfig.setStatus(CheckStatusEnum.OK.getCode());
            assetsBillConfigService.updateByPrimaryKeySelective(assetsBillConfig);
            return null;
        });
        log.info("batch user bill check ok,billConfig:{}", JSON.toJSONString(assetsBillConfig));
    }

    public void batchInsertAndUpdateProfitRepair(Date startTime, Date endTime, List<BillContractProfitCoinDetail> coinDetailList, List<BillContractProfitTransfer> transferList) {
        log.info("...batchInsertAndUpdate startTime:{} endTime:{} billContractProfit start ,coinDetailList.size:{},transferList.size:{}",
                DateUtil.getDefaultDateStr(startTime), DateUtil.getDefaultDateStr(endTime), CollectionUtils.size(coinDetailList), CollectionUtils.size(transferList));

        dbHelper.doDbOpInReconMaster(() -> {
            if (CollectionUtils.isNotEmpty(coinDetailList) && CollectionUtils.isNotEmpty(transferList)) {
                if (CollectionUtils.size(coinDetailList) == CollectionUtils.size(transferList)) {
                    Date updateDate = new Date();
                    int sumCount = 0;
                    Map<Byte, List<BillContractProfitCoinDetail>> map = coinDetailList.stream().collect(Collectors.groupingBy(BillContractProfitCoinDetail::getAccountType));
                    for (List<BillContractProfitCoinDetail> list : map.values()) {
                        if (CollectionUtils.isEmpty(list)) {
                            continue;
                        }
                        BillContractProfitCoinDetail coinDetail = list.get(0);
                        sumCount = billContractProfitCoinDetailMapper.batchUpdateStatus(coinDetail.getAccountType(), coinDetail.getAccountParam(),
                                ProfitStatusEnum.PERFORMED.getCode(),
                                ProfitStatusEnum.NOT_PERFORMED.getCode(), updateDate, list.stream().map(BillContractProfitCoinDetail::getId).collect(Collectors.toList()));
                        log.info("...batchInsertAndUpdate sumCount -{}, coinDetailList.size-{} billconfig-{}",
                                sumCount, CollectionUtils.size(list), coinDetail);
                        List<BillContractProfitTransfer> resultList = transferList.stream()
                                .filter(item -> item.getTransferCount().compareTo(BigDecimal.ZERO) != 0
                                        && Objects.equals(item.getAccountType(), coinDetail.getAccountType()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(resultList)) {
                            billContractProfitTransferService.batchInsert(resultList);
                        }
                    }

                } else {
                    //AlarmUtils.error(" ...batchInsertAndUpdate ProfitTransfer data has diff startTime-{} endTime-{}  coinDetailList-{},transferList-{}", DateUtil.getDefaultDateStr(startTime), DateUtil.getDefaultDateStr(endTime), coinDetailList, transferList);
                }
            }
            return null;
        });
    }

    public void batchInsertAndUpdateProfit(AssetsBillConfig assetsBillConfig,
                                           List<BillContractProfitCoinDetail> coinDetailList,
                                           List<BillContractProfitTransfer> transferList,
                                           List<BillContractProfitSymbolDetail> symbolDetailList) {
        log.info("...batchInsertAndUpdate AssetsCheckType:{} checkOkTime:{} billContractProfit start ,coinDetailList.size:{},transferList.size:{}",
                assetsBillConfig.getAssetsCheckType(), DateUtil.getDefaultDateStr(assetsBillConfig.getCheckOkTime()), CollectionUtils.size(coinDetailList), CollectionUtils.size(transferList));
        dbHelper.doDbOpInReconMaster(() -> {
            if (CollectionUtils.isNotEmpty(coinDetailList) && CollectionUtils.isNotEmpty(transferList)) {
                if (CollectionUtils.size(coinDetailList) == CollectionUtils.size(transferList)) {
                    Date updateDate = new Date();
                    int sumCount = 0;
                    // 更新coin数据
                    Map<Byte, List<BillContractProfitCoinDetail>> map = coinDetailList.stream().collect(Collectors.groupingBy(BillContractProfitCoinDetail::getAccountType));
                    for (List<BillContractProfitCoinDetail> list : map.values()) {
                        if (CollectionUtils.isEmpty(list)) {
                            continue;
                        }
                        for (BillContractProfitCoinDetail billContractProfitCoinDetail : list) {
                            billContractProfitCoinDetail.setStatus(ProfitStatusEnum.PERFORMED.getCode());
                            billContractProfitCoinDetail.setUpdateTime(updateDate);
                            sumCount += billContractProfitCoinDetailMapper.updateById(billContractProfitCoinDetail.getAccountType(), billContractProfitCoinDetail.getAccountParam(), billContractProfitCoinDetail);
                        }
                        log.info("...batchInsertAndUpdate sumCount -{}, coinDetailList.size-{} billconfig-{}",
                                sumCount, CollectionUtils.size(list));
                    }
                    // 更新symbol数据
                    if (CollectionUtils.isNotEmpty(symbolDetailList)) {
                        for (BillContractProfitSymbolDetail contractProfitSymbolDetail : symbolDetailList) {
                            contractProfitSymbolDetail.setUpdateTime(updateDate);
                            billContractProfitSymbolDetailMapper.updateById(contractProfitSymbolDetail.getAccountType(), contractProfitSymbolDetail.getAccountParam(), contractProfitSymbolDetail);
                        }
                    }
                    // 插入动账数据
                    List<BillContractProfitTransfer> insertTransferList = transferList.stream()
                            .filter(item -> item.getTransferCount().compareTo(BigDecimal.ZERO) != 0)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(insertTransferList)) {
                        billContractProfitTransferService.batchInsert(insertTransferList);
                    }
                    log.info("...batchInsertAndUpdate sumCount -{}, coinDetailList.size-{} billconfig-{}",
                            sumCount, CollectionUtils.size(insertTransferList));
                } else {
                    //AlarmUtils.error(" ...batchInsertAndUpdate ProfitTransfer data has diff check_ok_time-{}  coinDetailList-{},transferList-{}", assetsBillConfig.getCheckOkTime(), coinDetailList, transferList);
                }
            }
            return null;
        });
    }
}
