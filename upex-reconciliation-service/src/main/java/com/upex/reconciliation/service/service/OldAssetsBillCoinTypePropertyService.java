package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.dao.bill.entity.AssetsBillCoinTypeProperty;
import com.upex.reconciliation.service.dao.mapper.OldAssetsBillCoinTypePropertyMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Slf4j
@Service
public class OldAssetsBillCoinTypePropertyService {

    @Resource
    private BillDbHelper dbHelper;


    @Resource(name = "oldAssetsBillCoinTypePropertyMapper")
    private OldAssetsBillCoinTypePropertyMapper oldAssetsBillCoinTypePropertyMapper;




    public List<AssetsBillCoinTypeProperty> selectByCheckTime(Date checkTime,
                                                              String bizType,
                                                              String billCheckType,
                                                              String billCheckParam) {
        return dbHelper.doDbOpInBillMaster(() -> oldAssetsBillCoinTypePropertyMapper.selectByCheckTime(checkTime, bizType, billCheckType, billCheckParam));
    }


    public List<AssetsBillCoinTypeProperty> selectByCoinIdCheckTime(Date checkTime,
                                                              Integer coinId,
                                                              String billCheckType,
                                                              String billCheckParam) {
        return dbHelper.doDbOpInBillMaster(() -> oldAssetsBillCoinTypePropertyMapper.selectByCoinIdCheckTime(checkTime, coinId, billCheckType, billCheckParam));
    }


}
