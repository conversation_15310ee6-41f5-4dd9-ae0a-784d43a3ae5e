package com.upex.reconciliation.service.utils;

import com.upex.reconciliation.facade.enums.WithdrawCheckResultEnum;
import com.upex.reconciliation.service.model.dto.WithdrawCheckResultDTO;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


@Slf4j
@UtilityClass
public class AsyncUtils {

    public static Boolean fastFailAsyncResult(List<CompletableFuture<Boolean>> asyncList) {
        if (CollectionUtils.isEmpty(asyncList)) {
            return true;
        }
        for (CompletableFuture<Boolean> future : asyncList) {
            try {
                Boolean callResult = future.get();
                if (!Boolean.TRUE.equals(callResult)) {
                    return false;
                }
            } catch (Exception e) {
                log.error("AsyncUtils fastFailAsyncResult recall error:", e);
            }
        }
        return true;
    }



    public static Boolean fastFailAsyncResult(Map<String,CompletableFuture<Boolean>> asyncMap) {
        if (MapUtils.isEmpty(asyncMap)) {
            return true;
        }
        for (CompletableFuture<Boolean> future : asyncMap.values()) {
            try {
                Boolean callResult = future.get();
                if (!Boolean.TRUE.equals(callResult)) {
                    return false;
                }
            } catch (Exception e) {
                log.error("AsyncUtils fastFailAsyncResult recall error:", e);
            }
        }
        return true;
    }

    public static WithdrawCheckResultDTO fastFailAsyncResultV2(Map<String, CompletableFuture<WithdrawCheckResultDTO>> asyncMap) {
        if (MapUtils.isEmpty(asyncMap)) {
            return new WithdrawCheckResultDTO();
        }
        for (CompletableFuture<WithdrawCheckResultDTO> future : asyncMap.values()) {
            try {
                WithdrawCheckResultDTO callResult = future.get();
                if (!Boolean.TRUE.equals(callResult.isResult())) {
                    return callResult;
                }
            } catch (Exception e) {
                log.error("AsyncUtils fastFailAsyncResult recall error:", e);
            }
        }
        return new WithdrawCheckResultDTO();
    }

}
