package com.upex.reconciliation.service.common.concurrent;

import java.util.NoSuchElementException;
import java.util.concurrent.ConcurrentSkipListMap;

/**
 * 封装并发排序map
 *
 * @param <K>
 * @param <V>
 */
public class ConcurrentSortMap<K, V> extends ConcurrentSkipListMap<K, V> {
    /**
     * 获取第一个key 如果map为空，返回null
     *
     * @return
     */
    public K firstKey() {
        try {
            return super.firstKey();
        } catch (NoSuchElementException e) {
            return null;
        }
    }

    /**
     * 获取第一个key 如果map为空，返回null
     *
     * @return
     */
    public K firstKey(K def) {
        K key = firstKey();
        return key == null ? def : key;
    }

    /**
     * 获取最后一个key 如果map为空，返回null
     *
     * @return
     */
    public K lastKey() {
        try {
            return super.lastKey();
        } catch (NoSuchElementException e) {
            return null;
        }
    }

    /**
     * 获取最后一个key 如果map为空，返回def
     *
     * @return
     */
    public K lastKey(K def) {
        K key = lastKey();
        return key == null ? def : key;
    }
}
