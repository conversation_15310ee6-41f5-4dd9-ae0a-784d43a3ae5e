package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.client.kafka.MessageDeserializer;
import com.alibaba.otter.canal.connector.core.producer.MQMessageUtils;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.alibaba.otter.canal.protocol.Message;
import com.google.common.base.Stopwatch;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.upex.reconciliation.facade.enums.SQLTypeEnum;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.FinancialVirtualAccountBizService;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.entity.FinancialVirtualAccount;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 增量用户同步
 */
@Slf4j
public class FinancialAccountConsumerRunnable implements KafkaConsumerLifecycle {
    private volatile boolean running = true;
    private Map<String, Object> consumerConfig;

    private AlarmNotifyService alarmNotifyService;
    private Set<Integer> closeConsumerPatition = new HashSet<>();
    private FinancialVirtualAccountBizService financialVirtualAccountBizService;
    private Integer partitionNum;
    private Map<Integer, KafkaConsumer<String, Message>> partitionConsumerMap = new HashMap<>();

    private KafkaConsumerConfig kafkaConsumerConfig;
    @Resource
    private ReconciliationSpringContext context;

    private String topic = KafkaTopicEnum.RECON_SYNC_FINANCIAL_ACCOUNT_TOPIC.getCode();

    public FinancialAccountConsumerRunnable(ReconciliationSpringContext context, String kafkaServerAddr, KafkaConsumerConfig kafkaConsumerConfig, FinancialVirtualAccountBizService financialVirtualAccountBizService) {
        this.kafkaConsumerConfig = kafkaConsumerConfig;
        this.partitionNum = kafkaConsumerConfig.getPartitionNum();
        this.financialVirtualAccountBizService = financialVirtualAccountBizService;
        consumerConfig = new HashMap<String, Object>();
        alarmNotifyService = context.getAlarmNotifyService();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServerAddr);
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MessageDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, kafkaConsumerConfig.getConsumerGroupId());
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, kafkaConsumerConfig.getKafkaBatchSize());
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
    }

    @Override
    public void shutdown() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return closeConsumerPatition.size() != 1;
    }

    @Override
    public String getThreadPrefixName() {
        return "kafka-consumer-thread-capital-order-consumer";
    }

    @Override
    public void run() {
        // 初始化
        log.info("FinancialVirtualAccountConsumerRunnable consumerRunnables.run");
        init();
        log.info("FinancialVirtualAccountConsumerRunnable init finished");
        partitionConsumerMap.forEach((partition, kafkaConsumer) -> new Thread(() -> {
            try {
                startConsume(partition, kafkaConsumer);
            } catch (Exception e) {
                alarmNotifyService.alarmException(topic + "消费者异常");
                log.error("FinancialVirtualAccountConsumerRunnable consumerRunnables Exception:", e);
            }
        }).start());
    }

    public void init() {
        // 创建监听器，数量和partition数量保持一致
        for (int i = 0; i < partitionNum; i++) {
            KafkaConsumer<String, Message> currentConsumer = new KafkaConsumer<>(consumerConfig);
            List<TopicPartition> partitions = new ArrayList<>();
            partitions.add(new TopicPartition(topic, i));
            currentConsumer.assign(partitions);
            partitionConsumerMap.put(i, currentConsumer);
        }
    }

    private void startConsume(Integer partition, KafkaConsumer<String, Message> consumer) {
        log.info("FinancialVirtualAccountConsumerRunnable consumerRunnables.run partition {}", partition);
        Stopwatch stopwatch;
        TopicPartition topicPartition = new TopicPartition(topic, partition);
        Map<TopicPartition, OffsetAndMetadata> partitionOffsetMap = new HashMap<>();
        Long offset = null;
        Integer retryCount = 0;
        while (running) {
            try {
                kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig(KafkaTopicEnum.RECON_SYNC_FINANCIAL_ACCOUNT_TOPIC);
                if (!kafkaConsumerConfig.getIsOpen() || (retryCount >= kafkaConsumerConfig.getRetryCount())) {
                    TimeUnit.SECONDS.sleep(kafkaConsumerConfig.getSleepTime());
                    continue;
                }
                offset = null;
                // 从kafka集群中拉取消息df
                ConsumerRecords<String, Message> consumerRecords = consumer.poll(3000);
                stopwatch = Stopwatch.createStarted();
                log.info("FinancialVirtualAccountConsumerRunnable consumerRunnables.run partition {} consumerRecords size {}", partition, consumerRecords.count());
                for (ConsumerRecord<String, Message> consumerRecord : consumerRecords) {
                    offset = consumerRecord.offset();
                    if (kafkaConsumerConfig.getShowLogOpen()) {
                        log.info("offset:{}, 接收到消息：{}", offset, consumerRecord.value());
                    }
                    MQMessageUtils.EntryRowData[] datas = buildMessageData(consumerRecord.value());
                    List<FlatMessage> flatMessages = MQMessageUtils.messageConverter(datas, consumerRecord.value().getId()); // 串行分区
                    handle(flatMessages, kafkaConsumerConfig);
                }
                log.info("FinancialVirtualAccountConsumerRunnable consumerRunnables.run partition {} consumerRecords size {} execute success, time:{}", partition, consumerRecords.count(), stopwatch.stop());
                // 成功提交offset
                consumer.commitSync();
                retryCount = 0;
            } catch (Exception e) {
                log.error("FinancialVirtualAccountConsumerRunnable startConsume partitionOffsetMap:{}, error ", partitionOffsetMap, e);
                if (retryCount < kafkaConsumerConfig.getRetryCount()) {
                    alarmNotifyService.alarmException(topic + "消费消息异常");
                }
                // 异常时默认重试3次，当达到3次及以上时会sleep
                if (Objects.nonNull(offset) && (retryCount < kafkaConsumerConfig.getRetryCount())) {
                    retryCount++;
                    // 重置offset
                    consumer.seek(topicPartition, offset);
                    partitionOffsetMap.put(topicPartition, new OffsetAndMetadata(offset));
                    consumer.commitSync(partitionOffsetMap);
                }
            }
        }
        consumer.close();
        closeConsumerPatition.add(partition);
        log.info("FinancialVirtualAccountConsumerRunnable consumer.close success {}", partition);
    }


    public void handle(List<FlatMessage> flatMessages, KafkaConsumerConfig kafkaConsumerConfig) {
        if (kafkaConsumerConfig.getShowLogOpen()) {
            log.info("FinancialVirtualAccountConsumerRunnable startConsume flatMessages:{} ", JSONObject.toJSONString(flatMessages));
        }
        if (CollectionUtils.isNotEmpty(flatMessages)) {
            Map<SQLTypeEnum, List<FlatMessage>> collect = flatMessages.stream()
                    .filter(item -> kafkaConsumerConfig.getTableName().equals(item.getTable()) && StringUtils.isNotBlank(item.getType()))
                    .collect(Collectors.groupingBy(item -> SQLTypeEnum.convert(item.getType())));
            if (kafkaConsumerConfig.getShowLogOpen()) {
                log.info("FinancialVirtualAccountConsumerRunnable startConsume collect keys:{} ", collect.keySet());
            }

            // 优先处理INSERT类型
            if (collect.containsKey(SQLTypeEnum.INSERT)) {
                List<FinancialVirtualAccount> insertFinancialAccountList = buildBillChangeDataList(collect.get(SQLTypeEnum.INSERT));
                if (kafkaConsumerConfig.getShowLogOpen()) {
                    log.info("FinancialVirtualAccountConsumerRunnable startConsume collect INSERT FinancialVirtualAccounts size:{} ",
                            JSONObject.toJSONString(insertFinancialAccountList.stream().map(FinancialVirtualAccount::getId).collect(Collectors.toList())));
                }
                financialVirtualAccountBizService.saveHandler(insertFinancialAccountList);
                if (kafkaConsumerConfig.getShowLogOpen()) {
                    log.info("FinancialVirtualAccountConsumerRunnable startConsume FinancialVirtualAccountService.batchSaveFinancialVirtualAccount success for INSERT");
                    // 过滤出 insertFinancialAccountList 中修改时间最小的订单
                    insertFinancialAccountList.stream().min(Comparator.comparing(FinancialVirtualAccount::getSourceUpdateTime)).ifPresent(minOrder -> log.info("FinancialVirtualAccountConsumerRunnable startConsume minOrder:{}, current time:{}", minOrder.getSourceUpdateTime(), System.currentTimeMillis()));
                }
            }

            // 然后处理UPDATE类型
            if (collect.containsKey(SQLTypeEnum.UPDATE)) {
                List<FinancialVirtualAccount> updateOrders = buildBillChangeDataList(collect.get(SQLTypeEnum.UPDATE));
                if (kafkaConsumerConfig.getShowLogOpen()) {
                    log.info("FinancialVirtualAccountConsumerRunnable startConsume collect UPDATE FinancialVirtualAccounts size:{} ",
                            JSONObject.toJSONString(updateOrders.stream().map(FinancialVirtualAccount::getId).collect(Collectors.toList())));
                }
                financialVirtualAccountBizService.modifyHandler(updateOrders);
                if (kafkaConsumerConfig.getShowLogOpen()) {
                    log.info("FinancialVirtualAccountConsumerRunnable startConsume FinancialVirtualAccountService.batchSaveFinancialVirtualAccount success for UPDATE");
                    // 过滤出 insertOrders 中修改时间最小的订单
                    updateOrders.stream().min(Comparator.comparing(FinancialVirtualAccount::getSourceUpdateTime)).ifPresent(minOrder -> log.info("FinancialVirtualAccountConsumerRunnable startConsume minOrder:{}, current time:{}", minOrder.getSourceUpdateTime(), System.currentTimeMillis()));
                }
            }
        }
    }

    /**
     * cannal消息解析
     *
     * @param message
     * @return
     */
    public static MQMessageUtils.EntryRowData[] buildMessageData(Message message) {
        if (message.isRaw()) {
            List<ByteString> rawEntries = message.getRawEntries();
            MQMessageUtils.EntryRowData[] datas = new MQMessageUtils.EntryRowData[rawEntries.size()];
            int i = 0;
            for (Iterator var12 = rawEntries.iterator(); var12.hasNext(); ++i) {
                ByteString byteString = (ByteString) var12.next();
                try {
                    CanalEntry.Entry entry = CanalEntry.Entry.parseFrom(byteString);
                    CanalEntry.RowChange rowChange = CanalEntry.RowChange.parseFrom(entry.getStoreValue());
                    datas[i] = new MQMessageUtils.EntryRowData();
                    datas[i].entry = entry;
                    datas[i].rowChange = rowChange;
                } catch (InvalidProtocolBufferException var5) {
                    throw new RuntimeException(var5);
                }
            }
            return datas;
        } else {
            MQMessageUtils.EntryRowData[] datas = new MQMessageUtils.EntryRowData[message.getEntries().size()];
            int i = 0;
            for (Iterator var5 = message.getEntries().iterator(); var5.hasNext(); ++i) {
                CanalEntry.Entry entry = (CanalEntry.Entry) var5.next();
                try {
                    CanalEntry.RowChange rowChange = CanalEntry.RowChange.parseFrom(entry.getStoreValue());
                    datas[i] = new MQMessageUtils.EntryRowData();
                    datas[i].entry = entry;
                    datas[i].rowChange = rowChange;
                } catch (InvalidProtocolBufferException var4) {
                    throw new RuntimeException(var4);
                }
            }
            return datas;
        }
    }

    private List<FinancialVirtualAccount> buildBillChangeDataList(List<FlatMessage> flatMessages) {
        List<FinancialVirtualAccount> list = new ArrayList<>();
        for (FlatMessage flatMessage : flatMessages) {
            String typeStr = flatMessage.getType();
            boolean isDdl = flatMessage.getIsDdl();
            if (isDdl) {
                return Collections.emptyList();
            }
            SQLTypeEnum sqlTypeEnum = SQLTypeEnum.convert(typeStr);
            if (null == sqlTypeEnum) {
                log.error("unable to resolve sqlType:{}", typeStr);
                return Collections.emptyList();
            }
            List<Map<String, String>> dataList = flatMessage.getData();
            switch (sqlTypeEnum) {
                case UPDATE:
                case INSERT:
                    list.addAll(messageDecode(dataList, flatMessage));
            }
        }
        return list;
    }

    private List<FinancialVirtualAccount> messageDecode(List<Map<String, String>> dataList, FlatMessage flatMessage) {
        Map<String, String> oldMap = CollectionUtils.isEmpty(flatMessage.getOld()) ? new HashMap<>() : flatMessage.getOld().get(0);

        List<FinancialVirtualAccount> coList = new ArrayList<>();
        for (Map<String, String> map : dataList) {
            FinancialVirtualAccount co = new FinancialVirtualAccount();
            co.setId(map.get("id") == null ? null : Long.valueOf(map.get("id")));
            co.setUserId(map.get("account_id") == null ? null : Long.parseLong(map.get("account_id")));
            co.setCoinId(map.get("coin_id") == null ? null : Integer.valueOf(map.get("coin_id")));
            co.setCoinName(map.get("coin_name") == null ? null : map.get("coin_name"));
            co.setGroupType(map.get("group_type") == null ? null : Integer.valueOf(map.get("group_type")));
            co.setTotalBalance(map.get("total_balance") == null ? null : new BigDecimal(map.get("total_balance")));
            co.setSourceCreateTime(map.get("create_time") == null ? null : DateUtil.getMillisecondDate(map.get("create_time")));
            co.setSourceUpdateTime(map.get("update_time") == null ? null : DateUtil.getMillisecondDate(map.get("update_time")));
            co.setVersion(map.get("version") == null ? null : Long.parseLong(map.get("version")));
            co.setOldTotalBalance(oldMap.get("total_balance") == null ? null : new BigDecimal(oldMap.get("total_balance")));
            co.setOldUpdateTime(oldMap.get("update_time") == null ? null : DateUtil.getMillisecondDate(oldMap.get("update_time")));
            co.setPreSettleInterest(map.get("pre_settle_interest") == null ? null : new BigDecimal(map.get("pre_settle_interest")));
            Date createTime = new Date();
            co.setCreateTime(createTime);
            co.setUpdateTime(createTime);
            coList.add(co);
        }
        return coList;
    }

}


