package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.dao.entity.BillCoinProperty;
import com.upex.reconciliation.service.dao.mapper.BillCoinPropertyMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@Service
public class BillCoinPropertyService {
    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "billCoinPropertyMapper")
    private BillCoinPropertyMapper billCoinPropertyMapper;


    public int batchInsert(List<BillCoinProperty> records, Byte accountType, String accountParam) {
        if (CollectionUtils.isNotEmpty(records)) {
            return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.batchInsert(records, accountType, accountParam));
        }
        return 0;
    }

    public BillCoinProperty selectTime(Integer accountType,
                                String accountParam,
                                Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.selectTime(accountType, accountParam, checkTime));
    }

    public List<BillCoinProperty> selectAllAssets(Integer accountType,
                                       String accountParam,
                                       Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.selectAllAssets(accountType, accountParam, checkTime));
    }


    public Boolean updateByBillCoinProperty(Byte accountType, String accountParam, BillCoinProperty billCoinProperty) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.updateByBillCoinProperty(accountType, accountParam, billCoinProperty));
    }


    public BillCoinProperty selectById(Byte accountType, String accountParam, Long id) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.selectById(accountType, accountParam, id));
    }

    public List<BillCoinProperty> selectAssetsByEndTime(Date endTime, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.selectAssetsByEndTime(endTime, accountType, accountParam));
    }



    public List<BillCoinProperty> selectRangeCheckTimeRecord(Integer accountType,
                                                             String accountParam,
                                                             Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.selectRangeCheckTimeRecord(accountType, accountParam, checkTime));
    }

    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.deleteByCheckTime(accountType, accountParam, checkTime));
    }

    public Boolean batchDelete(Long beginId, Long pageSize,
                               Byte accountType,
                               String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.batchDelete(beginId, pageSize, accountType, accountParam));
    }

    public List<BillCoinProperty> selectCheckTimeRecord(Integer accountType,
                                                        String accountParam,
                                                        Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.selectCheckTimeRecord(accountType, accountParam, checkTime));
    }

    public BillCoinProperty selectLastCheckTimeRecord(Integer accountType,
                                                        String accountParam,
                                                        Integer coinId,
                                                        Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.selectLastCheckTimeRecord(accountType, accountParam, coinId,checkTime));
    }

    public Long getIdByCheckTime(Byte accountType, String accountParam, Date checkTime, String operation) {
        String operationNew = operation;
        return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.getIdByCheckTime(accountType, accountParam, checkTime, operationNew));
    }

    public boolean deleteByMaxId(Byte accountType, String accountParam, Long maxId, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.deleteByMaxId(accountType, accountParam, maxId, batchSize));
    }

    public Boolean deleteByLtCheckTime(Byte accountType, String accountParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinPropertyMapper.deleteByLtCheckTime(accountType, accountParam, checkTime, batchSize));
    }
}
