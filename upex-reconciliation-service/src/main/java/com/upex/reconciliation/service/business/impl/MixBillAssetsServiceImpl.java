package com.upex.reconciliation.service.business.impl;


import com.upex.mixcontract.process.facade.dto.MixAccountAssetsExtension;
import com.upex.reconciliation.facade.params.SysAssetsParams;
import com.upex.reconciliation.service.business.MixBillAssetsService;
import com.upex.reconciliation.service.business.StatisticsAssetsService;
import com.upex.reconciliation.service.dao.entity.BillCoinUserAssets;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.domain.MixContractAssetDto;
import com.upex.reconciliation.service.utils.MixExtensionCalUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


@Slf4j
@Service
public class MixBillAssetsServiceImpl implements MixBillAssetsService {


    @Resource
    private StatisticsAssetsService statisticsAssetsService;


    @Override
    public List<BillCoinUserProperty> calAndGetMixBillAssets(Long userId, SysAssetsParams sysAssetsParams, Date paramTime, ApolloReconciliationBizConfig apolloBillConfig) {
        Integer coinId = sysAssetsParams.getCoinId();
        Integer accountType = sysAssetsParams.getAccountType();
        String accountParam = sysAssetsParams.getAccountParam();
        List<BillCoinUserProperty> returnList = new ArrayList<>();
        log.info("userId = {}, paramTime = {}, sysAssetsParams = {}",userId,paramTime,sysAssetsParams);
        List<BillCoinUserAssets> sources ;
        if (coinId != null) {
            sources = statisticsAssetsService.queryRecordsExtensionStrategySingleCoin(userId, coinId, accountType, accountParam, paramTime);
        }else{
            sources = statisticsAssetsService.queryRecordsExtensionStrategyEachCoin(userId, accountType, accountParam, paramTime, true);
        }
        if (Objects.isNull(sources)) {
            log.info("mix calAndGetMixBillAssets source list is empty and billCoinUserAsset is null then return null !");
            return returnList;
        }
        returnList = dealBillCoinAssetsPros(sources,accountParam,apolloBillConfig, null);
        return returnList;
    }


    private List<BillCoinUserProperty> dealBillCoinAssetsPros(List<BillCoinUserAssets> sources, String accountParam, ApolloReconciliationBizConfig apolloBillConfig, MixAccountAssetsExtension mixAccountAssetsExtension){
        return dealBillCoinAssetsPros(sources,  accountParam,apolloBillConfig, mixAccountAssetsExtension,null);
    }

    private List<BillCoinUserProperty> dealBillCoinAssetsPros(List<BillCoinUserAssets> sources, String accountParam, ApolloReconciliationBizConfig apolloBillConfig,
                                                              MixAccountAssetsExtension mixAccountAssetsExtension, Set<Integer> hasPositionCoinList) {
        List<BillCoinUserProperty> returnList = new ArrayList<>();
        if(Objects.nonNull(mixAccountAssetsExtension) && CollectionUtils.isNotEmpty(sources)){
            Set<Integer> set ;
            //从apollo获取下架的币种，如果匹配，不计算权益
            if (CollectionUtils.isNotEmpty(apolloBillConfig.getCoinIdsEliminated())) {
                set = new HashSet<>(apolloBillConfig.getCoinIdsEliminated());
            }else{
                set = new HashSet<>();
            }

            for (BillCoinUserAssets info : sources) {
                if (set.contains(info.getCoinId())) {
                    continue;
                }
                MixContractAssetDto mixContractAssetDto = MixExtensionCalUtil.calAccountCoinEquityV2(info.getCoinId(),info.getBillCoinUserAssetsParamList(),info.getProp2(),info.getProp3(), mixAccountAssetsExtension.getMPriceMap(), mixAccountAssetsExtension.getSPriceMap());
                BillCoinUserProperty resultProperty = new BillCoinUserProperty();
                resultProperty.setProp1(mixContractAssetDto.getCoinTotalCount());
                resultProperty.setProp2(info.getProp2());
                resultProperty.setProp3(info.getProp3());
                resultProperty.setUserId(info.getUserId());
                resultProperty.setCoinId(info.getCoinId());
                returnList.add(resultProperty);
                if(hasPositionCoinList != null && mixContractAssetDto.getHasPositionFlag()){
                    // 账户权益不等于 prop2 + prop3，有持仓  resultProperty.getProp1().compareTo(info.getProp2().add(info.getProp3())) != 0
                    hasPositionCoinList.add(apolloBillConfig.getAccountType().intValue());
                }
            }
        }
        return returnList;
    }



    @Override
    public List<BillCoinUserProperty> queryBillAssetsWithMix(Long userId, Integer accountType, String accountParam, Date paramTime, MixAccountAssetsExtension extension, Set<Integer> hasPositionCoinList, Boolean isMaster) {
        List<BillCoinUserProperty>  returnList = new ArrayList<>();
        List<BillCoinUserAssets> assetsList = statisticsAssetsService.queryRecordsExtensionStrategyEachCoin(userId, accountType, accountParam, paramTime, isMaster);
        if (CollectionUtils.isNotEmpty(assetsList)) {
            ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType.byteValue());
            returnList = dealBillCoinAssetsPros(assetsList,accountParam,apolloBillConfig,extension,hasPositionCoinList);
        }
        return returnList;
    }



}
