package com.upex.reconciliation.service.service.client.cex.dto.req.binance;

import com.binance.connector.client.common.Pair;
import lombok.Data;

@Data
public class BinanceUserWithdrawHistoryReq extends BinanceApiBaseReq {

    private Long startTime;

    private Long endTime;

    private String coin;

    private Integer status;

    private String idList;

    public void setStartTime(Long startTime) {
        queryParams.add(new Pair("startTime", String.valueOf(startTime)));
    }

    public void setEndTime(Long endTime) {
        queryParams.add(new Pair("endTime", String.valueOf(endTime)));
    }

    public void setCoin(String coin) {
        queryParams.add(new Pair("coin", coin));
    }

    public void setStatus(Integer status) {
        queryParams.add(new Pair("status", String.valueOf(status)));
    }

    public void setIdList(String idList) {
       queryParams.add(new Pair("idList", idList));
    }
}
