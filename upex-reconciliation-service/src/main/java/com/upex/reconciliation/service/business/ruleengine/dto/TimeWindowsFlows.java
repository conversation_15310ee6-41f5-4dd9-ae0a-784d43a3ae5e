package com.upex.reconciliation.service.business.ruleengine.dto;

import com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TimeWindowsFlows {
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 账户类型
     */
    private String accountType;
    /**
     * 负值流水
     */
    private List<BillCoinTypeUserProperty> negativeFlows;
}
