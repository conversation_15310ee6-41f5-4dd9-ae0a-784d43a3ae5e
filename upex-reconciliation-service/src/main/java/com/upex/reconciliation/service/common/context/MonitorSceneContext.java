package com.upex.reconciliation.service.common.context;

import com.upex.reconciliation.service.business.MonitorNonInputDataRuleService;
import com.upex.reconciliation.service.model.config.MonitorSceneTaskConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.Data;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 业务上下文类
 * 用于传递所有的 @service对象到不同的 Command 、Module
 */
@Service
@Data
public class MonitorSceneContext implements ApplicationContextAware {
    private Map<String, MonitorNonInputDataRuleService> monitorDataRuleServiceMap;

    public void runScene(Long sceneId, Date bizTime, Byte accountType) {
        MonitorSceneTaskConfig taskConfig = ReconciliationApolloConfigUtils.getMonitorSceneTaskConfig(sceneId);
        if (taskConfig != null) {
            String aggregationDataRuleService = taskConfig.getDataProcessService();
            MonitorNonInputDataRuleService monitorDataRuleService = monitorDataRuleServiceMap.get(aggregationDataRuleService);
            if (monitorDataRuleService != null) {
                monitorDataRuleService.processScene(taskConfig.getSceneId(), bizTime, accountType);
            }
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        monitorDataRuleServiceMap = applicationContext.getBeansOfType(MonitorNonInputDataRuleService.class);
    }
}
