package com.upex.reconciliation.service.service.client.cex.convert.res;

import com.upex.reconciliation.service.service.client.cex.dto.res.ICexApiBaseRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import com.upex.reconciliation.service.service.client.cex.client.ICexApi;

public interface IConvertResAdpater<SR extends ICexApiBaseRes> extends ICexApi {

    CommonRes convertSpotCoinAssetRes(SR  sourceRes);

    CommonRes convertFundingCoinAssetRes(SR sourceRes);

    CommonRes convertUContractCoinAssetRes(SR sourceRes);

    CommonRes convertCoinContractCoinAssetRes(SR sourceRes);

    CommonRes convertMarginCoinAssetRes(SR sourceRes);

    CommonRes convertIsolatedMarginCoinAssetRes(SR sourceRes);

    CommonRes convertFlexEarnPositionRes(SR sourceRes);

    CommonRes convertLockedEarnPositionRes(SR sourceRes);

    CommonRes convertApikeyPermissionRes(SR sourceRes);

    CommonRes convertUserStatusRes(SR sourceRes);

    CommonRes convertSubUserListRes(SR sourceRes);

    CommonRes convertSpotPositionRes(SR sourceRes);

    CommonRes convertSubUserSpotAssetRes(SR sourceRes);

    CommonRes convertSubUserMarginAssetRes(SR sourceRes);

    CommonRes convertSubUserUContractAssetRes(SR sourceRes);

    CommonRes convertSubUserCoinContractAssetRes(SR sourceRes);

    CommonRes convertSupportCoinListRes(SR sourceRes);

    CommonRes  convertDepositeAddress(SR sourceRes);

    CommonRes convertUserDepositeHistory(SR sourceRes);

    CommonRes convertUserWithdrawHistory(SR sourceRes);

    CommonRes  convertPayTransferHistory(SR sourceRes);

    CommonRes convertParentSubTransferRecordRes(SR sourceRes);

    CommonRes convertUniversialTransferListRes(SR sourceRes);



}
