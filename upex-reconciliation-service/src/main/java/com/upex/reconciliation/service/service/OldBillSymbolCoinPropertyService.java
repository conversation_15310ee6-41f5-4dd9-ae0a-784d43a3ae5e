package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.dao.entity.BillSymbolCoinProperty;
import com.upex.reconciliation.service.dao.entity.BillSymbolProperty;

import java.util.Date;
import java.util.List;

public interface OldBillSymbolCoinPropertyService {
    List<BillSymbolCoinProperty> selectListByCheckTime(Byte accountType, String accountParam, Date checkOkTime);
}
