package com.upex.reconciliation.service.business;

import com.upex.reconciliation.facade.dto.results.StatisticsDetailProperty;
import com.upex.reconciliation.facade.enums.StatisticsStateEnum;
import com.upex.reconciliation.facade.enums.StatisticsTypeEnum;
import com.upex.reconciliation.facade.enums.TotalAssetsEnum;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.StatisticsAssetTypeProperty;
import com.upex.reconciliation.service.dao.entity.StatisticsProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserAssets;
import com.upex.ticker.facade.dto.PriceVo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/10/12 下午6:51
 * @Description
 */
public interface StatisticsAssetsService {

    /**
     * 需要使用额外策略获取统计记录(币种维度)
     *
     * @param userId
     * @param accountType
     * @param accountParam
     * @param snapshotTime
     * @param isMaster
     * @return
     */
    List<BillCoinUserAssets> queryRecordsExtensionStrategyEachCoin(Long userId, Integer accountType, String accountParam, Date snapshotTime, Boolean isMaster);

    List<BillCoinUserAssets> queryRecordsExtensionStrategySingleCoin(Long userId, Integer coinId, Integer accountType, String accountParam, Date snapshotTime);

    Map<Integer, BillCoinUserAssets> queryBillCoinUserAssetsMap(List<BillCoinUserProperty> allCoinPropertyList, Long userId, Integer accountType, String accountParam, Date lastFiveMin);

    BillCoinUserAssets querySingleCoinBillCoinUserAssets(Long userId, Integer accountType, Integer coinId, String accountParam, Date lastFiveMin);


}
