package com.upex.reconciliation.service.business.ruleengine.core;

import com.upex.reconciliation.service.business.ruleengine.enums.ExecuteTypeEnum;
import com.upex.reconciliation.service.business.RuleEngineDataService;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.thread.BaseAsyncTaskManager;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.business.ruleengine.config.RuleEngineProcessConfig;
import com.upex.reconciliation.service.business.ruleengine.mvel.MvelEngine;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 规则引擎
 */
@Slf4j
@Component
public class RuleEngine implements InitializingBean {
    private RuleEngineContext ruleEngineContext;
    @Autowired
    private AlarmNotifyService alarmNotifyService;
    @Autowired
    private RuleEngineDataService ruleEngineDataService;

    @Resource(name = "ruleEngineAsyncTaskManager")
    private BaseAsyncTaskManager baseAsyncTaskManager;

    @Override
    public void afterPropertiesSet() throws Exception {
        ruleEngineContext = new RuleEngineContext();
        ruleEngineContext.put(RuleEngineContext.ALARM_NOTIFY_SERVICE, alarmNotifyService);
        ruleEngineContext.put(RuleEngineContext.RULE_ENGINE_DATA_SERVICE, ruleEngineDataService);
    }

    /**
     * 根据账单流水执行
     *
     * @param currentBill
     */
    public void executeByBillFlow(CommonBillChangeData currentBill) {
        if (currentBill == null) {
            return;
        }
        List<RuleEngineProcessConfig> ruleEngineProcessConfigList = ReconciliationApolloConfigUtils.getRuleEngineProcessConfigList();
        for (RuleEngineProcessConfig ruleEngineProcessConfig : ruleEngineProcessConfigList) {
            if (ruleEngineProcessConfig.getIsOpen() && ExecuteTypeEnum.BILL_FLOW.getCode().equals(ruleEngineProcessConfig.getExecuteType())) {
                RuleEngineExecuteContext ruleEngineExecuteContext = createRuleEngineExecuteContext(ruleEngineProcessConfig);
                ruleEngineExecuteContext.getContext().put("billFlowData", currentBill);
                baseAsyncTaskManager.submit(() -> execute(ruleEngineExecuteContext));
            }
        }
    }

    /**
     * 根据账单流水执行
     *
     * @param currentBill
     */
    public void executeByBillFlow(RuleEngineProcessConfig ruleEngineProcessConfig, CommonBillChangeData currentBill) {
        if (currentBill == null || ruleEngineProcessConfig == null) {
            return;
        }
        RuleEngineExecuteContext ruleEngineExecuteContext = createRuleEngineExecuteContext(ruleEngineProcessConfig);
        ruleEngineExecuteContext.getContext().put("billFlowData", currentBill);
        execute(ruleEngineExecuteContext);
    }

    /**
     * 根据定时任务执行
     *
     * @param processCodes
     */
    public void executeBySchedule(List<String> processCodes) {
        if (CollectionUtils.isEmpty(processCodes)) {
            return;
        }
        for (String processCode : processCodes) {
            RuleEngineProcessConfig ruleEngineProcessConfig = ReconciliationApolloConfigUtils.getRuleEngineProcessConfigByCode(processCode);
            if (ruleEngineProcessConfig != null && ruleEngineProcessConfig.getIsOpen() && ExecuteTypeEnum.SCHEDULE.getCode().equals(ruleEngineProcessConfig.getExecuteType())) {
                RuleEngineExecuteContext ruleEngineExecuteContext = createRuleEngineExecuteContext(ruleEngineProcessConfig);
                execute(ruleEngineExecuteContext);
            }
        }
    }

    /**
     * 根据定时任务执行
     *
     * @param ruleEngineProcessConfig
     */
    public void executeBySchedule(RuleEngineProcessConfig ruleEngineProcessConfig) {
        if (ruleEngineProcessConfig != null && ruleEngineProcessConfig.getIsOpen() && ExecuteTypeEnum.SCHEDULE.getCode().equals(ruleEngineProcessConfig.getExecuteType())) {
            RuleEngineExecuteContext ruleEngineExecuteContext = createRuleEngineExecuteContext(ruleEngineProcessConfig);
            execute(ruleEngineExecuteContext);
        }
    }

    /**
     * 创建规则引擎执行上下文
     *
     * @param ruleEngineProcessConfig
     * @return
     */
    private RuleEngineExecuteContext createRuleEngineExecuteContext(RuleEngineProcessConfig ruleEngineProcessConfig) {
        RuleEngineExecuteContext ruleEngineExecuteContext = new RuleEngineExecuteContext();
        ruleEngineExecuteContext.setAlarmNotifyService(alarmNotifyService);
        ruleEngineExecuteContext.setRuleEngineDataService(ruleEngineDataService);
        ruleEngineExecuteContext.setRuleEngineProcessConfig(ruleEngineProcessConfig);
        ruleEngineExecuteContext.setExtensionConfig(ruleEngineProcessConfig.getExtensionConfig());
        return ruleEngineExecuteContext;
    }

    /**
     * 执行规则引擎
     *
     * @param ruleEngineExecuteContext
     */
    private void execute(RuleEngineExecuteContext ruleEngineExecuteContext) {
        RuleEngineProcessConfig ruleEngineProcessConfig = ruleEngineExecuteContext.getRuleEngineProcessConfig();
        long loadStartTimeMillis = System.currentTimeMillis();
        long loadExecuteTime = 0;
        long checkExecuteTime = 0;
        long handleExecuteTime = 0;
        try {
            if (StringUtils.isNotEmpty(ruleEngineProcessConfig.getDataLoaderRule())) {
                Object result = MvelEngine.evaluate(ruleEngineProcessConfig.getDataLoaderRule(), ruleEngineExecuteContext);
                if (result == null || !Boolean.parseBoolean(result.toString())) {
                    return;
                }
            }
        } catch (Exception e) {
            log.error("DataLoaderRule规则引擎执行异常，规则配置：{}", ruleEngineProcessConfig.getProcessCode(), e);
            return;
        }finally {
            if (ruleEngineProcessConfig.getLogShowOpen()) {
                loadExecuteTime = System.currentTimeMillis() - loadStartTimeMillis;
                log.info("execute DataLoaderRule 规则引擎 ruleEngineProcessConfig processCode={}, 执行耗时：DataLoaderRule:{}ms, DataCheckerRule:{}ms, DataHandlerRule:{}ms", ruleEngineProcessConfig.getProcessCode(), loadExecuteTime, checkExecuteTime, handleExecuteTime);
            }
        }

        long checkStartTimeMillis = System.currentTimeMillis();
        try {
            if (StringUtils.isNotEmpty(ruleEngineProcessConfig.getDataCheckerRule())) {
                Object result = MvelEngine.evaluate(ruleEngineProcessConfig.getDataCheckerRule(), ruleEngineExecuteContext);
                if (result == null || !Boolean.parseBoolean(result.toString())) {
                    return;
                }
            }
        } catch (Exception e) {
            log.error("DataCheckerRule规则引擎执行异常，规则配置：{}", ruleEngineProcessConfig.getProcessCode(), e);
            return;
        }finally {
            if (ruleEngineProcessConfig.getLogShowOpen()) {
                checkExecuteTime = System.currentTimeMillis() - checkStartTimeMillis;
                log.info("execute DataCheckerRule 规则引擎 ruleEngineProcessConfig processCode={}, 执行耗时：DataLoaderRule:{}ms, DataCheckerRule:{}ms, DataHandlerRule:{}ms", ruleEngineProcessConfig.getProcessCode(), loadExecuteTime, checkExecuteTime, handleExecuteTime);
            }
        }

        long handleStartTimeMillis = System.currentTimeMillis();
        try {
            if (StringUtils.isNotEmpty(ruleEngineProcessConfig.getDataHandlerRule())) {
                Object result = MvelEngine.evaluate(ruleEngineProcessConfig.getDataHandlerRule(), ruleEngineExecuteContext);
                if (result == null || !Boolean.parseBoolean(result.toString())) {
                    return;
                }
            }
        } catch (Exception e) {
            log.error("DataHandlerRule规则引擎执行异常，规则配置：{}", ruleEngineProcessConfig.getProcessCode(), e);
            return;
        }finally {
            if (ruleEngineProcessConfig.getLogShowOpen()) {
                handleExecuteTime = System.currentTimeMillis() - handleStartTimeMillis;
                log.info("execute DataHandlerRule 规则引擎 ruleEngineProcessConfig processCode={}, 执行耗时：DataLoaderRule:{}ms, DataCheckerRule:{}ms, DataHandlerRule:{}ms", ruleEngineProcessConfig.getProcessCode(), loadExecuteTime, checkExecuteTime, handleExecuteTime);
            }
        }
    }
}
