package com.upex.reconciliation.service.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillCoinUserPropertyError;
import com.upex.reconciliation.service.dao.mapper.BillCoinUserPropertyErrorMapper;
import com.upex.reconciliation.service.model.dto.MaxMinIdDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class BillCoinUserPropertyErrorService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billCoinUserPropertyErrorMapper")
    private BillCoinUserPropertyErrorMapper billCoinUserPropertyErrorMapper;

    public int batchInsert(List<BillCoinUserPropertyError> records, Byte accountType, String accountParam) {
        if (CollectionUtils.isNotEmpty(records)) {
            return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyErrorMapper.batchInsert(records, accountType, accountParam));
        }
        return 0;
    }

    public BillCoinUserPropertyError selectByUserCoinAndBizId(Long userId, Integer coinId, Long lastBizId, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyErrorMapper.selectByUserCoinAndBizId(userId, coinId, lastBizId, accountType, accountParam));
    }

    public BillCoinUserPropertyError selectLatestByUser(Long userId, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyErrorMapper.selectLatestByUser(userId, accountType, accountParam));
    }

    public Boolean batchDelete(Long beginId, Long pageSize, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyErrorMapper.batchDelete(beginId, pageSize, accountType, accountParam));
    }

    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date startTime, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyErrorMapper.deleteByCheckTime(accountType, accountParam, startTime, checkTime));
    }

    public Integer deleteById(Byte accountType, String accountParam, Long id) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyErrorMapper.deleteById(accountType, accountParam, id));
    }

    /**
     * 修复数据 xxl-job调用
     *
     * @param param
     */
    public void repairUserPropertyErrorData(JSONObject param) {
        JSONArray deleteIds = param.getJSONArray("deleteIds");
        Byte accountType = param.getByte("accountType");
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        // 删除数据
        if (deleteIds != null && deleteIds.size() > 0) {
            for (int i = 0; i < deleteIds.size(); i++) {
                deleteById(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), deleteIds.getLong(i));
            }
            log.info("repairUserPropertyErrorData delete success accountType:", accountTypeEnum.getCode());
        }
        // 插入数据
        JSONObject insertData = param.getJSONObject("insertData");
        if (insertData != null) {
            Long userId = insertData.getLong("userId");
            Integer coinId = insertData.getIntValue("coinId");
            Date checkTime = insertData.getDate("checkTime");
            Long lastBizId = insertData.getLong("lastBizId");
            String params = insertData.toJSONString();
            Date nowDate = new Date();
            BillCoinUserPropertyError billCoinUserPropertyError = new BillCoinUserPropertyError();
            billCoinUserPropertyError.setUserId(userId);
            billCoinUserPropertyError.setCoinId(coinId);
            billCoinUserPropertyError.setCheckTime(checkTime);
            billCoinUserPropertyError.setCreateTime(nowDate);
            billCoinUserPropertyError.setUpdateTime(nowDate);
            billCoinUserPropertyError.setLastBizId(lastBizId);
            billCoinUserPropertyError.setParams(params);
            batchInsert(Lists.newArrayList(billCoinUserPropertyError), accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
            log.info("repairUserPropertyErrorData insert success accountType:", accountTypeEnum.getCode());
        }
    }

    public Boolean deleteByLtCheckTime(Byte accountType, String accountParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyErrorMapper.deleteByLtCheckTime(accountType, accountParam, checkTime, batchSize));
    }

    public MaxMinIdDTO selectMaxMinId(Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyErrorMapper.selectMaxMinId(accountType, accountParam));
    }

    /**
     * 获取所有乱序用户数据最后一条<userId#coinId,BillCoinUserPropertyError></>
     *
     * @param accountType
     * @param accountParam
     * @return
     */
    public Map<String, BillCoinUserPropertyError> getAllLastUserPropertyErrorMap(Byte accountType, String accountParam) {
        Map<String, BillCoinUserPropertyError> resultMap = new HashMap<>();
        MaxMinIdDTO maxMinIdDTO = this.selectMaxMinId(accountType, accountParam);
        if (maxMinIdDTO == null) {
            return resultMap;
        }
        List<Long[]> idSegments = maxMinIdDTO.buildIdSegments(500L);
        for (Long[] idSegment : idSegments) {
            List<BillCoinUserPropertyError> billCoinUserPropertyErrors = dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyErrorMapper.getByStartIdAndEndId(accountType, accountParam, idSegment[0], idSegment[1]));
            for (BillCoinUserPropertyError billCoinUserPropertyError : billCoinUserPropertyErrors) {
                BillCoinUserPropertyError lastCoinUserPropertyError = resultMap.get(billCoinUserPropertyError.groupByUserCoinId());
                if (lastCoinUserPropertyError == null || billCoinUserPropertyError.getLastBizId() > lastCoinUserPropertyError.getLastBizId()) {
                    resultMap.put(billCoinUserPropertyError.groupByUserCoinId(), billCoinUserPropertyError);
                }
            }
        }
        return resultMap;
    }
}
