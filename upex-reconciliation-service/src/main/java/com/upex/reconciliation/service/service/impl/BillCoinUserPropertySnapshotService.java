package com.upex.reconciliation.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserPropertyError;
import com.upex.reconciliation.service.dao.mapper.BillCoinUserPropertySnapshotMapper;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.service.BillCoinUserPropertyErrorService;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.utils.task.TaskManager;
import com.upex.utils.task.TaskVoidBatchResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BillCoinUserPropertySnapshotService {

    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "taskManager")
    private TaskManager taskManager;
    @Resource
    private BillCoinUserPropertyErrorService billCoinUserPropertyErrorService;
    @Resource(name = "billCoinUserPropertySnapshotMapper")
    private BillCoinUserPropertySnapshotMapper billCoinUserPropertySnapshotMapper;


    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date checkTime, Long pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.deleteByCheckTime(accountType, accountParam, checkTime, pageSize));
    }


    public Boolean updateById(Byte accountType, String accountParam, BillCoinUserProperty billCoinUserProperty) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.updateById(accountType, accountParam, billCoinUserProperty));
    }

    public Boolean updateSelectiveById(Byte accountType, String accountParam, BillCoinUserProperty billCoinUserProperty) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.updateSelectiveById(accountType, accountParam, billCoinUserProperty));
    }

    public Integer updateSpropByUserCoinCheckTime(Byte accountType, String accountParam, Long userId, Integer coinId, Date checkTime, BillCoinUserProperty billCoinUserProperty) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.updateSpropByUserCoinCheckTime(accountType, accountParam, userId, coinId, checkTime, billCoinUserProperty));
    }

    public BillCoinUserProperty selectTime(Long userId,
                                           Integer accountType,
                                           String accountParam,
                                           Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectTime(userId, accountType, accountParam, checkTime));
    }


    public BillCoinUserProperty selectById(Byte accountType, String accountParam, Long id) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectById(accountType, accountParam, id));
    }

    public int batchInsert(List<BillCoinUserProperty> records, Byte accountType, String accountParam) {
        if (CollectionUtils.isNotEmpty(records)) {
            return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.batchInsert(records, accountType, accountParam));
        }
        return 0;
    }

    public Boolean batchDelete(Long beginId, Long pageSize, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.batchDelete(beginId, pageSize, accountType, accountParam));
    }

    public int batchInsertOrUpdate(List<BillCoinUserProperty> records, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.batchInsertOrUpdate(records, accountType, accountParam));
    }

    public List<BillCoinUserProperty> selectRangeCheckTimeRecordPage(Integer accountType,
                                                                     String accountParam,
                                                                     Date startTime,
                                                                     Date endTime,
                                                                     Long minId,
                                                                     Long pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectRangeCheckTimeRecordPage(accountType, accountParam, startTime, endTime, minId, pageSize));
    }

    public List<BillCoinUserProperty> selectMaxCheckTime(Byte accountType,
                                                         String accountParam,
                                                         Long userId) {
        List<BillCoinUserProperty> list = dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectMaxCheckTime(accountType, accountParam, userId));
        return CollectionUtils.isNotEmpty(list) ? list : Collections.emptyList();
    }


    public List<BillCoinUserProperty> selectCheckTimeRecord(Integer accountType,
                                                            String accountParam,
                                                            Date checkTime,
                                                            Long beginId,
                                                            Long pageSize) {
        List<BillCoinUserProperty> result = dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectCheckTimeRecord(accountType, accountParam, checkTime, beginId, pageSize));
        if (CollectionUtils.isNotEmpty(result)) {
            return result;
        }
        return Collections.emptyList();
    }

    public BillCoinUserProperty selectCoinUserBeforeCheckTimeRecord(Integer accountType,
                                                                    String accountParam,
                                                                    Integer coinId,
                                                                    Long userId,
                                                                    Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectCoinUserBeforeCheckTimeRecord(accountType, accountParam, coinId, userId, checkTime));
    }


    public List<BillCoinUserProperty> selectAllCoinUserByCheckTimeAndCoinIds(Integer accountType,
                                                                             String accountParam,
                                                                             Long userId,
                                                                             Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectAllCoinUserByCheckTimeAndCoinIds(accountType, accountParam, userId, checkTime));
    }


    public BillCoinUserProperty selectCoinUserLatest(Integer accountType,
                                                     String accountParam,
                                                     Integer coinId,
                                                     Long userId) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectCoinUserLatest(accountType, accountParam, coinId, userId));
    }

    public BillCoinUserProperty selectCoinUserAfterCheckTimeRecord(Integer accountType,
                                                                   String accountParam,
                                                                   Integer coinId,
                                                                   Long userId,
                                                                   Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectCoinUserAfterCheckTimeRecord(accountType, accountParam, coinId, userId, checkTime));
    }

    public List<BillCoinUserProperty> selectUserLatestRecord(Integer accountType,
                                                             String accountParam,
                                                             Long userId,
                                                             Integer coinId) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectUserLatestRecord(accountType, accountParam, userId, coinId));
    }

    public List<BillCoinUserProperty> selectByIds(Date checkTime, Integer coinId, List<Long> uids, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectByIds(checkTime, coinId, uids, accountType, accountParam));
    }

    /**
     * 获取时间片id
     *
     * @param checkTime
     * @return
     */
    public Long getIdByCheckTime(Byte accountType, String accountParam, Date checkTime, String operation) {
        String operationNew = operation;
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.getIdByCheckTime(accountType, accountParam, checkTime, operationNew));
    }

    /**
     * 删除数据
     *
     * @param accountType
     * @param accountParam
     * @param maxId
     * @param batchSize
     * @return
     */
    public Boolean deleteByMaxId(byte accountType, String accountParam, Long maxId, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.deleteByMaxId(accountType, accountParam, maxId, batchSize));
    }

    /**
     * 查询用户资产
     *
     * @param accountType
     * @param accountParam
     * @param userId
     * @param coinIds
     * @param checkTime
     * @return
     */
    public List<BillCoinUserProperty> selectCoinUserSnapshotAsset(Byte accountType,
                                                                  String accountParam,
                                                                  Long userId,
                                                                  List<Integer> coinIds,
                                                                  Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectCoinUserSnapshotAsset(accountType, accountParam, userId, coinIds, checkTime));
    }

    public Long selectMaxId(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectMaxId(accountType, accountParam, checkTime));
    }

    public Long selectMinId(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectMinId(accountType, accountParam, checkTime));
    }

    public List<BillCoinUserProperty> selectCoinUserSnapshotByIdSegment(Byte accountType, String accountParam, Date checkTime, Long minId, Long maxId) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectCoinUserSnapshotByIdSegment(accountType, accountParam, checkTime, minId, maxId));
    }

    /**
     * 并发查询资产快照Os
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @param pageSize
     * @param concurrentSize
     * @return
     */
    public List<BillCoinUserProperty> concurrentSelectCoinUserSnapshot(Byte accountType,
                                                                       String accountParam,
                                                                       Date checkTime,
                                                                       Long pageSize,
                                                                       Integer concurrentSize) {
        // 查询资产最大最小id
        Long maxId = selectMaxId(accountType, accountParam, checkTime);
        Long minId = selectMinId(accountType, accountParam, checkTime);
        if (maxId == null || minId == null) {
            return Collections.emptyList();
        }
        List<Long[]> idSegmentList = new ArrayList<>();
        for (int i = 0; i < ((maxId - minId) / pageSize + 1); i++) {
            idSegmentList.add(new Long[]{minId + i * pageSize, minId + (i + 1) * pageSize});
        }
        Queue<BillCoinUserProperty> snapshotQueue = new ConcurrentLinkedQueue<>();
        TaskVoidBatchResult queryResultIsEmpty = taskManager.forEachSubmitBatchAndWait(idSegmentList, (Long[] idSegment) -> {
            List<BillCoinUserProperty> billCoinUserPropertyList = this.selectCoinUserSnapshotByIdSegment(accountType, accountParam, checkTime, idSegment[0], idSegment[1]);
            if (CollectionUtils.isNotEmpty(billCoinUserPropertyList)) {
                billCoinUserPropertyList.forEach(item -> item.reloadDataParamsConvert(AccountTypeEnum.toEnum(accountType)));
                snapshotQueue.addAll(billCoinUserPropertyList);
            }
        }, concurrentSize);
        if (queryResultIsEmpty != null && queryResultIsEmpty.getFails().size() > 0) {
            throw new RuntimeException("concurrentSelectCoinUserSnapshot queryResultIsEmpty.getFails().size=" + queryResultIsEmpty.getFails().size());
        }
        return new ArrayList<>(snapshotQueue);
    }

    public BillCoinUserProperty selectLastUserAssetByUserCoinId(Byte accountType,
                                                                String accountParam,
                                                                Long userId,
                                                                Integer coinId,
                                                                Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectLastUserAssetByUserCoinId(accountType, accountParam, userId, coinId, checkTime));
    }

    /**
     * 修复snapshot资产倒叙表
     *
     * @param jobParam
     */
    public void repairReversedCoinUserProperty(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        String action = jsonObject.getString("action");
        Byte accountType = jsonObject.getByte("accountType");
        Long startCheckTime = jsonObject.getLong("startCheckTime");
        Long endCheckTime = jsonObject.getLong("endCheckTime");
        Long batchSize = jsonObject.getLong("batchSize") == null ? 1000L : jsonObject.getLong("batchSize");
        Long concurrentSize = jsonObject.getLong("concurrentSize") == null ? 10L : jsonObject.getLong("concurrentSize");
        Long logSize = jsonObject.getLong("logSize") == null ? 100L : jsonObject.getLong("logSize");
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        long timeSliceSize = globalBillConfig.getMergeTimeSliceSizeSecond() * BillConstants.MILLIS_PER_SECOND;
        // 查询出当前时间段的资产快照数据
        List<BillCoinUserPropertyError> billCoinUserPropertyErrorList = new ArrayList<>();
        Date currentCheckTime = new Date(startCheckTime);
        while (currentCheckTime.getTime() <= endCheckTime) {
            List<BillCoinUserProperty> currentBillCoinUserPropertyList = this.concurrentSelectCoinUserSnapshot(accountType, accountTypeEnum.getAccountParam(), currentCheckTime, batchSize, concurrentSize.intValue());
            Queue<BillCoinUserProperty> lastSnapshotQueue = new ConcurrentLinkedQueue<>();
            TaskVoidBatchResult queryResultIsEmpty = taskManager.forEachSubmitBatchAndWait(currentBillCoinUserPropertyList, (BillCoinUserProperty currentBillCoinUserProperty) -> {
                BillCoinUserProperty lastBillCoinUserProperty = this.selectLastUserAssetByUserCoinId(accountType, accountTypeEnum.getAccountParam(), currentBillCoinUserProperty.getUserId(), currentBillCoinUserProperty.getCoinId(), currentBillCoinUserProperty.getCheckTime());
                if (lastBillCoinUserProperty != null) {
                    lastBillCoinUserProperty.reloadDataParamsConvert(accountTypeEnum);
                    lastSnapshotQueue.add(lastBillCoinUserProperty);
                }
            }, concurrentSize.intValue());
            if (queryResultIsEmpty != null && queryResultIsEmpty.getFails().size() > 0) {
                throw new RuntimeException("concurrentSelectCoinUserSnapshot queryResultIsEmpty.getFails().size=" + queryResultIsEmpty.getFails().size());
            }
            // 查询出当期 快照数据 查询出下期快照数据 coin+user维度对比lastBillId是否乱序 如果乱序打印日志
            Map<String, BillCoinUserProperty> lastBillCoinUserPropertyMap = lastSnapshotQueue.stream().collect(Collectors.toMap(BillCoinUserProperty::groupByCoinUser, Function.identity()));
            for (BillCoinUserProperty currentBillCoinUserProperty : currentBillCoinUserPropertyList) {
                BillCoinUserProperty lastBillCoinUserProperty = lastBillCoinUserPropertyMap.get(currentBillCoinUserProperty.groupByCoinUser());
                if (lastBillCoinUserProperty != null && lastBillCoinUserProperty.getLastBizId() != null && lastBillCoinUserProperty.getLastBizId() != null
                        && lastBillCoinUserProperty.getLastBizId() > currentBillCoinUserProperty.getLastBizId()) {
                    BillCoinUserPropertyError billCoinUserPropertyError = BillCoinUserPropertyError.generateFromCoinUserProperty(lastBillCoinUserProperty, accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
                    billCoinUserPropertyError.setCheckTime(currentCheckTime);
                    billCoinUserPropertyError.setCreateTime(new Date());
                    billCoinUserPropertyError.setUpdateTime(new Date());
                    billCoinUserPropertyErrorList.add(billCoinUserPropertyError);
                }
            }
            // 查询下一期对比数据
            currentCheckTime = new Date(currentCheckTime.getTime() + timeSliceSize);
        }

        if (CollectionUtils.isNotEmpty(billCoinUserPropertyErrorList)) {
            log.error("repairReversedCoinUserPropertySnapshot accountType:{} size:{} billCoinUserPropertyErrorList:{}", accountType, billCoinUserPropertyErrorList.size(), JSON.toJSONString(billCoinUserPropertyErrorList.size() > logSize ? billCoinUserPropertyErrorList.subList(0, logSize.intValue()) : billCoinUserPropertyErrorList));
            if ("repair".equalsIgnoreCase(action)) {
                // 修复倒叙数据
                billCoinUserPropertyErrorService.batchInsert(billCoinUserPropertyErrorList, accountType, accountTypeEnum.getAccountParam());
            }
        }
    }

    public List<BillCoinUserProperty> selectCoinUserLtCheckTime(Byte accountType,
                                                                String accountParam,
                                                                Long userId,
                                                                Integer coinId,
                                                                Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.selectCoinUserLtCheckTime(accountType, accountParam, userId, coinId, checkTime));
    }

    public Boolean deleteByIds(Byte accountType, String accountParam, List<Long> coinUserIds) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertySnapshotMapper.deleteByIds(accountType, accountParam, coinUserIds));
    }
}
