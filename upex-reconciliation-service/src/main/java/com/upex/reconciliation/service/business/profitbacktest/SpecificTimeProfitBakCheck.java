package com.upex.reconciliation.service.business.profitbacktest;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.model.config.ProfitBakCheckConfig;
import com.upex.reconciliation.service.model.dto.ProfitBakCheckReq;
import com.upex.reconciliation.service.service.BillUserWithdrawProfitRecordService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 指定用户回测
 */
@Slf4j
@Service
public class SpecificTimeProfitBakCheck extends AbstractProfitBakCheck {

    @Resource
    BillUserWithdrawProfitRecordService billUserWithdrawProfitRecordService;

    private static final Integer pageSize = 100;

    @Override
    public ProfitBakCheckReq getProfitCheckReq() {
        ProfitBakCheckConfig profitBakCheckConfig = ReconciliationApolloConfigUtils.getProfitBakCheckConfig();
        if (profitBakCheckConfig == null) {
            return null;
        }
        ProfitBakCheckReq profitBakCheckReq;
        Long startTime = profitBakCheckConfig.getReqStartTime(), endTime = profitBakCheckConfig.getReqEndTime();
        List<Long> userIds = getUserIdByTime(startTime, endTime);
        profitBakCheckReq = ProfitBakCheckReq.builder()
                .userIds(userIds)
                .reqStartTime(new Date(startTime))
                .build();
        log.info("指定时间范围回测:{}", JSONObject.toJSONString(profitBakCheckReq));
        return profitBakCheckReq;
    }

    public List<Long> getUserIdByTime(Long startTime, Long endTime) {
        Long minId = billUserWithdrawProfitRecordService.selectMinId(new Date(startTime),new Date( endTime));
        Long maxId = billUserWithdrawProfitRecordService.selectMaxId(new Date(startTime),new Date(endTime));
        List<Long> allUserIds = Lists.newArrayList();
        Long startId = minId, endId;
        while (startId <= maxId) {
            endId = startId + pageSize;
            List<Long> userIds = billUserWithdrawProfitRecordService.getUserIdsIdRange(startId, endId);
            if (CollectionUtils.isNotEmpty(userIds)) {
                allUserIds.addAll(userIds);
            }
            startId = endId;
        }
        return allUserIds;
    }


    @Override
    public Boolean isSupport() {
        //配置apollo就是指定回测
        Boolean isSupport = true;
        ProfitBakCheckConfig profitBakCheckConfig = ReconciliationApolloConfigUtils.getProfitBakCheckConfig();
        if (profitBakCheckConfig != null && CollectionUtils.isEmpty(profitBakCheckConfig.getUserIds()) && profitBakCheckConfig.getReqStartTime() != null &&
                profitBakCheckConfig.getReqEndTime() != null &&
                (System.currentTimeMillis() - profitBakCheckConfig.getEffectiveTime() < BillConstants.FIVE_MINE_MIL_SEC)) {
            isSupport = true;
            return isSupport;
        }
        //配置xxl-job就是指定回测
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.isBlank(jobParam)) {
            isSupport = false;
        }
        try {
            JSONObject data = JSONObject.parseObject(jobParam);
            JSONArray userIds = data.getJSONArray("userIds");
            Long effectiveTime = data.getLong("effectiveTime");
            Long startTime = data.getLong("startTime");
            Long endTime = data.getLong("endTime");
            if (CollectionUtils.isEmpty(userIds) && (System.currentTimeMillis() - effectiveTime) < BillConstants.FIVE_MINE_MIL_SEC &&
                    startTime != null && endTime != null) {
                isSupport = true;
                return isSupport;
            }
        } catch (Exception e) {
            log.info("xxl-job-profit-bakcheck jobParam is error");
            isSupport = false;
        }
        return isSupport;
    }
}
