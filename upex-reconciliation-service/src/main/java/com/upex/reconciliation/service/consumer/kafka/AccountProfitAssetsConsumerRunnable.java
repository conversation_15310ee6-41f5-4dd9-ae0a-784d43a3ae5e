package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.client.kafka.MessageDeserializer;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.alibaba.otter.canal.protocol.Message;
import com.google.common.util.concurrent.RateLimiter;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.enums.SQLTypeEnum;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.business.module.impl.BillUserProfitCheckModule;
import com.upex.reconciliation.service.common.constants.ReconciliationCommandEnum;
import com.upex.reconciliation.service.common.constants.enums.LogLevelEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.entity.BillAllConfig;
import com.upex.reconciliation.service.model.BillCmdWrapper;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.service.BillAllConfigService;
import com.upex.reconciliation.service.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.*;
import static com.upex.reconciliation.service.utils.MetricsUtil.*;

/**
 * @Description: kafka消费者消费消息, 手动同步提交offset
 **/

@Slf4j
public class AccountProfitAssetsConsumerRunnable implements KafkaConsumerLifecycle {
    private List<KafkaConsumer<String, Message>> consumerList;
    private Byte accountType;
    private volatile boolean running = true;
    private Map<String, Object> consumerConfig;
    private String topic;
    private String groupId;
    private Integer partitionNum;
    private ApolloReconciliationBizConfig apolloBizConfig;
    private String kafkaConsumerKey;
    private Map<Integer, KafkaConsumer<String, Message>> partitionConsumerMap = new HashMap<>();
    private Set<Integer> closeConsumerPatition = new HashSet<>();
    private Map<Integer, Long> partitionOffsetMap = new ConcurrentHashMap<>();
    private AlarmNotifyService alarmNotifyService;
    private RedisTemplate<String, Object> redisTemplate;
    private BillEngineManager billEngineManager;
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    private BillAllConfigService billAllConfigService;
    private Map<Integer, RateLimiter> partitionRateLimiterMap = new ConcurrentHashMap<>();

    public AccountProfitAssetsConsumerRunnable(ReconciliationSpringContext context, String kafkaServers, KafkaConsumerConfig kafkaConsumerConfig, BillEngineManager billEngineManager, String kafkaConsumerKey) {
        this.accountType = Byte.valueOf(kafkaConsumerConfig.getAccountType());
        this.topic = kafkaConsumerConfig.getTopicName();
        this.groupId = EnvUtil.getKafkaConsumerGroup(kafkaConsumerConfig.getConsumerGroupId());
        this.partitionNum = kafkaConsumerConfig.getPartitionNum();
        this.consumerList = new ArrayList<>();
        this.alarmNotifyService = context.getAlarmNotifyService();
        this.redisTemplate = context.getRedisTemplate();
        this.billEngineManager = billEngineManager;
        this.accountAssetsServiceFactory = context.getAccountAssetsServiceFactory();
        this.billAllConfigService = context.getBillAllConfigService();
        this.kafkaConsumerKey = kafkaConsumerKey;
        consumerConfig = new HashMap<String, Object>();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServers);
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MessageDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, this.groupId);
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, kafkaConsumerConfig.getKafkaBatchSize());
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
        apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(Byte.valueOf(kafkaConsumerConfig.getAccountType()));
    }

    @Override
    public void run() {
        // 初始化
        log.info("AccountProfitAssetsConsumerRunnable consumerRunnables.run");
        init();
        log.info("AccountProfitAssetsConsumerRunnable init finished");
        for (Map.Entry<Integer, KafkaConsumer<String, Message>> entry : partitionConsumerMap.entrySet()) {
            new Thread(() -> {
                try {
                    startConsume(entry.getKey(), entry.getValue());
                } catch (Exception e) {
                    log.error("AccountProfitAssetsConsumerRunnable.startConsume error accountType {} partition {}", accountType, entry.getKey(), e);
                }
            }, "kafka-consumer-" + accountType + "-" + entry.getKey()).start();
        }
    }

    private void startConsume(Integer partition, KafkaConsumer<String, Message> consumer) {
        log.info("AccountProfitAssetsConsumerRunnable consumerRunnables.run accounType {} partition {}", accountType, partition);
        while (running) {
            try {
                // 从kafka集群中拉取消息df
                ConsumerRecords<String, Message> consumerRecords = consumer.poll(3000);
                AtomicInteger bizMessageSizeCounter = new AtomicInteger(0);
                consumerRecords.forEach(new java.util.function.Consumer<ConsumerRecord<String, Message>>() {
                    @Override
                    public void accept(ConsumerRecord<String, Message> consumerRecord) {
                        MetricsUtil.histogram(HISTOGRAM_KAFKA_CONSUMER + accountType + "_" + partition, () -> {
                            partitionOffsetMap.put(partition, consumerRecord.offset());
                            Long consumerTimestamp = System.currentTimeMillis();
                            List<FlatMessage> flatMessages = canalMessageDecode(consumerRecord.value());
                            Integer bizMessageSize = handle(accountType, flatMessages, consumerRecord.offset(), consumerRecord.partition(), consumerRecord.timestamp(), consumerTimestamp);
                            bizMessageSizeCounter.addAndGet(bizMessageSize);
                        });
                    }
                });
                consumer.commitSync();
                int messageCount = consumerRecords.count();
                if (messageCount > 0) {
                    partitionRateLimiterMap.get(partition).acquire(messageCount);
                    double rate = partitionRateLimiterMap.get(partition).getRate();
                    KafkaConsumerConfig kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig().getConsumerConfig().get(this.kafkaConsumerKey);
                    if (rate != kafkaConsumerConfig.getMsgRateLimit()) {
                        log.info("AccountProfitAssetsConsumerRunnable recover rateLimiter accountType:{} partition:{} rate:{} newRate:{}", accountType, partition, rate, kafkaConsumerConfig.getMsgRateLimit());
                        partitionRateLimiterMap.get(partition).setRate(kafkaConsumerConfig.getMsgRateLimit());
                    }
                }
            } catch (Exception e) {
                log.error("AccountProfitAssetsConsumerRunnable startConsume error accountType {} partition {} error:{}", accountType, partition, e.getMessage(), e);
                alarmNotifyService.alarm(accountType, KAFKA_POLL_PROFIT_CONSUMER_ERROR, accountType, partition);
            }
        }
        consumer.close();
        closeConsumerPatition.add(partition);
        log.info("AccountProfitAssetsConsumerRunnable consumer.close success {} {}", accountType, partition);
    }

    /**
     * 初始化kafka
     */
    private void init() {
        KafkaConsumer<String, Message> consumer = new KafkaConsumer<String, Message>(consumerConfig);
        try {
            consumer.subscribe(Arrays.asList(topic));
            Set<TopicPartition> assignment = new HashSet<>();
            while (assignment.size() == 0) {
                consumer.poll(Duration.ofSeconds(3));
                assignment = consumer.assignment();
                log.info("AccountProfitAssetsConsumerRunnable try consumer.assignment {} {} {}", accountType, topic, groupId);
            }
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
            BillAllConfig billAllConfig = billAllConfigService.selectByTypeAndParam(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
            // 重置消费位点
            Map<TopicPartition, OffsetAndMetadata> offsetMap = new HashMap<>();
            if (billAllConfig != null) {
                Long pullTime = billAllConfig.getCheckOkTime().getTime() - apolloBizConfig.getUserProfitPullKafkaMessageBackTime();
                Long pullTimeOffset = DateUtil.addHour(new Date(), apolloBizConfig.getUserProfitPullKafkaMessageBackHour()).getTime();
                Long minPullTime = Math.min(pullTime, pullTimeOffset);
                Map<TopicPartition, Long> topicPartitionLongMap = new HashMap<>();
                for (TopicPartition partitionInfo : assignment) {
                    topicPartitionLongMap.put(partitionInfo, minPullTime);
                }
                Map<TopicPartition, Long> endOffsets = consumer.endOffsets(assignment);
                Map<TopicPartition, Long> beginningOffsets = consumer.beginningOffsets(assignment);
                Map<TopicPartition, OffsetAndTimestamp> offsetsForTimes = consumer.offsetsForTimes(topicPartitionLongMap);
                for (Map.Entry<TopicPartition, OffsetAndTimestamp> entry : offsetsForTimes.entrySet()) {
                    long offset = entry.getValue() != null ? entry.getValue().offset() : endOffsets.get(entry.getKey());
                    offsetMap.put(entry.getKey(), new OffsetAndMetadata(offset));
                }
                log.info("AccountProfitAssetsConsumerRunnable offset info beginningOffsets={} endOffsets={} offsetsForTimes={}",
                        beginningOffsets, endOffsets, offsetsForTimes);
            }
            if (offsetMap.size() > 0) {
                for (Map.Entry<TopicPartition, OffsetAndMetadata> entry : offsetMap.entrySet()) {
                    consumer.seek(entry.getKey(), entry.getValue().offset());
                }
                consumer.commitSync();
            }
            log.info("AccountProfitAssetsConsumerRunnable finished start topic: {} config:{} offsetMap:{}",
                    topic, JSONObject.toJSONString(consumerConfig), offsetMap);
        } catch (Exception e) {
            log.error("AccountProfitAssetsConsumerRunnable.init reset offset error ", e);
        } finally {
            consumer.close();
        }
        KafkaConsumerConfig kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig().getConsumerConfig().get(this.kafkaConsumerKey);
        // 创建监听器，数量和partition数量保持一致
        for (int i = 0; i < partitionNum; i++) {
            KafkaConsumer<String, Message> currentConsumer = new KafkaConsumer<String, Message>(consumerConfig);
            List<TopicPartition> partitions = new ArrayList<>();
            partitions.add(new TopicPartition(topic, i));
            currentConsumer.assign(partitions);
            partitionRateLimiterMap.put(i, RateLimiter.create(kafkaConsumerConfig.getMsgRateLimit()));
            partitionConsumerMap.put(i, currentConsumer);
        }
    }

    /**
     * 消息处理
     *
     * @param accountType
     * @param flatMessages
     * @param offset
     * @param partition
     * @param kafkaTimestamp
     * @param consumerTimestamp
     * @return
     */
    public Integer handle(Byte accountType, List<FlatMessage> flatMessages, Long offset, Integer partition, Long kafkaTimestamp, Long consumerTimestamp) {
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        Integer bizMessageSize = 0;
        for (FlatMessage flatMessage : flatMessages) {
            List<CommonBillChangeData> commonBillChangeDataList = buildBillChangeDataList(accountType, apolloBizConfig, flatMessage, offset, partition);
            if (CollectionUtils.isNotEmpty(commonBillChangeDataList)) {
                bizMessageSize += commonBillChangeDataList.size();
                Set<Long> kafkaAbandonMessageBlackList = apolloBizConfig.getKafkaAbandonMessageBlackList();
                for (CommonBillChangeData commonBillChangeData : commonBillChangeDataList) {
                    if (commonBillChangeData.getCoinId() == null) {
                        throw new RuntimeException("AccountProfitAssetsConsumerRunnable kafka buildBillChangeDataList error, coinId is null");
                    }
                    commonBillChangeData.setKafkaTimestamp(kafkaTimestamp);
                    commonBillChangeData.setConsumerTimestamp(consumerTimestamp);
                    if (apolloBizConfig.getKafkaDecoderLogMsgIds().contains(commonBillChangeData.getBizId())) {
                        log.info("AccountProfitAssetsConsumerRunnable.handle log message accountType {} partition {} data {}", accountType, partition, JSON.toJSONString(commonBillChangeData));
                    }
                    if (kafkaAbandonMessageBlackList.contains(commonBillChangeData.getBizId())) {
                        log.info("AccountProfitAssetsConsumerRunnable.handle abandon message accountType {} partition {} data {}", accountType, partition, JSON.toJSONString(commonBillChangeData));
                        continue;
                    }
                    processBusiness(commonBillChangeData);
                }
            }
        }
        return bizMessageSize;
    }

    /**
     * 消息解析
     *
     * @param accountType
     * @param apolloBizConfig
     * @param flatMessage
     * @param offset
     * @param partition
     * @return
     */
    private List<CommonBillChangeData> buildBillChangeDataList(Byte accountType, ApolloReconciliationBizConfig apolloBizConfig, FlatMessage flatMessage, Long offset, Integer partition) {
        MetricsUtil.counter(COUNTER_BINLOG_CMD_ALL);
        String typeStr = flatMessage.getType();
        boolean isDdl = flatMessage.getIsDdl();
        if (isDdl) {
            return Collections.emptyList();
        }
        SQLTypeEnum sqlTypeEnum = SQLTypeEnum.convert(typeStr);
        if (null == sqlTypeEnum) {
            log.error("AccountProfitAssetsConsumerRunnable unable to resolve sqlType:{}", typeStr);
            return Collections.emptyList();
        }
        List<Map<String, String>> dataList = flatMessage.getData();
        switch (sqlTypeEnum) {
            case DELETE:
                log.error("AccountProfitAssetsConsumerRunnable.buildBillChangeDataList UPDATE accountType:{} newData:{} oldData:{}", accountType, JSON.toJSONString(dataList));
                if (CollectionUtils.isNotEmpty(dataList)) {
                    for (Map<String, String> message : dataList) {
                        String userId = getMessageUserId(message);
                        String id = getMessageId(message);
                        alarmNotifyService.alarm(accountType, KAFKA_CONSUMER_DELETE_DATA, accountType, userId, id);
                        redisTemplate.opsForSet().add(RedisUtil.KAFKA_ILLEGAL_MESSAGE_USER_IDS, userId);
                    }
                }
                return Collections.emptyList();
            case UPDATE:
                List<CommonBillChangeData> updateMessageList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(dataList)) {
                    List<Map<String, String>> whiteList = new ArrayList<>();
                    for (int i = 0; i < dataList.size(); i++) {
                        Map<String, String> message = dataList.get(i);
                        String id = getMessageId(message);
                        if (apolloBizConfig.getKafkaUpdateMessageWhiteList().contains(id)) {
                            whiteList.add(message);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(whiteList)) {
                        List<CommonBillChangeData> commonBillChangeDataList = accountAssetsServiceFactory.getMessageDecoder(accountType).messageDecode(alarmNotifyService, whiteList, flatMessage, partition, offset, accountType);
                        if (CollectionUtils.isNotEmpty(commonBillChangeDataList)) {
                            updateMessageList.addAll(commonBillChangeDataList);
                        }
                        BizLogUtils.log(LogLevelEnum.NON_KEY_INPUT, apolloBizConfig, "AccountProfitAssetsConsumerRunnable KafkaMsgDecoder update accountType {} partition:{}, offset:{} changelist:{} ", accountType, partition, offset, JSONObject.toJSONString(updateMessageList));
                    }
                }
                List<Map<String, String>> oldDataList = flatMessage.getOld();
                log.error("AccountProfitAssetsConsumerRunnable.buildBillChangeDataList UPDATE accountType:{} newData:{} oldData:{}", accountType, JSON.toJSONString(dataList), JSONObject.toJSONString(oldDataList));
                if (CollectionUtils.isNotEmpty(dataList)) {
                    Set<String> forbiddenUserIds = new HashSet<>();
                    for (int i = 0; i < dataList.size(); i++) {
                        Map<String, String> message = dataList.get(i);
                        String userId = getMessageUserId(message);
                        String id = getMessageId(message);
                        alarmNotifyService.alarm(accountType, KAFKA_CONSUMER_UPDATE_DATA, accountType, userId, id);
                        // 如果更新字段不在白名单中，写入redis提币阻断
                        if (oldDataList.size() > i) {
                            Map<String, String> oldMessage = oldDataList.get(i);
                            for (Map.Entry<String, String> oldMessageEntry : oldMessage.entrySet()) {
                                if (!apolloBizConfig.getKafkaUpdateColumnWhiteList().contains(oldMessageEntry.getKey())) {
                                    forbiddenUserIds.add(userId);
                                    break;
                                }
                            }
                        }
                    }
                    // 提币阻断
                    if (CollectionUtils.isNotEmpty(forbiddenUserIds)) {
                        forbiddenUserIds.forEach(userId -> {
                            alarmNotifyService.alarm(accountType, KAFKA_CONSUMER_UPDATE_DATA_FORBIDDEN, accountType, userId);
                            redisTemplate.opsForSet().add(RedisUtil.KAFKA_ILLEGAL_MESSAGE_USER_IDS, userId);
                        });
                    }
                }
                return updateMessageList;
            case INSERT:
                MetricsUtil.counter(COUNTER_BINLOG_CMD_INSERT);
                if (apolloBizConfig.getReconKafkaOpsConfig().isKafkaConsumeSkipBusinessLogic()) {
                    List<String> createTimes = new ArrayList<String>();
                    List<String> bizTimes = new ArrayList<String>();
                    List<String> createDates = new ArrayList<String>();
                    List<String> bizIds = new ArrayList<String>();
                    if (CollectionUtils.isNotEmpty(dataList)) {
                        for (Map<String, String> dataMap : dataList) {
                            if (dataMap.get("create_time") != null) {
                                createTimes.add(dataMap.get("create_time"));
                            }
                            if (dataMap.get("biz_time") != null) {
                                bizTimes.add(dataMap.get("biz_time"));
                            }
                            if (dataMap.get("create_date") != null) {
                                createDates.add(dataMap.get("create_date"));
                            }
                            if (dataMap.get("bill_id") != null) {
                                bizIds.add((dataMap.get("bill_id")));
                            } else if (dataMap.get("id") != null) {
                                bizIds.add((dataMap.get("id")));
                            }
                        }
                    }
                    BizLogUtils.log(LogLevelEnum.FULL, apolloBizConfig, "AccountProfitAssetsConsumerRunnable KafkaMsgDecoder accountType {} partition:{}, offset:{}  bizIds:{},createTimes:{}, bizTimes:{} ,createDates:{} ", accountType, partition, offset, JSONObject.toJSONString(bizIds), JSONObject.toJSONString(createTimes), JSONObject.toJSONString(bizTimes), JSONObject.toJSONString(createDates));
                    return Collections.emptyList();
                }
                List<CommonBillChangeData> list = accountAssetsServiceFactory.getMessageDecoder(accountType).messageDecode(alarmNotifyService, dataList, flatMessage, partition, offset, accountType);
                BizLogUtils.log(LogLevelEnum.NON_KEY_INPUT, apolloBizConfig, "AccountProfitAssetsConsumerRunnable KafkaMsgDecoder accountType {} partition:{}, offset:{} changelist:{} ", accountType, partition, offset, JSONObject.toJSONString(list));
                return list;
        }
        return Collections.emptyList();
    }

    /**
     * 消息入队
     *
     * @param data
     */
    private void processBusiness(CommonBillChangeData data) {
        BillCmdWrapper billCmdWrapper = new BillCmdWrapper(ReconciliationCommandEnum.BILL_CHANGE, data);
        BillUserProfitCheckModule userProfitCheckModule = billEngineManager.getUserProfitCheckModule(accountType);
        userProfitCheckModule.offerCommand(billCmdWrapper);
    }

    /**
     * 获取消息userid
     *
     * @param message
     * @return
     */
    private String getMessageUserId(Map<String, String> message) {
        if (MapUtils.isEmpty(message)) {
            return null;
        }
        String userId = message.get("account_id");
        if (StringUtils.isEmpty(userId)) {
            userId = message.get("user_id");
        }
        return userId;
    }

    /**
     * 获取消息id
     *
     * @param message
     * @return
     */
    private String getMessageId(Map<String, String> message) {
        if (MapUtils.isEmpty(message)) {
            return null;
        }
        String id = message.get("id");
        if (StringUtils.isEmpty(id)) {
            id = message.get("bill_id");
        }
        return id;
    }

    @Override
    public void shutdown() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return closeConsumerPatition.size() != partitionNum;
    }

    @Override
    public String getThreadPrefixName() {
        return "kafka-consumer-profit-" + accountType;
    }
}


