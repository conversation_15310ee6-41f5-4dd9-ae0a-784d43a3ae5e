package com.upex.reconciliation.service.common.chain;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 判断为空校验，强制校验，出错抛出异常
 * @ClassName: CheckEmptyValidataHandler
 * @date 2022/4/29 11:35 AM
 * <AUTHOR>
*/
@Component
public class CheckEmptyForceValidataHandler extends AbstractCheckHandler{
    @Override
    public void doHandler(Long userId, Long snapShotTime) {
        if (Objects.isNull(userId)) {
            throw new ApiException(BillExceptionEnum.ACCOUNT_PARAMS_IS_NULL, "账户参数用户id为空");
        }
        if (Objects.nonNull(next)) {
            next.doHandler(userId, snapShotTime);
        }
    }
}
