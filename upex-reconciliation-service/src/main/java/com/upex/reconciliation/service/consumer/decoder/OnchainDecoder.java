package com.upex.reconciliation.service.consumer.decoder;

import com.alibaba.otter.canal.protocol.FlatMessage;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class OnchainDecoder extends AbstractMessageDecoder {

    @Override
    public List<CommonBillChangeData> doMessageDecode(List<Map<String, String>> dataList, FlatMessage flatMessage, Integer partition, Long offset, Byte accountType) {
        List<CommonBillChangeData> commonBillChangeDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Map<String, String> map : dataList) {
                CommonBillChangeData commonBillChangeData = new CommonBillChangeData();
                commonBillChangeData.setAccountType(accountType);
                commonBillChangeData.setPartition(partition);
                commonBillChangeData.setOffset(offset);
                commonBillChangeData.setBizId(Long.valueOf(map.get("id")));
                commonBillChangeData.setOrderId(map.get("biz_order_id"));
                commonBillChangeData.setAccountId(Long.valueOf(map.get("account_id")));
                commonBillChangeData.setCoinId(Integer.valueOf(map.get("swap_token_id")));
                commonBillChangeData.setBizType(map.get("biz_type"));
                commonBillChangeData.setCreateTime(DateUtil.getMillisecondDate(map.get("biz_time")));
                commonBillChangeData.setBizTime(DateUtil.getMillisecondDate(map.get("biz_time")));
                commonBillChangeData.setBizTimeFromId(DateUtil.getMillisecondDate(map.get("biz_time")));
                // 设置可用变动 和 冻结变动
                BigDecimal beforeBalance = new BigDecimal(map.get("before_balance"));
                BigDecimal balanceChange = new BigDecimal(map.get("balance_change"));
                BigDecimal afterBalance = beforeBalance.add(balanceChange);
                BigDecimal beforeFrozen = new BigDecimal(map.get("before_frozen"));
                BigDecimal frozenChange = new BigDecimal(map.get("frozen_change"));
                BigDecimal afterFrozen = beforeFrozen.add(frozenChange);
                commonBillChangeData.setChangeProp1(balanceChange);
                commonBillChangeData.setChangeProp2(frozenChange);
                commonBillChangeData.setProp1(afterBalance);
                commonBillChangeData.setProp2(afterFrozen);

                commonBillChangeDataList.add(commonBillChangeData);
            }
        }
        return commonBillChangeDataList;
    }
}
