package com.upex.reconciliation.service.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.RateLimiter;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.ReconKafkaOpsConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 限流器相关公式和算法的工具类
 */

@Slf4j
public class RateLimitCalculationUtils {

    public static boolean checkPartitionSpeedAndRateLimit(ApolloReconciliationBizConfig apolloBizConfig, ReconKafkaOpsConfig reconKafkaOpsConfig, Long errorBillMapSize, CommonBillChangeData minErrorMapData, int startTimeSliceMapSize, int saveTimeSliceMapSize, Date checkOkTime, Map<Integer, Long> latestPartitionBizTime, Map<Integer, Long> partitionBizTimeGapMap, Map<Integer, RateLimiter> partitionMap) {
        Map<Integer, Double> limitRateMap = calculateLimitRate(partitionBizTimeGapMap, apolloBizConfig, reconKafkaOpsConfig);
        Integer fastestPartition = getFastestPartition(partitionBizTimeGapMap);
        log.info("RateLimiterManager.refresh get smart speed info accountType= {} ,latestPartitionBizTime={} , partitionBizTimeGapMap={} ,limitRateMap= {}", apolloBizConfig.getAccountType(), JSONObject.toJSONString(latestPartitionBizTime), JSONObject.toJSONString(partitionBizTimeGapMap), JSONObject.toJSONString(limitRateMap));
        // 处理限流
        Double errorLimitRate = null;
        boolean ifAlarm = false;
        boolean limitFastest = false;
        if (startTimeSliceMapSize > apolloBizConfig.getStartTimeSliceMapLimitSize() || errorBillMapSize > apolloBizConfig.getReconKafkaOpsConfig().getErrorBillMapLimitSize()) {
            errorLimitRate = BillConstants.ERROR_LIMIT_RATE;
            ifAlarm = true;
        }
        if (startTimeSliceMapSize > apolloBizConfig.getStartTimeSliceMapLimitSize() * reconKafkaOpsConfig.getFastestPartitionTrafficLimitThreshold()) {
            limitFastest = true;
        }
        for (int i = 0; i < reconKafkaOpsConfig.getKafkaPartitionNum(); i++) {
            RateLimiter rateLimiter = partitionMap.get(i);
            if (rateLimiter == null) {
                rateLimiter = RateLimiter.create(reconKafkaOpsConfig.getMqConsumerRateLimit());
                partitionMap.put(i, rateLimiter);
            } else {
                double partitionLimitRate = limitRateMap.get(i) == null ? reconKafkaOpsConfig.getMqConsumerRateLimit() : limitRateMap.get(i);
                partitionLimitRate = errorLimitRate != null ? errorLimitRate : partitionLimitRate;
                double currentRate = rateLimiter.getRate();
                log.info("RateLimiterManager.refresh info accoutType={} partition={} currentRate={} partitionLimitRate={} startTimeSliceMapSize={} errorBillMapSize={} saveTimeSliceMapSize={} checkTime={}, min errorMap data= {}", apolloBizConfig.getAccountType(), i, currentRate, partitionLimitRate, startTimeSliceMapSize, errorBillMapSize, saveTimeSliceMapSize, checkOkTime, JSONObject.toJSONString(minErrorMapData));
                if (currentRate != partitionLimitRate) {
                    rateLimiter.setRate(partitionLimitRate);
                }
            }
        }
        if (limitFastest) {
            RateLimiter rateLimiter = partitionMap.get(fastestPartition);
            if (rateLimiter != null) {
                rateLimiter.setRate(reconKafkaOpsConfig.getFastestPartitionTrafficLimitRate());
            }
        }
        return ifAlarm;

    }


    /**
     * 所有partition加总后等于总速度
     * bizTime过于靠前的partition，分得的速度越少，
     * bizTime落后的partition，分得的速度越多
     * mqConsumerRateCalFactor/(mqConsumerRateCalFactor + 快了多少分钟)  = 最终分得的速率比值
     *
     * @param partitionBizTimeGapMap 每个partition相比最慢partition，快了多少分钟，key时partition编号，value是快了多少分钟
     * @param apolloBizConfig
     * @return
     */
    public static Map<Integer, Double> calculateLimitRate(Map<Integer, Long> partitionBizTimeGapMap, ApolloReconciliationBizConfig apolloBizConfig, ReconKafkaOpsConfig reconKafkaOpsConfig) {
        Map<Integer, Double> limitRateMap = new HashMap<>();
        if (partitionBizTimeGapMap.size() == reconKafkaOpsConfig.getKafkaPartitionNum()) {
            double total = 0;
            for (Integer partition : partitionBizTimeGapMap.keySet()) {
                double factor = reconKafkaOpsConfig.getMqConsumerRateCalFactor() / (reconKafkaOpsConfig.getMqConsumerRateCalFactor() + partitionBizTimeGapMap.get(partition));
                limitRateMap.put(partition, factor);
                total += factor;
            }
            for (Integer partition : partitionBizTimeGapMap.keySet()) {
                double factor = limitRateMap.get(partition);
                limitRateMap.put(partition, reconKafkaOpsConfig.getMqConsumerRateLimit() * reconKafkaOpsConfig.getKafkaPartitionNum() * factor / total);
            }
        }
        return limitRateMap;
    }


    public static Long getMinPartitionBizTime(Map<Integer, Long> partitionBizTimeGapMap) {
        return partitionBizTimeGapMap.values().stream().min(Comparator.comparing(x -> x)).orElse(null);
    }


    public static Integer getFastestPartition(Map<Integer, Long> partitionBizTimeGapMap) {
        Optional<Map.Entry<Integer, Long>> maxEntry = partitionBizTimeGapMap.entrySet().stream()
                .max(Comparator.comparingLong(Map.Entry::getValue));
        // 处理结果
        if (maxEntry.isPresent()) {
            Map.Entry<Integer, Long> entry = maxEntry.get();
            return entry.getKey();
        } else {
            return 0;
        }
    }
}
