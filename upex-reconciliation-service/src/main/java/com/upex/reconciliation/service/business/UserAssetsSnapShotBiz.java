package com.upex.reconciliation.service.business;

import com.upex.bill.dto.params.UserTypeChangeParams;
import com.upex.bill.dto.results.ContractTradingVo;
import com.upex.reconciliation.facade.dto.results.*;
import com.upex.reconciliation.facade.params.*;
import com.upex.ticker.facade.dto.PriceVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/10/26 下午7:12
 * @Description
 */
public interface UserAssetsSnapShotBiz {

    /**
     * 查询用户出入金
     * @param reconUserTypeChangeParams
     * @return
     */
    List<ReconUserOutInBalanceVo> selectOutInBalance(ReconUserTypeChangeParams reconUserTypeChangeParams);

    /**
     * 用户出入金详情
     * @param reconUserTypeChangeParams
     * @return
     */
    ReconBillMessagePage<ReconUserOutInBalanceDetailsVo> selectDetails(ReconUserTypeChangeParams reconUserTypeChangeParams);

    /**
     * 其他收入
     * @param reconUserTypeChangeParams
     * @return
     */
    List<ReconUserOutInBalanceDetailsVo> selectOtherIncome(ReconUserTypeChangeParams reconUserTypeChangeParams);

    /**
     * 最后快照时间
     * @param reconUserTypeChangeParams
     * @return
     */
    String selectFinalSnapShotTime(ReconUserTypeChangeParams reconUserTypeChangeParams);

    /**
     * 根据用户当前时间查询不同业务系统用户资产快照
     * @param reconUserAssetsSnapShotParams
     * @return
     */
    ReconBillMessagePage<ReconTotalBalanceVo> selectCurrentSysAssetsSnapShotByTime(ReconUserAssetsSnapShotParams reconUserAssetsSnapShotParams);



    /**
     * 查询用户当前账户情况各系统资产总和
     * @param reconUserAssetsSnapShotParams
     * @return
     */
    ReconUserAssetsVo selectUserAssets(ReconUserAssetsSnapShotParams reconUserAssetsSnapShotParams);

    /**
     * 查询币币系统交易盈亏情况
     * @param reconUserTypeChangeParams
     * @return
     */
    List<ReconBbTradingVo> selectBbTrading(ReconUserTypeChangeParams reconUserTypeChangeParams);


    /**
     * 用户交易盈亏情况盈利额总额
     * @param reconUserTypeChangeParams
     * @return
     */
    ReconUserTradingVo selectUserProfitLossTotal(ReconUserTypeChangeParams reconUserTypeChangeParams);


    /**
     * 获取用户特定时间的资产详情
     *
     * @param userId       用户id
     * @param snapShotTime 特定时间
     * @param allCoinsMap
     * @param rates
     * @return
     */
    ReconTotalAssetsDetailVo listUserAssetsBySnapShotTime(Long userId, Long snapShotTime, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates) throws Exception;


    ReconTotalAssetsDetailVo listUserAssetsBySnapShotTime(Long userId, Long snapShotTime, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates, List<String> subSystemList, Set<Integer> hasPositionCoinList) throws Exception;

    ReconTotalAssetsDetailVo listUserAssetsBySnapShotTime(Long userId, Long snapShotTime, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates, List<String> subSystemList, Set<Integer> hasPositionCoinList, Boolean isMaster) throws Exception;




    /**
     * 查询用户特定时间的资产列表
     *
     * @param userIds      用户id集合
     * @param snapShotTime 特定时间
     * @param isMaster
     * @return
     */
    Map<Long, ReconTotalAssetsDetailVo> batchUserAssetsBySnapShotTime(List<Long> userIds, Long snapShotTime, Boolean isMaster);

    /**
     * 获取用户特定时间的资产详情
     *
     * @param reconUserAssetsParam 用户资产查询参数
     * @return {@link List<  ReconUserAssetsResult > }
     * <AUTHOR>
     * @date 2022/12/16 15:22
     */
    List<ReconUserAssetsResult> getUserAssetsByCoinAndTime(ReconUserAssetsParam reconUserAssetsParam);



    /**
     * 查询u合约和币本位合约盈亏
     * @param userTypeChangeParams
     * @return
     */
    List<ReconContractTradingVo> selectContract(ReconUserTypeChangeParams userTypeChangeParams);


}
