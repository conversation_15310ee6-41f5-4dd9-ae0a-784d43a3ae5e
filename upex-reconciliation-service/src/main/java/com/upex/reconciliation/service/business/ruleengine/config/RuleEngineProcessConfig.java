package com.upex.reconciliation.service.business.ruleengine.config;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * 规则引擎配置
 */
@Data
public class RuleEngineProcessConfig {
    /***流程开关***/
    private Boolean isOpen;
    /***流程日志开关***/
    private Boolean logShowOpen = false;
    /***流程code***/
    private String processCode;
    /***流程名称***/
    private String processName;
    /***流程扩展配置***/
    private JSONObject extensionConfig = new JSONObject();
    /***流程执行类型 @See ExecuteTypeEnum***/
    private String executeType;
    /***流程数据加载配置***/
    private String dataLoaderRule;
    /***流程数据校验配置***/
    private String dataCheckerRule;
    /***流程数据结果处理配置***/
    private String dataHandlerRule;
}
