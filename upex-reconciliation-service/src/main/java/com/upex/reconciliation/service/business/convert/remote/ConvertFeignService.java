//package com.upex.reconciliation.service.business.convert.remote;
//
//import com.upex.convert.facade.feign.inner.InnerConvertOrderFeignClient;
//import org.springframework.cloud.openfeign.FeignClient;
//
//@FeignClient(
//        value = "${upex.feignClient.convertrest}",
//        contextId = "InnerConvertOrderFeignClient",
//        path = "/inner/v1/convert",
//        primary = false,
//        url = "https://localhost:8080"
//)
//public interface ConvertFeignService extends InnerConvertOrderFeignClient {
//
//}
