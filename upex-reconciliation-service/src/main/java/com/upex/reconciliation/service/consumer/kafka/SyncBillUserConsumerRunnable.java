package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.fastjson.JSON;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.entity.BillUser;
import com.upex.reconciliation.service.model.dto.SyncBillUserDTO;
import com.upex.reconciliation.service.service.BillUserService;
import com.upex.reconciliation.service.utils.EnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.*;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.KAFKA_CONSUMER_TOPIC_ERROR;

/**
 * 增量用户同步
 */
@Slf4j
public class SyncBillUserConsumerRunnable implements KafkaConsumerLifecycle {
    private volatile boolean running = true;
    private Map<String, Object> consumerConfig;
    private String groupId;
    private Set<Integer> closeConsumerPatition = new HashSet<>();

    private AlarmNotifyService alarmNotifyService;
    private KafkaConsumer consumer;
    private ReconciliationSpringContext context;
    private BillUserService billUserService;
    private String topic = KafkaTopicEnum.RECON_USER_INCREMENT_SYNC_TOPIC.getCode();

    public SyncBillUserConsumerRunnable(ReconciliationSpringContext context, String kafkaServers, String groupId, Integer maxPollSiz) {
        this.context = context;
        this.billUserService = context.getBillUserService();
        this.groupId = EnvUtil.getKafkaConsumerGroup(groupId);
        alarmNotifyService = context.getAlarmNotifyService();
        consumerConfig = new HashMap<String, Object>();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServers);
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, this.groupId);
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollSiz);
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
    }

    @Override
    public void shutdown() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return closeConsumerPatition.size() != 1;
    }

    @Override
    public String getThreadPrefixName() {
        return "kafka-consumer-thread-sync-bill-user";
    }

    @Override
    public void run() {
        consumer = new KafkaConsumer<String, String>(consumerConfig);
        consumer.subscribe(Arrays.asList(topic));
        log.info("SyncBillUserConsumerRunnable run 。。。");
        while (running) {
            try {
                // 从kafka集群中拉取消息df
                ConsumerRecords<String, String> consumerRecords = consumer.poll(3000);
                List<SyncBillUserDTO> syncBillUserDTOS = new ArrayList<>();
                consumerRecords.forEach(new java.util.function.Consumer<ConsumerRecord<String, String>>() {
                    @Override
                    public void accept(ConsumerRecord<String, String> consumerRecord) {
                        if (!consumerRecord.topic().equals(topic)) {
                            alarmNotifyService.alarm(KAFKA_CONSUMER_TOPIC_ERROR, "bill-user", topic, consumerRecord.topic(), consumerRecord.partition());
                        }
                        // log.info("接收到消息：{}", consumerRecord.value());
                        SyncBillUserDTO syncBillUserDTO = JSON.parseObject(consumerRecord.value(), SyncBillUserDTO.class);
                        syncBillUserDTOS.add(syncBillUserDTO);
                    }
                });
                log.info("接收到消息数量：{}", syncBillUserDTOS.size());
                // 保存billUser
                saveBillUser(syncBillUserDTOS);
                // 手动提交位点
                consumer.commitSync();
            } catch (Exception e) {
                log.error("SyncBillUserConsumerRunnable startConsume error ", e);
            }
        }
        consumer.close();
        closeConsumerPatition.add(0);
        log.info("SyncBillUserConsumerRunnable consumer.close success {} {}");
    }

    /**
     * 批量保存数据
     *
     * @param
     */
    private void saveBillUser(List<SyncBillUserDTO> syncBillUserDTOS) {
        if (CollectionUtils.isEmpty(syncBillUserDTOS)) {
            return;
        }
        Date nowDate = new Date();
        List<Long> userIds = syncBillUserDTOS.stream().map(SyncBillUserDTO::getUserId).collect(Collectors.toList());
        List<BillUser> billUsers = billUserService.selectByIds(userIds);
        Map<Long, BillUser> existUserMap = billUsers.stream().collect(Collectors.toMap(BillUser::getId, item -> item));
        // 设置更新标记
        Map<Long, BillUser> billUserMap = new HashMap<>();
        for (SyncBillUserDTO syncBillUserDTO : syncBillUserDTOS) {
            BillUser billUser = billUserMap.get(syncBillUserDTO.getUserId());
            if (billUser == null) {
                billUser = existUserMap.get(syncBillUserDTO.getUserId());
                if (billUser == null) {
                    billUser = new BillUser();
                    billUser.setId(syncBillUserDTO.getUserId());
                    billUser.setCreateTime(nowDate);
                }
            }
            billUser.setUpdateTime(nowDate);
            if (billUser.getParentId() == null && syncBillUserDTO.getParentId() != null) {
                billUser.setParentId(syncBillUserDTO.getParentId());
            }
            boolean result = billUser.setStatusAndRegTimeByAccountType(syncBillUserDTO.getAccountType(), syncBillUserDTO.getRegisterTime());
            if (result) {
                billUserMap.put(syncBillUserDTO.getUserId(), billUser);
            }
        }
        //组装批量插入队列
        List<BillUser> insertBillUserList = new ArrayList<>();
        List<BillUser> updateBillUserList = new ArrayList<>();
        for (BillUser billUser : billUserMap.values()) {
            if (existUserMap.containsKey(billUser.getId())) {
                updateBillUserList.add(billUser);
            } else {
                insertBillUserList.add(billUser);
            }
        }
        billUserService.batchInsert(insertBillUserList);
        billUserService.batchUpdate(updateBillUserList);
    }
}


