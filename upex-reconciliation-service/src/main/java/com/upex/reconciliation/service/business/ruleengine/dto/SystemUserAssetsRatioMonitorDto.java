package com.upex.reconciliation.service.business.ruleengine.dto;

import com.upex.reconciliation.service.common.constants.BillConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SystemUserAssetsRatioMonitorDto {
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 开始时间
     */
    private String  startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 期初资产
     */
    private BigDecimal beginAmount;
    /**
     * 期末资产
     */
    private BigDecimal endAmount;
    /**
     * 折u比率
     */
    private BigDecimal ratio;

    public BigDecimal calculateRatio() {
        if (beginAmount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return endAmount.divide(beginAmount, BillConstants.FOUR, BigDecimal.ROUND_HALF_UP);
    }
}
