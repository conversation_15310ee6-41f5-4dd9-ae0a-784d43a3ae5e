package com.upex.reconciliation.service.business.profit;

import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;

/**
 * 盈利检测灰度服务
 *
 * <AUTHOR>
 * @Date 2025/4/25
 */
public interface CheckProfitGrayService {

    /**
     * 灰度命中
     *
     * @param checkResultsParams
     * @param billConfig
     * @return
     */
    boolean match(ReconCheckResultsParams checkResultsParams, GlobalBillConfig billConfig);
}
