package com.upex.reconciliation.service.utils;

import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

public class LogPrintUtils {



    public static String getBillCoinUserPropertyListPropStr(List<BillCoinUserProperty> propertyList) {
        if (CollectionUtils.isEmpty(propertyList)) {
            return "";
        }
        StringBuffer stringBuffer = new StringBuffer();
        for(BillCoinUserProperty property:propertyList){
            stringBuffer.append(getBillCoinUserPropertyPropStr(property)).append(".  ");
        }
        return stringBuffer.toString();
    }
    public static String getBillCoinUserPropertyPropStr(BillCoinUserProperty property) {
        if (property == null) {
            return "";
        }
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(" checkTime:").append(DateUtil.getSimpleDateStr(property.getCheckTime()))
                .append(" coinId:").append(property.getCoinId())
                .append(" prop1:").append(property.getProp1())
                .append(" prop2:").append(property.getProp2())
                .append(" prop3:").append(property.getProp3())
                .append(" prop4:").append(property.getProp4())
                .append(" prop5:").append(property.getProp5());
        return stringBuffer.toString();
    }

}
