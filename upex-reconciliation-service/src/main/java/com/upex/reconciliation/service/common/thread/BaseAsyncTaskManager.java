package com.upex.reconciliation.service.common.thread;

import com.upex.utils.executor.TracedThreadPoolExecutor;
import com.upex.utils.thread.ThreadFactoryBuilder;

import java.util.concurrent.*;

public class BaseAsyncTaskManager {

    private static final int MAX_POOL_SIZE = 200;
    private static final int CORE_POOL_SIZE = 50;
    private static final int QUEUE_CAPACITY = 500;
    private static final int KEEP_ALIVE_SECONDS = 20;

    //以秒为时间单位
    private TimeUnit unit = TimeUnit.SECONDS;

    private ThreadPoolExecutor threadPoolExecutor = null;

    public BaseAsyncTaskManager(String nameSuffix, int corePoolSize, int maxPoolSize, int workQueueSize) {
        //创建线程池
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNamePrefix(nameSuffix).build();
        threadPoolExecutor = new TracedThreadPoolExecutor(corePoolSize,
                maxPoolSize,
                KEEP_ALIVE_SECONDS,
                unit,
                new ArrayBlockingQueue<>(workQueueSize),
                namedThreadFactory,
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    public void submit(Runnable task) {
        threadPoolExecutor.submit(task);
    }

    public <T> Future<T> submit(Callable<T> task){
        return threadPoolExecutor.submit(task);
    }


    public ThreadPoolExecutor getThreadPoolExecutor() {
        return threadPoolExecutor;
    }
}
