package com.upex.reconciliation.service.model;

import com.upex.reconciliation.service.model.config.MonitorSceneTaskConfig;
import com.upex.reconciliation.service.model.enums.MonitorCmdEnum;
import com.upex.reconciliation.service.model.param.BillFlowMonitorData;
import com.upex.reconciliation.service.model.param.BillMonitorData;
import com.upex.reconciliation.service.model.param.MonitorIncomeAggregationData;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/16 15:46
 */
@Data
@AllArgsConstructor
public class MonitorCmdWrapper {
    /**
     * 规则配置
     */
    private final MonitorSceneTaskConfig taskConfig;

    /**
     * flow类型的，使用flow的数据
     * 聚合类型时，使用聚合后的数据
     */
    private MonitorCmdEnum monitorCmdEnum;

    /**
     * 数据
     */
    private final BillFlowMonitorData monitorData;


    private final BillMonitorData aggregationMonitorData;

    private Date bizTime;

}
