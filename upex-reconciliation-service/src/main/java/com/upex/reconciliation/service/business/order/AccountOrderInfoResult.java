package com.upex.reconciliation.service.business.order;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单接入返回结果
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AccountOrderInfoResult implements Serializable {
    private static final long serialVersionUID = 5789412675627728054L;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 账单流水类型
     */
    private String bizType;

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 下游订单id
     */
    private Long subOrderId;

    /**
     * 币种ID
     */
    private Integer coinId;

    /**
     * 订单金额
     */
    private BigDecimal prop1 = BigDecimal.ZERO;

    /**
     * lock
     */
    private BigDecimal prop2 = BigDecimal.ZERO;

    /**
     * frozen
     */
    private BigDecimal prop3 = BigDecimal.ZERO;

    /**
     * longFrozen
     */
    private BigDecimal prop4 = BigDecimal.ZERO;

    /**
     * shortFrozen
     */
    private BigDecimal prop5 = BigDecimal.ZERO;

    /**
     * 备用参数
     */
    private String params;
    /**
     * 开始时间
     */
    private long beginTime;
    /**
     * 结束时间
     */
    private long endTime;
    /**
     * 业务时间
     */
    private long bizTime;

    /**
     * 动账时间
     */
    private long transferTime;


    public BigDecimal getPropSum() {
        BigDecimal propSum = BigDecimal.ZERO;
        if (prop1 != null) {
            propSum = propSum.add(prop1);
        }
        if (prop2 != null) {
            propSum = propSum.add(prop2);
        }
        if (prop3 != null) {
            propSum = propSum.add(prop3);
        }
        if (prop4 != null) {
            propSum = propSum.add(prop4);
        }
        if (prop5 != null) {
            propSum = propSum.add(prop5);
        }
        return propSum;
    }

    /**
     * 判断资产是否有负数
     * @return
     */
    public boolean isNegativeProp() {
        if (prop1 != null && prop1.compareTo(BigDecimal.ZERO) < 0) {
            return true;
        }
        if (prop2 != null && prop2.compareTo(BigDecimal.ZERO) < 0) {
            return true;
        }
        if (prop3 != null && prop3.compareTo(BigDecimal.ZERO) < 0) {
            return true;
        }
        if (prop4 != null && prop4.compareTo(BigDecimal.ZERO) < 0) {
            return true;
        }
        if (prop5 != null && prop5.compareTo(BigDecimal.ZERO) < 0) {
            return true;
        }
        return false;
    }


    /**
     * 判断资产是否有正数
     * @return
     */
    public boolean isPositiveProp() {
        if (prop1 != null && prop1.compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        if (prop2 != null && prop2.compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        if (prop3 != null && prop3.compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        if (prop4 != null && prop4.compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        if (prop5 != null && prop5.compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        return false;
    }

    /**
     * 所有资产是否为0
     *
     * @return
     */
    public boolean allPropZero() {
        if (prop1 != null && prop1.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (prop2 != null && prop2.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (prop3 != null && prop3.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (prop4 != null && prop4.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (prop5 != null && prop5.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        return true;
    }

    /**
     * 设置所有资产是否为0
     * @return
     */
    public void setPropZero() {
        this.prop1 = BigDecimal.ZERO;
        this.prop2 = BigDecimal.ZERO;
        this.prop3 = BigDecimal.ZERO;
        this.prop4 = BigDecimal.ZERO;
        this.prop5 = BigDecimal.ZERO;
    }

    /**
     * 获取唯一名称，发送报警使用
     * @return
     */
    public String getUniqueName() {
        return this.getBizType()+ "#"+this.getCoinId();
    }

    @Override
    public String toString() {
        return "AccountOrderInfoResult{" +
                "userId=" + userId +
                ", bizType='" + bizType + '\'' +
                ", coinId=" + coinId +
                ", prop1=" + prop1 +
                ", prop2=" + prop2 +
                ", prop3=" + prop3 +
                ", prop4=" + prop4 +
                ", prop5=" + prop5 +
                ", params='" + params + '\'' +
                ", beginTime=" + beginTime +
                ", endTime=" + endTime +
                '}';
    }
}
