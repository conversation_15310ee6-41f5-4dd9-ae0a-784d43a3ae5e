package com.upex.reconciliation.service.service;


import com.upex.reconciliation.service.dao.entity.OrderBillConfig;

import java.util.List;

public interface OrderBillConfigService {

    /**
     * 获取配置
     * @param orderType
     * @param orderParam
     * @return
     */
    OrderBillConfig getOrderBillConfig(String orderType, String orderParam);

    /**
     * 获取orderBillConfig 配置列表
     * @param currentTimeMillis
     * @return
     */
    List<OrderBillConfig> getOrderBillConfigList(Long currentTimeMillis);

    /**
     * 更新配置
     * @param orderBillConfig
     */
    void updateOrderBillConfig(OrderBillConfig orderBillConfig);

    /**
     * 订单对账
     * @param jobParam
     */
    void repairOrderBillCheck(String jobParam);
}
