package com.upex.reconciliation.service.service;

import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.service.common.constants.enums.CapitalInitBusinessTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillCapitalInitProperty;
import com.upex.reconciliation.service.dao.entity.BillSymbolTradingConfig;
import com.upex.reconciliation.service.dao.mapper.BillSymbolTradingConfigMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BillSymbolTradingConfigService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billSymbolTradingConfigMapper")
    private BillSymbolTradingConfigMapper billSymbolTradingConfigMapper;
    @Resource
    private BillCapitalInitPropertyService billCapitalInitPropertyService;

    /**
     * 批量插入数据
     *
     * @param list
     * <AUTHOR>
     * @date 2022/12/9 21:13
     */
    public void batchInsert(List<BillSymbolTradingConfig> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            billSymbolTradingConfigMapper.batchInsert(list);
        }
    }

    /**
     * 查询配置列表
     *
     * @param accountType
     * @param accountParam
     * <AUTHOR>
     * @date 2022/12/9 21:14
     */
    public List<BillSymbolTradingConfig> listBillSymbolTradingConfigs(Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolTradingConfigMapper.selectBillSymbolTradingConfig(accountType, accountParam));
    }


    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolTradingConfigMapper.deleteByCheckTime(accountType, accountParam, checkTime));
    }

    public Boolean batchDelete(Long beginId, Long pageSize, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolTradingConfigMapper.batchDelete(beginId, pageSize, accountType, accountParam));
    }

    /**
     * 查询指定时间初始配置返回Map类型
     *
     * @param accountType
     * @param accountParam
     * <AUTHOR>
     * @date 2022/12/8 22:03
     */
    public Map<String, BillSymbolTradingConfig> getBillSymbolTradingConfigMap(Byte accountType, String accountParam) {
        List<BillSymbolTradingConfig> billSymbolTradingConfigs = listBillSymbolTradingConfigs(accountType, accountParam);
        return CollectionUtils.emptyIfNull(billSymbolTradingConfigs).stream()
                .collect(Collectors.toMap(BillSymbolTradingConfig::getSymbolId, Function.identity(), (key1, key2) -> key2));
    }

    public List<BillSymbolTradingConfig> listAll() {
        return dbHelper.doDbOpInReconMaster(() -> billSymbolTradingConfigMapper.listAll());
    }

    /**
     * @param jobParam
     */
    public void moveTradingConfigToCapitalInit(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        String action = jsonObject.getString("action");
        List<BillSymbolTradingConfig> billSymbolTradingConfigList = listAll();
        List<BillCapitalInitProperty> billCapitalInitPropertyList = new ArrayList<>();
        for (BillSymbolTradingConfig billSymbolTradingConfig : billSymbolTradingConfigList) {
            BillCapitalInitProperty billCapitalInitProperty = new BillCapitalInitProperty();
            billCapitalInitProperty.setAccountType(String.valueOf(billSymbolTradingConfig.getAccountType()));
            billCapitalInitProperty.setAccountParam(billSymbolTradingConfig.getAccountParam());
            billCapitalInitProperty.setCoinId(0);
            billCapitalInitProperty.setSymbolId(billSymbolTradingConfig.getSymbolId());
            billCapitalInitProperty.setInitValue(billSymbolTradingConfig.getInitValue());
            billCapitalInitProperty.setBusinessType(CapitalInitBusinessTypeEnum.CONTRACT_SYMBOL_REALIZED.getCode());
            billCapitalInitProperty.setBusinessTypeName(CapitalInitBusinessTypeEnum.CONTRACT_SYMBOL_REALIZED.getDesc());
            billCapitalInitProperty.setCheckTime(billSymbolTradingConfig.getCheckOkTime());
            billCapitalInitProperty.setCreateTime(billSymbolTradingConfig.getCreateTime());
            billCapitalInitProperty.setUpdateTime(billSymbolTradingConfig.getUpdateTime());
            billCapitalInitPropertyList.add(billCapitalInitProperty);
        }
        billCapitalInitPropertyService.batchInsert(billCapitalInitPropertyList);
    }
}