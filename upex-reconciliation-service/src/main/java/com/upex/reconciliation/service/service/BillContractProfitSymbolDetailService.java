package com.upex.reconciliation.service.service;


import com.upex.reconciliation.service.dao.entity.BillContractProfitSymbolDetail;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_coin_detail(币种维度盈亏明细表)】的数据库操作Service
 * @createDate 2023-06-09 17:18:46
 */
public interface BillContractProfitSymbolDetailService {

    /**
     * 批量插入数据
     *
     * @param accountType
     * @param accountParam
     * @param billContractProfitCoinDetailList
     * @return
     */
    int batchInsert(Byte accountType, String accountParam, List<BillContractProfitSymbolDetail> billContractProfitCoinDetailList);

    /**
     * 批量删除数据
     * @param accountType
     * @param accountParam
     * @param checkTime
     */
    int deleteByCheckTime(Byte accountType, String accountParam, Date checkTime);

    /**
     * 批量删除
     * @param minId
     * @param deleteSize
     * @param accountType
     * @param accountParam
     * @return
     */
    Boolean batchDelete(Long minId, Long deleteSize, Byte accountType, String accountParam);

    List<BillContractProfitSymbolDetail> selectListByAccountTypeAndCheckTime(Byte accountType, String accountParam, Date checkTime);
}
