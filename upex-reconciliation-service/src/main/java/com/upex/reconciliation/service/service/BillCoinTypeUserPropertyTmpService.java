package com.upex.reconciliation.service.service;

import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeUserPropertyTmp;
import com.upex.reconciliation.service.dao.mapper.BillCoinTypeUserPropertyTmpMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.utils.BeanCopierUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class BillCoinTypeUserPropertyTmpService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billCoinTypeUserPropertyTmpMapper")
    private BillCoinTypeUserPropertyTmpMapper billCoinTypeUserPropertyTmpMapper;
    @Resource
    private BillCoinTypeUserPropertyService billCoinTypeUserPropertyService;

    public int batchInsert(List<BillCoinTypeUserPropertyTmp> records) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyTmpMapper.batchInsert(records));
    }

    public Boolean deleteByUserId(Byte accountType, String accountParam, Long userId, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinTypeUserPropertyTmpMapper.deleteByUserId(accountType, accountParam, userId, batchSize));
    }

    public void repairCoinTypeUserPropertyTmp(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        String action = jsonObject.getString("action");
        Byte accountType = jsonObject.getByte("accountType");
        Long userId = jsonObject.getLong("userId");
        Long checkTime = jsonObject.getLong("checkTime");
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        if ("delete".equals(action)) {
            if (accountType != null && userId != null && checkTime != null) {
                Long pageSize = 500L;
                while (true) {
                    Boolean result = this.deleteByUserId(accountType, accountTypeEnum.getAccountParam(), userId, pageSize);
                    if (!result) {
                        break;
                    }
                }
            }
        } else if ("insert".equals(action)) {
            if (accountType != null && userId != null && checkTime != null) {
                Integer pageSize = 500;
                Long startId = 0L;
                while (true) {
                    List<BillCoinTypeUserProperty> billCoinTypeUserProperties = billCoinTypeUserPropertyService.selectByUserIdAndGtCheckTime(accountType, new Date(checkTime), startId, userId, pageSize);
                    if (CollectionUtils.isEmpty(billCoinTypeUserProperties)) {
                        break;
                    }
                    startId = billCoinTypeUserProperties.get(billCoinTypeUserProperties.size() - 1).getId();
                    List<BillCoinTypeUserPropertyTmp> billCoinTypeUserPropertyTmpList = new ArrayList<>();
                    billCoinTypeUserProperties.forEach(item -> {
                        BillCoinTypeUserPropertyTmp billCoinTypeUserPropertyTmp = BeanCopierUtil.copyProperties(item, BillCoinTypeUserPropertyTmp.class);
                        billCoinTypeUserPropertyTmp.setAccountType(accountType);
                        billCoinTypeUserPropertyTmp.setAccountParam(accountTypeEnum.getAccountParam());
                        billCoinTypeUserPropertyTmpList.add(billCoinTypeUserPropertyTmp);
                    });
                    this.batchInsert(billCoinTypeUserPropertyTmpList);
                }
            }
        }
    }
}
