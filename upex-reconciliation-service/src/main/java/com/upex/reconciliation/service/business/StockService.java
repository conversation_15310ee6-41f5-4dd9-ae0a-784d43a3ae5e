package com.upex.reconciliation.service.business;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.TimeoutUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 * 扣库存
 */
@Slf4j
@Service
public class StockService {

    /**
     * 库存还未初始化
     */
    public static final long UNINITIALIZED_STOCK = -2L;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private RedissonClient redissonClient;
    /**
     * 执行扣库存的脚本
     */
    public static final String REDUCE_STOCK_LUA;

    static {
        /*
         * @desc 扣减库存Lua脚本
         * @params 库存key
         * @return
         *      -2:库存未初始化
         * 		-1:库存不足
         * 		大于等于0:剩余库存（扣减之后剩余的库存）,直接返回
         */
        StringBuilder reduceString = new StringBuilder();
        reduceString.append("if (redis.call('exists', KEYS[1]) == 1) then");
        reduceString.append("    local current_value = tonumber(redis.call('get', KEYS[1]));");
        reduceString.append("    local input_number = tonumber(ARGV[1]);");
        reduceString.append("    local expire_time = tonumber(ARGV[2]);");
        reduceString.append("    local input_order_number = tonumber(ARGV[3]);");
        reduceString.append("    if (redis.call('exists', KEYS[2]) == 1) then");
        reduceString.append("        local isMember = redis.call('SISMEMBER', KEYS[2], input_order_number)");
        reduceString.append("        if isMember == 1 then");
        reduceString.append("            return current_value;");
        reduceString.append("        end;");
        reduceString.append("    end;");
        reduceString.append("    if (current_value >= input_number) then");
        reduceString.append("        local new_value = current_value - input_number;");
        reduceString.append("        redis.call('SET', KEYS[1], tostring(new_value));");
        reduceString.append("        if expire_time > 0 then");
        reduceString.append("            redis.call('EXPIRE', KEYS[1], expire_time);");
        reduceString.append("        end;");
        reduceString.append("        redis.call('SADD', KEYS[2], input_order_number);");
        reduceString.append("        return new_value;");
        reduceString.append("    end;");
        reduceString.append("    return -1;");
        reduceString.append("end;");
        reduceString.append("return -2;");
        REDUCE_STOCK_LUA = reduceString.toString();
    }

    /**
     * @param key             库存key
     * @param redisOrderNoKey redis订单号key，如果扣除库存成功，缓存订单号
     * @param expire          库存有效时间,单位分钟
     * @param num             扣减数量
     * @param initStock       初始化库存
     * @param orderNumber     订单号
     * @param redisLockKey    扣库存锁key
     * @return -1:库存不足; 大于等于0:扣减库存之后的剩余库存
     */
    public long subtractStock(String key, String redisOrderNoKey, long expire, BigDecimal num, BigDecimal initStock, Long orderNumber, String redisLockKey) {
        long expireSeconds = TimeoutUtils.toSeconds(expire, TimeUnit.MINUTES);
        long stock = stock(key, redisOrderNoKey, num, expireSeconds, orderNumber);
        // 初始化库存
        if (stock == UNINITIALIZED_STOCK) {
            Lock lock = redissonClient.getLock(redisLockKey);
            try {
                // 获取锁
                if (lock.tryLock()) {
                    // 双重验证，避免并发时重复回源到数据库
                    stock = stock(key, redisOrderNoKey, num, expireSeconds, orderNumber);
                    if (stock == UNINITIALIZED_STOCK) {
                        // 将库存设置到redis
                        redisTemplate.opsForValue().set(key, initStock.toPlainString(), expire, TimeUnit.MINUTES);
                        // 调一次扣库存的操作
                        stock = stock(key, redisOrderNoKey, num, expireSeconds, orderNumber);
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                lock.unlock();
            }

        }
        return stock;
    }

    /**
     * 扣库存
     *
     * @param key             库存key
     * @param redisOrderNoKey redis订单号key，如果扣除库存成功，缓存订单号
     * @param num             扣减库存数量
     * @param expireSeconds   库存有效时间（秒）
     * @param orderNumber     订单号
     * @return 扣减之后剩余的库存【-2:库存未初始化; -1:库存不足; 大于等于0:扣减库存之后的剩余库存】
     */
    private Long stock(String key, String redisOrderNoKey, BigDecimal num, long expireSeconds, Long orderNumber) {
        return redisTemplate.execute((RedisCallback<Long>) connection -> connection.eval(REDUCE_STOCK_LUA.getBytes(), ReturnType.INTEGER, 2, key.getBytes(), redisOrderNoKey.getBytes(), num.toPlainString().getBytes(), Long.toString(expireSeconds).getBytes(), Long.toString(orderNumber).getBytes()));
    }

    /**
     * 加库存
     *
     * @param key          库存key
     * @param expire       过期时间（秒）
     * @param num          库存数量
     * @param redisLockKey 加库存锁key
     * @return
     */
    public long addStock(String key, Long expire, int num, String redisLockKey) {
        boolean hasKey = Boolean.TRUE.equals(redisTemplate.hasKey(key));
        // 判断key是否存在，存在就直接更新
        if (hasKey) {
            return redisTemplate.opsForValue().increment(key, num);
        }

        Assert.notNull(expire, "初始化库存失败，库存过期时间不能为null");
        Lock redisLock = redissonClient.getLock(redisLockKey);
        try {
            if (redisLock.tryLock()) {
                // 获取到锁后再次判断一下是否有key
                hasKey = redisTemplate.hasKey(key);
                if (!hasKey) {
                    // 初始化库存
                    redisTemplate.opsForValue().set(key, Integer.toString(num), expire, TimeUnit.MINUTES);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisLock.unlock();
        }

        return num;
    }

    /**
     * 获取库存
     *
     * @param key 库存key
     * @return -1:不限库存; 大于等于0:剩余库存
     */
    public int getStock(String key) {
        Integer stock = (Integer) redisTemplate.opsForValue().get(key);
        return stock == null ? -1 : stock;
    }

    /**
     * 删除库存
     *
     * @param key 库存key
     */
    public int removeStock(String key) {
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            try {
                redisTemplate.delete(key);
            } catch (Exception e) {
                log.error("Redis delete key error, key:{},", key, e);
            }
        }
        return 0;
    }

}