package com.upex.reconciliation.service.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillCapitalOrder;
import com.upex.reconciliation.service.model.config.UserPermissionConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.service.PermissionRouteRuleService;
import com.upex.spot.dto.enums.SourceEnum;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * API提现权限路由服务实现类
 *
 * <AUTHOR>
 * @Date 2025/1/26
 */
@Service
public class OpenApiWithdrawPermissionRouteRuleServiceImpl implements PermissionRouteRuleService {

    @Resource
    private CapitalOrderService capitalOrderService;

    @Override
    public Pair<Boolean, String> isSelect(CommonBillChangeData billChangeData, UserPermissionConfig userPermissionConfig) {
        Pair<Boolean, String> pair = Pair.of(Boolean.FALSE, "");
        if (billChangeData.getAccountType() != AccountTypeEnum.SPOT.getCode() || !NumberUtil.isNumber(billChangeData.getOrderId())) {
            return pair;
        }
        BillCapitalOrder order = capitalOrderService.getCapitalOrderInfo(Long.valueOf(billChangeData.getOrderId()));
        if (Objects.isNull(order) || Objects.isNull(order.getSource())) {
            return pair;
        }
        SourceEnum sourceEnum = SourceEnum.toEnum(order.getSource().intValue());
        if (Objects.isNull(sourceEnum)) {
            return pair;
        }
        boolean isOpenApiSource = Objects.equals(sourceEnum, SourceEnum.OPEN_API);
        return Pair.of(isOpenApiSource, sourceEnum.getDesc());
    }
}
