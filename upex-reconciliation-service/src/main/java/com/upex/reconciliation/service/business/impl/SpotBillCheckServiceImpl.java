package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AbstractBillCheckService;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.dao.entity.BillCoinProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillWalletSupplementConfig;
import com.upex.reconciliation.service.model.alarm.CheckResult;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.*;
import com.upex.reconciliation.service.utils.NumberUtil;
import com.upex.ticker.facade.dto.PriceVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class SpotBillCheckServiceImpl extends AbstractBillCheckService {

    @Resource
    private AlarmNotifyService alarmNotifyService;

    @Override
    public Byte getAccountType() {
        return AccountTypeEnum.SPOT.getCode();
    }

    @Override
    public boolean checkBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty) {
        if (commonBillChangeData.getPropSum().subtract(commonBillChangeData.getChangePropSum()).compareTo(billCoinUserProperty.getPropSum()) == 0) {
            return true;
        }
        return false;
    }

    @Override
    public boolean checkCurrentBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty) {
        if (commonBillChangeData.getPropSum().compareTo(billCoinUserProperty.getPropSum()) == 0) {
            return true;
        }
        return false;
    }

    @Override
    public BigDecimal getPropSumByUserProperty(BillCoinUserProperty coinUserProperty) {
        return NumberUtil.add(coinUserProperty.getProp1(), coinUserProperty.getProp2(), coinUserProperty.getProp3(), coinUserProperty.getProp4(), coinUserProperty.getProp5());
    }

    @Override
    public CheckResult doCheckProperty(ApolloReconciliationBizConfig apolloBizConfig, String accountUniqueId, Long userId, Integer coinId, List<CommonBillChangeData> billChangeDataList, BillCoinUserProperty billCoinUserProperty) {
        CommonBillChangeData lastChange = billChangeDataList.get(billChangeDataList.size() - 1);
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        BigDecimal totalChangeProp1 = BigDecimal.ZERO;
        BigDecimal totalChangeProp2 = BigDecimal.ZERO;
        BigDecimal totalChangeProp3 = BigDecimal.ZERO;
        BigDecimal totalChangeProp4 = BigDecimal.ZERO;
        BigDecimal totalChangeProp5 = BigDecimal.ZERO;
        for (CommonBillChangeData bill : billChangeDataList) {
            totalChangeProp1 = totalChangeProp1.add(bill.getChangeProp1());
            totalChangeProp2 = totalChangeProp2.add(bill.getChangeProp2());
            totalChangeProp3 = totalChangeProp3.add(bill.getChangeProp3());
            totalChangeProp4 = totalChangeProp4.add(bill.getChangeProp4());
            totalChangeProp5 = totalChangeProp5.add(bill.getChangeProp5());
        }
        BigDecimal totalChangeProp = totalChangeProp1.add(totalChangeProp2).add(totalChangeProp3).add(totalChangeProp4).add(totalChangeProp5);
        if (lastChange.getPropSum().compareTo(billCoinUserProperty.getPropSum().add(totalChangeProp)) != 0) {
            log.error("CalculationCheckUtils.checkBillCoinProperty accountType {} accountUniqueId :{}  lastChange:{},billCoinUserProperty:{},totalChangeProp1:{},totalChangeProp2:{},totalChangeProp3:{},totalChangeProp4:{},totalChangeProp5:{}"
                    , getAccountType(), accountUniqueId, JSONObject.toJSONString(lastChange)
                    , JSONObject.toJSONString(billCoinUserProperty), totalChangeProp1.toPlainString(), totalChangeProp2.toPlainString(), totalChangeProp3.toPlainString(), totalChangeProp4.toPlainString(), totalChangeProp5.toPlainString());
            return CheckResult.fail(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), lastChange.getBizTime())
                    .addUserPropCheckData(lastChange.getAccountId(), lastChange.getCoinId(), billCoinUserProperty.getPropSum(), totalChangeProp, lastChange.getPropSum(), lastChange.getBizId(), "propSum");
        }
        return CheckResult.DEFAULT_SUCCESS;
    }

    @Override
    protected UserAssetsNegativeModel doCheckNegative(BillCoinUserProperty billCoinUserProperty, BigDecimal unRealized) {
        if (billCoinUserProperty.getPropSum().compareTo(BigDecimal.ZERO) < 0) {
            UserAssetsNegativeModel userAssetsNegativeModel = UserAssetsNegativeModel.builder()
                    .propValue(billCoinUserProperty.getPropSum())
                    .build();
            return userAssetsNegativeModel;
        }
        return null;
    }

    @Override
    public <T extends AbstractProperty> boolean checkAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult, ApolloReconciliationBizConfig apolloBillConfig, Map<Integer, PriceVo> ratesToUSDTCoinIdMap, Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap) {
        if (property == null || totalAccountAssetsInfoResult == null) {
            return false;
        }
        BigDecimal propertySum = property.getSpotComboCheck();
        BigDecimal resultSum = totalAccountAssetsInfoResult.getProp1().add(totalAccountAssetsInfoResult.getProp3());
        if (propertySum.compareTo(resultSum) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractSProperty> boolean checkSpropAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult, ApolloReconciliationBizConfig apolloBillConfig, Map<Integer, PriceVo> ratesToUSDTCoinIdMap, Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap) {
        if (property == null || totalAccountAssetsInfoResult == null) {
            return false;
        }
        BigDecimal propertySum = property.getSprop1().add(property.getSprop2()).add(property.getProp3());
        BigDecimal resultSum = totalAccountAssetsInfoResult.getProp1().add(totalAccountAssetsInfoResult.getProp2()).add(totalAccountAssetsInfoResult.getProp3());
        if (propertySum.compareTo(resultSum) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public BigDecimal getPropSumByProperty(AbstractProperty billCoinProperty) {
        return billCoinProperty == null ? BigDecimal.ZERO : billCoinProperty.getProp1().add(billCoinProperty.getProp2()).add(billCoinProperty.getProp3()).add(billCoinProperty.getProp4()).add(billCoinProperty.getProp5());
    }

    @Override
    public BigDecimal getPropByTransferFee(AbstractProperty abstractProperty) {
        return abstractProperty != null ? abstractProperty.getProp1() : BigDecimal.ZERO;
    }

    @Override
    public BigDecimal getChangePropSumByProperty(AbstractProperty abstractProperty) {
        return NumberUtil.add(abstractProperty.getChangeProp1(), abstractProperty.getChangeProp2(), abstractProperty.getChangeProp3(), abstractProperty.getChangeProp4(), abstractProperty.getChangeProp5());
    }

    @Override
    public BigDecimal getPropSumByBillChangeData(CommonBillChangeData currentBill) {
        return NumberUtil.add(currentBill.getProp1(), currentBill.getProp2(), currentBill.getProp3(), currentBill.getProp4(), currentBill.getProp5());
    }

    @Override
    public BigDecimal getChangePropSumByBillChangeData(CommonBillChangeData currentBill) {
        return NumberUtil.add(currentBill.getChangeProp1(), currentBill.getChangeProp2(), currentBill.getChangeProp3(), currentBill.getChangeProp4(), currentBill.getChangeProp5());
    }

    @Override
    public BigDecimal getChangePropSumByAbstractProperty(AbstractProperty abstractProperty) {
        return NumberUtil.add(abstractProperty.getChangeProp1(), abstractProperty.getChangeProp2(), abstractProperty.getChangeProp3(), abstractProperty.getChangeProp4(), abstractProperty.getChangeProp5());
    }

    /**
     * 获取业务线类型
     *
     * @return
     */
    @Override
    public List<String> getBusinessType() {
        return Arrays.asList(AccountTypeEnum.SPOT.getBizTypePrefix());
    }

    @Override
    public <T extends AbstractProperty> boolean checkPosition(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult) {
        return false;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitAssets(T oldProperty, BillCoinUserProperty newBillCoinUserProperty) {
        if (oldProperty == null || newBillCoinUserProperty == null) {
            return false;
        }
        if (oldProperty.getProp1().compareTo(newBillCoinUserProperty.getProp1()) != 0) {
            return false;
        }
        if (oldProperty.getProp3().compareTo(newBillCoinUserProperty.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinAssets(T oldProperty, BillCoinProperty billCoinProperty) {
        if (oldProperty == null || billCoinProperty == null) {
            return false;
        }
        if (oldProperty.getProp1().compareTo(billCoinProperty.getProp1()) != 0) {
            return false;
        }
        if (oldProperty.getProp2().compareTo(billCoinProperty.getProp2()) != 0) {
            return false;
        }
        if (oldProperty.getProp3().compareTo(billCoinProperty.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeAssets(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        if (oldProperty == null || newBillCoinTypeProperty == null) {
            return false;
        }
        if (oldProperty.getProp1().compareTo(newBillCoinTypeProperty.getProp1()) != 0) {
            return false;
        }
        if (oldProperty.getProp2().compareTo(newBillCoinTypeProperty.getProp2()) != 0) {
            return false;
        }
        if (oldProperty.getProp3().compareTo(newBillCoinTypeProperty.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinChangeAssets(T oldProperty, BillCoinProperty billCoinProperty) {
        if (oldProperty == null || billCoinProperty == null) {
            return false;
        }
        if (oldProperty.getChangeProp1().compareTo(billCoinProperty.getChangeProp1()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp2().compareTo(billCoinProperty.getChangeProp2()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp3().compareTo(billCoinProperty.getChangeProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeChangeAssets(T oldProperty, BillCoinTypeProperty billCoinTypeProperty) {
        if (oldProperty == null || billCoinTypeProperty == null) {
            return false;
        }
        if (oldProperty.getChangeProp1().compareTo(billCoinTypeProperty.getChangeProp1()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp2().compareTo(billCoinTypeProperty.getChangeProp2()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp3().compareTo(billCoinTypeProperty.getChangeProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinAssetsByAccountType(T oldProperty, BillCoinProperty newBillCoinUserProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        if (oldProperty != null) {
            dataCalResultDTO.setFactor1(oldProperty.getProp1().add(oldProperty.getProp2()).add(oldProperty.getProp3()).add(oldProperty.getProp4()).add(oldProperty.getProp5()));
        }
        if (newBillCoinUserProperty != null) {
            dataCalResultDTO.setFactor2(newBillCoinUserProperty.getProp1().add(newBillCoinUserProperty.getProp2()).add(newBillCoinUserProperty.getProp3()).add(newBillCoinUserProperty.getProp4()).add(newBillCoinUserProperty.getProp5()));
        }
        BigDecimal tolerance = coinComparisonToleranceMap.getOrDefault(newBillCoinUserProperty.getCoinId(), BigDecimal.ZERO);
        dataCalResultDTO.setResult(dataCalResultDTO.getFactor1().subtract(dataCalResultDTO.getFactor2()).abs());
        dataCalResultDTO.setPass(dataCalResultDTO.getResult().compareTo(tolerance) <= 0);
        return dataCalResultDTO.isPass();
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeInitAssetsByAccountType(List<T> oldPropertyList, BillCoinTypeProperty newBillCoinTypeProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        if (CollectionUtils.isEmpty(oldPropertyList) || newBillCoinTypeProperty == null) {
            return false;
        }
        BigDecimal tolerance = coinComparisonToleranceMap.getOrDefault(newBillCoinTypeProperty.getCoinId(), BigDecimal.ZERO);
        BigDecimal oldSum = BigDecimal.ZERO;
        for (T odlProperty : oldPropertyList) {
            oldSum = oldSum.add(odlProperty.getProp1()).add(odlProperty.getProp2()).add(odlProperty.getProp3()).add(odlProperty.getProp4()).add(odlProperty.getProp5());
        }
        BigDecimal newSum = newBillCoinTypeProperty.getProp1().add(newBillCoinTypeProperty.getProp2()).add(newBillCoinTypeProperty.getProp3()).add(newBillCoinTypeProperty.getProp4()).add(newBillCoinTypeProperty.getProp5());
        dataCalResultDTO.setFactor1(oldSum);
        dataCalResultDTO.setFactor2(newSum);
        dataCalResultDTO.setResult(oldSum.subtract(newSum).abs());
        if (oldSum.subtract(newSum).abs().compareTo(tolerance) > 0) {
            log.info("CheckAssetSpotServiceImpl checkCoinTypeAssetsByAccountType failed , old sum {}, new sum {} , property {}", oldSum, newSum, JSONObject.toJSONString(newBillCoinTypeProperty));
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeAssetsByAccountType(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        if (oldProperty == null) {
            return Objects.isNull(newBillCoinTypeProperty) || newBillCoinTypeProperty.getChangePropSum().compareTo(BigDecimal.ZERO) == 0;
        }
        if (oldProperty.getChangeProp1().compareTo(newBillCoinTypeProperty.getChangeProp1()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp2().compareTo(newBillCoinTypeProperty.getChangeProp2()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp3().compareTo(newBillCoinTypeProperty.getChangeProp3()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp4().compareTo(newBillCoinTypeProperty.getChangeProp4()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp5().compareTo(newBillCoinTypeProperty.getChangeProp5()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> BigDecimal sumForInAll(T property) {
        if (property == null) {
            return BigDecimal.ZERO;
        }
        return property.getProp1().add(property.getProp2()).add(property.getProp3()).add(property.getProp4()).add(property.getProp5());
    }

    @Override
    public void cleanAndSetAssetsProperty(AbstractProperty abstractProperty, Byte accountType) {
        BigDecimal prop1 = abstractProperty.getProp1();
        BigDecimal prop2 = abstractProperty.getProp2();
        BigDecimal prop3 = abstractProperty.getProp3();
        BigDecimal changeProp1 = abstractProperty.getChangeProp1();
        BigDecimal changeProp2 = abstractProperty.getChangeProp2();
        BigDecimal changeProp3 = abstractProperty.getChangeProp3();
        abstractProperty.cleaPropAndChangeProp();
        abstractProperty.setProp1(prop1);
        abstractProperty.setProp2(prop2);
        abstractProperty.setProp3(prop3);
        abstractProperty.setChangeProp1(changeProp1);
        abstractProperty.setChangeProp2(changeProp2);
        abstractProperty.setChangeProp3(changeProp3);
    }
}
