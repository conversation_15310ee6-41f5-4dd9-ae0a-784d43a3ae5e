package com.upex.reconciliation.service.service.impl;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.service.OldBillSymbolPropertyService;
import com.upex.reconciliation.service.dao.entity.BillSymbolProperty;
import com.upex.reconciliation.service.dao.mapper.OldBillSymbolPropertyMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class OldBillSymbolPropertyServiceImpl implements OldBillSymbolPropertyService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "oldBillSymbolPropertyMapper")
    private OldBillSymbolPropertyMapper oldBillSymbolPropertyMapper;

    @Override
    public List<BillSymbolProperty> selectListByCheckTime(Byte accountType, String accountParam, Date checkOkTime) {
        return dbHelper.doDbOpInBillMaster(() -> oldBillSymbolPropertyMapper.selectListByCheckTime(accountType, accountParam, checkOkTime));
    }
}
