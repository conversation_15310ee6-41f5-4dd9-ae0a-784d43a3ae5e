package com.upex.reconciliation.service.utils;


import com.upex.mixcontract.common.literal.enums.HoldSideEnum;
import com.upex.mixcontract.common.utils.ContractFormulaUtils;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.BillCheckService;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty;
import com.upex.reconciliation.service.model.dto.AbstractProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
public class CalculationCheckUtils {

    public static BigDecimal sumChange(BillCheckService billCheckService, List<BillCoinTypeProperty> coinTypeProperties, AccountTypeEnum accountTypeEnum) {
        return billCheckService.getChangePropSumByProperty(coinTypeProperties);
    }

    public static BillCoinTypeProperty sumChangeFee(List<BillCoinTypeProperty> coinUserProperties) {
        BillCoinTypeProperty billCoinTypeProperty = new BillCoinTypeProperty();
        if (!CollectionUtils.isEmpty(coinUserProperties)) {
            for (BillCoinTypeProperty item : coinUserProperties) {
                billCoinTypeProperty.setChangeProp1(billCoinTypeProperty.getChangeProp1().add(item.getChangeProp1()));
                billCoinTypeProperty.setChangeProp2(billCoinTypeProperty.getChangeProp2().add(item.getChangeProp2()));
                billCoinTypeProperty.setChangeProp3(billCoinTypeProperty.getChangeProp3().add(item.getChangeProp3()));
                billCoinTypeProperty.setChangeProp4(billCoinTypeProperty.getChangeProp4().add(item.getChangeProp4()));
                billCoinTypeProperty.setChangeProp5(billCoinTypeProperty.getChangeProp5().add(item.getChangeProp5()));
            }
        }
        return billCoinTypeProperty;
    }


    public static List<BillCoinTypeProperty> buildFeeAggregationData(Integer coinId, List<BillCoinTypeProperty> currentList, List<BillCoinTypeProperty> lastList) {
        if (CollectionUtils.isEmpty(currentList)) {
            return Collections.emptyList();
        }
        List<BillCoinTypeProperty> resultList = new ArrayList<>();
        Map<String, BillCoinTypeProperty> lastMap = !CollectionUtils.isEmpty(lastList) ? lastList.stream().collect(Collectors.toMap(BillCoinTypeProperty::getBizType, Function.identity())) : new HashMap<>();
        for (BillCoinTypeProperty currentProperty : currentList) {
            BillCoinTypeProperty lastBillCoinTypeProperty = lastMap.get(currentProperty.getBizType());
            BillCoinTypeProperty coinTypeProperty = calculateChange(coinId, currentProperty, lastBillCoinTypeProperty);
            resultList.add(coinTypeProperty);
        }
        return resultList;

    }


    public static BillCoinTypeProperty calculateChange(Integer coinId, AbstractProperty currentProperty, AbstractProperty lastProperty) {
        BillCoinTypeProperty aggregationData = new BillCoinTypeProperty();
        aggregationData.setCoinId(coinId);
        if (currentProperty == null) {
            return aggregationData;
        }
        aggregationData.setChangeProp1(lastProperty != null ? currentProperty.getProp1().subtract(lastProperty.getProp1()) : currentProperty.getProp1());
        aggregationData.setChangeProp2(lastProperty != null ? currentProperty.getProp2().subtract(lastProperty.getProp2()) : currentProperty.getProp2());
        aggregationData.setChangeProp3(lastProperty != null ? currentProperty.getProp3().subtract(lastProperty.getProp3()) : currentProperty.getProp3());
        aggregationData.setChangeProp4(lastProperty != null ? currentProperty.getProp4().subtract(lastProperty.getProp4()) : currentProperty.getProp4());
        aggregationData.setChangeProp5(lastProperty != null ? currentProperty.getProp5().subtract(lastProperty.getProp5()) : currentProperty.getProp5());
        return aggregationData;
    }

    /**
     * 计算仓位未实现
     *
     * @param holdSide
     * @param volume
     * @param averagePrice
     * @param mPrice
     * @param serverCalPlace
     * @return
     */
    public static BigDecimal calProfits(HoldSideEnum holdSide, BigDecimal volume, BigDecimal averagePrice, BigDecimal mPrice, Integer serverCalPlace, Boolean isContractAdlReceivedUserId) {
        return isContractAdlReceivedUserId && volume.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO
                : ContractFormulaUtils.calProfits(holdSide, volume, averagePrice, mPrice, serverCalPlace);
    }
}