package com.upex.reconciliation.service.business.module.processor;

import com.alibaba.fastjson.JSON;
import com.upex.reconciliation.service.business.MonitorCheckService;
import com.upex.reconciliation.service.model.BillCmdResult;
import com.upex.reconciliation.service.model.MonitorCmdWrapper;
import com.upex.reconciliation.service.model.config.MonitorSceneTaskConfig;
import com.upex.reconciliation.service.business.module.AbstractMonitorProcessor;
import com.upex.reconciliation.service.utils.MetricsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

import static com.upex.reconciliation.service.utils.MetricsUtil.HISTOGRAM_MONITOR_CHECK_TAKE_COMMAND;

/**
 * 多纬度指标监控的 check 阶段的总调度 processor
 * 负责接受所有场景的 cmd命令，并路由到具体的 java处理单元做check动作
 */
@Slf4j
@Service
public class MonitorCheckProcessor extends AbstractMonitorProcessor implements ApplicationContextAware {
    /***命令处理队列***/
    private BlockingQueue<MonitorCmdWrapper> cmdQueue = new ArrayBlockingQueue<>(10000);

    private Map<String, MonitorCheckService> monitorCheckServiceMap;

    @Resource
    private MonitorCheckProcessor monitorCheckProcessor;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        monitorCheckServiceMap = applicationContext.getBeansOfType(MonitorCheckService.class);
    }


    @PostConstruct
    public void runningTakeCommand() {
        // 启动主消费任务
        Thread thread = new Thread(() -> {
            while (true) {
                try {
                    takeCommand();
                } catch (Throwable ex) {
                    log.error("MonitorCheckProcessor failed", ex);
                    throw ex;
                }
            }
        }, "MonitorCheckProcessor");
        // todo 池化
        thread.start();
    }


    @Override
    public void offerCommand(MonitorCmdWrapper cmdWrapper) {
        try {
            this.cmdQueue.put(cmdWrapper);
        } catch (InterruptedException e) {
            String errorMsg = "MonitorCheckProcessor Command queue is full , queue size: " + this.cmdQueue.size();
            throw new RuntimeException(errorMsg);
        }
    }

    @Override
    public BillCmdResult takeCommand() {
        try {
            MonitorCmdWrapper cmdWrapper = this.cmdQueue.poll(1, TimeUnit.SECONDS);
            if (cmdWrapper == null) {
                return null;
            }
            try {
                return MetricsUtil.histogram(HISTOGRAM_MONITOR_CHECK_TAKE_COMMAND, () -> execCommand(cmdWrapper));
            } catch (Exception e) {
                log.error("error", e);
            }
            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * @param monitorCmdWrapper
     * @return
     */
    private BillCmdResult execCommand(MonitorCmdWrapper monitorCmdWrapper) {
        try {
            MonitorSceneTaskConfig monitorSceneTaskConfig = monitorCmdWrapper.getTaskConfig();
            // 找到对应的java实现类，表用并获取到结果
            String dataProcessService = monitorSceneTaskConfig.getCheckProcessService();
            MonitorCheckService monitorCheckService = monitorCheckServiceMap.get(dataProcessService);
            monitorCheckService.processScene(monitorSceneTaskConfig.getSceneId(), monitorCmdWrapper);
        } catch (Exception e) {
            log.error("MonitorCheckProcessor.execCommand error  {}", JSON.toJSONString(monitorCmdWrapper), e);
        }
        return null;
    }

}
