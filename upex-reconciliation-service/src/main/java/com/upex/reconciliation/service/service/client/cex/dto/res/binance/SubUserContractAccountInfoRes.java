package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SubUserContractAccountInfoRes implements IBinanceApiBaseRes{

    private FutureAccountInnerResp futureAccountResp;


    @Data
    public static class FutureAccountInnerResp {
        /**
         * "email": "<EMAIL>",
         * "canTrade": true,
         * "canDeposit": true,
         * "canWithdraw": true,
         * "feeTier": 0,
         * "updateTime": *************,
         * "asset": "USDT",
         * "totalInitialMargin": "0.********",
         * "totalMaintenanceMargin": "0.********",
         * "totalWalletBalance": "2.********",
         * "totalUnrealizedProfit": "0.********",
         * "totalMarginBalance": "2.********",
         * "totalPositionInitialMargin": "0.********",
         * "totalOpenOrderInitialMargin": "0.********",
         * "maxWithdrawAmount": "2.********",
         */
        private Boolean canTrade;
        private Boolean canDeposit;
        private Boolean canWithdraw;
        private Long updateTime;
        private String asset;
        private BigDecimal totalInitialMargin;
        private BigDecimal totalMaintenanceMargin;
        private BigDecimal totalMarginBalance;
        private BigDecimal totalUnrealizedProfit;
        private BigDecimal totalWalletBalance;
        private BigDecimal maxWithdrawAmount;
        private BigDecimal unrealizedProfit;
        private BigDecimal totalPositionInitialMargin;
        private BigDecimal totalOpenOrderInitialMargin;
        private List<SubUserContractAccountInfoInnerRes> assets;


    }
}
