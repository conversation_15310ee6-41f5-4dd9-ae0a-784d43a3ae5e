package com.upex.reconciliation.service.utils;


import com.upex.reconciliation.service.common.constants.BillConstants;

import java.util.*;

/**
 * @Author: allen
 * @Date: 2020-05-18 14:28
 * @DES: 分组key
 */
public class GroupByKeyUtil {

    /**
     * 按照 userId+coinId
     */
    public static final int USERID_COINID = 1;


    /**
     * 按照 userId+coinId+typeId
     */
    public static final int USERID_COINID_TYPEID = 2;

    /**
     * 按照coinId+typeId
     */
    public static final int COINID_TYPEID = 3;

    /**
     * 按照 coinId
     */
    public static final int COINID = 4;

    /**
     * 按照 symbolId
     */
    public static final int SYMBOLID = 5;

    /**
     * 按照 symbolId + bizType
     */
    public static final int SYMBOLID_BIZTYPE = 6;

    /**
     * 按照symbolId分组
     *
     * @return
     */
    public static String groupBySymbolId(String symbolId) {
        return String.format("%s", symbolId);
    }

    /**
     * 按照symbolId + bizType分组
     *
     * @return
     */
    public static String groupBySymbolIdAndBizType(String symbolId, String bizType) {
        return String.format("%s#%s", symbolId, bizType);
    }

    /**
     * 按照业务线+币种分组
     *
     * @return
     */
    public static String groupByAccountTypeAndCoinId(Byte accountType, Integer coinId) {
        return String.format("%s#%s", accountType,coinId);
    }

    /**
     * 按照symbolId + coinId分组
     *
     * @return
     */
    public static String groupBySymbolIdAndCoinId(String symbolId, int coinId) {
        return String.format("%s#%s", symbolId, coinId);
    }


    /**
     * 按照userId+coinId分组
     *
     * @return
     */
    public static String groupByUserIdAndCoinId(long userId, int coinId) {
        return String.format("%s#%s", userId, coinId);
    }

    /**
     * 按照userId+coinId+bizType分组
     *
     * @return
     */
    public static String groupByUserIdAndCoinIdAndTypeId(long userId, int coinId, String bizType) {
        return String.format("%s#%s#%s", userId, coinId, bizType);
    }

    /**
     * 按照coinId+bizType分组
     *
     * @return
     */
    public static String groupByCoinIdAndTypeId(int coinId, String bizType) {
        return String.format("%s#%s", coinId, bizType);
    }

    /**
     * 按照coinId+coinName分组
     *
     * @return
     */
    public static String groupByCoinIdAndCoinName(int coinId, String coinName) {
        return String.format("%s#%s", coinId, coinName);
    }

    /**
     * 按照coinId分组
     *
     * @return
     */
    public static String groupByCoinId(int coinId) {
        return String.format("%s", coinId);
    }

    /**
     * 按照uId+busLine+secBusLine+tId+sId分组
     * @return
     */
    public static String groupByUIdBusLineSecBusLineTidSId(Long uId, Integer busLine, String secBusLine, String tId, String sId) {
        return String.format("%s#%s#%s#%s#%s", uId, busLine,secBusLine,tId,sId);
    }

    /**
     * 按照userId+bizType+symbolId+coinId分组
     * @return
     */
    public static String groupByUserIdAndBizTypeAndSymbolIdAndTidAndCoinId(Long userId,String bizType,String symbolId,Integer coinId) {
        return String.format("%s#%s#%s#%s", userId, bizType,symbolId,coinId);
    }


    /**
     * 按照 auditId + businessLine + coinId + coinName 进行分组
     * @param auditId
     * @param businessLine
     * @param coinId
     * @param coinName
     * @return
     */
    public static String groupByAuditIdAndBusinessLineAndCoinIdAndCoinName(String auditId,Integer businessLine,Integer coinId,String coinName) {
        return String.format("%s#%s#%s#%s", auditId,businessLine,coinId,coinName);
    }

    /**
     * 按照 userId + coinId + channelCode 进行分组
     * @param userId
     * @param coinId
     * @param coinId
     * @param channelCode
     * @return
     */
    public static String groupByUserIdAndCoinIdAndChannelCode(Long userId,Integer coinId,String channelCode) {
        return String.format("%s#%s#%s", userId,coinId,channelCode);
    }

    /**
     * 按照userId+bizType分组
     *
     * @return
     */
    public static String groupByUserIdAndType(long userId, String bizType) {
        return String.format("%s#%s", userId, bizType);
    }
    /**
     * 按照coinuserId+checkOkTime分组
     *
     * @return
     */
    public static String groupByUserIdAndCheckTime(Long userId, Long checkOkTime) {
        return String.format("%s#%s", userId, checkOkTime);
    }

    /**
     * 按照businessLine+userId+bizId分组
     *
     * @return
     */
    public static String groupByBusinessLineAndUserIdAndBizId(Long userId, Integer groupType, Integer coinId) {
        return String.format("%s#%s#%s", userId, groupType, coinId);
    }

    /**
     * 按照businessLine+userId+bizId分组
     *
     * @return
     */
    public static String groupByBusinessLineAndUserIdAndBizId(Integer businessLine, Long userId, String bizId) {
        return String.format("%s#%s#%s", businessLine, userId, bizId);
    }



    public static Map<String, List<String>> groupByAccountType(List<String> transConfigList) {
        Map<String, List<String>> accountTypeBizTypeMap = new HashMap<>();
        for (String transConfig : transConfigList) {
            String[] strArr = transConfig.split(BillConstants.SEPARATOR);
            if (strArr.length == 2) {
                String accountTypeName = strArr[0];
                String bizType = strArr[1];
                List<String> accountTypes = accountTypeBizTypeMap.computeIfAbsent(accountTypeName, v -> {
                    return new ArrayList<>();
                });
                accountTypes.add(bizType);
            }
        }
        return accountTypeBizTypeMap;
    }

    public static String group(Integer groupType, Long userId, Integer coinId, Date snapshotTime) {
        return String.format("%s#%s#%s#%s", groupType, userId, coinId, snapshotTime.getTime());
    }
}
