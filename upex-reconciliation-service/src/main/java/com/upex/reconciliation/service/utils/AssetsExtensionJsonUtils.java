package com.upex.reconciliation.service.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.upex.mixcontract.process.facade.dto.MixAccountAssetsExtension;
import com.upex.reconciliation.facade.dto.results.ReconBillBaseUserAssetsRsp;
import com.upex.reconciliation.service.common.constants.BillConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * 仓位信息JSON工具类
 *
 * <AUTHOR>
 * @date 2023/2/28 11:14
 */
public class AssetsExtensionJsonUtils {
    /**
     * JSON字符串转List<MixAccountAssetsExtension>集合
     *
     * @param text
     * @return {@link List<MixAccountAssetsExtension> }
     * <AUTHOR>
     * @date 2023/2/28 14:35
     */
    public static List<MixAccountAssetsExtension> parseArray(String text) {
        Queue<MixAccountAssetsExtension> extensionList = new ConcurrentLinkedQueue<>();
        if (StringUtils.isBlank(text)) {
            return new CopyOnWriteArrayList<>(extensionList);
        }
        JSONArray jsonArray = JSONObject.parseArray(text);
        if (CollectionUtils.isEmpty(jsonArray)) {
            return new CopyOnWriteArrayList<>(extensionList);
        }

        jsonArray.stream().parallel().forEach(element -> {
            if (element == null) {
                return;
            }
            JSONObject jsonObject = (JSONObject) element;
            MixAccountAssetsExtension extension = getMixAccountAssetsExtension(jsonObject);
            extensionList.add(extension);
        });
        return new CopyOnWriteArrayList<>(extensionList);
    }

    private static MixAccountAssetsExtension getMixAccountAssetsExtension(JSONObject jsonObject) {
        MixAccountAssetsExtension extension = new MixAccountAssetsExtension();
        extension.setUId(jsonObject.getLong("uId"));
        extension.setBusLine(jsonObject.getInteger("busLine"));
        extension.setSecBusLine(jsonObject.getString("secBusLine"));
        extension.setTId(jsonObject.getString("tId"));
        extension.setSId(jsonObject.getString("sId"));
        extension.setHoldMode(NumberUtil.isNullDefaultZero(jsonObject.getInteger("holdMode")));
        extension.setMgnMode(jsonObject.getInteger("mgnMode"));
        extension.setPosiMgn(NumberUtil.isNullDefaultZero(jsonObject.getBigDecimal("posiMgn")));
        extension.setFxLLever(jsonObject.getInteger("fxLLever"));
        extension.setFxLLeverChg(NumberUtil.isNullDefaultZero(jsonObject.getInteger("fxLLeverChg")));
        extension.setLAvg(jsonObject.getBigDecimal("lAvg"));
        extension.setFxSLever(jsonObject.getInteger("fxSLever"));
        extension.setFxSLeverChg(NumberUtil.isNullDefaultZero(jsonObject.getInteger("fxSLeverChg")));
        extension.setCMgnLever(jsonObject.getInteger("cMgnLever"));
        extension.setCMgnLeverChg(NumberUtil.isNullDefaultZero(jsonObject.getInteger("cMgnLeverChg")));
        extension.setSAvg(jsonObject.getBigDecimal("sAvg"));
        extension.setLCountChg(NumberUtil.isNullDefaultZero(jsonObject.getBigDecimal("lCountChg")));
        extension.setLCount(NumberUtil.isNullDefaultZero(jsonObject.getBigDecimal("lCount")));
        extension.setSCountChg(NumberUtil.isNullDefaultZero(jsonObject.getBigDecimal("sCountChg")));
        extension.setSCount(NumberUtil.isNullDefaultZero(jsonObject.getBigDecimal("sCount")));
        extension.setUnR(NumberUtil.isNullDefaultZero(jsonObject.getBigDecimal("unR")));
        extension.setUnRInS(NumberUtil.isNullDefaultZero(jsonObject.getBigDecimal("unRInS")));
        extension.setMPrice(NumberUtil.isNullDefaultZero(jsonObject.getBigDecimal("mPrice")));
        extension.setSPrice(NumberUtil.isNullDefaultZero(jsonObject.getBigDecimal("sPrice")));
        extension.setMPriceMap(getMPriceMap(jsonObject.getJSONObject("mPriceMap")));
        extension.setSPriceMap(getSPriceMap(jsonObject.getJSONObject("sPriceMap")));
        return extension;
    }

    /**
     * JSON字符串转List<MixAccountAssetsExtension>集合
     *
     * @param text
     * @return {@link List<MixAccountAssetsExtension> }
     * <AUTHOR>
     * @date 2023/2/28 14:35
     */
    public static MixAccountAssetsExtension parseObject(String text) {
        if (StringUtils.isBlank(text)) {
            return new MixAccountAssetsExtension();
        }
        JSONObject jsonObject = JSONObject.parseObject(text);
        if (Objects.isNull(jsonObject)) {
            return new MixAccountAssetsExtension();
        }

        return getMixAccountAssetsExtension(jsonObject);
    }

    private static Map<String, BigDecimal> getMPriceMap(JSONObject jsonObject) {
        if (jsonObject == null) {
            return null;
        }
        Map<String, BigDecimal> priceMap = new ConcurrentHashMap<>();
        jsonObject.forEach((key, value) -> {
            if (value == null) {
                return;
            }
            priceMap.put(key, new BigDecimal(value.toString()));
        });
        return priceMap;
    }

    private static Map<Integer, BigDecimal> getSPriceMap(JSONObject jsonObject) {
        if (jsonObject == null) {
            return null;
        }
        Map<Integer, BigDecimal> priceMap = new ConcurrentHashMap<>();
        jsonObject.forEach((key, value) -> {
            if (value == null) {
                return;
            }
            priceMap.put(Integer.valueOf(key), new BigDecimal(value.toString()));
        });
        return priceMap;
    }

    /**
     * 对象转JSON字符串
     *
     * @param extensionList
     * @return {@link String }
     * <AUTHOR>
     * @date 2023/2/28 14:36
     */
    public static String toJSONString(List<? extends MixAccountAssetsExtension> extensionList) {
        if (CollectionUtils.isEmpty(extensionList)) {
            return "[]";
        }

        String jsonStrings = extensionList.stream().parallel().map(element -> {
            StringBuffer buffer = new StringBuffer();
            buffer.append("{");
            buffer.append("\"").append("busLine").append("\"").append(":").append(element.getBusLine()).append(",");
            buffer.append("\"").append("cMgnLever").append("\"").append(":").append(element.getCMgnLever()).append(",");
            buffer.append("\"").append("cMgnLeverChg").append("\"").append(":").append(element.getCMgnLeverChg()).append(",");
            buffer.append("\"").append("fxLLever").append("\"").append(":").append(element.getFxLLever()).append(",");
            buffer.append("\"").append("fxLLeverChg").append("\"").append(":").append(element.getFxLLeverChg()).append(",");
            buffer.append("\"").append("fxSLever").append("\"").append(":").append(element.getFxSLever()).append(",");
            buffer.append("\"").append("fxSLeverChg").append("\"").append(":").append(element.getFxSLeverChg()).append(",");
            buffer.append("\"").append("holdMode").append("\"").append(":").append(element.getHoldMode()).append(",");
            buffer.append("\"").append("lAvg").append("\"").append(":").append(NumberUtil.toPlainString(element.getLAvg())).append(",");
            buffer.append("\"").append("lCount").append("\"").append(":").append(NumberUtil.toPlainString(element.getLCount())).append(",");
            buffer.append("\"").append("lCountChg").append("\"").append(":").append(NumberUtil.toPlainString(element.getLCountChg())).append(",");
            buffer.append("\"").append("mPrice").append("\"").append(":").append(element.getMPrice()).append(",");
            buffer.append("\"").append("mgnMode").append("\"").append(":").append(element.getMgnMode()).append(",");
            buffer.append("\"").append("posiMgn").append("\"").append(":").append(NumberUtil.toPlainString(element.getPosiMgn())).append(",");
            buffer.append("\"").append("sAvg").append("\"").append(":").append(NumberUtil.toPlainString(element.getSAvg())).append(",");
            buffer.append("\"").append("sCount").append("\"").append(":").append(NumberUtil.toPlainString(element.getSCount())).append(",");
            buffer.append("\"").append("sCountChg").append("\"").append(":").append(NumberUtil.toPlainString(element.getSCountChg())).append(",");
            buffer.append("\"").append("sId").append("\"").append(":").append("\"").append(StringUtils.defaultString(element.getSId(), BillConstants.EMPTY)).append("\"").append(",");
            buffer.append("\"").append("sPrice").append("\"").append(":").append(NumberUtil.toPlainString(element.getSPrice())).append(",");
            buffer.append("\"").append("secBusLine").append("\"").append(":").append("\"").append(StringUtils.defaultString(element.getSecBusLine(), BillConstants.EMPTY)).append("\"").append(",");
            buffer.append("\"").append("tId").append("\"").append(":").append("\"").append(StringUtils.defaultString(element.getTId(), BillConstants.EMPTY)).append("\"").append(",");
            buffer.append("\"").append("unR").append("\"").append(":").append(NumberUtil.toPlainString(element.getUnR())).append(",");
            buffer.append("\"").append("unRInS").append("\"").append(":").append(NumberUtil.toPlainString(element.getUnRInS())).append(",");

            buffer.append("\"").append("mPriceMap").append("\"").append(":").append(toPriceMapJSONString(element.getMPriceMap())).append(",");
            buffer.append("\"").append("sPriceMap").append("\"").append(":").append(toPriceMapJSONString(element.getSPriceMap()));
            buffer.append("}");
            return buffer;
        }).collect(Collectors.joining(BillConstants.COMMA, BillConstants.LEFT_BRACKETS, BillConstants.RIGHT_BRACKETS));
        return jsonStrings;
    }


    private static <K> String toPriceMapJSONString(Map<K, BigDecimal> assetsMap) {
        if (MapUtils.isEmpty(assetsMap)) {
            return "{}";
        }

        StringBuffer buffer = new StringBuffer("{");
        for (Map.Entry<K, BigDecimal> entry : assetsMap.entrySet()) {
            buffer.append("\"").append(entry.getKey()).append("\"").append(":").append(NumberUtil.isNullDefaultZero(entry.getValue()).toPlainString()).append(",");
        }
        buffer.deleteCharAt(buffer.length() - 1);
        buffer.append("}");
        return buffer.toString();
    }


    /**
     * 将Map<Long, List<BillBaseUserAssetsRsp>>转换为JSON字符串
     *
     * @param billBaseUserAssetsRspMap 待转换的Map
     * @return 转换后的JSON字符串
     */
    public static String toJSONString(Map<Long, List<ReconBillBaseUserAssetsRsp>> billBaseUserAssetsRspMap) {
        if (MapUtils.isEmpty(billBaseUserAssetsRspMap)) {
            return "{}";
        }

        StringBuilder buffer = new StringBuilder("{");
        List<ReconBillBaseUserAssetsRsp> defaultReconBillBaseUserAssetsRspList = new ArrayList<>();
        for (Map.Entry<Long, List<ReconBillBaseUserAssetsRsp>> entry : billBaseUserAssetsRspMap.entrySet()) {
            Long userId = entry.getKey();
            List<ReconBillBaseUserAssetsRsp> userAssetsList = Optional.ofNullable(entry.getValue()).orElse(defaultReconBillBaseUserAssetsRspList);
            buffer.append("\"").append(userId * BillConstants.ONE_THOUSAND_TWENTY_FOUR).append("\":[");
            for (ReconBillBaseUserAssetsRsp userAssets : userAssetsList) {
                buffer.append("{")
                .append("\"marginFullNetBalance\":").append(userAssets.getMarginFullNetBalance()).append(",")
                .append("\"marginOneNetBalance\":").append(userAssets.getMarginOneNetBalance()).append(",")
                .append("\"userId\":").append(userAssets.getUserId() * BillConstants.ONE_THOUSAND_TWENTY_FOUR).append(",")
                .append("\"coinId\":").append(userAssets.getCoinId()).append(",")
                .append("\"coinName\":").append("\"").append(StringUtils.defaultIfBlank(userAssets.getCoinName(), BillConstants.EMPTY)).append("\"").append(",")
                .append("\"snapshotTime\":").append(userAssets.getSnapshotTime()).append(",")
                .append("\"spotBalance\":").append(userAssets.getSpotBalance()).append(",")
                .append("\"mixUBalance\":").append(userAssets.getMixUBalance()).append(",")
                .append("\"mixBBalance\":").append(userAssets.getMixBBalance()).append(",")
                .append("\"mixCBalance\":").append(userAssets.getMixCBalance()).append(",")
                .append("\"marginFullBalance\":").append(userAssets.getMarginFullBalance()).append(",")
                .append("\"marginOneBalance\":").append(userAssets.getMarginOneBalance()).append(",")
                .append("\"marginFullBorrowBalance\":").append(userAssets.getMarginFullBorrowBalance()).append(",")
                .append("\"marginOneBorrowBalance\":").append(userAssets.getMarginOneBorrowBalance()).append(",")
                .append("\"financialBalance\":").append(userAssets.getFinancialBalance()).append(",")
                .append("\"p2pBalance\":").append(userAssets.getP2pBalance()).append(",")
                .append("\"rate\":").append(userAssets.getRate())
                .append("},");
            }
            // 移除最后一个逗号
            if(CollectionUtils.isNotEmpty(userAssetsList)){
                buffer.deleteCharAt(buffer.length() - 1);
            }
            buffer.append("],");
        }

        // 移除最后一个逗号
        buffer.deleteCharAt(buffer.length() - 1);
        buffer.append("}");
        return buffer.toString();
    }
}
