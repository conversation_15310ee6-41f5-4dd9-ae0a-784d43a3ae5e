package com.upex.reconciliation.service.business;

import com.upex.reconciliation.service.business.ruleengine.dto.*;
import com.upex.reconciliation.service.dao.entity.BillBizTypeConfig;
import com.upex.reconciliation.service.dao.entity.BillFlowCheckConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.ticker.facade.dto.PriceVo;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 规则引擎数据服务接口 所有数据访问建议写在本类，方便后续代码维护
 */
public interface RuleEngineDataService {
    /**
     * 获取业务线业务
     *
     * @param currentBill
     * @return
     */
    BigDecimal getPropSumByBillChangeData(CommonBillChangeData currentBill);

    Date getCheckTime(SystemUserRelationshipDto systemUserRelationshipDto);

    /**
     * 获取用户开户资产数据
     *
     * @param userId
     * @param accountType
     * @param beginTime
     * @return
     */
    BigDecimal getBeginAssetsData(Long userId, String accountType, Date beginTime);

    BigDecimal getUserAssetsData(Long userId, List<String> accountType, Date beginTime);

    List<SystemUserAssetsRatioMonitorDto> calculateUserAssetsURatioMonitor(List<SystemUserAssetsRatioParam> systemUserAssetsRatioParamList);

    /**
     * 获取用户资产监控数据
     *
     * @param timeWindowsFlowsList
     * @return
     */
    List<SystemUserAssetsMonitorDto> calculateUserAssetsMonitorData(List<TimeWindowsFlows> timeWindowsFlowsList);

    /**
     * 获取指定窗口内用户流水
     *
     * @param systemUserRelationshipDto
     * @param endTime
     * @return
     */
    TimeWindowsFlows getTimeWindowsUserFlows(SystemUserRelationshipDto systemUserRelationshipDto, Date endTime, Integer windowIntervalTime);

    /**
     * 根据业务线获取业务类型配置
     *
     * @param accountType
     * @return
     */
    Map<String, BillBizTypeConfig> getBusinessBizTypeConfigMap(Byte accountType);

    /**
     * 获取用户快照资产
     *
     * @param userId
     * @param accountTypeList
     * @param snapshotTime
     * @return
     */
    Map<Integer, BigDecimal> getUserSnapshotAssets(Long userId, List<Byte> accountTypeList, Date snapshotTime, List<Integer> coinIdsList);

    /**
     * 获取用户快照USDT资产
     *
     * @param userId
     * @param accountTypeList
     * @param snapshotTime
     * @return
     */
    BigDecimal getUserSnapshotAssetsUsdt(Long userId, List<Byte> accountTypeList, Date snapshotTime, List<Integer> coinIdsList);

    /**
     * 获取用户待动账资产
     *
     * @param userId
     * @param toAccountTypeList
     * @param transferTypes
     * @param snapshotTime
     * @return
     */
    Map<Integer, BigDecimal> getUserUnProfitTransferAssets(Long userId, List<Integer> toAccountTypeList, List<Integer> transferTypes, Date snapshotTime);

    /**
     * 计算币种快照USDT资产
     *
     * @param coinAssetsMap
     * @param snapshotTime
     * @return
     */
    BigDecimal calculateCoinSnapshotAssetsUsdt(Map<Integer, BigDecimal> coinAssetsMap, Date snapshotTime);

    /**
     * 获取币种快照资产
     *
     * @param coinIdsList
     * @param snapshotTime
     * @return
     */
    Map<Integer, BigDecimal> getCoinSnapshotAssets(String assetsType, Date snapshotTime, List<Integer> coinIdsList);

    /**
     * 获取币种类型快照资产
     *
     * @param assetsType
     * @param snapshotTime
     * @param coinIdsList
     * @param bizTypeList
     * @return
     */
    Map<Integer, BigDecimal> getCoinTypeSnapshotAssets(String assetsType, Date snapshotTime, List<Integer> coinIdsList, List<String> bizTypeList);

    /**
     * 根据业务线获取业务类型配置
     *
     * @param accountType
     * @return
     */
    List<String> getBizInTypeList(Byte accountType);

    /**
     * 根据业务线获取业务类型配置
     *
     * @param accountType
     * @return
     */
    List<String> getBizOutTypeList(Byte accountType);

    /**
     * 获取用户资产流水
     *
     * @param currentBill
     * @return
     */
    BigDecimal getChangePropSumByBillChangeData(CommonBillChangeData currentBill);

    /**
     * 是否demo用户
     *
     * @param userId
     * @return
     */
    Boolean isDemoUser(Long userId);

    /**
     * 是否系统用户
     *
     * @param userId
     * @return
     */
    Boolean isSysUser(Long userId);

    /**
     * 获取系统用户展示字段
     * @param userId
     * @return
     */
    String getSysUserDisplayStr(Long userId);

    /**
     * 获取币种在指定时间的价格
     *
     * @param snapshotTime:
     * @return :
     */
    Map<Integer, PriceVo> getCoinIdRatesMapCache(Date snapshotTime);

    /**
     * 获取币种全部币种
     *
     * @param snapshotTime:
     * @return :
     */
    Map<Integer, String> getAllCoinsMapCache(Date snapshotTime);

    /**
     * 是否pap用户
     *
     * @param userId
     * @return
     */
    Boolean isPapUser(Long userId);

    /**
     * 获取用户业务类型对应手续费
     *
     * @param accountTypeList
     * @param checkTime
     * @param snapshotTime
     * @param coinIdsList
     * @return
     */
    Map<Integer, BigDecimal> getAccountTypeFeeTotalUsdt(List<Byte> accountTypeList, Date checkTime, Date snapshotTime, List<Integer> coinIdsList);

    /**
     * 根据业务线获取业务类型出入集合
     *
     * @param accountType
     * @return
     */
    Set<String> getBizTypeInOutSet(byte accountType);

    /**
     * 业务类型出入存储或者累加
     *
     * @param commonBillChangeData
     */
    BigDecimal storeOrSumInOut(CommonBillChangeData commonBillChangeData);

    /**
     * 获取用户change资产
     *
     * @param userId
     * @param accountTypeList
     * @param bizTypeList
     * @param startTime
     * @param endTime
     * @return
     */
    Map<Byte, BigDecimal> getUserChangePropSum(Long userId, List<Byte> accountTypeList, List<String> bizTypeList, List<Integer> coinIdsList, Date startTime, Date endTime);

    /**
     * 获取当前用户划转至的用户金额
     *
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    Map<Long, BigDecimal> getTransferAmount(Long userId, Date startTime, Date endTime);

    /**
     * 根据业务线及业务类型获取权限code
     *
     * @param commonBillChangeData
     * @return
     */
    Pair<List<Long>, String> getPermissionCodes(CommonBillChangeData commonBillChangeData);

    /**
     * 校验用户权限
     *
     * @param userId
     * @param permissionCode
     */
    Pair<Boolean, String> checkUserPermission(Long userId, Long permissionCode);

    /**
     * 校验用户权限列表
     *
     * @param userId
     * @param permissionCodeList
     */
    Pair<Boolean, String> checkUserPermissions(Long userId, List<Long> permissionCodeList);

    /**
     * 根据业务线及业务类型获取子账户配置
     *
     * @param accountType
     * @param bizType
     */
    BillFlowCheckConfig getSubAccountConfig(byte accountType, String bizType);

    /**
     * 是否子账户
     *
     * @param userId
     */
    Boolean isSubAccount(Long userId);

    /**
     * 是否是nd broker子账户
     *
     * @param userId
     */
    Boolean isBroker(Long userId);

}