package com.upex.reconciliation.service.business;

import com.github.rholder.retry.RetryException;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * 查询用户资产
 * <AUTHOR>
 */
public interface AssetsQueryService {

    /**
     * 查询用户资产
     * @param userId 用户Id
     * @param accountTypeEnum 业务类型
     * @return 返回用户资产列表
     * @throws ExecutionException 系统异常
     * @throws RetryException 重试异常
     */
    List<AccountAssetsInfoResult> queryUserAssets(Long userId, AccountTypeEnum accountTypeEnum, GlobalBillConfig globalBillConfig) throws ExecutionException, RetryException;

}
