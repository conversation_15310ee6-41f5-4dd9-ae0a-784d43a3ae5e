package com.upex.reconciliation.service.service;

import com.google.common.collect.Lists;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillUserWithdrawProfitRecord;
import com.upex.reconciliation.service.dao.entity.ReconWithdrawCheckRecord;
import com.upex.reconciliation.service.dao.mapper.BillUserWithdrawProfitRecordMapper;
import com.upex.reconciliation.service.dao.mapper.ReconWithdrawCheckRecordMapper;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.upex.reconciliation.service.common.constants.enums.BillRedisKeyEnum.RECON_WITHDRAWAL_CHECK_FINAL_RESULT;

@Slf4j
@Service
public class BillUserWithdrawProfitRecordService implements BillUserWithdrawProfitRecordMapper{

    @Resource
    private BillDbHelper billDbHelper;


    @Resource
    BillUserWithdrawProfitRecordMapper billUserWithdrawProfitRecordMapper;


    public void batchSaveWithdrawProfitResult(List<BillUserWithdrawProfitRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        if (!globalBillConfig.isSaveProfitRecord()) {
            return;
        }
        List<List<BillUserWithdrawProfitRecord>> billUserWithdrawProfitRecordList = Lists.partition(records, globalBillConfig.getInsertSqlSize());
        for (List<BillUserWithdrawProfitRecord> subRecords : billUserWithdrawProfitRecordList) {
            batchInsert(subRecords);
        }
    }

    public int batchInsert(List<BillUserWithdrawProfitRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }
        return billDbHelper.doDbOpInSnapshotGlobalMaster(() -> billUserWithdrawProfitRecordMapper.batchInsert(records));
    }

    public int insert(BillUserWithdrawProfitRecord record) {
        if (record == null) {
            return 0;
        }
        return billDbHelper.doDbOpInSnapshotGlobalMaster(() -> billUserWithdrawProfitRecordMapper.insert(record));
    }


    @Override
    public List<Long> getUserIdsIdRange(Long startId, Long endId) {
        if (startId == null || endId == null) {
            return Collections.EMPTY_LIST;
        }
        return billDbHelper.doDbOpInSnapshotGlobalMaster(() -> billUserWithdrawProfitRecordMapper.getUserIdsIdRange(startId, endId));
    }

    @Override
    public Long selectMaxId(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return 0L;
        }
        return billDbHelper.doDbOpInSnapshotGlobalMaster(() -> billUserWithdrawProfitRecordMapper.selectMaxId(startDate, endDate));
    }

    @Override
    public Long selectMinId(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return 0L;
        }
        return billDbHelper.doDbOpInSnapshotGlobalMaster(() -> billUserWithdrawProfitRecordMapper.selectMinId(startDate, endDate));
    }
}

