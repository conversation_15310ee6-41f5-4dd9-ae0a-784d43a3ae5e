package com.upex.reconciliation.service.service.client.cex.client;


import com.upex.reconciliation.service.service.client.cex.dto.req.common.CommonReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;

public abstract class AbstractCexApiClient implements ICexApiClient{

    @Override
    public CommonRes querySpotCoinAsset(CommonReq commonReq) {
        return null;
    }

    @Override
    public CommonRes queryFundingCoinAsset(CommonReq commonReq) {
        return null;
    }

    @Override
    public CommonRes queryUContractCoinAsset(CommonReq commonReq) {
        return null;
    }

    @Override
    public CommonRes queryCoinContractCoinAsset(CommonReq commonReq) {
        return null;
    }

    @Override
    public CommonRes queryMarginCoinAsset(CommonReq commonReq) {
        return null;
    }

    @Override
    public CommonRes queryIsolatedMarginCoinAsset(CommonReq commonReq) {
        return null;
    }

    @Override
    public CommonRes queryFlexEarnPosition(CommonReq commonReq) {
        return null;
    }

    @Override
    public CommonRes queryLockedEarnPosition(CommonReq commonReq) {
        return null;
    }

    @Override
    public CommonRes queryApikeyPermission(CommonReq commonReq) {
        return null;
    }

    @Override
    public CommonRes queryUserStatus(CommonReq commonReq) {
        return null;
    }

    @Override
    public Integer cexType() {
        return null;
    }
}
