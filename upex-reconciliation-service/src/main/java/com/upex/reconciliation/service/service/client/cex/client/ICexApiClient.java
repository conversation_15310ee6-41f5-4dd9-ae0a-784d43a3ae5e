package com.upex.reconciliation.service.service.client.cex.client;

import com.upex.reconciliation.service.service.client.cex.dto.req.common.CommonReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import org.checkerframework.checker.units.qual.C;

public interface ICexApiClient extends ICexApi{


    CommonRes querySpotCoinAsset(CommonReq commonReq);

    CommonRes queryFundingCoinAsset(CommonReq commonReq);

    CommonRes queryUContractCoinAsset(CommonReq commonReq);

    CommonRes queryCoinContractCoinAsset(CommonReq commonReq);

    CommonRes queryMarginCoinAsset(CommonReq commonReq);

    CommonRes queryIsolatedMarginCoinAsset(CommonReq commonReq);

    CommonRes queryFlexEarnPosition(CommonReq commonReq);

    CommonRes queryLockedEarnPosition(CommonReq commonReq);

    CommonRes queryApikeyPermission(CommonReq commonReq);

    CommonRes queryUserStatus(CommonReq commonReq);

    CommonRes querySubUserList(CommonReq commonReq);

    CommonRes querySpotPosition(CommonReq commonReq);

    CommonRes querySubUserSpotAsset(CommonReq commonReq);

    CommonRes querySubUserMarginAsset(CommonReq commonReq);

    CommonRes querySubUserUContractAsset(CommonReq commonReq);

    CommonRes querySubUserCoinContractAsset(CommonReq commonReq);

    CommonRes querySupportCoinList(CommonReq commonReq);

    CommonRes queryDepositeAddress(CommonReq commonReq);

    CommonRes  queryUserDepositeHistory(CommonReq commonReq);

    CommonRes  queryUserWithdrawHistory(CommonReq commonReq);

    CommonRes queryPayTransferHistory(CommonReq commonReq);

    CommonRes queryParentSubTransferRecord(CommonReq commonReq);

    CommonRes queryUniversialTransferList(CommonReq commonReq);


}
