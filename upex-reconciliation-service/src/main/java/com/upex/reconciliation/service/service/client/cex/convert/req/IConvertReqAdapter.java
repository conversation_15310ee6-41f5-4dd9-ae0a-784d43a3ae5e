package com.upex.reconciliation.service.service.client.cex.convert.req;

import com.upex.reconciliation.service.service.client.cex.dto.req.CexApiBaseReq;
import com.upex.reconciliation.service.service.client.cex.dto.req.binance.BinanceApiBaseReq;
import com.upex.reconciliation.service.service.client.cex.dto.req.binance.BinanceSubUserApiReq;
import com.upex.reconciliation.service.service.client.cex.dto.req.binance.BinanceSubUserContractApiReq;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CommonReq;
import com.upex.reconciliation.service.service.client.cex.client.ICexApi;

public interface IConvertReqAdapter<SQ extends CommonReq, TQ extends CexApiBaseReq> extends ICexApi {

    TQ defaultConvertReq(SQ sourceReq);

    BinanceApiBaseReq convertSubUserApiReq(CommonReq commonReq);

    BinanceApiBaseReq convertSubUserCoinContractAssetReq(CommonReq commonReq);

    BinanceApiBaseReq convertSubUserUContractAssetReq(CommonReq commonReq);

    BinanceApiBaseReq convertDepositeAddressApiReq(CommonReq commonReq);

    BinanceApiBaseReq convertDepositeHistoryApiReq(CommonReq commonReq);

    BinanceApiBaseReq convertWithdrawHistoryApiReq(CommonReq commonReq);

    BinanceApiBaseReq convertPayTransferHistoryApiReq(CommonReq commonReq);

    BinanceApiBaseReq convertParentSubTransferRecordApiReq(CommonReq commonReq);

    BinanceApiBaseReq convertUniversialTransferListApiReq(CommonReq commonReq);
}
