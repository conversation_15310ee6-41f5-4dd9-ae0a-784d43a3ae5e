package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.client.kafka.MessageDeserializer;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.alibaba.otter.canal.protocol.Message;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.util.concurrent.RateLimiter;
import com.upex.mixcontract.common.literal.enums.DelegateTypeEnum;
import com.upex.mixcontract.common.literal.enums.DepthSideEnum;
import com.upex.mixcontract.common.literal.enums.SecondBusinessLineEnum;
import com.upex.reconciliation.facade.enums.SQLTypeEnum;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.business.module.impl.BillOrderCheckModule;
import com.upex.reconciliation.service.common.RateLimiterManager;
import com.upex.reconciliation.service.common.constants.ReconciliationCommandEnum;
import com.upex.reconciliation.service.common.constants.enums.LogLevelEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.entity.OrderBillConfig;
import com.upex.reconciliation.service.model.BillCmdWrapper;
import com.upex.reconciliation.service.model.config.ApolloBillOrderDetailConfig;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.model.dto.AccountDealtRecordData;
import com.upex.reconciliation.service.service.BillAllConfigService;
import com.upex.reconciliation.service.service.OrderBillConfigService;
import com.upex.reconciliation.service.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.KAFKA_DEALT_CONSUMER_DELETE_DATA;
import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.KAFKA_POLL_DEALT_CONSUMER_ERROR;
import static com.upex.reconciliation.service.utils.MetricsUtil.HISTOGRAM_KAFKA_CONSUMER;

/**
 * @Description: kafka消费者消费消息, 手动同步提交offset
 **/

@Slf4j
public class AccountDealtRecordConsumerRunnable implements KafkaConsumerLifecycle {
    private List<KafkaConsumer<String, Message>> consumerList;
    private Byte accountType;
    private volatile boolean running = true;
    private Map<String, Object> consumerConfig;
    private String topic;
    private String groupId;
    private Integer partitionNum;
    private String kafkaConsumerKey;
    private Map<Integer, KafkaConsumer<String, Message>> partitionConsumerMap = new HashMap<>();
    private Set<Integer> closeConsumerPatition = new HashSet<>();
    private Map<Integer, Long> partitionOffsetMap = new ConcurrentHashMap<>();
    private AlarmNotifyService alarmNotifyService;
    private RedisTemplate<String, Object> redisTemplate;
    private BillEngineManager billEngineManager;
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    private BillAllConfigService billAllConfigService;
    private Map<Integer, RateLimiter> partitionRateLimiterMap = new ConcurrentHashMap<>();
    /***业务key****/
    private String businessKey;
    private OrderBillConfigService orderBillConfigService;
    /***分区业务时间***/
    private Map<Integer, Long> partitionTimeSliceKeyMap = new ConcurrentHashMap<>();
    /***幂等***/
    private LoadingCache<String, Boolean> idempotentLoadingCache;

    public AccountDealtRecordConsumerRunnable(ReconciliationSpringContext context, String kafkaServers, KafkaConsumerConfig kafkaConsumerConfig, BillEngineManager billEngineManager, String kafkaConsumerKey) {
        this.businessKey = kafkaConsumerConfig.getBusinessKey();
        this.accountType = Byte.valueOf(kafkaConsumerConfig.getAccountType());
        this.topic = kafkaConsumerConfig.getTopicName();
        this.groupId = EnvUtil.getKafkaConsumerGroup(kafkaConsumerConfig.getConsumerGroupId());
        this.partitionNum = kafkaConsumerConfig.getPartitionNum();
        this.consumerList = new ArrayList<>();
        this.alarmNotifyService = context.getAlarmNotifyService();
        this.redisTemplate = context.getRedisTemplate();
        this.billEngineManager = billEngineManager;
        this.accountAssetsServiceFactory = context.getAccountAssetsServiceFactory();
        this.billAllConfigService = context.getBillAllConfigService();
        this.orderBillConfigService = context.getOrderBillConfigService();
        this.kafkaConsumerKey = kafkaConsumerKey;
        consumerConfig = new HashMap<String, Object>();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServers);
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MessageDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, this.groupId);
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, kafkaConsumerConfig.getKafkaBatchSize());
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
        idempotentLoadingCache = Caffeine.newBuilder()
                .expireAfterWrite(kafkaConsumerConfig.getIdempotentCacheExpireMinutes(), TimeUnit.MINUTES)
                .build(new CacheLoader<String, Boolean>() {
                    @Override
                    public @Nullable Boolean load(@NonNull String key) throws Exception {
                        return false;
                    }
                });
    }

    @Override
    public void run() {
        // 初始化
        log.info("AccountDealtRecordConsumerRunnable consumerRunnables.run");
        init();
        log.info("AccountDealtRecordConsumerRunnable init finished");
        for (Map.Entry<Integer, KafkaConsumer<String, Message>> entry : partitionConsumerMap.entrySet()) {
            new Thread(() -> {
                try {
                    startConsume(entry.getKey(), entry.getValue());
                } catch (Exception e) {
                    log.error("AccountDealtRecordConsumerRunnable.startConsume error accountType {} partition {}", accountType, entry.getKey(), e);
                }
            }, "kafka-consumer-" + accountType + "-" + entry.getKey()).start();
        }
    }


    /**
     * 初始化kafka
     */
    private void init() {
        KafkaConsumer<String, Message> consumer = new KafkaConsumer<String, Message>(consumerConfig);
        try {
            consumer.subscribe(Arrays.asList(topic));
            Set<TopicPartition> assignment = new HashSet<>();
            while (assignment.size() == 0) {
                consumer.poll(Duration.ofSeconds(3));
                assignment = consumer.assignment();
                log.info("AccountDealtRecordConsumerRunnable try consumer.assignment {} {} {}", accountType, topic, groupId);
            }
            ApolloBillOrderDetailConfig apolloBillOrderDetailConfig = ReconciliationApolloConfigUtils.getApolloBillOrderDetailConfig(this.businessKey);
            OrderBillConfig orderBillConfig = orderBillConfigService.getOrderBillConfig(apolloBillOrderDetailConfig.getOrderType(), apolloBillOrderDetailConfig.getOrderParam());
            BillOrderCheckModule billOrderCheckModule = billEngineManager.getBillOrderCheckModule(this.businessKey);
            billOrderCheckModule.setLastCheckTime(orderBillConfig.getCheckOkTime());
            // 重置消费位点
            Map<TopicPartition, OffsetAndMetadata> offsetMap = new HashMap<>();
            if (orderBillConfig != null) {
                Long pullTime = orderBillConfig.getCheckOkTime().getTime() - apolloBillOrderDetailConfig.getUserProfitPullKafkaMessageBackTime();
                Map<TopicPartition, Long> topicPartitionLongMap = new HashMap<>();
                for (TopicPartition partitionInfo : assignment) {
                    topicPartitionLongMap.put(partitionInfo, pullTime);
                }
                Map<TopicPartition, Long> endOffsets = consumer.endOffsets(assignment);
                Map<TopicPartition, Long> beginningOffsets = consumer.beginningOffsets(assignment);
                Map<TopicPartition, OffsetAndTimestamp> offsetsForTimes = consumer.offsetsForTimes(topicPartitionLongMap);
                for (Map.Entry<TopicPartition, OffsetAndTimestamp> entry : offsetsForTimes.entrySet()) {
                    long offset = entry.getValue() != null ? entry.getValue().offset() : endOffsets.get(entry.getKey());
                    offsetMap.put(entry.getKey(), new OffsetAndMetadata(offset));
                }
                log.info("AccountDealtRecordConsumerRunnable offset info beginningOffsets={} endOffsets={} offsetsForTimes={}",
                        beginningOffsets, endOffsets, offsetsForTimes);
            }
            if (offsetMap.size() > 0) {
                for (Map.Entry<TopicPartition, OffsetAndMetadata> entry : offsetMap.entrySet()) {
                    consumer.seek(entry.getKey(), entry.getValue().offset());
                }
                consumer.commitSync();
            }
            log.info("AccountDealtRecordConsumerRunnable finished start topic: {} config:{} offsetMap:{}",
                    topic, JSONObject.toJSONString(consumerConfig), offsetMap);
        } catch (Exception e) {
            log.error("AccountDealtRecordConsumerRunnable.init reset offset error ", e);
        } finally {
            consumer.close();
        }
        KafkaConsumerConfig kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig().getConsumerConfig().get(this.kafkaConsumerKey);
        // 创建监听器，数量和partition数量保持一致
        for (int i = 0; i < partitionNum; i++) {
            KafkaConsumer<String, Message> currentConsumer = new KafkaConsumer<String, Message>(consumerConfig);
            List<TopicPartition> partitions = new ArrayList<>();
            partitions.add(new TopicPartition(topic, i));
            currentConsumer.assign(partitions);
            partitionRateLimiterMap.put(i, RateLimiter.create(kafkaConsumerConfig.getMsgRateLimit()));
            partitionConsumerMap.put(i, currentConsumer);
        }
    }

    private void startConsume(Integer partition, KafkaConsumer<String, Message> consumer) {
        log.info("AccountDealtRecordConsumerRunnable consumerRunnables.run accounType {} partition {}", accountType, partition);
        while (running) {
            try {
                // 从kafka集群中拉取消息df
                ConsumerRecords<String, Message> consumerRecords = consumer.poll(3000);
                AtomicInteger bizMessageSizeCounter = new AtomicInteger(0);
                consumerRecords.forEach(new java.util.function.Consumer<ConsumerRecord<String, Message>>() {
                    @Override
                    public void accept(ConsumerRecord<String, Message> consumerRecord) {
                        MetricsUtil.histogram(HISTOGRAM_KAFKA_CONSUMER + accountType + "_" + partition, () -> {
                            partitionOffsetMap.put(partition, consumerRecord.offset());
                            Long consumerTimestamp = System.currentTimeMillis();
                            List<FlatMessage> flatMessages = canalMessageDecode(consumerRecord.value());
                            Integer bizMessageSize = handle(accountType, flatMessages, consumerRecord.offset(), consumerRecord.partition(), consumerRecord.timestamp(), consumerTimestamp);
                            bizMessageSizeCounter.addAndGet(bizMessageSize);
                        });
                    }
                });
                consumer.commitSync();
                int messageCount = consumerRecords.count();
                if (messageCount > 0) {
                    // 限流逻辑
                    partitionRateLimiterMap.get(partition).acquire(messageCount);
                    KafkaConsumerConfig kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig().getConsumerConfig().get(this.kafkaConsumerKey);
                    // 判断partition 水位差 限流 没有触发限流判断则恢复限流
                    Long minTime = partitionTimeSliceKeyMap.values().stream().min(Comparator.comparing(x -> x)).orElse(0L);
                    Long maxTime = partitionTimeSliceKeyMap.values().stream().max(Comparator.comparing(x -> x)).orElse(0L);
                    if (maxTime - minTime > kafkaConsumerConfig.getPartitionWaterHeadLimit()) {
                        partitionRateLimiterMap.get(partition).setRate(RateLimiterManager.ERROR_LIMIT_RATE);
                        log.info("AccountDealtRecordConsumerRunnable limit rateLimiter accountType:{} partition:{} partitionInfo:{}", accountType, partition, getPartitionTimeLogStr(partitionTimeSliceKeyMap));
                        partitionRateLimiterMap.get(partition).setRate(kafkaConsumerConfig.getMsgRateLimit());
                    } else {
                        double rate = partitionRateLimiterMap.get(partition).getRate();
                        if (rate != kafkaConsumerConfig.getMsgRateLimit()) {
                            log.info("AccountDealtRecordConsumerRunnable recover rateLimiter accountType:{} partition:{} rate:{} newRate:{} partitionInfo:{}", accountType, partition, rate, kafkaConsumerConfig.getMsgRateLimit(), getPartitionTimeLogStr(partitionTimeSliceKeyMap));
                            partitionRateLimiterMap.get(partition).setRate(kafkaConsumerConfig.getMsgRateLimit());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("AccountDealtRecordConsumerRunnable startConsume error accountType {} partition {} error:{}", accountType, partition, e.getMessage(), e);
                alarmNotifyService.alarm(accountType, KAFKA_POLL_DEALT_CONSUMER_ERROR, accountType, partition);
            }
        }
        consumer.close();
        closeConsumerPatition.add(partition);
        log.info("AccountDealtRecordConsumerRunnable consumer.close success {} {}", accountType, partition);
    }

    /**
     * 消息处理
     *
     * @param accountType
     * @param flatMessages
     * @param offset
     * @param partition
     * @param kafkaTimestamp
     * @param consumerTimestamp
     * @return
     */
    public Integer handle(Byte accountType, List<FlatMessage> flatMessages, Long offset, Integer partition, Long kafkaTimestamp, Long consumerTimestamp) {
        Integer bizMessageSize = 0;
        for (FlatMessage flatMessage : flatMessages) {
            List<AccountDealtRecordData> commonBillChangeDataList = buildBillChangeDataList(accountType, flatMessage, offset, partition);
            if (CollectionUtils.isNotEmpty(commonBillChangeDataList)) {
                bizMessageSize += commonBillChangeDataList.size();
                for (AccountDealtRecordData commonBillChangeData : commonBillChangeDataList) {
                    commonBillChangeData.setPartition(partition);
                    commonBillChangeData.setKafkaTimestamp(kafkaTimestamp);
                    commonBillChangeData.setConsumerTimestamp(consumerTimestamp);
                    commonBillChangeData.setOffset(offset);
                    processBusiness(commonBillChangeData);
                }
            }
        }
        return bizMessageSize;
    }

    /**
     * 消息解析
     *
     * @param accountType
     * @param flatMessage
     * @param offset
     * @param partition
     * @return
     */
    private List<AccountDealtRecordData> buildBillChangeDataList(Byte accountType, FlatMessage flatMessage, Long offset, Integer partition) {
        String typeStr = flatMessage.getType();
        boolean isDdl = flatMessage.getIsDdl();
        if (isDdl) {
            return Collections.emptyList();
        }
        SQLTypeEnum sqlTypeEnum = SQLTypeEnum.convert(typeStr);
        if (null == sqlTypeEnum) {
            log.error("AccountDealtRecordConsumerRunnable unable to resolve sqlType:{}", typeStr);
            return Collections.emptyList();
        }
        List<Map<String, String>> dataList = flatMessage.getData();
        switch (sqlTypeEnum) {
            case DELETE:
                log.error("AccountDealtRecordConsumerRunnable.buildBillChangeDataList UPDATE accountType:{} newData:{} oldData:{}", accountType, JSON.toJSONString(dataList));
                if (CollectionUtils.isNotEmpty(dataList)) {
                    for (Map<String, String> message : dataList) {
                        String userId = getMessageUserId(message);
                        String id = getMessageId(message);
                        alarmNotifyService.alarm(accountType, KAFKA_DEALT_CONSUMER_DELETE_DATA, accountType, userId, id);
                    }
                }
                return Collections.emptyList();
            case UPDATE:
                List<Map<String, String>> oldDataList = flatMessage.getOld();
                log.error("AccountDealtRecordConsumerRunnable.buildBillChangeDataList UPDATE accountType:{} newData:{} oldData:{}", accountType, JSON.toJSONString(dataList), JSONObject.toJSONString(oldDataList));
                return Collections.emptyList();
            case INSERT:
                List<AccountDealtRecordData> list = messageDecode(dataList);
                ApolloBillOrderDetailConfig apolloBillOrderDetailConfig = ReconciliationApolloConfigUtils.getApolloBillOrderDetailConfig(this.businessKey);
                BizLogUtils.log(LogLevelEnum.FULL, apolloBillOrderDetailConfig.getDefaultLogLeve(), "AccountDealtRecordConsumerRunnable KafkaMsgDecoder accountType {} partition:{}, offset:{} changelist:{} ", accountType, partition, offset, JSONObject.toJSONString(list));
                return list;
        }
        return Collections.emptyList();
    }

    /**
     * 消息解析
     *
     * @param dataList
     * @return
     */
    private List<AccountDealtRecordData> messageDecode(List<Map<String, String>> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        List<AccountDealtRecordData> list = new ArrayList<>();
        for (Map<String, String> data : dataList) {
            SecondBusinessLineEnum secondBusinessLine = SecondBusinessLineEnum.toEnumByCode(Integer.parseInt(data.get("second_business_line")));
            DelegateTypeEnum delegateType = DelegateTypeEnum.toEnum(Integer.parseInt(data.get("delegate_type")));
            DepthSideEnum depthSideEnum = delegateType.getDepthSideEnum();
            Long userId = Long.parseLong(data.get("account_id"));
            String symbolId = data.get("symbol_id");
            Integer baseTokenId = Integer.parseInt(data.get("base_token_id"));
            Integer quoteTokenId = Integer.parseInt(data.get("quote_token_id"));
            BigDecimal dealtPrice = new BigDecimal(data.get("dealt_price"));
            BigDecimal dealtCount = new BigDecimal(data.get("dealt_count"));
            BigDecimal dealtAmount = new BigDecimal(data.get("dealt_amount"));
            BigDecimal profits = new BigDecimal(data.get("profits"));
            BigDecimal fee = new BigDecimal(data.get("fee"));
            Integer feeTokenId = Integer.parseInt(data.get("fee_token_id"));
            Date createTime = DateUtil.getMillisecondDate(data.get("create_time"));
            BigDecimal receivableFee = BigDecimal.ZERO;
            Boolean useDt = false;
            String feeDetail = data.get("fee_detail");
            if (StringUtils.isNotEmpty(feeDetail) && !feeDetail.equals("{}")) {
                Map<String, Object> feeDetailMap = JSON.parseObject(feeDetail, Map.class);
                receivableFee = Optional.ofNullable(feeDetailMap.get("tFee")).map(price -> new BigDecimal(price.toString())).orElse(BigDecimal.ZERO);
                useDt = Optional.ofNullable(feeDetailMap.get("useDt")).map(dt -> Boolean.parseBoolean(dt.toString())).orElse(false);
            }

            AccountDealtRecordData accountDealtRecordData = new AccountDealtRecordData();
            accountDealtRecordData.setBizId(Long.parseLong(data.get("id")));
            accountDealtRecordData.setAccountId(userId);
            accountDealtRecordData.setSecondBusinessLine(data.get("second_business_line"));
            accountDealtRecordData.setDelegateType(data.get("delegate_type"));
            accountDealtRecordData.setDepthSide(String.valueOf(depthSideEnum.getCode()));
            accountDealtRecordData.setSymbolId(symbolId);
            accountDealtRecordData.setBaseTokenId(baseTokenId);
            accountDealtRecordData.setQuoteTokenId(quoteTokenId);
            accountDealtRecordData.setDealtPrice(dealtPrice);
            accountDealtRecordData.setDealtCount(dealtCount);
            accountDealtRecordData.setDealtAmount(dealtAmount);
            accountDealtRecordData.setProfits(profits);
            accountDealtRecordData.setCreateTime(createTime);
            accountDealtRecordData.setBizTime(createTime);
            accountDealtRecordData.setFee(fee);
            accountDealtRecordData.setFeeTokenId(feeTokenId);
            accountDealtRecordData.setReceivableFee(receivableFee);
            accountDealtRecordData.setUseDt(useDt);
            list.add(accountDealtRecordData);
        }
        return list;
    }

    /**
     * 消息入队
     *
     * @param data
     */
    private void processBusiness(AccountDealtRecordData data) {
        // 幂等判断
        if (alreadyHasUniqueId(data.getAccountId(), data.getBizId())) {
            log.info("AccountDealtRecordConsumerRunnable KafkaMsgDecoder accountType:{} partition:{}, offset:{} orderType:{} data:{}", accountType, data.getPartition(), data.getOffset(), businessKey, JSONObject.toJSONString(data));
            return;
        }
        // 消息分发
        BillCmdWrapper billCmdWrapper = new BillCmdWrapper(ReconciliationCommandEnum.BILL_CHANGE, data);
        BillOrderCheckModule billOrderCheckModule = billEngineManager.getBillOrderCheckModule(this.businessKey);
        partitionTimeSliceKeyMap.put(data.getPartition(), data.getBizTime().getTime());
        billOrderCheckModule.offerCommand(billCmdWrapper);
    }

    /**
     * 获取消息userid
     *
     * @param message
     * @return
     */
    private String getMessageUserId(Map<String, String> message) {
        if (MapUtils.isEmpty(message)) {
            return null;
        }
        String userId = message.get("account_id");
        if (StringUtils.isEmpty(userId)) {
            userId = message.get("user_id");
        }
        return userId;
    }

    /**
     * 获取消息id
     *
     * @param message
     * @return
     */
    private String getMessageId(Map<String, String> message) {
        if (MapUtils.isEmpty(message)) {
            return null;
        }
        String id = message.get("id");
        if (StringUtils.isEmpty(id)) {
            id = message.get("bill_id");
        }
        return id;
    }

    /**
     * 获取partition时间日志
     *
     * @param partitionTimeSliceKeyMap
     * @return
     */
    private String getPartitionTimeLogStr(Map<Integer, Long> partitionTimeSliceKeyMap) {
        StringBuffer stringBuffer = new StringBuffer();
        for (Map.Entry<Integer, Long> entry : partitionTimeSliceKeyMap.entrySet()) {
            stringBuffer.append(entry.getKey()).append(":").append(DateUtil.longToDate(entry.getValue())).append("  ");
        }
        return stringBuffer.toString();
    }

    /**
     * 是否幂等
     *
     * @param userId
     * @param bizId
     * @return
     */
    public boolean alreadyHasUniqueId(Long userId, Long bizId) {
        String uniqueId = userId + "#" + bizId;
        Boolean result = idempotentLoadingCache.getIfPresent(uniqueId);
        if (result == null) {
            idempotentLoadingCache.put(uniqueId, true);
        }
        return result != null;
    }

    @Override
    public void shutdown() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return closeConsumerPatition.size() != partitionNum;
    }

    @Override
    public String getThreadPrefixName() {
        return "kafka-consumer-dealt-" + accountType;
    }
}


