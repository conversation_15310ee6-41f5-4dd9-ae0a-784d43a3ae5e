package com.upex.reconciliation.service.business.convert.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ConvertOrder implements Serializable{
    /**
     * orderId: 订单ID
     */
    private Long orderId;
    /**
     * clientOrderId: 三方订单号
     */
    private Long clientOrderId;
    /**
     * accountId: 账户ID
     */
    private Long accountId;
    /**
     * accountType: 账户类型 1-普通用户，其他-系统账户
     */
    private Integer accountType;
    /**
     * fromCoinId: 消耗币种ID
     */
    private Integer fromCoinId;
    /**
     * fromCoinCount: 消耗币数量
     */
    private BigDecimal fromCoinCount;
    /**
     * toCoinId: 获得币种ID
     */
    private Integer toCoinId;
    /**
     * toCoinCount: 获得币数量
     */
    private BigDecimal toCoinCount;
    /**
     * pc1: 成本价1
     */
    private BigDecimal pc1;
    /**
     * pt1: 利润价1
     */
    private BigDecimal pt1;
    /**
     * pc2: 成本价2
     */
    private BigDecimal pc2;
    /**
     * pt2: 利润价2
     */
    private BigDecimal pt2;
    /**
     * baseSpread1: 基础差幅1
     */
    private BigDecimal baseSpread1;
    /**
     * floatSpread1: 浮动差幅1
     */
    private BigDecimal floatSpread1;
    /**
     * baseSpread2: 基础差幅2
     */
    private BigDecimal baseSpread2;
    /**
     * floatSpread2: 浮动差幅2
     */
    private BigDecimal floatSpread2;
    /**
     * convertFeeRate: 固定手续费率
     */
    private BigDecimal convertFeeRate;
    /**
     * convertFeeRate2: 固定手续费率2
     */
    private BigDecimal convertFeeRate2;
    /**
     * quotedType: 报价类型 1-盘口 2-OSL
     */
    private Integer quotedType;
    /**
     * showFlag: 是否展示给用户 0否 1是
     */
    private Integer showFlag;
    /**
     * eps: 终端来源
     */
    private Integer eps;
    /**
     * status: 状态 0-初始化 1-F资产完成 3-T资产完成
     */
    private Integer status;
    /**
     * params: 扩展字段
     */
    private String params;
    /**
     * createTime: 创建时间
     */
    private Date createTime;
    /**
     * updateTime: 更新时间
     */
    private Date updateTime;
    /**
     * version: 数据版本字段
     */
    private Long version;

}
