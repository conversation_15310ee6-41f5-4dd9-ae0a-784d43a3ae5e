package com.upex.reconciliation.service.business.impl;

import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.MonitorCheckService;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.model.MonitorCmdWrapper;
import com.upex.reconciliation.service.model.param.BillMonitorData;
import com.upex.reconciliation.service.model.param.MonitorFeeAggregationData;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;

@Slf4j
@Service
public class FeeAggregationMonitorCheckServiceImpl implements MonitorCheckService {


    @Autowired
    private AlarmNotifyService alarmNotifyService;


    @Override
    public boolean processScene(Long sceneId, MonitorCmdWrapper monitorCmdWrapper) {
        BillMonitorData billAggregationMonitorData = monitorCmdWrapper.getAggregationMonitorData();
        boolean totalCheckResult = true;
        if (billAggregationMonitorData instanceof MonitorFeeAggregationData) {
            MonitorFeeAggregationData aggregationMonitorDatas = (MonitorFeeAggregationData) monitorCmdWrapper.getAggregationMonitorData();
            totalCheckResult = aggregationMonitorDatas.doCheck();
            if (!totalCheckResult) {
                // 推送聚合模板的告警，目前也是往<多维度指标对账告警>群进行推送
                alarmNotifyService.alarm(AlarmTemplateEnum.FEE_SCENE_CHECK, aggregationMonitorDatas.getAccountType(),
                        aggregationMonitorDatas.getCoinId(),
                        DateUtil.getDefaultDateStr(billAggregationMonitorData.getBizTime()),
                        aggregationMonitorDatas.getFeeCountChange().stripTrailingZeros().toPlainString(),
                        aggregationMonitorDatas.getVolumeCountChange().stripTrailingZeros().toPlainString(),
                        NumberUtil.divideAndStripZeroDefaultOneStr(aggregationMonitorDatas.getFeeCountChange(),
                                aggregationMonitorDatas.getVolumeCountChange(),
                                BillConstants.SERVER_DEFAULT_SCALE_SIX, RoundingMode.HALF_UP),
                        aggregationMonitorDatas.getMinTolerance().stripTrailingZeros().toPlainString(),
                        aggregationMonitorDatas.getMaxTolerance().stripTrailingZeros().toPlainString());
            }
        }
        return totalCheckResult;
    }


}
