package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.dao.bill.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.bill.entity.BillUser;
import com.upex.reconciliation.service.dao.mapper.OldBillUserMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OldBillUserService {


    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "oldBillUserMapper")
    private OldBillUserMapper oldBillUserMapper;


    public List<BillUser> selectUserIdList(Byte accountType, String accountParam, Long maxId, Integer pageSize) {
        return dbHelper.doDbOpInBillMaster(() -> oldBillUserMapper.selectUserIdList(accountType, accountParam, maxId, pageSize));
    }


    public static void main(String[] args) {
        BillCoinUserProperty billCoinUserProperty = new BillCoinUserProperty();
        // System.out.println(ClassLayout.parseInstance(billCoinUserProperty).toPrintable());

        UUID uuid = UUID.randomUUID();
        // System.out.println(uuid);

        BillUser billUser1 = new BillUser();
        billUser1.setId(1L);
        billUser1.setUserId(123L);
        BillUser billUser2 = new BillUser();
        billUser2.setId(2L);
        billUser2.setUserId(123L);
        BillUser billUser3 = new BillUser();
        billUser3.setId(3L);
        billUser3.setUserId(456L);


        BillUser billUser4 = new BillUser();
        billUser4.setId(4L);
        billUser4.setUserId(123L);

        BillUser billUser5 = new BillUser();
        billUser5.setId(5L);
        billUser5.setUserId(123L);


        List<BillUser> billUsers = new ArrayList<>();
        billUsers.add(billUser1);
        billUsers.add(billUser2);
        billUsers.add(billUser3);
        billUsers.add(billUser4);
        billUsers.add(billUser5);

        Map<Long, BillUser> map = billUsers.stream().collect(Collectors.toMap(BillUser::getUserId, Function.identity(), (key1, key2) -> key2));
        // System.out.println("test result is : " + JSONObject.toJSONString(map));
    }


}
