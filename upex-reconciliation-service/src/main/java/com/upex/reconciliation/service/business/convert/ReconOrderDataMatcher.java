package com.upex.reconciliation.service.business.convert;

import com.alibaba.fastjson.JSON;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderStausEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.business.convert.model.CombinedOrderData;
import com.upex.reconciliation.service.business.convert.model.ConvertBill;
import com.upex.reconciliation.service.business.convert.model.ConvertOrder;
import com.upex.reconciliation.service.dao.entity.ReconOrderFailureRecord;
import com.upex.reconciliation.service.model.config.ReconOrderConfig;
import com.upex.reconciliation.service.service.ReconOrderFailureRecordService;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import static com.upex.reconciliation.service.business.convert.ReconOrderConstants.*;

@Component
@Slf4j
public class ReconOrderDataMatcher {

    private static final DateTimeFormatter SHARD_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmm");

    // 在类定义中添加锁映射
    private final ConcurrentHashMap<String, Lock> orderLocks = new ConcurrentHashMap<>();

    @Resource
    private ReconOrderFailureRecordService reconOrderFailureRecordService;

    @Resource(name = "reconRedisTemplate")
    private RedisTemplate<String, Object> reconRedisTemplate;

    @Resource
    private ReconOrderDataProcessor orderDataProcessor;

    /**
     * 保存主订单数据
     *
     * @param orderData 主订单数据
     */
    public void saveMainOrderData(ConvertOrder orderData) {

        ReconOrderConfig reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
        if (orderData == null || orderData.getOrderId() == null||!reconOrderConfig.isOpen()) {
            log.warn("Invalid main order data: {}", orderData);
            return;
        }
        String orderId = String.valueOf(orderData.getOrderId());
        String key = MAIN_ORDER_PREFIX + orderId;
        String value = JSON.toJSONString(orderData);
        // 保存主订单数据到Redis
        reconRedisTemplate.opsForValue().set(key, value, reconOrderConfig.getOrderExpireTime(), TimeUnit.MINUTES);
        // 保存订单ID到索引
        saveMainOrderIndex(orderId,reconOrderConfig);
        log.info("Saved main order data: {}", orderId);
        // 对账
        tryMatchOrder(orderId);
    }

    /**
     * 保存流水订单数据
     *
     * @param bill    流水订单数据
     * @param bizType 业务类型
     */
    public void saveFlowOrderData(ConvertBill bill, String bizType) {

        ReconOrderConfig reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
        if (bill == null || bill.getOrderId() == null || !reconOrderConfig.getFlowTypes().contains(bizType)|| !reconOrderConfig.isOpen()) {
            log.warn("Invalid or unsupported flow order data: {}, bizType: {}", bill, bizType);
            return;
        }
        if (!isValidConvertOrder(bill,bizType)) {
            log.warn("Invalid convert order data: {}, bizType: {}", bill, bizType);
            return;
        }
        String orderId = String.valueOf(bill.getOrderId());
        String key = FLOW_ORDER_PREFIX + bizType + ":" + orderId;
        String value = JSON.toJSONString(bill);
        // 保存流水订单数据到Redis
        reconRedisTemplate.opsForValue().set(key, value, reconOrderConfig.getOrderExpireTime(), TimeUnit.MINUTES);
        log.info("Saved flow order data: {}, bizType: {}", orderId, bizType);
        // 保存订单ID到索引
        saveMainOrderIndex(orderId,reconOrderConfig);
        // 尝试对账
        tryMatchOrder(orderId);
    }

    /**
     * 验证是否为有效的闪兑订单流水
     */
    private boolean isValidConvertOrder(ConvertBill bill, String bizType) {

        // 获取配置
        ReconOrderConfig reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
        if (reconOrderConfig.getUserBizTypes().contains(bizType)) {
            return true;
        }
        // 判断是否存在主订单
        String orderId = String.valueOf(bill.getOrderId());
        String mainOrderKey = MAIN_ORDER_PREFIX + orderId;
        Boolean hasMainOrder = reconRedisTemplate.hasKey(mainOrderKey);
        if (Boolean.TRUE.equals(hasMainOrder)) {
            return true;
        }
        for (String userBizType : reconOrderConfig.getUserBizTypes()) {
            String userOrderKey = FLOW_ORDER_PREFIX + userBizType + ":" + orderId;
            Boolean hasUserOrder = reconRedisTemplate.hasKey(userOrderKey);
            if (Boolean.TRUE.equals(hasUserOrder)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 尝试匹配订单进行对账
     *
     * @param orderId 订单ID
     */
    public void tryMatchOrder(String orderId) {
        // 获取主订单数据
        String mainOrderKey = MAIN_ORDER_PREFIX + orderId;
        Object order = reconRedisTemplate.opsForValue().get(mainOrderKey);
        if (Objects.isNull(order) || StringUtils.isEmpty(order.toString())) {
            log.info("Main order not found for matching: {}", orderId);
            return;
        }
        // 检查所有需要的流水订单类型是否都存在
        boolean allFlowOrdersExist = true;
        ReconOrderConfig reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);

        for (String bizType : reconOrderConfig.getFlowTypes()) {
            String flowOrderKey = FLOW_ORDER_PREFIX + bizType + ":" + orderId;
            Boolean exists = reconRedisTemplate.hasKey(flowOrderKey);
            if (Boolean.FALSE.equals(exists)) {
                allFlowOrdersExist = false;
                log.info("Flow order not found for bizType: {}, orderId: {}", bizType, orderId);
                break;
            }
        }
        // 如果所有需要的流水订单都存在，则进行匹配处理
        if (allFlowOrdersExist) {
            // 获取或创建该订单的锁
            Lock orderLock = orderLocks.computeIfAbsent(orderId, k -> new ReentrantLock());
            // 尝试获取锁，如果获取不到，直接返回
            if (!orderLock.tryLock()) {
                log.info("Order {} is already being processed by another thread, skipping", orderId);
                return;
            }
            try {
                ConvertOrder mainOrderData = JSON.parseObject(order.toString(), ConvertOrder.class);
                // 创建组合订单对象
                CombinedOrderData combinedOrderData = new CombinedOrderData();
                combinedOrderData.addMainOrder(mainOrderData);

                // 获取所有流水订单数据
                for (String bizType : reconOrderConfig.getFlowTypes()) {
                    String flowOrderKey = FLOW_ORDER_PREFIX + bizType + ":" + orderId;
                    Object bill = reconRedisTemplate.opsForValue().get(flowOrderKey);
                    if (Objects.isNull(bill)) {
                        log.error("flow order not found for bizType: {}, orderId: {}", bizType, orderId);
                        return;
                    }
                    ConvertBill flowOrderData = JSON.parseObject(bill.toString(), ConvertBill.class);
                    combinedOrderData.addFlowOrder(bizType, flowOrderData);
                }
                // 处理组合订单
                ReconOrderResult orderResult = orderDataProcessor.processCombinedOrder(combinedOrderData);
                if (orderResult.isSuccess()) {
                    cleanupRedisData(orderId);
                    log.info("Successfully matched and processed order: {}", orderId);
                } else {
                    // 对账失败，记录失败信息
                    recordFailure(mainOrderData, orderResult);
                    cleanupRedisData(orderId);
                    log.warn("Failed to process combined order: {}", orderId);
                }
            }finally {
                // 处理完成后释放锁
                orderLock.unlock();
                // 清理锁对象
                if (orderLock instanceof ReentrantLock && !((ReentrantLock) orderLock).hasQueuedThreads()) {
                    orderLocks.remove(orderId);
                }
            }

        }
    }


    /**
     * 清理Redis中的订单数据
     */
    private void cleanupRedisData(String orderId) {
        try {
            // 删除主订单数据
            String mainOrderKey = MAIN_ORDER_PREFIX + orderId;
            reconRedisTemplate.delete(mainOrderKey);

            ReconOrderConfig reconOrderConfig = ReconciliationApolloConfigUtils.getReconOrderConfig(ReconOrderTypeEnum.CONVERT);
            // 删除流水订单数据
            List<String> flowOrderKeys = new ArrayList<>();
            for (String bizType : reconOrderConfig.getFlowTypes()) {
                flowOrderKeys.add(FLOW_ORDER_PREFIX + bizType + ":" + orderId);
            }
            if (!flowOrderKeys.isEmpty()) {
                reconRedisTemplate.delete(flowOrderKeys);
            }
            // 删除索引数据
            if (reconOrderConfig.isDynamicShardingEnabled()) {
                // 动态分片情况下需要计算分片key
                String shardKey = MAIN_ORDER_TIMESTAMP_PREFIX +
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
                reconRedisTemplate.opsForZSet().remove(shardKey, orderId);
            } else {
                reconRedisTemplate.opsForZSet().remove(DEFAULT_MAIN_ORDER_INDEX_KEY, orderId);
            }

            log.info("Cleaned up Redis data for order: {}", orderId);

        } catch (Exception e) {
            log.warn("Failed to cleanup Redis data for order: {}", orderId, e);
            // 清理失败不影响对账结果
        }
    }
    /**
     * 新增主订单索引保存的方法
     */
    private void saveMainOrderIndex(String orderId,ReconOrderConfig reconOrderConfig) {

        String shardKey = getDynamicShardKey(reconOrderConfig);
        reconRedisTemplate.opsForZSet().add(shardKey, orderId, (double) System.currentTimeMillis() / 1000);
        if (reconOrderConfig.isDynamicShardingEnabled()) {
            // 设置分片过期时间
            reconRedisTemplate.expire(shardKey, reconOrderConfig.getOrderExpireTime() + 10, TimeUnit.MINUTES);
        }
        log.info("Saved main order index for shard key: {}", shardKey);
    }

    private String getDynamicShardKey(ReconOrderConfig reconOrderConfig) {
        if (reconOrderConfig.isDynamicShardingEnabled()) {
            LocalDateTime now = LocalDateTime.now();
            return MAIN_ORDER_TIMESTAMP_PREFIX + now.format(SHARD_FORMATTER);
        }
        return DEFAULT_MAIN_ORDER_INDEX_KEY;
    }

    /**
     * 记录失败信息
     *
     * @param mainOrderData 主订单数据
     * @param orderResult   对账结果
     */
    private void recordFailure(ConvertOrder mainOrderData, ReconOrderResult orderResult) {
        ReconOrderFailureRecord failureRecord = ReconOrderFailureRecord.builder()
                .bizId(mainOrderData.getOrderId())
                .userId(mainOrderData.getAccountId())
                .orderId(mainOrderData.getOrderId())
                .orderType(ReconOrderTypeEnum.CONVERT.getCode())
                .failureType(orderResult.getFailureType().toString())
                .failureReason(orderResult.getFailureType().getMessage())
                .status(ReconOrderStausEnum.RECONCILED_FAILURE.getCode()) // 对账失败
                .rawData(mainOrderData.toString())
                .createTime(new Date())
                .updateTime(new Date())
                .build();
        reconOrderFailureRecordService.recordFailure(failureRecord);
    }
}
