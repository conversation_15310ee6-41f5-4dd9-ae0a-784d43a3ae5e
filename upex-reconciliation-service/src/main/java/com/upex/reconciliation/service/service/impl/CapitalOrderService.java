package com.upex.reconciliation.service.service.impl;

import com.google.common.base.Stopwatch;
import com.upex.reconciliation.facade.enums.ApolloKeyEnum;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillCapitalOrder;
import com.upex.reconciliation.service.dao.entity.CapitalOrder;
import com.upex.reconciliation.service.dao.mapper.CapitalOrderMapper;
import com.upex.reconciliation.service.model.config.ApolloReconciliationCapitalOrderConfig;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.utils.BeanCopierUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/28 05:19
 */
@Slf4j
@Service
public class CapitalOrderService {

    @Resource
    private BillDbHelper dbHelper;

    @Resource(name = "capitalOrderMapper")
    private CapitalOrderMapper capitalOrderMapper;

    public void batchSaveCapitalOrder(List<CapitalOrder> capitalOrders, boolean isUpdate, KafkaConsumerConfig kafkaConsumerConfig) {
        if (CollectionUtils.isEmpty(capitalOrders)) {
            if (kafkaConsumerConfig.getShowLogOpen()) {
                log.info("CapitalOrderService batchSaveCapitalOrder success, size:{}, isUpdate:{}", CollectionUtils.size(capitalOrders), isUpdate);
            }
            return;
        }
        Stopwatch stopwatch = Stopwatch.createStarted();
        Map<Boolean, List<BillCapitalOrder>> billCapitalOrdersMap = convertCapitalOrderToBillCapitalOrder(capitalOrders, isUpdate);
        log.info("convertCapitalOrderToBillCapitalOrder success, size:{}, isUpdate:{}, time:{}", CollectionUtils.size(capitalOrders), isUpdate, stopwatch.stop());
        stopwatch.reset().start();
        for (Boolean isUpdateKey : billCapitalOrdersMap.keySet()) {
            if (isUpdateKey) {
                //dbHelper.doDbOpInReconMaster(() -> capitalOrderMapper.batchInsertOrUpdate(billCapitalOrdersMap.get(isUpdateKey)));
                dbHelper.doDbOpInReconMaster(() -> capitalOrderMapper.batchUpdate(billCapitalOrdersMap.get(isUpdateKey)));
                log.info("batchUpdate CapitalOrder success, size:{}, isUpdate:{}, time:{}", CollectionUtils.size(billCapitalOrdersMap.get(isUpdateKey)), isUpdate, stopwatch.stop());
                stopwatch.reset().start();
            } else {
                dbHelper.doDbOpInReconMaster(() -> capitalOrderMapper.batchInsert(billCapitalOrdersMap.get(isUpdateKey)));
                log.info("batchInsert CapitalOrder success, size:{}, isUpdate:{}, time:{}", CollectionUtils.size(billCapitalOrdersMap.get(isUpdateKey)), isUpdate, stopwatch.stop());
                stopwatch.reset().start();
            }
        }
        if (kafkaConsumerConfig.getShowLogOpen()) {
            log.info("CapitalOrderService batchSaveCapitalOrder success, size:{}, isUpdate:{}, updateSize:{}, insertSize:{}", CollectionUtils.size(capitalOrders), isUpdate, CollectionUtils.size(billCapitalOrdersMap.get(true)), CollectionUtils.size(billCapitalOrdersMap.get(false)));
        }
    }

    private Map<Boolean, List<BillCapitalOrder>> convertCapitalOrderToBillCapitalOrder(List<CapitalOrder> capitalOrders, boolean isUpdate) {
        // 获取表中订单信息
        Map<Boolean, List<BillCapitalOrder>> resultMap = new HashMap<>();
        List<Long> orderList = capitalOrders.stream().map(CapitalOrder::getOrderId).collect(Collectors.toList());
        List<BillCapitalOrder> billCapitalOrders = dbHelper.doDbOpInReconMaster(() -> capitalOrderMapper.selectByOrderNoList(orderList));
        Map<Long, BillCapitalOrder> dbCapitalOrderMap = billCapitalOrders.stream().collect(Collectors.toMap(BillCapitalOrder::getOrderId, Function.identity(), (k1, k2) -> k1));

        Set<Byte> billStatus = ReconciliationApolloConfigUtils.getApolloObjectConfig(ApolloKeyEnum.CAPITAL_ORDER_APOLLO_CONFIG, ApolloReconciliationCapitalOrderConfig.class).getBillStateSet();
        for (CapitalOrder capitalOrder : capitalOrders) {
            BillCapitalOrder billCapitalOrder = BeanCopierUtil.copyProperties(capitalOrder, BillCapitalOrder.class);
            if (isUpdate) {
                BillCapitalOrder dbCapitalOrder = dbCapitalOrderMap.get(capitalOrder.getOrderId());
                if (Objects.nonNull(dbCapitalOrder)) {
                    if (billStatus.contains(capitalOrder.getStatus()) && dbCapitalOrder.getBillTime() == null) {
                        billCapitalOrder.setBillTime(capitalOrder.getSourceUpdateDate());
                    }
                    resultMap.computeIfAbsent(isUpdate, v -> new ArrayList<>()).add(billCapitalOrder);
                }else {
                    if (billStatus.contains(capitalOrder.getStatus())) {
                        billCapitalOrder.setBillTime(capitalOrder.getSourceUpdateDate());
                    }
                    resultMap.computeIfAbsent(false, v -> new ArrayList<>()).add(billCapitalOrder);
                }
            }else {
                resultMap.computeIfAbsent(isUpdate, v -> new ArrayList<>()).add(billCapitalOrder);
            }
        }
        return resultMap;
    }

    public BillCapitalOrder getCapitalOrderInfo(Long orderNo) {
        if (Objects.isNull(orderNo)) {
            return null;
        }
        return dbHelper.doDbOpInReconMaster(() -> capitalOrderMapper.selectByOrderNo(orderNo));
    }

    public List<BillCapitalOrder> queryAllRechargeFailureAmount(List<Integer> rechargeFailureStatusList) {
        return dbHelper.doDbOpInReconMaster(() -> capitalOrderMapper.selectAllRechargeFailureAmount(rechargeFailureStatusList));
    }

    public List<BillCapitalOrder> querySysWalletCompensateRechargeFailureAmount() {
        return dbHelper.doDbOpInReconMaster(() -> capitalOrderMapper.selectSysWalletCompensateRechargeFailureAmount());
    }

    public List<BillCapitalOrder> queryRechargeChain() {
        return dbHelper.doDbOpInReconMaster(() -> capitalOrderMapper.queryRechargeChain());
    }
}
