package com.upex.reconciliation.service.utils;


import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class FreeMarkerUtils {

    /**
     * 模板 + 数据 = 内容
     *
     * @param templateName
     * @param templateContent
     * @param root
     * @return
     */
    public static String processToString(String templateName, String templateContent, Map<String, Object> root) {
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_28);
        StringTemplateLoader stringTemplateLoader = new StringTemplateLoader();
        stringTemplateLoader.putTemplate(templateName, templateContent);
        configuration.setTemplateLoader(stringTemplateLoader);
        configuration.setNumberFormat("0.################");
        try {
            Template template = configuration.getTemplate(templateName, "UTF-8");
            String result = FreeMarkerTemplateUtils.processTemplateIntoString(template, root);
            return result;
        } catch (IOException e) {
            log.error("", e);
            //AlarmUtils.error("send lark message template：{} content does not exist!", templateName);
            return null;
        } catch (TemplateException e) {
            log.error("", e);
            //AlarmUtils.error("send lark message template:{} content parsing failed", templateName);
            return null;
        }
    }
}
