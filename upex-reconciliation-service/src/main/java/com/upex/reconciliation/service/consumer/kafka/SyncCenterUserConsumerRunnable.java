package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.client.kafka.MessageDeserializer;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.alibaba.otter.canal.protocol.Message;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.enums.SQLTypeEnum;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.dto.SyncBillUserDTO;
import com.upex.reconciliation.service.service.BillUserService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.EnvUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.*;

import static com.upex.reconciliation.facade.enums.SQLTypeEnum.INSERT;
import static com.upex.reconciliation.service.utils.DateUtil.FMT_yyyy_MM_dd_HH_mm_ss;

/**
 * 增量用户同步
 */
@Slf4j
public class SyncCenterUserConsumerRunnable implements KafkaConsumerLifecycle {
    private volatile boolean running = true;
    private Map<String, Object> consumerConfig;
    private String groupId;
    private Set<Integer> closeConsumerPatition = new HashSet<>();
    private AlarmNotifyService alarmNotifyService;
    private KafkaConsumer consumer;
    private ReconciliationSpringContext context;
    private BillUserService billUserService;
    private KafkaProducer<String, String> kafkaProducer;
    private String topic = KafkaTopicEnum.RECON_SYNC_CENTER_USER_INFO_TOPIC.getCode();

    public SyncCenterUserConsumerRunnable(ReconciliationSpringContext context, String kafkaServers, String groupId, Integer maxPollSiz) {
        this.context = context;
        this.billUserService = context.getBillUserService();
        this.groupId = EnvUtil.getKafkaConsumerGroup(groupId);
        alarmNotifyService = context.getAlarmNotifyService();
        kafkaProducer = context.getKafkaProducer();
        consumerConfig = new HashMap<String, Object>();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServers);
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MessageDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, this.groupId);
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollSiz);
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
    }

    @Override
    public void shutdown() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return closeConsumerPatition.size() != 1;
    }

    @Override
    public String getThreadPrefixName() {
        return "kafka-consumer-thread-sync-center-user-info";
    }

    @Override
    public void run() {
        consumer = new KafkaConsumer<String, Message>(consumerConfig);
        consumer.subscribe(Arrays.asList(topic));
        log.info("SyncCenterUserConsumerRunnable run 。。。");
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        while (running) {
            try {
                // 从kafka集群中拉取消息df
                ConsumerRecords<String, Message> consumerRecords = consumer.poll(3000);
                List<SyncBillUserDTO> syncBillUserDTOS = new ArrayList<>();
                consumerRecords.forEach(new java.util.function.Consumer<ConsumerRecord<String, Message>>() {
                    @Override
                    public void accept(ConsumerRecord<String, Message> consumerRecord) {
                        List<FlatMessage> flatMessages = canalMessageDecode(consumerRecord.value());
                        for (FlatMessage flatMessage : flatMessages) {
                            SQLTypeEnum sqlTypeEnum = SQLTypeEnum.convert(flatMessage.getType());
                            boolean isDdl = flatMessage.getIsDdl();
                            if (isDdl || sqlTypeEnum == null || sqlTypeEnum != INSERT) {
                                continue;
                            }
                            List<Map<String, String>> dataList = flatMessage.getData();
                            if (CollectionUtils.isEmpty(dataList)) {
                                continue;
                            }
                            for (Map<String, String> data : dataList) {
                                SyncBillUserDTO syncBillUserDTO = new SyncBillUserDTO();
                                syncBillUserDTO.setUserId(Long.parseLong(data.get("id")));
                                String parentId = data.get("parent_id");
                                if (StringUtils.isNotEmpty(parentId)) {
                                    syncBillUserDTO.setParentId(Long.parseLong(parentId));
                                }
                                Date createdDate = DateUtil.str2date(data.get("created_date"), FMT_yyyy_MM_dd_HH_mm_ss);
                                syncBillUserDTO.setRegisterTime(createdDate);
                                syncBillUserDTO.setAccountType(AccountTypeEnum.UNKNOWN.getCode());
                                syncBillUserDTOS.add(syncBillUserDTO);
                            }
                            if(globalBillConfig.isSyncUserCenterLog()){
                                log.info("SyncUserCenterData,syncBillUserDTOS:{}", JSONObject.toJSONString(syncBillUserDTOS));
                            }
                        }
                    }
                });
                for (SyncBillUserDTO syncBillUserDTO : syncBillUserDTOS) {
                    ProducerRecord<String, String> incrementRecord = new ProducerRecord<>(KafkaTopicEnum.RECON_USER_INCREMENT_SYNC_TOPIC.getCode(), syncBillUserDTO.getUserId().toString(), JSON.toJSONString(syncBillUserDTO));
                    kafkaProducer.send(incrementRecord);
                }
                consumer.commitSync();
            } catch (Exception e) {
                log.error("SyncCenterUserConsumerRunnable startConsume error ", e);
            }
        }
        consumer.close();
        closeConsumerPatition.add(0);
        log.info("SyncCenterUserConsumerRunnable consumer.close success {} {}");
    }

}


