package com.upex.reconciliation.service.common.constants.enums;

public enum ContractEnum {
    /**
     * usdt合约
     */
    USDTACCOUNT(1,"default"),
    /**
     * 币币合约
     */
    SPOTCONTRACT(2,"default"),
    /**
     * 页号
     */
    PAGENO(1,"页号"),
    /**
     * 页大小
     */
    PAGESIZE(10,"页大小"),
    ;

    private Integer code;
    private String desc;

    ContractEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ContractEnum toEnum(Integer code) {
        for (ContractEnum item : ContractEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

}
