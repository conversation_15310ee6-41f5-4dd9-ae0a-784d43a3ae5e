package com.upex.reconciliation.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.upex.bill.dto.enums.ProfitTransferStatusEnum;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.commons.log.util.JsonUtils;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.AssetsCheckService;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.business.createtablebyroute.BillContractProfitTransferTableCreator;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.CapitalInitBusinessTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.ProfitSourceTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.ProfitTransferTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.ProfitTypeEnum;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.dao.mapper.BillContractProfitCoinDetailMapper;
import com.upex.reconciliation.service.dao.mapper.BillContractProfitSymbolDetailMapper;
import com.upex.reconciliation.service.dao.mapper.BillContractProfitTransferMapper;
import com.upex.reconciliation.service.model.config.ApolloProfitTransferConfig;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;
import com.upex.reconciliation.service.model.dto.BillTimeSliceDTO;
import com.upex.reconciliation.service.model.dto.TransferDto;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.reconciliation.service.utils.SplitTableUtils;
import com.upex.reconciliation.service.utils.TimeSliceCalcUtils;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.utils.log.AlarmUtils;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.TRANSFER_FOR_BILL_PROFIT_ERROR;
import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.TRANSFER_FOR_BILL_PROFIT_INFO;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_transfer(盈亏动账记录表)】的数据库操作Service实现
 * @createDate 2023-06-09 17:18:46
 */
@Slf4j
@Service
public class BillContractProfitTransferServiceImpl implements BillContractProfitTransferService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billContractProfitTransferMapperWrapper")
    private BillContractProfitTransferMapper billContractProfitTransferMapper;
    @Resource(name = "billContractProfitSymbolDetailMapperWrapper")
    private BillContractProfitSymbolDetailMapper billContractProfitSymbolDetailMapper;
    @Resource(name = "billContractProfitCoinDetailMapperWrapper")
    private BillContractProfitCoinDetailMapper billContractProfitCoinDetailMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private SerialNoGenerator noGenerator;
    //@Resource
    //private AssetsRetryerListener assetsRetryerListener;
    @Resource
    private AssetsCheckService assetsCheckService;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    @Resource
    private BillCapitalInitPropertyService billCapitalInitPropertyService;
    @Resource
    private ReconSystemAccountService reconSystemAccountService;
    //@Resource
    //private AlarmManageService alarmManageService;
    @Resource
    private AlarmNotifyService alarmNotifyService;
    @Resource
    private AssetsContractProfitCoinDetailService assetsContractProfitCoinDetailService;
    @Resource
    private AssetsContractProfitSymbolDetailService assetsContractProfitSymbolDetailService;
    @Resource
    private BillContractProfitTransferTableCreator billContractProfitTransferTableCreator;

    @Override
    public void generateChargeRecordForResetBill(AccountTypeEnum accountTypeEnum, Date resetCheckTime, Date lastCheckTime) {
        dbHelper.doDbOpInReconMasterTransaction(() -> {
            List<Byte> mixContractType = AccountTypeEnum.getTransferType().stream()
                    .map(AccountTypeEnum::getCode)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(mixContractType) || !mixContractType.contains(accountTypeEnum.getCode())) {
                return null;
            }

            //获取对账产生的数据
            List<BillContractProfitTransfer> transferList = new ArrayList<>();
            for (ProfitSourceTypeEnum sourceType : ProfitSourceTypeEnum.getRollBackSourceTypeEnums()) {
                transferList.addAll(selectByCheckOkTime(resetCheckTime, lastCheckTime,
                        accountTypeEnum.getCode(), sourceType.getCode(), accountTypeEnum.getAccountParam()));
            }
            log.info("BillContractProfitTransferService.generateChargeRecordForResetBill resetCheckTime={}, lastCheckTime={}, -{},transferList.size-{}", DateUtil.getDefaultDateStr(resetCheckTime), DateUtil.getDefaultDateStr(lastCheckTime), CollectionUtils.size(transferList));
            // 如果存在执行中数据不能回退
            List<BillContractProfitTransfer> undoList = transferList.stream()
                    .filter(item -> ProfitTransferStatusEnum.UN_TRANSFER.getCode().equals(item.getStatus())
                            || ProfitTransferStatusEnum.FAILED.getCode().equals(item.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.size(undoList) > 0) {
                log.error("BillContractProfitTransferService.generateChargeRecordForResetBill has undo data resetCheckTime={}, lastCheckTime={}, -{},-{},transferList-{}",
                        DateUtil.getDefaultDateStr(resetCheckTime), DateUtil.getDefaultDateStr(lastCheckTime), JSONObject.toJSONString(undoList));
                throw new RuntimeException("transfer data has undo data ");
            }

            //删除盈利统计信息并且迁移历史
            if (AccountTypeEnum.INTERNAL == accountTypeEnum) {
                List<AssetsContractProfitCoinDetail> assetsContractProfitCoinDetails = assetsContractProfitCoinDetailService.getAllAfterRecord(resetCheckTime,
                        accountTypeEnum.getBizTag(), accountTypeEnum.getAccountParam());
                if (CollectionUtils.isNotEmpty(assetsContractProfitCoinDetails)) {
                    assetsContractProfitCoinDetailService.deleteAfterRecord(resetCheckTime, accountTypeEnum.getBizTag(), accountTypeEnum.getAccountParam());
                    assetsContractProfitCoinDetailService.batchInsertHis(accountTypeEnum.getBizTag(), accountTypeEnum.getAccountParam(), assetsContractProfitCoinDetails);
                }
                List<AssetsContractProfitSymbolDetail> assetsContractProfitSymbolDetails = assetsContractProfitSymbolDetailService.getAllAfterRecord(resetCheckTime,
                        accountTypeEnum.getBizTag(), accountTypeEnum.getAccountParam());
                if (CollectionUtils.isNotEmpty(assetsContractProfitSymbolDetails)) {
                    assetsContractProfitSymbolDetailService.deleteAfterRecord(resetCheckTime, accountTypeEnum.getBizTag(), accountTypeEnum.getAccountParam());
                    assetsContractProfitSymbolDetailService.batchInsertHis(accountTypeEnum.getBizTag(), accountTypeEnum.getAccountParam(), assetsContractProfitSymbolDetails);
                }
            } else {
                List<BillContractProfitCoinDetail> allCoinAfterRecord = billContractProfitCoinDetailMapper.getAllAfterRecord(resetCheckTime,
                        accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
                if (CollectionUtils.isNotEmpty(allCoinAfterRecord)) {
                    billContractProfitCoinDetailMapper.deleteAfterRecord(resetCheckTime, accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
                    billContractProfitCoinDetailMapper.batchInsertHis(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), allCoinAfterRecord);
                }
                List<BillContractProfitSymbolDetail> allSymbolAfterRecord = billContractProfitSymbolDetailMapper.getAllAfterRecord(resetCheckTime,
                        accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
                if (CollectionUtils.isNotEmpty(allSymbolAfterRecord)) {
                    billContractProfitSymbolDetailMapper.deleteAfterRecord(resetCheckTime, accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
                    billContractProfitSymbolDetailMapper.batchInsertHis(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), allSymbolAfterRecord);
                }
                log.info("BillContractProfitTransferService.generateChargeRecordForResetBill resetCheckTime={}, lastCheckTime={}, -{},allCoinAfterRecord.size-{},allSymbolAfterRecord.size-{}",
                        DateUtil.getDefaultDateStr(resetCheckTime), DateUtil.getDefaultDateStr(lastCheckTime), CollectionUtils.size(allCoinAfterRecord), CollectionUtils.size(allSymbolAfterRecord));
            }

            //没有数据不需要做冲销，直接返回
            if (CollectionUtils.isEmpty(transferList)) {
                return null;
            }
            Date generateTime = new Date();
            List<BillContractProfitTransfer> changesList = Lists.newArrayList();
            transferList.forEach(item -> changesList.add(BillContractProfitTransfer.builder()
                    .id(noGenerator.nextNo())
                    .checkOkTime(item.getCheckOkTime())
                    .coinId(item.getCoinId())
                    .bizId(item.getBizId())
                    .version(noGenerator.nextNo())
                    .batchNo(item.getBatchNo())
                    .transferInUserId(item.getTransferInUserId())
                    .sourceType(ProfitSourceTypeEnum.BILL_RESET.getCode())
                    .accountType(accountTypeEnum.getCode())
                    .accountParam(accountTypeEnum.getAccountParam())
                    .toAccountType(item.getToAccountType())
                    .toAccountParam(item.getToAccountParam())
                    .transferType(ProfitTransferTypeEnum.toEnum(item.getTransferType()).getRollbackProfitTransferType().getCode())
                    .status(ProfitTransferStatusEnum.UN_TRANSFER.getCode())
                    .createTime(generateTime)
                    .updateTime(generateTime)
                    .transferCount(item.getTransferCount().negate())
                    .build()));
            if (CollectionUtils.isNotEmpty(changesList)) {
                batchInsert(changesList);
            }
            log.info("BillContractProfitTransferService.generateChargeRecordForResetBill  resetCheckTime={} result= {}",
                    DateUtil.getDefaultDateStr(resetCheckTime), JsonUtils.getJson(changesList));
            return null;
        });
    }

    @Override
    public void transferUseBillCheckResult(Date executeTime) {
        ApolloProfitTransferConfig transferConfig = ReconciliationApolloConfigUtils.getApolloProfitTransferConfig();
        log.info("recon transferUseBillCheckResult transferConfig:{}", JSONObject.toJSONString(transferConfig));
        if (!transferConfig.isOpen()) {
            return;
        }
        //使用延迟时间更改获取数据时间范围
        // Date maxDate = DateUtil.addHour(executeTime, transferConfig.getDelayHour() * -1);
        // 查询未处理 和处理中的数据
        int executeCount = transferConfig.getExecuteCount();
        //月初第一天 check上个月的表是否还有待动账数据
        List<BillContractProfitTransfer> transferList = new ArrayList<>();
        if (DateUtil.getDayOfMonth(executeTime) < transferConfig.getAcrossMonthsCheckDays()) {
            List<BillContractProfitTransfer> lastDate = selectAllByCheckOkTime(DateUtil.getLastMonthEndTime(executeTime), executeCount);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lastDate)) {
                log.info("transferUseBillCheckResult lastMonth transferList:{}, executeTime:{}", org.apache.commons.collections.CollectionUtils.size(lastDate), executeTime);
                transferList.addAll(lastDate);
            }
        }
        transferList.addAll(selectAllByCheckOkTime(executeTime, executeCount));
        log.info("transferUseBillCheckResult transferList:{}, executeTime:{}", CollectionUtils.size(transferList), executeTime);
        this.transferForBillProfit(executeTime, transferList);
    }

    @Override
    public void transferUserBillCheckResultByAccountType(Date executeTime, List<Byte> toAccountTypes) {
        ApolloProfitTransferConfig transferConfig = ReconciliationApolloConfigUtils.getApolloProfitTransferConfig();
        log.info("recon transferUserBillCheckResultByAccountType transferConfig:{}", JSONObject.toJSONString(transferConfig));
        if (!transferConfig.isOpen()) {
            return;
        }
        //使用延迟时间更改获取数据时间范围
        // Date maxDate = DateUtil.addHour(executeTime, transferConfig.getDelayHour() * -1);
        // 查询未处理 和处理中的数据
        int executeCount = transferConfig.getExecuteCount();
        //月初第一天 check上个月的表是否还有待动账数据
        List<BillContractProfitTransfer> transferList = new ArrayList<>();
        if (DateUtil.getDayOfMonth(executeTime) < transferConfig.getAcrossMonthsCheckDays()) {
            List<BillContractProfitTransfer> lastDate = selectAllByToAccountTypeAndCheckOkTime(DateUtil.getLastMonthEndTime(executeTime), executeCount, toAccountTypes);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lastDate)) {
                log.info("transferUserBillCheckResultByAccountType lastMonth transferList:{}, executeTime:{}", org.apache.commons.collections.CollectionUtils.size(lastDate), executeTime);
                transferList.addAll(lastDate);
            }
        }
        transferList.addAll(selectAllByToAccountTypeAndCheckOkTime(executeTime, executeCount, toAccountTypes));
        log.info("transferUserBillCheckResultByAccountType transferList:{}, executeTime:{}", CollectionUtils.size(transferList), executeTime);
        this.transferForBillProfit(executeTime, transferList);
    }

    private void transferForBillProfit(Date executeTime, List<BillContractProfitTransfer> transferList) {
        try {
            ApolloProfitTransferConfig apolloProfitTransferConfig = ReconciliationApolloConfigUtils.getApolloProfitTransferConfig();
            Date transferTime = new Date();
            Map<Integer, String> allCoinsMap = commonService.getAllCoinsMapCache(executeTime.getTime());
            for (BillContractProfitTransfer item : transferList) {
                if (ProfitTransferStatusEnum.SUCCEED.getCode().equals(item.getStatus())) {
                    log.error("BillContractProfitTransferService.transferForBillProfit status is succeed, item:{}", JSONObject.toJSONString(item));
                    continue;
                }
                // 增加apollo动账类型，指定类型不允许动账   transferType  兼容老数据toAccountType为空的情况
                if (Objects.isNull(item.getToAccountType())) {
                    item.setToAccountType(AccountTypeEnum.SPOT.getCode());
                    item.setToAccountParam(BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM);
                }
                AccountTypeEnum anEnum = AccountTypeEnum.toEnum(item.getToAccountType());
                if (anEnum == null || apolloProfitTransferConfig.getNotTransferTypeList().contains(item.getTransferType())) {
                    log.error("BillContractProfitTransferService.transferForBillProfit interrupt anEnum:{}, transferType:{}", JSONObject.toJSONString(anEnum), item.getTransferType());
                    continue;
                }
                // 增加重试，ApiException不重试  默认取现货
                //Retryer<Boolean> retry = RetryUtils.getRetryer(apolloProfitTransferConfig.getSleepTime(), apolloProfitTransferConfig.getRetryNum(), TimeUnit.SECONDS, true, assetsRetryerListener);
                //Boolean result = retry.call(() -> transferServiceFactory.getTransferService(anEnum).transfer(anEnum, item, transferTime, allCoinsMap));
                Boolean result = accountAssetsServiceFactory.getTransferService(anEnum.getCode()).transfer(anEnum, item, transferTime, allCoinsMap);
                int updateTransfer;
                if (Objects.nonNull(result) && Boolean.TRUE.equals(result)) {
                    updateTransfer = updateStatusWithTransferTime(ProfitTransferStatusEnum.SUCCEED.getCode(),
                            item.getStatus(), noGenerator.nextNo(), item.getVersion(),
                            transferTime, transferTime, item.getId(), item.getCheckOkTime());
                } else {
                    updateTransfer = updateStatus(ProfitTransferStatusEnum.FAILED.getCode(),
                            item.getStatus(), noGenerator.nextNo(), item.getVersion(),
                            transferTime, item.getId(), item.getCheckOkTime());
                    String message = String.format("动账失败数据详情信息: \n" +
                                    "bizId: %s\n " +
                                    "batchNo: %s\n " +
                                    "checkOkTime: %s\n " +
                                    "accountType: %s\n " +
                                    "toAccountType: %s\n " +
                                    "coinId: %s\n " +
                                    "transferCount: %s\n " +
                                    "status: %s\n " +
                                    "sourceType: %s\n " +
                                    "transferType: %s\n " +
                                    "transferInUserId: %s\n " +
                                    "result: %s",
                            item.getBizId(), item.getBatchNo(), item.getCheckOkTime().getTime(), item.getAccountType(), item.getToAccountType(),
                            item.getCoinId(), item.getTransferCount(), Objects.requireNonNull(ProfitTransferStatusEnum.toEnum(item.getStatus().byteValue())).getDesc(),
                            Objects.requireNonNull(ProfitSourceTypeEnum.toEnum(item.getSourceType())).getDesc(), ProfitTransferTypeEnum.toEnum(item.getTransferType()).getDesc(),
                            item.getTransferInUserId(), result);
                    alarmNotifyService.alarm(apolloProfitTransferConfig.isTransferErrorEmergencyOpen() ? TRANSFER_FOR_BILL_PROFIT_ERROR : TRANSFER_FOR_BILL_PROFIT_INFO, message);
                }
                if (updateTransfer != BillConstants.ONE) {
                    AlarmUtils.error("BillContractProfitTransferService.transfer  failed record:{} result:{} ,updateCount :{}", item, result, updateTransfer);
                    alarmNotifyService.alarm(TRANSFER_FOR_BILL_PROFIT_ERROR, "动账更新条数不一致");
                }
                log.info("BillContractProfitTransferService.transferForBillProfit executeTime={}, dateTime={},transferItem={},result={}",
                        DateUtil.getDefaultDateStr(executeTime), DateUtil.getDefaultDateStr(transferTime), JsonUtils.getJson(item), result);
            }
        } catch (Exception e) {
            log.error("BillContractProfitTransferService.transferForBillProfit execute Exception:", e);
        }
    }

    @Override
    public void greaterTransferData(AssetsBillConfig assetsBillConfig, AssetsCheckConfig assetsCheckConfig, List<BillContractProfitCoinDetail> profitCoinList, List<BillContractProfitTransfer> transferList, List<BillContractProfitSymbolDetail> symbolDetailList) {
        //获取应该走动账数据生成的只有合约类的业务线
        List<String> profitTypeList = Lists.newArrayList(ProfitTypeEnum.COIN_PROFIT.getCode(), ProfitTypeEnum.SYMBOL_PROFIT.getCode());
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.getMixContractType()) {
            // usd 已实现期初
            List<BillCapitalInitProperty> capitalInitPropertyList = billCapitalInitPropertyService.selectRecords(String.valueOf(accountTypeEnum.getCode()), accountTypeEnum.getAccountParam(), CapitalInitBusinessTypeEnum.USD_REALIZED_TRANSFER.getCode());
            Map<String, BillCapitalInitProperty> capitalInitPropertyMap = capitalInitPropertyList.stream().collect(Collectors.toMap(BillCapitalInitProperty::groupCoinProfitKey, item -> item));
            log.info("greaterTransferData capitalInitPropertyMap :{}", JSON.toJSONString(capitalInitPropertyMap));
            // 当期动账数据
            List<BillContractProfitCoinDetail> billAllContractProfitCoinDetailList = billContractProfitCoinDetailMapper.getAllBillContractProfitCoinDetailList(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), profitTypeList, assetsBillConfig.getCheckOkTime());
            Date lastCheckOkTime = new Date(assetsBillConfig.getCheckOkTime().getTime() - assetsCheckConfig.getTimeSliceAssetsCheckInterval());
            // 上期动账数据
            List<BillContractProfitCoinDetail> lastBillAllContractProfitCoinDetailList = billContractProfitCoinDetailMapper.getAllBillContractProfitCoinDetailList(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), profitTypeList, lastCheckOkTime);
            Map<String, BillContractProfitCoinDetail> lastBillAllContractProfitCoinDetailMap = lastBillAllContractProfitCoinDetailList.stream().collect(Collectors.toMap(BillContractProfitCoinDetail::getCoinProfitType, item -> item));
            // 计算增量数据
            for (BillContractProfitCoinDetail billContractProfitCoinDetail : billAllContractProfitCoinDetailList) {
                BillCapitalInitProperty capitalInitProperty = capitalInitPropertyMap.get(billContractProfitCoinDetail.getCoinProfitType());
                BillContractProfitCoinDetail lastBillContractProfitCoinDetail = lastBillAllContractProfitCoinDetailMap.get(billContractProfitCoinDetail.getCoinProfitType());
                //如果存在初始化数据 则进行数据重新计算
                if (capitalInitProperty != null && capitalInitProperty.getCheckTime().getTime() == assetsBillConfig.getCheckOkTime().getTime()) {
                    BigDecimal realizedCount = capitalInitProperty.getInitValue();
                    billContractProfitCoinDetail.setRealizedCount(realizedCount);
                    billContractProfitCoinDetail.setProfitCount(billContractProfitCoinDetail.getRealizedCount().add(billContractProfitCoinDetail.getUnrealizedCount()).negate());
                    billContractProfitCoinDetail.setProfitAmount(billContractProfitCoinDetail.getProfitCount().multiply(billContractProfitCoinDetail.getRate()));
                } else if (lastBillContractProfitCoinDetail != null) {
                    // 当期+历史已实现 重新算增量
                    BigDecimal realizedCount = billContractProfitCoinDetail.getChangeRealizedCount().add(lastBillContractProfitCoinDetail.getRealizedCount());
                    billContractProfitCoinDetail.setRealizedCount(realizedCount);
                    billContractProfitCoinDetail.setProfitCount(billContractProfitCoinDetail.getRealizedCount().add(billContractProfitCoinDetail.getUnrealizedCount()).negate());
                    billContractProfitCoinDetail.setProfitCountIncr(billContractProfitCoinDetail.getProfitCount().subtract(lastBillContractProfitCoinDetail.getProfitCount()));
                    billContractProfitCoinDetail.setProfitAmount(billContractProfitCoinDetail.getProfitCount().multiply(billContractProfitCoinDetail.getRate()));
                } else {
                    billContractProfitCoinDetail.setProfitCountIncr(billContractProfitCoinDetail.getProfitCount());
                }
            }
            profitCoinList.addAll(billAllContractProfitCoinDetailList);

            // 计算symbol增量 当期symbol数据
            List<BillContractProfitSymbolDetail> contractProfitSymbolDetailList = billContractProfitSymbolDetailMapper.getAllContractProfitSymbolDetailList(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), profitTypeList, assetsBillConfig.getCheckOkTime());
            // 上期symbol数据
            List<BillContractProfitSymbolDetail> lastContractProfitSymbolDetailList = billContractProfitSymbolDetailMapper.getAllContractProfitSymbolDetailList(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), profitTypeList, lastCheckOkTime);
            Map<String, BillContractProfitSymbolDetail> lastBillContractProfitSymbolDetailMap = lastContractProfitSymbolDetailList.stream().collect(Collectors.toMap(BillContractProfitSymbolDetail::groupByAccountSymbolProfitType, item -> item));
            for (BillContractProfitSymbolDetail contractProfitSymbolDetail : contractProfitSymbolDetailList) {
                BillContractProfitSymbolDetail lastContractProfitSymbolDetail = lastBillContractProfitSymbolDetailMap.get(contractProfitSymbolDetail.groupByAccountSymbolProfitType());
                if (lastContractProfitSymbolDetail != null) {
                    contractProfitSymbolDetail.setProfitCountIncr(contractProfitSymbolDetail.getProfitCount().subtract(lastContractProfitSymbolDetail.getProfitCount()));
                } else {
                    contractProfitSymbolDetail.setProfitCountIncr(contractProfitSymbolDetail.getProfitCount());
                }
                symbolDetailList.add(contractProfitSymbolDetail);
            }
        }
        transferList.addAll(generateProfitTransfersList(profitCoinList));
    }

    @Override
    public void greaterTransferData(ApolloReconciliationBizConfig apolloBizConfig, BillTimeSliceDTO billTimeSliceDTO) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        Date checkOkTime = billTimeSliceDTO.getBillConfig().getCheckOkTime();
        // 当期动账数据
        List<String> profitTypeList = Lists.newArrayList(ProfitTypeEnum.COIN_PROFIT.getCode(), ProfitTypeEnum.SYMBOL_PROFIT.getCode());
        List<BillContractProfitCoinDetail> currentContractProfitCoinDetailList = billTimeSliceDTO.getBillContractProfitCoinDetailList().stream().filter(item -> profitTypeList.contains(item.getProfitType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(currentContractProfitCoinDetailList)) {
            return;
        }
        Date lastCheckOkTime = new Date(TimeSliceCalcUtils.getPreSaveTimeSlice(checkOkTime.getTime(), apolloBizConfig.getTimeSliceSize(), apolloBizConfig.getMergeTimeSliceSize()));
        // 上期动账数据 获取应该走动账数据生成的只有合约类的业务线
        Map<String, BillContractProfitCoinDetail> lastBillAllContractProfitCoinDetailMap = billContractProfitCoinDetailMapper.getAllBillContractProfitCoinDetailList(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), profitTypeList, lastCheckOkTime)
                .stream().collect(Collectors.toMap(BillContractProfitCoinDetail::groupByAccountCoinProfitType, item -> item));
        // 计算增量数据
        for (BillContractProfitCoinDetail currentContractProfitCoinDetail : currentContractProfitCoinDetailList) {
            BillContractProfitCoinDetail lastBillContractProfitCoinDetail = lastBillAllContractProfitCoinDetailMap.get(currentContractProfitCoinDetail.groupByAccountCoinProfitType());
            //如果存在初始化数据 则进行数据重新计算
            if (lastBillContractProfitCoinDetail != null) {
                BigDecimal realizedCount = currentContractProfitCoinDetail.getChangeRealizedCount().add(lastBillContractProfitCoinDetail.getRealizedCount());
                BigDecimal profitCount = realizedCount.add(currentContractProfitCoinDetail.getUnrealizedCount()).add(currentContractProfitCoinDetail.getInitCount()).negate();
                BigDecimal profitCountIncr = profitCount.subtract(lastBillContractProfitCoinDetail.getProfitCount());
                currentContractProfitCoinDetail.setRealizedCount(realizedCount);
                currentContractProfitCoinDetail.setProfitCount(profitCount);
                currentContractProfitCoinDetail.setProfitCountIncr(profitCountIncr);
                currentContractProfitCoinDetail.setProfitAmount(profitCount.multiply(currentContractProfitCoinDetail.getRate()));
            } else {
                currentContractProfitCoinDetail.setProfitCountIncr(currentContractProfitCoinDetail.getProfitCount());
            }
        }
    }

    @Override
    public void resetCheckTransfer(List<TransferDto> transferIdList) {
        List<BillContractProfitTransfer> transferList = new ArrayList<>();
        transferList.addAll(selectTransferByIds(transferIdList, ProfitTransferStatusEnum.SUCCEED.getCode(), ProfitSourceTypeEnum.BILL_CHECK.getCode()));
        transferList.addAll(selectTransferByIds(transferIdList, ProfitTransferStatusEnum.SUCCEED.getCode(), ProfitSourceTypeEnum.BILL_CONTRACT_FEE.getCode()));
        log.info("resetCheckTransfer transferIdList:{} transferList.size:{},", transferIdList, CollectionUtils.size(transferList));
        if (CollectionUtils.isEmpty(transferList)) {
            return;
        }
        // 动账冲销处理
        Date currentDate = new Date();
        List<BillContractProfitTransfer> changesList = transferList.stream().map(item -> {
            BillContractProfitTransfer transfer = new BillContractProfitTransfer();
            transfer.setId(noGenerator.nextNo());
            transfer.setCheckOkTime(item.getCheckOkTime());
            transfer.setCoinId(item.getCoinId());
            transfer.setBizId(item.getBizId());
            transfer.setVersion(noGenerator.nextNo());
            transfer.setBatchNo(item.getBatchNo());
            transfer.setTransferInUserId(item.getTransferInUserId());
            transfer.setSourceType(ProfitSourceTypeEnum.BILL_RESET.getCode());
            transfer.setAccountType(item.getAccountType());
            transfer.setAccountParam(item.getAccountParam());
            transfer.setToAccountType(item.getToAccountType());
            transfer.setToAccountParam(item.getToAccountParam());
            transfer.setTransferType(ProfitTransferTypeEnum.toEnum(item.getTransferType()).getRollbackProfitTransferType().getCode());
            transfer.setStatus(ProfitTransferStatusEnum.UN_TRANSFER.getCode());
            transfer.setCreateTime(currentDate);
            transfer.setUpdateTime(currentDate);
            transfer.setTransferCount(item.getTransferCount().negate());
            return transfer;
        }).collect(Collectors.toList());

        int affectedRows = 0;
        if (CollectionUtils.isNotEmpty(changesList)) {
            affectedRows = batchInsert(changesList);
        }
        log.info("resetCheckTransfer transferIdList={},changesListSize={} affectedRows= {}", transferIdList, CollectionUtils.size(changesList), affectedRows);
    }

    @Override
    public void batchSave(ApolloReconciliationBizConfig apolloBizConfig, BillConfig billConfig, List<AccountAssetsInfoResult> billSystemFeeBillList) {
        Date currentDate = new Date();
        List<BillContractProfitTransfer> changesList = Lists.newArrayList();
        Map<Integer, PriceVo> rates = commonService.getCoinIdRatesMapCache(billConfig.getCheckOkTime().getTime());
        List<BillContractProfitCoinDetail> billContractProfitCoinDetails = Lists.newArrayList();
        long batchNo = noGenerator.nextNo();

        billSystemFeeBillList.stream()
                .filter(item -> !item.allPropZero())
                .forEach(item -> {
                    BillContractProfitCoinDetail billContractProfitDetail = BillContractProfitCoinDetailBuilder.get()
                            .buildGlobalProp(noGenerator, batchNo)
                            .buildAccountAssetsInfo(item)
                            .buildTimeAndToInitCount(billConfig, commonService.checkRateBySwapTokenIdReturnUSDT(item.getCoinId(), rates))
                            .buildTypeAndStatus(ProfitTransferStatusEnum.UN_TRANSFER, ProfitTypeEnum.MIX_CONTRACT_SYSTEM_DEAL_FEE)
                            .buildBaseTime(currentDate)
                            .create();
                    billContractProfitCoinDetails.add(billContractProfitDetail);

                    if (item.getProp2().compareTo(BigDecimal.ZERO) > 0) {
                        BillContractProfitTransfer transfer = BillContractProfitTransferBuilder.get()
                                .buildGlobalProp(noGenerator, billContractProfitDetail.getId())
                                .buildAccountAssetsInfo(item)
                                .buildTimeAndToAccount(apolloBizConfig, billConfig)
                                .buildTypeAndStatus(ProfitTransferTypeEnum.SYSTEM_CONTRACT_FEE, ProfitSourceTypeEnum.BILL_CONTRACT_FEE)
                                .buildBaseTime(currentDate).create();
                        changesList.add(transfer);
                    }
                });
        if (CollectionUtils.isNotEmpty(changesList)) {
            batchInsert(changesList);
        }
        if (CollectionUtils.isNotEmpty(billContractProfitCoinDetails)) {
            //MonitorSummaryUtil.record(JobMonitorMetricNameEnum.BILL_CONTRACT_PROFIT_COIN_DETAIL_INSERT_COUNT.getName(), billContractProfitCoinDetails.size());
            billContractProfitCoinDetailMapper.batchInsert(apolloBizConfig.getAccountType(), apolloBizConfig.getAccountParam(), billContractProfitCoinDetails);
            //MonitorSummaryUtil.record(JobMonitorMetricNameEnum.BILL_CONTRACT_PROFIT_COIN_DETAIL_INSERT_TIME_SUMMARY.getName() ,new Date().getTime()-currentDate.getTime());
        }

    }

    @Override
    public List<BillContractProfitTransfer> selectByCheckOkTime(Date timeStart, Date timeEnd, Byte accountType, int sourceType, String accountParam) {
        List<BillContractProfitTransfer> result = new ArrayList<>();
        Map<String, Pair<Date, Date>> intervalTimeMap = SplitTableUtils.getBillContractProfitTransferIntervalTimeByMonth(timeStart, timeEnd);
        for (Map.Entry<String, Pair<Date, Date>> pairEntry : intervalTimeMap.entrySet()) {
            List<BillContractProfitTransfer> billContractProfitTransfers = dbHelper.doDbOpInReconMaster(() -> billContractProfitTransferMapper.selectByCheckOkTime(pairEntry.getValue().getLeft(), pairEntry.getValue().getRight(), accountType, sourceType, accountParam, pairEntry.getKey()));
            result.addAll(billContractProfitTransfers);
        }
        return result;
    }

    @Override
    public List<BillContractProfitTransfer> selectAllByCheckOkTime(Date checkOkTime, int pageSize) {
        String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(checkOkTime);
        return billContractProfitTransferMapper.selectAllByCheckOkTime(checkOkTime, pageSize, tableNameSuffix);
    }

    public List<BillContractProfitTransfer> selectAllByToAccountTypeAndCheckOkTime(Date checkOkTime, int pageSize, List<Byte> toAccountTypes) {
        String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(checkOkTime);
        return billContractProfitTransferMapper.selectAllByToAccountTypeAndCheckOkTime(checkOkTime, pageSize, toAccountTypes, tableNameSuffix);
    }

    @Override
    public List<BillContractProfitTransfer> selectAllByIdList(List<TransferDto> idList) {
        List<BillContractProfitTransfer> result = new ArrayList<>();
        Map<Date, List<Long>> checkTimeIdsMap = idList.stream().collect(Collectors.groupingBy(TransferDto::getCheckOkTime, Collectors.mapping(TransferDto::getId, Collectors.toList())));
        for (Map.Entry<Date, List<Long>> dateListEntry : checkTimeIdsMap.entrySet()) {
            String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(dateListEntry.getKey());
            result.addAll(billContractProfitTransferMapper.selectAllByIdList(dateListEntry.getValue(), tableNameSuffix));
        }
        return result;
    }

    @Override
    public int updateStatus(Integer statusNew, Integer statusOld, Long versionNew,
                            Long version, Date updateTime, Long id, Date checkOkTime) {
        String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(checkOkTime);
        return billContractProfitTransferMapper.updateStatus(statusNew, statusOld, versionNew, version, updateTime, id, tableNameSuffix);
    }

    @Override
    public int updateStatusWithTransferTime(Integer statusNew, Integer statusOld, Long versionNew, Long version,
                                            Date updateTime, Date transferTime, Long id, Date checkOkTime) {
        String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(checkOkTime);
        return billContractProfitTransferMapper.updateStatusWithTransferTime(statusNew, statusOld, versionNew, version, updateTime, transferTime, id, tableNameSuffix);
    }

    @Override
    public int batchInsert(List<BillContractProfitTransfer> list) {
        int result = 0;
        Map<Date, List<BillContractProfitTransfer>> transferMap = list.stream().collect(Collectors.groupingBy(BillContractProfitTransfer::getCheckOkTime));
        for (Map.Entry<Date, List<BillContractProfitTransfer>> dateListEntry : transferMap.entrySet()) {
            String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(dateListEntry.getKey());
            result += billContractProfitTransferMapper.batchInsert(dateListEntry.getValue(), tableNameSuffix);
        }
        return result;
    }

    @Override
    public List<BillContractProfitTransfer> selectDelayedTransactionByStatusAndTime(Date checkOkTime) {
        String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(checkOkTime);
        return billContractProfitTransferMapper.selectDelayedTransactionByStatusAndTime(checkOkTime, tableNameSuffix);
    }


    @Override
    public List<BillContractProfitTransfer> selectEachDelayedTransactionByStatusAndTime(Date startTime, Date checkOkTime, Byte toAccountType) {
        String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(checkOkTime);
        return billContractProfitTransferMapper.selectEachDelayedTransactionByStatusAndTime(startTime, checkOkTime, toAccountType, tableNameSuffix);
    }

    @Override
    public List<BillContractProfitTransfer> selectDelayedTransactionByTime(Date checkOkTime) {
        String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(checkOkTime);
        return billContractProfitTransferMapper.selectDelayedTransactionByTime(checkOkTime, tableNameSuffix);
    }

    @Override
    public List<BillContractProfitTransfer> selectTransferByIds(List<TransferDto> transferList, Integer status, Integer sourceType) {
        List<BillContractProfitTransfer> result = new ArrayList<>();
        Map<Date, List<Long>> transferMap = transferList.stream().collect(Collectors.groupingBy(TransferDto::getCheckOkTime, Collectors.mapping(TransferDto::getId, Collectors.toList())));
        for (Map.Entry<Date, List<Long>> dateListEntry : transferMap.entrySet()) {
            String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(dateListEntry.getKey());
            result.addAll(billContractProfitTransferMapper.selectTransferByIds(dateListEntry.getValue(), status, sourceType, tableNameSuffix));
        }
        return result;
    }

    @Override
    public void transferFailureRepair() {
        ApolloProfitTransferConfig transferConfig = ReconciliationApolloConfigUtils.getApolloProfitTransferConfig();
        if (!transferConfig.isOpen()) {
            return;
        }
        List<BillContractProfitTransfer> transferResultList = new ArrayList<>();
        Date executeTime = DateUtil.getMonthStartTime(new Date());
        IntStream.range(BillConstants.ZERO, transferConfig.getTransferFailureMonths()).boxed().forEach(i -> {
            Date snapshotTime = DateUtil.addMonth(executeTime, BillConstants.NEG_ONE * i);
            // 1、查询所有失败的记录
            List<BillContractProfitTransfer> transferList = queryTransferRecordsByStatusAndTime(ProfitTransferStatusEnum.FAILED, snapshotTime);
            log.info("transferUseBillCheckResult transferList:{}, executeTime:{}", CollectionUtils.size(transferList), executeTime);
            if (CollectionUtils.isNotEmpty(transferList)) {
                transferResultList.addAll(transferList);
            }
        });
        if (CollectionUtils.isNotEmpty(transferResultList)) {
            this.transferForBillProfit(new Date(), transferResultList);
        }
    }

    @Override
    public int deleteByCheckTime(Byte accountType, String accountParam, Date checkTime) {
        String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(checkTime);
        return billContractProfitTransferMapper.deleteByCheckTime(accountType, accountParam, checkTime, tableNameSuffix);
    }

    @Override
    public Boolean batchDelete(Long beginId, Long pageSize, Byte accountType, String accountParam) {
        return billContractProfitTransferMapper.batchDelete(beginId, pageSize, accountType, accountParam, null);
    }

    @Override
    public List<BillContractProfitTransfer> selectListByAccountTypeAndCheckTime(Byte accountType, String accountParam, Date checkTime) {
        String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(checkTime);
        return billContractProfitTransferMapper.selectListByAccountTypeAndCheckTime(accountType, accountParam, checkTime, tableNameSuffix);
    }

    @Override
    public void createTableForMonth(Integer mounth) {
        if (mounth != null && mounth > 0) {
            Date nowDate = new Date();
            for (int i = 0; i < mounth; i++) {
                String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(DateUtil.addMonth(nowDate, i));
                createTable(tableNameSuffix);
            }
        }
    }


    @Override
    public Map<Integer, BigDecimal> selectAllUnProfitTransferByCheckOkTime(List<Byte> accountTypeList, Date checkOkTime) {
        List<Integer> typeEnums = Lists.newArrayList(ProfitTransferTypeEnum.SYSTEM_RESET.getCode(), ProfitTransferTypeEnum.SYSTEM_CONTRACT_FEE.getCode(), ProfitTransferTypeEnum.SYSTEM_SPOT_FEE.getCode(), ProfitTransferTypeEnum.SYSTEM_SPOT_FEE_RESET.getCode(), ProfitTransferTypeEnum.SYSTEM_LEVER_FEE.getCode(), ProfitTransferTypeEnum.SYSTEM_LEVER_FEE_RESET.getCode(), ProfitTransferTypeEnum.SYSTEM_CONTRACT_COST_INTEREST.getCode(), ProfitTransferTypeEnum.SYSTEM_CONTRACT_COST_INTEREST_RESET.getCode(), ProfitTransferTypeEnum.SYSTEM_UTA_SPOT_FEE.getCode(), ProfitTransferTypeEnum.SYSTEM_UTA_SPOT_FEE_RESET.getCode()
                , ProfitTransferTypeEnum.SYSTEM_UTA_CONTRACT_FEE.getCode(), ProfitTransferTypeEnum.SYSTEM_UTA_CONTRACT_FEE_RESET.getCode());
        String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(checkOkTime);
        List<BillContractProfitTransfer> transfers = Lists.newArrayList();
        transfers.addAll(billContractProfitTransferMapper.selectAllUnProfitTransferByCheckOkTime(accountTypeList, checkOkTime, typeEnums, tableNameSuffix));
        if (DateUtil.isFirstDayOfMonth(checkOkTime)) {
            tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(DateUtil.getLastMonthEndTime(checkOkTime));
            transfers.addAll(billContractProfitTransferMapper.selectAllUnProfitTransferByCheckOkTime(accountTypeList, checkOkTime, typeEnums, tableNameSuffix));
        }
        Map<Integer, BigDecimal> unProfitTransferMap = new HashMap<>();
        for (BillContractProfitTransfer transfer : transfers) {
            unProfitTransferMap.put(transfer.getCoinId(), transfer.getTransferCount());
        }
        return unProfitTransferMap;
    }

    @Override
    public Map<Integer, BigDecimal> getUserUnProfitTransfer(Long userId, List<Byte> toAccountTypeList, List<Integer> transferTypes, Date checkOkTime, Date transferTime) {
        String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(checkOkTime);
        List<BillContractProfitTransfer> transfers = billContractProfitTransferMapper.getUserUnProfitTransfer(userId, toAccountTypeList, checkOkTime, transferTime, transferTypes, tableNameSuffix);
        Map<Integer, BigDecimal> unProfitTransferMap = new HashMap<>();
        for (BillContractProfitTransfer transfer : transfers) {
            unProfitTransferMap.put(transfer.getCoinId(), transfer.getTransferCount());
        }
        return unProfitTransferMap;
    }

    @Override
    public Date getNotProfitTransferMinCheckOkTime(Date tableTime) {
        String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(tableTime);
        return billContractProfitTransferMapper.getNotProfitTransferMinCheckOkTime(tableNameSuffix);
    }

    private Integer createTable(String tableSuffix) {
        return billContractProfitTransferMapper.createTable(tableSuffix);
    }

    @Override
    public List<BillContractProfitTransfer> queryTransferRecordsByStatusAndTime(ProfitTransferStatusEnum profitTransferStatusEnum, Date executeTime) {
        String tableNameSuffix = billContractProfitTransferTableCreator.getTableSuffixName(executeTime);
        return billContractProfitTransferMapper.selectTransferRecordsByStatusAndTime(profitTransferStatusEnum.getCode(), tableNameSuffix);
    }

    @Override
    public List<BillContractProfitTransfer> listDelayedTransactionsByTime(Date checkOkTime) {
        List<BillContractProfitTransfer> result = new ArrayList<>();
        result.addAll(selectDelayedTransactionByStatusAndTime(checkOkTime));
        result.addAll(selectDelayedTransactionByTime(checkOkTime));

        if (CollectionUtils.isEmpty(result)) {
            return result;
        }

        return new ArrayList<>(result.stream().collect(Collectors.toMap(BillContractProfitTransfer::getCoinId, Function.identity(), (key1, key2) -> {
            key1.setTransferCount(key1.getTransferCount().add(key2.getTransferCount()));
            return key1;
        })).values());
    }


    /**
     * 添加到动账数据
     *
     * @param profitCoinDetails
     * @return
     */
    private List<BillContractProfitTransfer> generateProfitTransfersList(List<BillContractProfitCoinDetail> profitCoinDetails) {
        if (CollectionUtils.isEmpty(profitCoinDetails)) {
            return Lists.newArrayList();
        }
        Date currentDate = new Date();
        ApolloProfitTransferConfig apolloProfitTransferConfig = ReconciliationApolloConfigUtils.getApolloProfitTransferConfig();
        return profitCoinDetails.stream().map(element -> getBillContractProfitTransfer(element, apolloProfitTransferConfig.getTransferToUId(), currentDate))
                .collect(Collectors.toList());
    }

    private BillContractProfitTransfer getBillContractProfitTransfer(BillContractProfitCoinDetail coinDetail, Long transferInUserId, Date currentDate) {
        BillContractProfitTransfer billContractProfitTransfer = new BillContractProfitTransfer();
        billContractProfitTransfer.setBatchNo(coinDetail.getBatchNo());
        billContractProfitTransfer.setBizId(coinDetail.getId());
        billContractProfitTransfer.setSourceType(ProfitSourceTypeEnum.BILL_CHECK.getCode());
        billContractProfitTransfer.setId(noGenerator.nextNo());
        billContractProfitTransfer.setCheckOkTime(coinDetail.getCheckOkTime());
        billContractProfitTransfer.setAccountType(coinDetail.getAccountType());
        billContractProfitTransfer.setAccountParam(coinDetail.getAccountParam());
        billContractProfitTransfer.setToAccountType(AccountTypeEnum.SPOT.getCode());
        billContractProfitTransfer.setToAccountParam(AccountTypeEnum.SPOT.getAccountParam());
        billContractProfitTransfer.setCoinId(coinDetail.getCoinId());
        billContractProfitTransfer.setTransferCount(coinDetail.getProfitCountIncr());
        billContractProfitTransfer.setStatus(ProfitTransferStatusEnum.UN_TRANSFER.getCode());
        billContractProfitTransfer.setTransferType(Objects.requireNonNull(getProfitTransferTypeEnum(coinDetail.getProfitType())).getCode());
        billContractProfitTransfer.setTransferInUserId(transferInUserId);
        billContractProfitTransfer.setVersion(noGenerator.nextNo());
        billContractProfitTransfer.setCreateTime(currentDate);
        billContractProfitTransfer.setUpdateTime(currentDate);
        return billContractProfitTransfer;
    }

    private ProfitTransferTypeEnum getProfitTransferTypeEnum(String profitType) {
        ProfitTypeEnum typeEnum = ProfitTypeEnum.toEnum(profitType);
        if (Objects.isNull(typeEnum)) {
            throw new RuntimeException("getProfitTransferTypeEnum null");
        } else if (ProfitTypeEnum.COIN_PROFIT.equals(typeEnum)) {
            return ProfitTransferTypeEnum.COIN_PROFIT;
        } else if (ProfitTypeEnum.SYMBOL_PROFIT.equals(typeEnum)) {
            return ProfitTransferTypeEnum.SYMBOL_PROFIT;
        } else if (ProfitTypeEnum.MIX_CONTRACT_SYSTEM_DEAL_FEE.equals(typeEnum)) {
            return ProfitTransferTypeEnum.SYSTEM_CONTRACT_FEE;
        }
        throw new RuntimeException("getProfitTransferTypeEnum null");
    }

    @Override
    public List<BillContractProfitCoinDetail> getAllBillContractProfitCoinDetailList(Byte accountType,
                                                                                     String accountParam,
                                                                                     List<String> profitTypeList,
                                                                                     Date checkOkTime) {
        return billContractProfitCoinDetailMapper.getAllBillContractProfitCoinDetailList(accountType, accountParam, profitTypeList, checkOkTime);

    }
}




