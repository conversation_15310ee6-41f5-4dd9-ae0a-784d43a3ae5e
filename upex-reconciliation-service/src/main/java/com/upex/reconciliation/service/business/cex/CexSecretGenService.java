package com.upex.reconciliation.service.business.cex;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.model.config.ApolloThirdCexAssetConfig;
import com.upex.reconciliation.service.service.ThirdCexUserService;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.GenSecretRequest;
import com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum;
import com.upex.reconciliation.service.service.client.cex.utils.Ed25519Utils;
import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.KeyPair;
import java.util.concurrent.TimeUnit;

import static com.upex.reconciliation.service.common.constants.enums.BillRedisKeyEnum.THIRD_CEX_ASSET_SECRET_KEY;

@Service
public class CexSecretGenService {

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    ThirdCexUserService thirdCexUserService;
     public String generateSecret(GenSecretRequest request,String userId) throws Exception
    {
        ThirdCexUser user=thirdCexUserService.selectByCexTypeAndUserId(request.getCexType(),request.getCexUserId());
        if(user==null){
            throw new ApiException(ReconCexExceptionEnum.USER_NOT_EXISTS);
        }
        // 调用服务层生成密钥逻辑
        KeyPair keyPair = Ed25519Utils.generateKeyPair();
        // 转换为 PEM 并打印
        String pemPubKey = Ed25519Utils.encodePublicKeyToPEM(keyPair.getPublic());

        String pemPrivKey = Ed25519Utils.encodePrivateKeyToPEM(keyPair.getPrivate());
        pemPrivKey= HmacUtil.encrypt(pemPrivKey);
        Pair<String,String> secret = new Pair<>(pemPubKey,pemPrivKey);
        String secretKey=String.format(THIRD_CEX_ASSET_SECRET_KEY.getKey(), request.getCexType(), request.getCexUserId(),request.getApiKeyLabel(),userId);
        ApolloThirdCexAssetConfig config = ReconciliationApolloConfigUtils.getThirdCexAssetConfig();
        redisTemplate.opsForValue().set(secretKey, JSONObject.toJSONString(secret),config.getTmpSecretSave(), TimeUnit.MINUTES);
        return pemPubKey;
    }
}
