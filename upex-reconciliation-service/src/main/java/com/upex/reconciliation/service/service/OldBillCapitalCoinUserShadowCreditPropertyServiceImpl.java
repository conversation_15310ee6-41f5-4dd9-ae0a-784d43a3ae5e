package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.mapper.OldBillCapitalCoinUserShadowCreditPropertyMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class OldBillCapitalCoinUserShadowCreditPropertyServiceImpl implements OldBillCapitalCoinUserShadowCreditPropertyService {

    @Resource
    private BillDbHelper billDbHelper;

    @Resource
    private OldBillCapitalCoinUserShadowCreditPropertyMapper oldBillCapitalCoinUserShadowCreditPropertyMapper;


    @Override
    public void removeResetTimeShadowCreditList(Date resetCheckTime) {
        billDbHelper.doDbOpInSnapshotGlobalMaster(() -> {
            oldBillCapitalCoinUserShadowCreditPropertyMapper.deleteResetTimeShadowCreditList(resetCheckTime);
            return null;
        });
    }
}
