package com.upex.reconciliation.service.business;

import com.fiat.fund.facade.client.bill.FundAccountBillQueryFacade;
import com.upex.bill.facade.AccountAssetsService;
import com.upex.contract.entrance.feign2.AccountAssetsFeignClient;
import com.upex.financial.facade.bill.FinancialBillInnerFeign;
import com.upex.margin.facade.inner.InnerMarginCrossAssetsCheckFeignClient;
import com.upex.margin.facade.inner.InnerMarginIsolatedAssetsCheckFeignClient;
import com.upex.mixcontract.process.facade.feign.inner.InnerBillFeignClient;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.spot.facade.bill.SpotBillsService;
import com.upex.swap.facade.SwapBillsService;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2021-04-22 14:30
 * @desc
 **/
@Component
public class BusinessRegistry {

    @Resource
    private SpotBillsService spotBillsService;
    @Resource
    private AccountAssetsFeignClient contractBillsServiceFacade;
    @Resource
    private FundAccountBillQueryFacade otcBillsService;
    @Resource
    private InnerBillFeignClient mixContractService;
    @Resource
    private MixAccountAssetsCalExtensionBillService mixAccountAssetsCalExtensionBillService;
    @Resource
    private SwapBillsService swapBillsService;
    @Resource
    private InnerMarginCrossAssetsCheckFeignClient innerMarginCrossAssetsCheckFeignClient;
    @Resource
    private InnerMarginIsolatedAssetsCheckFeignClient innerMarginIsolatedAssetsCheckFeignClient;
    @Resource
    private FinancialBillInnerFeign financialBillInnerFeign;

    /**
     * 业务系统服务映射
     */
    private Map<AccountTypeEnum, AccountAssetsService> userBillHandleMap = new ConcurrentHashMap<>();

    /**
     * 业务系统计算用户资产扩展映射
     */
    private Map<AccountTypeEnum, MixAccountAssetsCalExtensionBillService> calExtensionMap = new ConcurrentHashMap<>();

    /**
     * 对账系统计算资产扩展映射
     */
    private Map<AccountTypeEnum, BillCalExtensionStrategy> billCalExtensionStrategyMap = new ConcurrentHashMap<>();


    @PostConstruct
    private void init() {
        userBillHandleMap.put(AccountTypeEnum.SPOT, spotBillsService);
        userBillHandleMap.put(AccountTypeEnum.SWAP_MAIN, contractBillsServiceFacade);
        userBillHandleMap.put(AccountTypeEnum.OTC, otcBillsService);
        userBillHandleMap.put(AccountTypeEnum.S_USDT_MIX_CONTRACT_BL, mixContractService);
        userBillHandleMap.put(AccountTypeEnum.S_USD_MIX_CONTRACT_BL, mixContractService);
        userBillHandleMap.put(AccountTypeEnum.USDT_MIX_CONTRACT_BL, mixContractService);
        userBillHandleMap.put(AccountTypeEnum.USD_MIX_CONTRACT_BL, mixContractService);
        userBillHandleMap.put(AccountTypeEnum.USDC_MIX_CONTRACT_BL, mixContractService);
        userBillHandleMap.put(AccountTypeEnum.S_USDC_MIX_CONTRACT_BL, mixContractService);
        userBillHandleMap.put(AccountTypeEnum.SWAP_CHAIN, swapBillsService);
        userBillHandleMap.put(AccountTypeEnum.LEVER_FULL, innerMarginCrossAssetsCheckFeignClient);
        userBillHandleMap.put(AccountTypeEnum.LEVER_ONE, innerMarginIsolatedAssetsCheckFeignClient);
        userBillHandleMap.put(AccountTypeEnum.FINANCIAL, financialBillInnerFeign);

        calExtensionMap.put(AccountTypeEnum.S_USDT_MIX_CONTRACT_BL, mixAccountAssetsCalExtensionBillService);
        calExtensionMap.put(AccountTypeEnum.S_USD_MIX_CONTRACT_BL, mixAccountAssetsCalExtensionBillService);
        calExtensionMap.put(AccountTypeEnum.USDT_MIX_CONTRACT_BL, mixAccountAssetsCalExtensionBillService);
        calExtensionMap.put(AccountTypeEnum.USD_MIX_CONTRACT_BL, mixAccountAssetsCalExtensionBillService);
        calExtensionMap.put(AccountTypeEnum.USDC_MIX_CONTRACT_BL, mixAccountAssetsCalExtensionBillService);
        calExtensionMap.put(AccountTypeEnum.S_USDC_MIX_CONTRACT_BL, mixAccountAssetsCalExtensionBillService);

//        billCalExtensionStrategyMap.put(AccountTypeEnum.SWAP_MAIN, swapBillCalExtensionStrategyImpl);
//        billCalExtensionStrategyMap.put(AccountTypeEnum.S_USDT_MIX_CONTRACT_BL, mixBillCalExtensionStrategyImpl);
//        billCalExtensionStrategyMap.put(AccountTypeEnum.S_USD_MIX_CONTRACT_BL, mixBillCalExtensionStrategyImpl);
//        billCalExtensionStrategyMap.put(AccountTypeEnum.USDT_MIX_CONTRACT_BL, mixBillCalExtensionStrategyImpl);
//        billCalExtensionStrategyMap.put(AccountTypeEnum.USD_MIX_CONTRACT_BL, mixBillCalExtensionStrategyImpl);
//        billCalExtensionStrategyMap.put(AccountTypeEnum.USDC_MIX_CONTRACT_BL, mixBillCalExtensionStrategyImpl);
//        billCalExtensionStrategyMap.put(AccountTypeEnum.S_USDC_MIX_CONTRACT_BL, mixBillCalExtensionStrategyImpl);
//        coinNetChargeAmountServiceMap.put(CoinNetChargeAmountServiceContant.SPOT_SERVICE, spotChainAssetBillsQueryClient);
//        coinNetChargeAmountServiceMap.put(CoinNetChargeAmountServiceContant.LEGAL_CURRENCY_SERVICE, fiatBillFacade);

    }

    /**
     * 获取业务系统对账处理接口
     *
     * @param accountTypeEnum
     * @return
     */
    public AccountAssetsService getAccountAssetsService(AccountTypeEnum accountTypeEnum) {
        return userBillHandleMap.get(accountTypeEnum);
    }

    /**
     * 获取业务系统扩展处理类
     *
     * @return
     */
    public MixAccountAssetsCalExtensionBillService getBusinessCalExtension(AccountTypeEnum accountTypeEnum) {
        return calExtensionMap.get(accountTypeEnum);
    }

    /**
     * 获取对账系统资产计算扩展策略类
     *
     * @return
     */
    public BillCalExtensionStrategy getBillCalExtensionStrategy(AccountTypeEnum accountTypeEnum) {
        return billCalExtensionStrategyMap.get(accountTypeEnum);
    }

}
