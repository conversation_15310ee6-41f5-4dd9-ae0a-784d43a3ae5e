package com.upex.reconciliation.service.common;

import org.springframework.dao.DataAccessException;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import javax.sql.DataSource;
import java.util.List;

public class DynamicJdbcTemplate extends JdbcTemplate {

    public DynamicJdbcTemplate() {
        super();
    }

    public DynamicJdbcTemplate(DataSource dataSource) {
        super(dataSource);
    }

    @Override
    public <T> T queryForObject(String sql, Class<T> requiredType) throws DataAccessException {
        return this.queryForObject(sql, this.getSingleColumnRowMapper(requiredType));
    }

    @Override
    public <T> T queryForObject(String sql, RowMapper<T> rowMapper) throws DataAccessException {
        List<T> results = super.query(sql, rowMapper);
        return nullableSingleResult(results);
    }

    private <T> T nullableSingleResult(List<T> results) {
        if (CollectionUtils.isEmpty(results)) {
            return null;
        } if (results.size() > 1) {
            throw new IncorrectResultSizeDataAccessException(1, results.size());
        } else {
            return results.iterator().next();
        }
    }
}
