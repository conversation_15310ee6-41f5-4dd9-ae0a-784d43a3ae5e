package com.upex.reconciliation.service.business.cex;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexTransferHistory;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexPayTransferHistory;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.service.ThirdCexUserService;
import com.upex.reconciliation.service.service.client.cex.client.CexApiClientFactory;
import com.upex.reconciliation.service.service.client.cex.client.ICexApiClient;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CommonReq;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.UserPayTransferHistoryListReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.*;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.user.CexSubUserListRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.user.CommonUserStatusRes;
import com.upex.reconciliation.service.service.client.cex.enmus.CexTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum;
import com.upex.reconciliation.service.service.client.cex.funcs.CexApiInvoker;
import com.upex.reconciliation.service.service.client.cex.utils.CexUserHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

import static com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum.APICLIENT_NOT_FOUND;


@Slf4j
@Service
public class CexApiService {

    @Resource
    CexApiClientFactory cexApiClientFactory;

    @Resource
    CexUserConfigBizService cexUserConfigBizService;

    @Resource
    ThirdCexUserService thirdCexUserService;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    AlarmNotifyService alarmNotifyService;

    public CommonRes<CommonCoinAssetRes> querySpotCoinAsset(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.querySpotCoinAsset(req));

    }

    public CommonRes<CommonCoinAssetRes> queryFundingCoinAsset(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryFundingCoinAsset(req));

    }

    public CommonRes<CommonCoinAssetRes> queryUContractCoinAsset(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryUContractCoinAsset(req));

    }

    public CommonRes<CommonCoinAssetRes> queryCoinContractCoinAsset(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryCoinContractCoinAsset(req));

    }

    public CommonRes<CommonCoinAssetRes> queryMarginCoinAsset(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryMarginCoinAsset(req));

    }

    public CommonRes<CommonCoinAssetRes> queryIsolatedMarginCoinAsset(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryIsolatedMarginCoinAsset(req));
    }

    public CommonRes<CommonCoinAssetRes> queryFlexEarnPosition(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryFlexEarnPosition(req));
    }

    public CommonRes<CommonCoinAssetRes> queryLockedEarnPosition(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryLockedEarnPosition(req));
    }

    public CommonRes<CommonApiKeyPermissionRes> queryApikeyPermission(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryApikeyPermission(req));
    }

    public CommonRes<CommonUserStatusRes> queryUserStatus(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryUserStatus(req));
    }

    public CommonRes<CexSubUserListRes> querySubUserList(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.querySubUserList(req));
    }

    public CommonRes<CommonCoinAssetRes> querySpotPosition(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.querySpotPosition(req));
    }

    public CommonRes<CommonCoinAssetRes> querySubUserSpotAsset(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.querySubUserSpotAsset(req));
    }

    public CommonRes<CommonCoinAssetRes> querySubUserMarginAsset(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.querySubUserMarginAsset(req));
    }

    public CommonRes<CommonCoinAssetRes> querySubUserUContractAsset(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.querySubUserUContractAsset(req));
    }

    public CommonRes<CommonCoinAssetRes> querySubUserCoinContractAsset(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.querySubUserCoinContractAsset(req));
    }

    public CommonRes<CommonCoinInfoListRes> querySupportCoinList(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.querySupportCoinList(req));
    }

    public CommonRes<CommonDepositeAddressRes> queryUserDepositeAddress(CommonReq commonReq) {
        ThirdCexUser user = thirdCexUserService.selectByCexTypeAndUserId(commonReq.getCexType(), commonReq.getCexUserId());
        if(user==null){
            throw new ApiException(ReconCexExceptionEnum.USER_NOT_EXISTS);
        }
        if(user.getParentUserId()!=null){
            throw new ApiException(ReconCexExceptionEnum.SUBUSER_NOT_SUPPOERT);
        }
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryDepositeAddress(req));
    }

    public CommonRes<CommonDepositeHistoryRes> queryUserDepositeHistory(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryUserDepositeHistory(req));
    }

    public CommonRes<CommonWithdrawHistoryRes> queryUserWithdrawHistory(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryUserWithdrawHistory(req));
    }

    public CommonRes<List<ThirdCexPayTransferHistory>> queryPayTransferHistory(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryPayTransferHistory(req));
    }

    public CommonRes<List<ThirdCexTransferHistory>> queryParentSubTransferRecord(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryParentSubTransferRecord(req));
    }

    public CommonRes<List<ThirdCexTransferHistory>> queryUniversialTransferList(CommonReq commonReq) {
        return execute(commonReq, (cexApiClient, req) -> cexApiClient.queryUniversialTransferList(req));
    }

    public CommonRes execute(CommonReq commonReq, CexApiInvoker invoker) {
        try {
            ThirdCexUser thirdCexUser = thirdCexUserService.selectByCexTypeAndUserId(commonReq.getCexType(), commonReq.getCexUserId());
            if(thirdCexUser==null){
                return CommonRes.getFailApiBaseRes(ReconCexExceptionEnum.USER_NOT_EXISTS);
            }
            CexUserHolder.setUserInfo(thirdCexUser.getCexUserId(), thirdCexUser.getCexEmail());
            if (commonReq.getApiKey() == null && commonReq.getPrivateKey() == null) {
                ThirdCexUserConfig thirdCexUserConfig = cexUserConfigBizService.getThirdCexUserConfigByUserId(commonReq.getCexType(), commonReq.getCexUserId());
                commonReq.setApiKey(thirdCexUserConfig.getApiKey());
                commonReq.setPrivateKey(thirdCexUserConfig.getApiKeyPrivate());
                CexUserHolder.setUserInfo(thirdCexUserConfig.getCexUserId(), thirdCexUserConfig.getCexEmail());
            }
            ICexApiClient cexApiClient = cexApiClientFactory.getCexApiClient(commonReq.getCexType());
            if (cexApiClient == null) {
                throw new ApiException(APICLIENT_NOT_FOUND);
            }
            return invoker.apply(cexApiClient, commonReq);
        } catch (ApiException e){
            log.info("捕获到 upex-ApiException: {}",  e);
            return CommonRes.getFailApiBaseRes(ReconCexExceptionEnum.getByCode(e.getCode()));
        }
        catch (
                Exception e) {
            log.error("未知异常: ", e);
            alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_API_UNKONW_ERROR,commonReq.getCexUserId(),CexTypeEnum.fromType(commonReq.getCexType()).getName(), e.getMessage());
            return CommonRes.getUnknowApiErrorBaseRes();
        }finally {
            CexUserHolder.clear();
        }
    }
}
