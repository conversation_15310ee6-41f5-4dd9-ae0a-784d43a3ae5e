package com.upex.reconciliation.service.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.upex.commons.support.exception.ApiException;
import com.upex.mixcontract.common.literal.MixConstants;
import com.upex.mixcontract.common.literal.enums.ExceptionEnum;
import com.upex.mixcontract.common.literal.enums.HoldSideEnum;
import com.upex.mixcontract.common.utils.ContractFormulaUtils;
import com.upex.mixcontract.process.facade.dto.MixAccountAssetsExtension;
import com.upex.reconciliation.facade.dto.results.ReconAccountAssetsInfoResult;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.dao.entity.BillCoinUserAssets;
import com.upex.reconciliation.service.model.domain.MixContractAssetDto;
import com.upex.reconciliation.service.model.dto.BillCoinUserAssetsParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-11-22 17:28
 * @desc
 **/
@Slf4j
public class MixExtensionCalUtil {

    /**
     * 计算账户币本位账户权益
     *
     * @param coinId 币种
     * @param params 参数
     * @param prop2 参数
     * @param prop3 参数
     * @param accountParam
     * @param markPriceMap
     * @param rateMap
     * @return
     */
    public static MixContractAssetDto calAccountCoinEquity(Integer coinId, String params, BigDecimal prop2, BigDecimal prop3, String accountParam, Map<String, BigDecimal> markPriceMap, Map<Integer, BigDecimal> rateMap) {
        if (StringUtils.isEmpty(accountParam)) {
            throw new ApiException(ExceptionEnum.PARAM_VALIDATE_ERROR);
        }
        //多币对信息
        List<MixAccountAssetsExtension> assetsExtensionList = Lists.newArrayList();
        if (!StringUtils.isEmpty(params)) {
            assetsExtensionList = JSONObject.parseArray(params, MixAccountAssetsExtension.class);
        }
        BigDecimal unRealised = BigDecimal.ZERO;
        boolean hasPositionFlag = false;
        if (!CollectionUtils.isEmpty(assetsExtensionList)) {
            for (MixAccountAssetsExtension assetsExtension : assetsExtensionList) {
                BigDecimal markPrice = markPriceMap.get(assetsExtension.getSId());
                if (markPrice == null) {
                    throw new ApiException(ExceptionEnum.PARAM_VALIDATE_ERROR);
                }
                assetsExtension.setUnR(BigDecimal.ZERO);
                if (assetsExtension.getLCount().compareTo(BigDecimal.ZERO) > 0) {
                    hasPositionFlag = true;
                    assetsExtension.setUnR(assetsExtension.getUnR().add(ContractFormulaUtils.calProfits(HoldSideEnum.LONG_POSITIONS, assetsExtension.getLCount(),
                            assetsExtension.getLAvg(), markPrice, null)));
                }
                if (assetsExtension.getSCount().compareTo(BigDecimal.ZERO) > 0) {
                    hasPositionFlag = true;
                    assetsExtension.setUnR(assetsExtension.getUnR().add(ContractFormulaUtils.calProfits(HoldSideEnum.SHORT_POSITIONS, assetsExtension.getSCount(),
                            assetsExtension.getSAvg(), markPrice, null)));
                }
                unRealised = unRealised.add(assetsExtension.getUnR());
            }
        }

        BigDecimal settlePrice = rateMap.get(coinId);
        if (settlePrice == null) {
            settlePrice = BigDecimal.ONE;
        }
        BigDecimal unRealisedInSettleTokenId = unRealised.divide(settlePrice, MixConstants.SERVER_PLACE, BigDecimal.ROUND_DOWN);
        //权益 = 未实现 + 逐仓保证金 + 余额
        BigDecimal coinTotalCount = unRealisedInSettleTokenId.add(prop3).add(prop2);
        return new MixContractAssetDto(coinTotalCount, hasPositionFlag);
    }

    /**
     * 计算账户币本位账户权益
     *
     * @param accountAssets
     * @param accountParam
     * @param markPriceMap
     * @param rateMap
     * @return
     */
    public static MixContractAssetDto calAccountCoinEquity(ReconAccountAssetsInfoResult accountAssets, String accountParam, Map<String, BigDecimal> markPriceMap, Map<Integer, BigDecimal> rateMap) {
        return calAccountCoinEquity(accountAssets.getCoinId(),accountAssets.getParams(),accountAssets.getProp2(),accountAssets.getProp3(),accountParam, markPriceMap, rateMap);
    }


    public static MixContractAssetDto calAccountCoinEquityV2(Integer coinId, List<BillCoinUserAssetsParam> assetsExtensionList, BigDecimal prop2, BigDecimal prop3, Map<String, BigDecimal> markPriceMap, Map<Integer, BigDecimal> rateMap) {
        //多币对信息
        BigDecimal unRealised = BigDecimal.ZERO;
        boolean hasPositionFlag = false;
        if (!CollectionUtils.isEmpty(assetsExtensionList)) {
            for (MixAccountAssetsExtension assetsExtension : assetsExtensionList) {
                BigDecimal markPrice = markPriceMap.get(assetsExtension.getSId());
                if (markPrice == null) {
                    throw new ApiException(ExceptionEnum.PARAM_VALIDATE_ERROR);
                }
                assetsExtension.setUnR(BigDecimal.ZERO);
                if (assetsExtension.getLCount().compareTo(BigDecimal.ZERO) > 0) {
                    hasPositionFlag = true;
                    assetsExtension.setUnR(assetsExtension.getUnR().add(ContractFormulaUtils.calProfits(HoldSideEnum.LONG_POSITIONS, assetsExtension.getLCount(),
                            assetsExtension.getLAvg(), markPrice, null)));
                }
                if (assetsExtension.getSCount().compareTo(BigDecimal.ZERO) > 0) {
                    hasPositionFlag = true;
                    assetsExtension.setUnR(assetsExtension.getUnR().add(ContractFormulaUtils.calProfits(HoldSideEnum.SHORT_POSITIONS, assetsExtension.getSCount(),
                            assetsExtension.getSAvg(), markPrice, null)));
                }
                unRealised = unRealised.add(assetsExtension.getUnR());
            }
        }

        BigDecimal settlePrice = rateMap.get(coinId);
        if (settlePrice == null) {
            settlePrice = BigDecimal.ONE;
        }
        BigDecimal unRealisedInSettleTokenId = unRealised.divide(settlePrice, MixConstants.SERVER_PLACE, BigDecimal.ROUND_DOWN);
        //权益 = 未实现 + 逐仓保证金 + 余额
        BigDecimal coinTotalCount = unRealisedInSettleTokenId.add(prop3).add(prop2);
        return new MixContractAssetDto(coinTotalCount, hasPositionFlag);

    }

    /**
     * 计算账户币本位账户权益
     *
     * @param accountAssets
     * @param markPriceMap
     * @param rateMap
     * @return
     */
    public static MixContractAssetDto calAccountCoinEquity(ReconAccountAssetsInfoResult accountAssets, Map<String, BigDecimal> markPriceMap, Map<Integer, BigDecimal> rateMap) {
        return calAccountCoinEquity(accountAssets, BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM,markPriceMap,rateMap);
    }

    /**
     * 计算账户币本位账户权益
     *
     * @param billCoinUserAssets
     * @param markPriceMap
     * @param rateMap
     * @return
     */
    public static MixContractAssetDto calAccountCoinEquity(BillCoinUserAssets billCoinUserAssets, Map<String, BigDecimal> markPriceMap, Map<Integer, BigDecimal> rateMap) {
        return calAccountCoinEquity(billCoinUserAssets.getCoinId(),billCoinUserAssets.getParams(),billCoinUserAssets.getProp2(),billCoinUserAssets.getProp3(),BillConstants.DEFAULT_ASSETS_CHECK_TYPE_PARAM, markPriceMap, rateMap);
    }


}
