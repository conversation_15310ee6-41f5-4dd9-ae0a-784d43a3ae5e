package com.upex.reconciliation.service.service.client.cex.config.binance;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.PropertyPreFilter;
import com.upex.reconciliation.service.model.config.ApolloThirdCexAssetConfig;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CommonReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.binance.CoinContractAccountInfoInnerRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.binance.CoinContractAccountInfoRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.binance.DepositeAddressInnerRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.binance.DepositeAddressRes;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
@Component
public class SensitiveFieldFilter  implements PropertyPreFilter {
    private static final Set<String> SENSITIVE_FIELDS = new HashSet<>(Arrays.asList(
            "cexEmail","email", "address", "apiKey", "privateKey", "secretKey", "token"
    ));

    @PostConstruct
    private void init() {
        ApolloThirdCexAssetConfig config =ReconciliationApolloConfigUtils.getThirdCexAssetConfig();
        if(CollectionUtils.isNotEmpty(config.getSensitiveFieldList())){
            SENSITIVE_FIELDS.addAll(config.getSensitiveFieldList());
        }
    }

    @Override
    public boolean apply(JSONSerializer serializer, Object source, String name) {
        return !SENSITIVE_FIELDS.contains(name);
    }

    public static void main(String[] args) {
        SensitiveFieldFilter filter = new SensitiveFieldFilter();
        CommonReq commonReq=new CommonReq();
        commonReq.setCexEmail("<EMAIL>");
        commonReq.setCexType(1);
        commonReq.setPrivateKey("priv123");
        commonReq.setCexUserId("user123");
        String filteredReq = JSONObject.toJSONString(commonReq, filter);

        DepositeAddressRes response = new DepositeAddressRes();
        DepositeAddressInnerRes innerRes = new DepositeAddressInnerRes();
        innerRes.setCoin("BTC");
        innerRes.setAddress("0x1234567890abcdef");
        innerRes.setTag("tag123");
        response.add(innerRes);
//        String filteredRes = JSONObject.toJSONString(response.getData(), filter);
        CoinContractAccountInfoRes coinContractAccountInfoRes=new CoinContractAccountInfoRes();
        CoinContractAccountInfoInnerRes coinContractAccountInfoInnerRes=new CoinContractAccountInfoInnerRes();
        coinContractAccountInfoInnerRes.setAsset("BTC");
        coinContractAccountInfoInnerRes.setWalletBalance(new BigDecimal("100.00"));
        coinContractAccountInfoInnerRes.setAvailableBalance(new BigDecimal("50.00"));
        coinContractAccountInfoInnerRes.setMaxWithdrawAmount(new BigDecimal("20.00"));
        coinContractAccountInfoInnerRes.setUpdateTime(System.currentTimeMillis());
        List<CoinContractAccountInfoInnerRes> assets=new ArrayList<>();
        assets.add(coinContractAccountInfoInnerRes);
        coinContractAccountInfoRes.setAssets( assets);

        System.out.println("Filtered Request: " + JSONObject.toJSONString(coinContractAccountInfoRes,filter));

    }
}
