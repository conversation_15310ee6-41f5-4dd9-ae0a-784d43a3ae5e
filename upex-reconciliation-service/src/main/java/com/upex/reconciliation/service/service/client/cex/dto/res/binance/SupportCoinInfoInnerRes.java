package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SupportCoinInfoInnerRes {


    /**
     * "coin": "AGLD",
     *         "depositAllEnable": true,
     *         "withdrawAllEnable": true,
     *         "name": "Adventure Gold",
     *         "free": "0",
     *         "locked": "0",
     *         "freeze": "0",
     *         "withdrawing": "0",
     *         "ipoing": "0",
     *         "ipoable": "0",
     */
    private String coin;
    private String name;
    private Boolean depositEnable;
    private Boolean withdrawEnable;
    private BigDecimal  free;
    private BigDecimal  locked;
    private BigDecimal  freeze;
    private BigDecimal  withdrawing;
    private BigDecimal  ipoing;
    private BigDecimal  ipoable;
    private List<CoinNetwork> networkList;

    @Data
    public class CoinNetwork {
        /**
         * "network": "SCROLL",
         *                 "coin": "SCR",
         *                 "withdrawIntegerMultiple": "0.00000001",
         *                 "isDefault": true,
         *                 "depositEnable": true,
         *                 "withdrawEnable": true,
         *                 "depositDesc": "",
         *                 "withdrawDesc": "",
         *                 "specialTips": "SCROLL is a L2 network. After crediting, its corresponding L1 transaction needs to be confirmed before unlocking, which takes approximately 1-2 hours.",
         *                 "specialWithdrawTips": "",
         *                 "name": "Scroll",
         *                 "resetAddressStatus": false,
         *                 "addressRegex": "^(0x)[0-9A-Fa-f]{40}$",
         *                 "memoRegex": "",
         *                 "withdrawFee": "0.2",
         *                 "withdrawMin": "4",
         *                 "withdrawMax": "9999999",
         *                 "withdrawInternalMin": "0",
         *                 "depositDust": "0.00000001",
         *                 "minConfirm": 1,
         *                 "unLockConfirm": 0,
         *                 "sameAddress": false,
         *                 "estimatedArrivalTime": 1,
         *                 "busy": false,
         *                 "contractAddressUrl": "https://scrollscan.com/token/",
         *                 "contractAddress": "0xd29687c813d741e2f938f4ac377128810e217b1b"
         */

        private String coin;
        private String network;
        private Boolean isDefault;
        private Boolean depositEnable;
        private Boolean withdrawEnable;
        private String depositDesc;
        private String withdrawDesc;
        private String name;

    }
}
