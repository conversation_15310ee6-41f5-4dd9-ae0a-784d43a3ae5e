package com.upex.reconciliation.service.dao.mapper.cex;

import com.upex.reconciliation.service.dao.cex.entity.ThirdCexDepositeHistory;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.UserDepositeHistoryListReq;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface ThirdCexDepositeHistoryMapper {

    int insert(ThirdCexDepositeHistory history);

    ThirdCexDepositeHistory selectById(Long id);

    List<ThirdCexDepositeHistory> selectByCexUserAndType(@Param("cexUserId") Long cexUserId, @Param("cexType") Integer cexType);

    int update(ThirdCexDepositeHistory history);

    List<ThirdCexDepositeHistory> selectList(
            @Param("cexUserId") Long cexUserId,
            @Param("cexType") Integer cexType,
            @Param("coinName") String coinName,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);

    int batchInsert(List<ThirdCexDepositeHistory> records);

    List<ThirdCexDepositeHistory> selectPageByUserIds( List<String> cexUserIds,  UserDepositeHistoryListReq depositeReq);

    int countPageByUserIds( List<String> cexUserIds,  UserDepositeHistoryListReq depositeReq);

    int deleteByDepositeIds(@Param("depositeIds") List<String> depositeIds);
}

