package com.upex.reconciliation.service.business.module;

import com.upex.mixcontract.common.framework.AttributeMap;
import com.upex.mixcontract.common.framework.module.AbstractModule;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.model.BillCmdResult;
import com.upex.reconciliation.service.model.BillCmdWrapper;

import java.util.concurrent.BlockingQueue;

public abstract class AbstractBillModule extends AbstractModule<BillLogicGroup> {
    protected BlockingQueue<BillCmdWrapper> cmdQueue;
    protected AccountTypeEnum accountTypeEnum;

    public AbstractBillModule(BillLogicGroup logicGroup) {
        super(logicGroup);
    }

    @Override
    public void init(AttributeMap initContext) {

    }

    /**
     * 消费 消息
     * 从队列中拿数据，同时处理业务逻辑
     *
     * @return
     */
    public abstract BillCmdResult takeCommand();


    public abstract void offerCommand(BillCmdWrapper cmdWrapper);
}
