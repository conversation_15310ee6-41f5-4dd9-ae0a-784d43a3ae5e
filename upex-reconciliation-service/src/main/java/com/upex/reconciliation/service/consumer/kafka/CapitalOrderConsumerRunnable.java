package com.upex.reconciliation.service.consumer.kafka;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.client.kafka.MessageDeserializer;
import com.alibaba.otter.canal.connector.core.producer.MQMessageUtils;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.alibaba.otter.canal.protocol.Message;
import com.google.common.base.Stopwatch;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.upex.reconciliation.facade.enums.SQLTypeEnum;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.ReconInnerTransferService;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.entity.CapitalOrder;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.service.impl.CapitalOrderService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 同步充值订单同步
 */
@Slf4j
public class CapitalOrderConsumerRunnable implements KafkaConsumerLifecycle, ApplicationContextAware {
    private volatile boolean running = true;
    private Map<String, Object> consumerConfig;

    private AlarmNotifyService alarmNotifyService;
    private Set<Integer> closeConsumerPatition = new HashSet<>();
    private KafkaConsumer consumer;
    private CapitalOrderService capitalOrderService;
    private Integer partitionNum;
    private Map<Integer, KafkaConsumer<String, Message>> partitionConsumerMap = new HashMap<>();

    private KafkaConsumerConfig kafkaApolloConsumerConfig;
    @Resource
    private ReconciliationSpringContext context;
    private ReconInnerTransferService reconInnerTransferService;

    private String topic = KafkaTopicEnum.RECON_CAPITAL_ORDER_SYNC_TOPIC.getCode();

    public CapitalOrderConsumerRunnable(ReconciliationSpringContext context, String kafkaServerAddr, KafkaConsumerConfig kafkaConsumerConfig, ReconInnerTransferService reconInnerTransferService) {
        this.capitalOrderService = context.getCapitalOrderService();
        this.kafkaApolloConsumerConfig = kafkaConsumerConfig;
        this.partitionNum = kafkaConsumerConfig.getPartitionNum();
        this.reconInnerTransferService = reconInnerTransferService;
        consumerConfig = new HashMap<String, Object>();
        alarmNotifyService = context.getAlarmNotifyService();
        consumerConfig.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServerAddr);
        consumerConfig.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerConfig.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MessageDeserializer.class);
        consumerConfig.put(ConsumerConfig.GROUP_ID_CONFIG, kafkaApolloConsumerConfig.getConsumerGroupId());
        consumerConfig.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, kafkaApolloConsumerConfig.getKafkaBatchSize());
        consumerConfig.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        consumerConfig.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, OffsetResetStrategy.EARLIEST.toString());
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.context = (ReconciliationSpringContext) applicationContext.getBean(ReconciliationSpringContext.class);
    }

    @Override
    public void shutdown() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return closeConsumerPatition.size() != 1;
    }

    @Override
    public String getThreadPrefixName() {
        return "kafka-consumer-thread-capital-order-consumer";
    }

    @Override
    public void run() {
        try {
            // 初始化
            log.info("CapitalOrderConsumerRunnable consumerRunnables.run");
            consumer = new KafkaConsumer<>(consumerConfig);
            consumer.subscribe(Collections.singletonList(topic));
            log.info("CapitalOrderConsumerRunnable init finished");
            startConsume(0, consumer);
        } catch (Exception e) {
            log.error("CapitalOrderConsumerRunnable consumerRunnables Exception:", e);
        }
    }


    private void startConsume(Integer partition, KafkaConsumer<String, Message> consumer) {
        log.info("CapitalOrderConsumerRunnable consumerRunnables.run partition {}", partition);
        KafkaConsumerConfig kafkaConsumerConfig;
        Stopwatch stopwatch;
        while (running) {
            try {
                kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig(KafkaTopicEnum.RECON_CAPITAL_ORDER_SYNC_TOPIC);
                if (!kafkaConsumerConfig.getIsOpen()) {
                    TimeUnit.SECONDS.sleep(kafkaConsumerConfig.getSleepTime());
                    continue;
                }
                // 从kafka集群中拉取消息df
                ConsumerRecords<String, Message> consumerRecords = consumer.poll(3000);
                stopwatch = Stopwatch.createStarted();
                log.info("CapitalOrderConsumerRunnable consumerRunnables.run partition {} consumerRecords size {}", partition, consumerRecords.count());
                for (ConsumerRecord<String, Message> consumerRecord : consumerRecords) {
                    if (kafkaConsumerConfig.getShowLogOpen()) {
                        log.info("接收到消息：{}", consumerRecord.value());
                    }
                    MQMessageUtils.EntryRowData[] datas = buildMessageData(consumerRecord.value());
                    List<FlatMessage> flatMessages = MQMessageUtils.messageConverter(datas, consumerRecord.value().getId()); // 串行分区
                    handle(flatMessages, kafkaConsumerConfig);
                }
                log.info("CapitalOrderConsumerRunnable consumerRunnables.run partition {} consumerRecords size {} execute success, time:{}", partition, consumerRecords.count(), stopwatch.stop());
                // 保存billUser
                // 手动提交位点
                consumer.commitSync();
            } catch (Exception e) {
                log.error("CapitalOrderConsumerRunnable startConsume partition:{}, error ", partition, e);
            }
        }

        consumer.close();
        closeConsumerPatition.add(partition);
        log.info("CapitalOrderConsumerRunnable consumer.close success {}", partition);
    }


    public void handle(List<FlatMessage> flatMessages, KafkaConsumerConfig kafkaConsumerConfig) {
        if (kafkaConsumerConfig.getShowLogOpen()) {
            log.info("CapitalOrderConsumerRunnable startConsume flatMessages:{} ", JSONObject.toJSONString(flatMessages));
        }
        if (CollectionUtils.isNotEmpty(flatMessages)) {
            Stopwatch stopwatch = Stopwatch.createStarted();
            Map<SQLTypeEnum, List<FlatMessage>> collect = flatMessages.stream()
                    .filter(item -> "spot_capital_order_info".equals(item.getTable()) && StringUtils.isNotBlank(item.getType()))
                    .collect(Collectors.groupingBy(item -> SQLTypeEnum.convert(item.getType())));
            if (kafkaConsumerConfig.getShowLogOpen()) {
                log.info("CapitalOrderConsumerRunnable startConsume collect keys:{} time:{}", collect.keySet(), stopwatch.stop());
                stopwatch.reset().start();
            }

            // 优先处理INSERT类型
            if (collect.containsKey(SQLTypeEnum.INSERT)) {
                List<CapitalOrder> insertOrders = buildBillChangeDataList(collect.get(SQLTypeEnum.INSERT));
                if (kafkaConsumerConfig.getShowLogOpen()) {
                    log.info("CapitalOrderConsumerRunnable startConsume collect INSERT capitalOrders size:{} time:{}",
                            CollectionUtils.size(insertOrders), stopwatch.stop());
                    stopwatch.reset().start();
                }
                capitalOrderService.batchSaveCapitalOrder(insertOrders, false, kafkaConsumerConfig); // INSERT 不用标记为UPDATE
                log.info("CapitalOrderConsumerRunnable startConsume capitalOrderService.batchSaveCapitalOrder success for INSERT size:{} time:{}", CollectionUtils.size(insertOrders), stopwatch.stop());
                stopwatch.reset().start();
                // 发送订单数据到redis
                reconInnerTransferService.reportedOrderData(insertOrders);
                if (kafkaConsumerConfig.getShowLogOpen()) {
                    log.info("CapitalOrderConsumerRunnable startConsume capitalOrderService.batchsaveCapitalOrder success for INSERT reportedOrderData time:{}", stopwatch.stop());
                    stopwatch.reset().start();
                    // 过滤出 insertOrders 中修改时间最小的订单
                    insertOrders.stream().min(Comparator.comparing(CapitalOrder::getSourceUpdateDate)).ifPresent(minOrder -> log.info("CapitalOrderConsumerRunnable startConsume minOrder:{}, current time:{}", minOrder.getSourceUpdateDate(), System.currentTimeMillis()));
                    log.info("CapitalOrderConsumerRunnable INSERT startConsume time:{}", stopwatch.stop());
                    stopwatch.reset().start();
                }
            }

            // 然后处理UPDATE类型
            if (collect.containsKey(SQLTypeEnum.UPDATE)) {
                List<CapitalOrder> updateOrders = buildBillChangeDataList(collect.get(SQLTypeEnum.UPDATE));
                if (kafkaConsumerConfig.getShowLogOpen()) {
                    log.info("CapitalOrderConsumerRunnable startConsume collect UPDATE capitalOrders size:{} time:{}",
                            CollectionUtils.size(updateOrders), stopwatch.stop());
                    stopwatch.reset().start();
                }
                capitalOrderService.batchSaveCapitalOrder(updateOrders, true, kafkaConsumerConfig); // UPDATE 标记为UPDATE
                log.info("CapitalOrderConsumerRunnable startConsume capitalOrderService.batchSaveCapitalOrder success for UPDATE size:{} time:{}", CollectionUtils.size(updateOrders), stopwatch.stop());
                stopwatch.reset().start();
                if (kafkaConsumerConfig.getShowLogOpen()) {
                    log.info("CapitalOrderConsumerRunnable startConsume capitalOrderService.batchsaveCapitalOrder success for UPDATE");
                    // 过滤出 insertOrders 中修改时间最小的订单
                    updateOrders.stream().min(Comparator.comparing(CapitalOrder::getSourceUpdateDate)).ifPresent(minOrder -> log.info("CapitalOrderConsumerRunnable startConsume minOrder:{}, current time:{}", minOrder.getSourceUpdateDate(), System.currentTimeMillis()));
                    log.info("CapitalOrderConsumerRunnable UPDATE startConsume time:{}", stopwatch.stop());
                }
            }
        }
    }

    /**
     * cannal消息解析
     *
     * @param message
     * @return
     */
    public static MQMessageUtils.EntryRowData[] buildMessageData(Message message) {
        if (message.isRaw()) {
            List<ByteString> rawEntries = message.getRawEntries();
            MQMessageUtils.EntryRowData[] datas = new MQMessageUtils.EntryRowData[rawEntries.size()];
            int i = 0;
            for (Iterator var12 = rawEntries.iterator(); var12.hasNext(); ++i) {
                ByteString byteString = (ByteString) var12.next();
                try {
                    CanalEntry.Entry entry = CanalEntry.Entry.parseFrom(byteString);
                    CanalEntry.RowChange rowChange = CanalEntry.RowChange.parseFrom(entry.getStoreValue());
                    datas[i] = new MQMessageUtils.EntryRowData();
                    datas[i].entry = entry;
                    datas[i].rowChange = rowChange;
                } catch (InvalidProtocolBufferException var5) {
                    throw new RuntimeException(var5);
                }
            }
            return datas;
        } else {
            MQMessageUtils.EntryRowData[] datas = new MQMessageUtils.EntryRowData[message.getEntries().size()];
            int i = 0;
            for (Iterator var5 = message.getEntries().iterator(); var5.hasNext(); ++i) {
                CanalEntry.Entry entry = (CanalEntry.Entry) var5.next();
                try {
                    CanalEntry.RowChange rowChange = CanalEntry.RowChange.parseFrom(entry.getStoreValue());
                    datas[i] = new MQMessageUtils.EntryRowData();
                    datas[i].entry = entry;
                    datas[i].rowChange = rowChange;
                } catch (InvalidProtocolBufferException var4) {
                    throw new RuntimeException(var4);
                }
            }
            return datas;
        }
    }

    private List<CapitalOrder> buildBillChangeDataList(List<FlatMessage> flatMessages) {
        List<CapitalOrder> list = new ArrayList<>();
        for (FlatMessage flatMessage : flatMessages) {
            String typeStr = flatMessage.getType();
            boolean isDdl = flatMessage.getIsDdl();
            if (isDdl) {
                return Collections.emptyList();
            }
            SQLTypeEnum sqlTypeEnum = SQLTypeEnum.convert(typeStr);
            if (null == sqlTypeEnum) {
                log.error("unable to resolve sqlType:{}", typeStr);
                return Collections.emptyList();
            }
            List<Map<String, String>> dataList = flatMessage.getData();
            switch (sqlTypeEnum) {
                case UPDATE:
                case INSERT:
                    list.addAll(messageDecode(dataList, flatMessage));
            }
        }
        return list;
    }

    private List<CapitalOrder> messageDecode(List<Map<String, String>> dataList, FlatMessage flatMessage) {
        List<CapitalOrder> coList = new ArrayList<>();
        for (Map<String, String> map : dataList) {
            CapitalOrder co = new CapitalOrder();
            co.setSourceId(map.get("id") == null ? null : Long.valueOf(map.get("id")));
            co.setOrderId(map.get("order_id") == null ? null : Long.parseLong(map.get("order_id")));
            co.setClientOid(map.get("client_oid"));
            co.setUserId(map.get("user_id") == null ? null : Long.parseLong(map.get("user_id")));
            co.setCoinId(map.get("coin_id") == null ? null : Integer.valueOf(map.get("coin_id")));
            co.setChainCoinId(map.get("chain_coin_id") == null ? null : Integer.valueOf(map.get("chain_coin_id")));
            co.setAmount(map.get("amount") == null ? null : new BigDecimal(map.get("amount")));
            co.setFees(map.get("fees") == null ? null : new BigDecimal(map.get("fees")));
            co.setBizType(map.get("biz_type") == null ? null : Byte.valueOf(map.get("biz_type")));
            co.setBizSubType(map.get("biz_sub_type") == null ? null : Byte.valueOf(map.get("biz_sub_type")));
            co.setAccountType(map.get("account_type") == null ? null : Byte.valueOf(map.get("account_type")));
            co.setFromAddress(map.get("from_address"));
            co.setAddress(map.get("address"));
            co.setAddressHash(map.get("address_hash"));
            co.setAddressLs(map.get("address_ls"));
            co.setAddressLsHash(map.get("address_ls_hash"));
            co.setFromAddressHash(map.get("from_address_hash"));
            co.setFromAddressCrypt(map.get("from_address_crypt"));
            co.setTxId(map.get("tx_id"));
            co.setOutputIndex(map.get("output_index") == null ? null : Integer.valueOf(map.get("output_index")));
            co.setBlockNumber(map.get("block_number") == null ? null : Integer.valueOf(map.get("block_number")));
            co.setConfirmNum(map.get("confirm_num") == null ? null : Integer.valueOf(map.get("confirm_num")));
            co.setL1ConfirmNum(map.get("l1_confirm_num") == null ? null : Integer.valueOf(map.get("l1_confirm_num")));
            co.setSource(map.get("source") == null ? null : Byte.valueOf(map.get("source")));
            co.setIp(map.get("ip"));
            co.setDeviceId(map.get("device_id"));
            co.setStatus(map.get("status") == null ? null : Byte.valueOf(map.get("status")));
            co.setWaitingStatus(map.get("waiting_status") == null ? null : Byte.valueOf(map.get("waiting_status")));
            co.setRiskStatus(map.get("risk_status") == null ? null : Byte.valueOf(map.get("risk_status")));
            co.setAddressTag(map.get("address_tag"));
            co.setReason(map.get("reason"));
            co.setCreator(map.get("creator"));
            co.setParams(map.get("params"));
            co.setRiskRemark(map.get("risk_remark"));
            co.setSourceCreateDate(map.get("create_date") == null ? null : DateUtil.getMillisecondDate(map.get("create_date")));
            co.setSourceUpdateDate(map.get("update_date") == null ? null : DateUtil.getMillisecondDate(map.get("update_date")));
            co.setInnerUserId(map.get("inner_user_id") == null ? null : Long.parseLong(map.get("inner_user_id")));
            co.setRequestInfo(map.get("request_info"));
            co.setIsPledge(map.get("is_pledge") == null ? null : Byte.valueOf(map.get("is_pledge")));
            co.setNoPwdFlag(map.get("no_pwd_flag") == null ? null : Byte.valueOf(map.get("no_pwd_flag")));
            co.setFlag(map.get("flag"));
            co.setFeeCoinId(map.get("fee_coin_id") == null ? null : Integer.valueOf(map.get("fee_coin_id")));
            co.setBurnFee(map.get("burn_fee") == null ? null : new BigDecimal(map.get("burn_fee")));
            co.setCreateDate(new Date());
            co.setUpdateDate(new Date());
            coList.add(co);
        }
        return coList;
    }


    /**
     * 初始化kafka
     */
    private void init() {
        // 创建监听器，数量和partition数量保持一致
        for (int i = 0; i < partitionNum; i++) {
            KafkaConsumer<String, Message> currentConsumer = new KafkaConsumer<String, Message>(consumerConfig);
            List<TopicPartition> partitions = new ArrayList<>();
            partitions.add(new TopicPartition(topic, i));
            currentConsumer.assign(partitions);
            partitionConsumerMap.put(i, currentConsumer);
        }
    }


}


