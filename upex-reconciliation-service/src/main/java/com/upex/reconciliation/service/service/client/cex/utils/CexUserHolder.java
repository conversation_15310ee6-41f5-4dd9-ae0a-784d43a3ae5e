package com.upex.reconciliation.service.service.client.cex.utils;

import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;

public class CexUserHolder {


    private static final ThreadLocal<String> currentUser = new ThreadLocal<>();

    private static final ThreadLocal<String> currentCexEmail = new ThreadLocal<>();

    public static void setUserInfo(String cexUserId, String cexEmail){
        currentUser.set(cexUserId);
        currentCexEmail.set(cexEmail);
    }

    /**
     * 设置当前用户ID
     */
    static void setCurrentUserId(String userId) {
        currentUser.set(userId);
    }


    public static String getCexEmail() {
        return currentCexEmail.get();
    }

    /**
     * 获取当前用户ID
     */
    public static String getCurrentUserId() {
        return currentUser.get();
    }


    /**
     * 清除用户信息（建议在请求结束时调用）
     */
    public static void clear() {
        currentUser.remove();
        currentCexEmail.remove();
    }



}
