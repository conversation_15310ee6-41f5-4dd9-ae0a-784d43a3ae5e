package com.upex.reconciliation.service.model.config;

import com.upex.reconciliation.service.model.dto.ProfitAlarmDto;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-11-18 17:13
 * @desc
 **/
@Data
public class GlobalBillConfig {

    /**
     * 是否写入报警日志
     */
    private boolean writeAlarmLog = true;

    /**
     * 资产检测列表
     */
    private List<AssetsCheckConfig> assetsCheckList;

    /**
     * 对账检测
     */
    private boolean alarmCheck = true;

    /**
     * 检测白名单
     */
    private List<String> checkPassUserIds;

    /**
     * 业务检测列表
     */
    private List<CheckVo> businessConfig;

    /**
     * 是否显示币种信息和汇率日志
     */
    private boolean showCoinInfoAndRatesLog = false;

    /**
     * 是否显示billList日志
     */
    private boolean showBillListLog = false;

    /**
     * 全局对账检测
     */
    private boolean globalAlarmCheck = false;

    /**
     * 真实资金对账检测
     */
    private boolean realAssetsAlarmCheck = false;

    /**
     * 资金对账检测
     */
    private boolean capitalAssetsCheck = false;

    /**
     * 用户提币开关检测
     */
    private boolean userWithdrawalAlarmCheck = false;
    /**
     * 用户负值资产开关检测
     */
    private boolean userNegativeAssetsAlarmCheck = false;
    /**
     * 用户实时负值资产开关检测
     */
    private boolean userRealTimeNegativeAssetsAlarmCheck = false;

    /**
     * 打开重新统计开关
     */
    private boolean openReStatistics = false;

    /**
     * 获取指定的统计时间
     */
    private Long statisticsTime;

    /**
     * 获取指定的统计时间
     */
    private Long statisticsInternal;

    // 小于等于这个值的log级别才打印日志
    private int logLevelCode = 6;

    /**
     * 邮箱地址
     */
    private String emailUrl;

    /**
     * 邮件接收者
     */
    private String receipt;

    /**
     * 对账差额预警值
     */
    private BigDecimal warningValue;

    /**
     * 对账差额禁止提币报警值
     */
    private BigDecimal alarmValue;

    /**
     * 对账差额重试报警值 30w
     */
    private BigDecimal reWarningValue;

    /**
     * 资金对账报警差额
     */
    private BigDecimal capitalWarningValue = BigDecimal.ZERO;

    /**
     * 资金对账阻断提币表达式
     */
    private String capitalBlockedWithdrawExpression = "";

    /**
     * 资金对账上一次成功时间间隔，默认10分钟
     */
    private Integer capitalLastSuccessTimeIntervalMin = 10;

    /**
     * 资金对账阻断提币次数
     */
    private Integer capitalBlockedWithdrawCount = 4;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 配置最小CheckOkTime的业务线
     * (ex:10_default,30_default,40_cmt_btcusdt)
     */
    private List<String> minCheckOkTimeSubSystemList = new ArrayList<>();
    /**
     * 检查负值资产的业务线
     * (ex:10_default,30_default,40_cmt_btcusdt)
     */
    private List<String> checkNegativeAssetsSubSystemList = new ArrayList<>();


    /**
     * 检测提币白名单
     */
    private List<Long> checkWithdrawWhiteListUserIds = new ArrayList<>();
    /**
     * 检测提币订单白名单
     */
    private List<String> checkWithdrawWhiteListOrders = new ArrayList<>();

    /**
     * 全链路数据上报开关
     */
    private boolean linkUploadSwitch = false;
    /**
     * 同步执行链路数据
     */
    private boolean syncExecuteLinkUploadOpen = false;

    /**
     * Redis锁超时时间
     */
    private Long globalAssetsCheckLockTimeOut = 300000L;

    /**
     * 明文加密密钥
     */
    private String encryptKey;

    /**
     * 解密测试内容
     */
    private String decryptCheck;

    /**
     * 提币检测总开关(默认打开，置为false就关闭，返回不允许提币)
     */
    private boolean selectCheckForTheResultsOpen = true;

    /**
     * 慢接口耗时日志打印开关（默认关闭false）
     */
    private boolean slowInterfaceLogOpen = false;

    /**
     * 提币接口开关（默认打开）
     */
    private boolean withdrawInterfaceLogOpen = true;
    /**
     * 换汇数据开关
     */
    private boolean currencyConverterOpen = false;
    /**
     * 换汇账户
     */
    private Long exchangeAccount = null;
    /**
     * 换汇数据的业务线
     * (ex:52_default,53_default,54_default)
     */
    private List<String> currencyConverterSubSystemList = new ArrayList<>();

    /**
     * 接口请求失败休眠时间，单位毫秒
     */
    private Integer sleepTime = 100;
    /**
     * 接口请求失败重试次数
     */
    private Integer reTryNumber = 3;
    /**
     * 检查实时负值资产的业务线
     * (ex:10_default,30_default,40_cmt_btcusdt)
     */
    private List<String> checkRealTimeNegativeAssetsSubSystemList = new ArrayList<>();

    /**
     * 提币检测报警——redis缓存过期时间，默认5分钟
     */
    private long withdrawalCheckExpire = 1000 * 60 * 5;

    // 接口 /inner/v1/bill/selectCheckForTheResults 是否启用新逻辑
    private boolean selectCheckForTheResultsNewVersion = true;

    // 接口 /inner/v1/bill/selectCheckForTheResults 是否启用反算逻辑
    private boolean selectCheckForTheResultsBackCalculation = true;


    // 合并存储时间片数量，需要跟业务线配置保持一致 单位秒
    private Long mergeTimeSliceSizeSecond = 5 * 60L;

    private Long checkStepGapThreshold = 4L;

    private boolean compareCoinPropertyNewOldSwitch = false;

    private boolean compareCoinPropertyOnlyOldSwitch = false;


    /**
     * 检测用户是否存在白名单
     */
    private List<Long> checkUserIsExistWhiteList = new ArrayList<>();

    /**
     * 提币检测接口，慢日志耗时的阈值，耗时大小超过该值的步骤，需要打印日志，单位：毫秒
     */
    private Long withDrawSlowLogThreshold = 100L;
    /**
     * 提币检测接口，慢日志耗时的阈值，耗时大小超过该值的步骤，打印lark，单位：毫秒
     */
    private Long withDrawSlowLogAlarmThreshold = 1000L;

    /**
     * demo用户禁止提币检测开关，开启后进行demo检测
     */
    private boolean demoUserBanWithdrawAlarmCheck = true;

    /**
     * 延迟入账
     */
    private boolean checkDelayAccountOpen = false;

    private Set<Long> delayAccountWhiteList = new HashSet<>();

    private Set<Byte> runningAccountTypes = new HashSet<>();

    /**
     * 盈利检查开关
     */
    private boolean checkProfitOpen = true;

    /**
     * 时间间隔盈利集合
     * 30,60,24*60
     */
    private List<Integer> timePeriodProfitList = new ArrayList<>();

    /**
     * 盈利检查报警开关
     */
    private boolean checkProfitAlarmOpen = false;

    /**
     * 时间周期盈利警告map
     * Map<24*60,ProfitAlarmDto>
     * Map<1*60,ProfitAlarmDto>
     * Map<30,ProfitAlarmDto>
     */
    private Map<Integer, ProfitAlarmDto> timePeriodProfitAlarmMap = new HashMap<>();

    /**
     * 检查用户盈利的业务线
     * (ex:10_default,30_default,40_cmt_btcusdt)
     */
    private List<String> checkProfitSubSystemList = new ArrayList<>();


    /**
     * 检查用户盈利增量流水的业务线
     * (ex:10_default,30_default,40_cmt_btcusdt)
     */
    private List<String> checkIncrProfitSubSystemList = new ArrayList<>();


    /**
     * 盈利白名单用户名单
     */
    private List<Long> profitWhiteUserList = new ArrayList<>();

    /**
     * 时间间隔盈利redis存活时间
     * 单位  小时
     */
    private Long timePeriodProfitRedisTime = 24L;

    /**
     * bill_coin_type_user表，生产首次有数据时间 2024-05-25
     */
    private Long billCoinTypeUserTableFirstUseTime = 1716566400000L;

    /**
     * 获取总账配置
     *
     * @param assetsCheckType
     * @return
     */
    public AssetsCheckConfig getAssetsCheckConfig(String assetsCheckType) {
        if (CollectionUtils.isEmpty(assetsCheckList)) {
            return null;
        }
        for (AssetsCheckConfig assetsCheckConfig : assetsCheckList) {
            if (assetsCheckType.equals(assetsCheckConfig.getAssetsCheckType())) {
                return assetsCheckConfig;
            }
        }
        return null;
    }

    /**
     * 同步盈利检查报警开关
     */
    private boolean sycCheckProfitAlarmOpen = false;

    /***提币盈亏检测超时时间 单位 秒***/
    private Integer checkProfitAccountTimeout = 3;
    /**
     * 提币检测接口总返回结果缓存失效时间（分钟）
     */
    private Long withdrawCheckTotalResultCacheTimeoutMinute = 5L;

    /**
     * 校验流水被update或delete时，阻断提币
     */
    private boolean verifyUserIllegalModificationBillData = false;
    /***总账kafka相关配置***/
    private AssetsKafkaConfig assetsKafkaConfig;


    /**
     * 提币检测-资金托管检查开关
     */
    private boolean checkHostingBalanceOpen = false;
    /**
     * 用户黑名单，禁止提币
     */
    private List<Long> checkUserBlackList = new ArrayList<>();
    private List<WithdrawHostingBalanceConfig> withdrawHostingBalanceConfigs = new ArrayList<>();
    private Long timeSliceSize = 60L;
    private Integer mergeTimeSliceSize = 5;


    /**
     * 相同提币阻断原因次数
     * 超过次数，lark报警
     */
    private Long withdrawBlockLarkAlarmThreshold = 10L;

    /**
     * 相同提币阻断时间间隔
     * 默认时间：5 minutes
     */
    private Integer withdrawBlockLarkAlarmTimeFixed = 5;
    /**
     * 平台阻断提币差额时间间隔
     * 默认时间：5 minutes
     */
    private Long platformBlockedWithdrawValueTimeFixed = 5L;

    /**
     * 平台阻断提币代码集合
     */
    private Set<Integer> platformCodeSet = new HashSet<>();

    /**
     * 平台阻断提币业务来源集合
     */
    private Set<String> platformBusinessSourceSet = new HashSet<>();

    /**
     * 用户阻断提币代码集合
     */
    private Set<Integer> userCodeSet = new HashSet<>();

    /**
     * 平台阻断提币差额
     */
    private BigDecimal platformBlockedWithdrawValue = BigDecimal.ZERO;

    /**
     * 查询用户来源开关
     */
    private Boolean queryUserSourceOpen = false;

    /**
     * 查询用户来源验证开关
     */
    private Boolean queryUserSourceVerificationOpen = false;
    /**
     * 总账消息打印日志币种集合
     */
    private Set<Integer> ledgerLogCoinSet = new HashSet<>();
    /***是否是模拟盘环境***/
    private boolean papTradingEnvironment = false;

    private boolean isSaveReconWithdrawResult = true;

    private Long withdrawCheckSaveResultCacheTimeoutMinute = 5L;
    /***盈利检测批量用户***/
    private Set<Long> checkProfitAccountForTimeUserIds = new HashSet<>();

    private boolean syncUserCenterLog = false;
    /***提币盈利检测是否检测子账号***/
    private boolean isCheckProfitAccountChildAccount = false;

    /**
     * 提币检测子账户类型
     ***/
    private List<Integer> childAccountTypeList = new ArrayList<>();

    /**
     * 提币检测最大子账户
     ***/
    private Integer maxChildAccountCount = 100;

    /**
     * 盈利检测子账户分页条数
     */
    private Integer childAccountPageSize = 1000;

    /**
     * 盈利检测最大子账户数
     */
    private Integer maxChildProfitAccountCount = 100000;

    private Integer insertSqlSize = 500;
    /**
     * 是否保存提币盈利记录
     */
    private boolean isSaveProfitRecord = true;

    /**
     * 盈利检测灰度服务名称
     */
    private String checkProfitGrayService = "hashCheckProfitGrayServiceImpl";

    /**
     * 盈利检测服务名称
     */
    private String checkUserProfitService = "redisCheckUserProfitServiceImpl";

    /**
     * 盈利检测灰度白名单
     */
    private List<Long> profitGrayWhileList = new ArrayList<>();

    /**
     * 盈利检测灰度百分比
     */
    private Integer profitGrayPercent = 0;

    private boolean isSaveProfitRecordFromData = true;

    /**
     * 盈利是否保存redis且告警
     */
    private boolean profitSaveRedisAndAlarm = false;
    /**
     * 盈利分片查询时间间隔，单位分钟
     */
    private Integer incrProfitCheckTimeMinuteInterval = 60;
    /**
     * 盈利分片查询用户黑名单，配置某用户ID不进行分片查询
     */
    private List<Integer> incrProfitCheckTimeSliceBlackAccountTypeList = new ArrayList<>();
    /**
     * 盈利分片查询业务线黑名单，配置某业务线不进行分片查询
     */
    private List<Long> incrProfitCheckTimeSliceBlackUserList = new ArrayList<>();
    /**
     * 盈利数据异常是否发送至发送至风控
     */
    private boolean isSendProfitAbnormalToRisk = false;
    /**
     * 期初用户资产过期时间
     */
    private Long userBeginAssetsRedisExpireHour = 25L;

    /**
     * 期初用户资产保留数量
     */
    private Integer userBeginAssetsMaxCount = 12;

    /**
     * redis期初用户资产
     */
    private boolean redisUserBeginAssets = false;
    /***盈利检测风控用户 失败检测时间***/
    private Long checkProfitRiskErrorTimeMs = 60000L;
    /***盈利检测风控用户 成功检测时间***/
    private Long checkProfitRiskSuccessTimeMs = 600000L;
    /***盈利检测风控用户 风控检测开关***/
    private boolean checkProfitUserRiskCheckOpen = false;
    /***提币检测用户盈亏明细告警运行实例 和 盈亏实时计算放在一个实例上***/
    private String userProfitAlarmStartJobServiceInstanceName = "";
    /***全局丢弃消息明单***/
    private Set<Long> globalAbandonMessageUserIds = new HashSet<>();

    /**
     * OSL承兑账户配置
     */
    private Map<Long, BigDecimal> oslFlashExConfig = new HashMap<>();

    /**
     * 总账是否开启
     *
     * @return
     */
    public boolean ledgerIsOpen() {
        if (assetsCheckList == null) {
            return false;
        }
        return assetsCheckList.stream().filter(item -> !item.isOpen()).collect(Collectors.toList()).size() == 0;
    }
}
