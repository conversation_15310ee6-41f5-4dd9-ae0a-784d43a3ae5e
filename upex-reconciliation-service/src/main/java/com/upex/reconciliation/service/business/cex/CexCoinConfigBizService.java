package com.upex.reconciliation.service.business.cex;

import com.upex.reconciliation.service.dao.cex.entity.CoinNameConfig;
import com.upex.reconciliation.service.service.CexCoinNameConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.upex.reconciliation.service.service.client.cex.CexConstants.CEX_CONFIG_NAME;

@Service
public class CexCoinConfigBizService {

    @Resource
    private CexCoinNameConfigService cexCoinNameConfigService;

    Map<String, String> cexCoinNameConfigMap = new ConcurrentHashMap<String, String>();


    void init() {
        List<CoinNameConfig> coinNameConfigs = cexCoinNameConfigService.selectAll();
        if (CollectionUtils.isNotEmpty(coinNameConfigs)) {
            for (CoinNameConfig coinNameConfig : coinNameConfigs) {
                String key = String.format(CEX_CONFIG_NAME, coinNameConfig.getCexType(), coinNameConfig.getCexCoinName());
                cexCoinNameConfigMap.put(key, coinNameConfig.getBgCoinName());
            }
        }
    }

    public String selectBgCoinNameByCexTypeAndCexCoinName(Integer cexType, String cexCoinName) {
        if(cexCoinNameConfigMap.isEmpty()){
            init();
        }
        String key = String.format(CEX_CONFIG_NAME, cexType, cexCoinName);
        return cexCoinNameConfigMap.get(key);
    }

    public int batchInsert(List<CoinNameConfig> list) {
        list.forEach(coinNameConfig -> {
            String key = String.format(CEX_CONFIG_NAME, coinNameConfig.getCexType(), coinNameConfig.getCexCoinName());
            cexCoinNameConfigMap.put(key, coinNameConfig.getBgCoinName());
        });
        return cexCoinNameConfigService.batchInsert(list);
    }
}
