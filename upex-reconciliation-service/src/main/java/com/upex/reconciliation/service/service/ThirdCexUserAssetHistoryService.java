package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserAssetHistory;
import com.upex.reconciliation.service.dao.mapper.cex.ThirdCexUserAssetHistoryMapper;
import com.upex.reconciliation.service.service.client.cex.enmus.ThirdAssetType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class ThirdCexUserAssetHistoryService {

    @Resource
    BillDbHelper billDbHelper;

    @Resource
    ThirdCexUserAssetHistoryMapper thirdCexUserAssetHistoryMapper;


    public int batchInsert(List<ThirdCexUserAssetHistory> records) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserAssetHistoryMapper.insertBatch(records));
    }

    public List<ThirdCexUserAssetHistory> selectTotalUserAsset(String cexUserId, Integer cexType) {
        return selectUserAssetByThirdAssetType(cexUserId, cexType, ThirdAssetType.parentUserAssetTypes());
    }

    public List<ThirdCexUserAssetHistory> selectSubUserTotalAsset(String cexUserId, Integer cexType) {
        return selectUserAssetByThirdAssetType(cexUserId, cexType, ThirdAssetType.subUserAssetTypes());
    }

    public List<ThirdCexUserAssetHistory> selectUserAssetByThirdAssetType(String cexUserId, Integer cexType, List<Integer> thirdAssetTypes) {
        Integer thridAssetType = null;
        if(thirdAssetTypes.contains(ThirdAssetType.PARENT_SPOT.getType())){
            thridAssetType= ThirdAssetType.PARENT_SPOT.getType();
        }
        if(thirdAssetTypes.contains(ThirdAssetType.SUB_SPOT.getType())){
            thridAssetType= ThirdAssetType.SUB_SPOT.getType();
        }
        Date checkSyncTime = selectMaxCheckSyncTime(cexUserId, cexType, thridAssetType);
        if(checkSyncTime==null){
            return Collections.EMPTY_LIST;
        }
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserAssetHistoryMapper.selectUserAssetByThirdAssetType(cexUserId, cexType, thirdAssetTypes, checkSyncTime));
    }

    public Date selectMaxCheckSyncTime(String cexUserId, Integer cexType, Integer thirdAssetType) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexUserAssetHistoryMapper.selectMaxCheckSyncTime(cexUserId, cexType, thirdAssetType));
    }


}
