package com.upex.reconciliation.service.business.cex;

import com.google.common.collect.Lists;
import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.CexAssetConfig;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexDepositeHistory;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.model.config.ApolloThirdCexAssetConfig;
import com.upex.reconciliation.service.service.ThirdCexDepositeHistoryService;
import com.upex.reconciliation.service.service.ThirdCexUserService;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CexUserListRequest;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.CommonReq;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.UniversalTransferRecordReq;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.UserDepositeHistoryListReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonDepositeHistoryInnerRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonDepositeHistoryRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.PageData;
import com.upex.reconciliation.service.service.client.cex.enmus.CexAssetHistoryTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum.NOUSER_MATCH_QUERY;
@Slf4j
@Service
public class CexUserDepositeHistoryBizService extends AbstractCexAssetSyncHistory implements ICexAssetSyncHistory {


    @Resource
    ThirdCexUserService thirdCexUserService;

    @Resource
    ThirdCexDepositeHistoryService thirdCexDepositeHistoryService;

    @Resource
    CexApiService cexApiService;

    @Resource
    BillDbHelper billDbHelper;

    public PageData<ThirdCexDepositeHistory> getUserDepositeHistory(UserDepositeHistoryListReq userDepositeHistoryListReq) {
        if (userDepositeHistoryListReq.getCexUserId() == null && userDepositeHistoryListReq.getCexType() == null) {
            throw new ApiException(ReconCexExceptionEnum.UNSUPPORTED_QUERY);
        }
        List<ThirdCexUser> thirdCexUsers = thirdCexUserService.selectByCondition(CexUserListRequest.builder()
                .cexType(userDepositeHistoryListReq.getCexType())
                .cexUserStatus(userDepositeHistoryListReq.getCexUserStatus())
                .cexUserId(userDepositeHistoryListReq.getCexUserId())
                .userManagerId(userDepositeHistoryListReq.getUserManagerId())
                .useType(userDepositeHistoryListReq.getUseType())
                .build());
        if (CollectionUtils.isEmpty(thirdCexUsers)) {
            return PageData.empty();
        }
        List<String> cexUserIds = thirdCexUsers.stream().map(ThirdCexUser::getCexUserId).collect(Collectors.toList());
        List<ThirdCexDepositeHistory> thirdCexDepositeHistories = thirdCexDepositeHistoryService.selectPageByUserIds(cexUserIds, userDepositeHistoryListReq);
        int count = thirdCexDepositeHistoryService.countPageByUserIds(cexUserIds, userDepositeHistoryListReq);
        return new PageData<>(thirdCexDepositeHistories, count);
    }

    public List<ThirdCexDepositeHistory> queryUserDepositeHistoryAndSync(UserDepositeHistoryListReq userDepositeHistoryListReq) {
        List<ThirdCexDepositeHistory> thirdCexDepositeHistories = new ArrayList<>();
        CommonRes<CommonDepositeHistoryRes> commonRes = cexApiService.queryUserDepositeHistory(userDepositeHistoryListReq);
        if (commonRes.getSuccess() && CollectionUtils.isNotEmpty(commonRes.getData())) {
            log.info("FinshQueryDepositeHistory,size:{},userId:{},cexType:{}", commonRes.getData().size(), userDepositeHistoryListReq.getCexUserId(), userDepositeHistoryListReq.getCexType());
            Long version = SerialNoGenerator.getMinIdByTime(new Date());
            for (CommonDepositeHistoryInnerRes depositeHistoryInnerRes : commonRes.getData()) {
                ThirdCexDepositeHistory history = new ThirdCexDepositeHistory();
                history.setDepositeId(depositeHistoryInnerRes.getDepositeId());
                history.setCexUserId(depositeHistoryInnerRes.getCexUserId());
                history.setCexEmail(depositeHistoryInnerRes.getCexEmail());
                history.setCexType(depositeHistoryInnerRes.getCexType());
                history.setCoinName(depositeHistoryInnerRes.getCoinName());
                history.setTxId(depositeHistoryInnerRes.getTxId());
                history.setAddress(depositeHistoryInnerRes.getAddress());
                history.setAddressTag(depositeHistoryInnerRes.getAddressTag());
                history.setNetwork(depositeHistoryInnerRes.getNetwork());
                history.setAmount(depositeHistoryInnerRes.getAmount());
                history.setStatus(depositeHistoryInnerRes.getStatus());
                history.setDepositeBeginTime(depositeHistoryInnerRes.getDepositeBeginTime());
                history.setDepositeEndTime(depositeHistoryInnerRes.getDepositeEndTime());
                history.setWalletType(depositeHistoryInnerRes.getWalletType());
                history.setTransferType(depositeHistoryInnerRes.getTransferType());
                history.setConfirmTimes(depositeHistoryInnerRes.getConfirmTimes());
                history.setUnlockConfirm(depositeHistoryInnerRes.getUnlockConfirm());
                history.setFee1(depositeHistoryInnerRes.getFee1());
                history.setFee1Coin(depositeHistoryInnerRes.getFee1Coin());
                history.setFee2(depositeHistoryInnerRes.getFee2());
                history.setFee2Coin(depositeHistoryInnerRes.getFee2Coin());
                history.setCheckSyncTime(userDepositeHistoryListReq.getCheckSyncTime());
                history.setCreateTime(new Date());
                history.setUpdateTime(new Date());
                history.setVersion(version);
                thirdCexDepositeHistories.add(history);
            }
        }
        return thirdCexDepositeHistories;
    }

    @Override
    public void syncAssetHistory(CexAssetConfig cexAssetConfig,ThirdCexUserConfig userConfig, Date startTime, Date endTime, Date checkSyncTime) {
        UserDepositeHistoryListReq userDepositeHistoryListReq = new UserDepositeHistoryListReq(userConfig.getCexType(), userConfig.getCexUserId(), userConfig.getApiKey(), userConfig.getApiKeyPrivate(), startTime, endTime,checkSyncTime);
        List<ThirdCexDepositeHistory> thirdCexDepositeHistories =queryUserDepositeHistoryAndSync(userDepositeHistoryListReq);
        ApolloThirdCexAssetConfig config = ReconciliationApolloConfigUtils.getThirdCexAssetConfig();
        billDbHelper.doDbOpInReconMasterTransaction(()-> {
            if(CollectionUtils.isNotEmpty(thirdCexDepositeHistories)) {
                List<List<ThirdCexDepositeHistory>> partisionDepositeHistories = Lists.partition(thirdCexDepositeHistories, config.getSqlInsertSize());
                for (List<ThirdCexDepositeHistory> partision : partisionDepositeHistories) {
                    thirdCexDepositeHistoryService.batchInsert(partision);
                }
                log.info("insertDepositeHistorySiz:{}", partisionDepositeHistories.size());
            }
            saveModAssetConfig(cexAssetConfig, CexAssetHistoryTypeEnum.DEPOSITE, userConfig, checkSyncTime);
            return null;
        });
    }


    @Override
    public CexAssetHistoryTypeEnum getAssetHistoryType() {
        return CexAssetHistoryTypeEnum.DEPOSITE;
    }
}
