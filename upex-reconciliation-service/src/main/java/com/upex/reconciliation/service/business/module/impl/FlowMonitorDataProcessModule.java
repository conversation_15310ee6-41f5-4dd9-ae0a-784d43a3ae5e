package com.upex.reconciliation.service.business.module.impl;

import com.alibaba.fastjson.JSON;
import com.upex.mixcontract.common.framework.AttributeMap;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.FinancialBillOrderService;
import com.upex.reconciliation.service.business.ReconInnerTransferService;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.module.AbstractBillModule;
import com.upex.reconciliation.service.business.ruleengine.core.RuleEngine;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.ReconciliationCommandEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.model.BillCmdResult;
import com.upex.reconciliation.service.model.BillCmdWrapper;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;


/**
 * 多纬度指标监控：
 * <p>
 * 流式处理场景，由内存对账的业务线流水消息，触发后续的对账
 * 做到一帐单一对
 * 需要的数据格式与流水的格式保持一致
 */
@Slf4j
public class FlowMonitorDataProcessModule extends AbstractBillModule {
    /***命令处理队列***/
    private BlockingQueue<BillCmdWrapper> cmdQueue = new ArrayBlockingQueue<>(10000);
    private AlarmNotifyService alarmNotifyService;
    private RuleEngine ruleEngine;
    private ApolloReconciliationBizConfig apolloBizConfig;
    private ReconInnerTransferService reconInnerTransferService;
    private FinancialBillOrderService financialBillOrderService;

    public FlowMonitorDataProcessModule(BillLogicGroup logicGroup) {
        super(logicGroup);
    }

    @Override
    public void init(AttributeMap initContext) {
        ReconciliationSpringContext reconciliationSpringContext = initContext.get(BillLogicGroup.ENGINE_SPRING_CONTEXT_KEY);
        this.alarmNotifyService = reconciliationSpringContext.getAlarmNotifyService();
        this.ruleEngine = reconciliationSpringContext.getRuleEngine();
        accountTypeEnum = initContext.get(BillLogicGroup.ACCOUNT_TYPE_ENUM_KEY);
        apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        this.reconInnerTransferService = reconciliationSpringContext.getReconInnerTransferService();
        this.financialBillOrderService = reconciliationSpringContext.getFinancialBillOrderService();
    }

    @Override
    public void offerCommand(BillCmdWrapper cmdWrapper) {
        if (ReconciliationCommandEnum.BILL_CHANGE.equals(cmdWrapper.getCommandEnum())) {
            try {
                this.cmdQueue.put(cmdWrapper);
            } catch (Exception e) {
                String errorMsg = "FlowMonitorDataProcessModule Command queue is full : " + cmdWrapper.getCommandEnum().getName()
                        + " queue size: " + this.cmdQueue.size() + " logicGroup: " + logicGroup.getName();
                log.error(errorMsg, e);
            }
        }
    }

    @Override
    public BillCmdResult takeCommand() {
        BillCmdWrapper cmdWrapper = null;
        try {
            cmdWrapper = this.cmdQueue.poll(1, TimeUnit.SECONDS);
            if (cmdWrapper == null) {
                return null;
            }
            return execCommand(cmdWrapper);
        } catch (Exception e) {
            log.error("FlowMonitorDataProcessModule check failed with error accountType {} data {}", accountTypeEnum.getCode(), cmdWrapper == null ? "" : JSON.toJSONString(cmdWrapper), e);
            log.error("FlowMonitorDataProcessModule check accountTypeEnum:{}", accountTypeEnum.getCode(), e);
        }
        return null;
    }

    /**
     * @param cmdWrapper
     * @return
     */
    private BillCmdResult execCommand(BillCmdWrapper cmdWrapper) {
        if (ReconciliationCommandEnum.BILL_CHANGE.equals(cmdWrapper.getCommandEnum())) {
            CommonBillChangeData commonBillChangeData = (CommonBillChangeData) cmdWrapper.getCommandData();
            ruleEngine.executeByBillFlow(commonBillChangeData);

            if (apolloBizConfig.isReconInnerTransferOpen()) {
                if (this.cmdQueue.size() > BillConstants.ALARM_QUEUE_SIZE) {
                    log.info("FlowMonitorDataProcessModule reconInnerTransferService.billHandle start, this.cmdQueue.size:{}", this.cmdQueue.size());
                }
                reconInnerTransferService.billHandle(commonBillChangeData);
            }

            if (apolloBizConfig.isCalculateIncrOrderOpen()) {
                if (this.cmdQueue.size() > BillConstants.ALARM_QUEUE_SIZE) {
                    log.info("FlowMonitorDataProcessModule reconInnerTransferService.billHandle start, this.cmdQueue.size:{}", this.cmdQueue.size());
                }
                // 将制定流水放入理财redis中，供后续资金对账使用
                financialBillOrderService.billHandle(commonBillChangeData);
            }
        }
        return null;
    }


}
