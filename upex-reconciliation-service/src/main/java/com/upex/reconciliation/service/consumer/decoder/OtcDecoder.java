package com.upex.reconciliation.service.consumer.decoder;

import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.upex.config.coin.SpotCoinDTO;
import com.upex.config.facade.coin.CoinConfigService;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.NumberUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.reconciliation.service.utils.SpringElUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class OtcDecoder extends AbstractMessageDecoder {
    @Resource
    private CoinConfigService coinConfigService;

    @Override
    public List<CommonBillChangeData> doMessageDecode(List<Map<String, String>> dataList, FlatMessage flatMessage, Integer partition, Long offset, Byte accountType) {
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
        List<CommonBillChangeData> commonBillChangeDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Map<String, String> map : dataList) {
                Boolean ignore = SpringElUtil.getBooleanValue(apolloBizConfig.getIgnoreDataDecoderEl(), map);
                if (ignore != null && ignore) {
                    log.info("OtcDecoder.messageDecode ignore data:{}", JSON.toJSONString(map));
                    continue;
                }
                SpotCoinDTO spotCoinDTO = coinConfigService.getCoinBaseInfoByCoinName(map.get("currency_code")).get();
                if (spotCoinDTO == null) {
                    log.info("OtcDecoder.messageDecode coinId is null data:{}", JSON.toJSONString(map));
                    continue;
                }
                CommonBillChangeData commonBillChangeData = new CommonBillChangeData();
                commonBillChangeData.setAccountType(accountType);
                commonBillChangeData.setPartition(partition);
                commonBillChangeData.setOffset(offset);
                commonBillChangeData.setBizId(Long.valueOf(map.get("id")));
                commonBillChangeData.setOrderId(map.get("order_id"));
                commonBillChangeData.setCoinId(spotCoinDTO.getCoinId());
                commonBillChangeData.setAccountId(Long.parseLong(map.get("user_id")));
                commonBillChangeData.setBizType(map.get("type"));
                commonBillChangeData.setBizTime(DateUtil.getMillisecondDate(map.get("created_date")));
                commonBillChangeData.setCreateTime(DateUtil.getMillisecondDate(map.get("created_date")));
                commonBillChangeData.setBizTimeFromId(DateUtil.getMillisecondDate(map.get("created_date")));
                String key = commonBillChangeData.getBizType() + "#" + map.get("notes");
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("notes", map.get("notes"));
                commonBillChangeData.setParams(JSON.toJSONString(paramMap));
                // 设置可用变动 和 冻结变动
                BigDecimal amount = new BigDecimal(map.get("amount"));
                BigDecimal beforeBalance = new BigDecimal(map.get("before_balance"));
                BigDecimal afterBalance = new BigDecimal(map.get("after_balance"));
                BigDecimal balanceChange = NumberUtil.subtract(afterBalance, beforeBalance);
                BigDecimal beforeFreeze = new BigDecimal(map.get("before_freeze"));
                BigDecimal afterFreeze = new BigDecimal(map.get("after_freeze"));
                BigDecimal freezeChange = NumberUtil.subtract(afterFreeze, beforeFreeze);
                commonBillChangeData.setChangeProp1(balanceChange);
                commonBillChangeData.setChangeProp2(freezeChange);
                commonBillChangeData.setProp1(afterBalance);
                commonBillChangeData.setProp2(afterFreeze);
                commonBillChangeDataList.add(commonBillChangeData);
            }
        }
        return commonBillChangeDataList;
    }
}
