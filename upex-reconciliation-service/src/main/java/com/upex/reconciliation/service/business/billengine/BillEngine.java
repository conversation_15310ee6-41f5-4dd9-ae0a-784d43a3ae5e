package com.upex.reconciliation.service.business.billengine;

import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.module.impl.*;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BillEngine extends AbstractBillEngine {
    private BillUserCheckModule billUserCheckModule;
    private PropertyInitModule propertyInitModule;
    private BillUserAssetCheckModule billUserAssetCheckModule;
    private BillTimeSliceCheckModule billTimeSliceCheckModule;
    private FlowMonitorDataProcessModule flowMonitorDataProcessModule;

    public BillEngine(ReconciliationSpringContext context, AccountTypeEnum accountTypeEnum) {
        super(context, accountTypeEnum);
        billUserCheckModule = mainLogicGroup.getModule(BillUserCheckModule.class);
        propertyInitModule = mainLogicGroup.getModule(PropertyInitModule.class);
        billTimeSliceCheckModule = mainLogicGroup.getModule(BillTimeSliceCheckModule.class);
        billUserAssetCheckModule = mainLogicGroup.getModule(BillUserAssetCheckModule.class);
        flowMonitorDataProcessModule = mainLogicGroup.getModule(FlowMonitorDataProcessModule.class);

    }

    @Override
    protected void innerStart() {
        String accountTypePrefix = accountTypeEnum.getCode() + "-";
        // 启动主消费任务
        startProtectedThread("UserCheck-" + accountTypePrefix, 0, () -> {
            try {
                billUserCheckModule.takeCommand();
            } catch (Throwable ex) {
                log.error("billUserCheckModule accountType:{} failed", accountTypeEnum.getCode(), ex);
                throw ex;
            }
            return true;
        });
        // 启动初始化线程
        startProtectedThread("Init-" + accountTypePrefix, 0, () -> {
            try {
                propertyInitModule.takeCommand();
            } catch (Throwable ex) {
                log.error("propertyInitModule accountType:{} failed", accountTypeEnum.getCode(), ex);
                throw ex;
            }
            return true;
        });
        // 启动初始化线程
        startProtectedThread("TimeSlice-" + accountTypePrefix, 0, () -> {
            try {
                billTimeSliceCheckModule.takeCommand();
            } catch (Throwable ex) {
                log.error("billTimeSliceCheckModule accountType:{} failed", accountTypeEnum.getCode(), ex);
                throw ex;
            }
            return true;
        });
        // 启动流水处理线程
        startProtectedThread("BillUserAsset-" + accountTypePrefix, 0, () -> {
            try {
                billUserAssetCheckModule.takeCommand();
            } catch (Throwable ex) {
                log.error("billUserAssetCheckModule accountType:{} failed", accountTypeEnum.getCode(), ex);
                throw ex;
            }
            return true;
        });
        // 启动流水处理线程
        startProtectedThread("FlowMonitorDataProcessModule-" + accountTypePrefix, 0, () -> {
            try {
                flowMonitorDataProcessModule.takeCommand();
            } catch (Throwable ex) {
                log.error("flowMonitorDataProcessModule accountType:{} failed", accountTypeEnum.getCode(), ex);
                throw ex;
            }
            return true;
        });
    }
}
