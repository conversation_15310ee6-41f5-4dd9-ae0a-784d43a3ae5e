package com.upex.reconciliation.service.service.client.cex.enmus;

import lombok.Getter;

@Getter
public enum CexAssetHistoryTypeEnum {

    DEPOSITE(1, "deposite"),
    WITHDRAW(2, "withdraw"),
    PARENT_SUB_TRANSFER(3, "parent_sub_transfer"),
    PAY_TRANSFER(4, "pay_transfer"),
    PARENT_UNIVERSIAL_TRANSFER(5, "parent_universial_transfer"),
    PARENT_BALANCE(6, "parent_balance"),
    SUB_BALANCE(7, "sub_balance");

    private int value;

    private String name;

    CexAssetHistoryTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }
}
