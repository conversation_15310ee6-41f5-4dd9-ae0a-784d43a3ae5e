package com.upex.reconciliation.service.service.impl;

import com.upex.reconciliation.service.business.createtablebyroute.BillContractProfitTransferTableCreator;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.enums.ProfitTransferTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillContractProfitTransfer;
import com.upex.reconciliation.service.dao.mapper.OldBillContractProfitTransferMapper;
import com.upex.reconciliation.service.service.OldBillContractProfitTransferService;
import com.upex.reconciliation.service.utils.SplitTableUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class OldBillContractProfitTransferServiceImpl implements OldBillContractProfitTransferService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "oldBillContractProfitTransferMapper")
    private OldBillContractProfitTransferMapper billContractProfitTransferMapper;
    @Resource
    private BillContractProfitTransferTableCreator billContractProfitTransferTableCreator;

    @Override
    public List<BillContractProfitTransfer> selectDelayedTransactionByTimeAndType(Date checkOkTime, List<ProfitTransferTypeEnum> profitTransferTypeEnumList, Date splitDate) {
        return dbHelper.doDbOpInBillMaster(() -> billContractProfitTransferMapper.selectDelayedTransactionByTimeAndType(checkOkTime,
                profitTransferTypeEnumList.stream().map(ProfitTransferTypeEnum::getCode).collect(Collectors.toList()),
                splitDate == null ? billContractProfitTransferTableCreator.getTableSuffixName(checkOkTime) : billContractProfitTransferTableCreator.getTableSuffixName(splitDate)));
    }

    @Override
    public List<BillContractProfitTransfer> selectDelayedTransactionByStatusAndTimeAndType(Date checkOkTime, List<ProfitTransferTypeEnum> profitTransferTypeEnumList, Date splitDate) {
        return dbHelper.doDbOpInBillMaster(() -> billContractProfitTransferMapper.selectDelayedTransactionByStatusAndTimeAndType(checkOkTime,
                profitTransferTypeEnumList.stream().map(ProfitTransferTypeEnum::getCode).collect(Collectors.toList()),
                splitDate == null ? billContractProfitTransferTableCreator.getTableSuffixName(checkOkTime) : billContractProfitTransferTableCreator.getTableSuffixName(splitDate)));
    }


    @Override
    public List<BillContractProfitTransfer> selectEachDelayedTransactionByTimeAndType(Date checkOkTime, List<ProfitTransferTypeEnum> profitTransferTypeEnumList, Date splitDate) {
        return dbHelper.doDbOpInBillMaster(() -> billContractProfitTransferMapper.selectEachDelayedTransactionByTimeAndType(checkOkTime,
                profitTransferTypeEnumList.stream().map(ProfitTransferTypeEnum::getCode).collect(Collectors.toList()),
                splitDate == null ? billContractProfitTransferTableCreator.getTableSuffixName(checkOkTime) : billContractProfitTransferTableCreator.getTableSuffixName(splitDate)));
    }

    @Override
    public List<BillContractProfitTransfer> selectEachDelayedTransactionByStatusAndTimeAndType(Date checkOkTime, List<ProfitTransferTypeEnum> profitTransferTypeEnumList, Date splitDate) {
        return dbHelper.doDbOpInBillMaster(() -> billContractProfitTransferMapper.selectEachDelayedTransactionByStatusAndTimeAndType(checkOkTime,
                profitTransferTypeEnumList.stream().map(ProfitTransferTypeEnum::getCode).collect(Collectors.toList()),
                splitDate == null ? billContractProfitTransferTableCreator.getTableSuffixName(checkOkTime) : billContractProfitTransferTableCreator.getTableSuffixName(splitDate)));
    }

    @Override
    public List<BillContractProfitTransfer> selectListByAccountTypeAndCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInBillMaster(() -> {
            String tableSuffix = billContractProfitTransferTableCreator.getTableSuffixName(checkTime);
            return billContractProfitTransferMapper.selectListByAccountTypeAndCheckTime(accountType, accountParam, checkTime, tableSuffix);
        });
    }

    @Override
    public Map<Integer, BigDecimal> sumInitUnProfitTransfers(Byte accountType, String accountParam, Date checkTime) {
        List<BillContractProfitTransfer> contractProfitTransferList = dbHelper.doDbOpInBillMaster(() -> {
            String tableSuffix = billContractProfitTransferTableCreator.getTableSuffixName(checkTime);
            return billContractProfitTransferMapper.sumInitUnProfitTransfers(accountType, accountParam, checkTime, tableSuffix);
        });
        Map<Integer, BigDecimal> resultMap = new HashMap<>();
        contractProfitTransferList.forEach(item -> {
            resultMap.put(item.getCoinId(), item.getTransferCount());
        });
        return resultMap;
    }
}
