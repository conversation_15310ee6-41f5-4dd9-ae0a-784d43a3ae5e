package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.upex.bill.dto.results.AccountAssetsInfoResult ;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.CheckAssetService;
import com.upex.reconciliation.service.dao.entity.BillCoinProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.model.dto.AbstractProperty;
import com.upex.reconciliation.service.model.dto.DataCalResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class CheckAssetLeverageServiceImpl implements CheckAssetService {
    /**
     * 获取业务线类型
     *
     * @return
     */
    @Override
    public List<String> getBusinessType() {
        return Arrays.asList(
                AccountTypeEnum.LEVER.getBizTypePrefix(),
                AccountTypeEnum.LEVER_ONE.getBizTypePrefix(),
                AccountTypeEnum.LEVER_FULL.getBizTypePrefix()
        );
    }

    /**
     * <li>合约</li>
     * 对账检查逻辑方法
     *
     * @param <T>
     * @param property                     合并流水后返回本期期末资产
     * @param totalAccountAssetsInfoResult 获取业务系统的资产
     * @return
     */
    @Override
    public <T extends AbstractProperty> boolean checkAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult) {
        if (property == null || totalAccountAssetsInfoResult == null) {
            return false;
        }
        if (property.getProp1().compareTo(totalAccountAssetsInfoResult.getProp1()) != 0) {
            return false;
        }
        if (property.getProp2().compareTo(totalAccountAssetsInfoResult.getProp2()) != 0) {
            return false;
        }
        if (property.getProp3().compareTo(totalAccountAssetsInfoResult.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkPosition(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult) {
        if (property == null || totalAccountAssetsInfoResult == null) {
            return false;
        }
        if (property.getProp1().compareTo(totalAccountAssetsInfoResult.getProp1()) != 0) {
            return false;
        }
        if (property.getProp2().compareTo(totalAccountAssetsInfoResult.getProp2()) != 0) {
            return false;
        }
        if (property.getProp3().compareTo(totalAccountAssetsInfoResult.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitAssets(T oldProperty, BillCoinUserProperty newBillCoinUserProperty) {
        if (oldProperty == null || newBillCoinUserProperty == null) {
            return false;
        }
        if (oldProperty.getProp1().compareTo(newBillCoinUserProperty.getProp1()) != 0) {
            return false;
        }
        if (oldProperty.getProp2().compareTo(newBillCoinUserProperty.getProp2()) != 0) {
            return false;
        }
        if (oldProperty.getProp3().compareTo(newBillCoinUserProperty.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinAssets(T oldProperty, BillCoinProperty billCoinProperty) {
        if (oldProperty == null || billCoinProperty == null) {
            return false;
        }
        if (oldProperty.getProp1().compareTo(billCoinProperty.getProp1()) != 0) {
            return false;
        }
        if (oldProperty.getProp2().compareTo(billCoinProperty.getProp2()) != 0) {
            return false;
        }
        if (oldProperty.getProp3().compareTo(billCoinProperty.getProp3()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeAssets(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        if (oldProperty == null || newBillCoinTypeProperty == null) {
            return false;
        }
        if (oldProperty.getProp1().compareTo(newBillCoinTypeProperty.getProp1()) != 0) {
            return false;
        }
        if (oldProperty.getProp2().compareTo(newBillCoinTypeProperty.getProp2()) != 0) {
            return false;
        }
        if (oldProperty.getProp3().compareTo(newBillCoinTypeProperty.getProp3()) != 0) {
            return false;
        }
        return true;
    }


    @Override
    public <T extends AbstractProperty> boolean checkInitCoinChangeAssets(T oldProperty, BillCoinProperty billCoinProperty) {
        if (oldProperty == null || billCoinProperty == null) {
            return false;
        }
        if (oldProperty.getChangeProp1().compareTo(billCoinProperty.getChangeProp1()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp2().compareTo(billCoinProperty.getChangeProp2()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp3().compareTo(billCoinProperty.getChangeProp3()) != 0) {
            return false;
        }
        return true;
    }


    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeChangeAssets(T oldProperty, BillCoinTypeProperty billCoinTypeProperty) {
        if (oldProperty == null || billCoinTypeProperty == null) {
            return false;
        }
        if (oldProperty.getChangeProp1().compareTo(billCoinTypeProperty.getChangeProp1()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp2().compareTo(billCoinTypeProperty.getChangeProp2()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp3().compareTo(billCoinTypeProperty.getChangeProp3()) != 0) {
            return false;
        }
        return true;
    }


    @Override
    public <T extends AbstractProperty> boolean checkCoinAssetsByAccountType(T oldProperty, BillCoinProperty newBillCoinUserProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        if (oldProperty != null) {
            dataCalResultDTO.setFactor1(oldProperty.getProp1().add(oldProperty.getProp2()));
        }
        if (newBillCoinUserProperty != null) {
            dataCalResultDTO.setFactor2(newBillCoinUserProperty.getProp1().add(newBillCoinUserProperty.getProp2()));
        }
        BigDecimal tolerance = coinComparisonToleranceMap.getOrDefault(newBillCoinUserProperty.getCoinId(), BigDecimal.ZERO);
        dataCalResultDTO.setResult(dataCalResultDTO.getFactor1().subtract(dataCalResultDTO.getFactor2()).abs());
        dataCalResultDTO.setPass(dataCalResultDTO.getResult().compareTo(tolerance) <= 0);
        return dataCalResultDTO.isPass();
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeInitAssetsByAccountType(List<T> oldPropertyList, BillCoinTypeProperty newBillCoinTypeProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        if (CollectionUtils.isEmpty(oldPropertyList) || newBillCoinTypeProperty == null) {
            return false;
        }
        BigDecimal tolerance = coinComparisonToleranceMap.getOrDefault(newBillCoinTypeProperty.getCoinId(), BigDecimal.ZERO);
        BigDecimal oldSum = BigDecimal.ZERO;
        for (T odlProperty : oldPropertyList) {
            oldSum = oldSum.add(odlProperty.getProp1()).add(odlProperty.getProp2());
        }
        BigDecimal newSum = newBillCoinTypeProperty.getProp1().add(newBillCoinTypeProperty.getProp2());
        dataCalResultDTO.setFactor1(oldSum);
        dataCalResultDTO.setFactor2(newSum);
        dataCalResultDTO.setResult(oldSum.subtract(newSum).abs());
        if (oldSum.subtract(newSum).abs().compareTo(tolerance) > 0) {
            log.info("CheckAssetSpotServiceImpl checkCoinTypeAssetsByAccountType failed , old sum {}, new sum {} , property {}", oldSum, newSum, JSONObject.toJSONString(newBillCoinTypeProperty));
            return false;
        }
        return true;
    }


    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeAssetsByAccountType(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        if (oldProperty == null) {
            return Objects.isNull(newBillCoinTypeProperty) || newBillCoinTypeProperty.getChangePropSum().compareTo(BigDecimal.ZERO) == 0;
        }
        if (oldProperty.getChangeProp1().compareTo(newBillCoinTypeProperty.getChangeProp1()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp2().compareTo(newBillCoinTypeProperty.getChangeProp2()) != 0) {
            return false;
        }
        return true;
    }




    @Override
    public <T extends AbstractProperty> BigDecimal sumForInAll(T property) {
        if (property == null) {
            return BigDecimal.ZERO;
        }
        return property.getProp1().add(property.getProp2());
    }


}