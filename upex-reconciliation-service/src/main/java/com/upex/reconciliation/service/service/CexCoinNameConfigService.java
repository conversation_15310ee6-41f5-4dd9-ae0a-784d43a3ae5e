package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.CoinNameConfig;
import com.upex.reconciliation.service.dao.mapper.cex.CoinNameConfigMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CexCoinNameConfigService {

    @Resource
    private CoinNameConfigMapper coinNameConfigMapper;

    @Resource
    BillDbHelper billDbHelper;


    public List<CoinNameConfig> selectAll() {
        return billDbHelper.doDbOpInReconMaster(() -> coinNameConfigMapper.selectAll());
    }

    public int batchInsert(List<CoinNameConfig> list) {
        return billDbHelper.doDbOpInReconMaster(() -> coinNameConfigMapper.batchInsert(list));
    }
}
