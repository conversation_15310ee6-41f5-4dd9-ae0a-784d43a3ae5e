package com.upex.reconciliation.service.service;

import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillUserPosition;
import com.upex.reconciliation.service.dao.entity.BillUserPositionTmp;
import com.upex.reconciliation.service.dao.mapper.BillUserPositionTmpMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.utils.BeanCopierUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class BillUserPositionTmpService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billUserPositionTmpMapper")
    private BillUserPositionTmpMapper billUserPositionTmpMapper;
    @Resource
    private BillUserPositionService billUserPositionService;

    public int batchInsert(List<BillUserPositionTmp> records) {
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionTmpMapper.batchInsert(records));
    }

    private Boolean deleteByUserId(Byte accountType, String accountParam, Long userId, Long pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionTmpMapper.deleteByUserId(accountType, accountParam, userId, pageSize));
    }
    private Boolean deleteByCheckTime(Byte accountType, String accountParam, Long checkTime, Long pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billUserPositionTmpMapper.deleteByCheckTime(accountType, accountParam, new Date(checkTime), pageSize));
    }
    public void repairUserPositionTmp(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        String action = jsonObject.getString("action");
        Byte accountType = jsonObject.getByte("accountType");
        Long userId = jsonObject.getLong("userId");
        Long checkTime = jsonObject.getLong("checkTime");
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        if ("delete".equals(action)) {
            if (accountType != null && userId != null && checkTime != null) {
                Long pageSize = 500L;
                while (true) {
                    Boolean result = this.deleteByUserId(accountType, accountTypeEnum.getAccountParam(), userId, pageSize);
                    if (!result) {
                        break;
                    }
                }
            }else  if (accountType != null  && checkTime != null) {
                Long pageSize = 500L;
                while (true) {
                    Boolean result = this.deleteByCheckTime(accountType, accountTypeEnum.getAccountParam(), checkTime, pageSize);
                    if (!result) {
                        break;
                    }
                }
            }
        } else if ("insert".equals(action)) {
            if (accountType != null && userId != null && checkTime != null) {
                Integer pageSize = 500;
                Long startId = 0L;
                while (true) {
                    List<BillUserPosition> billUserPositionList = billUserPositionService.selectByUserIdAndGtCheckTime(accountType, accountTypeEnum.getAccountParam(), new Date(checkTime), startId, pageSize, userId);
                    if (CollectionUtils.isEmpty(billUserPositionList)) {
                        break;
                    }
                    startId = billUserPositionList.get(billUserPositionList.size() - 1).getId();
                    List<BillUserPositionTmp> billUserPositionTmpList = new ArrayList<>();
                    billUserPositionList.forEach(item -> {
                        BillUserPositionTmp billUserPositionTmp = BeanCopierUtil.copyProperties(item, BillUserPositionTmp.class);
                        billUserPositionTmp.setAccountType(accountTypeEnum.getCode());
                        billUserPositionTmp.setAccountParam(accountTypeEnum.getAccountParam());
                        billUserPositionTmpList.add(billUserPositionTmp);
                    });
                    this.batchInsert(billUserPositionTmpList);
                }
            }else if(accountType != null && checkTime != null){
                Integer pageSize = 500;
                Long startId = 0L;
                while (true) {
                    List<BillUserPosition> billUserPositionList = billUserPositionService.selectRangeCheckTimeRecordPageQuery(Integer.valueOf(accountType), accountTypeEnum.getAccountParam(), new Date(checkTime), startId, pageSize);
                    if (CollectionUtils.isEmpty(billUserPositionList)) {
                        break;
                    }
                    startId = billUserPositionList.get(billUserPositionList.size() - 1).getId();
                    List<BillUserPositionTmp> billUserPositionTmpList = new ArrayList<>();
                    billUserPositionList.forEach(item -> {
                        BillUserPositionTmp billUserPositionTmp = BeanCopierUtil.copyProperties(item, BillUserPositionTmp.class);
                        billUserPositionTmp.setAccountType(accountTypeEnum.getCode());
                        billUserPositionTmp.setAccountParam(accountTypeEnum.getAccountParam());
                        billUserPositionTmpList.add(billUserPositionTmp);
                    });
                    this.batchInsert(billUserPositionTmpList);
                }
            }
        }
    }


}
