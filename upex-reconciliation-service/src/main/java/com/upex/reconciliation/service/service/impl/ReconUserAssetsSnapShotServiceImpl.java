package com.upex.reconciliation.service.service.impl;

import com.google.common.base.Stopwatch;
import com.google.common.cache.Cache;
import com.google.common.collect.Sets;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.commons.support.exception.ApiException;
import com.upex.commons.support.model.ResponseResult;
import com.upex.config.coin.SpotCoinDTO;
import com.upex.config.facade.coin.CoinConfigService;
import com.upex.contract.process.ContractConfigUtils;
import com.upex.contract.process.dto.config.ContractConfigVo;
import com.upex.exchange.framework.util.ExchangeUtils;
import com.upex.mixcontract.process.facade.dto.MixAccountAssetsExtension;
import com.upex.mixcontract.process.facade.feign.inner.InnerQueryPriceFeignClient;
import com.upex.reconciliation.facade.dto.results.*;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.enums.ApolloKeyEnum;
import com.upex.reconciliation.facade.enums.AssetsCheckTypeEnum;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;
import com.upex.reconciliation.facade.params.ReconUserAssetsBySnapShotTimeParams;
import com.upex.reconciliation.facade.params.ReconUserAssetsSnapShotParams;
import com.upex.reconciliation.facade.params.ReconUserTypeChangeParams;
import com.upex.reconciliation.facade.params.SysAssetsParams;
import com.upex.reconciliation.service.business.*;
import com.upex.reconciliation.service.business.prometheus.InterfaceMonitorMetricNameEnum;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.MerkelTreeConstant;
import com.upex.reconciliation.service.common.constants.UserAssetsDetailConstant;
import com.upex.reconciliation.service.common.constants.enums.BalanceTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.ContractEnum;
import com.upex.reconciliation.service.common.constants.enums.OutInTypeEnum;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillConfig;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.BillBaseAssetSnapshotApolloConfig;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.config.SysUserIdsBillConfig;
import com.upex.reconciliation.service.service.BillCoinTypeUserPropertyService;
import com.upex.reconciliation.service.service.BillCoinUserPropertyService;
import com.upex.reconciliation.service.service.BillConfigService;
import com.upex.reconciliation.service.utils.*;
import com.upex.spot.facade.SpotCoinService;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.utils.task.TaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.upex.reconciliation.facade.enums.AccountTypeEnum.*;


@Service
@Slf4j
public class ReconUserAssetsSnapShotServiceImpl implements ReconUserAssetsSnapShotService {


    @Resource(name = "coinTaskManager")
    private TaskManager coinTaskManager;

    @Resource(name = "assetSnapshotTaskManager")
    private TaskManager assetSnapshotTaskManager;

    @Resource
    private InnerQueryPriceFeignClient innerQueryPriceFeignClient;

    @Resource(name = "billCoinTypeUserPropertyService")
    private BillCoinTypeUserPropertyService billCoinTypeUserPropertyService;

    /**
     * 合约账户扩展参数缓存
     */
    private Cache<String, MixAccountAssetsExtension> extensionCache = CacheUtils.getNewCache(4, 2, TimeUnit.MINUTES);

    @Resource
    private SpotCoinService spotCoinService;

    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;
    @Resource(name = "coinInfoTaskManager")
    private TaskManager coinInfoTaskManager;


    @Resource
    private BillConfigService billConfigService;


    @Resource
    private AssetsQueryService assetsQueryService;

    @Resource
    private CoinConfigService coinConfigService;

    @Resource
    private CommonService commonService;

    @Resource
    private UserAssetsSnapShotBiz userAssetsSnapShotBiz;

    @Resource
    private MixBillAssetsService mixBillAssetsService;


    @Autowired
    private BillCoinUserPropertySnapshotService billCoinUserPropertySnapshotService;


    @Resource(name = "coinAssetsTaskManager")
    private TaskManager coinAssetsTaskManager;

    @Resource
    private ReconBaseService reconBaseService;


    public List<String> getSubSystem() {
        SysUserIdsBillConfig sysUserIdsBillConfig = ReconciliationApolloConfigUtils.getSysUserIdsBillConfig();
        List<String> subSystem = sysUserIdsBillConfig.getSubSystemList();
        log.info("subSystem: {}", subSystem);
        return subSystem;
    }


    // 按类型取时间点差值
    // 改动：查询出来的coin_type_user数据只有change
    @Override
    public List<BillCoinTypeUserProperty> getTimeDifferenceV2(Long userId, Long startTime, Long endTime, Integer accountType, String accountParam) {
        log.info("getTimeDifference param userid={},startTime={},endTime={},accountType={},accountparam={}", userId, startTime, endTime, accountType, accountParam);
        // 改动：查询出来的coin_type_user_property 数据只有change
        Long checkedEndTime = checkAndGetEndTime(accountType, accountParam, endTime);
        List<BillCoinTypeUserProperty> betweenList = billCoinTypeUserPropertyService.selectBetweenTime(userId, new Date(startTime), new Date(checkedEndTime), accountType);
        List<BillCoinTypeUserProperty> list = aggregationTimeDifferenceV2(betweenList);
        return list;
    }


    @Override
    public List<BillCoinTypeUserProperty> getTimeDifferenceOnChange(Long userId, Long startTime, Long endTime, Integer accountType, String accountParam) {
        Long checkedEndTime = checkAndGetEndTime(accountType, accountParam, endTime);
        List<BillCoinTypeUserProperty> betweenList = billCoinTypeUserPropertyService.selectBetweenTime(userId, new Date(startTime), new Date(checkedEndTime), accountType);
        List<BillCoinTypeUserProperty> list = aggregationTimeDifferenceOnChange(betweenList);
        return list;
    }


    private List<BillCoinTypeUserProperty> aggregationTimeDifferenceOnChange(List<BillCoinTypeUserProperty> billCoinTypeUserProperties) {
        // 具体计算聚合逻辑
        List<BillCoinTypeUserProperty> list = new ArrayList<>();
        Map<String, List<BillCoinTypeUserProperty>> groupMap = billCoinTypeUserProperties.stream().collect(Collectors.groupingBy(k -> k.getUserId().toString() + BillConstants.UNDERSCORE_SEPARATOR + k.getCoinId().toString() + BillConstants.UNDERSCORE_SEPARATOR + k.getBizType()));
        for (Map.Entry<String, List<BillCoinTypeUserProperty>> entry : groupMap.entrySet()) {
            List<BillCoinTypeUserProperty> userCoinBizTypeList = entry.getValue();
            // 将时间段内的所有 change做加总，然后赋值到 prop上
            if (CollectionUtils.isNotEmpty(userCoinBizTypeList)) {
                BillCoinTypeUserProperty billCoinTypeUserProperty = new BillCoinTypeUserProperty();
                billCoinTypeUserProperty.setCoinId(userCoinBizTypeList.get(0).getCoinId());
                billCoinTypeUserProperty.setUserId(userCoinBizTypeList.get(0).getUserId());
                billCoinTypeUserProperty.setBizType(userCoinBizTypeList.get(0).getBizType());
                userCoinBizTypeList.forEach(item -> {
                    billCoinTypeUserProperty.setChangeProp1(billCoinTypeUserProperty.getChangeProp1().add(item.getChangeProp1()));
                    billCoinTypeUserProperty.setChangeProp2(billCoinTypeUserProperty.getChangeProp2().add(item.getChangeProp2()));
                    billCoinTypeUserProperty.setChangeProp3(billCoinTypeUserProperty.getChangeProp3().add(item.getChangeProp3()));
                    billCoinTypeUserProperty.setChangeProp4(billCoinTypeUserProperty.getChangeProp4().add(item.getChangeProp4()));
                    billCoinTypeUserProperty.setChangeProp5(billCoinTypeUserProperty.getChangeProp5().add(item.getChangeProp5()));
                });
                list.add(billCoinTypeUserProperty);
            }

        }
        return list;
    }

    private List<BillCoinTypeUserProperty> aggregationTimeDifferenceV3(List<BillCoinTypeUserProperty> billCoinTypeUserProperties) {
        // 具体计算聚合逻辑
        Map<String, BillCoinTypeUserProperty> billCoinTypeUserPropertyMap = billCoinTypeUserProperties.stream().collect(Collectors.toMap(k -> k.getUserId().toString() + BillConstants.UNDERSCORE_SEPARATOR + k.getCoinId().toString() + BillConstants.UNDERSCORE_SEPARATOR + k.getBizType(), Function.identity(), (key1, key2) -> {
            BillCoinTypeUserProperty billCoinTypeProperty = new BillCoinTypeUserProperty();
            billCoinTypeProperty.setCoinId(key1.getCoinId());
            billCoinTypeProperty.setUserId(key1.getUserId());
            billCoinTypeProperty.setBizType(key1.getBizType());
            billCoinTypeProperty.setProp1(key1.getProp1().add(key2.getChangeProp1()));
            billCoinTypeProperty.setProp2(key1.getProp2().add(key2.getChangeProp2()));
            billCoinTypeProperty.setProp3(key1.getProp3().add(key2.getChangeProp3()));
            billCoinTypeProperty.setProp4(key1.getProp4().add(key2.getChangeProp4()));
            billCoinTypeProperty.setProp5(key1.getProp5().add(key2.getChangeProp5()));
            return billCoinTypeProperty;
        }));
        return (List<BillCoinTypeUserProperty>) billCoinTypeUserPropertyMap.values();

    }


    private List<BillCoinTypeUserProperty> aggregationTimeDifferenceV2(List<BillCoinTypeUserProperty> billCoinTypeUserProperties) {
        // 具体计算聚合逻辑
        List<BillCoinTypeUserProperty> list = new ArrayList<>();
        Map<String, List<BillCoinTypeUserProperty>> groupMap = billCoinTypeUserProperties.stream().collect(Collectors.groupingBy(k -> k.getUserId().toString() + BillConstants.UNDERSCORE_SEPARATOR + k.getCoinId().toString() + BillConstants.UNDERSCORE_SEPARATOR + k.getBizType()));
        for (Map.Entry<String, List<BillCoinTypeUserProperty>> entry : groupMap.entrySet()) {
            List<BillCoinTypeUserProperty> userCoinBizTypeList = entry.getValue();
            // 将时间段内的所有 change做加总，然后赋值到 prop上
            if (CollectionUtils.isNotEmpty(userCoinBizTypeList)) {
                BillCoinTypeUserProperty billCoinTypeUserProperty = new BillCoinTypeUserProperty();
                billCoinTypeUserProperty.setCoinId(userCoinBizTypeList.get(0).getCoinId());
                billCoinTypeUserProperty.setUserId(userCoinBizTypeList.get(0).getUserId());
                billCoinTypeUserProperty.setBizType(userCoinBizTypeList.get(0).getBizType());
                userCoinBizTypeList.forEach(item -> {
                    billCoinTypeUserProperty.setProp1(billCoinTypeUserProperty.getProp1().add(item.getChangeProp1()));
                    billCoinTypeUserProperty.setProp2(billCoinTypeUserProperty.getProp2().add(item.getChangeProp2()));
                    billCoinTypeUserProperty.setProp3(billCoinTypeUserProperty.getProp3().add(item.getChangeProp3()));
                    billCoinTypeUserProperty.setProp4(billCoinTypeUserProperty.getProp4().add(item.getChangeProp4()));
                    billCoinTypeUserProperty.setProp5(billCoinTypeUserProperty.getProp5().add(item.getChangeProp5()));
                });
                list.add(billCoinTypeUserProperty);
            }

        }
        return list;
    }


    @Override
    public ReconBillMessagePage<ReconUserOutInBalanceDetailsVo> selectDetails(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        Integer pageNo = reconUserTypeChangeParams.getPageNo();
        Integer pageSize = reconUserTypeChangeParams.getPageSize();
        log.info("pageNo = {},pageSize = {}", pageNo, pageSize);
        if (pageNo <= 0 || pageSize <= 0) {
            throw new ApiException(BillExceptionEnum.ILLEGAL_PARAMS);
        }
        if (pageSize >= BillConstants.MAX_PAGESIZE) {
            pageSize = BillConstants.MAX_PAGESIZE;
        }
        ReconBillMessagePage<ReconUserOutInBalanceDetailsVo> reconBillMessagePage = new ReconBillMessagePage<ReconUserOutInBalanceDetailsVo>();
        List<String> allType = getAllOutInType(UserAssetsDetailConstant.ALL_TYPE);
        List<ReconUserOutInBalanceDetailsVo> reconUserOutInBalanceDetailsVos = getDetailsList(reconUserTypeChangeParams, allType, UserAssetsDetailConstant.DETAILS);
        // 过滤掉金额为0的
        List<ReconUserOutInBalanceDetailsVo> filterList = reconUserOutInBalanceDetailsVos.stream().filter(a -> a.getBalance().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        reconBillMessagePage.setCurrentPage(pageNo);
        reconBillMessagePage.setPageSize(pageSize);

        reconBillMessagePage.setTotal(filterList.size());
        reconBillMessagePage.setItems(filterList);
        return reconBillMessagePage;
    }


    private List<ReconUserOutInBalanceDetailsVo> getDetailsList(ReconUserTypeChangeParams reconUserTypeChangeParams, List<String> allType, Integer status) {
        List<String> subSystem = getSubSystem();
        List<String> spotList = getAllOutInType((int) SPOT.getCode());
        List<String> otcList = getAllOutInType((int) OTC.getCode());
        Queue<ReconUserOutInBalanceDetailsVo> reconUserOutInBalanceDetailsVos = new ConcurrentLinkedQueue<>();
        Stopwatch stopWatch = Stopwatch.createStarted();

        coinInfoTaskManager.forEachSubmitBatchAndWait(subSystem, string -> {
            String[] strings = string.split("-");
            Integer accountType = Integer.valueOf(strings[0].toUpperCase());
            String accountParam = strings[1].toUpperCase();
            ReconUserTypeChangeParams paramsCopy = BeanCopierUtil.copyProperties(reconUserTypeChangeParams, ReconUserTypeChangeParams.class);
            paramsCopy.setAccountType(accountType);
            paramsCopy.setAccountParam(accountParam);
            AccountTypeEnum anEnum = toEnum(accountType.byteValue());

            Stopwatch startWatch = Stopwatch.createStarted();
            if (UserAssetsDetailConstant.DETAILS == status) {
                // 分类别查询
                if (anEnum == SPOT) {
                    List<ReconUserOutInBalanceDetailsVo> totalBalanceVoList = selectSingleDetails(paramsCopy, spotList);
                    reconUserOutInBalanceDetailsVos.addAll(totalBalanceVoList);
                } else if (anEnum == OTC) {
                    List<ReconUserOutInBalanceDetailsVo> totalBalanceVoList = selectSingleDetails(paramsCopy, otcList);
                    reconUserOutInBalanceDetailsVos.addAll(totalBalanceVoList);
                }
            } else if (UserAssetsDetailConstant.OTHER_INCOME == status) {
                List<ReconUserOutInBalanceDetailsVo> totalBalanceVoList = selectSingleDetails(paramsCopy, allType);
                reconUserOutInBalanceDetailsVos.addAll(totalBalanceVoList);
            }
            log.info("UserAssetsSnapShotServiceImpl.getDetailsList selectSingleDetails execute end, accountType:{}, accountParam:{}，time:{}", accountType, accountParam, startWatch.stop());
        });
        log.info("UserAssetsSnapShotServiceImpl.getDetailsList selectSingleDetails execute end ，time:{}", stopWatch.stop());
        return new ArrayList<>(reconUserOutInBalanceDetailsVos);
    }


    public List<ReconUserOutInBalanceDetailsVo> selectSingleDetails(ReconUserTypeChangeParams reconUserTypeChangeParams, List<String> type) {
        Long userId = reconUserTypeChangeParams.getUserId();
        Integer accountType = reconUserTypeChangeParams.getAccountType();
        String accountParam = reconUserTypeChangeParams.getAccountParam();
        Date beginTime = getBeginTime(userId, reconUserTypeChangeParams.getBeginTime(), reconUserTypeChangeParams.getEndTime(), accountType, accountParam);
        Date endTime = getEndTime(userId, reconUserTypeChangeParams.getBeginTime(), reconUserTypeChangeParams.getEndTime(), accountType, accountParam);

//        BillCoinUserProperty begin = billCoinUserPropertySnapshotService.selectTime(userId, accountType, accountParam, beginTime);
//        BillCoinUserProperty end = billCoinUserPropertySnapshotService.selectTime(userId, accountType, accountParam, endTime);
//
//        Date beginDate = Objects.isNull(begin) ? beginTime : begin.getCheckTime();
//        Date endDate = Objects.isNull(end) ? endTime : end.getCheckTime();

        List<BillCoinTypeUserProperty> list = new ArrayList<>();
        List<BillCoinTypeUserProperty> endList = billCoinTypeUserPropertyService.selectUserTypeByTimeAndTypeAndCoinId(userId, type, reconUserTypeChangeParams.getCoinId(), beginTime, endTime, accountType);
        // 筛选过滤
        List<ReconUserOutInBalanceDetailsVo> reconUserOutInBalanceDetailsVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(endList)) {
            return reconUserOutInBalanceDetailsVos;
        }
        BigDecimal zero = BigDecimal.ZERO;
        List<BillCoinTypeUserProperty> filterList = endList.stream().filter(a -> a.getChangeProp1().compareTo(zero) != 0 || a.getChangeProp2().compareTo(zero) != 0 || a.getChangeProp3().compareTo(zero) != 0 || a.getChangeProp4().compareTo(zero) != 0 || a.getChangeProp5().compareTo(zero) != 0).collect(Collectors.toList());
        list.addAll(filterList);
        reconUserOutInBalanceDetailsVos = getSingleDetail(list);
        return reconUserOutInBalanceDetailsVos;
    }

    public List<ReconUserOutInBalanceDetailsVo> getSingleDetail(List<BillCoinTypeUserProperty> list) {
        List<ReconUserOutInBalanceDetailsVo> reconUserOutInBalanceDetailsVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return reconUserOutInBalanceDetailsVos;
        }
        List<BillCoinTypeUserProperty> aggregationList = aggregationTimeDifferenceV2(list);
        for (BillCoinTypeUserProperty billCoinTypeUserProperty : aggregationList) {
            ReconUserOutInBalanceDetailsVo reconUserOutInBalanceDetailsVo = new ReconUserOutInBalanceDetailsVo();
            reconUserOutInBalanceDetailsVo.setBizType(billCoinTypeUserProperty.getBizType());
            reconUserOutInBalanceDetailsVo.setShowBalance(billCoinTypeUserProperty.getProp1().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconUserOutInBalanceDetailsVo.setTime(billCoinTypeUserProperty.getCheckTime());
            reconUserOutInBalanceDetailsVo.setCoinId(billCoinTypeUserProperty.getCoinId());
            reconUserOutInBalanceDetailsVo.setCoinName(selectCoinName(billCoinTypeUserProperty.getCoinId()));
            reconUserOutInBalanceDetailsVo.setBalance(billCoinTypeUserProperty.getPropSum());
            String typeName = getTypeName(billCoinTypeUserProperty.getBizType());
            if (typeName == null) {
                // 非法的bizType
                log.error("ReconUserAssetsSnapShotServiceImpl getSingleDetail invalid bizType {}", billCoinTypeUserProperty.getBizType());
                continue;
            }
            reconUserOutInBalanceDetailsVo.setTypeName(typeName);
            reconUserOutInBalanceDetailsVos.add(reconUserOutInBalanceDetailsVo);
        }
        return reconUserOutInBalanceDetailsVos;
    }

    // 根据不同账户类型获取不同账户名称
    public String getTypeName(String code) {
        OutInTypeEnum outInTypeEnum = OutInTypeEnum.toEnumByType(code);
        if (outInTypeEnum == null) {
            return null;
        }
        return outInTypeEnum.getDesc();
    }


    // 获取所有入出类型字符串
    public List<String> getAllOutInType(Integer code) {
        List<String> allType = new ArrayList<>();
        SysUserIdsBillConfig sysUserIdsBillConfig = ReconciliationApolloConfigUtils.getSysUserIdsBillConfig();
        List<String> totalIn = sysUserIdsBillConfig.getTotalIn();
        List<String> totalOut = sysUserIdsBillConfig.getTotalOut();
        List<String> internalIn = sysUserIdsBillConfig.getInternalIn();
        List<String> internalOut = sysUserIdsBillConfig.getInternalOut();
        List<String> otcIn = sysUserIdsBillConfig.getOtcIn();
        List<String> otcOut = sysUserIdsBillConfig.getOtcOut();
        List<String> other = sysUserIdsBillConfig.getOther();
        AccountTypeEnum anEnum = toEnum(code.byteValue());
        if (anEnum == SPOT) {
            allType.addAll(totalIn);
            allType.addAll(totalOut);
            allType.addAll(internalIn);
            allType.addAll(internalOut);
            allType.addAll(other);
            return allType;
        } else if (anEnum == OTC) {
            allType.addAll(otcIn);
            allType.addAll(otcOut);
            return allType;
        } else {
            allType.addAll(totalIn);
            allType.addAll(totalOut);
            allType.addAll(internalIn);
            allType.addAll(internalOut);
            allType.addAll(otcIn);
            allType.addAll(otcOut);
            allType.addAll(other);
            return allType;
        }
    }


    // 获取交易对
    public String getProductName(String accountParam) {
        ContractConfigVo contractConfigVo = ContractConfigUtils.getProductConfig(accountParam.toLowerCase());
        if (Objects.nonNull(contractConfigVo)) {
            String productName = contractConfigVo.getProductName();
            return productName;
        }
        return null;
    }


    public Date getBeginTime(Long userId, Long beginTime, Long endTime, Integer accountType, String accountParam) {
        if (Objects.isNull(beginTime) || Objects.isNull(endTime)) {
            BillCoinUserProperty billUserBegin = billCoinUserPropertyService.selectBySingleUserIdLatest(userId, accountParam, accountType);
            if (!Objects.isNull(billUserBegin)) {
                Date time = DateUtil.addDay(billUserBegin.getCheckTime(), -30);
                // Date time = DateUtil.addHour(billUserBegin.getCheckOkTime(), -1);
                return time;
            }
            return new Date();
        } else {
            Date time = DateUtil.getDefaultDate(DateUtil.longToDate(beginTime));
            return time;
        }
    }

    public Date getEndTime(Long userId, Long beginTime, Long endTime, Integer accountType, String accountParam) {
        if (Objects.isNull(beginTime) || Objects.isNull(endTime)) {
            BillCoinUserProperty billUserBegin = billCoinUserPropertyService.selectBySingleUserIdLatest(userId, accountParam, accountType);
            if (!Objects.isNull(billUserBegin)) {
                Date time = billUserBegin.getCheckTime();
                return time;
            }
            return new Date();
        } else {
            Date time = DateUtil.getDefaultDate(DateUtil.longToDate(endTime));
            return time;
        }
    }

    public Long checkAndGetEndTime(Integer accountType, String accountParam, Long endTime) {
        BillConfig billConfig = billConfigService.selectByTypeAndParam(accountType.byteValue(), accountParam);
        if (billConfig == null) {
            log.error("ReconUserAssetsSnapShotServiceImpl checkAndGetEndTime error accountType {} , accountParam {}", accountType, accountParam);
            throw new ApiException(BillExceptionEnum.SYSTEM_ERROR);
        }
        return billConfig.getCheckOkTime().getTime() <= endTime ? billConfig.getCheckOkTime().getTime() : endTime;
    }

    // 获取交易对
    public List<ReconUserOutInBalanceVo> selectOutInBalanceByTime(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        // 校验 endTime
        List<ReconUserOutInBalanceVo> reconUserOutInBalanceVos = new ArrayList<>();
        List<BillCoinTypeUserProperty> list = getTimeDifferenceV2(reconUserTypeChangeParams.getUserId(), reconUserTypeChangeParams.getBeginTime(), reconUserTypeChangeParams.getEndTime(), reconUserTypeChangeParams.getAccountType(), reconUserTypeChangeParams.getAccountParam());

        SysUserIdsBillConfig sysUserIdsBillConfig = ReconciliationApolloConfigUtils.getSysUserIdsBillConfig();
        List<String> totalIn = sysUserIdsBillConfig.getTotalIn();
        List<String> totalOut = sysUserIdsBillConfig.getTotalOut();
        List<String> internalIn = sysUserIdsBillConfig.getInternalIn();
        List<String> internalOut = sysUserIdsBillConfig.getInternalOut();
        List<String> otcIn = sysUserIdsBillConfig.getOtcIn();
        List<String> otcOut = sysUserIdsBillConfig.getOtcOut();
        List<String> other = sysUserIdsBillConfig.getOther();
        List<BillCoinTypeUserProperty> totalInList = new ArrayList<>();
        List<BillCoinTypeUserProperty> totalOutList = new ArrayList<>();
        List<BillCoinTypeUserProperty> internalInList = new ArrayList<>();
        List<BillCoinTypeUserProperty> internalOutList = new ArrayList<>();
        List<BillCoinTypeUserProperty> otcInList = new ArrayList<>();
        List<BillCoinTypeUserProperty> otcOutList = new ArrayList<>();
        List<BillCoinTypeUserProperty> otherList = new ArrayList<>();
        AccountTypeEnum anEnum = AccountTypeEnum.toEnum(reconUserTypeChangeParams.getAccountType().byteValue());
        // 获取所有信息，循环遍历分组,按照业务系统划分
        if (anEnum == SPOT) {
            if (CollectionUtils.isNotEmpty(list)) {
                for (BillCoinTypeUserProperty billCoinTypeUserProperty : list) {
                    // 筛选条件
                    if (totalIn.contains(billCoinTypeUserProperty.getBizType())) {
                        totalInList.add(billCoinTypeUserProperty);
                    }
                    if (totalOut.contains(billCoinTypeUserProperty.getBizType())) {
                        totalOutList.add(billCoinTypeUserProperty);
                    }
                    if (internalIn.contains(billCoinTypeUserProperty.getBizType())) {
                        internalInList.add(billCoinTypeUserProperty);
                    }
                    if (internalOut.contains(billCoinTypeUserProperty.getBizType())) {
                        internalOutList.add(billCoinTypeUserProperty);
                    }
                    if (other.contains(billCoinTypeUserProperty.getBizType())) {
                        otherList.add(billCoinTypeUserProperty);
                    }
                }
            }
            List<ReconUserOutInBalanceVo> totalInBalanceVoList = selectSpotOrOtc(totalInList, BalanceTypeEnum.TOTALIN.getCode());
            List<ReconUserOutInBalanceVo> totalOutBalanceVoList = selectSpotOrOtc(totalOutList, BalanceTypeEnum.TOTALOUT.getCode());
            List<ReconUserOutInBalanceVo> internalInBalanceVoList = selectSpotOrOtc(internalInList, BalanceTypeEnum.INTERNALIN.getCode());
            List<ReconUserOutInBalanceVo> internalOutBalanceVoList = selectSpotOrOtc(internalOutList, BalanceTypeEnum.INTERNALOUT.getCode());
            List<ReconUserOutInBalanceVo> otherBalanceVoList = selectSpotOrOtc(otherList, BalanceTypeEnum.OTHER.getCode());
            reconUserOutInBalanceVos.addAll(totalInBalanceVoList);
            reconUserOutInBalanceVos.addAll(totalOutBalanceVoList);
            reconUserOutInBalanceVos.addAll(internalInBalanceVoList);
            reconUserOutInBalanceVos.addAll(internalOutBalanceVoList);
            reconUserOutInBalanceVos.addAll(otherBalanceVoList);
            return selectTotal(reconUserOutInBalanceVos);
        } else if (anEnum == OTC) {
            if (CollectionUtils.isNotEmpty(list)) {
                for (BillCoinTypeUserProperty billCoinTypeUserProperty : list) {
                    if (otcIn.contains(billCoinTypeUserProperty.getBizType())) {
                        otcInList.add(billCoinTypeUserProperty);
                    }
                    if (otcOut.contains(billCoinTypeUserProperty.getBizType())) {
                        otcOutList.add(billCoinTypeUserProperty);
                    }
                }
            }
            List<ReconUserOutInBalanceVo> otcInBalanceVoList = selectSpotOrOtc(otcInList, BalanceTypeEnum.OTCIN.getCode());
            List<ReconUserOutInBalanceVo> otcOutBalanceVoList = selectSpotOrOtc(otcOutList, BalanceTypeEnum.OTCOUT.getCode());
            reconUserOutInBalanceVos.addAll(otcInBalanceVoList);
            reconUserOutInBalanceVos.addAll(otcOutBalanceVoList);
            return selectTotal(reconUserOutInBalanceVos);
        }
        //除了spot otc swap_main 的情况
        return selectTotal(reconUserOutInBalanceVos);
    }


    public List<ReconUserOutInBalanceVo> selectCmt(List<BillCoinTypeUserProperty> list, Integer type) {
        List<ReconUserOutInBalanceVo> outInBalanceVoList = new ArrayList<>();
        BalanceTypeEnum balanceTypeEnum = BalanceTypeEnum.toEnum(type);
        Map<Integer, List<BillCoinTypeUserProperty>> map = list.stream().collect(Collectors.groupingBy(BillCoinTypeUserProperty::getCoinId));
        for (Map.Entry<Integer, List<BillCoinTypeUserProperty>> entry : map.entrySet()) {
            ReconUserOutInBalanceVo reconUserOutInBalanceVo = new ReconUserOutInBalanceVo();
            List<BillCoinTypeUserProperty> billCoinTypeUserProperties = entry.getValue();
            BigDecimal totalProp1 = billCoinTypeUserProperties.stream().map(BillCoinTypeUserProperty::getProp1).reduce(BigDecimal.ZERO, BigDecimal::add);
            reconUserOutInBalanceVo.setCoinId(entry.getKey());
            switch (balanceTypeEnum) {
                case TOTALIN:
                    reconUserOutInBalanceVo.setTotalInBalance(totalProp1);
                    break;
                case TOTALOUT:
                    reconUserOutInBalanceVo.setTotalOutBalance(totalProp1);
                    break;
                case INTERNALIN:
                    reconUserOutInBalanceVo.setInternalInBalance(totalProp1);
                    break;
                case INTERNALOUT:
                    reconUserOutInBalanceVo.setInternalOutBalance(totalProp1);
                    break;
                case OTCIN:
                    reconUserOutInBalanceVo.setOtcInBalance(totalProp1);
                    break;
                case OTCOUT:
                    reconUserOutInBalanceVo.setOtcOutBalance(totalProp1);
                    break;
                case OTHER:
                    reconUserOutInBalanceVo.setOtherBalance(totalProp1);
                    break;
                default:
                    break;
            }
            outInBalanceVoList.add(reconUserOutInBalanceVo);
        }
        return outInBalanceVoList;
    }

    public List<ReconUserOutInBalanceVo> selectTotal(List<ReconUserOutInBalanceVo> reconUserOutInBalanceVos) {
        Map<Integer, List<ReconUserOutInBalanceVo>> map = reconUserOutInBalanceVos.stream().collect(Collectors.groupingBy(ReconUserOutInBalanceVo::getCoinId));
        List<ReconUserOutInBalanceVo> total = new ArrayList<>();
        BigDecimal zero = BigDecimal.ZERO;
        for (Map.Entry<Integer, List<ReconUserOutInBalanceVo>> entry : map.entrySet()) {
            ReconUserOutInBalanceVo reconUserOutInBalanceVo = new ReconUserOutInBalanceVo();
            List<ReconUserOutInBalanceVo> reconUserOutInBalanceVoList = entry.getValue();
            reconUserOutInBalanceVo.setCoinId(reconUserOutInBalanceVoList.get(0).getCoinId());
            for (ReconUserOutInBalanceVo user : reconUserOutInBalanceVoList) {
                if (user.getTotalInBalance() != null && (user.getTotalInBalance().compareTo(zero) != 0)) {
                    reconUserOutInBalanceVo.setTotalInBalance(user.getTotalInBalance());
                }
                if (user.getTotalOutBalance() != null && (user.getTotalOutBalance().compareTo(zero) != 0)) {
                    reconUserOutInBalanceVo.setTotalOutBalance(user.getTotalOutBalance());
                }
                if (user.getInternalInBalance() != null && (user.getInternalInBalance().compareTo(zero) != 0)) {
                    reconUserOutInBalanceVo.setInternalInBalance(user.getInternalInBalance());
                }
                if (user.getInternalOutBalance() != null && (user.getInternalOutBalance().compareTo(zero) != 0)) {
                    reconUserOutInBalanceVo.setInternalOutBalance(user.getInternalOutBalance());
                }
                if (user.getOtcInBalance() != null && (user.getOtcInBalance().compareTo(zero) != 0)) {
                    reconUserOutInBalanceVo.setOtcInBalance(user.getOtcInBalance());
                }
                if (user.getOtcOutBalance() != null && (user.getOtcOutBalance().compareTo(zero) != 0)) {
                    reconUserOutInBalanceVo.setOtcOutBalance(user.getOtcOutBalance());
                }
                if (user.getOtherBalance() != null && (user.getOtherBalance().compareTo(zero) != 0)) {
                    reconUserOutInBalanceVo.setOtherBalance(user.getOtherBalance());
                }
            }
            total.add(reconUserOutInBalanceVo);
        }
        return total;
    }


    public List<ReconUserOutInBalanceVo> selectSpotOrOtc(List<BillCoinTypeUserProperty> list, Integer type) {
        List<ReconUserOutInBalanceVo> outInBalanceVoList = new ArrayList<>();
        BalanceTypeEnum balanceTypeEnum = BalanceTypeEnum.toEnum(type);
        Map<Integer, List<BillCoinTypeUserProperty>> totalInMap = list.stream().collect(Collectors.groupingBy(BillCoinTypeUserProperty::getCoinId));
        for (Map.Entry<Integer, List<BillCoinTypeUserProperty>> totalInEntry : totalInMap.entrySet()) {
            ReconUserOutInBalanceVo reconUserOutInBalanceVo = new ReconUserOutInBalanceVo();
            List<BillCoinTypeUserProperty> billCoinTypeUserProperties = totalInEntry.getValue();
            reconUserOutInBalanceVo.setCoinId(totalInEntry.getKey());
            BigDecimal reduce = billCoinTypeUserProperties.stream().map(property -> property.getPropSum()).reduce(BigDecimal.ZERO, BigDecimal::add);
            switch (balanceTypeEnum) {
                case TOTALIN:
                    reconUserOutInBalanceVo.setTotalInBalance(reduce);
                    break;
                case TOTALOUT:
                    reconUserOutInBalanceVo.setTotalOutBalance(reduce);
                    break;
                case INTERNALIN:
                    reconUserOutInBalanceVo.setInternalInBalance(reduce);
                    break;
                case INTERNALOUT:
                    reconUserOutInBalanceVo.setInternalOutBalance(reduce);
                    break;
                case OTCIN:
                    reconUserOutInBalanceVo.setOtcInBalance(reduce);
                    break;
                case OTCOUT:
                    reconUserOutInBalanceVo.setOtcOutBalance(reduce);
                    break;
                case OTHER:
                    reconUserOutInBalanceVo.setOtherBalance(reduce);
                    break;
                default:
                    break;
            }
            outInBalanceVoList.add(reconUserOutInBalanceVo);
        }
        return outInBalanceVoList;
    }


    /**
     * 获取混合合约快照信息
     *
     * @param params
     * @return
     */
    private List<ReconTotalBalanceVo> getCurrentMixAssets(ReconUserAssetsSnapShotParams params) {
        List<ReconTotalBalanceVo> returnList = new ArrayList<>();
        Date checkTime = reconBaseService.getGlobalTime();
        SysAssetsParams sysAssetsParams = new SysAssetsParams();
        sysAssetsParams.setCoinId(params.getCoinId());
        sysAssetsParams.setAccountType(params.getAccountType());
        sysAssetsParams.setAccountParam(params.getAccountParam());
        ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(params.getAccountType().byteValue());
        // 返回集合--获取BillCoinUserAssets 或 BillCoinUserProperty的结果集，目的在于获取prop1 和 prop2
        List<BillCoinUserProperty> list = mixBillAssetsService.calAndGetMixBillAssets(params.getUserId(), sysAssetsParams, checkTime, apolloBillConfig);

        if (CollectionUtils.isNotEmpty(list)) {
            Map<Integer, List<BillCoinUserProperty>> map = list.stream().collect(Collectors.groupingBy(BillCoinUserProperty::getCoinId));
            for (Map.Entry<Integer, List<BillCoinUserProperty>> entry : map.entrySet()) {
                ReconTotalBalanceVo reconTotalBalanceVo = new ReconTotalBalanceVo();
                List<BillCoinUserProperty> listInLoop = entry.getValue();

                BigDecimal btcProp1 = listInLoop.stream().map(BillCoinUserProperty::getProp1).reduce(BigDecimal.ZERO, BigDecimal::add);
                reconTotalBalanceVo.setBalance(btcProp1);
                reconTotalBalanceVo.setShowBalance(btcProp1.setScale(8, BigDecimal.ROUND_DOWN).toPlainString());

                BigDecimal btcProp2 = listInLoop.stream().map(BillCoinUserProperty::getProp2).reduce(BigDecimal.ZERO, BigDecimal::add);
                reconTotalBalanceVo.setBalanceCanUse(btcProp2);
                reconTotalBalanceVo.setShowBalanceCanUse(btcProp2.setScale(8, BigDecimal.ROUND_DOWN).toPlainString());

                reconTotalBalanceVo.setCoinId(entry.getKey());
                reconTotalBalanceVo.setCoinName(selectCoinName(entry.getKey()));
                reconTotalBalanceVo.setProductCode(getProductName(params.getAccountParam()));
                returnList.add(reconTotalBalanceVo);
            }
        }
        return returnList;
    }


    @Override
    public ReconBillMessagePage<ReconTotalBalanceVo> selectCurrentSysAssetsSnapShotByTime(ReconUserAssetsSnapShotParams reconUserAssetsSnapShotParams) {
        List<ReconTotalBalanceVo> reconTotalBalanceVoList = new ArrayList<>();
        ReconUserAssetsSnapShotParams paramsCopy = BeanCopierUtil.copyProperties(reconUserAssetsSnapShotParams, ReconUserAssetsSnapShotParams.class);
        Integer accountType = paramsCopy.getAccountType();
        AccountTypeEnum anEnum = toEnum(accountType.byteValue());
        if (anEnum.isContract()) {
            reconTotalBalanceVoList = getCurrentMixAssets(paramsCopy);
        } else {
            // 根据用户查询checkTime
            paramsCopy.setAccountParam(ContractEnum.USDTACCOUNT.getDesc());
            Long userId = paramsCopy.getUserId();
            String accountParam = paramsCopy.getAccountParam();
            BillCoinUserProperty billUser = billCoinUserPropertyService.selectBySingleUserIdLatest(userId, accountParam, accountType);
            if (Objects.isNull(billUser)) {
                return selectPaging(paramsCopy.getPageNo(), paramsCopy.getPageSize(), reconTotalBalanceVoList);
            }
            // todo 还是索引的问题，get离这个时间往前最近的一次对账成功的时间，无论币种，这块需要加下索引
            BillCoinUserProperty begin = billCoinUserPropertySnapshotService.selectTime(userId, accountType, accountParam, billUser.getCheckTime());
            Date checkTime = Objects.isNull(begin) ? billUser.getCheckTime() : begin.getCheckTime();
            List<BillCoinUserProperty> billCoinUserProperties = billCoinUserPropertySnapshotService.selectAllCoinUserByCheckTimeAndCoinIds(accountType, accountParam, userId, checkTime);
            if (CollectionUtils.isNotEmpty(billCoinUserProperties)) {
                Map<Integer, List<BillCoinUserProperty>> map = billCoinUserProperties.stream().collect(Collectors.groupingBy(BillCoinUserProperty::getCoinId));
                for (Map.Entry<Integer, List<BillCoinUserProperty>> entry : map.entrySet()) {
                    ReconTotalBalanceVo reconTotalBalanceVo = new ReconTotalBalanceVo();
                    List<BillCoinUserProperty> billCoinUserPropertyList = entry.getValue();
                    BigDecimal bbAndOtc = billCoinUserPropertyList.stream().map(BillCoinUserProperty::getPropSum).reduce(BigDecimal.ZERO, BigDecimal::add);
                    reconTotalBalanceVo.setCoinId(entry.getKey());
                    reconTotalBalanceVo.setBalance(bbAndOtc);
                    reconTotalBalanceVo.setShowBalance(bbAndOtc.setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
                    reconTotalBalanceVo.setCoinName(selectCoinName(entry.getKey()));
                    if (Objects.nonNull(reconTotalBalanceVo.getCoinName())) {
                        reconTotalBalanceVoList.add(reconTotalBalanceVo);
                    }
                }
            }
        }
        ReconBillMessagePage<ReconTotalBalanceVo> reconBillMessagePage = selectPaging(paramsCopy.getPageNo(), paramsCopy.getPageSize(), reconTotalBalanceVoList);
        return reconBillMessagePage;
    }


    @Override
    public List<ReconUserOutInBalanceDetailsVo> selectOtherIncome(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        SysUserIdsBillConfig sysUserIdsBillConfig = ReconciliationApolloConfigUtils.getSysUserIdsBillConfig();
        List<String> otherList = sysUserIdsBillConfig.getOther();
        List<ReconUserOutInBalanceDetailsVo> reconUserOutInBalanceDetailsVos = getDetailsList(reconUserTypeChangeParams, otherList, UserAssetsDetailConstant.OTHER_INCOME);
        List<ReconUserOutInBalanceDetailsVo> filterList = reconUserOutInBalanceDetailsVos.stream().filter(a -> a.getBalance().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        return filterList;
    }

    @Override
    public List<ReconUserOutInBalanceVo> selectOutInBalance(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        ReconUserTypeChangeParams changeParams = BeanCopierUtil.copyProperties(reconUserTypeChangeParams, ReconUserTypeChangeParams.class);

        List<ReconUserOutInBalanceVo> reconUserOutInBalanceVos = new ArrayList<>();
        List<ReconUserOutInBalanceVo> total = new ArrayList<>();
        List<String> subSystem = getSubSystem();
        Stopwatch stopwatch = Stopwatch.createStarted();
        for (String string : subSystem) {
            String[] strings = string.split("-");
            Integer accountType = Integer.valueOf(strings[0].toUpperCase());
            String accountParam = strings[1].toUpperCase();
            changeParams.setAccountType(accountType);
            changeParams.setAccountParam(accountParam);
            List<ReconUserOutInBalanceVo> totalBalanceVoList = selectOutInBalanceByTime(changeParams);
            reconUserOutInBalanceVos.addAll(totalBalanceVoList);
            printLogForSlowInterface(accountType, accountParam, stopwatch, "selectOutInBalance");
        }
        Map<Integer, List<ReconUserOutInBalanceVo>> map = reconUserOutInBalanceVos.stream().collect(Collectors.groupingBy(ReconUserOutInBalanceVo::getCoinId));
        for (Map.Entry<Integer, List<ReconUserOutInBalanceVo>> entry : map.entrySet()) {
            ReconUserOutInBalanceVo reconUserOutInBalanceVo = new ReconUserOutInBalanceVo();
            List<ReconUserOutInBalanceVo> totalBalanceVoList = entry.getValue();
            reconUserOutInBalanceVo.setCoinId(entry.getKey());
            for (ReconUserOutInBalanceVo vo : totalBalanceVoList) {
                reconUserOutInBalanceVo.setTotalInBalance(reconUserOutInBalanceVo.getTotalInBalance().add(vo.getTotalInBalance()));
                reconUserOutInBalanceVo.setTotalOutBalance(reconUserOutInBalanceVo.getTotalOutBalance().add(vo.getTotalOutBalance()));
                reconUserOutInBalanceVo.setInternalInBalance(reconUserOutInBalanceVo.getInternalInBalance().add(vo.getInternalInBalance()));
                reconUserOutInBalanceVo.setInternalOutBalance(reconUserOutInBalanceVo.getInternalOutBalance().add(vo.getInternalOutBalance()));
                reconUserOutInBalanceVo.setOtcInBalance(reconUserOutInBalanceVo.getOtcInBalance().add(vo.getOtcInBalance()));
                reconUserOutInBalanceVo.setOtcOutBalance(reconUserOutInBalanceVo.getOtcOutBalance().add(vo.getOtcOutBalance()));
                reconUserOutInBalanceVo.setOtherBalance(reconUserOutInBalanceVo.getOtherBalance().add(vo.getOtherBalance()));
            }

            reconUserOutInBalanceVo.setDifferenceBalance(reconUserOutInBalanceVo.getTotalInBalance().add(reconUserOutInBalanceVo.getInternalInBalance()).add(reconUserOutInBalanceVo.getOtcInBalance()).add(reconUserOutInBalanceVo.getOtherBalance()).add(reconUserOutInBalanceVo.getTotalOutBalance()).add(reconUserOutInBalanceVo.getInternalOutBalance()).add(reconUserOutInBalanceVo.getOtcOutBalance()));

            reconUserOutInBalanceVo.setShowTotalInBalance(reconUserOutInBalanceVo.getTotalInBalance().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconUserOutInBalanceVo.setShowTotalOutBalance(reconUserOutInBalanceVo.getTotalOutBalance().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconUserOutInBalanceVo.setShowInternalInBalance(reconUserOutInBalanceVo.getInternalInBalance().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconUserOutInBalanceVo.setShowInternalOutBalance(reconUserOutInBalanceVo.getInternalOutBalance().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconUserOutInBalanceVo.setShowOtcInBalance(reconUserOutInBalanceVo.getOtcInBalance().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconUserOutInBalanceVo.setShowOtcOutBalance(reconUserOutInBalanceVo.getOtcOutBalance().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconUserOutInBalanceVo.setShowOtherBalance(reconUserOutInBalanceVo.getOtherBalance().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconUserOutInBalanceVo.setShowDifferenceBalance(reconUserOutInBalanceVo.getDifferenceBalance().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconUserOutInBalanceVo.setCoinName(selectCoinName(reconUserOutInBalanceVo.getCoinId()));
            total.add(reconUserOutInBalanceVo);
        }
        return total;
    }

    public String selectCoinName(Integer coinId) {
        // 调用接口
        Optional<SpotCoinDTO> spotCoinDTO = coinConfigService.getCoinOptional(coinId, -1);
        if (!Objects.isNull(spotCoinDTO) && !spotCoinDTO.equals(Optional.empty())) {
            String coinName = spotCoinDTO.map(SpotCoinDTO::getCoinName).orElseGet(null);
            if (Objects.isNull(coinName)) {
                log.info("coinName is null coinId = {}, coinName = {}", coinId, coinName);
            }
            return coinName;
        }
        return null;
    }


    @Override
    public List<AccountAssetsInfoResult> queryUserSingleRealTimeAssets(Long userId, String subSystem, Long requestDate, GlobalBillConfig globalBillConfig) {
        Stopwatch slowLogStopwatch = Stopwatch.createStarted();
        List<AccountAssetsInfoResult> userAssetsResult;
        String[] systemSplit = subSystem.split(BillConstants.SEPARATOR);
        byte accountType = Byte.parseByte(systemSplit[0]);
        AccountTypeEnum accountTypeEnum = toEnum(accountType);
        try {
            // 如果 requestDate 距离当前时间超过7天，则取当前时间
            long currentTimeMillis = System.currentTimeMillis();
            Date lastMin = DateUtil.getLastMin(requestDate,BillConstants.ONE);
            if (requestDate < currentTimeMillis - BillConstants.SEVEN_DAY_MIL_SEC) {
                lastMin = DateUtil.getLastMin(currentTimeMillis,BillConstants.ONE);
            }
            Map<Integer, PriceVo> ratesMap = commonService.getCoinIdRatesMapCache(lastMin.getTime());
            userAssetsResult = assetsQueryService.queryUserAssets(userId, accountTypeEnum, globalBillConfig);
            if(AssetsCheckTypeEnum.VALUE.getCode().equals(systemSplit[1])){
                Map<Long, AccountAssetsInfoResult> collect = userAssetsResult.stream().collect(Collectors.toMap(AccountAssetsInfoResult::getUserId, userAsset -> {
                    AccountAssetsInfoResult result = new AccountAssetsInfoResult();
                    result.setUserId(userAsset.getUserId());
                    result.setCoinId(userAsset.getCoinId());
                    BigDecimal price = commonService.checkRateBySwapTokenIdReturnUSDT(userAsset.getCoinId(), ratesMap);
                    result.setProp1(CalculateBillAssetsAmountUtil.calculateUserBillAssetsResult(accountTypeEnum, userAsset).multiply(price));
                    return result;
                }, (oldValue, newValue) -> {
                    AccountAssetsInfoResult result = new AccountAssetsInfoResult();
                    result.setUserId(oldValue.getUserId());
                    result.setProp1(oldValue.getProp1().add(newValue.getProp1()));
                    return result;
                }));
                if(collect.get(userId).getProp1().compareTo(BigDecimal.ZERO) >= 0){
                    return Collections.emptyList();
                }else {
                    return new ArrayList<>(collect.values());
                }
            }else {
                userAssetsResult = userAssetsResult.stream().filter(element -> CalculateBillAssetsAmountUtil.isUserAssetsNegative(element, accountTypeEnum)).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("queryUserRealTimeAssets is error， userId:{}, subSystem:{}, requestDate:{}", userId, subSystem, requestDate, e);
            throw new RuntimeException("提币检测查询用户实时资产接口异常", e);
        } finally {
            BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_queryUserSingleRealTimeAssets.getName(), userId, accountType);
        }
        return userAssetsResult;
    }

    @Override
    public ReconTotalAssetsDetailVo getUserAssetsBySnapShotTime(ReconUserAssetsBySnapShotTimeParams params) {
        try {
            //获取全部币种
            Map<Integer, String> allCoinsMap = commonService.getAllCoinsMapCache(params.getSnapShotTime());
            Map<Integer, PriceVo> rates = commonService.getCoinIdRatesMapCache(params.getSnapShotTime());
            if (CollectionUtils.isNotEmpty(params.getAccountTypes())) {
                // 校验accountTypes
                List<String> subSystemList = ReconciliationApolloConfigUtils.getApolloObjectConfig(ApolloKeyEnum.BILL_BASE_ASSET_SNAPSHOT_APOLLO_CONFIG, BillBaseAssetSnapshotApolloConfig.class).getSubSystemList();
                for (String subSystem : params.getAccountTypes()) {
                    if (!subSystemList.contains(subSystem)) {
                        throw new ApiException(BillExceptionEnum.ACCOUNT_PARAMS_IS_NULL, "账户参数用户id为空或快照时间为空");
                    }
                }
                Set<Integer> hasPositionCoinList = Sets.newConcurrentHashSet();
                ReconTotalAssetsDetailVo reconTotalAssetsDetailVo = userAssetsSnapShotBiz.listUserAssetsBySnapShotTime(params.getUserId(), params.getSnapShotTime(), allCoinsMap, rates, params.getAccountTypes(), hasPositionCoinList);
                reconTotalAssetsDetailVo.setHasPositionCoinList(hasPositionCoinList);
                return reconTotalAssetsDetailVo;
            } else {
                return userAssetsSnapShotBiz.listUserAssetsBySnapShotTime(params.getUserId(), params.getSnapShotTime(), allCoinsMap, rates);
            }
        } catch (Exception e) {
            log.error("getUserAssetsBySnapShotTime execute error:", e);
            return ReconTotalAssetsDetailVo.init();
        }
    }

    @Override
    public List<BillCoinUserProperty> obtainDataWithDiffAccountType(AccountTypeEnum anEnum, Long userId, SysAssetsParams sysAssetsParams, Date checkTime) {
        return obtainDataWithDiffAccountType(anEnum, userId, sysAssetsParams, checkTime, null);
    }


    @Override
    public List<BillCoinUserProperty> obtainDataWithDiffAccountType(AccountTypeEnum anEnum, Long userId, SysAssetsParams sysAssetsParams, Date checkTime, Set<Integer> hasPositionCoinList) {
        return obtainDataWithDiffAccountType(anEnum, userId, sysAssetsParams, checkTime, hasPositionCoinList, Boolean.TRUE);
    }

    @Override
    public List<BillCoinUserProperty> obtainDataWithDiffAccountType(AccountTypeEnum anEnum, Long userId, SysAssetsParams sysAssetsParams, Date checkTime, Set<Integer> hasPositionCoinList, Boolean isMaster) {
        List<BillCoinUserProperty> billCoinUserProperties = new ArrayList<>();
        //表数据是一段时间的流水增量，对帐的时候会计算上期+增量
        Integer accountType = sysAssetsParams.getAccountType();
        switch (anEnum) {
            case USDT_MIX_CONTRACT_BL:
            case USD_MIX_CONTRACT_BL:
            case USDC_MIX_CONTRACT_BL:
                MixAccountAssetsExtension extension = CacheUtils.computeIfAbsent(extensionCache, accountType + BillConstants.POUND_SIGN + DateUtil.getLastMin(checkTime).getTime(), () -> {
                    return innerQueryPriceFeignClient.queryPriceByTime(accountType, checkTime.getTime());
                });
                billCoinUserProperties = mixBillAssetsService.queryBillAssetsWithMix(userId, accountType, sysAssetsParams.getAccountParam(), checkTime, extension, hasPositionCoinList, isMaster);
                break;
            default:
                billCoinUserProperties = getDefaultAssets(userId, sysAssetsParams, checkTime, accountType, isMaster);
                break;
        }
        return billCoinUserProperties;
    }


    /**
     * 获取默认业务线资产
     *
     * @param userId          用户id
     * @param sysAssetsParams 系统资产参数
     * @param snapshotTime    快照时间
     * @param accountType     账户类型
     * @param isMaster
     * @return java.util.List<com.upex.bill.domain.BillCoinUserProperty>
     * @Date 2022/5/7 3:18 PM
     * <AUTHOR>
     */
    private List<BillCoinUserProperty> getDefaultAssets(Long userId, SysAssetsParams sysAssetsParams, Date snapshotTime, Integer accountType, Boolean isMaster) {
        Date lastFiveMin = DateUtil.getLastFiveMin(snapshotTime);
        //获取该用户最新对帐时间
        String accountParam = sysAssetsParams.getAccountParam();
        // 快照时间小于用户创建时间，返回空集合
        BillCoinUserProperty billCoinUserPropertyToCheckExist = billCoinUserPropertyService.selectBySingleUserIdLatest(userId, accountParam, accountType);
        if (billCoinUserPropertyToCheckExist == null) {
            return new ArrayList<>();
        }
        BillConfig billConfig = billConfigService.selectByTypeAndParam(accountType.byteValue(), accountParam);
        if (billConfig.getCheckOkTime().getTime() < lastFiveMin.getTime()) {
            // 还未对到那个时刻，非法参数，直接返回空
            return new ArrayList<>();
        }
        Map<Integer, BillCoinUserProperty> assetsMap = queryBillCoinUserAssetsMap(userId, accountType, accountParam, lastFiveMin);
        return new ArrayList<>(assetsMap.values());

    }


    public Map<Integer, BillCoinUserProperty> queryBillCoinUserAssetsMap(Long userId, Integer accountType, String accountParam, Date lastFiveMin) {
        Map<Integer, BillCoinUserProperty> coinIdBillCoinUserAssetsMap = new ConcurrentHashMap<>();
        List<BillCoinUserProperty> allCoinPropertyList = billCoinUserPropertyService.selectUserLatestAllRecord(accountType, accountParam, userId);
        List<BillCoinUserProperty> allCoinPropertyListAged = allCoinPropertyList.stream().filter(item -> item.getCheckTime().compareTo(lastFiveMin) <= 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(allCoinPropertyListAged)) {
            allCoinPropertyListAged.forEach(item -> {
                coinIdBillCoinUserAssetsMap.put(item.getCoinId(), item);
            });
        }
        List<Integer> allCoinIds = allCoinPropertyList.stream().filter(item -> item.getCheckTime().compareTo(lastFiveMin) > 0).map(BillCoinUserProperty::getCoinId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(allCoinIds)) {
            // 处理额外需要查询的币种记录
            coinAssetsTaskManager.forEachSubmitBatchAndWait(allCoinIds, coinId -> {
                BillCoinUserProperty billCoinUserProperty = billCoinUserPropertySnapshotService.selectCoinUserBeforeCheckTimeRecord(accountType, accountParam, coinId, userId, lastFiveMin);
                if (billCoinUserProperty != null) {
                    coinIdBillCoinUserAssetsMap.put(billCoinUserProperty.getCoinId(), billCoinUserProperty);
                }
            }, BillConstants.TEN);
        }
        return coinIdBillCoinUserAssetsMap;
    }


    public static ReconBillMessagePage<ReconTotalBalanceVo> selectPaging(Integer pageNo, Integer pageSize, List<ReconTotalBalanceVo> reconTotalBalanceVoList) {
        ReconBillMessagePage<ReconTotalBalanceVo> reconBillMessagePage = new ReconBillMessagePage<ReconTotalBalanceVo>();
        if (Objects.isNull(pageNo) || Objects.isNull(pageSize)) {
            pageNo = ContractEnum.PAGENO.getCode();
            pageSize = ContractEnum.PAGESIZE.getCode();
        }
        if (pageNo <= 0 || pageSize <= 0) {
            throw new ApiException(BillExceptionEnum.ILLEGAL_PARAMS);
        }
        if (pageSize >= BillConstants.MAX_PAGESIZE) {
            pageSize = BillConstants.MAX_PAGESIZE;
        }
        reconBillMessagePage.setCurrentPage(pageNo);
        reconBillMessagePage.setPageSize(pageSize);

        reconBillMessagePage.setTotal(reconTotalBalanceVoList.size());
        reconBillMessagePage.setItems(reconTotalBalanceVoList);
        return reconBillMessagePage;
    }


    @Override
    public String selectFinalSnapShotTime(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss ");
        //获取特定业务线的最后快照时间
        if (Objects.nonNull(reconUserTypeChangeParams) && Objects.nonNull(reconUserTypeChangeParams.getAccountType()) && Objects.nonNull(reconUserTypeChangeParams.getAccountParam())) {
            BillConfig billConfig = billConfigService.selectByTypeAndParam(reconUserTypeChangeParams.getAccountType().byteValue(), reconUserTypeChangeParams.getAccountParam());
            if (billConfig != null) {
                return dateFormat.format(billConfig.getCheckOkTime());
            }
        }
        // 全局对账时间
        Date checkOkTime = reconBaseService.getGlobalTime();
        return dateFormat.format(checkOkTime);
    }


    @Override
    public ReconUserAssetsVo selectUserAssets(ReconUserAssetsSnapShotParams params) {
        ReconUserAssetsVo reconUserAssetsVo = new ReconUserAssetsVo();
        List<String> subSystem = getSubSystem();//获取子系统列表
        Stopwatch stopwatch = Stopwatch.createStarted();
        Stopwatch stopwatchForTotal = Stopwatch.createStarted();
        ReconUserAssetsSnapShotParams paramsCopy = BeanCopierUtil.copyProperties(params, ReconUserAssetsSnapShotParams.class);
        for (String string : subSystem) {
            String[] strings = string.split("-");
            Integer accountType = Integer.valueOf(strings[0].toUpperCase());
            String accountParam = strings[1].toUpperCase();
            paramsCopy.setAccountType(accountType);
            paramsCopy.setAccountParam(accountParam);

            AccountTypeEnum anEnum = toEnum(accountType.byteValue());
//            统计otc资产
            statisticsOtcAssets(paramsCopy, reconUserAssetsVo, anEnum);
            printLogForSlowInterface(accountType, accountParam, stopwatch, "selectUserAssets-statisticsOtcAssets");
//            统计现货资产
            statisticsSpotAssets(paramsCopy, reconUserAssetsVo, anEnum);
            printLogForSlowInterface(accountType, accountParam, stopwatch, "selectUserAssets-statisticsSpotAssets");
//            混合合约
            statisticsMixContractAssets(paramsCopy, reconUserAssetsVo, anEnum);
            printLogForSlowInterface(accountType, accountParam, stopwatch, "selectUserAssets-statisticsMixContractAssets");
//            总资产
            statisticsTotalAssets(reconUserAssetsVo);
            printLogForSlowInterface(accountType, accountParam, stopwatch, "selectUserAssets-statisticsTotalAssets");
            printLogForSlowInterface(accountType, accountParam, stopwatchForTotal, "selectUserAssets-cycle total");
        }
        return reconUserAssetsVo;
    }


    private void printLogForSlowInterface(Integer accountType, String accountParam, Stopwatch stopwatch, String logSign) {
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        if (globalBillConfig.isSlowInterfaceLogOpen()) {
            log.info("UserAssetsSnapShotServiceImpl-{} accountType:{},accountParam:{},time:{} .......", logSign, accountType, accountParam, stopwatch.stop());
            stopwatch.reset().start();
        }
    }


    /**
     * 统计USD混合合约资产
     *
     * @param params            查询用户资产快照参数类
     * @param reconUserAssetsVo 用户资产VO类
     * @param anEnum            账户类型
     * @return void
     * @Date 2022/6/27 3:37 PM
     * <AUTHOR>
     */
    private void usdMixContractAssets(ReconUserAssetsSnapShotParams params, ReconUserAssetsVo reconUserAssetsVo, AccountTypeEnum anEnum) {
        if (anEnum == USD_MIX_CONTRACT_BL) {
            List<ReconTotalBalanceVo> list = getCurrentMixAssets(params);
            BigDecimal asset = dealTotalBalances(list, anEnum, true);
            reconUserAssetsVo.setUsdMixAssets(asset);
            reconUserAssetsVo.setShowUsdMixAssets(asset.setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            list.clear();
        }
    }

    /**
     * 统计混合合约资产
     *
     * @param params            查询用户资产快照参数类
     * @param reconUserAssetsVo 用户资产VO类
     * @param anEnum            账户类型
     * @return void
     * @Date 2022/6/27 3:36 PM
     * <AUTHOR>
     */
    private void statisticsMixContractAssets(ReconUserAssetsSnapShotParams params, ReconUserAssetsVo reconUserAssetsVo, AccountTypeEnum anEnum) {
        usdtMixContractAssets(params, reconUserAssetsVo, anEnum);
        usdMixContractAssets(params, reconUserAssetsVo, anEnum);
//            USDC是否需要乘以汇率,和币本位一样
        usdcMixContractAssets(params, reconUserAssetsVo, anEnum);
    }

    /**
     * 统计USDC混合合约资产
     *
     * @param params            查询用户资产快照参数类
     * @param reconUserAssetsVo 用户资产VO类
     * @param anEnum            账户类型
     * @return void
     * @Date 2022/6/27 3:37 PM
     * <AUTHOR>
     */
    private void usdcMixContractAssets(ReconUserAssetsSnapShotParams params, ReconUserAssetsVo reconUserAssetsVo, AccountTypeEnum anEnum) {
        if (anEnum == USDC_MIX_CONTRACT_BL) {
            List<ReconTotalBalanceVo> list = getCurrentMixAssets(params);
            BigDecimal asset = dealTotalBalances(list, anEnum, true);
            reconUserAssetsVo.setUsdcMixAssets(asset);
            reconUserAssetsVo.setShowUsdcMixAssets(asset.setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            list.clear();
        }
    }

    /**
     * 统计USDT混合合约资产
     *
     * @param params            查询用户资产快照参数类
     * @param reconUserAssetsVo 用户资产VO类
     * @param anEnum            账户类型
     * @return void
     * @Date 2022/6/27 3:37 PM
     * <AUTHOR>
     */
    private void usdtMixContractAssets(ReconUserAssetsSnapShotParams params, ReconUserAssetsVo reconUserAssetsVo, AccountTypeEnum anEnum) {
        if (anEnum == USDT_MIX_CONTRACT_BL) {
            List<ReconTotalBalanceVo> list = getCurrentMixAssets(params);
            BigDecimal asset = dealTotalBalances(list, anEnum, false);
            reconUserAssetsVo.setUsdtMixAssets(asset);
            reconUserAssetsVo.setShowUsdtMixAssets(asset.setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            list.clear();
        }
    }

    /**
     * 统计混合合约资产
     *
     * @param reconUserAssetsVo 用户资产VO类
     * @return void
     * @Date 2022/6/27 3:40 PM
     * <AUTHOR>
     */
    private void statisticsTotalAssets(ReconUserAssetsVo reconUserAssetsVo) {
        reconUserAssetsVo.setTotal(reconUserAssetsVo.calTotal());
        reconUserAssetsVo.setShowTotal(reconUserAssetsVo.getTotal().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
    }


    /**
     * 统计现货资产
     *
     * @param params            查询用户资产快照参数类
     * @param reconUserAssetsVo 用户资产VO类
     * @param anEnum            账户类型
     * @return void
     * @Date 2022/6/27 3:37 PM
     * <AUTHOR>
     */
    private void statisticsSpotAssets(ReconUserAssetsSnapShotParams params, ReconUserAssetsVo reconUserAssetsVo, AccountTypeEnum anEnum) {
        if (anEnum == SPOT) {
            //根据用户当前时间查询不同业务系统用户资产快照
            List<ReconTotalBalanceVo> list = selectCurrentSysAssetsSnapShotByTime(params).getItems();
            if (CollectionUtils.isNotEmpty(list)) {
                BigDecimal asset = dealTotalBalances(list, anEnum, true);
                reconUserAssetsVo.setSpotAssets(asset);
                reconUserAssetsVo.setShowSpotAssets(asset.setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
                list.clear();
            }
        }
    }


    /**
     * 统计otc资产
     *
     * @param params            查询用户资产快照参数类
     * @param reconUserAssetsVo 用户资产VO类
     * @param anEnum            账户类型
     * @return void
     * @Date 2022/6/27 3:38 PM
     * <AUTHOR>
     */
    private void statisticsOtcAssets(ReconUserAssetsSnapShotParams params, ReconUserAssetsVo reconUserAssetsVo, AccountTypeEnum anEnum) {
        if (anEnum == OTC) {
            //根据用户当前时间查询不同业务系统用户资产快照
            List<ReconTotalBalanceVo> list = selectCurrentSysAssetsSnapShotByTime(params).getItems();
            if (CollectionUtils.isNotEmpty(list)) {
                BigDecimal asset = dealTotalBalances(list, anEnum, true);
                reconUserAssetsVo.setOtcAssets(asset);
                reconUserAssetsVo.setShowOtcAssets(asset.setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
                list.clear();
            }
        }
    }


    private BigDecimal dealTotalBalances(List<ReconTotalBalanceVo> list, AccountTypeEnum anEnum, boolean isMulRate) {
        BigDecimal result = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(list)) {
            if (!isMulRate) {
                result = list.stream().map(ReconTotalBalanceVo::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
                return result;
            }
            Map<Integer, List<ReconTotalBalanceVo>> collect = list.stream().collect(Collectors.groupingBy(ReconTotalBalanceVo::getCoinId));
            for (Map.Entry<Integer, List<ReconTotalBalanceVo>> entry : collect.entrySet()) {
                Integer key = entry.getKey();
                List<ReconTotalBalanceVo> value = entry.getValue();
                BigDecimal rate = rate(key);
                BigDecimal reduce = value.stream().map(ReconTotalBalanceVo::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(rate);
                result = result.add(reduce);
            }
        }
        return result;
    }


    // 判断是否包含*
    private List<String> getSpecialCharacters(List<String> list) {
        List<String> strList = new ArrayList<>();
        String specialCharacters = "*";
        //增加非空判断
        if (CollectionUtils.isNotEmpty(list)) {
            for (String str : list) {
                if (str.contains(specialCharacters)) {
                    strList.add(str);
                }
            }
        }
        return strList;
    }

    private BigDecimal rate(Integer coinId) {
        // 获取汇率, 根据币种id获取币种名称
        BigDecimal zero = BigDecimal.ZERO;
        String coinName = selectCoinName(coinId);
        if (Objects.isNull(coinName)) {
            return zero;
        }
        String leftCoin = selectCoinName(coinId).toUpperCase();
        String rightCoin = "usdt".toUpperCase();
        String tradingOn = leftCoin + "_" + rightCoin;
        Map<Object, Object> rateMap = new HashMap<>();
        // 获取交易对接口
        ResponseResult<Map<Object, Object>> responseResult = spotCoinService.getCoinRate();
        for (Map.Entry<Object, Object> en : responseResult.getData().entrySet()) {
            String key = (String) en.getKey();
            String value = (String) en.getValue();
            if (tradingOn.toLowerCase().equals(key)) {
                rateMap.put(tradingOn, value);
            }
        }
        BigDecimal rate = ExchangeUtils.getRate(leftCoin, rightCoin, rateMap);
        //logger.info("rate rate={},tradingOn={}", rate, tradingOn);
        return rate == null ? BigDecimal.ZERO : rate;
    }


    private void containAndAddToList(BillCoinTypeUserProperty billCoinTypeUserProperty, List<String> source, List<String> specialSource, List<BillCoinTypeUserProperty> sourceList) {
        if (Objects.isNull(billCoinTypeUserProperty)) {
            throw new ApiException(BillExceptionEnum.ACCOUNT_PARAMS_IS_NULL);
        }
        String bizType = billCoinTypeUserProperty.getBizType();
        if (CollectionUtils.isNotEmpty(source) && source.contains(bizType)) {
            sourceList.add(billCoinTypeUserProperty);
        }
        if (CollectionUtils.isNotEmpty(specialSource)) {
            // 匹配"*"的类型
            for (String str : specialSource) {
                String str1 = getStr(str);
                if (bizType.contains(str1)) {
                    sourceList.add(billCoinTypeUserProperty);
                }
            }
        }
    }

    private String getStr(String str) {
        int index = str.indexOf("*");
        // 截取*出现位置前面的字符串
        return str.substring(0, index);
    }


    @Override
    public List<ReconBbTradingVo> selectBbTrading(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        List<ReconBbTradingVo> reconBbTradingVoList = new ArrayList<>();
        SysUserIdsBillConfig sysUserIdsBillConfig = ReconciliationApolloConfigUtils.getSysUserIdsBillConfig();
        ReconUserTypeChangeParams paramsCopy = BeanCopierUtil.copyProperties(reconUserTypeChangeParams, ReconUserTypeChangeParams.class);
        paramsCopy.setAccountType((int) SPOT.getCode());
        paramsCopy.setAccountParam(ContractEnum.USDTACCOUNT.getDesc());
        List<BillCoinTypeUserProperty> list = getTimeDifferenceV2(paramsCopy.getUserId(), paramsCopy.getBeginTime(), paramsCopy.getEndTime(), paramsCopy.getAccountType(), paramsCopy.getAccountParam());

        List<String> income = sysUserIdsBillConfig.getIncome();
        List<String> spending = sysUserIdsBillConfig.getSpending();
        List<String> buy = sysUserIdsBillConfig.getBuy();
        List<String> sell = sysUserIdsBillConfig.getSell();
        List<String> poundage = sysUserIdsBillConfig.getPoundage();
        List<BillCoinTypeUserProperty> incomeList = new ArrayList<>();
        List<BillCoinTypeUserProperty> spendingList = new ArrayList<>();
        List<BillCoinTypeUserProperty> buyList = new ArrayList<>();
        List<BillCoinTypeUserProperty> sellList = new ArrayList<>();
        List<BillCoinTypeUserProperty> poundageList = new ArrayList<>();

        List<String> specialBuy = getSpecialCharacters(buy);
        List<String> specialSell = getSpecialCharacters(sell);
        List<String> specialIncome = getSpecialCharacters(income);
        List<String> specialSpending = getSpecialCharacters(spending);
        List<String> specialPoundage = getSpecialCharacters(poundage);
        if (CollectionUtils.isNotEmpty(list)) {
            for (BillCoinTypeUserProperty bctp : list) {
                // 筛选条件
                // 包含*的单独考虑
                containAndAddToList(bctp, income, specialIncome, incomeList);

                containAndAddToList(bctp, spending, specialSpending, spendingList);

                containAndAddToList(bctp, poundage, specialPoundage, poundageList);

                containAndAddToList(bctp, buy, specialBuy, buyList);

                containAndAddToList(bctp, sell, specialSell, sellList);
            }
        }
        List<ReconBbTradingVo> incomeBbTradingList = selectSpotOrOtcTrading(incomeList, BalanceTypeEnum.INCOME.getCode());
        List<ReconBbTradingVo> spendingBbTradingList = selectSpotOrOtcTrading(spendingList, BalanceTypeEnum.SPENDING.getCode());
        List<ReconBbTradingVo> buyBbTradingList = selectSpotOrOtcTrading(buyList, BalanceTypeEnum.BUY.getCode());
        List<ReconBbTradingVo> sellBbTradingList = selectSpotOrOtcTrading(sellList, BalanceTypeEnum.SELL.getCode());
        List<ReconBbTradingVo> poundageBbTradingList = selectSpotOrOtcTrading(poundageList, BalanceTypeEnum.POUNDAGE.getCode());
        reconBbTradingVoList.addAll(incomeBbTradingList);
        reconBbTradingVoList.addAll(spendingBbTradingList);
        reconBbTradingVoList.addAll(buyBbTradingList);
        reconBbTradingVoList.addAll(sellBbTradingList);
        reconBbTradingVoList.addAll(poundageBbTradingList);
        // 分组
        List<ReconBbTradingVo> total = selectSpotTotal(reconBbTradingVoList);
        // 计算变化值
        for (ReconBbTradingVo reconBbTradingVo : total) {
            reconBbTradingVo.setChange(reconBbTradingVo.getIncome().add(reconBbTradingVo.getSpending()).add(reconBbTradingVo.getBuy()).add(reconBbTradingVo.getSell()));
            reconBbTradingVo.setShowIncome(reconBbTradingVo.getIncome().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconBbTradingVo.setShowSpending(reconBbTradingVo.getSpending().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconBbTradingVo.setShowBuy(reconBbTradingVo.getBuy().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconBbTradingVo.setShowSell(reconBbTradingVo.getSell().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconBbTradingVo.setShowPoundage(reconBbTradingVo.getPoundage().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconBbTradingVo.setShowChange(reconBbTradingVo.getChange().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
            reconBbTradingVo.setCoinName(selectCoinName(reconBbTradingVo.getCoinId()));
        }
        return total;
    }

    public List<ReconBbTradingVo> selectSpotTotal(List<ReconBbTradingVo> list) {
        Map<Integer, List<ReconBbTradingVo>> map = list.stream().collect(Collectors.groupingBy(ReconBbTradingVo::getCoinId));
        List<ReconBbTradingVo> total = new ArrayList<>();
        BigDecimal zero = BigDecimal.ZERO;
        for (Map.Entry<Integer, List<ReconBbTradingVo>> entry : map.entrySet()) {
            ReconBbTradingVo reconBbTradingVo = new ReconBbTradingVo();
            List<ReconBbTradingVo> reconBbTradingVos = entry.getValue();
            reconBbTradingVo.setCoinId(reconBbTradingVos.get(0).getCoinId());
            for (ReconBbTradingVo spot : reconBbTradingVos) {
                if (spot.getIncome() != null && (spot.getIncome().compareTo(zero) != 0)) {
                    reconBbTradingVo.setIncome(spot.getIncome());
                }
                if (spot.getSpending() != null && (spot.getSpending().compareTo(zero) != 0)) {
                    reconBbTradingVo.setSpending(spot.getSpending());
                }
                if (spot.getPoundage() != null && (spot.getPoundage().compareTo(zero) != 0)) {
                    reconBbTradingVo.setPoundage(spot.getPoundage());
                }
                if (spot.getBuy() != null && (spot.getBuy().compareTo(zero) != 0)) {
                    reconBbTradingVo.setBuy(spot.getBuy());
                }
                if (spot.getSell() != null && (spot.getSell().compareTo(zero) != 0)) {
                    reconBbTradingVo.setSell(spot.getSell());
                }
            }
            total.add(reconBbTradingVo);
        }
        return total;
    }

    public List<ReconBbTradingVo> selectSpotOrOtcTrading(List<BillCoinTypeUserProperty> list, Integer type) {
        List<ReconBbTradingVo> reconBbTradingVoList = new ArrayList<>();
        BalanceTypeEnum balanceTypeEnum = BalanceTypeEnum.toEnum(type);
        Map<Integer, List<BillCoinTypeUserProperty>> map = list.stream().collect(Collectors.groupingBy(BillCoinTypeUserProperty::getCoinId));
        for (Map.Entry<Integer, List<BillCoinTypeUserProperty>> entry : map.entrySet()) {
            ReconBbTradingVo reconBbTradingVo = new ReconBbTradingVo();
            List<BillCoinTypeUserProperty> billCoinTypeUserProperties = entry.getValue();
            reconBbTradingVo.setCoinId(entry.getKey());
            BigDecimal reduce = billCoinTypeUserProperties.stream().map(BillCoinTypeUserProperty::getPropSum).reduce(BigDecimal.ZERO, BigDecimal::add);
            switch (balanceTypeEnum) {
                case INCOME:
                    reconBbTradingVo.setIncome(reduce);
                    break;
                case SPENDING:
                    reconBbTradingVo.setSpending(reduce);
                    break;
                case POUNDAGE:
                    reconBbTradingVo.setPoundage(reduce);
                    break;
                case BUY:
                    reconBbTradingVo.setBuy(reduce);
                    break;
                case SELL:
                    reconBbTradingVo.setSell(reduce);
                    break;
                default:
                    break;
            }
            reconBbTradingVoList.add(reconBbTradingVo);
        }
        return reconBbTradingVoList;
    }


    @Override
    public List<ReconContractTradingVo> selectContract(ReconUserTypeChangeParams reconUserTypeChangeParams) {
//        SysUserIdsBillConfig sysUserIdsBillConfig = ApolloBillConfigUtil.getSysUserIdsBillConfig();
//        List<String> uAccountType = sysUserIdsBillConfig.getUAccountType();
//        List<String> bAccountType = sysUserIdsBillConfig.getBAccountType();
//        if (ContractEnum.USDTACCOUNT.getCode().equals(userTypeChangeParams.getType())) {
//            return selectContract(uAccountType, userTypeChangeParams, userTypeChangeParams.getUserId());
//        } else if (ContractEnum.SPOTCONTRACT.getCode().equals(userTypeChangeParams.getType())) {
//            return selectContract(bAccountType, userTypeChangeParams, userTypeChangeParams.getUserId());
//        }
//        return null;
        return new ArrayList<>();
    }

    @Override
    public ReconUserTradingVo selectUserProfitLossTotal(ReconUserTypeChangeParams reconUserTypeChangeParams) {
        ReconUserTradingVo reconUserTradingVo = new ReconUserTradingVo();
        Date now = new Date();
        ReconUserTypeChangeParams paramsCopy = BeanCopierUtil.copyProperties(reconUserTypeChangeParams, ReconUserTypeChangeParams.class);
        List<ReconBbTradingVo> reconBbTradingVoList = selectBbTrading(paramsCopy);
        // 取今日的汇率
        Map<Integer, PriceVo> rates = commonService.getCoinIdRatesMapCache(now.getTime());
        for (ReconBbTradingVo reconBbTradingVo : reconBbTradingVoList) {
            // 统一走common的汇率
            BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(reconBbTradingVo.getCoinId(), rates);
            reconBbTradingVo.setChange(rate.multiply(reconBbTradingVo.getChange()));
//            reconBbTradingVo.setChange(rate(reconBbTradingVo.getCoinId()).multiply(reconBbTradingVo.getChange()));
            // bbTradingVo.setChange(rate.multiply(bbTradingVo.getChange()));

        }
        // 计算币币盈利总额、u合约盈亏总额、币合约盈亏总额
        reconUserTradingVo.setSpotProfitTotal(reconBbTradingVoList.stream().map(ReconBbTradingVo::getChange).reduce(BigDecimal.ZERO, BigDecimal::add));
        reconUserTradingVo.setShowSpotProfitTotal(reconUserTradingVo.getSpotProfitTotal().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
        paramsCopy.setType(ContractEnum.USDTACCOUNT.getCode());
        List<ReconContractTradingVo> reconContractTradingVoList = selectContract(paramsCopy);
        // usdt合约
        reconUserTradingVo.setUContractProfitLossTotal(reconContractTradingVoList.stream().map(ReconContractTradingVo::getTotalBalance).reduce(BigDecimal.ZERO, BigDecimal::add));
        reconUserTradingVo.setShowUContractProfitLossTotal(reconUserTradingVo.getUContractProfitLossTotal().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
        paramsCopy.setType(ContractEnum.SPOTCONTRACT.getCode());
        List<ReconContractTradingVo> spotReconContractTradingVoList = selectContract(paramsCopy);
        // 币本位合约
        for (ReconContractTradingVo reconContractTradingVo : spotReconContractTradingVoList) {
            reconContractTradingVo.setTotalBalance(rate(reconContractTradingVo.getCoinId()).multiply(reconContractTradingVo.getTotalBalance()));
            // contractTradingVo.setTotalBalance(rate.multiply(contractTradingVo.getTotalBalance()));
        }
        reconUserTradingVo.setBContractProfitLossTotal(spotReconContractTradingVoList.stream().map(ReconContractTradingVo::getTotalBalance).reduce(BigDecimal.ZERO, BigDecimal::add));
        reconUserTradingVo.setShowBContractProfitLossTotal(reconUserTradingVo.getBContractProfitLossTotal().setScale(8, BigDecimal.ROUND_DOWN).toPlainString());
        return reconUserTradingVo;
    }

    private BigDecimal getUserMixTotalBalance(AccountTypeEnum anEnum, List<BillCoinUserProperty> billCoinUserProperties) {
        BigDecimal total = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(billCoinUserProperties)) {
            Map<Integer, List<BillCoinUserProperty>> collect = billCoinUserProperties.stream().collect(Collectors.groupingBy(BillCoinUserProperty::getCoinId));
            for (Map.Entry<Integer, List<BillCoinUserProperty>> entry : collect.entrySet()) {
                BigDecimal rate = rate(entry.getKey());
                BigDecimal reduce = CalculateBillAssetsAmountUtil.calculateBillAssetsAmountResult(anEnum, entry.getValue());
                BigDecimal balance = reduce.multiply(rate);
                total = total.add(balance);
            }
        }
        return total;
    }


    @Override
    public ReconTotalAssetsDetailVo listUserAssetsBySnapShotTime(Long userId, Long snapShotTime, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates) {
        List<String> subSystemList = ReconciliationApolloConfigUtils.getApolloObjectConfig(ApolloKeyEnum.BILL_BASE_ASSET_SNAPSHOT_APOLLO_CONFIG, BillBaseAssetSnapshotApolloConfig.class).getSubSystemList();
        Set<Integer> hasPositionCoinList = Sets.newConcurrentHashSet();
        ReconTotalAssetsDetailVo reconTotalAssetsDetailVo = listUserAssetsBySnapShotTime(userId, snapShotTime, allCoinsMap, rates, subSystemList, hasPositionCoinList, Boolean.TRUE);
        reconTotalAssetsDetailVo.setHasPositionCoinList(hasPositionCoinList);
        return reconTotalAssetsDetailVo;
    }


    @Override
    public ReconTotalAssetsDetailVo listUserAssetsBySnapShotTime(Long userId, Long snapShotTime, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates, List<String> subSystemList, Set<Integer> hasPositionCoinList, Boolean isMaster) {
        Queue<ReconTotalAssetsVO> totalAssets = new ConcurrentLinkedQueue<>();
        Queue<BigDecimal> userTotalBalanceList = new ConcurrentLinkedQueue<>();
        Date checkTime = snapShotTime == null ? new Date() : new Date(snapShotTime);
        Date lastFiveMin = DateUtil.getLastFiveMin(checkTime);
        assetSnapshotTaskManager.forEachSubmitBatchAndWait(subSystemList, (String subSystem) -> {
            Stopwatch slowLogStopwatch = Stopwatch.createStarted();
            String[] strings = subSystem.split(BillConstants.SEPARATOR);
            Integer accountType = Integer.valueOf(strings[0].toUpperCase());
            String accountParam = strings[1].toUpperCase();
            AccountTypeEnum anEnum = toEnum(accountType.byteValue());
            try {
                SysAssetsParams sysAssetsParams = new SysAssetsParams();
                sysAssetsParams.setAccountType(accountType);
                sysAssetsParams.setAccountParam(accountParam);

//                得到用户 业务线下的 所有资产，此时获取的是币种 + 数量
                List<BillCoinUserProperty> billCoinUserProperties = obtainDataWithDiffAccountType(anEnum, userId, sysAssetsParams, lastFiveMin, hasPositionCoinList, isMaster);

                //创建 totalAssets 集合
                Queue<ReconTotalAssetsVO> totalAssetList = createTotalAssetList(anEnum, billCoinUserProperties, allCoinsMap, rates);
                BigDecimal totalAccount = totalAssetList.stream().map(ReconTotalAssetsVO::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);

                totalAssets.addAll(totalAssetList);
                userTotalBalanceList.add(totalAccount);
            } finally {
                BizLogUtils.printLogForSlowInterface(slowLogStopwatch, InterfaceMonitorMetricNameEnum.selectCheckForTheResults_listUserAssetsBySnapShotTime.getName(), userId, accountType.byteValue());
            }
        });


        return ReconTotalAssetsDetailVo.builder()
                .coinId(BillConstants.TWO)
                .coinName(BillConstants.COIN_NAME_USDT)
                .totalBalance(userTotalBalanceList.stream().reduce(BigDecimal.ZERO, BigDecimal::add))
                .totalAssetsDetail(new ArrayList<>(totalAssets))
                .hasPositionCoinList(hasPositionCoinList)
                .build();
    }


    @Override
    public Queue<ReconTotalAssetsVO> createTotalAssetList(AccountTypeEnum typeEnum, List<BillCoinUserProperty> billCoinUserProperties, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates) {
        Queue<ReconTotalAssetsVO> resultList = new ConcurrentLinkedQueue<>();
        if (CollectionUtils.isNotEmpty(billCoinUserProperties)) {
            Map<Integer, List<BillCoinUserProperty>> collect = billCoinUserProperties.stream().collect(Collectors.groupingBy(BillCoinUserProperty::getCoinId));
            // 改为多线程处理
            coinInfoTaskManager.forEachSubmitBatchAndWait(collect.entrySet(), entry -> {
                ReconTotalAssetsVO reconTotalAssetsVO = getTotalAssetsVO(typeEnum, allCoinsMap, rates, entry);
                resultList.add(reconTotalAssetsVO);
            }, BillConstants.TEN);
        }
        return resultList;
    }


    private ReconTotalAssetsVO getTotalAssetsVO(AccountTypeEnum typeEnum, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates, Map.Entry<Integer, List<BillCoinUserProperty>> entry) {
        ReconTotalAssetsVO reconTotalAssetsVO = new ReconTotalAssetsVO();
        Integer coinId = entry.getKey();
        PriceVo priceVo = rates.getOrDefault(coinId, new PriceVo(1, BigDecimal.ZERO, System.currentTimeMillis()));
        BigDecimal rate = NumberUtil.isNullDefaultZero(priceVo.getPrice());
        BigDecimal reduce = CalculateBillAssetsAmountUtil.calculateBillAssetsAmountResult(typeEnum, entry.getValue());
        BigDecimal loanCount = CalculateBillAssetsAmountUtil.calculateBillLoanCountAmountResult(typeEnum, entry.getValue());
        BigDecimal balance = reduce.multiply(rate);
        reconTotalAssetsVO.setCoinId(coinId);
        reconTotalAssetsVO.setCoinName(allCoinsMap.getOrDefault(coinId, MerkelTreeConstant.EMPTY).toUpperCase());
        reconTotalAssetsVO.setAssetsType((int) typeEnum.getCode());
        reconTotalAssetsVO.setLoanCount(loanCount);
        reconTotalAssetsVO.setCount(reduce);
        reconTotalAssetsVO.setURate(rate);
        reconTotalAssetsVO.setBalance(balance);
        return reconTotalAssetsVO;
    }

}
