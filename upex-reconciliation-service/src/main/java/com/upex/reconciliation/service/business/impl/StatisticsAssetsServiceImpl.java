package com.upex.reconciliation.service.business.impl;

import com.upex.reconciliation.service.business.StatisticsAssetsService;
import com.upex.reconciliation.service.service.BillCoinUserPropertyService;
import com.upex.reconciliation.service.service.BillConfigService;
import com.upex.reconciliation.service.service.BillUserPositionService;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.dto.BillCoinUserAssetsParam;
import com.upex.reconciliation.service.service.impl.BillCoinUserPropertySnapshotService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.utils.task.TaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Slf4j
@Service
public class StatisticsAssetsServiceImpl implements StatisticsAssetsService {


    @Resource(name = "coinAssetsTaskManager")
    private TaskManager coinAssetsTaskManager;

    @Autowired
    private BillCoinUserPropertyService billCoinUserPropertyService;

    @Resource
    private BillConfigService billConfigService;

    @Autowired
    private BillCoinUserPropertySnapshotService billCoinUserPropertySnapshotService;

    @Resource
    private BillUserPositionService billUserPositionService;


    @Override
    public List<BillCoinUserAssets> queryRecordsExtensionStrategyEachCoin(Long userId, Integer accountType, String accountParam, Date snapshotTime, Boolean isMaster) {
        Date lastFiveMin = DateUtil.getLastFiveMin(snapshotTime);
        // 多线程获取用户币种资产（包含用户持仓）
        //获取该用户最新对帐时间
        // 快照时间小于用户创建时间，返回空集合
        BillConfig billConfig = billConfigService.selectByTypeAndParam(accountType.byteValue(), accountParam);
        if (billConfig.getCheckOkTime().getTime() < lastFiveMin.getTime()) {
            // 还未对到那个时刻，非法参数，直接返回空
            return new ArrayList<>();
        }
        List<BillCoinUserProperty> allCoinPropertyList = billCoinUserPropertyService.selectUserLatestAllRecord(accountType, accountParam, userId);
        if (CollectionUtils.isEmpty(allCoinPropertyList)) {
            return new ArrayList<>();
        }
        Map<Integer, BillCoinUserAssets> coinIdBillCoinUserAssetsMap = queryBillCoinUserAssetsMap(allCoinPropertyList,userId, accountType, accountParam, lastFiveMin);
        // 查持仓，用于计算后续的未实现
        List<BillUserPosition> billUserPositionList = billUserPositionService.selectByCheckTime(accountType, accountParam, lastFiveMin, userId);
        if (CollectionUtils.isNotEmpty(billUserPositionList)) {
            // 按照 交易对使用的右币进行汇总 52都是 USDT  53可能有各种各样的币   54是 USDC
            Map<Integer, List<BillUserPosition>> positionMap = billUserPositionList.stream().collect(Collectors.groupingBy(BillUserPosition::getCoinId));
            for (Map.Entry<Integer, List<BillUserPosition>> entry : positionMap.entrySet()) {
                Integer coinId = entry.getKey();
                List<BillUserPosition> positions = entry.getValue();
                List<BillCoinUserAssetsParam> paramList = new ArrayList<>();
                BillCoinUserAssets billCoinUserAssets = coinIdBillCoinUserAssetsMap.computeIfAbsent(coinId, item -> {
                    BillCoinUserAssets asset = new BillCoinUserAssets();
                    asset.setUserId(userId);
                    asset.setCoinId(coinId);
                    asset.setCheckOkTime(lastFiveMin);
                    return asset;
                });
                for (BillUserPosition billUserPosition : positions) {
                    BillCoinUserAssetsParam billCoinUserAssetsParam = new BillCoinUserAssetsParam();
                    billCoinUserAssetsParam.setSAvg(billUserPosition.getSAvg());
                    billCoinUserAssetsParam.setLAvg(billUserPosition.getLAvg());
                    billCoinUserAssetsParam.setSCount(billUserPosition.getSCount());
                    billCoinUserAssetsParam.setLCount(billUserPosition.getLCount());
                    billCoinUserAssetsParam.setSId(billUserPosition.getSymbolId());
                    paramList.add(billCoinUserAssetsParam);
                }
                billCoinUserAssets.setBillCoinUserAssetsParamList(paramList);
            }
        }
        return new ArrayList<>(coinIdBillCoinUserAssetsMap.values());
    }

    @Override
    public List<BillCoinUserAssets> queryRecordsExtensionStrategySingleCoin(Long userId, Integer coinId, Integer accountType, String accountParam, Date snapshotTime) {
        List<BillCoinUserAssets> result = new ArrayList<>();
        Date lastFiveMin = DateUtil.getLastFiveMin(snapshotTime);
        BillCoinUserAssets billCoinUserAssets = querySingleCoinBillCoinUserAssets(userId, coinId, accountType, accountParam, lastFiveMin);
        // 查持仓，用于计算后续的未实现
        List<BillUserPosition> positions = billUserPositionService.selectByCheckTimeAndCoinId(accountType, accountParam, lastFiveMin, userId, coinId);
        if (CollectionUtils.isNotEmpty(positions)) {
            // 按照 交易对使用的右币进行汇总 52都是 USDT  53可能有各种各样的币   54是 USDC
            List<BillCoinUserAssetsParam> paramList = new ArrayList<>();
            for (BillUserPosition billUserPosition : positions) {
                BillCoinUserAssetsParam billCoinUserAssetsParam = new BillCoinUserAssetsParam();
                billCoinUserAssetsParam.setSAvg(billUserPosition.getSAvg());
                billCoinUserAssetsParam.setLAvg(billUserPosition.getLAvg());
                billCoinUserAssetsParam.setSCount(billUserPosition.getSCount());
                billCoinUserAssetsParam.setLCount(billUserPosition.getLCount());
                billCoinUserAssetsParam.setSId(billUserPosition.getSymbolId());
                paramList.add(billCoinUserAssetsParam);
            }
            billCoinUserAssets.setBillCoinUserAssetsParamList(paramList);
        }
        result.add(billCoinUserAssets);
        return result;
    }



    @Override
    public Map<Integer, BillCoinUserAssets> queryBillCoinUserAssetsMap(List<BillCoinUserProperty> allCoinPropertyList, Long userId, Integer accountType, String accountParam, Date lastFiveMin) {
        Map<Integer,BillCoinUserAssets> coinIdBillCoinUserAssetsMap=new ConcurrentHashMap<>();
        List<BillCoinUserProperty> allCoinPropertyListAged = allCoinPropertyList.stream().filter(item -> item.getCheckTime().compareTo(lastFiveMin) <= 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(allCoinPropertyListAged)) {
            allCoinPropertyListAged.forEach(item -> {
                BillCoinUserAssets billCoinUserAssets = new BillCoinUserAssets();
                BeanUtils.copyProperties(item, billCoinUserAssets);
                coinIdBillCoinUserAssetsMap.put(billCoinUserAssets.getCoinId(),billCoinUserAssets);
            });
        }
        List<Integer> allCoinIds = allCoinPropertyList.stream().filter(item -> item.getCheckTime().compareTo(lastFiveMin) > 0).map(BillCoinUserProperty::getCoinId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(allCoinIds)) {
            // 处理额外需要查询的币种记录
            coinAssetsTaskManager.forEachSubmitBatchAndWait(allCoinIds, coinId -> {
                BillCoinUserProperty billCoinUserProperty = billCoinUserPropertySnapshotService.selectCoinUserBeforeCheckTimeRecord(accountType, accountParam, coinId, userId, lastFiveMin);
                if (billCoinUserProperty != null) {
                    BillCoinUserAssets billCoinUserAssets = new BillCoinUserAssets();
                    BeanUtils.copyProperties(billCoinUserProperty, billCoinUserAssets);
                    coinIdBillCoinUserAssetsMap.put(billCoinUserAssets.getCoinId(),billCoinUserAssets);
                }
            }, BillConstants.TEN);
        }
        return coinIdBillCoinUserAssetsMap;
    }

    @Override
    public BillCoinUserAssets querySingleCoinBillCoinUserAssets(Long userId, Integer accountType, Integer coinId, String accountParam, Date lastFiveMin){
        BillCoinUserProperty billCoinUserProperty = billCoinUserPropertySnapshotService.selectCoinUserBeforeCheckTimeRecord(accountType, accountParam, coinId, userId, lastFiveMin);
        BillCoinUserAssets billCoinUserAssets = new BillCoinUserAssets();
        if (billCoinUserProperty == null) {
            billCoinUserAssets.setUserId(userId);
            billCoinUserAssets.setCoinId(coinId);
            billCoinUserAssets.setCheckOkTime(lastFiveMin);
            return billCoinUserAssets;
        }
        BeanUtils.copyProperties(billCoinUserProperty, billCoinUserAssets);
        return billCoinUserAssets;
    }

}
