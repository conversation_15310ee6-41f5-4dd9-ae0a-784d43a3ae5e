package com.upex.reconciliation.service.service.client.cex.dto.req.common;

import com.upex.commons.support.exception.ApiException;
import com.upex.commons.support.hmac.HmacEncryptUtil;
import com.upex.reconciliation.service.service.client.cex.enmus.*;
import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import lombok.Data;
import javax.validation.constraints.NotNull;

import static com.upex.reconciliation.service.service.client.cex.enmus.IReconCexErrorCode.*;

@Data
public class AddApiKeyReq {

    /**
     * 交易所注册邮箱
     */
    @NotNull(message = IReconCexErrorCode.ILLEGAL_CEXTYPE)
    private String cexEmail;


    /**
     * 交易所类型（1: Binance, 2: OKX）
     */
    @NotNull(message = IReconCexErrorCode.EMAIL_CANNOT_BENULL)
    private Integer cexType;

    /**
     * 交易所用户ID
     */
    @NotNull(message = IReconCexErrorCode.USERID_CANNOT_BENULL)
    private String cexUserId;

    /**
     * API Key 标签/描述
     */
    @NotNull(message =APIKEY_LABEL_CANNOT_BENULL)
    private String apiKeyLabel;

    /**
     * API Key 公钥
     */
    @NotNull(message = APIKEY_PUB_SECRET_CANNOT_BENULL)
    private String apiKeyPub;

    /**
     * 交易所 API Key
     */
    @NotNull(message =APIKEY_CANNOT_BENULL)
    private String apiKey;

    /**
     * 是否为资产监控用途
     */
    @NotNull(message = IF_ASSET_MONITOR_CANNOT_BENULL)
    private Boolean ifAssetMonitor;

//    /**
//     * API Key 权限配置
//     */
//    @NotNull(message = "API Key 权限信息不能为空")
//    private ApiKeyPermission apiKeyPermission;



    public void setCexType(Integer cexType) {
        CexTypeEnum cexTypeEnum = CexTypeEnum.fromType(cexType);
        if (cexTypeEnum == null) {
            throw new ApiException(ReconCexExceptionEnum.ILLEGAL_CEXTYPE);
        }
        this.cexType = cexType;
    }

}

