package com.upex.reconciliation.service.common.feign;

import com.upex.commons.support.si.ServiceIsolationConfigProvider;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 实现接口配置
 * <AUTHOR>
 */
@Component
public class ServiceIsolationConfigProviderConfig implements ServiceIsolationConfigProvider {

    @Override
    public String getAppId() {
        return ReconciliationApolloConfigUtils.getApolloGatewayConfig().getAppid();
    }

    @Override
    public String getAk() {
        return ReconciliationApolloConfigUtils.getApolloGatewayConfig().getAk();
    }

    @Override
    public String getSk() {
        return ReconciliationApolloConfigUtils.getApolloGatewayConfig().getSk();
    }

    @Override
    public Set<String> getDomains() {
        return ReconciliationApolloConfigUtils.getApolloGatewayConfig().getDomains();
    }
}