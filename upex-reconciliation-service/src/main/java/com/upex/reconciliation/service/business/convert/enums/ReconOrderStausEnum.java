package com.upex.reconciliation.service.business.convert.enums;

import lombok.Getter;

/**
 * 订单对账状态枚举
 */
@Getter
public enum ReconOrderStausEnum {

    // 未对账
    NOT_RECONCILED((byte) 0, "未对账"),
    // 对账成功
    RECONCILED_SUCCESS((byte) 1, "对账成功"),
    // 对账失败
    RECONCILED_FAILURE((byte) 2, "对账失败"),
    // 忽略对账
    IGNORED((byte) 3, "忽略对账");

    private final byte code;
    private final String desc;

    ReconOrderStausEnum(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
