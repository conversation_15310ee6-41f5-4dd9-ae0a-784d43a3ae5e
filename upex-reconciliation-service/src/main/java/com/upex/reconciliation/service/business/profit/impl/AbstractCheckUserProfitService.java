package com.upex.reconciliation.service.business.profit.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.business.ReconCheckBillResultService;
import com.upex.reconciliation.service.business.ReconUserAssetsSnapShotService;
import com.upex.reconciliation.service.business.UserQueryService;
import com.upex.reconciliation.service.business.profit.CheckUserProfitService;
import com.upex.reconciliation.service.business.profitabnormal.ProfitAbnormalService;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.BillRedisKeyEnum;
import com.upex.reconciliation.service.common.constants.enums.ProfitSourceEnum;
import com.upex.reconciliation.service.dao.entity.BillUserWithdrawProfitRecord;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.dto.AccountProfitDTO;
import com.upex.reconciliation.service.model.dto.ProfitAlarmDto;
import com.upex.reconciliation.service.model.enums.BillWhiteListTypeEnum;
import com.upex.reconciliation.service.service.BillUserWithdrawProfitRecordService;
import com.upex.reconciliation.service.service.BillWhiteListConfigService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.NumberUtil;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.utils.task.TaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static com.upex.reconciliation.service.business.profitbacktest.AbstractProfitBakCheck.PROFIT_BAKCHECK_FROM_XXLJOB;

/**
 * 用户及子账户盈利检测抽象类
 *
 * <AUTHOR>
 * @Date 2025/4/26
 */
@Slf4j
public abstract class AbstractCheckUserProfitService implements CheckUserProfitService {

    @Resource
    protected CommonService commonService;

    @Resource
    protected BillWhiteListConfigService billWhiteListConfigService;

    @Resource(name = "redisTemplate")
    protected RedisTemplate<String, Object> redisTemplate;

    @Resource
    protected ReconCheckBillResultService reconCheckBillResultService;

    @Resource
    protected UserQueryService userQueryService;

    @Resource(name = "assetUserProfitTaskManager")
    protected TaskManager assetUserProfitTaskManager;

    @Resource
    protected ReconUserAssetsSnapShotService reconUserAssetsSnapShotService;

    @Resource
    BillUserWithdrawProfitRecordService billUserWithdrawProfitRecordService;

    @Resource
    ProfitAbnormalService profitAbnormalService;

    @Override
    public boolean checkProfit(ReconCheckResultsParams params, GlobalBillConfig globalBillConfig, Integer timePeriod) {
        Stopwatch startWatch = Stopwatch.createStarted();

        // 增加redis
        Long userId = params.getUserId();
        // 盈利白名单用户名单
        Set<Long> profitWhiteUsers = billWhiteListConfigService.getLongWhiteList(BillWhiteListTypeEnum.PROFIT_WHITE_USER.getCode());
        if (profitWhiteUsers.contains(userId)) {
            return false;
        }

        Map<Integer, ProfitAlarmDto> timeRangeProfitAlarmMap = globalBillConfig.getTimePeriodProfitAlarmMap();
        ProfitAlarmDto profitAlarmDto = timeRangeProfitAlarmMap.get(timePeriod);
        if (profitAlarmDto == null) {
            return false;
        }
        String redisKey = String.format(BillRedisKeyEnum.RECON_WITHDRAWAL_CHECK_PROFIT_RESULT.getKey(), userId, profitAlarmDto.getProfitAlarmAmount(), profitAlarmDto.getProfitRatio(), globalBillConfig.getTimePeriodProfitRedisTime());

        String redisCheckResult = (String) redisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotBlank(redisCheckResult)) {
            log.info("checkProfitAccount redisKey:{} redisCheckResult:{}", redisKey, redisCheckResult);
            if (profitWhiteUsers.contains(userId)) {
                return false;
            }
            return Boolean.parseBoolean(redisCheckResult);
        }
        Stopwatch rateWatch = Stopwatch.createStarted();
        Long requestTime = params.getRequestDate();
        Date requestDate = new Date(requestTime);
        Date beginDate = DateUtil.addMinute(requestDate, BillConstants.NEG_ONE * timePeriod);
        Map<Integer, PriceVo> ratesMap = commonService.getCoinIdRatesMapCache(beginDate.getTime());
        Map<Integer, String> allCoinsMap = commonService.getAllCoinsMapCache(beginDate.getTime());

        log.info("AbstractCheckUserProfitService checkProfitAccount queryRates requestTime:{}, userId:{}, timePeriod:{}, time:{}", requestTime, userId, timePeriod, rateWatch.stop());

        Stopwatch profitWatch = Stopwatch.createStarted();

        Map<Long, AccountProfitDTO> userAccountProfitMap = new ConcurrentHashMap<>();

        // 获取账号盈利
        userAccountProfitMap.put(userId, getUserProfitAmount(params, globalBillConfig, timePeriod, allCoinsMap, ratesMap));
        profitWatch.stop();

        Stopwatch childProfitWatch = Stopwatch.createStarted();
        // 获取子账号盈利
        if (globalBillConfig.isCheckProfitAccountChildAccount()) {
            org.apache.commons.lang3.tuple.Pair <Boolean, List<Long>> pair = userQueryService.getChildListByParentId(userId, globalBillConfig.getChildAccountTypeList(), globalBillConfig.getChildAccountPageSize(), globalBillConfig.getMaxChildProfitAccountCount());
            if (pair.getLeft()) {
                if (CollectionUtils.isNotEmpty(pair.getRight())) {
                    log.info("AbstractCheckUserProfitService checkProfitAccount getChildListByParentId parentUserId:{} child size:{}", userId, pair.getRight().size());
                    userAccountProfitMap.putAll(getUserChildProfitAmount(params, pair.getRight(), globalBillConfig, timePeriod, allCoinsMap, ratesMap));
                }
            } else {
                log.info("AbstractCheckUserProfitService checkProfitAccount userId:{} childAccount exceeds:{}", userId, globalBillConfig.getMaxChildAccountCount());
            }
        }
        childProfitWatch.stop();

        BigDecimal userAllProfitAmount = userAccountProfitMap.values().stream().map(AccountProfitDTO::getProfitAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal userAllBeginAmount = userAccountProfitMap.values().stream().map(AccountProfitDTO::getBeginBalance).reduce(BigDecimal.ZERO, BigDecimal::add);

        boolean checkResult = checkProfitAmount(userAllProfitAmount, userAllBeginAmount, profitAlarmDto);
        // 全步骤日志打印
        log.info("AbstractCheckUserProfitService checkProfitAccount traceId:{}, profitAmount:{}, beginAmount:{}, requestTime:{}, userId:{}, timePeriod:{}, " +
                        "checkResult:{}, allTime:{}, 查rates时间:{}, 母账户时间:{}, 子账户时间:{}, requestTimeStr:{} allUserProfitInfo:{}",
                params.getTraceId(), userAllProfitAmount, userAllBeginAmount, requestTime, userId, timePeriod, checkResult,
                startWatch.stop(), rateWatch, profitWatch, childProfitWatch, DateUtil.getDefaultDateStr(new Date(requestTime)), JSON.toJSONString(userAccountProfitMap));
        boolean returnResult = checkResult;
        if (checkResult && globalBillConfig.isProfitSaveRedisAndAlarm()) {
            if (globalBillConfig.isCheckProfitAlarmOpen()) {
                reconCheckBillResultService.syncSendAlarmMessage(params, AlarmTemplateEnum.CAPITAL_BILL_PROFIT_WITHDRAW_DETAIL, params.getUserId(), userAllProfitAmount, userAllBeginAmount,globalBillConfig.isCheckProfitUserRiskCheckOpen(),globalBillConfig.isSendProfitAbnormalToRisk());
                redisTemplate.opsForList().rightPush(String.format(BillRedisKeyEnum.RECON_WITHDRAWAL_PROFIT_RISK_USER_ALARM.getKey()), JSON.toJSONString(Map.of("userId", userId, "requestDate", requestTime)));
                boolean sendAbnormalProfitRiskV2Result = profitAbnormalService.sendAbnormalProfitRiskV2(requestTime, params.getUserId(), userAccountProfitMap);
                returnResult = !sendAbnormalProfitRiskV2Result;
            }
            // 开启风控推送，走用户放空策略 不走老策略
            if(!globalBillConfig.isCheckProfitUserRiskCheckOpen()){
                redisTemplate.opsForValue().set(redisKey, String.valueOf(returnResult), globalBillConfig.getTimePeriodProfitRedisTime(), TimeUnit.HOURS);
            }
        }
        // 入库
        saveProfit(params, userAccountProfitMap, globalBillConfig, beginDate);
        return returnResult;
    }

    /**
     * 获取账户盈利数据
     *
     * @param params
     * @param globalBillConfig
     * @param timePeriod
     * @param allCoinsMap
     * @param rates
     * @return
     */
    public abstract AccountProfitDTO getUserProfitAmount(ReconCheckResultsParams params, GlobalBillConfig globalBillConfig,
                                                         Integer timePeriod, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates);

    /**
     * 获取所有子账户盈利数据
     *
     * @param params
     * @param childUserIdList
     * @param globalBillConfig
     * @param timePeriod
     * @param allCoinsMap
     * @param rates
     * @return
     */
    protected abstract Map<Long, AccountProfitDTO> getUserChildProfitAmount(ReconCheckResultsParams params, List<Long> childUserIdList, GlobalBillConfig globalBillConfig,
                                                         Integer timePeriod, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates);

    /**
     * 入库
     *
     * @param checkResultsParams
     * @param userAccountProfitMap
     * @param billConfig
     */
    private void saveProfit(ReconCheckResultsParams checkResultsParams, Map<Long, AccountProfitDTO> userAccountProfitMap, GlobalBillConfig billConfig, Date beginDate) {
        if (CollectionUtil.isEmpty(userAccountProfitMap)) {
            return;
        }
        if (!billConfig.isSaveProfitRecord()) {
            return;
        }
        Long parentUserId = checkResultsParams.getUserId();
        try {
            Date date = new Date();
            Long requestDate = checkResultsParams.getRequestDate();
            Map<Integer, PriceVo> rates = commonService.getCoinIdTradePriceMap(requestDate);
            List<BillUserWithdrawProfitRecord> profitRecordList = new ArrayList<>();
            BigDecimal userAllProfitAmount = userAccountProfitMap.values().stream().map(AccountProfitDTO::getProfitAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            for (Map.Entry<Long, AccountProfitDTO> profitEntry : userAccountProfitMap.entrySet()) {
                for (Map.Entry<Integer, BigDecimal> profitCoinEntry : profitEntry.getValue().getProfitCoinMap().entrySet()) {
                    BillUserWithdrawProfitRecord profitRecord = new BillUserWithdrawProfitRecord();
                    profitRecord.setParentUserId(parentUserId);
                    // 母账户总盈利
                    if (Objects.equals(parentUserId, profitEntry.getKey())) {
                        profitRecord.setReconProfitTotalAmount(userAllProfitAmount);
                    } else {
                        profitRecord.setUserId(profitEntry.getKey());
                    }
                    profitRecord.setCoinId(profitCoinEntry.getKey());
                    profitRecord.setRequestId("rc" + checkResultsParams.getTraceId());
                    profitRecord.setProfitCount(profitCoinEntry.getValue());
                    BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(profitCoinEntry.getKey(), rates);
                    profitRecord.setProfitUsdtAmount(profitCoinEntry.getValue().multiply(rate));
                    profitRecord.setProfitSource(checkResultsParams.getBusinessSource().equals(PROFIT_BAKCHECK_FROM_XXLJOB) ? ProfitSourceEnum.BACKTEST.getCode() : ProfitSourceEnum.BUSINESS.getCode());
                    profitRecord.setOrderId(checkResultsParams.getOrderId());
                    profitRecord.setRecordCode(checkResultsParams.getRecordCode());
                    profitRecord.setBusinessSource(checkResultsParams.getBusinessSource());
                    profitRecord.setRequestTime(new Date(requestDate));
                    profitRecord.setCheckBeginTime(beginDate);
                    profitRecord.setCheckEndTime(new Date(requestDate));
                    profitRecord.setCheckOkTime(date);
                    profitRecord.setCreateTime(date);
                    profitRecord.setUpdateTime(date);
                    profitRecordList.add(profitRecord);
                }
            }
            List<List<BillUserWithdrawProfitRecord>> billUserWithdrawProfitRecordList = Lists.partition(profitRecordList, billConfig.getInsertSqlSize());
            assetUserProfitTaskManager.forEachSubmitBatchAndWait(billUserWithdrawProfitRecordList, (List<BillUserWithdrawProfitRecord> subRecords) -> billUserWithdrawProfitRecordService.batchInsert(subRecords), BillConstants.TEN);
        } catch (Exception e) {
            log.error("saveProfit checkResultsParams:{} error:{}", checkResultsParams, e);
        }
    }

    private boolean checkProfitAmount(BigDecimal profitAmount, BigDecimal beginBalance, ProfitAlarmDto profitAlarmDto) {
        if (NumberUtil.isEmptyOrZero(profitAmount)) {
            return false;
        }
        BigDecimal profitAlarmAmount = profitAlarmDto.getProfitAlarmAmount();
        BigDecimal profitRatio = profitAlarmDto.getProfitRatio();
        if (NumberUtil.isEmptyOrZero(beginBalance)) {
            // 期初为0，资产增加总值(盈利) > 盈利报警总数量
            return profitAmount.compareTo(profitAlarmAmount) > 0;
        }
        BigDecimal maxProfitAlarmAmount = profitAlarmDto.getMaxProfitAlarmAmount();
        // 资产增加总值(盈利) > 盈利报警总数量 && 资产增加百分比(盈利/期初) > 盈利/期初 或 用户盈利大于等于最大盈利告警阈值
        return (profitAmount.compareTo(profitAlarmAmount) > 0 && profitAmount.divide(beginBalance, RoundingMode.HALF_UP).compareTo(profitRatio) > 0)
                || (maxProfitAlarmAmount.compareTo(BigDecimal.ZERO) > 0 && profitAmount.compareTo(maxProfitAlarmAmount) >= 0);
    }
}
