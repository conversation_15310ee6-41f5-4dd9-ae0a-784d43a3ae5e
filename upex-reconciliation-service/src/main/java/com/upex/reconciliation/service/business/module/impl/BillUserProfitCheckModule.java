package com.upex.reconciliation.service.business.module.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.*;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.common.concurrent.ConcurrentSortMap;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.entity.BillAllConfig;
import com.upex.reconciliation.service.model.BillCmdResult;
import com.upex.reconciliation.service.model.BillCmdWrapper;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.BillProfitTimeSliceDTO;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.service.BillAllConfigService;
import com.upex.reconciliation.service.utils.*;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.utils.log.AlarmUtils;
import com.upex.utils.task.TaskManager;
import com.upex.utils.task.TaskVoidBatchResult;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.USER_PROFIT_MODULE_TAKE_COMMAND_ERROR;
import static com.upex.reconciliation.service.utils.MetricsUtil.HISTOGRAM_USER_TAKE_COMMAND;


/**
 * 用户实时盈亏计算
 */
@Slf4j
public class BillUserProfitCheckModule {
    /***批量处理变化用户数***/
    private static final Integer CHANGE_USER_BATCH_SIZE = 50000;
    /***命令处理队列***/
    private BlockingQueue<BillCmdWrapper> cmdQueue = new ArrayBlockingQueue<>(3000);
    private AccountTypeEnum accountTypeEnum;
    private CommonService commonService;
    private BillEngineManager billEngineManager;
    private BillAllConfigService billAllConfigService;
    private AlarmNotifyService alarmNotifyService;
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    private SerialNoGenerator noGenerator;
    private RedisTemplate<String, Object> reconRedisTemplate;
    private TaskManager userProfitRtTaskManager;
    private TaskManager userBeginAssetsTaskManager;
    private UserBeginAssetsRedisService userBeginAssetsRedisService;
    /***盈利计算时间片窗口***/
    private final ConcurrentSortMap<Long, BillProfitTimeSliceDTO> startTimeSliceDTOMap = new ConcurrentSortMap();
    /***需要实时计算变化用户***/
    private final Map<Long, Long> startTimeSliceChangeUserMap = new ConcurrentHashMap();
    /***需要计算的期初资产变化用户***/
    private static volatile Map<Long, Long> beginAssetsChangeUserMap = new ConcurrentHashMap();

    public BillUserProfitCheckModule(AccountTypeEnum accountTypeEnum, ReconciliationSpringContext reconciliationSpringContext, BillEngineManager billEngineManager) {
        this.accountTypeEnum = accountTypeEnum;
        this.commonService = reconciliationSpringContext.getCommonService();
        this.billEngineManager = billEngineManager;
        this.billAllConfigService = reconciliationSpringContext.getBillAllConfigService();
        this.alarmNotifyService = reconciliationSpringContext.getAlarmNotifyService();
        this.accountAssetsServiceFactory = reconciliationSpringContext.getAccountAssetsServiceFactory();
        this.noGenerator = reconciliationSpringContext.getSerialNoGenerator();
        this.reconRedisTemplate = reconciliationSpringContext.getReconRedisTemplate();
        this.userProfitRtTaskManager = reconciliationSpringContext.getUserProfitRtTaskManager();
        this.userBeginAssetsTaskManager = reconciliationSpringContext.getUserBeginAssetsTaskManager();
        this.userBeginAssetsRedisService = reconciliationSpringContext.getUserBeginAssetsRedisService();
        // 启动消费线程
        new Thread(() -> {
            while (true) {
                takeCommand();
            }
        }, "kafka-consumer-profit-timeslice-" + accountTypeEnum.getCode()).start();
        userBeginAssetsTaskManager.scheduleAtFixedRate(() -> recalculateUserBeginAssets(), 5, 5, TimeUnit.MINUTES);
        // 钩子函数
        Runtime.getRuntime().addShutdownHook(new Thread(() -> recalculateUserBeginAssets()));
    }

    public void offerCommand(BillCmdWrapper cmdWrapper) {
        try {
            this.cmdQueue.put(cmdWrapper);
        } catch (InterruptedException e) {
            String errorMsg = "BillUserProfitCheckModule Command queue is full : " + cmdWrapper.getCommandEnum().getName()
                    + " queue size: " + this.cmdQueue.size() + " logicGroup: " + accountTypeEnum.getCode();
            AlarmUtils.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }
    }

    private BillCmdResult takeCommand() {
        BillCmdWrapper cmdWrapper = null;
        try {
            cmdWrapper = this.cmdQueue.poll(1, TimeUnit.SECONDS);
            if (cmdWrapper == null) {
                return null;
            }
            BillCmdWrapper finalCmdWrapper = cmdWrapper;
            return MetricsUtil.histogram(HISTOGRAM_USER_TAKE_COMMAND + accountTypeEnum.getCode(), () -> execCommand(finalCmdWrapper));
        } catch (Exception e) {
            log.error("BillUserProfitCheckModule check failed with error accountType {} data {} {}", accountTypeEnum.getCode(), cmdWrapper == null ? "" : JSON.toJSONString(cmdWrapper), e);
            alarmNotifyService.alarm(accountTypeEnum.getCode(), USER_PROFIT_MODULE_TAKE_COMMAND_ERROR, accountTypeEnum.getCode());
        }
        return null;
    }

    private BillCmdResult execCommand(BillCmdWrapper cmdWrapper) {
        CommonBillChangeData commonBillChangeData = (CommonBillChangeData) cmdWrapper.getCommandData();
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(commonBillChangeData.getAccountType());
        BizLogUtils.info(commonBillChangeData.getAccountId(), apolloBizConfig.getUserProfitLogUserIds(), "BillUserProfitCheckModule finished process message accountType {} accountUniqueId:{}", commonBillChangeData.getAccountType(), commonBillChangeData.getBizId());
        long timeOffset = TimeSliceCalcUtils.getTimeSlice(commonBillChangeData.getBizTime().getTime(), apolloBizConfig.getMergeTimeSliceSecond());
        BillProfitTimeSliceDTO billProfitTimeSliceDTO = startTimeSliceDTOMap.computeIfAbsent(timeOffset, v -> {
            return new BillProfitTimeSliceDTO(accountTypeEnum, timeOffset);
        });
        recalculateProfit(billProfitTimeSliceDTO, commonBillChangeData);
//        storeBeginAssetsChangeUser(commonBillChangeData);
        return null;
    }

    /**
     * 记录期初资产用户变化用户
     *
     * @param changeData
     */
    private void storeBeginAssetsChangeUser(CommonBillChangeData changeData) {
        beginAssetsChangeUserMap.put(changeData.getAccountId(), changeData.getBizTime().getTime());
    }

    /**
     * 累积计算时间片增量盈利 盈利=资金变动-入出类型
     *
     * @param billProfitTimeSliceDTO
     * @param commonBillChangeData
     */
    private void recalculateProfit(BillProfitTimeSliceDTO billProfitTimeSliceDTO, CommonBillChangeData commonBillChangeData) {
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(commonBillChangeData.getAccountType());
        BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountTypeEnum.getCode());
        BigDecimal changePropSum = billCheckService.getChangePropSumByBillChangeData(commonBillChangeData);

        // 计算 用户-》币种-》交易类型 增量盈利
        Map<String, BigDecimal> bizTypeProfitMap = billProfitTimeSliceDTO.getUserCoinBizTypeProfitMap().computeIfAbsent(commonBillChangeData.getAccountId(), v -> new ConcurrentHashMap<>()).computeIfAbsent(commonBillChangeData.getCoinId(), v -> new ConcurrentHashMap<>());
        BigDecimal lastBizTypeChangePropSum = bizTypeProfitMap.computeIfAbsent(commonBillChangeData.getBizType(), v -> new BigDecimal(0));
        bizTypeProfitMap.put(commonBillChangeData.getBizType(), lastBizTypeChangePropSum.add(changePropSum));

        // 入出流水累积盈利
        if (apolloBizConfig.getCheckProfitInOutBizType().contains(commonBillChangeData.getBizType())) {
            return;
        }
        // 计算用户-》币种 增量盈利
        Map<Integer, BigDecimal> coinProfitMap = billProfitTimeSliceDTO.getUserCoinProfitMap().computeIfAbsent(commonBillChangeData.getAccountId(), v -> new ConcurrentHashMap<>());
        BigDecimal lastCoinChangePropSum = coinProfitMap.computeIfAbsent(commonBillChangeData.getCoinId(), v -> new BigDecimal(0));
        coinProfitMap.put(commonBillChangeData.getCoinId(), lastCoinChangePropSum.add(changePropSum));

        // 放入变化队列触发重算
        startTimeSliceChangeUserMap.put(commonBillChangeData.getAccountId(), commonBillChangeData.getAccountId());
        BizLogUtils.info(commonBillChangeData.getAccountId(), apolloBizConfig.getUserProfitLogUserIds(), "BillUserProfitCheckModule recalculate profit end accountType:{} bizId:{} changePropSum:{} lastChangePropSum:{} msg:{}", accountTypeEnum.getCode(), commonBillChangeData.getBizId(), changePropSum, lastCoinChangePropSum, JSON.toJSONString(commonBillChangeData));
    }

    /**
     * 重算和存储用户盈亏
     */
    public void recalculateAndSaveUserProfit() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("startTimeSliceChangeUserMap.remove start");
        List<Long> changeUserIdList = new ArrayList<>();
        for (Map.Entry<Long, Long> entry : startTimeSliceChangeUserMap.entrySet()) {
            changeUserIdList.add(startTimeSliceChangeUserMap.remove(entry.getKey()));
            if (changeUserIdList.size() >= CHANGE_USER_BATCH_SIZE) {
                break;
            }
        }
        stopWatch.stop();
        if (changeUserIdList.isEmpty()) {
            return;
        }
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        Integer userProfitRecalculateHour = apolloBizConfig.getUserProfitRecalculateHour();
        Date lastCalculateDate = getLastCalculateDate(apolloBizConfig);
        // 按照用户汇总所有时间片盈亏
        stopWatch.start("startTimeSliceDTOMap.entrySet start startTimeSliceChangeUserMap.size:" + startTimeSliceChangeUserMap.size());
        // 用户-》时间片-》币种-》盈亏
        Map<Long, Map<Long, Map<Integer, BigDecimal>>> totalUserCoinProfitMap = new ConcurrentHashMap<>();
        for (Map.Entry<Long, BillProfitTimeSliceDTO> entry : startTimeSliceDTOMap.entrySet()) {
            BillProfitTimeSliceDTO billProfitTimeSliceDTO = entry.getValue();
            Long timeSliceKey = entry.getKey();
            if (billProfitTimeSliceDTO.getTimeSlice() < lastCalculateDate.getTime()) {
                continue;
            }
            for (Long changeUserId : changeUserIdList) {
                Map<Integer, BigDecimal> coinProfitMap = billProfitTimeSliceDTO.getUserCoinProfitMap().getOrDefault(changeUserId, Collections.emptyMap());
                if (MapUtil.isNotEmpty(coinProfitMap)) {
                    totalUserCoinProfitMap.computeIfAbsent(changeUserId, v -> new ConcurrentHashMap<>()).put(timeSliceKey, coinProfitMap);
                }
            }
        }
        stopWatch.stop();

        // 存储到redis
        stopWatch.start("totalUserCoinProfitMap.entrySet push redis");
        // redisKey-》时间片-》币种-》盈亏
        List<Pair<String, Map<Long, Map<Integer, BigDecimal>>>> userProfitList = new ArrayList<>();
        for (Map.Entry<Long, Map<Long, Map<Integer, BigDecimal>>> entry : totalUserCoinProfitMap.entrySet()) {
            Long userId = entry.getKey();
            Map<Long, Map<Integer, BigDecimal>> timeSliceCoinProfitMap = entry.getValue();
            if (MapUtil.isNotEmpty(timeSliceCoinProfitMap)) {
                for (Map.Entry<Long, Map<Integer, BigDecimal>> timeSliceEntry : timeSliceCoinProfitMap.entrySet()) {
                    Map<Integer, BigDecimal> coinProfitMap = timeSliceEntry.getValue();
                    for (Map.Entry<Integer, BigDecimal> coinEntry : coinProfitMap.entrySet()) {
                        coinProfitMap.put(coinEntry.getKey(), coinEntry.getValue().stripTrailingZeros());
                    }
                }
                userProfitList.add(Pair.of(RedisUtil.getBusinessUserProfitIncrement(accountTypeEnum.getCode(), userId), timeSliceCoinProfitMap));
            }
        }
        TaskVoidBatchResult taskResult = userProfitRtTaskManager.forEachSubmitBatchAndWait(userProfitList, (Pair<String, Map<Long, Map<Integer, BigDecimal>>> userProfitPair) -> {
            reconRedisTemplate.opsForValue().set(userProfitPair.getKey(), JSON.toJSONString(userProfitPair.getValue()), userProfitRecalculateHour, TimeUnit.HOURS);
        }, apolloBizConfig.getUserProfitPushRedisConcurrence());
        if ((taskResult != null && taskResult.getFails().size() > 0)) {
            log.error("BillUserProfitCheckModule task failed, accountType:{}", accountTypeEnum.getCode(), this.cmdQueue.size());
        }
        stopWatch.stop();
        log.info("BillUserProfitCheckModule recalculateAndSaveUserProfit end accountType:{} changeUserIdListSize:{} startTimeSliceChangeUserMap:{} startTimeSliceDTOMapSize:{} stopWatch:{}", accountTypeEnum.getCode(), changeUserIdList.size(), startTimeSliceChangeUserMap.size(), startTimeSliceDTOMap.size(), stopWatch.prettyPrint());
    }

    /**
     * 清理 实时计算用户盈亏
     */
    public void clearMemoryUserProfit() {
        BillAllConfig billAllConfig = billAllConfigService.selectByTypeAndParam(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam());
        if (billAllConfig == null) {
            return;
        }
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        Date lastCalculateDate = getLastCalculateDate(apolloBizConfig);
        for (Map.Entry<Long, BillProfitTimeSliceDTO> entry : startTimeSliceDTOMap.entrySet()) {
            BillProfitTimeSliceDTO billProfitTimeSliceDTO = entry.getValue();
            if (billProfitTimeSliceDTO.getTimeSlice() < billAllConfig.getCheckOkTime().getTime() && billProfitTimeSliceDTO.getTimeSlice() < lastCalculateDate.getTime()) {
                startTimeSliceDTOMap.remove(entry.getKey());
                log.info("BillUserProfitCheckModule clearMemoryUserProfit accountType:{} remove timeSlice:{} checkTime:{}", accountTypeEnum.getCode(), DateUtil.date2str(new Date(entry.getKey())), DateUtil.date2str(billAllConfig.getCheckOkTime()));
            } else {
                break;
            }
        }
        log.info("BillUserProfitCheckModule clearMemoryUserProfit end accountType:{} checkTime:{} lastCalculateDate:{}", accountTypeEnum.getCode(), DateUtil.date2str(billAllConfig.getCheckOkTime()), DateUtil.date2str(lastCalculateDate));
    }

    /**
     * 获取内存重算时间
     *
     * @return
     */
    private Date getLastCalculateDate(ApolloReconciliationBizConfig apolloBizConfig) {
        Integer userProfitRecalculateHour = apolloBizConfig.getUserProfitRecalculateHour();
        Date nowDate = new Date();
        Date lastCalculateDate = DateUtil.addHour(nowDate, -userProfitRecalculateHour);
        return lastCalculateDate;
    }

    public void recalculateUserProfit(Long userId, Long startTime, Long endTime) {
        // 打印当前redis 盈亏数据
        String userProfitRedisKey = RedisUtil.getBusinessUserProfitIncrement(accountTypeEnum.getCode(), userId);
        String userProfitRedisValue = (String) reconRedisTemplate.opsForValue().get(userProfitRedisKey);
        log.info("BillUserProfitCheckModule recalculateUserProfit start accountType:{} userId:{} startTime:{} endTime:{} userProfitRedisKey:{} userProfitRedisValue:{}", accountTypeEnum.getCode(), userId, DateUtil.date2str(new Date(startTime)), DateUtil.date2str(new Date(endTime)), userProfitRedisKey, userProfitRedisValue);

        // 币种-》盈亏
        Map<Integer, BigDecimal> preTotalFinalCoinProfitMap = new HashMap<>();
        // 币种-》业务类型-》盈亏  累加所有时间片盈亏
        Map<Integer, Map<String, BigDecimal>> totalCoinBizTypeProfitMap = new HashMap<>();
        for (Map.Entry<Long, BillProfitTimeSliceDTO> entry : startTimeSliceDTOMap.entrySet()) {
            BillProfitTimeSliceDTO billProfitTimeSliceDTO = entry.getValue();
            Long timeSliceKey = entry.getKey();
            if (timeSliceKey < startTime || timeSliceKey > endTime) {
                continue;
            }
            // 合并预计算币种-》盈亏
            Map<Integer, BigDecimal> memoryCoinProfitMap = billProfitTimeSliceDTO.getUserCoinProfitMap().getOrDefault(userId, Collections.emptyMap());
            for (Map.Entry<Integer, BigDecimal> memoryCoinProfitMapEntry : memoryCoinProfitMap.entrySet()) {
                Integer coinId = memoryCoinProfitMapEntry.getKey();
                BigDecimal profit = memoryCoinProfitMapEntry.getValue();
                preTotalFinalCoinProfitMap.put(coinId, preTotalFinalCoinProfitMap.getOrDefault(coinId, BigDecimal.ZERO).add(profit));
            }
            // 合并计算 币种-》交易类型-》盈亏
            Map<Integer, Map<String, BigDecimal>> memoryCoinBizTypeProfitMap = billProfitTimeSliceDTO.getUserCoinBizTypeProfitMap().getOrDefault(userId, Collections.emptyMap());
            for (Map.Entry<Integer, Map<String, BigDecimal>> memoryCoinBizTypeProfitMapEntry : memoryCoinBizTypeProfitMap.entrySet()) {
                Integer coinId = memoryCoinBizTypeProfitMapEntry.getKey();
                Map<String, BigDecimal> memoryBizTypeProfitMap = memoryCoinBizTypeProfitMapEntry.getValue();
                for (Map.Entry<String, BigDecimal> memoryBizTypeProfitMapEntry : memoryBizTypeProfitMap.entrySet()) {
                    String bizType = memoryBizTypeProfitMapEntry.getKey();
                    BigDecimal profit = memoryBizTypeProfitMapEntry.getValue();
                    // 累积用户-》币种-》交易类型-》盈亏
                    Map<String, BigDecimal> totalBizTypeProfitMap = totalCoinBizTypeProfitMap.computeIfAbsent(coinId, v -> new HashMap<>());
                    totalBizTypeProfitMap.put(bizType, totalBizTypeProfitMap.getOrDefault(bizType, BigDecimal.ZERO).add(profit));
                }
            }
        }

        // 合并计算币种盈亏
        Map<Integer, BigDecimal> totalCoinProfitMap = new HashMap<>();
        for (Map.Entry<Integer, Map<String, BigDecimal>> totalCoinBizTypeProfitMapEntry : totalCoinBizTypeProfitMap.entrySet()) {
            Integer coinId = totalCoinBizTypeProfitMapEntry.getKey();
            Map<String, BigDecimal> totalBizTypeProfitMap = totalCoinBizTypeProfitMapEntry.getValue();
            // 计算币种盈亏
            BigDecimal totalProfit = totalBizTypeProfitMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            totalCoinProfitMap.put(coinId, totalProfit);
        }

        // 合并计算入出盈亏
        Map<Integer, BigDecimal> totalInOutCoinProfitMap = new HashMap<>();
        for (Map.Entry<Integer, Map<String, BigDecimal>> totalCoinBizTypeProfitMapEntry : totalCoinBizTypeProfitMap.entrySet()) {
            Integer coinId = totalCoinBizTypeProfitMapEntry.getKey();
            Map<String, BigDecimal> totalBizTypeProfitMap = totalCoinBizTypeProfitMapEntry.getValue();
            // 计算币种盈亏
            ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
            for (Map.Entry<String, BigDecimal> totalBizTypeProfitMapEntry : totalBizTypeProfitMap.entrySet()) {
                String bizType = totalBizTypeProfitMapEntry.getKey();
                BigDecimal profit = totalBizTypeProfitMapEntry.getValue();
                if (apolloBizConfig.getCheckProfitInOutBizType().contains(bizType)) {
                    totalInOutCoinProfitMap.put(coinId, totalInOutCoinProfitMap.getOrDefault(coinId, BigDecimal.ZERO).add(profit));
                }
            }
        }

        // 合并计算最终盈亏
        Map<Integer, BigDecimal> totalFinalCoinProfitMap = new HashMap<>();
        for (Map.Entry<Integer, BigDecimal> totalCoinProfitMapEntry : totalCoinProfitMap.entrySet()) {
            Integer coinId = totalCoinProfitMapEntry.getKey();
            BigDecimal totalProfit = totalCoinProfitMapEntry.getValue();
            BigDecimal inOutProfit = totalInOutCoinProfitMap.getOrDefault(coinId, new BigDecimal(0));
            totalFinalCoinProfitMap.put(coinId, totalProfit.subtract(inOutProfit));
        }

        // 计算折U总盈亏
        Map<Integer, PriceVo> rates = commonService.getCoinIdTradePriceMap(endTime);
        Map<Integer, BigDecimal> priceMap = new HashMap<>();
        Map<Integer, BigDecimal> coinUsdtProfitMap = new HashMap<>();
        BigDecimal totalProfit = BigDecimal.ZERO;
        for (Map.Entry<Integer, BigDecimal> totalFinalCoinProfitMapEntry : totalFinalCoinProfitMap.entrySet()) {
            Integer coinId = totalFinalCoinProfitMapEntry.getKey();
            BigDecimal profit = totalFinalCoinProfitMapEntry.getValue();
            BigDecimal price = NumberUtil.isNullDefaultZero(rates.get(coinId).getPrice());
            BigDecimal usdtProfit = profit.multiply(price);
            coinUsdtProfitMap.put(coinId, usdtProfit);
            priceMap.put(coinId, price);
            totalProfit = totalProfit.add(usdtProfit);
        }

        // 时间片信息
        Date nowDate = new Date();
        Long firstKey = startTimeSliceDTOMap.firstKey(nowDate.getTime());
        Long lastKey = startTimeSliceDTOMap.lastKey(nowDate.getTime());
        int startTimeSliceSize = startTimeSliceDTOMap.size();
        log.info("BillUserProfitCheckModule recalculateUserProfit end accountType:{} startTimeSliceFirstKey:{} startTimeSliceLastKey:{} startTimeSliceSize:{} userId:{} startTime:{} endTime:{} totalProfit:{} coinUsdtProfitMap:{} totalCoinBizTypeProfitMap:{} totalCoinProfitMap:{} totalInOutCoinProfitMap:{} totalFinalCoinProfitMap:{} preTotalFinalCoinProfitMap:{} priceMap:{}", accountTypeEnum.getCode(), DateUtil.date2str(new Date(firstKey)), DateUtil.date2str(new Date(lastKey)), startTimeSliceSize, userId, DateUtil.date2str(new Date(startTime)), DateUtil.date2str(new Date(endTime)), totalProfit, JSON.toJSONString(coinUsdtProfitMap), JSON.toJSONString(totalCoinBizTypeProfitMap), JSON.toJSONString(totalCoinProfitMap), JSON.toJSONString(totalInOutCoinProfitMap), JSON.toJSONString(totalFinalCoinProfitMap), JSON.toJSONString(preTotalFinalCoinProfitMap), JSON.toJSONString(priceMap));
    }

    /**
     * 计算用户币种+业务类型盈利
     *
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    public Map<Integer, Map<String, BigDecimal>> recalculateUserCoinBizTypeProfit(Long userId, Long startTime, Long endTime) {
        // 币种-》业务类型-》盈亏  累加所有时间片盈亏
        Map<Integer, Map<String, BigDecimal>> totalCoinBizTypeProfitMap = new HashMap<>();
        for (Map.Entry<Long, BillProfitTimeSliceDTO> entry : startTimeSliceDTOMap.entrySet()) {
            BillProfitTimeSliceDTO billProfitTimeSliceDTO = entry.getValue();
            Long timeSliceKey = entry.getKey();
            if (timeSliceKey < startTime || timeSliceKey > endTime) {
                continue;
            }
            // 合并计算 币种-》交易类型-》盈亏
            ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
            Map<Integer, Map<String, BigDecimal>> memoryCoinBizTypeProfitMap = billProfitTimeSliceDTO.getUserCoinBizTypeProfitMap().getOrDefault(userId, Collections.emptyMap());
            for (Map.Entry<Integer, Map<String, BigDecimal>> memoryCoinBizTypeProfitMapEntry : memoryCoinBizTypeProfitMap.entrySet()) {
                Integer coinId = memoryCoinBizTypeProfitMapEntry.getKey();
                for (Map.Entry<String, BigDecimal> memoryBizTypeProfitMapEntry : memoryCoinBizTypeProfitMapEntry.getValue().entrySet()) {
                    String bizType = memoryBizTypeProfitMapEntry.getKey();
                    BigDecimal profit = memoryBizTypeProfitMapEntry.getValue();
                    // 入出不算盈利
                    if (!apolloBizConfig.getCheckProfitInOutBizType().contains(bizType) && profit.compareTo(BigDecimal.ZERO) != 0) {
                        totalCoinBizTypeProfitMap.computeIfAbsent(coinId, v -> new HashMap<>()).compute(bizType, (k, oldValue) -> oldValue == null ? profit : oldValue.add(profit));
                    }
                }
            }
        }

        // 时间片信息
        Date nowDate = new Date();
        Long firstKey = startTimeSliceDTOMap.firstKey(nowDate.getTime());
        Long lastKey = startTimeSliceDTOMap.lastKey(nowDate.getTime());
        int startTimeSliceSize = startTimeSliceDTOMap.size();
        log.info("BillUserProfitCheckModule recalculateUserProfit end accountType:{} startTimeSliceFirstKey:{} startTimeSliceLastKey:{} startTimeSliceSize:{} userId:{} startTime:{} endTime:{} totalCoinBizTypeProfitMap:{}", accountTypeEnum.getCode(), DateUtil.date2str(new Date(firstKey)), DateUtil.date2str(new Date(lastKey)), startTimeSliceSize, userId, DateUtil.date2str(new Date(startTime)), DateUtil.date2str(new Date(endTime)), JSON.toJSONString(totalCoinBizTypeProfitMap));

        return totalCoinBizTypeProfitMap;
    }

    /**
     * 期初资产计算
     */
    private void recalculateUserBeginAssets() {
        if (CollectionUtil.isEmpty(beginAssetsChangeUserMap)) {
            return;
        }
        Map<Long, Long> beginAssetsUserMap = beginAssetsChangeUserMap;
        beginAssetsChangeUserMap = new ConcurrentHashMap<>();
        beginAssetsUserMap.forEach((userId, timestamp) -> userBeginAssetsRedisService.addUserBeginAssets(userId, timestamp));
    }
}