package com.upex.reconciliation.service.common.constants;

/**
 * @Author: allen
 * @Date: 2020-05-14 19:49
 * @DES: 账单类型
 */
public final class BillBizTypeConstant {

    /**
     * 资产表数据归集到流水记的类型
     */
    public static final String BILL_BALANCE_TYPE = "other";

    /**
     * 总账用户ID
     */
    public static final Long BILL_TOTAL_USER_ID = -1L;

    /**
     * bill coin user
     */
    public static final int BILL_COIN_USER = 1;

    /**
     * bill coin type user
     */
    public static final int BILL_COIN_TYPE_USER = 2;

    /**
     * 流水扩展类型
     */
    public static final String BILL_EXTENSION_TYPE = "bill_extension_type";
    /**
     * 流水类型：内部划转(平仓或资金费用结算时不同币种间的互换)换汇类型
     */
    public static final String BILL_INNER_TRANS_MIDDLE_EXCHANGE = "inner_trans_middle_exchange";


    /**
     * 扩展类型SID
     */
    public static final String BILL_EXTENSION_SID = "markPrice";



}
