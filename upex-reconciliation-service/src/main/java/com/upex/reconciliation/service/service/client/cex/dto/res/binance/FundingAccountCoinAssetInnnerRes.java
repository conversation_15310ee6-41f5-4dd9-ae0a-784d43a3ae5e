package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@NoArgsConstructor
@Data
public class FundingAccountCoinAssetInnnerRes {
    /**
     * 币种
     */
    private String asset;
    /**
     * 可用余额
     */
    private BigDecimal free;
    /**
     * 锁定余额
     */
    private BigDecimal locked;
    /**
     * 冻结金额
     */
    private BigDecimal freeze;

    private BigDecimal withdrawing;

}
