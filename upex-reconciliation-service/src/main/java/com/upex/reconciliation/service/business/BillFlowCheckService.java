package com.upex.reconciliation.service.business;

import com.upex.reconciliation.service.dao.entity.BillFlowCheckConfig;
import com.upex.reconciliation.service.model.config.UserPermissionConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * bill流水检测服务
 * 
 * <AUTHOR>
 * @Date 2024/12/25
 */
public interface BillFlowCheckService {

    /**
     * 业务类型出入存储或者累加
     *
     * @param commonBillChangeData
     */
    BigDecimal storeOrSumInOut(CommonBillChangeData commonBillChangeData);

    /**
     * 根据业务线获取业务类型出入集合
     *
     * @param accountType
     * @return
     */
    Set<String> getBizTypeInOutSet(byte accountType);

    /**
     * 根据业务线及业务类型获取权限code
     *
     * @param accountType
     * @return
     */
    List<UserPermissionConfig> getUserPermissionCode(byte accountType, String bizType);

    /**
     * 根据业务线及业务类型获取子账户配置
     *
     * @param accountType
     * @return
     */
    BillFlowCheckConfig getSubAccount(byte accountType, String bizType);
}
