package com.upex.reconciliation.service.business.createtablebyroute;

import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.model.config.ReconTableRouteConfig;
import com.upex.reconciliation.service.service.BillUserPositionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
public class BillUserPositionTableCreator extends AbstractTableCreator {
    @Resource
    private BillUserPositionService billUserPositionService;

    @Override
    public String getTableType() {
        return "billUserPosition";
    }

    @Override
    public void createTable(String accountType) {
        ReconTableRouteConfig.TableRouteRule tableRouteRule = getTableRouteRule(accountType);
        if (tableRouteRule == null) {
            return;
        }
        if (ReconTableRouteConfig.TableRouteRuleEnum.MONTH.name().equalsIgnoreCase(tableRouteRule.getRule())) {
            throw new RuntimeException(getTableType() + "不支持创建表类型" + tableRouteRule.getRule());
        }
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(Byte.valueOf(accountType));
        billUserPositionService.createTableForDay(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), Objects.requireNonNullElse(tableRouteRule.getCreateDay(), 7));
    }
}
