package com.upex.reconciliation.service.service.client.cex.dto.req.binance;

import com.binance.connector.client.common.Pair;
import com.binance.connector.client.common.auth.Authentication;
import com.upex.reconciliation.service.service.client.cex.dto.req.CexApiBaseReq;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

@NoArgsConstructor
@Data
public class BinanceApiBaseReq implements CexApiBaseReq {

    List<Pair> queryParams= new ArrayList<>(0);

    List<Pair> collectionQueryParams= new ArrayList<>(0);

    Map<String,String> cookieParams=new HashMap<>(0);

    Map<String,Object> formParams=new HashMap<>(0);

    private Object body;
    Map<String, String> headerParams= new HashMap<>(){
        {
            put("Content-Type", "application/x-www-form-urlencoded");
            put("Accept","application/json");
        }
    };

    Map<String, Authentication> authentications=new HashMap<>(){
        {
            Authentication timeUnit =
                    (queryParams, headerParams, cookieParams, payload, method, uri) -> {

                    };
            put("timeUnit", timeUnit);
        }
    };

    String[] authNames = new String[]{"timeUnit"};



}
