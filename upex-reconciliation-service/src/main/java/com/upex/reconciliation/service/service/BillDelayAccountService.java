package com.upex.reconciliation.service.service;


import com.upex.reconciliation.service.common.constants.enums.ActiveFlagEnum;
import com.upex.reconciliation.service.dao.entity.BillDelayAccount;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;

import java.util.Date;
import java.util.List;

/**
 * 用户延迟入账数据业务
 *
 * <AUTHOR>
 */
public interface BillDelayAccountService {
    /**
     * 批量插入数据
     *
     * @param list
     */
    void batchInsert(List<BillDelayAccount> list);

    void saveDelayAccount(CommonBillChangeData commonBillChangeData, ApolloReconciliationBizConfig apolloBizConfig);

    /**
     * 查询历史有延迟入账的数据
     *
     * @return
     */
    List<BillDelayAccount> selectHistoryDelayAccountList(Long userId, Integer activeFlag);

    /**
     * 更新状态
     *
     * @param billDelayAccount
     * @return
     */
    int updateDelayAccount(BillDelayAccount billDelayAccount);

    int updateDelayAccountByTime(Date checkOkDate, ActiveFlagEnum activeFlagEnum);

    /**
     * 删除数据
     *
     * @param accountType
     * @param accountParam
     * @param checkTime
     * @param batchSize
     * @return
     */
    Boolean deleteByLtCheckTime(Byte accountType, String accountParam, Date checkTime, Long batchSize);
}
