package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.mixcontract.common.literal.enums.BusinessTypeEnum;
import com.upex.mixcontract.common.literal.enums.SecondBusinessLineEnum;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AbstractBillCheckService;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.PropEnum;
import com.upex.reconciliation.service.common.constants.enums.SymbolCoinPropEnum;
import com.upex.reconciliation.service.common.constants.enums.SymbolPropEnum;
import com.upex.reconciliation.service.common.constants.enums.TransferFeeTypeEnum;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.alarm.CheckResult;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.*;
import com.upex.reconciliation.service.service.ReconSystemAccountService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.NumberUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.ticker.facade.dto.PriceVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Supplier;


@Service
@Slf4j
public class UtaBillCheckServiceImpl extends AbstractBillCheckService {
    @Resource
    private AlarmNotifyService alarmNotifyService;
    @Resource
    private ReconSystemAccountService reconSystemAccountService;
    /***BGB抵扣类型***/
    private static final List<String> BGB_RECEIVABLE_FEE_TYPE_LIST = Lists.newArrayList("13");
    /***业务线入账手续费类型***/
    private static final List<String> RECEIVED_BIZ_TRANSFER_FEE_TYPE_LIST = Lists.newArrayList("14");
    /***合约symbol维度prop***/
    private static final Map<SymbolPropEnum, PropEnum> SYMBOL_PROP_ENUM_MAP = new HashMap<>() {{
        put(SymbolPropEnum.RE_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_PROP_100_1.getPropCode()));
        put(SymbolPropEnum.RE_UN_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_PROP_100_2.getPropCode()));
        put(SymbolPropEnum.L_COUNT, PropEnum.toEnum(PropEnum.SYMBOL_PROP_100_3.getPropCode()));
        put(SymbolPropEnum.S_COUNT, PropEnum.toEnum(PropEnum.SYMBOL_PROP_100_4.getPropCode()));
        put(SymbolPropEnum.UN_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_PROP_100_5.getPropCode()));
        put(SymbolPropEnum.MARGIN_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_PROP_100_6.getPropCode()));
        put(SymbolPropEnum.REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_PROP_100_7.getPropCode()));
        put(SymbolPropEnum.INIT_VALUE, PropEnum.toEnum(PropEnum.SYMBOL_PROP_100_8.getPropCode()));
    }};
    /***合约symbolCoin维度prop***/
    private static final Map<SymbolCoinPropEnum, PropEnum> SYMBOL_COIN_PROP_ENUM_MAP = new HashMap<>() {{
        put(SymbolCoinPropEnum.MARGIN_UN_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_COIN_PROP_100_5.getPropCode()));
        put(SymbolCoinPropEnum.MARGIN_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_COIN_PROP_100_6.getPropCode()));
        put(SymbolCoinPropEnum.REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_COIN_PROP_100_7.getPropCode()));
        put(SymbolCoinPropEnum.RE_REALIZED, PropEnum.toEnum(PropEnum.SYMBOL_COIN_PROP_100_8.getPropCode()));
    }};

    @Override
    public Byte getAccountType() {
        return AccountTypeEnum.UNIFIED.getCode();
    }

    @Override
    public boolean checkBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty) {
        if (commonBillChangeData.getProp2to5Sum().subtract(commonBillChangeData.getChangeProp2to5Sum()).compareTo(billCoinUserProperty.getProp2to5Sum()) == 0) {
            return true;
        }
        return false;
    }

    @Override
    public boolean checkCurrentBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty) {
        if (commonBillChangeData.getProp2to5Sum().subtract(commonBillChangeData.getChangeProp2to5Sum()).compareTo(billCoinUserProperty.getProp2to5Sum()) == 0) {
            return true;
        }
        return false;
    }

    @Override
    public BigDecimal getPropSumByUserProperty(BillCoinUserProperty coinUserProperty) {
        return NumberUtil.add(coinUserProperty.getProp2(), coinUserProperty.getProp3());
    }

    @Override
    public CheckResult doCheckProperty(ApolloReconciliationBizConfig apolloBizConfig, String accountUniqueId, Long userId, Integer coinId, List<CommonBillChangeData> billChangeDataList, BillCoinUserProperty billCoinUserProperty) {
        CommonBillChangeData lastChange = billChangeDataList.get(billChangeDataList.size() - 1);
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(apolloBizConfig.getAccountType());
        BigDecimal totalChangeProp1 = BigDecimal.ZERO;
        BigDecimal totalChangeProp2 = BigDecimal.ZERO;
        BigDecimal totalChangeProp3 = BigDecimal.ZERO;
        BigDecimal totalChangeProp4 = BigDecimal.ZERO;
        BigDecimal totalChangeProp5 = BigDecimal.ZERO;
        for (CommonBillChangeData bill : billChangeDataList) {
            totalChangeProp1 = totalChangeProp1.add(bill.getChangeProp1());
            totalChangeProp2 = totalChangeProp2.add(bill.getChangeProp2());
            totalChangeProp3 = totalChangeProp3.add(bill.getChangeProp3());
            totalChangeProp4 = totalChangeProp4.add(bill.getChangeProp4());
            totalChangeProp5 = totalChangeProp5.add(bill.getChangeProp5());
        }
        if (lastChange.getProp2to5Sum().subtract(lastChange.getChangeProp2to5Sum()).compareTo(billCoinUserProperty.getProp2to5Sum()) != 0) {
            log.error("UtaBillCheckServiceImpl.checkBillCoinProperty accountType {} accountUniqueId :{}  lastChange:{},billCoinUserProperty:{},totalChangeProp1:{},totalChangeProp2:{},totalChangeProp3:{},totalChangeProp4:{},totalChangeProp5:{} totalChangeProp2to5Sum:{}"
                    , getAccountType(), accountUniqueId, JSONObject.toJSONString(lastChange)
                    , JSONObject.toJSONString(billCoinUserProperty), totalChangeProp1.toPlainString(), totalChangeProp2.toPlainString(), totalChangeProp3.toPlainString(), totalChangeProp4.toPlainString(), totalChangeProp5.toPlainString(), lastChange.getProp2to5Sum().toPlainString());
            return CheckResult.fail(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), lastChange.getBizTime())
                    .addUserPropCheckData(lastChange.getAccountId(), lastChange.getCoinId(), billCoinUserProperty.getProp2to5Sum(), lastChange.getChangeProp2to5Sum(), lastChange.getProp2to5Sum(), lastChange.getBizId(), "prop2to5Sum");
        }
        return CheckResult.DEFAULT_SUCCESS;
    }

    @Override
    protected UserAssetsNegativeModel doCheckNegative(BillCoinUserProperty billCoinUserProperty, BigDecimal unRealized) {
        if (billCoinUserProperty.getProp2().add(billCoinUserProperty.getProp3()).compareTo(BigDecimal.ZERO) < 0) {
            UserAssetsNegativeModel userAssetsNegativeModel = UserAssetsNegativeModel.builder()
                    .propValue(billCoinUserProperty.getPropSum())
                    .build();
            return userAssetsNegativeModel;
        }
        return null;
    }

    @Override
    public <T extends AbstractProperty> boolean checkAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult, ApolloReconciliationBizConfig apolloBillConfig, Map<Integer, PriceVo> ratesToUSDTCoinIdMap, Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap) {
        if (property == null || totalAccountAssetsInfoResult == null) {
            return false;
        }
        BigDecimal propertySum = property.getProp2().add(property.getProp3());
        BigDecimal resultSum = totalAccountAssetsInfoResult.getProp2().add(totalAccountAssetsInfoResult.getProp3());
        if (propertySum.compareTo(resultSum) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractSProperty> boolean checkSpropAssets(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult, ApolloReconciliationBizConfig apolloBillConfig, Map<Integer, PriceVo> ratesToUSDTCoinIdMap, Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap) {
        if (property == null || totalAccountAssetsInfoResult == null) {
            return false;
        }
        BigDecimal propertySum = property.getSprop2().add(property.getSprop3());
        BigDecimal resultSum = totalAccountAssetsInfoResult.getProp2().add(totalAccountAssetsInfoResult.getProp3());
        if (propertySum.compareTo(resultSum) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public BigDecimal getPropSumByProperty(AbstractProperty billCoinProperty) {
        return billCoinProperty == null ? BigDecimal.ZERO : billCoinProperty.getProp2().add(billCoinProperty.getProp3());
    }

    @Override
    public BigDecimal getPropByTransferFee(AbstractProperty abstractProperty) {
        return abstractProperty != null ? abstractProperty.getProp2() : BigDecimal.ZERO;
    }

    @Override
    public BigDecimal getChangePropSumByProperty(AbstractProperty abstractProperty) {
        return NumberUtil.add(abstractProperty.getChangeProp2(), abstractProperty.getChangeProp3());
    }

    @Override
    public BigDecimal getPropSumByBillChangeData(CommonBillChangeData currentBill) {
        return NumberUtil.add(currentBill.getProp2(), currentBill.getProp3());
    }

    @Override
    public BigDecimal getChangePropSumByBillChangeData(CommonBillChangeData currentBill) {
        return NumberUtil.add(currentBill.getChangeProp2(), currentBill.getChangeProp3());
    }

    @Override
    public BigDecimal getChangePropSumByAbstractProperty(AbstractProperty abstractProperty) {
        return NumberUtil.add(abstractProperty.getChangeProp2(), abstractProperty.getChangeProp3());
    }

    /**
     * 获取业务线类型
     *
     * @return
     */
    @Override
    public List<String> getBusinessType() {
        return Arrays.asList(AccountTypeEnum.UNIFIED.getBizTypePrefix());
    }

    @Override
    public <T extends AbstractProperty> boolean checkPosition(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult) {
        return false;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitAssets(T oldProperty, BillCoinUserProperty newBillCoinUserProperty) {
        if (oldProperty == null || newBillCoinUserProperty == null) {
            return false;
        }
        if (oldProperty.getProp2Prop3Sum().compareTo(newBillCoinUserProperty.getProp2Prop3Sum()) != 0) {
            return false;
        }
        if (oldProperty.getProp4().compareTo(newBillCoinUserProperty.getProp4()) != 0) {
            return false;
        }
        if (oldProperty.getProp5().compareTo(newBillCoinUserProperty.getProp5()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinAssets(T oldProperty, BillCoinProperty billCoinProperty) {
        if (oldProperty == null || billCoinProperty == null) {
            return false;
        }
        if (oldProperty.getProp2Prop3Sum().compareTo(billCoinProperty.getProp2Prop3Sum()) != 0) {
            return false;
        }
        if (oldProperty.getProp4().compareTo(billCoinProperty.getProp4()) != 0) {
            return false;
        }
        if (oldProperty.getProp5().compareTo(billCoinProperty.getProp5()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeAssets(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        if (oldProperty == null || newBillCoinTypeProperty == null) {
            return false;
        }
        if (oldProperty.getProp2Prop3Sum().compareTo(newBillCoinTypeProperty.getProp2Prop3Sum()) != 0) {
            return false;
        }
        if (oldProperty.getProp4().compareTo(newBillCoinTypeProperty.getProp4()) != 0) {
            return false;
        }
        if (oldProperty.getProp5().compareTo(newBillCoinTypeProperty.getProp5()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinChangeAssets(T oldProperty, BillCoinProperty billCoinProperty) {
        if (oldProperty == null || billCoinProperty == null) {
            return false;
        }
        if (oldProperty.getChangeProp2().add(oldProperty.getChangeProp3()).compareTo(billCoinProperty.getChangeProp2().add(billCoinProperty.getChangeProp3())) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp4().compareTo(billCoinProperty.getChangeProp4()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp5().compareTo(billCoinProperty.getChangeProp5()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkInitCoinTypeChangeAssets(T oldProperty, BillCoinTypeProperty billCoinTypeProperty) {
        if (oldProperty == null || billCoinTypeProperty == null) {
            return false;
        }
        if (oldProperty.getProp2Prop3Sum().compareTo(billCoinTypeProperty.getProp2Prop3Sum()) != 0) {
            return false;
        }
        if (oldProperty.getProp4().compareTo(billCoinTypeProperty.getProp4()) != 0) {
            return false;
        }
        if (oldProperty.getProp5().compareTo(billCoinTypeProperty.getProp5()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinAssetsByAccountType(T oldProperty, BillCoinProperty newBillCoinUserProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        if (oldProperty != null) {
            dataCalResultDTO.setFactor1(oldProperty.getProp2().add(oldProperty.getProp3()));
        }
        if (newBillCoinUserProperty != null) {
            dataCalResultDTO.setFactor2(newBillCoinUserProperty.getProp2().add(newBillCoinUserProperty.getProp3()));
        }
        BigDecimal tolerance = coinComparisonToleranceMap.getOrDefault(newBillCoinUserProperty.getCoinId(), BigDecimal.ZERO);
        dataCalResultDTO.setResult(dataCalResultDTO.getFactor1().subtract(dataCalResultDTO.getFactor2()).abs());
        dataCalResultDTO.setPass(dataCalResultDTO.getResult().compareTo(tolerance) <= 0);
        return dataCalResultDTO.isPass();
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeInitAssetsByAccountType(List<T> oldPropertyList, BillCoinTypeProperty newBillCoinTypeProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO) {
        if (CollectionUtils.isEmpty(oldPropertyList) || newBillCoinTypeProperty == null) {
            return false;
        }
        BigDecimal tolerance = coinComparisonToleranceMap.getOrDefault(newBillCoinTypeProperty.getCoinId(), BigDecimal.ZERO);
        BigDecimal oldSum = BigDecimal.ZERO;
        for (T odlProperty : oldPropertyList) {
            oldSum = oldSum.add(odlProperty.getProp2()).add(odlProperty.getProp3());
        }
        BigDecimal newSum = newBillCoinTypeProperty.getProp2().add(newBillCoinTypeProperty.getProp3());
        dataCalResultDTO.setFactor1(oldSum);
        dataCalResultDTO.setFactor2(newSum);
        dataCalResultDTO.setResult(oldSum.subtract(newSum).abs());
        if (oldSum.subtract(newSum).abs().compareTo(tolerance) > 0) {
            log.info("CheckAssetSpotServiceImpl checkCoinTypeAssetsByAccountType failed , old sum {}, new sum {} , property {}", oldSum, newSum, JSONObject.toJSONString(newBillCoinTypeProperty));
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> boolean checkCoinTypeAssetsByAccountType(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty) {
        if (oldProperty == null) {
            return Objects.isNull(newBillCoinTypeProperty) || newBillCoinTypeProperty.getChangePropSum().compareTo(BigDecimal.ZERO) == 0;
        }
        if (oldProperty.getChangeProp2().add(oldProperty.getChangeProp3()).compareTo(newBillCoinTypeProperty.getChangeProp2().add(newBillCoinTypeProperty.getChangeProp3())) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp4().compareTo(newBillCoinTypeProperty.getChangeProp4()) != 0) {
            return false;
        }
        if (oldProperty.getChangeProp5().compareTo(newBillCoinTypeProperty.getChangeProp5()) != 0) {
            return false;
        }
        return true;
    }

    @Override
    public <T extends AbstractProperty> BigDecimal sumForInAll(T property) {
        if (property == null) {
            return BigDecimal.ZERO;
        }
        return property.getProp2().add(property.getProp3());
    }

    @Override
    public BigDecimal getExchangeDataChangeAssets(CommonBillChangeData commonBillChangeData) {
        return commonBillChangeData.getChangeProp6();
    }

    @Override
    public AssetsBillCoinTypeProperty convertToAssetsBillCoinTypeProperty(Byte accountType, BillCoinTypeProperty billCoinTypeProperty) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        String assetsBizType = accountTypeEnum.getBizTypePrefix() + BillConstants.SEPARATOR + billCoinTypeProperty.getBizType();
        AssetsBillCoinTypeProperty assetsBillCoinTypeProperty = new AssetsBillCoinTypeProperty();
        assetsBillCoinTypeProperty.setCoinId(billCoinTypeProperty.getCoinId());
        assetsBillCoinTypeProperty.setBizType(assetsBizType);
        assetsBillCoinTypeProperty.setChangeProp2(billCoinTypeProperty.getChangeProp2());
        assetsBillCoinTypeProperty.setChangeProp3(billCoinTypeProperty.getChangeProp3());
        return assetsBillCoinTypeProperty;
    }

    @Override
    public BigDecimal getQuoteTokenAssetsByProperty(AbstractProperty abstractProperty) {
        return abstractProperty.getProp7();
    }

    @Override
    public void setInitAndUnRealizedProp(AbstractProperty billSymbolProperty, BigDecimal initValue, BigDecimal unRealized) {
        billSymbolProperty.setProp5(unRealized);
        billSymbolProperty.setProp8(initValue);
    }

    @Override
    public void setSCountLCountProp(AbstractProperty billSymbolProperty, BigDecimal sCount, BigDecimal lCount) {
        billSymbolProperty.setProp3(lCount);
        billSymbolProperty.setProp4(sCount);
    }

    @Override
    public BigDecimal getQuoteTokenChangeAssets(AbstractProperty abstractProperty) {
        return abstractProperty.getChangeProp7();
    }

    @Override
    public BigDecimal getMarginRealizedProp(AbstractProperty abstractProperty) {
        return abstractProperty.getProp6();
    }

    @Override
    public BigDecimal getMarginRealizedChangeProp(AbstractProperty abstractProperty) {
        return abstractProperty.getChangeProp6();
    }

    /**
     * 系统用户手续费入账bizType（需要根据second_business_line 区分现货、合约），上线切换时：入账需要剔除14业务类型系统已入账金额（系统待入账金额=当前时段累计待入账金额-系统已入账金额）
     * 除 13  其余bizType 使用fee 进行统计，按tb_financial_record.second_business_line 区分现货、合约业务类型入账（系统账户不同）
     * <p>
     * 13   bgb抵扣 当期应收 总应收
     * 其他bizType  当期应收 总应收
     * 14 业务系统入账 当期实收 总实收
     * 82/83/92/93 对账系统入账 当期实收 总实收
     * <p>
     * 当期手续费： bgb当期+其他bizType当期-业务入账当期
     * 待动账：bgb总应收-其他bizType总应收-业务系统入账总实收 - 对账系统入账总实收
     *
     * @param commonBillChangeData
     * @param billTimeSliceDTO
     */
    @Override
    public void recalculateTransferFee(CommonBillChangeData commonBillChangeData, BillTimeSliceDTO billTimeSliceDTO) {
        BusinessTypeEnum businessTypeEnum = SecondBusinessLineEnum.toEnumByCode(Integer.parseInt(commonBillChangeData.getSecondBusinessLine())).getBusinessType();
        if (businessTypeEnum == null) {
            return;
        }
        TransferFeeTypeEnum transferFeeTypeEnum = TransferFeeTypeEnum.toEnum(businessTypeEnum);
        if (transferFeeTypeEnum == null) {
            return;
        }
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(commonBillChangeData.getAccountType());
        if (!apolloBizConfig.isTransferFeeOpen()) {
            return;
        }
        if (apolloBizConfig.getTransferFeeReceivableBizType().contains
                (commonBillChangeData.getBizType())) {
            // 处理应收 Sets.newHashSet("12", "23", "24", "25", "26", "53", "54", "55", "56", "31", "32", "33", "34", "35", "36", "37", "38", "84", "85", "86", "87");
            BigDecimal changeFee = commonBillChangeData.getChangeTransferFee().negate();
            BillTransferFeeCoinDetail billTransferFeeCoinDetail = getTimeSliceBillTransferFeeCoinDetail(billTimeSliceDTO, transferFeeTypeEnum.getCode(), commonBillChangeData.getCoinId());
            billTransferFeeCoinDetail.setChangeProp1(billTransferFeeCoinDetail.getChangeProp1().add(changeFee));
            billTransferFeeCoinDetail.setProp1(billTransferFeeCoinDetail.getProp1().add(changeFee));
            log.info("UtaBillCheckServiceImpl recalculateTransferFee prop1 checkTime:{} message:{} BillTransferFeeCoinDetail:{}", DateUtil.date2str(commonBillChangeData.getBizTime()), JSON.toJSONString(commonBillChangeData), JSON.toJSONString(billTransferFeeCoinDetail));
        } else if (apolloBizConfig.getTransferFeeReceivedBizType().contains(commonBillChangeData.getBizType())) {
            // 处理实收 Sets.newHashSet("82", "83", "92", "93");
            BigDecimal changeFee = commonBillChangeData.getChangeProp2();
            BillTransferFeeCoinDetail billTransferFeeCoinDetail = getTimeSliceBillTransferFeeCoinDetail(billTimeSliceDTO, transferFeeTypeEnum.getCode(), commonBillChangeData.getCoinId());
            billTransferFeeCoinDetail.setChangeProp2(billTransferFeeCoinDetail.getChangeProp2().add(changeFee));
            billTransferFeeCoinDetail.setProp2(billTransferFeeCoinDetail.getProp2().add(changeFee));
            log.info("UtaBillCheckServiceImpl recalculateTransferFee prop2 checkTime:{} message:{} BillTransferFeeCoinDetail:{}", DateUtil.date2str(commonBillChangeData.getBizTime()), JSON.toJSONString(commonBillChangeData), JSON.toJSONString(billTransferFeeCoinDetail));
        } else if (RECEIVED_BIZ_TRANSFER_FEE_TYPE_LIST.contains(commonBillChangeData.getBizType())) {
            // 业务线入账手续费
            BigDecimal changeFee = commonBillChangeData.getChangeProp2();
            BillTransferFeeCoinDetail billTransferFeeCoinDetail = getTimeSliceBillTransferFeeCoinDetail(billTimeSliceDTO, transferFeeTypeEnum.getCode(), commonBillChangeData.getCoinId());
            billTransferFeeCoinDetail.setChangeProp3(billTransferFeeCoinDetail.getChangeProp3().add(changeFee));
            billTransferFeeCoinDetail.setProp3(billTransferFeeCoinDetail.getProp3().add(changeFee));
        } else if (BGB_RECEIVABLE_FEE_TYPE_LIST.contains(commonBillChangeData.getBizType())) {
            // 处理bgg抵扣应收
            BigDecimal changeFee = commonBillChangeData.getChangeProp2().negate();
            BillTransferFeeCoinDetail billTransferFeeCoinDetail = getTimeSliceBillTransferFeeCoinDetail(billTimeSliceDTO, transferFeeTypeEnum.getCode(), commonBillChangeData.getCoinId());
            billTransferFeeCoinDetail.setChangeProp4(billTransferFeeCoinDetail.getChangeProp4().add(changeFee));
            billTransferFeeCoinDetail.setProp4(billTransferFeeCoinDetail.getProp4().add(changeFee));
        }
    }

    @Override
    public void calculateMsgTimeSliceSymbol(Byte accountType, BillTimeSliceDTO billTimeSliceDTO, CommonBillChangeData commonBillChangeData) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        String symbolId = commonBillChangeData.getSymbolId();
        BillSymbolProperty billSymbolProperty = billTimeSliceDTO.getSymbolPropertyMap().computeIfAbsent(symbolId, v -> {
            BillSymbolProperty newBillSymbolProperty = new BillSymbolProperty();
            newBillSymbolProperty.setSymbolId(symbolId);
            return newBillSymbolProperty;
        });
        billSymbolProperty.setChangeProp6(billSymbolProperty.getChangeProp6().add(commonBillChangeData.getChangeProp6()));
        billSymbolProperty.setChangeProp7(billSymbolProperty.getChangeProp7().add(commonBillChangeData.getChangeProp7()));
        // 累积已实现非重算用户已实现， 重算用户已实现在merge计算
        if (!reconSystemAccountService.isContractAdlReceivedUserId(accountTypeEnum, commonBillChangeData.getSymbolId(), commonBillChangeData.getAccountId())) {
            billSymbolProperty.setChangeProp1(billSymbolProperty.getChangeProp1().add(commonBillChangeData.getChangeProp7()));
        }
    }

    @Override
    public void calculateMsgTimeSliceSymbolCoin(Byte accountType, BillTimeSliceDTO billTimeSliceDTO, CommonBillChangeData commonBillChangeData) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        BillSymbolCoinProperty symbolCoinProperty = billTimeSliceDTO.getSymbolCoinPropertyMap().computeIfAbsent(BillSymbolCoinProperty.generateKey(commonBillChangeData.getSymbolId(), commonBillChangeData.getCoinId()), v -> {
            BillSymbolCoinProperty billSymbolCoinProperty = new BillSymbolCoinProperty();
            billSymbolCoinProperty.setSymbolId(commonBillChangeData.getSymbolId());
            billSymbolCoinProperty.setCoinId(commonBillChangeData.getCoinId());
            billSymbolCoinProperty.setAccountType(commonBillChangeData.getAccountType());
            return billSymbolCoinProperty;
        });
        symbolCoinProperty.setChangeProp6(symbolCoinProperty.getChangeProp6().add(commonBillChangeData.getChangeProp6()));
        symbolCoinProperty.setChangeProp7(symbolCoinProperty.getChangeProp7().add(commonBillChangeData.getChangeProp7()));
        symbolCoinProperty.setProp6(symbolCoinProperty.getProp6().add(commonBillChangeData.getChangeProp6()));
        symbolCoinProperty.setProp7(symbolCoinProperty.getProp7().add(commonBillChangeData.getChangeProp7()));
        // 累积已实现非重算用户已实现， 重算用户已实现在merge计算
        if (!reconSystemAccountService.isContractAdlReceivedUserId(accountTypeEnum, commonBillChangeData.getSymbolId(), commonBillChangeData.getAccountId())) {
            symbolCoinProperty.setChangeProp8(symbolCoinProperty.getChangeProp8().add(commonBillChangeData.getChangeProp7()));
            symbolCoinProperty.setProp8(symbolCoinProperty.getProp8().add(commonBillChangeData.getChangeProp7()));
        }
    }

    /**
     * 获取feeTypeCoinDetail
     *
     * @param billTimeSliceDTO
     * @param feeType
     * @param coinId
     * @return
     */
    private BillTransferFeeCoinDetail getTimeSliceBillTransferFeeCoinDetail(BillTimeSliceDTO billTimeSliceDTO, String feeType, Integer coinId) {
        String feeTypeCoinKey = BillTransferFeeCoinDetail.buildByFeedTypeCoin(feeType, coinId);
        BillTransferFeeCoinDetail billTransferFeeCoinDetail = billTimeSliceDTO.getBillTransferFeeCoinDetailMap().computeIfAbsent(feeTypeCoinKey, v -> {
            BillTransferFeeCoinDetail feeCoinDetail = new BillTransferFeeCoinDetail();
            feeCoinDetail.setCoinId(coinId);
            feeCoinDetail.setFeeType(feeType);
            return feeCoinDetail;
        });
        return billTransferFeeCoinDetail;
    }

    @Override
    public void setBillSymbolProperty(BillSymbolProperty billSymbolProperty, SymbolPropEnum symbolPropEnum, BigDecimal value) {
        billSymbolProperty.setPropByPropEnum(SYMBOL_PROP_ENUM_MAP.get(symbolPropEnum), value);
    }

    @Override
    public void setBillSymbolCoinProperty(BillSymbolCoinProperty billSymbolCoinProperty, SymbolCoinPropEnum symbolCoinPropEnum, BigDecimal value) {
        billSymbolCoinProperty.setPropByPropEnum(SYMBOL_COIN_PROP_ENUM_MAP.get(symbolCoinPropEnum), value);
    }

    @Override
    public void setBillSymbolProperty(BillSymbolProperty billSymbolProperty, Supplier<Tuple2<SymbolPropEnum, BigDecimal>> supplier) {
        Tuple2<SymbolPropEnum, BigDecimal> tuple2 = supplier.get();
        this.setBillSymbolProperty(billSymbolProperty, tuple2.getT1(), tuple2.getT2());
    }

    @Override
    public void setBillSymbolCoinProperty(BillSymbolCoinProperty billSymbolCoinProperty, Supplier<Tuple2<SymbolCoinPropEnum, BigDecimal>> supplier) {
        Tuple2<SymbolCoinPropEnum, BigDecimal> tuple2 = supplier.get();
        this.setBillSymbolCoinProperty(billSymbolCoinProperty, tuple2.getT1(), tuple2.getT2());
    }

    @Override
    public BigDecimal getBillSymbolProperty(BillSymbolProperty billSymbolProperty, SymbolPropEnum symbolPropEnum) {
        return billSymbolProperty.getPropByPropEnum(SYMBOL_PROP_ENUM_MAP.get(symbolPropEnum));
    }

    @Override
    public BigDecimal getBillSymbolCoinProperty(BillSymbolCoinProperty billSymbolCoinProperty, SymbolCoinPropEnum symbolCoinPropEnum) {
        return billSymbolCoinProperty.getPropByPropEnum(SYMBOL_COIN_PROP_ENUM_MAP.get(symbolCoinPropEnum));
    }

    @Override
    public void cleanAndSetAssetsProperty(AbstractProperty abstractProperty, Byte accountType) {
        BigDecimal prop2 = abstractProperty.getProp2();
        BigDecimal prop3 = abstractProperty.getProp3();
        BigDecimal changeProp2 = abstractProperty.getChangeProp2();
        BigDecimal changeProp3 = abstractProperty.getChangeProp3();
        abstractProperty.cleaPropAndChangeProp();
        abstractProperty.setProp2(prop2);
        abstractProperty.setProp3(prop3);
        abstractProperty.setChangeProp2(changeProp2);
        abstractProperty.setChangeProp3(changeProp3);
    }
}
