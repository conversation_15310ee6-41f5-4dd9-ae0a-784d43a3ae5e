package com.upex.reconciliation.service.service;

import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;

import java.util.Date;
import java.util.List;

/**
 * 业务系统实时流水
 * <AUTHOR>
 * @date 2024/5/22 15:40
 */
public interface RealTimeFlowService {
    /**
     * 查询指定业务线增量流水
     *
     * @param userId
     * @param startDate
     * @param endDate
     * @param accountTypeEnum
     * @return {@link List< AccountAssetsInfoResult> }
     * <AUTHOR>
     * @date 2024/5/22 16:04
     */
    List<AccountAssetsInfoResult> queryIncrFlows(Long userId, Date startDate, Date endDate, AccountTypeEnum accountTypeEnum);

}
