package com.upex.reconciliation.service.service.impl;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.service.OldBillContractProfitCoinDetailService;
import com.upex.reconciliation.service.dao.entity.BillContractProfitCoinDetail;
import com.upex.reconciliation.service.dao.mapper.OldBillContractProfitCoinDetailMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_coin_detail(币种维度盈亏明细表)】的数据库操作Service实现
 * @createDate 2023-06-09 17:18:46
 */
@Service
public class OldBillContractProfitCoinDetailServiceImpl implements OldBillContractProfitCoinDetailService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "oldBillContractProfitCoinDetailMapper")
    private OldBillContractProfitCoinDetailMapper oldBillContractProfitCoinDetailMapper;

    @Override
    public List<BillContractProfitCoinDetail> selectListByAccountTypeAndCheckTime(Byte accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInBillMaster(() -> {
            return oldBillContractProfitCoinDetailMapper.selectListByAccountTypeAndCheckTime(accountType, accountParam, checkTime);
        });
    }
}




