package com.upex.reconciliation.service.common.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SymbolPropEnum {
    L_COUNT("多仓数量"),
    S_COUNT("空仓数量"),
    INIT_VALUE("初始值"),
    UN_REALIZED("未实现"),
    MARGIN_REALIZED("资产值4"),
    REALIZED("已实现保证金币金额"),
    RE_REALIZED("重算已实现右币金额(排除重算用户)"),
    RE_UN_REALIZED("重算未实现");
    private String propName;
}
