package com.upex.reconciliation.service.business.prometheus;

import com.google.common.collect.Maps;
import io.micrometer.core.instrument.DistributionSummary;
import io.micrometer.core.instrument.Metrics;

import java.util.Map;
import java.util.Objects;

public class MonitorSummaryUtil {

    private static final Map<String, DistributionSummary> summaryMap = Maps.newHashMap();

    static {
        for (JobMonitorMetricNameEnum nameEnum : JobMonitorMetricNameEnum.values()) {
            if (nameEnum.isIndependent()) {
                initDistributionSummary(nameEnum.getName());
            }
        }

        for (InterfaceMonitorMetricNameEnum nameEnum : InterfaceMonitorMetricNameEnum.values()) {
            if (nameEnum.isIndependent()) {
                initDistributionSummary(nameEnum.getName());
            }
        }
    }

    private static DistributionSummary initDistributionSummary(String name) {
        DistributionSummary distributionSummary = DistributionSummary.builder(name)
                .maximumExpectedValue(10000D)
                .minimumExpectedValue(1D)
                .publishPercentiles(0.5, 0.90, 0.99, 1)
                .register(Metrics.globalRegistry);
        summaryMap.put(name, distributionSummary);
        return distributionSummary;
    }


    public static void record(String key, long cost) {
        DistributionSummary distributionSummary = summaryMap.get(key);
        if (Objects.isNull(distributionSummary)) {
            distributionSummary = initDistributionSummary(key);
        }
        distributionSummary.record(cost);
    }


}
