package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.upex.bill.dto.results.DeleteDataResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.AssetsCheckTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.TimeUnitEnum;
import com.upex.reconciliation.service.dao.entity.AssetsBillConfig;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillConfig;
import com.upex.reconciliation.service.model.config.ApolloBillDeleteConfig;
import com.upex.reconciliation.service.model.config.DeleteDetailConfig;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.service.impl.BillCoinUserPropertySnapshotService;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.utils.task.TaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Supplier;

/**
 * 删除历史数据
 */
@Service
@Slf4j
public class DeleteHistoryDataManagerService {
    @Resource(name = "deleteTaskManager")
    private TaskManager deleteTaskManager;
    @Resource(name = "taskManager")
    private TaskManager taskManager;
    @Resource
    private AssetsBillConfigService assetsBillConfigService;
    @Resource
    private AssetsBillCoinPropertyService assetsBillCoinPropertyService;
    @Resource
    private AssetsBillCoinTypePropertyService assetsBillCoinTypePropertyService;
    @Resource
    private AssetsBillConfigSnapshotService assetsBillConfigSnapshotService;
    @Resource
    private BillConfigService billConfigService;
    @Resource
    private BillCoinPropertyService billCoinPropertyService;
    @Resource
    private BillCoinTypePropertyService billCoinTypePropertyService;
    @Resource
    private BillCoinUserPropertyAssetSnapshotService billCoinUserPropertyAssetSnapshotService;
    @Resource
    private BillCoinTypeUserPropertyService billCoinTypeUserPropertyService;
    @Resource
    private BillSymbolCoinPropertyService billSymbolCoinPropertyService;
    @Resource
    private BillSymbolPropertyService billSymbolPropertyService;
    @Resource
    private BillUserPositionService billUserPositionService;
    @Resource
    private BillCoinUserPropertySnapshotService billCoinUserPropertySnapshotService;
    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;
    @Resource
    private BillDelayAccountService billDelayAccountService;
    @Resource
    private BillCoinUserPropertyErrorService billCoinUserPropertyErrorService;

    public List<DeleteDataResult> deleteHistoryData() {
        CopyOnWriteArrayList<DeleteDataResult> results = new CopyOnWriteArrayList<>();
        ApolloBillDeleteConfig apolloBillDeleteConfig = ReconciliationApolloConfigUtils.getApolloBillDeleteConfig();
        deleteTaskManager.forEachSubmitBatchAndWait(apolloBillDeleteConfig.getDeleteDetailConfigList(), (apolloConfig -> {
            DeleteDataResult result = new DeleteDataResult();
            String accountType = apolloConfig.getAccountType();
            String accountParam = apolloConfig.getAccountParam();
            result.setAccountType(accountType);
            result.setAccountParam(accountParam);
            Stopwatch stopwatch = Stopwatch.createStarted();
            // 是否继续执行
            if (canExecute(accountType, accountParam, apolloConfig)) {
                if (AssetsCheckTypeEnum.toEnum(accountType) != null) {
                    // 删除总账数据
                    this.deleteLedgerHistoryData(apolloConfig);
                } else if (AccountTypeEnum.toEnum(Byte.valueOf(accountType)) != null) {
                    // 删除业务线数据
                    this.deleteBusinessHistoryData(apolloConfig);
                }
            }
            result.setDeleteResult(true);
            results.add(result);
            log.info("deleteHistoryData success accountType:{} config:{} time:{}", accountType, accountParam, JSON.toJSONString(apolloConfig), stopwatch.stop());
        }), apolloBillDeleteConfig.getDeleteConcurrence());
        return results;
    }

    private void deleteBusinessHistoryData(DeleteDetailConfig apolloBillDeleteConfig) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(Byte.valueOf(apolloBillDeleteConfig.getAccountType()));
        if (accountTypeEnum == null) {
            return;
        }
        Date checkOkTime = DateUtil.str2date(DateUtil.date2str(new Date(), DateUtil.FMT_yyyy_MM_dd), DateUtil.FMT_yyyy_MM_dd);
        Date deleteTime = TimeUnitEnum.toDate(checkOkTime, apolloBillDeleteConfig.getBillDataKeepTime());
        if (deleteTime.getTime() >= checkOkTime.getTime()) {
            log.info("deleteHistoryData deleteBusinessHistoryData start deleteTime error assetAccountType:{} checkOkTime:{} rollbackTime:{}", accountTypeEnum.getCode(), DateUtil.date2str(checkOkTime), DateUtil.date2str(deleteTime));
            return;
        }
        // 根据保留时间删除
        executeDeleteTask(String.format("deleteBillCoinProperty-%s", accountTypeEnum.getCode()), deleteTime, () -> {
            return billCoinPropertyService.deleteByLtCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), deleteTime, apolloBillDeleteConfig.getPageSize());
        });
        executeDeleteTask(String.format("deleteBillCoinTypeProperty-%s", accountTypeEnum.getCode()), deleteTime, () -> {
            return billCoinTypePropertyService.deleteByLtCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), deleteTime, apolloBillDeleteConfig.getPageSize());
        });
//        executeDeleteTask(String.format("deleteBillCoinUserPropertyAssetSnapshot-%s", accountTypeEnum.getCode()), deleteTime, () -> {
//            return billCoinUserPropertyAssetSnapshotService.deleteByLtCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), deleteTime, apolloBillDeleteConfig.getPageSize());
//        });
//        executeDeleteTask(String.format("deleteBillCoinUserPropertyError-%s", accountTypeEnum.getCode()), deleteTime, () -> {
//            return billCoinUserPropertyErrorService.deleteByLtCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), deleteTime, apolloBillDeleteConfig.getPageSize());
//        });
        executeDeleteTask(String.format("deleteBillDelayAccount-%s", accountTypeEnum.getCode()), deleteTime, () -> {
            return billDelayAccountService.deleteByLtCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), deleteTime, apolloBillDeleteConfig.getPageSize());
        });
        if (accountTypeEnum.haveUserPosition()) {
            executeDeleteTask(String.format("deleteBillSymbolProperty-%s", accountTypeEnum.getCode()), deleteTime, () -> {
                return billSymbolPropertyService.deleteByLtCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), deleteTime, apolloBillDeleteConfig.getPageSize());
            });
            executeDeleteTask(String.format("deleteBillSymbolCoinProperty-%s", accountTypeEnum.getCode()), deleteTime, () -> {
                return billSymbolCoinPropertyService.deleteByLtCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), deleteTime, apolloBillDeleteConfig.getPageSize());
            });
        }
        // 根据时间片删除
        while (true) {
            List<BillConfig> billConfigList = billConfigService.selectByLtCheckTimeAsc(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), deleteTime, apolloBillDeleteConfig.getPageSize());
            if (CollectionUtils.isEmpty(billConfigList)) {
                break;
            }
            for (BillConfig billConfig : billConfigList) {
                Date checkTime = billConfig.getCheckOkTime();
                boolean result = billCoinTypeUserPropertyService.tableExists(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime);
                if (result) {
                    executeDeleteTask(String.format("deleteBillCoinTypeUserProperty-%s", accountTypeEnum.getCode()), checkTime, () -> {
                        return billCoinTypeUserPropertyService.deleteByCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime, apolloBillDeleteConfig.getPageSize());
                    });
                }
                if (accountTypeEnum.haveUserPosition()) {
                    result = billUserPositionService.tableExists(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime);
                    if (result) {
                        executeDeleteTask(String.format("deleteBillUserPosition-%s", accountTypeEnum.getCode()), checkTime, () -> {
                            return billUserPositionService.deleteByCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime, apolloBillDeleteConfig.getPageSize());
                        });
                    }
                }
                executeDeleteTask(String.format("deleteBillCoinUserPropertySnapshot-%s", accountTypeEnum.getCode()), checkTime, () -> {
                    // 删除用户 批量查询当期用户 根据当期用户查询可以删除的历史资产 批量删除
                    List<BillCoinUserProperty> billCoinUserPropertyList = billCoinUserPropertySnapshotService.concurrentSelectCoinUserSnapshot(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime, apolloBillDeleteConfig.getPageSize(), apolloBillDeleteConfig.getConcurrentSize());
                    List<List<BillCoinUserProperty>> billCoinUserPropertyPatitionList = Lists.partition(billCoinUserPropertyList, apolloBillDeleteConfig.getConcurrentSize());
                    Queue<Long> coinUserSnapshotIdQueue = new ConcurrentLinkedQueue<>();
                    taskManager.forEachSubmitBatchAndWait(billCoinUserPropertyPatitionList, (List<BillCoinUserProperty> coinUserProperties) -> {
                        coinUserProperties.forEach(coinUserProperty -> {
                            billCoinUserPropertySnapshotService.selectCoinUserLtCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), coinUserProperty.getUserId(), coinUserProperty.getCoinId(), checkTime).forEach(item -> {
                                coinUserSnapshotIdQueue.add(item.getId());
                            });
                        });
                    }, apolloBillDeleteConfig.getConcurrentSize());
                    List<List<Long>> coinUserSnapshotIdPatitionList = Lists.partition(new ArrayList(coinUserSnapshotIdQueue), apolloBillDeleteConfig.getPageSize().intValue());
                    taskManager.forEachSubmitBatchAndWait(coinUserSnapshotIdPatitionList, (List<Long> coinUserIds) -> {
                        billCoinUserPropertySnapshotService.deleteByIds(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), coinUserIds);
                    }, apolloBillDeleteConfig.getConcurrentSize());
                    return false;
                });
                billConfigService.deleteByCheckTime(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), checkTime);
            }
        }
        log.info("business rollBackData end accountType:{} rollbackTime:{}", apolloBillDeleteConfig.getAccountType(), DateUtil.date2str(deleteTime));
    }

    /**
     * 是否可以执行删除
     *
     * @param accountType
     * @param accountParam
     * @param apolloConfig
     * @return
     */
    private boolean canExecute(String accountType, String accountParam, DeleteDetailConfig apolloConfig) {
        return apolloConfig.isOpen();
    }

    /**
     * 删除总账历史数据
     */
    private void deleteLedgerHistoryData(DeleteDetailConfig apolloBillDeleteConfig) {
        if (!apolloBillDeleteConfig.isOpen()) {
            return;
        }
        AssetsCheckTypeEnum assetsCheckTypeEnum = AssetsCheckTypeEnum.toEnum(apolloBillDeleteConfig.getAccountType());
        AssetsBillConfig assetsBillConfig = assetsBillConfigService.selectByTypeAndParam(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam());
        if (assetsBillConfig == null) {
            return;
        }
        Date checkOkTime = DateUtil.str2date(DateUtil.date2str(assetsBillConfig.getCheckOkTime(), DateUtil.FMT_yyyy_MM_dd), DateUtil.FMT_yyyy_MM_dd);
        Date deleteTime = TimeUnitEnum.toDate(checkOkTime, apolloBillDeleteConfig.getBillDataKeepTime());
        if (deleteTime.getTime() >= checkOkTime.getTime()) {
            log.info("deleteHistoryData deleteLedgerHistoryData start deleteTime error assetAccountType:{} checkOkTime:{} rollbackTime:{}", assetsCheckTypeEnum.getCode(), DateUtil.date2str(checkOkTime), DateUtil.date2str(deleteTime));
            return;
        }
        executeDeleteTask(String.format("deleteAssetsBillCoinProperty-%s", assetsCheckTypeEnum.getCode()), deleteTime, () -> {
            return assetsBillCoinPropertyService.deleteByLtCheckTime(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam(), deleteTime, apolloBillDeleteConfig.getPageSize());
        });
        executeDeleteTask(String.format("deleteAssetsBillCoinTypeProperty-%s", assetsCheckTypeEnum.getCode()), deleteTime, () -> {
            return assetsBillCoinTypePropertyService.deleteByLtCheckTime(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam(), deleteTime, apolloBillDeleteConfig.getPageSize());
        });
        executeDeleteTask(String.format("deleteAssetsBillConfigSnapshot-%s", assetsCheckTypeEnum.getCode()), deleteTime, () -> {
            return assetsBillConfigSnapshotService.deleteByLtCheckTime(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam(), deleteTime, apolloBillDeleteConfig.getPageSize());
        });
        log.info("deleteHistoryData deleteLedgerHistoryData end assetsBillConfigSnapshotService.deleteByCheckTime assetAccountType:{} checkOkTime:{} rollbackTime:{}", assetsCheckTypeEnum.getCode(), DateUtil.date2str(checkOkTime), DateUtil.date2str(deleteTime));
    }


    /**
     * 执行删除和输出日志
     *
     * @param taskName
     * @param task
     */
    private void executeDeleteTask(String taskName, Date deleteTime, Supplier<Boolean> task) {
        int deleteBatchCount = 0;
        boolean result = true;
        Long totalStartTime = System.currentTimeMillis();
        while (result) {
            deleteBatchCount = deleteBatchCount + 1;
            Long startTime = System.currentTimeMillis();
            result = task.get();
            log.info("deleteHistoryData executeDeleteTask start taskName:{}  deleteTime:{} deleteBatchCount:{} singleTaskExecTimeMs:{}", taskName, DateUtil.date2str(deleteTime), deleteBatchCount, (System.currentTimeMillis() - startTime));
        }
        log.info("deleteHistoryData executeDeleteTask end taskName:{}  deleteTime:{} deleteBatchCount:{} taskExecTimeMs:{}", taskName, DateUtil.date2str(deleteTime), deleteBatchCount, (System.currentTimeMillis() - totalStartTime));
    }
}
