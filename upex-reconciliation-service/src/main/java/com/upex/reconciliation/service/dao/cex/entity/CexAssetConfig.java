package com.upex.reconciliation.service.dao.cex.entity;

import lombok.Data;

import java.util.Date;

/**
 * 资产同步时间配置实体类
 */
@Data
public class CexAssetConfig {

    private Long id;
    private Integer assetHistoryType; // 资产类型
    private Integer cexType;
    private String cexUserId;
    private Date checkSyncTime;     // 资产同步时间
    private Integer status;
    private Date createTime;        // 创建时间
    private Date updateTime;        // 最后更新时间
}
