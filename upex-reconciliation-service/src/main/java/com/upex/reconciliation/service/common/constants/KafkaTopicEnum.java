package com.upex.reconciliation.service.common.constants;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum KafkaTopicEnum {
    RECON_USER_SNAPSHOT_SYNC_TOPIC("recon_user_snapshot_sync_topic", "syncUserSnapshotConsumer", "资产快照topic"),
    RECON_USER_INCREMENT_SYNC_TOPIC("recon_user_increment_sync_topic", "syncBillUserConsumer", "用户增量同步topic"),

    RECON_USER_INITALL_SYNC_TOPIC("bg_recon_upex-reconciliation_sync_initall_user_topic", "syncInitAllUserConsumer", "用户初始化同步topic"),

    RECON_CAPITAL_ORDER_SYNC_TOPIC("bg_recon-upex-reconciliation-job-spot_capital_order_info", "syncCapitalOrderConsumer", "充提流同步topic"),
    RECON_USER_POSITION_SYNC_TOPIC("bg_recon_upex-reconciliation-job_user_position_sync_prod", "", "用户持仓同步topic"),
    RECON_SYNC_CENTER_USER_INFO_TOPIC("bg_recon_upex-reconciliation-job_sync_center_user_info", "syncCenterUserInfoConsumer", "同步用户中心信息topic"),
    RECON_SYNC_FINANCIAL_ACCOUNT_TOPIC("bg_recon_upex-reconciliation-job_sync_financial_account", "syncFinancialAccountConsumer", "同步理财账户实时资产信息topic"),
    RECON_SYNC_BUSINESS_BILL_FLOW_TOPIC("recon_consumer_bill_type_*", "syncBusinessBillFlowConsumer", "同步业务线流水表"),
    RECON_SYNC_USER_PROFIT_AMOUNT_TOPIC("bg_recon_upex-reconciliation-job_sync_profit_amount", "syncUserProfitAccountConsumer", "同步用户盈利金额topic"),
    RECON_ORDER_FLOW_CONSUMER_TYPE("RECON_ORDER_FLOW_CONSUMER_TYPE", "reconOrderFlowConsumer", "订单对账流水topic"),
    RECON_ORDER_CONVERT_CONSUMER_TYPE("RECON_ORDER_CONVERT_CONSUMER_TYPE", "reconOrderConvertConsumer", "闪兑订单topic"),
    RECON_SYNC_DEALT_RECORD_TOPIC("recon_sync_dealt_record_*", "accountDealtRecordConsumerRunnable", "同步业务线成交记录表topic"),
    ;
    private String code;
    private String consumerName;
    private String desc;
}
