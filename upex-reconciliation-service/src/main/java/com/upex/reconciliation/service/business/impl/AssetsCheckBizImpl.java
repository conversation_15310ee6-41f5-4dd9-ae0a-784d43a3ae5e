package com.upex.reconciliation.service.business.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.upex.bill.dto.enums.AccountTypeEnum;
import com.upex.bill.dto.enums.AssetsCheckTypeEnum;
import com.upex.bill.dto.params.AssetsBaseRequest;
import com.upex.bill.dto.params.BasePageRequest;
import com.upex.bill.dto.params.BaseRequest;
import com.upex.bill.dto.results.AssetsInfoResult;
import com.upex.bill.dto.results.PageResponse;
import com.upex.bill.dto.results.SyncInfoResult;
import com.upex.reconciliation.service.business.*;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.enums.CapitalInitBusinessTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.LogLevelEnum;
import com.upex.reconciliation.service.common.constants.enums.ProfitTransferTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.QueryUserAssetsSceneEnum;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.config.TransferDiffTolerateExitValueConfig;
import com.upex.reconciliation.service.model.param.UserAssetsCompareParam;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.utils.BizLogUtils;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.MatchUtils;
import com.upex.reconciliation.service.utils.NumberUtil;
import com.upex.ticker.facade.dto.PriceVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2020-11-19 14:51
 * @desc
 **/
@Service
@Slf4j
public class AssetsCheckBizImpl implements AssetsCheckBiz {
    @Autowired
    private AssetsService internalAssetsService;
    @Resource
    private AssetsCheckService assetsCheckService;
    @Resource
    private AssetsBillConfigService assetsBillConfigService;
    @Resource
    private CommonService commonService;
    @Resource
    private OldBillContractProfitTransferService oldBillContractProfitTransferService;
    @Resource
    private BillContractProfitTransferService billContractProfitTransferService;
    @Resource
    private BillCapitalInitPropertyService billCapitalInitPropertyService;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    @Resource
    private BillCoinUserPropertyService billCoinUserPropertyService;
    private Map<AssetsCheckTypeEnum, AssetsService> assetsCheckHandleMap = new ConcurrentHashMap<>();


    @PostConstruct
    private void init() {
        assetsCheckHandleMap.put(AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS, internalAssetsService);
        assetsCheckHandleMap.put(AssetsCheckTypeEnum.LEVER_SPOT_TOTAL_ASSETS, internalAssetsService);
    }


    /**
     * 获取期初资产查询对象
     *
     * @param assetsBaseRequest
     * @return
     */
    private AssetsBaseRequest getStartAssetsBaseRequest(AssetsBaseRequest assetsBaseRequest) {
        AssetsBaseRequest startAssetsBaseRequest = new AssetsBaseRequest();
        startAssetsBaseRequest.setMaxId(assetsBaseRequest.getMaxId());
        startAssetsBaseRequest.setAssetsCheckType(assetsBaseRequest.getAssetsCheckType());
        startAssetsBaseRequest.setAssetsCheckParam(assetsBaseRequest.getAssetsCheckParam());
        startAssetsBaseRequest.setBeginTime(assetsBaseRequest.getBeginTime());
        startAssetsBaseRequest.setEndTime(assetsBaseRequest.getBeginTime());
        return startAssetsBaseRequest;
    }


    @Override
    public void initSyncInfoAndAssets(AssetsBillConfig assetsBillConfig, AssetsCheckConfig assetsCheckConfig,
                                      AssetsBaseRequest assetsBaseRequest, BasePageRequest basePageRequest) {
        AssetsCheckTypeEnum assetsCheckTypeEnum = AssetsCheckTypeEnum.toEnum(assetsCheckConfig.getAssetsCheckType());
        PageResponse<SyncInfoResult> pageResponse = null;
        AssetsBaseRequest startAssetsBaseRequest = getStartAssetsBaseRequest(assetsBaseRequest);
        do {
            AssetsService assetsService = assetsCheckHandleMap.get(assetsCheckTypeEnum);
            pageResponse = assetsService.querySyncInfo(assetsCheckConfig, assetsBaseRequest, basePageRequest.getPageNo(), basePageRequest.getPageSize());
            if (Objects.isNull(pageResponse)) {
                return;
            }
            List<SyncInfoResult> syncInfoResults = pageResponse.getData();
            if (CollectionUtils.isNotEmpty(syncInfoResults)) {
                List<Long> syncIds = syncInfoResults.stream().map(SyncInfoResult::getSyncId).collect(Collectors.toList());
                List<AssetsInfoResult> assetsInfoResults = assetsService.queryAssets(syncIds, startAssetsBaseRequest);
                if (CollectionUtils.isNotEmpty(assetsInfoResults)) {
                    log.info("AssetsCheckBizImpl.initSyncInfoAndAssets assetsCheckTypeEnum:{},assetsInfoResults:{},baseRequest:{}", JSON.toJSONString(assetsCheckTypeEnum), JSON.toJSONString(assetsInfoResults), JSON.toJSONString(assetsBaseRequest));
                    //批量写入
                    assetsCheckService.initSyncInfoAndAssets(assetsInfoResults, assetsBillConfig, assetsCheckConfig, startAssetsBaseRequest, pageResponse);
                }
            }
            if (pageResponse.isHasNextPage()) {
                assetsBaseRequest = assetsBaseRequest.clone();
                assetsBaseRequest.setMaxId(pageResponse.getMaxId());
            }
            basePageRequest.setPageNo(basePageRequest.getPageNo() + 1);
        } while (pageResponse.isHasNextPage());

    }

    @Override
    public List<AssetsInfoResult> getAssetsBills(AssetsCheckConfig assetsCheckConfig, AssetsBillConfig assetsBillConfig, AssetsBaseRequest assetsBaseRequest, BasePageRequest basePageRequest, GlobalBillConfig globalBillConfig) {
        List<AssetsInfoResult> list = new ArrayList<>();
        PageResponse<AssetsInfoResult> pageResponse = null;
        AssetsCheckTypeEnum assetsCheckTypeEnum = AssetsCheckTypeEnum.toEnum(assetsBillConfig.getAssetsCheckType());
        do {
            pageResponse = assetsCheckHandleMap.get(assetsCheckTypeEnum).queryBillInfo(assetsCheckConfig, assetsBaseRequest, basePageRequest.getPageNo(), basePageRequest.getPageSize(), new Date(assetsBaseRequest.getEndTime()), globalBillConfig, null);
            log.info("in all check getAssetsBills assetsCheckConfig {},assetsBaseRequest {}, result : {}", JSONObject.toJSONString(assetsCheckConfig), JSONObject.toJSONString(assetsBaseRequest), JSONObject.toJSONString(pageResponse));
            if (Objects.isNull(pageResponse)) {
                return null;
            }
            List<AssetsInfoResult> resultList = pageResponse.getData();
            if (CollectionUtils.isNotEmpty(resultList)) {
                list.addAll(resultList);
            }
            if (pageResponse.isHasNextPage()) {
                assetsBaseRequest = assetsBaseRequest.clone();
                assetsBaseRequest.setMaxId(pageResponse.getMaxId());
            }
            basePageRequest.setPageNo(basePageRequest.getPageNo() + 1);
        } while (pageResponse.isHasNextPage());

        return list;
    }

    @Override
    public List<AssetsInfoResult> getAssetsBillsWithFee(AssetsCheckConfig assetsCheckConfig, AssetsBillConfig assetsBillConfig,
                                                        AssetsBaseRequest assetsBaseRequest, BasePageRequest basePageRequest,
                                                        GlobalBillConfig globalBillConfig, Map<Integer, BigDecimal> feeMaps) {
        List<AssetsInfoResult> list = new ArrayList<>();
        PageResponse<AssetsInfoResult> pageResponse = null;
        AssetsCheckTypeEnum assetsCheckTypeEnum = AssetsCheckTypeEnum.toEnum(assetsBillConfig.getAssetsCheckType());
        do {
            pageResponse = assetsCheckHandleMap.get(assetsCheckTypeEnum).queryBillInfo(assetsCheckConfig, assetsBaseRequest, basePageRequest.getPageNo(), basePageRequest.getPageSize(), new Date(assetsBaseRequest.getEndTime()), globalBillConfig, feeMaps);
            log.info("in all check getAssetsBills assetsCheckConfig {},assetsBaseRequest {}, result : {}", JSONObject.toJSONString(assetsCheckConfig), JSONObject.toJSONString(assetsBaseRequest), JSONObject.toJSONString(pageResponse));
            if (Objects.isNull(pageResponse)) {
                return null;
            }
            List<AssetsInfoResult> resultList = pageResponse.getData();
            if (CollectionUtils.isNotEmpty(resultList)) {
                list.addAll(resultList);
            }
            if (pageResponse.isHasNextPage()) {
                assetsBaseRequest = assetsBaseRequest.clone();
                assetsBaseRequest.setMaxId(pageResponse.getMaxId());
            }
            basePageRequest.setPageNo(basePageRequest.getPageNo() + 1);
        } while (pageResponse.isHasNextPage());

        return list;
    }

    @Override
    public boolean checkAssetsBills(List<AssetsInfoResult> list, AssetsBillConfig assetsBillConfig, AssetsCheckConfig assetsCheckConfig, AssetsBaseRequest assetsBaseRequest, Long timeInterval, GlobalBillConfig globalBillConfig, Map<Integer, BigDecimal> feeMaps) {
        //5分钟无流水账单,变更配置表对账时间
        if (CollectionUtils.isEmpty(list)) {
            assetsBillConfig.setFlag(true);
            Date updateTime = new Date();
            assetsBillConfig.setUpdateTime(updateTime);
            assetsBillConfigService.updateByPrimaryKeySelective(assetsBillConfig);
            return true;
        }
        log.info("check assetBillConfig assetsBillConfig: {},billSize {}", assetsBillConfig, list.size());

        List<BillContractProfitTransfer> transferList = Lists.newArrayList();
        List<BillContractProfitCoinDetail> coinDetailList = Lists.newArrayList();
        List<BillContractProfitSymbolDetail> symbolDetailList = Lists.newArrayList();
        //写入动账数据
        if (AssetsCheckTypeEnum.isInternalTotalAssets(assetsCheckConfig.getAssetsCheckType()) && assetsCheckConfig.isCollectTransactionDataOpen()) {
            billContractProfitTransferService.greaterTransferData(assetsBillConfig, assetsCheckConfig, coinDetailList, transferList, symbolDetailList);
        }

        Date lastCheckOkTime = new Date(assetsBillConfig.getCheckOkTime().getTime() - timeInterval);
        //查询 coinId+流水汇总新的
        List<AssetsBillCoinProperty> assetsBillCoinPropertyList = assetsCheckService.
                collectBillCoinProperty(lastCheckOkTime, list, assetsBillConfig, globalBillConfig);

        //汇总 coinId+typeId
        List<AssetsBillCoinTypeProperty> assetsBillCoinTypePropertyList = assetsCheckService.
                collectBillCoinTypeProperty(assetsBaseRequest, lastCheckOkTime, list, assetsBillConfig, globalBillConfig);

        //获取系统币种总资产
        BizLogUtils.log(LogLevelEnum.NON_KEY_INPUT, globalBillConfig, "AssetsCheckBizImpl begin to getTotalAssets assetsBillConfig: {},assetsBaseRequest {}", JSON.toJSONString(assetsBillConfig), JSONObject.toJSONString(assetsBaseRequest));
        Map<Integer, AssetsInfoResult> totalAssetsMap = getTotalAssets(assetsBillConfig, assetsBaseRequest);
        BizLogUtils.log(LogLevelEnum.NON_KEY_RESULT, globalBillConfig, "AssetsCheckBizImpl getTotalAssets result {}", JSON.toJSONString(totalAssetsMap));

        //计算账户总资金
        if (totalAssetsMap != null) {
            //根据入出业务类型对总资产
            // assetsBillCoinTypePropertyList 来自于 assetsBillCoinTypeProperty表+ 合并流水
            // totalAssetsMap 来自于查 查 bill_coin表
            boolean checkPass = checkInAndOutAssets(assetsBillCoinTypePropertyList, totalAssetsMap, assetsCheckConfig, assetsBillConfig, feeMaps);
            BizLogUtils.log(LogLevelEnum.NON_KEY_RESULT, globalBillConfig, "In all checkInAndOutAssets assetsCheckConfig {}, assetsBillConfig{}, result {}", JSONObject.toJSONString(assetsCheckConfig), JSONObject.toJSONString(assetsBillConfig), checkPass);
            if (!checkPass) {
                return false;
            }
        }

        //批量回写数据
        assetsCheckService.batchInsertAndUpdate(assetsBillCoinPropertyList, assetsBillCoinTypePropertyList, assetsBillConfig, transferList, coinDetailList, symbolDetailList);
        return true;
    }


    /**
     * 总账入出对账
     *
     * @param assetsBillCoinTypePropertyList
     * @param totalAssetsMap
     * @param assetsCheckConfig
     * @param assetsBillConfig
     * @return
     */
    private boolean checkInAndOutAssets(List<AssetsBillCoinTypeProperty> assetsBillCoinTypePropertyList,
                                        Map<Integer, AssetsInfoResult> totalAssetsMap, AssetsCheckConfig assetsCheckConfig,
                                        AssetsBillConfig assetsBillConfig, Map<Integer, BigDecimal> feeMaps) {

        boolean result = true;
        List<String> transferInList = assetsCheckConfig.getTransferIn();
        List<String> transferOutList = assetsCheckConfig.getTransferOut();
        //coinId+type+billCoinTypeUserProperty
        if (transferInList == null || transferOutList == null) {
            return true;
        }

        Map<Integer, AssetsBillCoinTypeProperty> transferInMap = new HashMap<>(16);
        Map<Integer, AssetsBillCoinTypeProperty> transferOutMap = new HashMap<>(16);
        Map<Integer, AssetsBillCoinTypeProperty> transferOtherMap = new HashMap<>(16);

        Map<Integer, Map<String, BigDecimal>> inMap = Maps.newHashMap();
        Map<Integer, Map<String, BigDecimal>> inChangeMap = Maps.newHashMap();
        Map<Integer, Map<String, BigDecimal>> outMap = Maps.newHashMap();
        Map<Integer, Map<String, BigDecimal>> outChangeMap = Maps.newHashMap();
        Map<Integer, Map<String, BigDecimal>> otherMap = Maps.newHashMap();
        Map<Integer, Map<String, BigDecimal>> otherChangeMap = Maps.newHashMap();

        List<BillContractProfitTransfer> transfers = getBillContractProfitTransfers(assetsBillConfig, assetsCheckConfig);
        // 现货待冻账初始值
        Map<Integer, BigDecimal> spotInitUnProfitTransfers = getSpotInitUnProfitTransfers();
        List<TransferDiffTolerateExitValueConfig> transferDiffTolerateExitValueList = assetsCheckConfig.getTransferDiffTolerateExitValueList();
        Map<Integer, BigDecimal> transferDiffTolerateExitValueMap = CollectionUtils.isNotEmpty(transferDiffTolerateExitValueList) ? transferDiffTolerateExitValueList.stream().collect(Collectors.toMap(TransferDiffTolerateExitValueConfig::getCoinId, TransferDiffTolerateExitValueConfig::getTransferDiffTolerateExitValue)) : Collections.emptyMap();

        List<AssetsBillCoinTypeProperty> notConfigBizTypePropertyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(assetsBillCoinTypePropertyList)) {
            for (AssetsBillCoinTypeProperty assetsBillCoinTypeProperty : assetsBillCoinTypePropertyList) {
                String bizType = assetsBillCoinTypeProperty.getBizType();
                //入类型 p1+p2+p3+p4+p5
                if (MatchUtils.checkValidate(transferInList, bizType)) {
                    log.info("In all check AssetsCheckBizImpl checkInAndOutAssets transfer in Property collect {}", JSON.toJSONString(assetsBillCoinTypeProperty));
                    transferInMap = collectCoinIdTypeAssets(transferInMap, assetsBillCoinTypeProperty);
                    //出类型 p1+p2+p3+p4+p5
                } else if (MatchUtils.checkValidate(transferOutList, bizType)) {
                    log.info("In all check AssetsCheckBizImpl checkInAndOutAssets transfer out Property collect {}", JSON.toJSONString(assetsBillCoinTypeProperty));
                    transferOutMap = collectCoinIdTypeAssets(transferOutMap, assetsBillCoinTypeProperty);
                } else {
                    //未打开实际检查,放在入的类型中
//                    transferInMap = collectCoinIdTypeAssets(transferInMap, assetsBillCoinTypeProperty);
                    // 未匹配到的类型
                    log.info("In all check AssetsCheckBizImpl checkInAndOutAssets transfer other Property collect {}", JSON.toJSONString(assetsBillCoinTypeProperty));
                    notConfigBizTypePropertyList.add(assetsBillCoinTypeProperty);
                    transferOtherMap = collectCoinIdTypeAssets(transferOtherMap, assetsBillCoinTypeProperty);
                }
            }

            Map<Integer, BillContractProfitTransfer> pendingBalanceMap = transfers.stream()
                    .filter(asset -> assetsCheckConfig.getSubSystemList().contains(asset.getToAccountType() + BillConstants.SEPARATOR + asset.getToAccountParam()))
                    .collect(Collectors.toMap(BillContractProfitTransfer::getCoinId, Function.identity(), (key1, key2) -> {
                        key2.setTransferCount(key1.getTransferCount().add(key2.getTransferCount()));
                        return key2;
                    }));
            BillContractProfitTransfer defaultBillContractProfitTransfer = new BillContractProfitTransfer();
            defaultBillContractProfitTransfer.setTransferCount(BigDecimal.ZERO);

            log.info("checkInAndOutAssets old assetsCheckType:{} checkTime:{} transferInMap:{} transferOutMap:{} transferOtherMap:{} spotInitUnProfitTransfers:{} transferDiffTolerateExitValueMap:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(assetsBillConfig.getCheckOkTime()), JSON.toJSONString(transferInMap), JSON.toJSONString(transferOutMap), JSON.toJSONString(transferOtherMap), JSON.toJSONString(spotInitUnProfitTransfers), JSON.toJSONString(transferDiffTolerateExitValueMap));

            //获取全部币种汇率
            Map<String, PriceVo> allRate = commonService.getRatesToUSDT();
            //在入出map中进行匹配,根据币种获取资产，计算各自总资产，得到入出的总资产
            for (Map.Entry<Integer, AssetsBillCoinTypeProperty> entry : transferInMap.entrySet()) {
                //out
                Integer coinId = entry.getKey();
                AssetsBillCoinTypeProperty outBillCoinTypeProperty = transferOutMap.get(coinId);
                AssetsInfoResult assetsInfoResult = totalAssetsMap.get(coinId);
                BillContractProfitTransfer billContractProfitTransfer = pendingBalanceMap.getOrDefault(coinId, defaultBillContractProfitTransfer);
                BigDecimal transferCount = feeMaps.getOrDefault(coinId, BigDecimal.ZERO).add(spotInitUnProfitTransfers.getOrDefault(coinId, BigDecimal.ZERO));
                BigDecimal transferCountNew = billContractProfitTransfer.getTransferCount();
                BigDecimal totalBalance = BigDecimal.ZERO;
                if (assetsInfoResult != null) {
                    totalBalance = assetsInfoResult.getPropSum();
                }
                BigDecimal transferIn = entry.getValue().getProp1();
                BigDecimal transferOut = BigDecimal.ZERO;
                if (outBillCoinTypeProperty != null) {
                    transferOut = outBillCoinTypeProperty.getProp1();
                }
                // 公式调整  总资产 - (入+出) + 待动账金额 = 0
                BigDecimal diffBalance = totalBalance.subtract(transferIn.add(transferOut)).add(transferCount);
                BigDecimal targetDiff = transferDiffTolerateExitValueMap.getOrDefault(coinId, BigDecimal.ZERO);
                log.info("AssetsCheckBizImpl info checkInAndOutAssets assets check accountType {},checkTime={},not equals coinId={},diff balance={},totalBalance={}, transferCount= {} in={},out={} targetDiff:{}",
                        assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(assetsBillConfig.getCheckOkTime()), coinId, diffBalance, totalBalance, transferCount, transferIn, transferOut, targetDiff);
                if (diffBalance.compareTo(targetDiff) != 0) {
                    //将币种折算成USDT，然后和容忍值对比,容忍值暂时为10
                    BigDecimal rate = commonService.checkRateByCoinIdAndReturnUSDT(coinId, allRate);
                    if (diffBalance.abs().multiply(rate).compareTo(assetsCheckConfig.getTotalDiffTolerateExitValue()) < 0) {
                        log.warn("assets check not equals but in totalDiffTolerateExitValue accountType:{},not equals coinId:{},diff balance:{}", assetsCheckConfig.getAssetsCheckType(), coinId, diffBalance);
                        //在容忍值范围之内
                        continue;
                    } else {
                        List<AssetsBillCoinTypeProperty> coinTypeDetailList = assetsBillCoinTypePropertyList.stream().filter(property -> property.getCoinId().equals(coinId)).collect(Collectors.toList());

                        // 获取有变化的资产类型，但没有在匹配的入出类型
                        Map<String, BigDecimal> bizTypeMap = notConfigBizTypePropertyList.stream()
                                .filter(property -> property.getCoinId().equals(coinId) && property.getChangePropSum().compareTo(BigDecimal.ZERO) != 0)
                                .collect(Collectors.toMap(AssetsBillCoinTypeProperty::getBizType, AssetsBillCoinTypeProperty::getChangePropSum, (key1, key2) -> key2));

                        log.error("AssetsCheckBizImpl checkInAndOutAssets assets check accountType {},accountParam={},not equals coinId={},diff balance={},rate={},totalBalance={}, transferCount= {}" +
                                        "in={},out={},assetsBillCoinTypePropertyList={},bizTypeMap {}",
                                assetsCheckConfig.getAssetsCheckType(), assetsCheckConfig.getAssetsCheckParam(), coinId, diffBalance, rate,
                                totalBalance, transferCount, transferIn, transferOut, JSONObject.toJSONString(coinTypeDetailList), JSONObject.toJSONString(bizTypeMap));

                        result = false;
                    }
                }
            }
        }
        return result;
    }


    private Map<Integer, AssetsInfoResult> getTotalAssets(AssetsBillConfig assetsBillConfig, AssetsBaseRequest baseRequest) {
        Map<Integer, AssetsInfoResult> resultMap = new HashMap<>();
        AssetsCheckTypeEnum assetsCheckTypeEnum = AssetsCheckTypeEnum.toEnum(assetsBillConfig.getAssetsCheckType());
        List<AssetsInfoResult> allAssets = assetsCheckHandleMap.get(assetsCheckTypeEnum).queryAllAssets(baseRequest);
        if (CollectionUtils.isNotEmpty(allAssets)) {
            return allAssets.stream().collect(Collectors.toMap(AssetsInfoResult::getCoinId, Function.identity(), (key1, key2) -> key2));
        }
        return resultMap;
    }


    /**
     * 根据
     *
     * @param assetsBillConfig
     * @param assetsCheckConfig
     * @return
     */
    @Override
    public List<BillContractProfitTransfer> getBillContractProfitTransfers(AssetsBillConfig assetsBillConfig, AssetsCheckConfig assetsCheckConfig) {
        // 待动账金额
        List<ProfitTransferTypeEnum> typeEnums = Lists.newArrayList(ProfitTransferTypeEnum.SYSTEM_CONTRACT_FEE, ProfitTransferTypeEnum.SYSTEM_RESET, ProfitTransferTypeEnum.SYSTEM_SPOT_FEE, ProfitTransferTypeEnum.SYSTEM_SPOT_FEE_RESET, ProfitTransferTypeEnum.SYSTEM_LEVER_FEE, ProfitTransferTypeEnum.SYSTEM_LEVER_FEE_RESET);
        List<BillContractProfitTransfer> transfers = Lists.newArrayList();
        transfers.addAll(oldBillContractProfitTransferService.selectDelayedTransactionByTimeAndType(assetsBillConfig.getCheckOkTime(), typeEnums, null));
        transfers.addAll(oldBillContractProfitTransferService.selectDelayedTransactionByStatusAndTimeAndType(assetsBillConfig.getCheckOkTime(), typeEnums, null));
        if (DateUtil.isFirstDayOfMonth(assetsBillConfig.getCheckOkTime()) || assetsCheckConfig.getQueryCrossMonthTransfer()) {
            Date lastMonth = DateUtil.getLastMonthEndTime(assetsBillConfig.getCheckOkTime());
            transfers.addAll(oldBillContractProfitTransferService.selectDelayedTransactionByTimeAndType(assetsBillConfig.getCheckOkTime(), typeEnums, lastMonth));
            transfers.addAll(oldBillContractProfitTransferService.selectDelayedTransactionByStatusAndTimeAndType(assetsBillConfig.getCheckOkTime(), typeEnums, lastMonth));
        }
        // 是否需要补数据，如果需要补的话，也添加到 transfers当中

        List<BillContractProfitTransfer> fromNewBillContractProfit = compensateBillContractProfitTransferData(assetsBillConfig, assetsCheckConfig);
        log.info("compensateBillContractProfitTransferData assetsBillConfig:{}, transfers:{} , fromNewBillContractProfit {}", JSONObject.toJSONString(assetsBillConfig), JSONObject.toJSONString(transfers), JSONObject.toJSONString(fromNewBillContractProfit));
        transfers.addAll(fromNewBillContractProfit);
        return transfers;
    }

    /**
     * 获取待冻账初始值
     *
     * @return
     */
    public Map<Integer, BigDecimal> getSpotInitUnProfitTransfers() {
        List<BillCapitalInitProperty> capitalInitPropertyList = billCapitalInitPropertyService.selectRecords(String.valueOf(AccountTypeEnum.SPOT.getCode()), AccountTypeEnum.SPOT.getAccountParam(), CapitalInitBusinessTypeEnum.SPOT_UN_PROFIT_TRANSFER.getCode());
        Map<Integer, BigDecimal> capitalInitPropertyMap = capitalInitPropertyList.stream().collect(Collectors.toMap(BillCapitalInitProperty::getCoinId, BillCapitalInitProperty::getInitValue));
        return capitalInitPropertyMap;
    }

    /**
     * 获取待动账金额，从老bill中获取，如果不够，则需要从新的内存对账这边补数
     *
     * @param assetsBillConfig
     * @param assetsCheckConfig
     * @return
     */
    @Override
    public List<BillContractProfitTransfer> compensateBillContractProfitTransferData(AssetsBillConfig assetsBillConfig, AssetsCheckConfig assetsCheckConfig) {
        // 查询每个业务线逐条的待动账数据
        List<BillContractProfitTransfer> allBillContractProfitTransferList = new ArrayList<>();
        List<ProfitTransferTypeEnum> typeEnums = Lists.newArrayList(ProfitTransferTypeEnum.SYSTEM_CONTRACT_FEE, ProfitTransferTypeEnum.SYSTEM_RESET, ProfitTransferTypeEnum.SYSTEM_SPOT_FEE, ProfitTransferTypeEnum.SYSTEM_SPOT_FEE_RESET, ProfitTransferTypeEnum.SYSTEM_LEVER_FEE, ProfitTransferTypeEnum.SYSTEM_LEVER_FEE_RESET);
        List<BillContractProfitTransfer> transfers = Lists.newArrayList();
        transfers.addAll(oldBillContractProfitTransferService.selectEachDelayedTransactionByTimeAndType(assetsBillConfig.getCheckOkTime(), typeEnums, null));
        transfers.addAll(oldBillContractProfitTransferService.selectEachDelayedTransactionByStatusAndTimeAndType(assetsBillConfig.getCheckOkTime(), typeEnums, null));
        if (DateUtil.isFirstDayOfMonth(assetsBillConfig.getCheckOkTime()) || assetsCheckConfig.getQueryCrossMonthTransfer()) {
            Date lastMonth = DateUtil.getLastMonthEndTime(assetsBillConfig.getCheckOkTime());
            transfers.addAll(oldBillContractProfitTransferService.selectEachDelayedTransactionByTimeAndType(assetsBillConfig.getCheckOkTime(), typeEnums, lastMonth));
            transfers.addAll(oldBillContractProfitTransferService.selectEachDelayedTransactionByStatusAndTimeAndType(assetsBillConfig.getCheckOkTime(), typeEnums, lastMonth));
        }


        Map<Byte, Date> checkOkTimeMap = new HashMap<>();
        for (BillContractProfitTransfer billContractProfitTransfer : transfers) {
            Date checkTime = checkOkTimeMap.getOrDefault(billContractProfitTransfer.getToAccountType(), new Date(0));
            if (billContractProfitTransfer.getCheckOkTime().compareTo(checkTime) > 0) {
                checkOkTimeMap.put(billContractProfitTransfer.getToAccountType(), billContractProfitTransfer.getCheckOkTime());
            }
        }
        // 遍历，记录下每个业务线最大的checkOkTime，然后看下跟当前checkTime有没有gap，有的话就是需要补数的业务线
        for (Byte toAccountType : checkOkTimeMap.keySet()) {
            Date accountTypeCheckOkTime = checkOkTimeMap.get(toAccountType);
            if (accountTypeCheckOkTime.compareTo(assetsBillConfig.getCheckOkTime()) < 0) {
                List<BillContractProfitTransfer> singleAccountTypeBillContractProfitTransferList = billContractProfitTransferService.selectEachDelayedTransactionByStatusAndTime(accountTypeCheckOkTime, assetsBillConfig.getCheckOkTime(), toAccountType);
                if (CollectionUtils.isNotEmpty(singleAccountTypeBillContractProfitTransferList)) {
                    allBillContractProfitTransferList.addAll(singleAccountTypeBillContractProfitTransferList);
                }
            }
        }
        return allBillContractProfitTransferList;
    }

    @Override
    public void checkUserAssets(UserAssetsCompareParam param) {
        List<Byte> accountTypes = param.getAccountTypes();
        if (CollectionUtils.isEmpty(accountTypes)) {
            return;
        }
        Date checkTime = new Date(param.getCheckTime());
        List<Integer> coinIds = param.getCoinIds();
        for (Byte accountType : accountTypes) {
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
            BaseRequest baseRequest = getBaseRequest(param, accountTypeEnum);
            // 获取三方用户资产
            List<BillCoinUserProperty> thirdPartyBillCoinUserPropertyList = accountAssetsServiceFactory
                    .queryUserAssets(accountType, Arrays.asList(param.getUserId()), baseRequest, QueryUserAssetsSceneEnum.INIT);
            Map<Integer, BillCoinUserProperty> thirdPartyUserAssertsMap = thirdPartyBillCoinUserPropertyList.stream()
                    .collect(Collectors.toMap(BillCoinUserProperty::getCoinId, Function.identity()));

            // 获取当前对账用户资产
            Map<Integer, BigDecimal> userAssetMap = billCoinUserPropertyService
                    .getUserSnapshotAssets(param.getUserId(), Arrays.asList(accountType), checkTime, coinIds);
            for (Map.Entry<Integer, BillCoinUserProperty> assetEntry : thirdPartyUserAssertsMap.entrySet()) {
                Integer coinId = assetEntry.getKey();
                if (CollectionUtils.isNotEmpty(coinIds) && !coinIds.contains(coinId)) {
                    continue;
                }
                BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountType);
                BigDecimal thirdPartyUserAsset = billCheckService.getPropSumByUserProperty(assetEntry.getValue());
                BigDecimal userAsset = userAssetMap.get(coinId);
                if (!NumberUtil.equal(thirdPartyUserAsset, userAsset)) {
                    log.error("checkUserAssets userId:{} checkTime:{} accountType:{} coinId:{} reconciliation total assets:{}, third party total assets:{}",
                            param.getUserId(), DateUtil.date2str(checkTime), accountType, coinId, userAsset, thirdPartyUserAsset);
                }
            }
            // 判断是否有对账用户资产存在，而三方不存在
            Collection<Integer> subtractCoinIds = CollectionUtil.subtract(userAssetMap.keySet(), thirdPartyUserAssertsMap.keySet());
            if (CollectionUtils.isNotEmpty(subtractCoinIds)) {
                log.error("checkUserAssets userId:{} checkTime:{} accountType:{} coinIds:{} reconciliation total assets exists but third party not exists",
                        param.getUserId(), DateUtil.date2str(checkTime), accountType, JSON.toJSONString(subtractCoinIds));
            }
        }
    }

    /**
     * 获取基础请求对象
     *
     * @param param
     * @param accountTypeEnum
     * @return
     */
    private BaseRequest getBaseRequest(UserAssetsCompareParam param, AccountTypeEnum accountTypeEnum) {
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setBeginTime(param.getCheckTime());
        baseRequest.setEndTime(param.getCheckTime());
        baseRequest.setAccountType(accountTypeEnum.getCode());
        baseRequest.setAccountParam(accountTypeEnum.getAccountParam());
        baseRequest.setMaxId(1L);
        return baseRequest;
    }

    private Map<Integer, AssetsBillCoinTypeProperty> collectCoinIdTypeAssets(Map<Integer, AssetsBillCoinTypeProperty> assetsMap, AssetsBillCoinTypeProperty assetsBillCoinTypeProperty) {
        if (Objects.isNull(assetsMap)) {
            assetsMap = new HashMap<>();
        }
        if (assetsMap.get(assetsBillCoinTypeProperty.getCoinId()) != null) {
            AssetsBillCoinTypeProperty userProperty = assetsMap.get(assetsBillCoinTypeProperty.getCoinId());
            BigDecimal totalBalance = getOneBillCoinTotalAssets(assetsBillCoinTypeProperty);
            userProperty.setProp1(userProperty.getProp1().add(totalBalance));
            assetsMap.put(assetsBillCoinTypeProperty.getCoinId(), userProperty);
        } else {
            AssetsBillCoinTypeProperty assetProperty = new AssetsBillCoinTypeProperty();
            BigDecimal totalBalance = getOneBillCoinTotalAssets(assetsBillCoinTypeProperty);
            assetProperty.setProp1(totalBalance);
            assetProperty.setCoinId(assetsBillCoinTypeProperty.getCoinId());
            assetsMap.put(assetsBillCoinTypeProperty.getCoinId(), assetProperty);
        }
        return assetsMap;
    }

    private BigDecimal getOneBillCoinTotalAssets(AssetsBillCoinTypeProperty bill) {
        if (bill.getBizType().startsWith(AccountTypeEnum.LEVER_FULL.getBizTypePrefix())
                || bill.getBizType().startsWith(AccountTypeEnum.LEVER_ONE.getBizTypePrefix())) {
            return bill.getProp1().add(bill.getProp2());
        }
        return bill.getProp1().add(bill.getProp2()).add(bill.getProp3())
                .add(bill.getProp4()).add(bill.getProp5());
    }

}