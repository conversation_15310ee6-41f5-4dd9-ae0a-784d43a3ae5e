package com.upex.reconciliation.service.service.client.cex.enmus;


public interface IReconCexErrorCode {

    String UNKNOW_ERROR = "UNKNOW_ERROR";
    String ILLEGAL_PARAMS = "ILLEGAL_PARAMS";
    String ILLEGAL_CEXTYPE = "ILLEGAL_CEXTYPE";
    String ILLEGAL_USERTYPE = "ILLEGAL_USERTYPE";
    String ILLEGAL_USETYPE = "ILLEGAL_USETYPE";
    String ILLEGAL_TRADETYPE = "ILLEGAL_TRADETYPE";
    String USERID_CANNOT_BENULL="USERID_CANNOT_BENULL";
    String PARENT_USERID_CANNOT_BENULL="PARENT_USERID_CANNOT_BENULL";
    String EMAIL_CANNOT_BENULL="EMAIL_CANNOT_BENULL";
    String APIKEY_LABEL_CANNOT_BENULL="APIKEY_LABEL_CANNOT_BENULL";
    String APIKEY_CANNOT_BENULL="APIKEY_CANNOT_BENULL";
    String APIKEY_PUB_SECRET_CANNOT_BENULL="APIKEY_PUB_SECRET_CANNOT_BENULL";
    String IF_ASSET_MONITOR_CANNOT_BENULL="IF_ASSET_MONITOR_CANNOT_BENULL";
    String USER_MANAGER_STATUS_CANNOT_BENULL="USER_MANAGER_STATUS_CANNOT_BENULL";
    String APIKEY_ID_CANNOT_BENULL="APIKEY_ID_CANNOT_BENULL";
    String THIRD_ASSET_TYPE_CANNOT_BENULL="THIRD_ASSET_TYPE_CANNOT_BENULL";
    String COINNAME_CANNOT_BENULL="COINNAME_CANNOT_BENULL";
    String ID_CANNOT_BENULL="ID_CANNOT_BENULL";
}
