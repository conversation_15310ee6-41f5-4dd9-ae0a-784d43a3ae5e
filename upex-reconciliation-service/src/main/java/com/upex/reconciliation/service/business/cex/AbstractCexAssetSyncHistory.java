package com.upex.reconciliation.service.business.cex;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.CexAssetConfig;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.service.CexAssetconfigService;
import com.upex.reconciliation.service.service.client.cex.enmus.CexAssetHistoryTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.CexAssetSyncEnum;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Date;
@Slf4j
public abstract class AbstractCexAssetSyncHistory implements ICexAssetSyncHistory{

    @Resource
    CexAssetconfigService cexAssetconfigService;


    @Override
    public void syncAssetHistory(CexAssetConfig cexAssetConfig,ThirdCexUserConfig userConfig, Date startTime, Date endTime, Date checkSyncTime) {

    }

    @Override
    public CexAssetHistoryTypeEnum getAssetHistoryType() {
        return null;
    }

    public void saveModAssetConfig(CexAssetConfig cexAssetConfig, CexAssetHistoryTypeEnum cexAssetHistoryTypeEnum, ThirdCexUserConfig userConfig, Date checkSyncTime) {
            if (cexAssetConfig == null) {
                cexAssetConfig = new CexAssetConfig();
                cexAssetConfig.setAssetHistoryType(cexAssetHistoryTypeEnum.getValue());
                cexAssetConfig.setCreateTime(new Date());
                cexAssetConfig.setUpdateTime(new Date());
                cexAssetConfig.setCexUserId(userConfig.getCexUserId());
                cexAssetConfig.setCexType(userConfig.getCexType());
                cexAssetConfig.setStatus(CexAssetSyncEnum.INIT_SYNC.getValue());
                cexAssetConfig.setCheckSyncTime(checkSyncTime);
                cexAssetconfigService.insert(cexAssetConfig);
            } else {
                cexAssetConfig.setStatus(CexAssetSyncEnum.NORMAL_SYNC.getValue());
                cexAssetConfig.setCheckSyncTime(checkSyncTime);
                cexAssetConfig.setUpdateTime(new Date());
                cexAssetconfigService.update(cexAssetConfig);
            }
        log.info("ModConfigFinish,cexAssetHistoryTypeEnum:{},userId:{}", cexAssetHistoryTypeEnum.getName(), userConfig.getCexUserId());
    }
}
