package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.upex.data.risk.dto.params.EventCheckParam;
import com.upex.data.risk.dto.results.EventCheckResult;
import com.upex.data.risk.facade.inner.RiskEventCheckFacade;
import com.upex.reconciliation.facade.model.ReconBillUserVo;
import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import com.upex.reconciliation.service.business.BaseTaskFactory;
import com.upex.reconciliation.service.business.RiskDataService;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.RiskUploadDataConstants;
import com.upex.reconciliation.service.common.thread.BaseAsyncTaskManager;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.dto.SelectCheckForTheResultsDto;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.utils.task.BaseTask;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RiskDataServiceImpl implements RiskDataService {

    @Resource
    private SerialNoGenerator serialNoGenerator;

    @Autowired
    private RiskEventCheckFacade riskEventCheckFacade;

    @Resource(name = "billBusinessTaskManager")
    private BaseAsyncTaskManager billBusinessTaskManager;

    @Override
    public void riskUploadDataForWithdraw(ReconCheckResultsParams checkResultsParams, ReconBillUserVo billUserVo) {
        if (StringUtils.isBlank(checkResultsParams.getRecordCode())) {
            return;
        }
        SelectCheckForTheResultsDto selectCheckForTheResultsDto = new SelectCheckForTheResultsDto();
        selectCheckForTheResultsDto.setUserId(checkResultsParams.getUserId());
        selectCheckForTheResultsDto.setRecordCode(checkResultsParams.getRecordCode());
        selectCheckForTheResultsDto.setCheckResult(billUserVo.isPass());
        selectCheckForTheResultsDto.setEventTime(DateUtil.get(BillConstants.ONE, new Date()));
        EventCheckParam eventCheckParam = new EventCheckParam();
        eventCheckParam.setBizId(String.valueOf(serialNoGenerator.nextNo()));
        eventCheckParam.setEventCode(RiskUploadDataConstants.WITHDRAW_STEP_NO);
        eventCheckParam.setParams(JSONObject.toJSONString(selectCheckForTheResultsDto));

        // 上报参数
        String jsonString = JSONObject.toJSONString(eventCheckParam);

        // 验证是否打开链路上报
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        if (Objects.isNull(globalBillConfig) || !globalBillConfig.isLinkUploadSwitch()) {
            log.info("riskLinkUploadDataHandle globalBillConfig is null or linkUploadSwitch is false,eventCheckParam:{}", jsonString);
            return;
        }

        // 是否同步执行
        if (globalBillConfig.isSyncExecuteLinkUploadOpen()) {
            log.info("riskLinkUploadDataHandle() syncExecuteLinkUploadOpen:{},eventCheckParam:{}", globalBillConfig.isSyncExecuteLinkUploadOpen(), jsonString);
            this.riskLinkUploadDataHandle(eventCheckParam);
        } else {
            log.info("billBusinessTaskManager.submit() syncExecuteLinkUploadOpen:{},eventCheckParam:{}", globalBillConfig.isSyncExecuteLinkUploadOpen(), jsonString);
            BaseTask baseTask = BaseTaskFactory.createBaseTask(eventCheckParam, this::riskLinkUploadDataHandle);
            billBusinessTaskManager.submit(baseTask);
        }
    }

    public void riskLinkUploadDataHandle(EventCheckParam eventCheckParam) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            EventCheckResult eventCheckResult = riskEventCheckFacade.eventCheck(eventCheckParam);
            log.info("riskLinkUploadDataHandle-riskEventCheckFacade.eventCheck() eventCheckParam：{},eventCheckResult：{},time:{}", JSONObject.toJSONString(eventCheckParam), JSONObject.toJSONString(eventCheckResult), stopwatch.stop());
        } catch (Exception e) {
            log.error("RiskDataServiceImpl.riskLinkUploadDataHandle,riskLinkUpLoadDataParam:{}", JSONObject.toJSONString(eventCheckParam), e);
        }

    }


}
