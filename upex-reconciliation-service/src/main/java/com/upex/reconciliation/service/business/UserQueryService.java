package com.upex.reconciliation.service.business;

import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * 用户及子账户服务
 *
 * <AUTHOR>
 * @Date 2025/4/24
 */
public interface UserQueryService {

    /**
     * 根据parentId及用户类型获取所有子账户id列表
     *
     * @param parentId
     * @param childTypeList
     * @param pageSize
     * @param maxTotal
     * @return
     */
    Pair<Boolean, List<Long>> getChildListByParentId(Long parentId, List<Integer> childTypeList, Integer pageSize, Integer maxTotal);

}
