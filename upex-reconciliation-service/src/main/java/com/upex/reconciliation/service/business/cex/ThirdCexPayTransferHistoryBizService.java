package com.upex.reconciliation.service.business.cex;

import com.google.common.collect.Lists;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.AlarmTemplateEnum;
import com.upex.reconciliation.service.dao.cex.entity.CexAssetConfig;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexPayTransferHistory;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUserConfig;
import com.upex.reconciliation.service.model.config.ApolloThirdCexAssetConfig;
import com.upex.reconciliation.service.service.ThirdCexPayTransferHistoryService;
import com.upex.reconciliation.service.service.ThirdCexUserService;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.UserPayTransferHistoryListReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import com.upex.reconciliation.service.service.client.cex.enmus.CexAssetHistoryTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.CexTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.WithdrawLegalEnum;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.utils.task.BaseTask;
import com.upex.utils.task.TaskManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ThirdCexPayTransferHistoryBizService extends AbstractCexAssetSyncHistory implements ICexAssetSyncHistory{

    @Resource
    ThirdCexPayTransferHistoryService thirdCexPayTransferHistoryService;

    @Resource
    CexApiService cexApiService;

    @Resource
    ThirdCexUserService thirdCexUserService;

    @Resource(name = "thirdCexTaskManager")
    TaskManager taskManager;

    @Resource
    AlarmNotifyService alarmNotifyService;

    @Resource
    BillDbHelper billDbHelper;



    public List<ThirdCexPayTransferHistory> queryPayTransferHistoryAndSync(UserPayTransferHistoryListReq userPayTransferHistoryListReq) {
        CommonRes<List<ThirdCexPayTransferHistory>> commonRes = cexApiService.queryPayTransferHistory(userPayTransferHistoryListReq);
        List<ThirdCexPayTransferHistory> thirdCexPayTransferHistories = new ArrayList<>();
        if (commonRes.getSuccess() && CollectionUtils.isNotEmpty(commonRes.getData())) {
            for (ThirdCexPayTransferHistory payTransferHistoryInnerRes : commonRes.getData()) {
                ThirdCexPayTransferHistory payTransferHistory = payTransferHistoryInnerRes;
                payTransferHistory.setCheckSyncTime(userPayTransferHistoryListReq.getCheckSyncTime());
                thirdCexPayTransferHistories.add(payTransferHistory);
            }
        }
        return thirdCexPayTransferHistories;
    }

    public void checkPayTransferRecord() throws InterruptedException {
        List<ThirdCexPayTransferHistory> unCheckWithdrawRecords =thirdCexPayTransferHistoryService.selectUnCheckWithdraw(CexTypeEnum.BINANCE.getType(), WithdrawLegalEnum.UNCHECK.getValue());
        while(CollectionUtils.isNotEmpty(unCheckWithdrawRecords)) {
            for (ThirdCexPayTransferHistory record : unCheckWithdrawRecords) {
                ThirdCexUser thirdCexUser = thirdCexUserService.selectByCexTypeAndUserId(record.getCexType(), record.getCexUserId());
                if (thirdCexUser == null) {
                    record.setIsLegal(WithdrawLegalEnum.ILLEGAL.getValue());
                    alarmNotifyService.cexAlarm(AlarmTemplateEnum.THIRD_CEX_WITHDRAW_ADDRESS_ILLEGAL, record.getCexUserId(),CexTypeEnum.fromType(record.getCexType()).getName(), record.getTransferId());
                } else {
                    record.setIsLegal(WithdrawLegalEnum.NORMAL.getValue());
                }
                thirdCexPayTransferHistoryService.checkWithdraw(record.getId(),record.getIsLegal());
            }
            unCheckWithdrawRecords=  thirdCexPayTransferHistoryService.selectUnCheckWithdraw(CexTypeEnum.BINANCE.getType(), WithdrawLegalEnum.UNCHECK.getValue());
            Thread.sleep(1000);
        }
    }

    @Override
    public void syncAssetHistory(CexAssetConfig cexAssetConfig,ThirdCexUserConfig userConfig, Date startTime, Date endTime, Date checkSyncTime) {
        UserPayTransferHistoryListReq userPayTransferHistoryListReq = new UserPayTransferHistoryListReq(userConfig.getCexType(), userConfig.getCexUserId(), userConfig.getApiKey(), userConfig.getApiKeyPrivate(), startTime, endTime,checkSyncTime);
        List<ThirdCexPayTransferHistory>  cexPayTransferHistories=queryPayTransferHistoryAndSync(userPayTransferHistoryListReq);
        ApolloThirdCexAssetConfig config = ReconciliationApolloConfigUtils.getThirdCexAssetConfig();
        billDbHelper.doDbOpInReconMaster(()->{
            if(CollectionUtils.isNotEmpty(cexPayTransferHistories)) {
                List<List<ThirdCexPayTransferHistory>> partisionPayTransferHistories = Lists.partition(cexPayTransferHistories, config.getSqlInsertSize());
                for (List<ThirdCexPayTransferHistory> partision : partisionPayTransferHistories) {
                    thirdCexPayTransferHistoryService.batchInsert(partision);
                }
            }
            saveModAssetConfig(cexAssetConfig, CexAssetHistoryTypeEnum.PAY_TRANSFER, userConfig, checkSyncTime);
            return null;
        });
    }

    @Override
    public CexAssetHistoryTypeEnum getAssetHistoryType() {
        return CexAssetHistoryTypeEnum.PAY_TRANSFER;
    }
}
