package com.upex.reconciliation.service.service.client.cex.dto.req.common;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.service.client.cex.enmus.CexTypeEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.IReconCexErrorCode;
import com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum;
import com.upex.reconciliation.service.service.client.cex.enmus.UserManagerStatusEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;

import static com.upex.reconciliation.service.service.client.cex.enmus.IReconCexErrorCode.USER_MANAGER_STATUS_CANNOT_BENULL;

@Data
public class UserManagerStatusModReq {

    /**
     * 交易所类型（1: Binance, 2: OKX）
     */
    @NotNull(message = IReconCexErrorCode.ILLEGAL_CEXTYPE)
    private Integer cexType;

    /**
     * 交易所用户ID
     */
    @NotNull(message = IReconCexErrorCode.USERID_CANNOT_BENULL)
    private String cexUserId;

    /**
     * 状态（1: 启用, 0: 禁用）
     */
    @NotNull(message = USER_MANAGER_STATUS_CANNOT_BENULL)
    private UserManagerStatusEnum status;


    public void setCexType(Integer cexType) {
        CexTypeEnum cexTypeEnum = CexTypeEnum.fromType(cexType);
        if (cexTypeEnum == null) {
            throw new ApiException(ReconCexExceptionEnum.ILLEGAL_CEXTYPE);
        }
        this.cexType = cexType;
    }
}
