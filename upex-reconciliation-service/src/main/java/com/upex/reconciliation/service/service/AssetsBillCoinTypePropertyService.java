package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.constants.enums.AssetsCheckTypeEnum;
import com.upex.reconciliation.service.dao.entity.AssetsBillCoinTypeProperty;
import com.upex.reconciliation.service.dao.mapper.AssetsBillCoinTypePropertyMapper;
import com.upex.reconciliation.service.common.BillDbHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
public class AssetsBillCoinTypePropertyService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource
    private AssetsBillCoinTypePropertyMapper assetsBillCoinTypePropertyMapper;

    public int batchUpdate(List<AssetsBillCoinTypeProperty> list, String billCheckType, String billCheckParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinTypePropertyMapper.batchUpdate(list, billCheckType, billCheckParam));
    }

    public int batchInsert(List<AssetsBillCoinTypeProperty> list, String billCheckType, String billCheckParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinTypePropertyMapper.batchInsert(list, billCheckType, billCheckParam));
    }

    public Boolean deleteByCheckTime(String billCheckType, String billCheckParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinTypePropertyMapper.deleteByCheckTime(billCheckType, billCheckParam, checkTime, batchSize));
    }

    public List<AssetsBillCoinTypeProperty> selectByCheckTime(Date checkTime,
                                                              String bizType,
                                                              String billCheckType,
                                                              String billCheckParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinTypePropertyMapper.selectByCheckTime(checkTime, bizType, billCheckType, billCheckParam));
    }


    public Date selectLastCheckOkTime(Date checkTime,
                                      String billCheckType,
                                      String billCheckParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinTypePropertyMapper.selectLastCheckOkTime(checkTime, billCheckType, billCheckParam));
    }


    public List<AssetsBillCoinTypeProperty> selectByCoinIdCheckTime(Date checkTime,
                                                                    Integer coinId,
                                                                    String billCheckType,
                                                                    String billCheckParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinTypePropertyMapper.selectByCoinIdCheckTime(checkTime, coinId, billCheckType, billCheckParam));
    }

    public Long getIdByCheckTime(String accountType, String accountParam, Date checkTime, String operation) {
        String operationNew = operation;
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinTypePropertyMapper.getIdByCheckTime(accountType, accountParam, checkTime, operationNew));
    }

    public boolean deleteByMaxId(String accountType, String accountParam, Long maxId, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinTypePropertyMapper.deleteByMaxId(accountType, accountParam, maxId, batchSize));
    }

    public Long countByCheckTime(Date checkTime,
                                 String bizType,
                                 String billCheckType,
                                 String billCheckParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinTypePropertyMapper.countByCheckTime(checkTime, bizType, billCheckType, billCheckParam));
    }

    public List<AssetsBillCoinTypeProperty> selectPageByCheckTime(Date checkTime,
                                                                  String bizType,
                                                                  String billCheckType,
                                                                  String billCheckParam,
                                                                  Long startOffset,
                                                                  Integer pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinTypePropertyMapper.selectPageByCheckTime(checkTime, bizType, billCheckType, billCheckParam, startOffset, pageSize));
    }

    public Map<Integer, BigDecimal> getCoinTypeSnapshotAssets(String assetsType, Date snapshotTime, List<Integer> coinIdsList, List<String> bizTypeList) {
        // 先查出snapshotTime之前的资产数据
        AssetsCheckTypeEnum assetsCheckTypeEnum = AssetsCheckTypeEnum.toEnum(assetsType);
        List<AssetsBillCoinTypeProperty> assetsBillCoinTypePropertyList = dbHelper.doDbOpInReconMaster(() -> assetsBillCoinTypePropertyMapper.getCoinTypeSnapshotAssets(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam(), snapshotTime, coinIdsList, bizTypeList));
        Map<Integer, List<AssetsBillCoinTypeProperty>> groupByCoinIdMap = assetsBillCoinTypePropertyList.stream().collect(Collectors.groupingBy(AssetsBillCoinTypeProperty::getCoinId));
        // 计算每个币种的资产总和
        Map<Integer, BigDecimal> assetsMap = new HashMap<>();
        groupByCoinIdMap.forEach((coinId, coinTypePropertyList) -> {
            coinTypePropertyList.forEach(coinTypeProperty -> {
                BigDecimal assets = assetsMap.getOrDefault(coinId, BigDecimal.ZERO);
                assetsMap.put(coinId, assets.add(coinTypeProperty.getPropSum()));
            });
        });
        return assetsMap;
    }

    public Boolean deleteByLtCheckTime(String billCheckType, String billCheckParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinTypePropertyMapper.deleteByLtCheckTime(billCheckType, billCheckParam, checkTime, batchSize));
    }
}
