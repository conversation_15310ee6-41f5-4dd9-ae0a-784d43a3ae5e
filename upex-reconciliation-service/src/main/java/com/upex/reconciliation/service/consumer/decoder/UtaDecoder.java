package com.upex.reconciliation.service.consumer.decoder;

import com.alibaba.otter.canal.protocol.FlatMessage;
import com.google.common.collect.Sets;
import com.upex.config.coin.SpotCoinDTO;
import com.upex.config.facade.coin.CoinConfigService;
import com.upex.reconciliation.service.model.domain.UtaBillChangeModel;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.unified.account.dto.enums.BizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class UtaDecoder extends AbstractMessageDecoder {
    @Resource
    private CoinConfigService coinConfigService;

    private static final Set<Integer> noReconciliationList = Sets.newHashSet(
            BizTypeEnum.CANCEL_OPEN_LONG.getCode(),
            BizTypeEnum.CANCEL_OPEN_SHORT.getCode(),
            BizTypeEnum.CANCEL_CLOSE_LONG.getCode(),
            BizTypeEnum.CANCEL_CLOSE_SHORT.getCode(),
            BizTypeEnum.CLOSE_LONG_LOCK.getCode(),
            BizTypeEnum.CLOSE_SHORT_LOCK.getCode(),
            BizTypeEnum.FORCE_CLOSE_LONG_LOCK.getCode(),
            BizTypeEnum.FORCE_CLOSE_SHORT_LOCK.getCode(),
            BizTypeEnum.BURST_CLOSE_LONG_LOCK.getCode(),
            BizTypeEnum.BURST_CLOSE_SHORT_LOCK.getCode(),
            BizTypeEnum.OFFSET_REDUCE_CLOSE_LONG_LOCK.getCode(),
            BizTypeEnum.OFFSET_REDUCE_CLOSE_SHORT_LOCK.getCode(),
            BizTypeEnum.BUY_LOCK.getCode(),
            BizTypeEnum.SELL_LOCK.getCode(),
            BizTypeEnum.FORCE_BUY_LOCK_SSM.getCode(),
            BizTypeEnum.FORCE_SELL_LOCK_SSM.getCode(),
            BizTypeEnum.BURST_BUY_LOCK_SSM.getCode(),
            BizTypeEnum.BURST_SELL_LOCK_SSM.getCode());

    @Override
    public List<CommonBillChangeData> doMessageDecode(List<Map<String, String>> dataList, FlatMessage flatMessage, Integer partition, Long offset, Byte accountType) {
        List<CommonBillChangeData> commonBillChangeDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (Map<String, String> map : dataList) {
                if (map.get("biz_type") != null) {
                    if (noReconciliationList.contains(Integer.parseInt(map.get("biz_type")))) {
                        continue;
                    }
                }

                UtaBillChangeModel model = UtaBillChangeModel.genarateBillChangeModel(map);
                CommonBillChangeData commonBillChangeData = model.getBillChangeData(accountType);
                commonBillChangeData.setPartition(partition);
                commonBillChangeData.setOffset(offset);
                SpotCoinDTO spotCoinDTO = coinConfigService.getCoin(commonBillChangeData.getCoinId());
                commonBillChangeData.setTokenId(spotCoinDTO.getCoinName());
                commonBillChangeDataList.add(commonBillChangeData);
            }
        }
        return commonBillChangeDataList;
    }
}
