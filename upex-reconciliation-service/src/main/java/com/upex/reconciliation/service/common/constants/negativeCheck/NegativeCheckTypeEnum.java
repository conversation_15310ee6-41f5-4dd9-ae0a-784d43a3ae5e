package com.upex.reconciliation.service.common.constants.negativeCheck;

import lombok.Getter;

@Getter
public enum NegativeCheckTypeEnum {
    /**
     * 个人总资产（如果是具体币种资产，则是币种id，不在此枚举中体现）
     */
    TOTAL_ASSETS(-1),
    ;
    private final int code;

    NegativeCheckTypeEnum(int code) {
        this.code = code;
    }

    public static String getCheckTypeName(int key) {
        if (TOTAL_ASSETS.code == key) {
            return TOTAL_ASSETS.name();
        }
        return "coin";
    }
}
