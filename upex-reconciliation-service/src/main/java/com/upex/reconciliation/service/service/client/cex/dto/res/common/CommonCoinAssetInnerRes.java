package com.upex.reconciliation.service.service.client.cex.dto.res.common;

import com.upex.reconciliation.service.service.client.cex.enmus.CexTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommonCoinAssetInnerRes  implements ICommonBaseRes{
    /**
     * 币种名称
     */
    private String coinName;
    /**
     * 总余额
     */
    private BigDecimal totalBalance;
    /**
     * 可用余额
     */
    private BigDecimal availableBalance;
    /**
     * 锁仓余额
     */
    private BigDecimal locked;
    /**
     * 冻结余额
     */
    private BigDecimal freeze;
    private BigDecimal withdrawing;
    private BigDecimal ipoable;
    private BigDecimal btcValuation;
    /**
     * 合约保证金
     */
    private BigDecimal marginBalance;
    /*
    *杠杆已借
     */
    private BigDecimal borrowedBalance;

    private Integer thirdAssetType;
    /**
     * 交易所类型
     * @see CexTypeEnum
     */
    private Integer cexType;
    /**
     * 更新时间，如api返回则用api返回，没有不设置
     */
    private Long changeTime;

    private BigDecimal usdtBalance;

    private Date createTime;


    public CommonCoinAssetInnerRes(String coinName, BigDecimal totalBalance, BigDecimal avaiableBalance, BigDecimal borrowedBalance, BigDecimal marginBalance, Date changeTime, Integer accountType,Date createTime,Integer cexType) {
        this.coinName = coinName;
        this.totalBalance = totalBalance;
        this.availableBalance = avaiableBalance;
        this.borrowedBalance = borrowedBalance;
        this.marginBalance = marginBalance;
        this.changeTime = changeTime!=null?changeTime.getTime():null;
        this.thirdAssetType=accountType;
        this.createTime  = createTime;
        this.cexType = cexType;

    }
}
