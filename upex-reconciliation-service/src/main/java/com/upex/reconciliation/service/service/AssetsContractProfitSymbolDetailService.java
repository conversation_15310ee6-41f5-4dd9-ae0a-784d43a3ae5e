package com.upex.reconciliation.service.service;


import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.AssetsContractProfitSymbolDetail;
import com.upex.reconciliation.service.dao.mapper.AssetsContractProfitSymbolDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill_contract_profit_coin_detail(币种维度盈亏明细表)】的数据库操作Service
 * @createDate 2023-06-09 17:18:46
 */
@Slf4j
@Service
public class AssetsContractProfitSymbolDetailService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "assetsContractProfitSymbolDetailMapper")
    private AssetsContractProfitSymbolDetailMapper assetsContractProfitSymbolDetailMapper;

    public int batchInsert(String accountType, String accountParam, List<AssetsContractProfitSymbolDetail> billContractProfitCoinDetailList) {
        if (CollectionUtils.isNotEmpty(billContractProfitCoinDetailList)) {
            return dbHelper.doDbOpInReconMaster(() -> {
                return assetsContractProfitSymbolDetailMapper.batchInsert(accountType, accountParam, billContractProfitCoinDetailList);
            });
        }
        return 0;
    }

    public boolean deleteByCheckTime(String accountType, String accountParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitSymbolDetailMapper.deleteByCheckTime(accountType, accountParam, checkTime, batchSize));
    }

    public Boolean batchDelete(Long beginId, Long pageSize, String accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> {
            return assetsContractProfitSymbolDetailMapper.batchDelete(beginId, pageSize, accountType, accountParam);
        });
    }

    public List<AssetsContractProfitSymbolDetail> selectListByAccountTypeAndCheckTime(String accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> {
            return assetsContractProfitSymbolDetailMapper.selectListByAccountTypeAndCheckTime(accountType, accountParam, checkTime);
        });
    }

    public List<AssetsContractProfitSymbolDetail> selectLastValidListByAccountTypeAndCheckTime(String accountType, String accountParam, Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> {
            return assetsContractProfitSymbolDetailMapper.selectLastValidListByAccountTypeAndCheckTime(accountType, accountParam, checkTime);
        });
    }

    public int deleteAfterRecord(
            Date resetCheckTime,
            String accountType,
            String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitSymbolDetailMapper.deleteAfterRecord(resetCheckTime, accountType, accountParam));
    }

    public List<AssetsContractProfitSymbolDetail> getAllAfterRecord(
            Date resetCheckTime,
            String accountType,
            String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitSymbolDetailMapper.getAllAfterRecord(resetCheckTime, accountType, accountParam));
    }

    public int batchInsertHis(
            String accountType,
            String accountParam,
            List<AssetsContractProfitSymbolDetail> list) {
        return dbHelper.doDbOpInReconMaster(() -> assetsContractProfitSymbolDetailMapper.batchInsertHis(accountType, accountParam, list));
    }
}
