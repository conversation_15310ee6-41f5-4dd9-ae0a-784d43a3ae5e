package com.upex.reconciliation.service.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.BillCheckService;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.entity.BillCoinUserPropertyError;
import com.upex.reconciliation.service.dao.mapper.BillCoinUserPropertyMapper;
import com.upex.reconciliation.service.model.dto.MaxMinIdDTO;
import com.upex.reconciliation.service.model.dto.UserCoinIdDTO;
import com.upex.reconciliation.service.service.impl.BillCoinUserPropertySnapshotService;
import com.upex.utils.task.TaskManager;
import com.upex.utils.task.TaskVoidBatchResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BillCoinUserPropertyService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billCoinUserPropertyMapper")
    private BillCoinUserPropertyMapper billCoinUserPropertyMapper;
    @Resource
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    @Resource
    private BillCoinUserPropertySnapshotService billCoinUserPropertySnapshotService;
    @Resource
    private BillCoinUserPropertyAssetSnapshotService billCoinUserPropertyAssetSnapshotService;
    @Resource
    private BillCoinUserPropertyErrorService billCoinUserPropertyErrorService;
    @Resource(name = "taskManager")
    private TaskManager taskManager;

    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date checkTime, Long pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.deleteByCheckTime(accountType, accountParam, checkTime, pageSize));
    }

    public Boolean updateById(Byte accountType, String accountParam, BillCoinUserProperty billCoinUserProperty) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.updateById(accountType, accountParam, billCoinUserProperty));
    }

    public Boolean updateSpropById(Byte accountType, String accountParam, BillCoinUserProperty billCoinUserProperty) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.updateSpropById(accountType, accountParam, billCoinUserProperty));
    }

    public Boolean updateSelectiveById(Byte accountType, String accountParam, BillCoinUserProperty billCoinUserProperty) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.updateSelectiveById(accountType, accountParam, billCoinUserProperty));
    }

    public int batchUpdate(List<BillCoinUserProperty> updateList, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.batchUpdate(updateList, accountType, accountParam));
    }


    public BillCoinUserProperty selectById(Byte accountType, String accountParam, Long id) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectById(accountType, accountParam, id));
    }


    public int batchInsert(List<BillCoinUserProperty> records, Byte accountType, String accountParam) {
        if (CollectionUtils.isNotEmpty(records)) {
            return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.batchInsert(records, accountType, accountParam));
        }
        return 0;
    }

    public int batchInsertIgnore(List<BillCoinUserProperty> records, Byte accountType, String accountParam) {
        if (CollectionUtils.isNotEmpty(records)) {
            return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.batchInsertIgnore(records, accountType, accountParam));
        }
        return 0;
    }

    public Boolean batchDelete(Long beginId, Long pageSize, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.batchDelete(beginId, pageSize, accountType, accountParam));
    }

    public List<BillCoinUserProperty> selectByIds(Date checkTime, Integer coinId, List<Long> uids, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectByIds(checkTime, coinId, uids, accountType, accountParam));
    }


    public List<BillCoinUserProperty> selectRangeCheckTimeRecordPage(Integer accountType,
                                                                     String accountParam,
                                                                     Date startTime,
                                                                     Date endTime,
                                                                     Long minId,
                                                                     Long pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectRangeCheckTimeRecordPage(accountType, accountParam, startTime, endTime, minId, pageSize));
    }


    public List<BillCoinUserProperty> selectRangeCheckTimeRecord(Integer accountType,
                                                                 String accountParam,
                                                                 Date startTime,
                                                                 Date endTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectRangeCheckTimeRecord(accountType, accountParam, startTime, endTime));
    }


    public List<BillCoinUserProperty> selectAll(Integer accountType,
                                                String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectAll(accountType, accountParam));
    }

    public List<BillCoinUserProperty> selectUserLatestRecord(Integer accountType,
                                                             String accountParam,
                                                             Long userId,
                                                             Integer coinId) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectUserLatestRecord(accountType, accountParam, userId, coinId));
    }

    public List<BillCoinUserProperty> selectUserLatestAllRecord(Integer accountType,
                                                                String accountParam,
                                                                Long userId) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectUserLatestAllRecord(accountType, accountParam, userId));
    }


    public List<BillCoinUserProperty> selectUserCoinIds(Integer accountType,
                                                        String accountParam,
                                                        Long userId,
                                                        List<Integer> coinIds) {
        List<BillCoinUserProperty> list = dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectUserCoinIds(accountType, accountParam, userId, coinIds));
        return CollectionUtils.isNotEmpty(list) ? list : Collections.emptyList();
    }


    public BillCoinUserProperty selectUserCoinLatestRecord(Integer accountType,
                                                           String accountParam,
                                                           Long userId,
                                                           Integer coinId,
                                                           Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectUserCoinLatestRecord(accountType, accountParam, userId, coinId, checkTime));
    }


    public BillCoinUserProperty selectUserCoinRecord(Byte accountType,
                                                     String accountParam,
                                                     Long userId,
                                                     Integer coinId) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectUserCoinRecord(accountType, accountParam, userId, coinId));
    }

    /**
     * 批量查询用户
     *
     * @param accountType
     * @param accountParam
     * @param records      参数只用  user_id 和coin_id
     * @return 注意，值返回 id，user_id，coin_id
     */
    public List<UserCoinIdDTO> selectByUserAndCoins(Byte accountType,
                                                    String accountParam,
                                                    List<BillCoinUserProperty> records) {
        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectByUserAndCoins(accountType, accountParam, records));
    }

    public List<BillCoinUserProperty> selectByUserIdsAndCheckTime(List<Long> userIds,
                                                                  Date checkTime,
                                                                  Byte accountType,
                                                                  String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectByUserIdsAndCheckTime(userIds, checkTime, accountType, accountParam));
    }


    public List<Long> selectUserIds(Integer accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectUserIds(accountType, accountParam));
    }

    public BillCoinUserProperty selectTime(Long userId,
                                           Integer accountType,
                                           String accountParam,
                                           Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectTime(userId, accountType, accountParam, checkTime));

    }

    public BillCoinUserProperty selectBySingleUserIdLatest(Long fUid,
                                                           String accountParam,
                                                           Integer accountType) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectBySingleUserIdLatest(fUid, accountParam, accountType));

    }


    public BillCoinUserProperty selectCheckByUidAndBusinessTime(Long fUid,
                                                                Date queryTime,
                                                                String accountParam,
                                                                Integer accountType) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectCheckByUidAndBusinessTime(fUid, queryTime, accountParam, accountType));

    }


    public List<BillCoinUserProperty> selectCheckForTheResults(Long fUid,
                                                               String accountParam,
                                                               Integer accountType) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectCheckForTheResults(fUid, accountParam, accountType));

    }

    public List<Long> selectLastUserIdByCheckTime(Byte accountType,
                                                  String accountParam,
                                                  Date startTime,
                                                  Long startOffset,
                                                  Integer pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectLastUserIdByCheckTime(accountType, accountParam, startTime, startOffset, pageSize));
    }

    public Long countByGteCheckTime(Byte accountType,
                                    String accountParam,
                                    Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.countByGteCheckTime(accountType, accountParam, checkTime));
    }

    public List<Long> cursorSelectLastUserIdByCheckTime(Byte accountType,
                                                        String accountParam,
                                                        Date startTime) {
        return dbHelper.doDbOpInReconMasterTransaction(() -> {
            Set<Long> userIdList = new HashSet<>();
            try (Cursor<Long> userIdStream = billCoinUserPropertyMapper.cursorSelectLastUserIdByCheckTime(accountType, accountParam, startTime)) {
                for (Long userId : userIdStream) {
                    userIdList.add(userId);
                }
            } catch (IOException e) {
                throw new RuntimeException("billCoinUserPropertyMapper.cursorSelectLastUserIdByCheckTime error", e);
            }
            return new ArrayList<>(userIdList);
        });
    }

    /**
     * 获取当前表最大id
     *
     * @param accountType
     * @param accountParam
     * @return
     */
    public Long selectMaxId(Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectMaxId(accountType, accountParam));
    }

    /**
     * 查询用户某个时刻快照资产
     *
     * @param userId
     * @param accountTypeList
     * @param snapshotTime
     * @param coinIdsList
     * @return
     */
    public Map<Integer, BigDecimal> getUserSnapshotAssets(Long userId, List<Byte> accountTypeList, Date snapshotTime, List<Integer> coinIdsList) {
        Map<Integer, BigDecimal> assetsMap = new HashMap<>();
        for (Byte accountType : accountTypeList) {
            BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountType);
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
            List<Integer> coinIdList = coinIdsList;
            if (CollectionUtils.isEmpty(coinIdsList)) {
                List<BillCoinUserProperty> billCoinUserPropertyList = this.selectUserLatestAllRecord((int) accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), userId);
                coinIdList = billCoinUserPropertyList.stream().map(BillCoinUserProperty::getCoinId).collect(Collectors.toList());
            }
            if (!coinIdList.isEmpty()) {
                List<List<Integer>> coinIdListList = Lists.partition(coinIdList, 100);
                for (List<Integer> coinIdListTemp : coinIdListList) {
                    List<BillCoinUserProperty> billCoinUserPropertySnapshotList = billCoinUserPropertySnapshotService.selectCoinUserSnapshotAsset(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), userId, coinIdListTemp, snapshotTime);
                    billCoinUserPropertySnapshotList.forEach(billCoinUserProperty ->
                            assetsMap.put(billCoinUserProperty.getCoinId(), assetsMap.getOrDefault(billCoinUserProperty.getCoinId(), BigDecimal.ZERO).add(billCheckService.getPropSumByUserProperty(billCoinUserProperty))));
                }
            }
        }
        return assetsMap;
    }

    /**
     * 查询用户最新快照资产
     *
     * @param userId
     * @param accountType
     * @param accountParam
     * @return
     */
    public Pair<List<BillCoinUserProperty>, List<BillCoinUserProperty>> getLatestUserSnapshotAssets(Long userId, Byte accountType, String accountParam) {
        List<BillCoinUserProperty> billCoinUserPropertyList = this.selectUserLatestAllRecord((int) accountType, accountParam, userId);
        List<Integer> coinIdList = billCoinUserPropertyList.stream().map(BillCoinUserProperty::getCoinId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(coinIdList)) {
            return Pair.of(null, null);
        }
        Date checkTime = billCoinUserPropertyList.stream().map(BillCoinUserProperty::getCheckTime).max(Comparator.naturalOrder()).get();
        List<BillCoinUserProperty> billCoinUserPropertySnapshotList = new ArrayList<>();
        List<List<Integer>> coinIdListList = Lists.partition(coinIdList, 100);
        for (List<Integer> coinIdListTemp : coinIdListList) {
            List<BillCoinUserProperty> userSnapshotAssetList = billCoinUserPropertySnapshotService.selectCoinUserSnapshotAsset(accountType, accountParam, userId, coinIdListTemp, checkTime);
            if (CollectionUtil.isNotEmpty(userSnapshotAssetList)) {
                billCoinUserPropertySnapshotList.addAll(userSnapshotAssetList);
            }
        }
        return Pair.of(billCoinUserPropertyList, billCoinUserPropertySnapshotList);
    }

    public List<BillCoinUserProperty> selectCoinUserByStartAndEndId(Byte accountType, String accountParam, Long startId, Long endId) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectCoinUserByStartAndEndId(accountType, accountParam, startId, endId));
    }

    public Long selectMinId(Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectMinId(accountType, accountParam));
    }

    public MaxMinIdDTO selectMaxMinId(Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectMaxMinId(accountType, accountParam));
    }

    public Map<Long, Date> selectMinCreateTimeBatch(Byte accountType,
                                                    String accountParam, Long startId, Long endId) {
        List<Map<String, Object>> rawResults = dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectMinCreateTimeBatch(accountType, accountParam, startId, endId));
        Map<Long, Date> resultMap = new HashMap<>();
        for (Map<String, Object> row : rawResults) {
            Long userId = ((Number) row.get("userId")).longValue();
            Date createTime = (Date) row.get("minCreateTime");
            resultMap.put(userId, createTime);
        }
        return resultMap;
    }

    public Map<Long, Date> selectUserMinCreateTimeBatch(Byte accountType,
                                                        String accountParam, Integer pageSize) {
        Map<Long, Date> existUserCreateTimeMap = Maps.newHashMap();
        Long minId = selectMinId(accountType, accountParam);
        Long maxId = selectMaxId(accountType, accountParam);
        Long startId = minId, endId;
        while (startId <= maxId) {
            endId = startId + pageSize;
            Map<Long, Date> userCreateTimeMap = selectMinCreateTimeBatch(accountType, accountParam, startId, endId);
            if (!userCreateTimeMap.isEmpty()) {
                userCreateTimeMap.forEach((userId, createTime) -> {
                    Date existCreateTime = existUserCreateTimeMap.get(userId);
                    if (existCreateTime == null || existCreateTime.after(createTime)) {
                        existUserCreateTimeMap.put(userId, createTime);
                    }
                });
            }
            startId = endId;
        }
        return existUserCreateTimeMap;
    }


    public void repairCoinUserProperty(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        String action = jsonObject.getString("action");
        Byte accountType = jsonObject.getByte("accountType");
        Long coinUserPropertyId = jsonObject.getLong("coinUserPropertyId");
        Long snapshotCoinUserPropertyId = jsonObject.getLong("snapshotCoinUserPropertyId");
        Long assetSnapshotCoinUserPropertyId = jsonObject.getLong("assetSnapshotCoinUserPropertyId");
        BigDecimal prop1 = jsonObject.getBigDecimal("prop1");
        BigDecimal prop2 = jsonObject.getBigDecimal("prop2");
        BigDecimal prop3 = jsonObject.getBigDecimal("prop3");
        BigDecimal prop4 = jsonObject.getBigDecimal("prop4");
        BigDecimal prop5 = jsonObject.getBigDecimal("prop5");
        BigDecimal changeProp1 = jsonObject.getBigDecimal("changeProp1");
        BigDecimal changeProp2 = jsonObject.getBigDecimal("changeProp2");
        BigDecimal changeProp3 = jsonObject.getBigDecimal("changeProp3");
        BigDecimal changeProp4 = jsonObject.getBigDecimal("changeProp4");
        BigDecimal changeProp5 = jsonObject.getBigDecimal("changeProp5");
        String params = jsonObject.getString("params");
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        if ("update".equals(action)) {
            if (accountTypeEnum != null && (coinUserPropertyId != null || snapshotCoinUserPropertyId != null)) {
                BillCoinUserProperty billCoinUserProperty = new BillCoinUserProperty();
                billCoinUserProperty.setProp1(prop1);
                billCoinUserProperty.setProp2(prop2);
                billCoinUserProperty.setProp3(prop3);
                billCoinUserProperty.setProp4(prop4);
                billCoinUserProperty.setProp5(prop5);
                billCoinUserProperty.setChangeProp1(changeProp1);
                billCoinUserProperty.setChangeProp2(changeProp2);
                billCoinUserProperty.setChangeProp3(changeProp3);
                billCoinUserProperty.setChangeProp4(changeProp4);
                billCoinUserProperty.setChangeProp5(changeProp5);
                billCoinUserProperty.setParams(params);
                if (coinUserPropertyId != null) {
                    billCoinUserProperty.setId(coinUserPropertyId);
                    this.updateSelectiveById(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), billCoinUserProperty);
                }
                if (snapshotCoinUserPropertyId != null) {
                    billCoinUserProperty.setId(snapshotCoinUserPropertyId);
                    billCoinUserPropertySnapshotService.updateSelectiveById(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), billCoinUserProperty);
                }
                if (assetSnapshotCoinUserPropertyId != null) {
                    billCoinUserProperty.setId(assetSnapshotCoinUserPropertyId);
                    billCoinUserPropertyAssetSnapshotService.updateSelectiveById(accountTypeEnum.getCode(), accountTypeEnum.getAccountParam(), billCoinUserProperty);
                }
            }
        }
    }

    /**
     * 修复业务线sprop
     *
     * @param jobParam
     */
    public void repairCoinUserPropertySprop(String jobParam) {
        JSONObject jsonObject = JSONObject.parseObject(jobParam);
        String action = jsonObject.getString("action");
        Byte accountType = jsonObject.getByte("accountType");
        Long batchSize = jsonObject.getLong("batchSize") == null ? 1000L : jsonObject.getLong("batchSize");
        Long concurrentSize = jsonObject.getLong("concurrentSize") == null ? 10L : jsonObject.getLong("concurrentSize");
        Long repairUserId = jsonObject.getLong("userId");
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        if ("init".equalsIgnoreCase(action) || "repairUser".equals(action)) {
            StopWatch stopWatch = new StopWatch();
            // 搬家修复数据
            if ("init".equalsIgnoreCase(action)) {
                // 获取分页情况
                stopWatch.start("selectMaxMinId");
                MaxMinIdDTO maxMinIdDTO = this.selectMaxMinId(accountType, accountTypeEnum.getAccountParam());
                log.info("BillCoinUserPropertyService.repairCoinUserPropertySprop init accountType:{} maxMinIdDTO:{}", accountType, JSON.toJSONString(maxMinIdDTO));
                stopWatch.stop();
                // 更新用户数据
                stopWatch.start("billCoinUserPropertyMapper.updatePropToSprop");
                TaskVoidBatchResult queryResultIsEmpty = taskManager.forEachSubmitBatchAndWait(maxMinIdDTO.buildIdSegments(batchSize), (Long[] idSegment) -> {
                    List<BillCoinUserProperty> billCoinUserPropertyList = dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectCoinUserByIdSegment(accountType, accountTypeEnum.getAccountParam(), idSegment[0], idSegment[1]));
                    billCoinUserPropertyList.forEach(billCoinUserProperty -> {
                        billCoinUserProperty.setSprop1(billCoinUserProperty.getProp1());
                        billCoinUserProperty.setSprop2(billCoinUserProperty.getProp2());
                        billCoinUserProperty.setSprop3(billCoinUserProperty.getProp3());
                        billCoinUserProperty.setSprop4(billCoinUserProperty.getProp4());
                        billCoinUserProperty.setSprop5(billCoinUserProperty.getProp5());
                        billCoinUserProperty.setSprop6(billCoinUserProperty.getProp6());
                        billCoinUserProperty.setSprop7(billCoinUserProperty.getProp7());
                        billCoinUserProperty.setSprop8(billCoinUserProperty.getProp8());
                        this.updateSpropById(accountType, accountTypeEnum.getAccountParam(), billCoinUserProperty);
                        //billCoinUserPropertySnapshotService.updateSpropByUserCoinCheckTime(accountType, accountTypeEnum.getAccountParam(), billCoinUserProperty.getUserId(), billCoinUserProperty.getCoinId(), billCoinUserProperty.getCheckTime(), billCoinUserProperty);
                    });
                    log.info("BillCoinUserPropertyService.repairCoinUserPropertySprop updateSpropById data accountType:{} idSegment:{}", accountType, Arrays.toString(idSegment));
                }, concurrentSize.intValue());
                if (queryResultIsEmpty != null && queryResultIsEmpty.getFails().size() > 0) {
                    throw new RuntimeException("concurrentSelectCoinUserSnapshot queryResultIsEmpty.getFails().size=" + queryResultIsEmpty.getFails().size());
                }
                stopWatch.stop();
            }
            // 查询error表 保留最后一条数据 userId+coinId
            stopWatch.start("billCoinUserPropertyErrorService.getAllLastUserPropertyErrorMap");
            Map<String, BillCoinUserPropertyError> lastUserPropertyErrorMap = billCoinUserPropertyErrorService.getAllLastUserPropertyErrorMap(accountType, accountTypeEnum.getAccountParam());
            stopWatch.stop();
            stopWatch.start("billCoinUserPropertySnapshotService.updateByUserCoinCheckTime lastUserPropertyErrorMapSize:" + lastUserPropertyErrorMap.size());
            for (Map.Entry<String, BillCoinUserPropertyError> entry : lastUserPropertyErrorMap.entrySet()) {
                Long userId = entry.getValue().getUserId();
                if (repairUserId != null && !repairUserId.equals(userId)) {
                    continue;
                }
                Integer coinId = entry.getValue().getCoinId();
                Long lastBizId = entry.getValue().getLastBizId();
                BillCoinUserProperty billCoinUserProperty = this.selectUserCoinRecord(accountType, accountTypeEnum.getAccountParam(), userId, coinId);
                billCoinUserProperty.reloadDataParamsConvert(accountTypeEnum);
                log.info("BillCoinUserPropertyService.repairCoinUserPropertySprop init data accountType:{} errorData:{} userData:{}", accountType, JSON.toJSONString(entry.getValue()), JSON.toJSONString(billCoinUserProperty));
                if (billCoinUserProperty.getLastBizId() != null && billCoinUserProperty.getLastBizId() > 0
                        && lastBizId != null && lastBizId > billCoinUserProperty.getLastBizId()) {
                    BillCoinUserProperty lastBillCoinUserProperty = JSON.parseObject(entry.getValue().getParams(), BillCoinUserProperty.class);
                    billCoinUserProperty.setSprop1(lastBillCoinUserProperty.getProp1());
                    billCoinUserProperty.setSprop2(lastBillCoinUserProperty.getProp2());
                    billCoinUserProperty.setSprop3(lastBillCoinUserProperty.getProp3());
                    billCoinUserProperty.setSprop4(lastBillCoinUserProperty.getProp4());
                    billCoinUserProperty.setSprop5(lastBillCoinUserProperty.getProp5());
                    billCoinUserProperty.setSprop6(lastBillCoinUserProperty.getProp6());
                    billCoinUserProperty.setSprop7(lastBillCoinUserProperty.getProp7());
                    billCoinUserProperty.setSprop8(lastBillCoinUserProperty.getProp8());
                    this.updateSpropById(accountType, accountTypeEnum.getAccountParam(), billCoinUserProperty);
                    billCoinUserPropertySnapshotService.updateSpropByUserCoinCheckTime(accountType, accountTypeEnum.getAccountParam(), userId, coinId, billCoinUserProperty.getCheckTime(), billCoinUserProperty);
                    log.info("BillCoinUserPropertyService.repairCoinUserPropertySprop update data accountType:{} userId:{} coinId:{} lastBizId:{}", accountType, userId, coinId, lastBizId, JSON.toJSONString(billCoinUserProperty));
                }
            }
            stopWatch.stop();
            log.info("BillCoinUserPropertyService.repairCoinUserPropertySprop init end accountType:{} stopWatch:{}", accountType, stopWatch.prettyPrint());
        }
    }

    public List<BillCoinUserProperty> selectCoinUserByUserIds(byte accountType, String accountParam, List<Long> userIds) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyMapper.selectCoinUserByUserIds(accountType, accountParam, userIds));
    }
}
