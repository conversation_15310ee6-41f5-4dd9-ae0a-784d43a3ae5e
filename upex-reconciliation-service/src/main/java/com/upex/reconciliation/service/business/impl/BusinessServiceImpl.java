package com.upex.reconciliation.service.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.facade.enums.ApolloKeyEnum;
import com.upex.reconciliation.service.business.BusinessRegistry;
import com.upex.reconciliation.service.business.BusinessService;
import com.upex.reconciliation.service.common.constants.BillBizTypeConstant;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.dao.entity.BillAllConfig;
import com.upex.reconciliation.service.dao.entity.BillCapitalOrder;
import com.upex.reconciliation.service.dao.entity.CapitalOrder;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.ApolloReconciliationCapitalOrderConfig;
import com.upex.reconciliation.service.model.config.KafkaConsumerConfig;
import com.upex.reconciliation.service.service.impl.CapitalOrderService;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.spot.facade.query.SpotChainAssetBillsQueryClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BusinessServiceImpl implements BusinessService {


    @Resource
    private BusinessRegistry businessRegistry;

    @Resource
    private SpotChainAssetBillsQueryClient spotChainAssetBillsQueryClient;

    @Resource
    private CapitalOrderService capitalOrderService;

    @Override
    public Map<Long, Map<Integer, AccountAssetsInfoResult>> getUserAssetsMapSingleThread(AccountTypeEnum accountTypeEnum, List<Long> userIds, com.upex.bill.dto.params.BaseRequest baseRequest, BillAllConfig billConfig) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Map<Long, Map<Integer, AccountAssetsInfoResult>> userAssetsMap = new HashMap<>(16);
        List<AccountAssetsInfoResult> assetsInfoResults = this.getUserAssetsSingleThread(accountTypeEnum, userIds, baseRequest, billConfig);
        log.info("...Computing Time AccountType:{}  CollectAssetsServiceImpl.assetsInfoResults:{}, userIds.size:{} , time:{}", accountTypeEnum.getCode(), assetsInfoResults.size(), CollectionUtils.size(userIds), stopwatch.stop());

        if (CollectionUtils.isNotEmpty(assetsInfoResults)) {
            userAssetsMap = assetsInfoResults.stream().collect(Collectors.groupingBy(AccountAssetsInfoResult::getUserId,
                    Collectors.toMap(AccountAssetsInfoResult::getCoinId, Function.identity(), (key1, key2) -> key2)));
        }
        return userAssetsMap;
    }

    @Override
    public List<AccountAssetsInfoResult> getUserAssetsSingleThread(AccountTypeEnum accountTypeEnum, List<Long> userIds, com.upex.bill.dto.params.BaseRequest baseRequest, BillAllConfig billConfig) {
        Queue<AccountAssetsInfoResult> accountAssetsInfoResults = new LinkedBlockingQueue<>();
        if (CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>(accountAssetsInfoResults);
        }

        // 获取apollo配置，切分list大小
        ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(baseRequest.getAccountType());
        return getAccountAssetsInfoResultsSingleThread(accountTypeEnum, userIds, baseRequest, billConfig, apolloBillConfig);

    }

    @Override
    public void failureOrderInit(String jobParam) {
        ApolloReconciliationCapitalOrderConfig apolloConfig = ReconciliationApolloConfigUtils.getApolloObjectConfig(ApolloKeyEnum.CAPITAL_ORDER_APOLLO_CONFIG, ApolloReconciliationCapitalOrderConfig.class);
        List<Long> idList = apolloConfig.getIds();
        if (CollectionUtils.isEmpty(idList)) {
            log.info("failureOrderInit-idList is empty");
            return;
        }
        List<List<Long>> partition = Lists.partition(idList, apolloConfig.getPageSize());
        KafkaConsumerConfig kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig(KafkaTopicEnum.RECON_CAPITAL_ORDER_SYNC_TOPIC);
        for (List<Long> ids : partition) {
            String response = spotChainAssetBillsQueryClient.queryCapitalOrderInfoListByIdList(ids);
            if ("[]".equals(response)) {
                log.info("failureOrderInit-response is empty");
                return;
            }
            List<CapitalOrder> capitalOrders = JSONObject.parseArray(response, CapitalOrder.class);
            Date createTime = new Date();
            capitalOrders.forEach(capitalOrder -> {
                capitalOrder.setSourceId(capitalOrder.getId());
                capitalOrder.setSourceCreateDate(capitalOrder.getCreateDate());
                capitalOrder.setSourceUpdateDate(capitalOrder.getUpdateDate());

                capitalOrder.setId(null);
                capitalOrder.setCreateDate(createTime);
                capitalOrder.setUpdateDate(createTime);
            });
            capitalOrderService.batchSaveCapitalOrder(capitalOrders, false, kafkaConsumerConfig);
        }
    }

    @Override
    public void failureOrderRepair() {
        ApolloReconciliationCapitalOrderConfig apolloConfig = ReconciliationApolloConfigUtils.getApolloObjectConfig(ApolloKeyEnum.CAPITAL_ORDER_APOLLO_CONFIG, ApolloReconciliationCapitalOrderConfig.class);
        List<Integer> rechargeFailureStatusList = apolloConfig.getRechargeFailureStatusList();

        List<BillCapitalOrder> rechargeFailureList = capitalOrderService.queryAllRechargeFailureAmount(rechargeFailureStatusList);
        List<BillCapitalOrder> sysWalletCompensateFailureList = capitalOrderService.querySysWalletCompensateRechargeFailureAmount();
        List<BillCapitalOrder> rechargeChainList = capitalOrderService.queryRechargeChain();

        dateDoHandle(apolloConfig, rechargeFailureList);
        dateDoHandle(apolloConfig, sysWalletCompensateFailureList);
        dateDoHandle(apolloConfig, rechargeChainList);
    }

    private void dateDoHandle(ApolloReconciliationCapitalOrderConfig apolloConfig, List<BillCapitalOrder> rechargeFailureList) {
        Map<Long, BillCapitalOrder> reconCapitalMap = rechargeFailureList.stream().collect(Collectors.toMap(BillCapitalOrder::getOrderId, Function.identity()));
        List<Long> idList = rechargeFailureList.stream().map(BillCapitalOrder::getSourceId).collect(Collectors.toList());
        List<List<Long>> partition = Lists.partition(idList, apolloConfig.getPageSize());
        KafkaConsumerConfig kafkaConsumerConfig = ReconciliationApolloConfigUtils.getKafkaConsumerConfig(KafkaTopicEnum.RECON_CAPITAL_ORDER_SYNC_TOPIC);
        for (List<Long> ids : partition) {
            String response = spotChainAssetBillsQueryClient.queryCapitalOrderInfoListByIdList(ids);
            if ("[]".equals(response)) {
                log.info("failureOrderInit-response is empty");
                return;
            }
            List<CapitalOrder> capitalOrders = JSONObject.parseArray(response, CapitalOrder.class);
            Date createTime = new Date();
            List<CapitalOrder> insertCapitalOrders = capitalOrders.stream().filter(spotCapitalOrder -> {
                        BillCapitalOrder billCapitalOrder = reconCapitalMap.get(spotCapitalOrder.getOrderId());
                        return Objects.nonNull(billCapitalOrder) && spotCapitalOrder.getUpdateDate().compareTo(billCapitalOrder.getSourceUpdateDate()) >= 0;
                    })
                    .peek(capitalOrder -> {
                        capitalOrder.setSourceId(capitalOrder.getId());
                        capitalOrder.setSourceCreateDate(capitalOrder.getCreateDate());
                        capitalOrder.setSourceUpdateDate(capitalOrder.getUpdateDate());
                        capitalOrder.setId(null);
                        capitalOrder.setCreateDate(createTime);
                        capitalOrder.setUpdateDate(createTime);
                    })
                    .collect(Collectors.toList());
            capitalOrderService.batchSaveCapitalOrder(insertCapitalOrders, true, kafkaConsumerConfig);
        }
    }


    /**
     * 单线程方式获取用户资产
     *
     * @param accountTypeEnum  用户类型枚举
     * @param userIds          用户集合
     * @param baseRequest      请求
     * @param billConfig       数据库的billConfig
     * @param apolloBillConfig apollo的billConfig
     * @return java.util.List<AccountAssetsInfoResult>
     * @throws
     * @Date 2022/10/27 11:57
     * <AUTHOR>
     */
    private List<AccountAssetsInfoResult> getAccountAssetsInfoResultsSingleThread(AccountTypeEnum accountTypeEnum, List<Long> userIds, com.upex.bill.dto.params.BaseRequest baseRequest, BillAllConfig billConfig, ApolloReconciliationBizConfig apolloBillConfig) {
        Queue<AccountAssetsInfoResult> accountAssetsInfoResults = new LinkedBlockingQueue<>();

        // 无总账用户ID
        List<Long> nonBillTotalUserIdList = userIds.stream().filter(userId -> !BillBizTypeConstant.BILL_TOTAL_USER_ID.equals(userId)).collect(Collectors.toList());
        List<List<Long>> subUserIdList = Lists.partition(nonBillTotalUserIdList, apolloBillConfig.getBatchQueryUserAssetsSize());

        for (List<Long> uIdList : subUserIdList) {
            if (apolloBillConfig.isBatchQueryUserAssetsOpen()) {
                batchQueryUserAssets(uIdList, accountTypeEnum, baseRequest, billConfig, accountAssetsInfoResults, apolloBillConfig);
            } else {
                singleQueryUserAssets(uIdList, accountTypeEnum, baseRequest, billConfig, accountAssetsInfoResults, apolloBillConfig);
            }
        }
        return new ArrayList<>(accountAssetsInfoResults);

    }

    /**
     * 批量查询用户资产
     *
     * @param uIdList
     * @param accountTypeEnum
     * @param baseRequest
     * @param billConfig
     * @param uIdList
     * @param accountAssetsInfoResults
     * <AUTHOR>
     * @date 2023/3/8 17:10
     */
    private void batchQueryUserAssets(List<Long> uIdList, AccountTypeEnum accountTypeEnum, com.upex.bill.dto.params.BaseRequest baseRequest, BillAllConfig billConfig, Queue<AccountAssetsInfoResult> accountAssetsInfoResults, ApolloReconciliationBizConfig apolloBillConfig) {
        List<AccountAssetsInfoResult> assetsInfoResults = businessRegistry.getAccountAssetsService(accountTypeEnum).queryUserAssets(uIdList, baseRequest);
        if (assetsInfoResults == null) {
            log.error("batchQueryUserAssets-query user assets is null baseRequest={},userIdList={},accountType={}", baseRequest, uIdList, accountTypeEnum);
//            alarmManageService.alarmBusinessUserAssetsNull(billConfig, accountTypeEnum, baseRequest,uIdList);
            return;
        }
        if (!apolloBillConfig.getFilterUserAssetForZeroOpen()) {
            accountAssetsInfoResults.addAll(assetsInfoResults);
        } else {
            // 过滤掉无资产的数据
            List<AccountAssetsInfoResult> filterAssetsInfoResultList = assetsInfoResults.stream().filter(AccountAssetsInfoResult::haveAsset).collect(Collectors.toList());
            accountAssetsInfoResults.addAll(filterAssetsInfoResultList);
            log.info("batchQueryUserAssets queryUserAssets accountType:{},filterUserAssetForZeroOpen:{},assetsInfoResults.size:{},filterAssetsInfoResultList.size:{},accountAssetsInfoResults.size:{}",
                    accountTypeEnum.getCode(), apolloBillConfig.getFilterUserAssetForZeroOpen(), CollectionUtils.size(assetsInfoResults), CollectionUtils.size(filterAssetsInfoResultList), CollectionUtils.size(accountAssetsInfoResults));
        }
    }


    /**
     * 查询用户资产
     *
     * @param uIdList
     * @param accountTypeEnum
     * @param baseRequest
     * @param billConfig
     * @param accountAssetsInfoResults
     * @param apolloBillConfig
     * <AUTHOR>
     * @date 2023/3/8 17:10
     */
    private void singleQueryUserAssets(List<Long> uIdList, AccountTypeEnum accountTypeEnum, com.upex.bill.dto.params.BaseRequest baseRequest, BillAllConfig billConfig, Queue<AccountAssetsInfoResult> accountAssetsInfoResults, ApolloReconciliationBizConfig apolloBillConfig) {
        for (Long userId : uIdList) {
            List<Long> userIdList = new ArrayList<>();
            userIdList.add(userId);
            List<AccountAssetsInfoResult> assetsInfoResults = businessRegistry.getAccountAssetsService(accountTypeEnum).queryUserAssets(userIdList, baseRequest);
            if (assetsInfoResults == null) {
                log.error("singleQueryUserAssets-query user assets is null baseRequest={},userIdList={},accountType={}", baseRequest, userIdList, accountTypeEnum);
//                alarmManageService.alarmBusinessUserAssetsNull(billConfig, accountTypeEnum, baseRequest,userIdList);
                continue;
            }
            if (!apolloBillConfig.getFilterUserAssetForZeroOpen()) {
                accountAssetsInfoResults.addAll(assetsInfoResults);
            } else {
                // 过滤掉无资产的数据
                List<AccountAssetsInfoResult> filterAssetsInfoResultList = assetsInfoResults.stream().filter(AccountAssetsInfoResult::haveAsset).collect(Collectors.toList());
                accountAssetsInfoResults.addAll(filterAssetsInfoResultList);
                log.info("singleQueryUserAssets accountType:{},filterUserAssetForZeroOpen:{},assetsInfoResults.size:{},filterAssetsInfoResultList.size:{},accountAssetsInfoResults.size:{}",
                        accountTypeEnum.getCode(), apolloBillConfig.getFilterUserAssetForZeroOpen(), CollectionUtils.size(assetsInfoResults), CollectionUtils.size(filterAssetsInfoResultList), CollectionUtils.size(accountAssetsInfoResults));
            }
        }
    }


}
