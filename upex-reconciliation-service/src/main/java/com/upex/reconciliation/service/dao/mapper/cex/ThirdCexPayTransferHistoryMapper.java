package com.upex.reconciliation.service.dao.mapper.cex;

import com.upex.reconciliation.service.dao.cex.entity.ThirdCexPayTransferHistory;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ThirdCexPayTransferHistoryMapper {

    /**
     * 插入一条转账记录
     */
    int insert(ThirdCexPayTransferHistory history);

    /**
     * 根据ID查询
     */
    ThirdCexPayTransferHistory selectById(Long id);

    /**
     * 根据用户ID和交易所类型查询
     */
    List<ThirdCexPayTransferHistory> selectByCexUserAndType(
        @Param("cexUserId") String cexUserId,
        @Param("cexType") Integer cexType);

    /**
     * 按条件更新记录
     */
    int update(ThirdCexPayTransferHistory history);

    /**
     * 按条件分页查询
     */
    List<ThirdCexPayTransferHistory> selectList(
        @Param("cexUserId") String cexUserId,
        @Param("cexType") Integer cexType,
        @Param("coinName") String coinName,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 批量插入
     */
    int batchInsert(List<ThirdCexPayTransferHistory> records);

    List<ThirdCexPayTransferHistory> selectUnCheckWithdraw(Integer cexType, Integer isLegal);

    int checkWithdraw(Long id, Integer isLegal);


    /**
     * 分页查询（支持多个用户ID）
     */
//    List<ThirdCexTransferHistory> selectPageByUserIds(
//        @Param("cexUserIds") List<String> cexUserIds,
//        @Param("transferReq") UserTransferHistoryListReq transferReq,
//        @Param("queryTime") LocalDateTime queryTime);

    /**
     * 分页总数统计
     */
//    int countPageByUserIds(
//        @Param("cexUserIds") List<String> cexUserIds,
//        @Param("transferReq") UserTransferHistoryListReq transferReq,
//        @Param("queryTime") LocalDateTime queryTime);
}
