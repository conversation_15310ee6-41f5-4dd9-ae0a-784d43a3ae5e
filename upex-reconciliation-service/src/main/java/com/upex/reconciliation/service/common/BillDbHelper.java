package com.upex.reconciliation.service.common;

import com.upex.commons.datasource.DataSourceInvoker;
import com.upex.commons.datasource.ShardDataSource;
import com.upex.reconciliation.facade.enums.MasterSlaveEnum;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.utils.task.function.FunctionP0;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

/**
  * 数据源帮助类
  * <AUTHOR>
  * @date 2023/2/13 12:15
  */
@Slf4j
@Component
public class BillDbHelper {

    public static final String RECONCILIATION_PREFIX = "reconciliation";

    public static final String BILL_PREFIX = "bill";

    public static final String SNAPSHOT_PREFIX = "snapshot";

    public static final String DS_GLOBAL_MIDDLE = "global";
    @Autowired
    private DataSourceInvoker dataSourceInvoker;

    @Resource(name = "defaultTransactionTemplate")
    public TransactionTemplate defaultTransactionTemplate;

    @Autowired
    @Qualifier(value = "repeatReadTransactionTemplate")
    private TransactionTemplate repeatReadTransactionTemplate;

    public <T> T doDbOpInSnapshotGlobalMaster(FunctionP0<T> invoker) {
        return doDbOp(globalSnapshotGlobalMaster(),invoker);
    }
    public <T> T doDbOpInSnapshotGlobalMasterTransaction(FunctionP0<T> invoker) {
        return doDbOp(globalSnapshotGlobalMaster(), () -> defaultTransactionTemplate.execute(transactionStatus -> invoker.run()));
    }

    public <T> T doDbOpInSnapshotGlobalMaster(FunctionP0<T> invoker,boolean isTransaction) {
        if(isTransaction) {
            return doDbOpInSnapshotGlobalMasterTransaction(invoker);
        }else{
            return doDbOpInSnapshotGlobalMaster(invoker);
        }
    }


    public <T> T doDbOpInBillMaster(FunctionP0<T> invoker) {
        return doDbOp(globalBillMaster(),invoker);
    }

    private ShardDataSource globalBillMaster() {
        log.info("Bill globalBillMaster 调用");
        return null;
    }

    private ShardDataSource globalBillSlave() {
        log.info("Bill globalBillSlave 调用");
        return null;
    }

    public <T> T doDbOpInReconMaster(FunctionP0<T> invoker) {
        return doDbOp(globalReconMaster(),invoker);
    }
    public <T> T doDbOpInReconMasterTransaction(FunctionP0<T> invoker) {
        return doDbOp(globalReconMaster(), () -> defaultTransactionTemplate.execute(transactionStatus -> invoker.run()));
    }


    public <T> T doDbOpRepeatReadInReconMasterTransaction(FunctionP0<T> invoker) {
        return doDbOp(globalReconMaster(), () -> repeatReadTransactionTemplate.execute(transactionStatus -> invoker.run()));
    }



    public <T> T doDbOpInReconMaster(FunctionP0<T> invoker, boolean isTransaction) {
        if(isTransaction) {
            return doDbOpInReconMasterTransaction(invoker);
        }else{
            return doDbOpInReconMaster(invoker);
        }
    }
    public  <T> T doDbOp(String dataSourceKey, FunctionP0<T> invoker) {
        String[] dataSource = dataSourceKey.split(BillConstants.SEPARATOR);
        ShardDataSource shardDatasource = ShardDataSource.build(dataSource[0], dataSource[1], dataSource[2]);
        return this.doDbOp(shardDatasource, invoker);
    }

    private <T> T doDbOp(ShardDataSource shardDataSource, FunctionP0<T> invoker) {
        return dataSourceInvoker.run(shardDataSource, invoker);
    }

    private ShardDataSource globalReconMaster() {
        return ShardDataSource.build(RECONCILIATION_PREFIX, DS_GLOBAL_MIDDLE, MasterSlaveEnum.MASTER.getType());
    }

    private ShardDataSource globalReconSlave() {
        return ShardDataSource.build(RECONCILIATION_PREFIX, DS_GLOBAL_MIDDLE, MasterSlaveEnum.SLAVE.getType());
    }

    private ShardDataSource globalSnapshotGlobalMaster() {
        return ShardDataSource.build(SNAPSHOT_PREFIX, DS_GLOBAL_MIDDLE, MasterSlaveEnum.MASTER.getType());
    }

}
