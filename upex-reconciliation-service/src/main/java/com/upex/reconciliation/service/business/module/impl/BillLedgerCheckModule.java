package com.upex.reconciliation.service.business.module.impl;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.upex.bill.dto.enums.ProfitTransferStatusEnum;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.*;
import com.upex.reconciliation.service.business.billengine.BillEngineManager;
import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.concurrent.ConcurrentSortMap;
import com.upex.reconciliation.service.common.constants.enums.AssetsCheckTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.CapitalInitBusinessTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.ProfitTypeEnum;
import com.upex.reconciliation.service.common.constants.enums.TimeUnitEnum;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.alarm.CheckResult;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.config.TransferDiffTolerateExitValueConfig;
import com.upex.reconciliation.service.model.dto.AbstractProperty;
import com.upex.reconciliation.service.model.dto.BillLedgerTimeSliceDTO;
import com.upex.reconciliation.service.model.dto.SymbolCheckProperty;
import com.upex.reconciliation.service.service.*;
import com.upex.reconciliation.service.utils.*;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.utils.util.SerialNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.*;


/**
 * 总账对账module
 */
@Slf4j
public class BillLedgerCheckModule {
    private AssetsCheckTypeEnum assetsCheckTypeEnum;
    private BillLedgerTimeSliceDTO lastLedgerSaveTimeSliceDTO;
    private AssetsBillConfigService assetsBillConfigService;
    private AssetsBillCoinPropertyService assetsBillCoinPropertyService;
    private AssetsBillCoinTypePropertyService assetsBillCoinTypePropertyService;
    private AssetsBillConfigSnapshotService assetsBillConfigSnapshotService;
    private MemoryDataService memoryDataService;
    private BillCapitalInitPropertyService billCapitalInitPropertyService;
    private CommonService commonService;
    private BillDbHelper dbHelper;
    private BillEngineManager billEngineManager;
    private BillAllConfigService billAllConfigService;
    private AlarmNotifyService alarmNotifyService;
    private BillContractProfitTransferService billContractProfitTransferService;
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    private AssetsContractProfitCoinDetailService assetsContractProfitCoinDetailService;
    private SerialNoGenerator noGenerator;
    private ReconSystemAccountService reconSystemAccountService;
    private AssetsContractProfitSymbolDetailService assetsContractProfitSymbolDetailService;
    private RedisTemplate<String, Object> redisTemplate;
    /***最后对账时间***/
    private Date lastCheckOkTime;
    /***最后存盘时间***/
    private Date lastSaveCheckOkTime;
    /***存储时间片 key：时间戳， value 按整分钟进行聚合***/
    private final ConcurrentSortMap<Long, BillLedgerTimeSliceDTO> saveLedgerTimeSliceDTOMap = new ConcurrentSortMap();
    /***盈亏换汇类型***/
    private List<String> exchangeAndProfitTypeList = Lists.newArrayList(ProfitTypeEnum.COIN_PROFIT.getCode(), ProfitTypeEnum.SYMBOL_PROFIT.getCode());

    public BillLedgerCheckModule(AssetsCheckTypeEnum assetsCheckTypeEnum, ReconciliationSpringContext reconciliationSpringContext, BillEngineManager billEngineManager) {
        this.assetsCheckTypeEnum = assetsCheckTypeEnum;
        this.assetsBillConfigService = reconciliationSpringContext.getAssetsBillConfigService();
        this.assetsBillCoinPropertyService = reconciliationSpringContext.getAssetsBillCoinPropertyService();
        this.assetsBillCoinTypePropertyService = reconciliationSpringContext.getAssetsBillCoinTypePropertyService();
        this.memoryDataService = reconciliationSpringContext.getMemoryDataService();
        this.billCapitalInitPropertyService = reconciliationSpringContext.getBillCapitalInitPropertyService();
        this.commonService = reconciliationSpringContext.getCommonService();
        this.dbHelper = reconciliationSpringContext.getDbHelper();
        this.billEngineManager = billEngineManager;
        this.assetsBillConfigSnapshotService = reconciliationSpringContext.getAssetsBillConfigSnapshotService();
        this.billAllConfigService = reconciliationSpringContext.getBillAllConfigService();
        this.alarmNotifyService = reconciliationSpringContext.getAlarmNotifyService();
        this.billContractProfitTransferService = reconciliationSpringContext.getBillContractProfitTransferService();
        this.accountAssetsServiceFactory = reconciliationSpringContext.getAccountAssetsServiceFactory();
        this.assetsContractProfitCoinDetailService = reconciliationSpringContext.getAssetsContractProfitCoinDetailService();
        this.noGenerator = reconciliationSpringContext.getSerialNoGenerator();
        this.reconSystemAccountService = reconciliationSpringContext.getReconSystemAccountService();
        this.assetsContractProfitSymbolDetailService = reconciliationSpringContext.getAssetsContractProfitSymbolDetailService();
        this.redisTemplate = reconciliationSpringContext.getRedisTemplate();
    }

    /**
     * 重启加载数据
     */
    public void rebootLoadData() {
        AssetsBillConfig assetsBillConfig = assetsBillConfigService.selectByTypeAndParam(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam());
        if (assetsBillConfig == null) {
            throw new RuntimeException("ledger rebootLoadData error assetsBillConfig is null " + assetsCheckTypeEnum.getCode());
        }
        AssetsCheckConfig assetsCheckConfig = ReconciliationApolloConfigUtils.getAssetsCheckConfig(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam());
        Date checkOkTime = assetsBillConfig.getCheckOkTime();
        List<AssetsBillCoinProperty> assetsBillCoinPropertyList = assetsBillCoinPropertyService.selectAssetsByEndTime(checkOkTime, assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam());
        List<AssetsBillCoinTypeProperty> assetsBillCoinTypePropertyList = new ArrayList<>();
        Long coinTypeCount = assetsBillCoinTypePropertyService.countByCheckTime(checkOkTime, null, assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam());
        if (coinTypeCount > 0) {
            Integer pageSize = assetsCheckConfig.getSingleSqlMaxSize();
            long pages = coinTypeCount % pageSize == 0 ? coinTypeCount / pageSize : (coinTypeCount / pageSize) + 1;
            for (long pageNum = 0; pageNum < pages; pageNum++) {
                List<AssetsBillCoinTypeProperty> coinTypeProperties = assetsBillCoinTypePropertyService.selectPageByCheckTime(checkOkTime, null, assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam(), (pageNum * pageSize), pageSize);
                assetsBillCoinTypePropertyList.addAll(coinTypeProperties);
            }
        }
        if (CollectionUtils.isEmpty(assetsBillCoinPropertyList) || CollectionUtils.isEmpty(assetsBillCoinTypePropertyList)) {
            throw new RuntimeException("ledger rebootLoadData error assetsBillCoinPropertyList or assetsBillCoinTypePropertyList " + assetsCheckTypeEnum.getCode());
        }
        List<AssetsContractProfitCoinDetail> lastAssetsContractProfitCoinDetailList = assetsContractProfitCoinDetailService.getLastValidAssetsContractProfitCoinDetailList(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam(), Lists.newArrayList(ProfitTypeEnum.COIN_PROFIT.getCode(), ProfitTypeEnum.SYMBOL_PROFIT.getCode()),
                checkOkTime);
        List<AssetsContractProfitSymbolDetail> lastAssetsContractProfitSymbolDetailList = assetsContractProfitSymbolDetailService.selectLastValidListByAccountTypeAndCheckTime(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam(), checkOkTime);

        // 构建时间片对象
        this.lastCheckOkTime = checkOkTime;
        this.lastSaveCheckOkTime = checkOkTime;
        BillLedgerTimeSliceDTO billLedgerTimeSliceDTO = new BillLedgerTimeSliceDTO();
        billLedgerTimeSliceDTO.setCheckTime(checkOkTime);
        billLedgerTimeSliceDTO.setAssetsCheckTypeEnum(assetsCheckTypeEnum);
        billLedgerTimeSliceDTO.setAssetsBillConfig(assetsBillConfig);
        billLedgerTimeSliceDTO.setAssetsBillCoinPropertyList(assetsBillCoinPropertyList);
        billLedgerTimeSliceDTO.setAssetsBillCoinTypePropertyList(assetsBillCoinTypePropertyList);
        billLedgerTimeSliceDTO.setAssetsContractProfitCoinDetailList(lastAssetsContractProfitCoinDetailList);
        billLedgerTimeSliceDTO.setAssetsContractProfitSymbolDetailList(lastAssetsContractProfitSymbolDetailList);
        this.lastLedgerSaveTimeSliceDTO = billLedgerTimeSliceDTO;
        this.redisTemplate.opsForValue().set(RedisUtil.getBusinessLastCheckOkTime(assetsCheckTypeEnum.getCode()), String.valueOf(checkOkTime.getTime()));
    }

    public BillLedgerTimeSliceDTO getLastLedgerSaveTimeSliceDTO() {
        return lastLedgerSaveTimeSliceDTO;
    }

    /**
     * 账账对账
     */
    public void doLedgerCheckStart() {
        AssetsBillConfig assetsBillConfig = assetsBillConfigService.selectByTypeAndParam(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam());
        if (assetsBillConfig == null) {
            return;
        }
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        AssetsCheckConfig assetsCheckConfig = globalBillConfig.getAssetsCheckConfig(assetsCheckTypeEnum.getCode());
        log.info("doLedgerCheckStart start assetsCheckType:{} lastCheckOkTime:{} ledgerTimeSliceKafkaComplete data:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(lastCheckOkTime), JSON.toJSONString(memoryDataService.getLedgerTimeSliceKafkaComplete()));
        SortedMap<Long, Map<String, Boolean>> ledgerTimeSliceKafkaComplete = memoryDataService.getLedgerTimeSliceKafkaComplete();
        if (AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getCode().equals(assetsCheckConfig.getAssetsCheckType())) {
            Date leverSpotCheckTime = billEngineManager.getLeverSpotCheckTime();
            Boolean leverSpotOpen = billEngineManager.getLeverSpotOpen();
            int timeSliceSize = ledgerTimeSliceKafkaComplete.size();
            log.info("internal check start timeSliceSize:{} lastCheckOkTime:{}", timeSliceSize, DateUtil.date2str(lastCheckOkTime));
            for (int i = 0; i < timeSliceSize; i++) {
                // 判断存储时间片大小 如果如果存储压力过大 停止对账
                if (saveLedgerTimeSliceDTOMap.size() > assetsCheckConfig.getSaveTimeSliceMapLimitSize()) {
                    log.info("BillLedgerCheckModule.doLedgerCheckStart saveLedgerTimeSliceDTOMap.size gte {} {}", assetsCheckConfig.getAssetsCheckType(), saveLedgerTimeSliceDTOMap.size());
                    break;
                }
                Long timeSliceKey = null;
                try {
                    timeSliceKey = ledgerTimeSliceKafkaComplete.firstKey();
                } catch (Exception e) {
                    break;
                }
                if (timeSliceKey == null) {
                    break;
                }
                if (timeSliceKey <= lastCheckOkTime.getTime()) {
                    memoryDataService.removeLedgerTimeSlice(timeSliceKey);
                    log.error("doLedgerCheckStart delay removeLedgerTimeSlice assetsCheckType:{} timeSliceKey:{} currentTimeSlice:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.longToDate(timeSliceKey), DateUtil.date2str(lastCheckOkTime));
                    continue;
                }
                Map<String, Boolean> kafkaCompleteMap = ledgerTimeSliceKafkaComplete.get(timeSliceKey);
                if (!isKafkaComplate(assetsCheckConfig, kafkaCompleteMap)) {
                    log.info("doLedgerCheckStart isKafkaComplate false assetsCheckType:{} timeSliceKey:{} data:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.longToDate(timeSliceKey), JSON.toJSONString(kafkaCompleteMap));
                    break;
                }
                if (leverSpotOpen && (leverSpotCheckTime == null || timeSliceKey > leverSpotCheckTime.getTime())) {
                    log.info("doLedgerCheckStart lever_spot delay assetsCheckType:{} timeSliceKey:{} levelSpotTimeSliceKey:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.longToDate(timeSliceKey), (leverSpotCheckTime != null ? DateUtil.date2str(leverSpotCheckTime) : ""));
                    break;
                }
                BillLedgerTimeSliceDTO mergeLedgerTimeSliceDTO = timeSliceMerge(timeSliceKey);
                // 入出对账
                boolean result = checkInAndOutAssets(mergeLedgerTimeSliceDTO);
                log.info("BillLedgerCheckModule.doLedgerCheckStart checkInAndOutAssets result assetsCheckType:{} checkTime:{} result:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(mergeLedgerTimeSliceDTO.getCheckTime()), result);
                if (!result) {
                    break;
                }
                // 多空仓
                result = checkLCountSCount(mergeLedgerTimeSliceDTO);
                log.info("BillLedgerCheckModule.doLedgerCheckStart checkLCountSCount result assetsCheckType:{} checkTime:{} result:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(mergeLedgerTimeSliceDTO.getCheckTime()), result);
                if (!result) {
                    break;
                }
                // 盈亏对账
                result = checkContractProfit(mergeLedgerTimeSliceDTO);
                log.info("BillLedgerCheckModule.doLedgerCheckStart checkContractProfit result assetsCheckType:{} checkTime:{} result:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(mergeLedgerTimeSliceDTO.getCheckTime()), result);
                if (!result) {
                    break;
                }
                // 动账金额检测
                result = checkProfitTransfer(mergeLedgerTimeSliceDTO);
                log.info("BillLedgerCheckModule.doLedgerCheckStart checkProfitTransfer result assetsCheckType:{} checkTime:{} result:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(mergeLedgerTimeSliceDTO.getCheckTime()), result);
                if (!result) {
                    alarmNotifyService.alarm(assetsCheckConfig.getAssetsCheckType(), LEDGER_TIME_SLICE_TRANSFER_CHECK_ERROR, DateUtil.longToDate(timeSliceKey));
                    //break;
                }
                lastCheckOkTime = new Date(timeSliceKey);
                memoryDataService.removeLedgerTimeSlice(timeSliceKey);
                this.redisTemplate.opsForValue().set(RedisUtil.getBusinessLastCheckOkTime(assetsCheckTypeEnum.getCode()), String.valueOf(timeSliceKey));
                if (TimeSliceCalcUtils.isSaveTimeSlice(timeSliceKey, assetsCheckConfig.getTimeSliceSize(), assetsCheckConfig.getMergeTimeSliceSize())) {
                    log.info("saveLedgerTimeSliceDTOMap put assetsCheckType:{} timeSsliceKey:{} size:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.longToDate(timeSliceKey), saveLedgerTimeSliceDTOMap.size());
                    saveLedgerTimeSliceDTOMap.put(timeSliceKey, mergeLedgerTimeSliceDTO);
                    lastLedgerSaveTimeSliceDTO = mergeLedgerTimeSliceDTO;
                }
            }
        } else {
            log.info("leverSpot check start timeSliceSize:{} lastCheckOkTime:{}", ledgerTimeSliceKafkaComplete.size(), DateUtil.date2str(lastCheckOkTime));
            Iterator<Map.Entry<Long, Map<String, Boolean>>> timeSliceKafkaCompleteIterator = ledgerTimeSliceKafkaComplete.entrySet().iterator();
            while (timeSliceKafkaCompleteIterator.hasNext()) {
                Map.Entry<Long, Map<String, Boolean>> timeSliceKafkaCompleteEntry = null;
                try {
                    timeSliceKafkaCompleteEntry = timeSliceKafkaCompleteIterator.next();
                } catch (Exception e) {
                    break;
                }
                if (timeSliceKafkaCompleteEntry == null) {
                    break;
                }
                Long timeSliceKey = timeSliceKafkaCompleteEntry.getKey();
                if (timeSliceKey <= lastCheckOkTime.getTime()) {
                    continue;
                }
                Map<String, Boolean> kafkaCompleteMap = timeSliceKafkaCompleteEntry.getValue();
                if (!isKafkaComplate(assetsCheckConfig, kafkaCompleteMap)) {
                    log.info("doLedgerCheckStart isKafkaComplate false assetsCheckType:{} timeSliceKey:{} data:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.longToDate(timeSliceKey), JSON.toJSONString(kafkaCompleteMap));
                    break;
                }
                BillLedgerTimeSliceDTO mergeLedgerTimeSliceDTO = timeSliceMerge(timeSliceKey);
                boolean result = checkInAndOutAssets(mergeLedgerTimeSliceDTO);
                if (!result) {
                    break;
                }
                log.info("doLedgerCheckStart result assetsCheckType:{} timeSliceKey:{} result:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.longToDate(timeSliceKey), JSON.toJSONString(result));
                lastCheckOkTime = new Date(timeSliceKey);
                if (TimeSliceCalcUtils.isSaveTimeSlice(timeSliceKey, assetsCheckConfig.getTimeSliceSize(), assetsCheckConfig.getMergeTimeSliceSize())) {
                    log.info("saveLedgerTimeSliceDTOMap put assetsCheckType:{} timeSliceKey:{} size:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.longToDate(timeSliceKey), saveLedgerTimeSliceDTOMap.size());
                    saveLedgerTimeSliceDTOMap.put(timeSliceKey, mergeLedgerTimeSliceDTO);
                    lastLedgerSaveTimeSliceDTO = mergeLedgerTimeSliceDTO;
                }
            }
        }
        log.info("doLedgerCheckStart end assetsCheckType:{} lastCheckOkTime:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(lastCheckOkTime));
    }

    /**
     * 多空仓+盈亏
     *
     * @param mergeLedgerTimeSliceDTO
     * @return
     */
    private boolean checkContractProfit(BillLedgerTimeSliceDTO mergeLedgerTimeSliceDTO) {
        AssetsCheckConfig assetsCheckConfig = ReconciliationApolloConfigUtils.getAssetsCheckConfig(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam());
        boolean result = true;
        if (!assetsCheckConfig.isLedgerCheckContractProfitOpen()) {
            return result;
        }
        Date checkTime = mergeLedgerTimeSliceDTO.getCheckTime();
        CheckResult checkResult = CheckResult.success(assetsCheckConfig.getAssetsCheckType(), assetsCheckConfig.getAssetsCheckParam(), mergeLedgerTimeSliceDTO.getCheckTime());
        Map<String, List<SymbolCheckProperty>> symbolCheckPropertyGroupByMap = mergeLedgerTimeSliceDTO.getSymbolCheckPropertyList().stream().collect(Collectors.groupingBy(SymbolCheckProperty::getSymbolId));
        // 重新计算盈亏换汇数据，并且清除动账数据
        Boolean notExistRecalculatePositionProfitSymbol = true;
        Boolean calculateExchangeProfitTransfer = true;
        Boolean existNegativePosition = false;
        for (Map.Entry<String, List<SymbolCheckProperty>> symbolCheckPropertyGroupEntry : symbolCheckPropertyGroupByMap.entrySet()) {
            String symbolId = symbolCheckPropertyGroupEntry.getKey();
            List<SymbolCheckProperty> symbolCheckPropertyList = symbolCheckPropertyGroupEntry.getValue();
            if (CollectionUtils.isEmpty(symbolCheckPropertyList)) {
                continue;
            }
            boolean notRecalculatePositionProfitSymbolSet = isNotRecalculatePositionProfitSymbol(symbolId, mergeLedgerTimeSliceDTO.getCheckTime(), assetsCheckConfig);
            if (notRecalculatePositionProfitSymbolSet) {
                notExistRecalculatePositionProfitSymbol = false;
            }
            BigDecimal realized = BigDecimal.ZERO;
            BigDecimal unRealized = BigDecimal.ZERO;
            BigDecimal initValue = BigDecimal.ZERO;
            BigDecimal reRealized = BigDecimal.ZERO;
            BigDecimal reUnRealized = BigDecimal.ZERO;
            Boolean symbolExistNegativePosition = false;
            for (SymbolCheckProperty symbolCheckProperty : symbolCheckPropertyList) {
                realized = realized.add(symbolCheckProperty.getProp3());
                unRealized = unRealized.add(symbolCheckProperty.getProp4());
                initValue = initValue.add(symbolCheckProperty.getProp5());
                reRealized = reRealized.add(symbolCheckProperty.getProp7());
                reUnRealized = reUnRealized.add(symbolCheckProperty.getProp8());
                if (!symbolExistNegativePosition) {
                    symbolExistNegativePosition = symbolCheckProperty.getExistNegativePosition();
                }
            }
            // 出现负值仓位 则盈亏换汇数据计算
            if (symbolExistNegativePosition) {
                existNegativePosition = true;
            }
            BigDecimal ledgerProfitCheckInitValue = mergeLedgerTimeSliceDTO.getLedgerProfitCheckInitValueMap().getOrDefault(symbolId, BigDecimal.ZERO);
            BigDecimal diff1 = realized.add(unRealized).add(initValue);
            BigDecimal diff2 = realized.add(reUnRealized).add(initValue);
            BigDecimal diff3 = reRealized.add(reUnRealized).add(initValue);
            BigDecimal ledgerProfitToleranceValue = assetsCheckConfig.getLedgerProfitToleranceValue();
            // 盈亏
            log.info("BillLedgerCheckModule.checkContractProfit check info assetsCheckType:{} checkTime:{} result:{} diff:{} symbolId:{} realized:{} unRealized:{} initValue:{} ledgerProfitCheckInitValue:{}  ledgerProfitToleranceValue:{} reRealized:{} reUnRealized:{} diff2:{} diff3:{} existNegativePosition:{} data:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(checkTime), !(diff1.compareTo(ledgerProfitToleranceValue) > 0), diff1, symbolId, realized, unRealized, initValue, ledgerProfitCheckInitValue, ledgerProfitToleranceValue, reRealized, reUnRealized, diff2, diff3, JSON.toJSONString(symbolCheckPropertyList));
            // 如果存在负仓 或者盈亏对账不对币对 则不进行盈亏对账
            if (!existNegativePosition && !notRecalculatePositionProfitSymbolSet) {
                if (diff1.compareTo(ledgerProfitToleranceValue) > 0) {
                    // 非整5分钟 可以进行diff2兼容对账 整5分钟涉及动账需要人工介入
                    boolean saveTimeSliceFlag = TimeSliceCalcUtils.isSaveTimeSlice(checkTime.getTime(), assetsCheckConfig.getTimeSliceSize(), assetsCheckConfig.getMergeTimeSliceSize());
                    if (!saveTimeSliceFlag) {
                        calculateExchangeProfitTransfer = false;
                        if (diff2.compareTo(ledgerProfitToleranceValue) > 0) {
                            result = false;
                            checkResult.setResult(false);
                            checkResult.addLedgerContractProfitCheckData(symbolId, realized, unRealized, initValue, ledgerProfitCheckInitValue, ledgerProfitToleranceValue, diff1, diff2, "重算对账失败");
                        } else {
                            checkResult.setResult(false);
                            checkResult.addLedgerContractProfitCheckData(symbolId, realized, unRealized, initValue, ledgerProfitCheckInitValue, ledgerProfitToleranceValue, diff1, diff2, "重算对账成功");
                        }
                    } else {
                        result = false;
                        checkResult.setResult(false);
                        checkResult.addLedgerContractProfitCheckData(symbolId, realized, unRealized, initValue, ledgerProfitCheckInitValue, ledgerProfitToleranceValue, diff1, diff2, "非重算对账失败");
                    }
                }
            }
        }
        if (checkResult.isFail()) {
            alarmNotifyService.alarm(assetsCheckConfig.getAssetsCheckType(), CHECK_LEDGER_CONTRACT_PROFIT_TEMPLATE, Map.of("checkResult", checkResult));
        }
        // 重新计算盈亏换汇数据，并且清除动账数据 对账通过-》存在负仓位或者diff1对账失败都不进行入账
        if (result && (!notExistRecalculatePositionProfitSymbol || existNegativePosition || !calculateExchangeProfitTransfer)) {
            mergeLedgerExchangeAndProfit(mergeLedgerTimeSliceDTO, assetsCheckConfig, false);
            log.info("BillLedgerCheckModule.checkContractProfit mergeLedgerExchangeAndProfit info assetsCheckType:{} checkTime:{} existNegativePosition:{} calculateExchangeProfitTransfer:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(mergeLedgerTimeSliceDTO.getCheckTime()), existNegativePosition, calculateExchangeProfitTransfer);
            alarmNotifyService.alarm(assetsCheckConfig.getAssetsCheckType(), NOT_CALCULATE_EXCHANGE_PROFIT_TRANSFER_ERROR, DateUtil.date2str(mergeLedgerTimeSliceDTO.getCheckTime()), existNegativePosition, calculateExchangeProfitTransfer);
        }
        return result;
    }


    /**
     * 多空仓+盈亏
     *
     * @param mergeLedgerTimeSliceDTO
     * @return
     */
    private boolean checkLCountSCount(BillLedgerTimeSliceDTO mergeLedgerTimeSliceDTO) {
        AssetsCheckConfig assetsCheckConfig = ReconciliationApolloConfigUtils.getAssetsCheckConfig(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam());
        boolean result = true;
        if (!assetsCheckConfig.isLedgerCheckLCountSCountOpen()) {
            return result;
        }
        CheckResult checkResult = CheckResult.success(assetsCheckConfig.getAssetsCheckType(), assetsCheckConfig.getAssetsCheckParam(), mergeLedgerTimeSliceDTO.getCheckTime());
        Map<String, List<SymbolCheckProperty>> symbolCheckPropertyGroupByMap = mergeLedgerTimeSliceDTO.getSymbolCheckPropertyList().stream().collect(Collectors.groupingBy(SymbolCheckProperty::getSymbolId));
        for (Map.Entry<String, List<SymbolCheckProperty>> symbolCheckPropertyGroupEntry : symbolCheckPropertyGroupByMap.entrySet()) {
            String symbolId = symbolCheckPropertyGroupEntry.getKey();
            List<SymbolCheckProperty> symbolCheckPropertyList = symbolCheckPropertyGroupEntry.getValue();
            if (CollectionUtils.isEmpty(symbolCheckPropertyList)) {
                continue;
            }
            BigDecimal lCount = BigDecimal.ZERO;
            BigDecimal sCount = BigDecimal.ZERO;
            BigDecimal initDiffValue = assetsCheckConfig.getLedgerLSCountInitDiffValueMap().getOrDefault(symbolId, BigDecimal.ZERO);
            for (SymbolCheckProperty symbolCheckProperty : symbolCheckPropertyList) {
                lCount = lCount.add(symbolCheckProperty.getProp1());
                sCount = sCount.add(symbolCheckProperty.getProp2());
            }
            BigDecimal diff = lCount.subtract(sCount).add(initDiffValue);
            // 多空仓
            log.info("BillLedgerCheckModule.checkProfitAndLCountSCount info assetsCheckType:{} checkTime:{} result:{}  symbolId:{} lCount:{} sCount:{} initDiffValue:{} diff:{} data:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(mergeLedgerTimeSliceDTO.getCheckTime()), !(diff.compareTo(BigDecimal.ZERO) != 0), symbolId, lCount, sCount, initDiffValue, diff, JSON.toJSONString(symbolCheckPropertyList));
            if (diff.compareTo(BigDecimal.ZERO) != 0) {
                result = false;
                checkResult.addLedgerLCountSCountCheckData(symbolId, lCount, sCount, diff);
                checkResult.setResult(false);
            }
        }
        if (checkResult.isFail()) {
            alarmNotifyService.alarm(assetsCheckConfig.getAssetsCheckType(), CHECK_LEDGER_LCOUNT_SCOUNT_TEMPLATE, Map.of("checkResult", checkResult));
        }
        return result;
    }

    /**
     * 待动账金额检测
     * 应收未收=内存总应收金额-内存总实收金额
     * 待动账金额=(数据库checkTime<=对账时间 and transferTime>对账时间) + 内存当期应收未收数据+存盘缓冲区应收未收对账数据
     * 验证公式：应收未收=待动账金额
     *
     * @param mergeLedgerTimeSliceDTO
     * @return
     */
    private boolean checkProfitTransfer(BillLedgerTimeSliceDTO mergeLedgerTimeSliceDTO) {
        AssetsCheckConfig assetsCheckConfig = ReconciliationApolloConfigUtils.getAssetsCheckConfig(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam());
        boolean result = true;
        if (!assetsCheckConfig.isLedgerCheckProfitTransferOpen()) {
            return result;
        }
        // 现货手续费动账初始值
        Map<Integer, BigDecimal> spotInitUnProfitTransfers = getSpotInitUnProfitTransfers();
        // 总应收实收
        Map<Integer, BigDecimal> totalFeeMaps = mergeLedgerTimeSliceDTO.getTotalFeeMaps();
        Map<Integer, BigDecimal> totalReceiveFeeMaps = mergeLedgerTimeSliceDTO.getTotalReceiveFeeMaps();
        Map<Integer, BigDecimal> totalInterestFeeMaps = mergeLedgerTimeSliceDTO.getTotalInterestFeeMaps();
        Map<Integer, BigDecimal> totalReceiveInterestFeeMaps = mergeLedgerTimeSliceDTO.getTotalReceiveInterestFeeMaps();
        // 合并计算应收未收金额
        Map<Integer, BigDecimal> totalTransferFeeMaps = mergeLedgerTimeSliceDTO.getTotalTransferFeeMaps();
        Map<Integer, BigDecimal> totalTransferInterestFeeMaps = mergeLedgerTimeSliceDTO.getTotalTransferInterestFeeMaps();
        Map<Integer, BigDecimal> totalProfitTransferFeeMaps = mergeSumMaps(totalTransferFeeMaps, totalTransferInterestFeeMaps);
        // 合并当期内存应收/实收
        Map<Integer, BigDecimal> currentChangeFeeMaps = mergeLedgerTimeSliceDTO.getCurrentChangeFeeMaps();
        Map<Integer, BigDecimal> currentChangeInterestFeeMaps = mergeLedgerTimeSliceDTO.getCurrentChangeInterestFeeMaps();
        Map<Integer, BigDecimal> currentChangeProfitTransferMaps = mergeSumMaps(currentChangeFeeMaps, currentChangeInterestFeeMaps);
        Map<Integer, BigDecimal> currentChangeReceiveFeeMaps = mergeLedgerTimeSliceDTO.getCurrentChangeReceiveFeeMaps();
        Map<Integer, BigDecimal> currentChangeReceiveInterestFeeMaps = mergeLedgerTimeSliceDTO.getCurrentChangeReceiveInterestFeeMaps();
        Map<Integer, BigDecimal> currentChangeReceiveProfitTransferMaps = mergeSumMaps(currentChangeReceiveFeeMaps, currentChangeReceiveInterestFeeMaps);

        // 合并存盘缓冲区应收未收
        Map<Integer, BigDecimal> totalSaveChangeProfitTransferMaps = new HashMap<>();
        Map<Integer, BigDecimal> totalSaveChangeReceiveProfitTransferMaps = new HashMap<>();
        for (Map.Entry<Long, BillLedgerTimeSliceDTO> entry : saveLedgerTimeSliceDTOMap.entrySet()) {
            BillLedgerTimeSliceDTO ledgerTimeSliceDTO = entry.getValue();
            Map<Integer, BigDecimal> mergeProfitTransferMaps = mergeSumMaps(ledgerTimeSliceDTO.getCurrentChangeFeeMaps(), ledgerTimeSliceDTO.getCurrentChangeInterestFeeMaps());
            Map<Integer, BigDecimal> mergeReceiveProfitTransferMaps = mergeSumMaps(ledgerTimeSliceDTO.getCurrentChangeReceiveFeeMaps(), ledgerTimeSliceDTO.getCurrentChangeReceiveInterestFeeMaps());
            log.info("checkProfitTransfer save cache data checkTime:{} totalChangeFeeMaps:{} totalChangeInterestFeeMaps:{} mergeProfitTransferMaps:{}", DateUtil.date2str(mergeLedgerTimeSliceDTO.getCheckTime()), JSON.toJSONString(ledgerTimeSliceDTO.getCurrentChangeFeeMaps()), JSON.toJSONString(ledgerTimeSliceDTO.getCurrentChangeInterestFeeMaps()), JSON.toJSONString(mergeProfitTransferMaps));
            totalSaveChangeProfitTransferMaps = mergeSumMaps(totalSaveChangeProfitTransferMaps, mergeProfitTransferMaps);
            totalSaveChangeReceiveProfitTransferMaps = mergeSumMaps(totalSaveChangeReceiveProfitTransferMaps, mergeReceiveProfitTransferMaps);
        }
        // DB checkTime<=对账时间 and transferTime>对账时间
        long dbSaveTimeSlice = this.lastSaveCheckOkTime.getTime();
        List<Byte> accountTypeList = Arrays.asList(AccountTypeEnum.SPOT.getCode(), AccountTypeEnum.LEVER_ONE.getCode(), AccountTypeEnum.LEVER_FULL.getCode(), AccountTypeEnum.OTC.getCode(), AccountTypeEnum.USDT_MIX_CONTRACT_BL.getCode(), AccountTypeEnum.USD_MIX_CONTRACT_BL.getCode(), AccountTypeEnum.USDC_MIX_CONTRACT_BL.getCode(), AccountTypeEnum.UNIFIED.getCode());
        Map<Integer, BigDecimal> dbTotalProfitTransferMaps = billContractProfitTransferService.selectAllUnProfitTransferByCheckOkTime(accountTypeList, new Date(dbSaveTimeSlice));
        log.info("checkProfitTransfer data checkTime:{} dbSaveTimeSlice:{} spotInitUnProfitTransfers:{} transferFeeMaps:{} transferInterestFeeMaps:{} totalProfitTransferMaps:{} totalChangeFeeMaps:{} totalChangeInterestFeeMaps:{} totalChangeProfitTransferMaps:{} totalSaveChangeProfitTransferMaps:{} dbTotalProfitTransferMaps:{} totalChangeReceiveProfitTransferMaps:{} totalSaveChangeReceiveProfitTransferMaps:{}", DateUtil.date2str(mergeLedgerTimeSliceDTO.getCheckTime()), DateUtil.longToDate(dbSaveTimeSlice), JSON.toJSONString(spotInitUnProfitTransfers), JSON.toJSONString(totalTransferFeeMaps), JSON.toJSONString(totalTransferInterestFeeMaps), JSON.toJSONString(totalProfitTransferFeeMaps), JSON.toJSONString(currentChangeFeeMaps), JSON.toJSONString(currentChangeInterestFeeMaps), JSON.toJSONString(currentChangeProfitTransferMaps), JSON.toJSONString(totalSaveChangeProfitTransferMaps), JSON.toJSONString(dbTotalProfitTransferMaps), JSON.toJSONString(currentChangeReceiveProfitTransferMaps), JSON.toJSONString(totalSaveChangeReceiveProfitTransferMaps));
        // 计算待动账金额
        Map<String, PriceVo> allRate = commonService.getRatesToUSDT();
        Map<Integer, BillCoinProperty> totalCoinPropertyMap = mergeLedgerTimeSliceDTO.getTotalCoinPropertyMap();
        boolean returnResult = true;
        for (Map.Entry<Integer, BillCoinProperty> coinPropertyEntry : totalCoinPropertyMap.entrySet()) {
            Integer coinId = coinPropertyEntry.getKey();
            // 总应收实收
            BigDecimal totalFeeCount = totalFeeMaps.getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal totalReceiveFeeCount = totalReceiveFeeMaps.getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal totalInterestFeeCount = totalInterestFeeMaps.getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal totalReceiveInterestFeeCount = totalReceiveInterestFeeMaps.getOrDefault(coinId, BigDecimal.ZERO);
            // 初始值 / 待动账
            BigDecimal spotInitUnProfitTransferCount = spotInitUnProfitTransfers.getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal contractInitUnProfitTransferCount = mergeLedgerTimeSliceDTO.getContractUnProfitInitValueMaps().getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal totalTransferCount = totalProfitTransferFeeMaps.getOrDefault(coinId, BigDecimal.ZERO);
            // 待动账
            BigDecimal currentChangeTransferCount = currentChangeProfitTransferMaps.getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal currentChangeReceiveTransferCount = currentChangeReceiveProfitTransferMaps.getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal totalSaveChangeTransferCount = totalSaveChangeProfitTransferMaps.getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal totalSaveChangeReceiveTransferCount = totalSaveChangeReceiveProfitTransferMaps.getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal dbTransferCount = dbTotalProfitTransferMaps.getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal diffCount = totalTransferCount.subtract(currentChangeTransferCount.subtract(currentChangeReceiveTransferCount).add(totalSaveChangeTransferCount).subtract(totalSaveChangeReceiveTransferCount).add(dbTransferCount)).add(spotInitUnProfitTransferCount).add(contractInitUnProfitTransferCount);
            result = diffCount.compareTo(BigDecimal.ZERO) == 0;
            Map<String, Object> logMap = new HashMap<>();
            logMap.put("totalFeeCount", totalFeeCount);
            logMap.put("totalReceiveFeeCount", totalReceiveFeeCount);
            logMap.put("totalInterestFeeCount", totalInterestFeeCount);
            logMap.put("totalReceiveInterestFeeCount", totalReceiveInterestFeeCount);
            logMap.put("spotInitUnProfitTransferCount", spotInitUnProfitTransferCount);
            logMap.put("totalTransferCount", totalTransferCount);
            logMap.put("currentChangeTransferCount", currentChangeTransferCount);
            logMap.put("currentChangeReceiveTransferCount", currentChangeReceiveTransferCount);
            logMap.put("totalSaveChangeTransferCount", totalSaveChangeTransferCount);
            logMap.put("totalSaveChangeReceiveTransferCount", totalSaveChangeReceiveTransferCount);
            logMap.put("dbTransferCount", dbTransferCount);
            logMap.put("diffCount", diffCount);
            log.info("checkProfitTransfer error checkTime:{} dbSaveTimeSlice:{} result:{} coinId:{} data:{}", DateUtil.date2str(mergeLedgerTimeSliceDTO.getCheckTime()), DateUtil.longToDate(dbSaveTimeSlice), result, coinId, JSON.toJSONString(logMap));
            if (!result) {
                returnResult = false;
            }
        }
        return returnResult;
    }

    /**
     * 存盘
     */
    public void doLedgerSaveDataStart() {
        AssetsBillConfig assetsBillConfig = assetsBillConfigService.selectByTypeAndParam(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam());
        if (assetsBillConfig == null) {
            return;
        }
        AssetsCheckConfig assetsCheckConfig = ReconciliationApolloConfigUtils.getAssetsCheckConfig(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam());
        Integer insertSqlMaxSize = assetsCheckConfig.getSingleSqlMaxSize();
        int saveTimeSliceDTOMapSize = saveLedgerTimeSliceDTOMap.size();
        log.info("doLedgerSaveDataStart start assetsCheckType:{} dbCheckOkTime:{} lastCheckOkTime:{} saveTimeSliceDTOMapSize:{}", assetsBillConfig.getAssetsCheckType(), DateUtil.date2str(assetsBillConfig.getCheckOkTime()), DateUtil.date2str(lastCheckOkTime), saveTimeSliceDTOMapSize);
        for (int i = 0; i < saveTimeSliceDTOMapSize; i++) {
            BillAllConfig billConfig = billAllConfigService.getMinCheckOkBillConfig(assetsCheckConfig.getSubSystemList());
            if (billConfig == null) {
                log.error("doLedgerSaveDataStart billConfig is null assetsCheckType:{} dbCheckOkTime:{} lastCheckOkTime:{} saveTimeSliceDTOMapSize:{}", assetsBillConfig.getAssetsCheckType(), DateUtil.date2str(assetsBillConfig.getCheckOkTime()), DateUtil.date2str(lastCheckOkTime), saveTimeSliceDTOMapSize);
                return;
            }
            Long timeSliceKey = saveLedgerTimeSliceDTOMap.firstKey();
            Date nowDate = new Date();
            Long intervalMs = TimeUnitEnum.toDuration(assetsCheckConfig.getLedgerDalySaveTime()).toMillis();
            if (nowDate.getTime() - timeSliceKey < intervalMs) {
                log.info("doLedgerSaveDataStart lte ledgerDalySaveTime assetsCheckType:{} ledgerDalySaveTime:{} dbCheckOkTime:{} lastCheckOkTime:{} saveTimeSliceDTOMapSize:{}", assetsBillConfig.getAssetsCheckType(), assetsCheckConfig.getLedgerDalySaveTime(), DateUtil.date2str(assetsBillConfig.getCheckOkTime()), DateUtil.date2str(lastCheckOkTime), saveTimeSliceDTOMapSize);
                return;
            }
            if (timeSliceKey > billConfig.getCheckOkTime().getTime()) {
                log.info("doLedgerSaveDataStart billConfig checkOkTime is delay assetsCheckType:{} dbCheckOkTime:{} lastCheckOkTime:{} saveTimeSliceDTOMapSize:{}", assetsBillConfig.getAssetsCheckType(), DateUtil.date2str(assetsBillConfig.getCheckOkTime()), DateUtil.date2str(lastCheckOkTime), saveTimeSliceDTOMapSize);
                return;
            }
            BillLedgerTimeSliceDTO ledgerTimeSliceDTO = saveLedgerTimeSliceDTOMap.get(timeSliceKey);
            dbHelper.doDbOpInReconMasterTransaction(() -> {
                StopWatch stopWatch = new StopWatch();
                List<AssetsBillCoinProperty> billCoinPropertyList = ledgerTimeSliceDTO.getAssetsBillCoinPropertyList();
                if (CollectionUtils.isNotEmpty(billCoinPropertyList)) {
                    stopWatch.start("assetsBillCoinPropertyService.batchInsert size:" + billCoinPropertyList.size());
                    List<List<AssetsBillCoinProperty>> assetsBillCoinPropertyListList = Lists.partition(billCoinPropertyList, insertSqlMaxSize);
                    for (List<AssetsBillCoinProperty> assetsBillCoinPropertyList : assetsBillCoinPropertyListList) {
                        assetsBillCoinPropertyService.batchInsert(assetsBillCoinPropertyList, assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam());
                    }
                    stopWatch.stop();
                }
                List<AssetsBillCoinTypeProperty> billCoinTypePropertyList = ledgerTimeSliceDTO.getAssetsBillCoinTypePropertyList();
                if (CollectionUtils.isNotEmpty(billCoinTypePropertyList)) {
                    stopWatch.start("assetsBillCoinTypePropertyService.batchInsert size:" + billCoinTypePropertyList.size());
                    List<List<AssetsBillCoinTypeProperty>> assetsBillCoinTypePropertyListList = Lists.partition(billCoinTypePropertyList, insertSqlMaxSize);
                    for (List<AssetsBillCoinTypeProperty> assetsBillCoinTypePropertyList : assetsBillCoinTypePropertyListList) {
                        assetsBillCoinTypePropertyService.batchInsert(assetsBillCoinTypePropertyList, assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam());
                    }
                    stopWatch.stop();
                }
                if (ledgerTimeSliceDTO.getCalculateExchangeProfitTransfer()) {
                    List<AssetsContractProfitCoinDetail> assetsContractProfitCoinDetailList = ledgerTimeSliceDTO.getAssetsContractProfitCoinDetailList();
                    if (CollectionUtils.isNotEmpty(assetsContractProfitCoinDetailList)) {
                        stopWatch.start("assetsContractProfitCoinDetailService.batchInsert size:" + assetsContractProfitCoinDetailList.size());
                        List<List<AssetsContractProfitCoinDetail>> assetsContractProfitCoinDetailListList = Lists.partition(assetsContractProfitCoinDetailList, insertSqlMaxSize);
                        for (List<AssetsContractProfitCoinDetail> contractProfitCoinDetailList : assetsContractProfitCoinDetailListList) {
                            assetsContractProfitCoinDetailService.batchInsert(assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam(), contractProfitCoinDetailList);
                        }
                        stopWatch.stop();
                    }
                    List<AssetsContractProfitSymbolDetail> assetsContractProfitSymbolDetailList = ledgerTimeSliceDTO.getAssetsContractProfitSymbolDetailList();
                    if (CollectionUtils.isNotEmpty(assetsContractProfitSymbolDetailList)) {
                        stopWatch.start("assetsContractProfitSymbolDetailService.batchInsert size:" + assetsContractProfitSymbolDetailList.size());
                        List<List<AssetsContractProfitSymbolDetail>> assetsContractProfitSymbolDetailListList = Lists.partition(assetsContractProfitSymbolDetailList, insertSqlMaxSize);
                        for (List<AssetsContractProfitSymbolDetail> contractProfitSymbolDetailList : assetsContractProfitSymbolDetailListList) {
                            assetsContractProfitSymbolDetailService.batchInsert(assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam(), contractProfitSymbolDetailList);
                        }
                        stopWatch.stop();
                    }
                    List<BillContractProfitTransfer> billContractProfitTransferList = ledgerTimeSliceDTO.getBillContractProfitTransferList();
                    if (CollectionUtils.isNotEmpty(billContractProfitTransferList)) {
                        stopWatch.start("billContractProfitTransferService.batchInsert size:" + billContractProfitTransferList.size());
                        billContractProfitTransferService.batchInsert(billContractProfitTransferList);
                        stopWatch.stop();
                    }
                }
                List<AssetsContractProfitCoinDetail> assetsContractProfitCoinDetailHisList = ledgerTimeSliceDTO.getAssetsContractProfitCoinDetailHisList();
                if (CollectionUtils.isNotEmpty(assetsContractProfitCoinDetailHisList)) {
                    stopWatch.start("assetsContractProfitCoinDetailService.batchInsertHis size:" + assetsContractProfitCoinDetailHisList.size());
                    List<List<AssetsContractProfitCoinDetail>> assetsContractProfitCoinDetailHisListList = Lists.partition(assetsContractProfitCoinDetailHisList, insertSqlMaxSize);
                    for (List<AssetsContractProfitCoinDetail> contractProfitCoinDetailList : assetsContractProfitCoinDetailHisListList) {
                        assetsContractProfitCoinDetailService.batchInsertHis(assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam(), contractProfitCoinDetailList);
                    }
                    stopWatch.stop();
                }
                List<AssetsContractProfitSymbolDetail> assetsContractProfitSymbolDetailHisList = ledgerTimeSliceDTO.getAssetsContractProfitSymbolDetailHisList();
                if (CollectionUtils.isNotEmpty(assetsContractProfitSymbolDetailHisList)) {
                    stopWatch.start("assetsContractProfitSymbolDetailService.batchInsert size:" + assetsContractProfitSymbolDetailHisList.size());
                    List<List<AssetsContractProfitSymbolDetail>> assetsContractProfitSymbolDetailHisListList = Lists.partition(assetsContractProfitSymbolDetailHisList, insertSqlMaxSize);
                    for (List<AssetsContractProfitSymbolDetail> contractProfitSymbolDetailList : assetsContractProfitSymbolDetailHisListList) {
                        assetsContractProfitSymbolDetailService.batchInsertHis(assetsBillConfig.getAssetsCheckType(), assetsBillConfig.getAssetsCheckParam(), contractProfitSymbolDetailList);
                    }
                    stopWatch.stop();
                }
                stopWatch.start("assetsBillConfigService.updateByPrimaryKeySelective and assetsBillConfigSnapshotService.batchInsert");
                AssetsBillConfig upAssetsBillConfig = ledgerTimeSliceDTO.getAssetsBillConfig();
                assetsBillConfigService.updateByPrimaryKeySelective(upAssetsBillConfig);
                AssetsBillConfigSnapshot assetsBillConfigSnapshot = upAssetsBillConfig.convertToAssetsBillConfigSnapshot();
                assetsBillConfigSnapshotService.batchInsert(Lists.newArrayList(assetsBillConfigSnapshot));
                stopWatch.stop();
                log.info("BillLedgerCheckModule dbHelper.doDbOpInReconMasterTransaction end checkTime:{} stopWatch:{}", DateUtil.date2str(new Date(timeSliceKey)), stopWatch.prettyPrint());
                return null;
            });
            saveLedgerTimeSliceDTOMap.remove(timeSliceKey);
            this.lastSaveCheckOkTime = new Date(timeSliceKey);
        }
    }

    /**
     * 业务线消息是否处理完成
     *
     * @param assetsCheckConfig
     * @param kafkaCompleteMap
     * @return
     */
    private boolean isKafkaComplate(AssetsCheckConfig assetsCheckConfig, Map<String, Boolean> kafkaCompleteMap) {
        if (kafkaCompleteMap == null) {
            return false;
        }
        List<String> subSystemList = assetsCheckConfig.getSubSystemList();
        Boolean result = false;
        for (String accountTypeKey : subSystemList) {
            Byte accountType = AccountTypeEnum.getAccountTypeByKey(accountTypeKey);
            result = kafkaCompleteMap.getOrDefault(accountType.toString(), false);
            if (!result) {
                break;
            }
        }
        return result;
    }

    /**
     * 时间片合并
     *
     * @param timeSliceKey
     * @return
     */
    private BillLedgerTimeSliceDTO timeSliceMerge(Long timeSliceKey) {
        AssetsCheckConfig assetsCheckConfig = ReconciliationApolloConfigUtils.getAssetsCheckConfig(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam());
        List<Byte> subSystemList = assetsCheckConfig.getSubSystemList().stream().map(accountTypeKey -> AccountTypeEnum.getAccountTypeByKey(accountTypeKey)).collect(Collectors.toList());
        // 构建时间片对象
        AssetsBillConfig newAssetsBillConfig = BeanCopierUtil.copyProperties(lastLedgerSaveTimeSliceDTO.getAssetsBillConfig(), AssetsBillConfig.class);
        Date nowDate = new Date();
        Date checkTime = new Date(timeSliceKey);
        newAssetsBillConfig.setCheckOkTime(checkTime);
        newAssetsBillConfig.setUpdateTime(nowDate);
        BillLedgerTimeSliceDTO ledgerTimeSliceDTO = new BillLedgerTimeSliceDTO();
        ledgerTimeSliceDTO.setCheckTime(checkTime);
        ledgerTimeSliceDTO.setAssetsCheckTypeEnum(assetsCheckTypeEnum);
        ledgerTimeSliceDTO.setAssetsBillConfig(newAssetsBillConfig);
        // 内存数据获取
        Map<String, Map<Integer, BillCoinProperty>> currentAccountTypeCoinPropertyMap = memoryDataService.getLedgerTimeSliceCoinPropertyMap().get(timeSliceKey);
        Map<String, Map<String, BillCoinTypeProperty>> currentAccountTypeCoinTypePropertyMap = memoryDataService.getLedgerTimeSliceCoinTypePropertyMap().get(timeSliceKey);
        // 业务线明细数据转换
        List<AssetsBillCoinTypeProperty> currentCoinTypePropertyList = new ArrayList<>();
        for (Map.Entry<String, Map<String, BillCoinTypeProperty>> accountTypeMapEntry : currentAccountTypeCoinTypePropertyMap.entrySet()) {
            Byte accountType = Byte.valueOf(accountTypeMapEntry.getKey());
            AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
            if (!subSystemList.contains(accountType)) {
                continue;
            }
            BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountTypeEnum.getCode());
            Map<Integer, BillCoinProperty> billCoinPropertyMap = currentAccountTypeCoinPropertyMap.get(accountType.toString());
            Map<String, BillCoinTypeProperty> billCoinTypePropertyMap = accountTypeMapEntry.getValue();
            // 计算应收、实收、初始值
            this.sumLedgerReceivableAbdReceived(accountTypeEnum, ledgerTimeSliceDTO, billCoinPropertyMap, billCoinTypePropertyMap);
            // 类型转换
            ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(Byte.valueOf(accountType));
            for (Map.Entry<String, BillCoinTypeProperty> coinPropertyEntry : accountTypeMapEntry.getValue().entrySet()) {
                BillCoinTypeProperty billCoinTypeProperty = coinPropertyEntry.getValue();
                if (accountTypeEnum.isContract()
                        && !(apolloBillConfig.getTransferIn().contains(billCoinTypeProperty.getBizType())
                        || apolloBillConfig.getTransferOut().contains(billCoinTypeProperty.getBizType()))) {
                    continue;
                }
                currentCoinTypePropertyList.add(billCheckService.convertToAssetsBillCoinTypeProperty(accountType, billCoinTypeProperty));
            }
            // 业务线coin维度数据聚合计算
            billCoinPropertyMap.values().forEach(billCoinProperty -> {
                cleanOtherProperty(accountType, billCoinProperty);
                BillCoinProperty oldBillCoinProperty = ledgerTimeSliceDTO.getTotalCoinPropertyMap().computeIfAbsent(billCoinProperty.getCoinId(), key -> {
                    BillCoinProperty createBillCoinProperty = new BillCoinProperty();
                    createBillCoinProperty.setCoinId(billCoinProperty.getCoinId());
                    createBillCoinProperty.setCheckTime(billCoinProperty.getCheckTime());
                    createBillCoinProperty.setCreateTime(billCoinProperty.getCreateTime());
                    createBillCoinProperty.setUpdateTime(billCoinProperty.getUpdateTime());
                    return createBillCoinProperty;
                });
                oldBillCoinProperty.mergeByCoinId(billCoinProperty);
            });
        }
        // 计算动账手续费
        for (Map.Entry<String, List<BillTransferFeeCoinDetail>> transferFeeCoinDetailMapEntry : memoryDataService.getLedgerTimeSliceTransferFeeCoinDetailMap().getOrDefault(timeSliceKey, new HashMap<>()).entrySet()) {
            Byte accountType = Byte.valueOf(transferFeeCoinDetailMapEntry.getKey());
            if (!subSystemList.contains(accountType)) {
                continue;
            }
            List<BillTransferFeeCoinDetail> transferFeeCoinDetailList = transferFeeCoinDetailMapEntry.getValue();
            Map<Integer, List<BillTransferFeeCoinDetail>> transferFeeCoinDetailListMap = transferFeeCoinDetailList.stream().collect(Collectors.groupingBy(BillTransferFeeCoinDetail::getCoinId));
            for (Map.Entry<Integer, List<BillTransferFeeCoinDetail>> transferFeeCoinDetailListMapEntry : transferFeeCoinDetailListMap.entrySet()) {
                Integer coinId = transferFeeCoinDetailListMapEntry.getKey();
                BigDecimal totalFee = BigDecimal.ZERO;
                BigDecimal changeFee = BigDecimal.ZERO;
                BigDecimal totalReceiveFee = BigDecimal.ZERO;
                BigDecimal changeReceiveFee = BigDecimal.ZERO;
                for (BillTransferFeeCoinDetail transferFeeCoinDetail : transferFeeCoinDetailListMapEntry.getValue()) {
                    totalFee = totalFee.add(transferFeeCoinDetail.getTotalReceivableFee());
                    changeFee = changeFee.add(transferFeeCoinDetail.getCurrentReceivableFee());
                    totalReceiveFee = totalReceiveFee.add(transferFeeCoinDetail.getTotalReceivedFee());
                    changeReceiveFee = changeReceiveFee.add(transferFeeCoinDetail.getCurrentReceivedFee());
                }
                ledgerTimeSliceDTO.getTotalFeeMaps().put(coinId, ledgerTimeSliceDTO.getTotalFeeMaps().getOrDefault(coinId, BigDecimal.ZERO).add(totalFee.negate()));
                ledgerTimeSliceDTO.getCurrentChangeFeeMaps().put(coinId, ledgerTimeSliceDTO.getCurrentChangeFeeMaps().getOrDefault(coinId, BigDecimal.ZERO).add(changeFee));
                ledgerTimeSliceDTO.getTotalReceiveFeeMaps().put(coinId, ledgerTimeSliceDTO.getTotalReceiveFeeMaps().getOrDefault(coinId, BigDecimal.ZERO).add(totalReceiveFee));
                ledgerTimeSliceDTO.getCurrentChangeReceiveFeeMaps().put(coinId, ledgerTimeSliceDTO.getCurrentChangeReceiveFeeMaps().getOrDefault(coinId, BigDecimal.ZERO).add(changeReceiveFee));
            }
        }
        // 计算动账金额
        for (Map.Entry<Integer, BigDecimal> entry : ledgerTimeSliceDTO.getTotalFeeMaps().entrySet()) {
            BigDecimal receiveFee = ledgerTimeSliceDTO.getTotalReceiveFeeMaps().getOrDefault(entry.getKey(), BigDecimal.ZERO);
            ledgerTimeSliceDTO.getTotalTransferFeeMaps().put(entry.getKey(), entry.getValue().negate().subtract(receiveFee));
        }
        // 计算合约利息动账金额
        for (Map.Entry<Integer, BigDecimal> entry : ledgerTimeSliceDTO.getTotalInterestFeeMaps().entrySet()) {
            BigDecimal receiveInterestFee = ledgerTimeSliceDTO.getTotalReceiveInterestFeeMaps().getOrDefault(entry.getKey(), BigDecimal.ZERO);
            ledgerTimeSliceDTO.getTotalTransferInterestFeeMaps().put(entry.getKey(), entry.getValue().negate().subtract(receiveInterestFee));
        }

        // 业务线coin维度数据聚合计算 coin维度当期增量和上期数据merge
        List<AssetsBillCoinProperty> assetsBillCoinPropertyList = AssetsBillCoinTypeProperty.sumGroupByCoin(currentCoinTypePropertyList);
        List<AssetsBillCoinProperty> lastAssetsBillCoinPropertyList = lastLedgerSaveTimeSliceDTO.getAssetsBillCoinPropertyList().stream().map(item -> {
            AssetsBillCoinProperty newAssetsBillCoinProperty = BeanCopierUtil.copyProperties(item, AssetsBillCoinProperty.class);
            newAssetsBillCoinProperty.setChangePropZero();
            newAssetsBillCoinProperty.setCheckTime(checkTime);
            newAssetsBillCoinProperty.setCreateTime(nowDate);
            newAssetsBillCoinProperty.setUpdateTime(nowDate);
            return newAssetsBillCoinProperty;
        }).collect(Collectors.toList());
        Map<Integer, AssetsBillCoinProperty> lastCoinPropertyMap = lastAssetsBillCoinPropertyList.stream().collect(Collectors.toMap(AssetsBillCoinProperty::getCoinId, item -> item));
        for (AssetsBillCoinProperty currentBillCoinProperty : assetsBillCoinPropertyList) {
            AssetsBillCoinProperty lastBillCoinProperty = lastCoinPropertyMap.get(currentBillCoinProperty.getCoinId());
            if (lastBillCoinProperty == null) {
                lastBillCoinProperty = new AssetsBillCoinProperty();
                lastBillCoinProperty.setCoinId(currentBillCoinProperty.getCoinId());
                lastAssetsBillCoinPropertyList.add(lastBillCoinProperty);
            }
            AssetsBillCoinProperty.addAssetsProp(lastBillCoinProperty, currentBillCoinProperty);
            lastBillCoinProperty.setCheckTime(checkTime);
            lastBillCoinProperty.setCreateTime(nowDate);
            lastBillCoinProperty.setUpdateTime(nowDate);
        }

        // 业务线coinType维度数据聚合计算 coinType维度当期增量和上期数据merge
        List<AssetsBillCoinTypeProperty> assetsBillCoinTypePropertyList = AssetsBillCoinTypeProperty.sumGroupByCoinBizType(currentCoinTypePropertyList);
        List<AssetsBillCoinTypeProperty> lastAssetsBillCoinTypePropertyList = lastLedgerSaveTimeSliceDTO.getAssetsBillCoinTypePropertyList().stream().map(item -> {
            AssetsBillCoinTypeProperty newAssetsBillCoinTypeProperty = BeanCopierUtil.copyProperties(item, AssetsBillCoinTypeProperty.class);
            newAssetsBillCoinTypeProperty.setChangePropZero();
            newAssetsBillCoinTypeProperty.setCheckTime(checkTime);
            newAssetsBillCoinTypeProperty.setCreateTime(nowDate);
            newAssetsBillCoinTypeProperty.setUpdateTime(nowDate);
            return newAssetsBillCoinTypeProperty;
        }).collect(Collectors.toList());
        Map<String, AssetsBillCoinTypeProperty> lastCoinTypePropertyMap = lastAssetsBillCoinTypePropertyList.stream().collect(Collectors.toMap(AssetsBillCoinTypeProperty::groupByCoinIdAndTypeId, item -> item));
        for (AssetsBillCoinTypeProperty currentBillCoinTypeProperty : assetsBillCoinTypePropertyList) {
            AssetsBillCoinTypeProperty lastBillCoinTypeProperty = lastCoinTypePropertyMap.get(currentBillCoinTypeProperty.groupByCoinIdAndTypeId());
            if (lastBillCoinTypeProperty == null) {
                lastBillCoinTypeProperty = new AssetsBillCoinTypeProperty();
                lastBillCoinTypeProperty.setCoinId(currentBillCoinTypeProperty.getCoinId());
                lastBillCoinTypeProperty.setBizType(currentBillCoinTypeProperty.getBizType());
                lastAssetsBillCoinTypePropertyList.add(lastBillCoinTypeProperty);
            }
            AssetsBillCoinTypeProperty.addAssetsProp(lastBillCoinTypeProperty, currentBillCoinTypeProperty);
            lastBillCoinTypeProperty.setCheckTime(checkTime);
            lastBillCoinTypeProperty.setCreateTime(nowDate);
            lastBillCoinTypeProperty.setUpdateTime(nowDate);
        }
        // 多空仓合并
        memoryDataService.getLedgerTimeSliceSymbolCheckPropertyMap().get(timeSliceKey).forEach((accountType, symbolCheckPropertyList) -> {
            ledgerTimeSliceDTO.getSymbolCheckPropertyList().addAll(symbolCheckPropertyList);
        });
        // 合并数据赋值
        ledgerTimeSliceDTO.setAssetsBillCoinPropertyList(lastAssetsBillCoinPropertyList);
        ledgerTimeSliceDTO.setAssetsBillCoinTypePropertyList(lastAssetsBillCoinTypePropertyList);

        // 业务线盈亏换汇数据合并
        memoryDataService.getLedgerTimeSliceContractProfitCoinDetailMap().getOrDefault(timeSliceKey, new HashMap<>()).forEach((accountType, contractProfitCoinDetailList) -> {
            ledgerTimeSliceDTO.getContractProfitCoinDetailList().addAll(contractProfitCoinDetailList.stream().filter(item -> exchangeAndProfitTypeList.contains(item.getProfitType())).collect(Collectors.toList()));
        });
        // 合并业务线盈亏数据
        memoryDataService.getLedgerTimeSliceContractProfitSymbolDetailMap().getOrDefault(timeSliceKey, new HashMap<>()).forEach((accountType, contractProfitSymbolDetailList) -> {
            ledgerTimeSliceDTO.getContractProfitSymbolDetailList().addAll(contractProfitSymbolDetailList.stream().filter(item -> exchangeAndProfitTypeList.contains(item.getProfitType())).collect(Collectors.toList()));
        });
        log.info("timeSliceMerge checkTime:{} result:{}", DateUtil.longToDate(timeSliceKey), JSON.toJSONString(ledgerTimeSliceDTO.getContractProfitCoinDetailList()));
        // 计算业务线盈亏、换汇、动账数据
        mergeLedgerExchangeAndProfit(ledgerTimeSliceDTO, assetsCheckConfig, true);
        return ledgerTimeSliceDTO;
    }

    /**
     * 执行对账逻辑
     *
     * @param ledgerTimeSliceDTO
     * @return
     */
    private boolean checkInAndOutAssets(BillLedgerTimeSliceDTO ledgerTimeSliceDTO) {
        AssetsCheckConfig assetsCheckConfig = ReconciliationApolloConfigUtils.getAssetsCheckConfig(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam());
        boolean result = true;
        if (!assetsCheckConfig.isLedgerInoutCheckOpen()) {
            return result;
        }
        // 现货待冻账初始值
        Map<Integer, BigDecimal> transferDiffTolerateExitValueMap = assetsCheckConfig.getTransferDiffTolerateExitValueList().stream().collect(Collectors.toMap(TransferDiffTolerateExitValueConfig::getCoinId, TransferDiffTolerateExitValueConfig::getTransferDiffTolerateExitValue));
        // 入出计算
        Map<Integer, Map<String, BigDecimal>> transferInMap = new HashMap<>(16);
        Map<Integer, Map<String, BigDecimal>> transferOutMap = new HashMap<>(16);
        Map<Integer, Map<String, BigDecimal>> transferOtherMap = new HashMap<>(16);
        Map<Integer, Map<String, BigDecimal>> transferInChangeMap = new HashMap<>(16);
        Map<Integer, Map<String, BigDecimal>> transferOutChangeMap = new HashMap<>(16);
        Map<Integer, Map<String, BigDecimal>> transferOtherChangeMap = new HashMap<>(16);
        for (AssetsBillCoinTypeProperty assetsBillCoinTypeProperty : ledgerTimeSliceDTO.getAssetsBillCoinTypePropertyList()) {
            String bizType = assetsBillCoinTypeProperty.getBizType();
            if (MatchUtils.checkValidate(assetsCheckConfig.getTransferIn(), bizType)) {
                transferInMap = collectCoinIdTypeAssets(transferInMap, assetsBillCoinTypeProperty);
                transferInChangeMap = collectCoinIdTypeChangeAssets(transferInChangeMap, assetsBillCoinTypeProperty);
            } else if (MatchUtils.checkValidate(assetsCheckConfig.getTransferOut(), bizType)) {
                transferOutMap = collectCoinIdTypeAssets(transferOutMap, assetsBillCoinTypeProperty);
                transferOutChangeMap = collectCoinIdTypeChangeAssets(transferOutChangeMap, assetsBillCoinTypeProperty);
            } else {
                transferOtherMap = collectCoinIdTypeAssets(transferOtherMap, assetsBillCoinTypeProperty);
                transferOtherChangeMap = collectCoinIdTypeChangeAssets(transferOtherChangeMap, assetsBillCoinTypeProperty);
            }
        }

        // 总资产对比 + 入出对比
        Map<String, PriceVo> allRate = commonService.getRatesToUSDT();
        CheckResult checkResult = CheckResult.success(assetsCheckConfig.getAssetsCheckType(), assetsCheckConfig.getAssetsCheckParam(), ledgerTimeSliceDTO.getCheckTime());
        Map<Integer, AssetsBillCoinProperty> totalAssetsCoinPropertyMap = ledgerTimeSliceDTO.getAssetsBillCoinPropertyList().stream().collect(Collectors.toMap(AssetsBillCoinProperty::getCoinId, key -> key));
        Map<Integer, BillCoinProperty> totalCoinPropertyMap = ledgerTimeSliceDTO.getTotalCoinPropertyMap();
        for (Map.Entry<Integer, BillCoinProperty> coinPropertyEntry : totalCoinPropertyMap.entrySet()) {
            Integer coinId = coinPropertyEntry.getKey();
            // 业务线资产和总账合并资产对比
            AssetsBillCoinProperty assetsBillCoinProperty = totalAssetsCoinPropertyMap.get(coinId);
            if (assetsBillCoinProperty != null) {
                if (coinPropertyEntry.getValue().getPropSum().compareTo(assetsBillCoinProperty.getPropSum()) != 0) {
                    log.error("BillLedgerCheckModule checkInAndOutAssets ledger prop compare error assetsCheckType:{} checkTime:{} coinId:{} propSum:{} assetsPropSum:{}", assetsCheckTypeEnum.getCode(), DateUtil.date2str(ledgerTimeSliceDTO.getCheckTime()), coinId, coinPropertyEntry.getValue().getPropSum(), assetsBillCoinProperty.getPropSum());
                }
            }

            // 入出对账
            BigDecimal totalBalance = coinPropertyEntry.getValue().getPropSum();
            BigDecimal transferIn = transferInMap.computeIfAbsent(coinId, k -> new HashMap<String, BigDecimal>()).values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal transferOut = transferOutMap.computeIfAbsent(coinId, k -> new HashMap<String, BigDecimal>()).values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            // 初始值 / 待动账
            BigDecimal initValue = transferDiffTolerateExitValueMap.getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal feeTransferCount = ledgerTimeSliceDTO.getTotalTransferFeeMaps().getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal interestFeeTransferCount = ledgerTimeSliceDTO.getTotalTransferInterestFeeMaps().getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal spotInitUnProfitTransferCount = ledgerTimeSliceDTO.getSpotUnProfitInitValueMaps().getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal contractInitUnProfitTransferCount = ledgerTimeSliceDTO.getContractUnProfitInitValueMaps().getOrDefault(coinId, BigDecimal.ZERO);
            BigDecimal transferCount = feeTransferCount.add(interestFeeTransferCount).add(spotInitUnProfitTransferCount).add(contractInitUnProfitTransferCount);
            // 公式调整  总资产 - (入+出) + 待动账金额 - 初始值= 0
            BigDecimal diffBalance = totalBalance.subtract(transferIn.add(transferOut)).add(transferCount).subtract(initValue);

            Map<String, Object> logSumDetailMap = new HashMap<>();
            logSumDetailMap.put("bizTotalReceivableFeeMaps", ledgerTimeSliceDTO.getBizTotalReceivableFeeMaps().get(coinId));
            logSumDetailMap.put("bizTotalReceivedFeeMaps", ledgerTimeSliceDTO.getBizTotalReceivedFeeMaps().get(coinId));
            logSumDetailMap.put("bizChangeReceivableFeeMaps", ledgerTimeSliceDTO.getBizChangeReceivableFeeMaps().get(coinId));
            logSumDetailMap.put("bizChangeReceivedFeeMaps", ledgerTimeSliceDTO.getBizChangeReceivedFeeMaps().get(coinId));
            logSumDetailMap.put("bizTotalReceivableInterestFeeMaps", ledgerTimeSliceDTO.getBizTotalReceivableInterestFeeMaps().get(coinId));
            logSumDetailMap.put("bizChangeReceivableInterestFeeMaps", ledgerTimeSliceDTO.getBizChangeReceivableInterestFeeMaps().get(coinId));
            logSumDetailMap.put("bizTotalReceivedInterestFeeMaps", ledgerTimeSliceDTO.getBizTotalReceivedInterestFeeMaps().get(coinId));
            logSumDetailMap.put("bizChangeReceivedInterestFeeMaps", ledgerTimeSliceDTO.getBizChangeReceivedInterestFeeMaps().get(coinId));
            logSumDetailMap.put("bizSpotUnProfitInitValueMaps", ledgerTimeSliceDTO.getBizSpotUnProfitInitValueMaps().get(coinId));
            logSumDetailMap.put("bizContractUnProfitInitValueMaps", ledgerTimeSliceDTO.getBizContractUnProfitInitValueMaps().get(coinId));
            logSumDetailMap.put("totalBalance", totalBalance);
            logSumDetailMap.put("transferIn", transferIn);
            logSumDetailMap.put("transferOut", transferOut);
            logSumDetailMap.put("initValue", initValue);
            logSumDetailMap.put("feeTransferCount", feeTransferCount);
            logSumDetailMap.put("interestFeeTransferCount", interestFeeTransferCount);
            logSumDetailMap.put("spotInitUnProfitTransferCount", spotInitUnProfitTransferCount);
            logSumDetailMap.put("contractInitUnProfitTransferCount", contractInitUnProfitTransferCount);
            logSumDetailMap.put("transferCount", transferCount);
            logSumDetailMap.put("diffBalance", diffBalance);
            log.info("BillLedgerCheckModule.checkInAndOutAssets info assetsCheckType:{} checkTime:{} diffBalance:{} coinId:{} data:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(ledgerTimeSliceDTO.getCheckTime()), diffBalance, coinId, JSON.toJSONString(logSumDetailMap));
            // 公式计算
            if (diffBalance.compareTo(BigDecimal.ZERO) != 0) {
                // 将币种折算成USDT，然后和容忍值对比,容忍值暂时为10
                BigDecimal rate = commonService.checkRateByCoinIdAndReturnUSDT(coinId, allRate);
                if (diffBalance.abs().multiply(rate).compareTo(assetsCheckConfig.getTotalDiffTolerateExitValue()) < 0) {
                    log.warn("assets check not equals but in totalDiffTolerateExitValue assetsCheckType:{} checkTime:{} not equals coinId:{},diff balance:{} rate:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(ledgerTimeSliceDTO.getCheckTime()), coinId, diffBalance, rate);
                } else {
                    result = false;
                    checkResult.setResult(false);
                    checkResult.addLedgerInOutProfitCheckData(coinId, totalBalance, transferIn, transferOut, transferCount, initValue, diffBalance, BigDecimal.ZERO, Collections.emptyMap());
                    // 打印错误日志
                    Map<String, Object> logBizTypeDetailMap = new HashMap<>();
                    logBizTypeDetailMap.put("transferInMap", transferInMap.get(coinId));
                    logBizTypeDetailMap.put("transferOutMap", transferOutMap.get(coinId));
                    logBizTypeDetailMap.put("transferOtherMap", transferOtherMap.get(coinId));
                    logBizTypeDetailMap.put("transferInChangeMap", transferInChangeMap.get(coinId));
                    logBizTypeDetailMap.put("transferOutChangeMap", transferOutChangeMap.get(coinId));
                    logBizTypeDetailMap.put("transferOtherChangeMap", transferOtherChangeMap.get(coinId));
                    log.error("BillLedgerCheckModule.checkInAndOutAssets error assetsCheckType {} checkTime={} coinId={} logSumDetailMap:{} logBizTypeDetailMap:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(ledgerTimeSliceDTO.getCheckTime()), coinId, JSON.toJSONString(logSumDetailMap), JSON.toJSONString(logBizTypeDetailMap));
                    if (assetsCheckConfig.isLedgerInoutCheckLogCoinTypeDetail()) {
                        List<AssetsBillCoinTypeProperty> coinTypeProperties = ledgerTimeSliceDTO.getAssetsBillCoinTypePropertyList().stream().filter(item -> item.getCoinId().equals(coinId)).collect(Collectors.toList());
                        log.info("BillLedgerCheckModule.checkInAndOutAssets error assetsCheckType {} checkTime={} coinId={} coinTypeDetail:{}", assetsCheckConfig.getAssetsCheckType(), DateUtil.date2str(ledgerTimeSliceDTO.getCheckTime()), coinId, JSON.toJSONString(coinTypeProperties));
                    }
                }
            }
        }
        if (checkResult.isFail()) {
            alarmNotifyService.alarm(assetsCheckConfig.getAssetsCheckType(), CHECK_LEDGER_IN_AND_OUT_ASSETS_TEMPLATE, Map.of("checkResult", checkResult));
        }
        return result;
    }

    /**
     * 数据累加
     *
     * @param assetsMap
     * @param assetsBillCoinTypeProperty
     * @return
     */
    private Map<Integer, Map<String, BigDecimal>> collectCoinIdTypeAssets(Map<Integer, Map<String, BigDecimal>> assetsMap, AssetsBillCoinTypeProperty assetsBillCoinTypeProperty) {
        Integer coinId = assetsBillCoinTypeProperty.getCoinId();
        String bizType = assetsBillCoinTypeProperty.getBizType();
        BigDecimal balance = getOneBillCoinTotalAssets(assetsBillCoinTypeProperty);
        Map<String, BigDecimal> bizTypeMap = assetsMap.computeIfAbsent(coinId, key -> new HashMap<>());
        bizTypeMap.put(bizType, bizTypeMap.getOrDefault(bizType, BigDecimal.ZERO).add(balance));
        return assetsMap;
    }

    private Map<Integer, Map<String, BigDecimal>> collectCoinIdTypeChangeAssets(Map<Integer, Map<String, BigDecimal>> assetsMap, AssetsBillCoinTypeProperty assetsBillCoinTypeProperty) {
        Integer coinId = assetsBillCoinTypeProperty.getCoinId();
        String bizType = assetsBillCoinTypeProperty.getBizType();
        BigDecimal balance = getOneBillCoinTotalChangeAssets(assetsBillCoinTypeProperty);
        Map<String, BigDecimal> bizTypeMap = assetsMap.computeIfAbsent(coinId, key -> new HashMap<>());
        bizTypeMap.put(bizType, bizTypeMap.getOrDefault(bizType, BigDecimal.ZERO).add(balance));
        return assetsMap;
    }

    private BigDecimal getOneBillCoinTotalAssets(AssetsBillCoinTypeProperty bill) {
        if (bill.getBizType().startsWith(AccountTypeEnum.LEVER_FULL.getBizTypePrefix())
                || bill.getBizType().startsWith(AccountTypeEnum.LEVER_ONE.getBizTypePrefix())) {
            return bill.getProp1().add(bill.getProp2());
        } else if (bill.getBizType().startsWith(AccountTypeEnum.USDT_MIX_CONTRACT_BL.getBizTypePrefix())
                || bill.getBizType().startsWith(AccountTypeEnum.USD_MIX_CONTRACT_BL.getBizTypePrefix())
                || bill.getBizType().startsWith(AccountTypeEnum.USDC_MIX_CONTRACT_BL.getBizTypePrefix())
                || bill.getBizType().startsWith(AccountTypeEnum.UNIFIED.getBizTypePrefix())) {
            return bill.getProp2().add(bill.getProp3());
        }
        return bill.getProp1().add(bill.getProp2()).add(bill.getProp3())
                .add(bill.getProp4()).add(bill.getProp5());
    }

    private BigDecimal getOneBillCoinTotalChangeAssets(AssetsBillCoinTypeProperty bill) {
        if (bill.getBizType().startsWith(AccountTypeEnum.LEVER_FULL.getBizTypePrefix())
                || bill.getBizType().startsWith(AccountTypeEnum.LEVER_ONE.getBizTypePrefix())) {
            return bill.getChangeProp1().add(bill.getChangeProp2());
        } else if (bill.getBizType().startsWith(AccountTypeEnum.USDT_MIX_CONTRACT_BL.getBizTypePrefix())
                || bill.getBizType().startsWith(AccountTypeEnum.USD_MIX_CONTRACT_BL.getBizTypePrefix())
                || bill.getBizType().startsWith(AccountTypeEnum.USDC_MIX_CONTRACT_BL.getBizTypePrefix())
                || bill.getBizType().startsWith(AccountTypeEnum.UNIFIED.getBizTypePrefix())) {
            return bill.getChangeProp2().add(bill.getChangeProp3());
        }
        return bill.getChangeProp1().add(bill.getChangeProp2()).add(bill.getChangeProp3())
                .add(bill.getChangeProp4()).add(bill.getChangeProp5());
    }

    private void cleanOtherProperty(Byte accountType, AbstractProperty billCoinProperty) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        if (accountTypeEnum.isLever()) {
            billCoinProperty.setProp3(BigDecimal.ZERO);
            billCoinProperty.setProp4(BigDecimal.ZERO);
            billCoinProperty.setProp5(BigDecimal.ZERO);
        } else if (accountTypeEnum.isContract()) {
            //混合合约校验prop2,prop3
            billCoinProperty.setProp1(BigDecimal.ZERO);
            billCoinProperty.setProp4(BigDecimal.ZERO);
            billCoinProperty.setProp5(BigDecimal.ZERO);
        } else if (accountTypeEnum.isUta()) {
            billCoinProperty.setProp1(BigDecimal.ZERO);
            billCoinProperty.setProp4(BigDecimal.ZERO);
            billCoinProperty.setProp5(BigDecimal.ZERO);
            billCoinProperty.setProp6(BigDecimal.ZERO);
            billCoinProperty.setProp7(BigDecimal.ZERO);
        }
    }

    private Map<Integer, BigDecimal> getSpotInitUnProfitTransfers() {
        List<BillCapitalInitProperty> capitalInitPropertyList = billCapitalInitPropertyService.selectRecords(String.valueOf(AccountTypeEnum.SPOT.getCode()), AccountTypeEnum.SPOT.getAccountParam(), CapitalInitBusinessTypeEnum.SPOT_UN_PROFIT_TRANSFER.getCode());
        Map<Integer, BigDecimal> capitalInitPropertyMap = capitalInitPropertyList.stream().collect(Collectors.toMap(BillCapitalInitProperty::getCoinId, BillCapitalInitProperty::getInitValue));
        return capitalInitPropertyMap;
    }

    public Date getLastCheckOkTime() {
        return lastCheckOkTime;
    }

    /**
     * 合并两个map求和
     *
     * @param map1
     * @param map2
     * @return
     */
    private Map<Integer, BigDecimal> mergeSumMaps(Map<Integer, BigDecimal> map1, Map<Integer, BigDecimal> map2) {
        Set<Integer> uionCoindIdSet = new HashSet<>();
        uionCoindIdSet.addAll(map1.keySet());
        uionCoindIdSet.addAll(map2.keySet());
        Map<Integer, BigDecimal> mergeMaps = new HashMap();
        for (Integer coinId : uionCoindIdSet) {
            mergeMaps.put(coinId, map1.getOrDefault(coinId, BigDecimal.ZERO).add(map2.getOrDefault(coinId, BigDecimal.ZERO)));
        }
        return mergeMaps;
    }

    /**
     * 汇总 业务线 数据
     *
     * @param dataMap
     * @param coinId
     * @param accountType
     * @param value
     * @return
     */
    private Map<Integer, Map<Byte, BigDecimal>> sumMapByCoinAndAccountType(Map<Integer, Map<Byte, BigDecimal>> dataMap, Integer coinId, Byte accountType, BigDecimal value) {
        Map<Byte, BigDecimal> subMap = dataMap.computeIfAbsent(coinId, k -> new HashMap<>());
        subMap.put(accountType, subMap.getOrDefault(accountType, BigDecimal.ZERO).add(value));
        return dataMap;
    }

    /**
     * 汇总总账应收实收
     *
     * @param accountTypeEnum
     * @param ledgerTimeSliceDTO
     * @param billCoinTypePropertyMap
     */
    private void sumLedgerReceivableAbdReceived(AccountTypeEnum accountTypeEnum, BillLedgerTimeSliceDTO ledgerTimeSliceDTO, Map<Integer, BillCoinProperty> billCoinPropertyMap, Map<String, BillCoinTypeProperty> billCoinTypePropertyMap) {
        Byte accountType = accountTypeEnum.getCode();
        BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountType);
        ApolloReconciliationBizConfig apolloBillConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(Byte.valueOf(accountType));

        for (Map.Entry<String, BillCoinTypeProperty> coinPropertyEntry : billCoinTypePropertyMap.entrySet()) {
            BillCoinTypeProperty billCoinTypeProperty = coinPropertyEntry.getValue();
            Integer coinId = billCoinTypeProperty.getCoinId();
            // 计算手续费实收
            if (billCoinTypeProperty.getBizType().equals(apolloBillConfig.getProfitTransferFeeCoinType()) ||
                    billCoinTypeProperty.getBizType().equals(apolloBillConfig.getProfitTransferFeeRecycleCoinType())) {
                BigDecimal totalFee = billCheckService.getPropSumByProperty(billCoinTypeProperty);
                BigDecimal changeFee = billCheckService.getChangePropSumByProperty(billCoinTypeProperty);

                ledgerTimeSliceDTO.getTotalReceiveFeeMaps().put(coinId, ledgerTimeSliceDTO.getTotalReceiveFeeMaps().getOrDefault(coinId, BigDecimal.ZERO).add(totalFee));
                ledgerTimeSliceDTO.getCurrentChangeReceiveFeeMaps().put(coinId, ledgerTimeSliceDTO.getCurrentChangeReceiveFeeMaps().getOrDefault(coinId, BigDecimal.ZERO).add(changeFee));

                this.sumMapByCoinAndAccountType(ledgerTimeSliceDTO.getBizTotalReceivedFeeMaps(), coinId, accountType, totalFee);
                this.sumMapByCoinAndAccountType(ledgerTimeSliceDTO.getBizChangeReceivedFeeMaps(), coinId, accountType, changeFee);
            }
            // 计算合约利息应收
            if (billCoinTypeProperty.getBizType().equals(apolloBillConfig.getContractInterestDealFeeBizType())) {
                BigDecimal totalInterestFee = billCheckService.getPropSumByProperty(billCoinTypeProperty);
                BigDecimal changeInterestFee = billCheckService.getChangePropSumByProperty(billCoinTypeProperty).negate();

                ledgerTimeSliceDTO.getTotalInterestFeeMaps().put(coinId, ledgerTimeSliceDTO.getTotalInterestFeeMaps().getOrDefault(coinId, BigDecimal.ZERO).add(totalInterestFee));
                ledgerTimeSliceDTO.getCurrentChangeInterestFeeMaps().put(coinId, ledgerTimeSliceDTO.getCurrentChangeInterestFeeMaps().getOrDefault(coinId, BigDecimal.ZERO).add(changeInterestFee));

                this.sumMapByCoinAndAccountType(ledgerTimeSliceDTO.getBizTotalReceivableInterestFeeMaps(), coinId, accountType, totalInterestFee);
                this.sumMapByCoinAndAccountType(ledgerTimeSliceDTO.getBizChangeReceivableInterestFeeMaps(), coinId, accountType, changeInterestFee);
            }
            // 计算合约利息实收
            if (billCoinTypeProperty.getBizType().equals(apolloBillConfig.getProfitTransferInterestFeeCoinType()) ||
                    billCoinTypeProperty.getBizType().equals(apolloBillConfig.getProfitTransferInterestFeeRecycleCoinType())) {
                ledgerTimeSliceDTO.getTotalReceiveInterestFeeMaps().put(coinId, ledgerTimeSliceDTO.getTotalReceiveInterestFeeMaps().getOrDefault(coinId, BigDecimal.ZERO).add(billCheckService.getPropSumByProperty(billCoinTypeProperty)));
                ledgerTimeSliceDTO.getCurrentChangeReceiveInterestFeeMaps().put(coinId, ledgerTimeSliceDTO.getCurrentChangeReceiveInterestFeeMaps().getOrDefault(coinId, BigDecimal.ZERO).add(billCheckService.getChangePropSumByProperty(billCoinTypeProperty)));

                this.sumMapByCoinAndAccountType(ledgerTimeSliceDTO.getBizTotalReceivedInterestFeeMaps(), coinId, accountType, billCoinTypeProperty.getProp2());
                this.sumMapByCoinAndAccountType(ledgerTimeSliceDTO.getBizTotalReceivedInterestFeeMaps(), coinId, accountType, billCoinTypeProperty.getProp2());
            }
        }
        // 计算币种应收手续费
        billCoinPropertyMap.values().forEach(billCoinProperty -> {
            Integer coinId = billCoinProperty.getCoinId();
            BigDecimal totalFee = billCoinProperty.getFee();
            BigDecimal changeFee = billCoinProperty.getChangeFee().negate();

            ledgerTimeSliceDTO.getTotalFeeMaps().put(coinId, ledgerTimeSliceDTO.getTotalFeeMaps().getOrDefault(coinId, BigDecimal.ZERO).add(totalFee));
            ledgerTimeSliceDTO.getCurrentChangeFeeMaps().put(coinId, ledgerTimeSliceDTO.getCurrentChangeFeeMaps().getOrDefault(coinId, BigDecimal.ZERO).add(changeFee));

            this.sumMapByCoinAndAccountType(ledgerTimeSliceDTO.getBizTotalReceivableFeeMaps(), coinId, accountType, totalFee);
            this.sumMapByCoinAndAccountType(ledgerTimeSliceDTO.getBizChangeReceivableFeeMaps(), coinId, accountType, changeFee);
        });
        // 计算合约待动账初始值
        Map<Integer, BigDecimal> contractCapitalInitPropertyMap = billCapitalInitPropertyService.selectRecords(String.valueOf(accountType), apolloBillConfig.getAccountParam(), CapitalInitBusinessTypeEnum.CONTRACT_UN_PROFIT_TRANSFER.getCode()).stream().collect(Collectors.toMap(BillCapitalInitProperty::getCoinId, BillCapitalInitProperty::getInitValue));
        contractCapitalInitPropertyMap.forEach((coinId, initValue) -> {
            ledgerTimeSliceDTO.getContractUnProfitInitValueMaps().put(coinId, ledgerTimeSliceDTO.getContractUnProfitInitValueMaps().getOrDefault(coinId, BigDecimal.ZERO).add(initValue));
            this.sumMapByCoinAndAccountType(ledgerTimeSliceDTO.getBizContractUnProfitInitValueMaps(), coinId, accountType, initValue);
        });
        // 计算现货动账初始值
        Map<Integer, BigDecimal> spotCapitalInitPropertyMap = billCapitalInitPropertyService.selectRecords(String.valueOf(accountType), apolloBillConfig.getAccountParam(), CapitalInitBusinessTypeEnum.SPOT_UN_PROFIT_TRANSFER.getCode()).stream().collect(Collectors.toMap(BillCapitalInitProperty::getCoinId, BillCapitalInitProperty::getInitValue));
        spotCapitalInitPropertyMap.forEach((coinId, initValue) -> {
            ledgerTimeSliceDTO.getSpotUnProfitInitValueMaps().put(coinId, ledgerTimeSliceDTO.getSpotUnProfitInitValueMaps().getOrDefault(coinId, BigDecimal.ZERO).add(initValue));
            this.sumMapByCoinAndAccountType(ledgerTimeSliceDTO.getBizSpotUnProfitInitValueMaps(), coinId, accountType, initValue);
        });
    }

    /**
     * 合并业务线盈亏换汇数据
     *
     * @param ledgerTimeSliceDTO
     * @param assetsCheckConfig
     * @param calculateExchangeProfitTransfer 是否计算动账数据，true=计算，false=不计算 不计算主要用于业务线出现仓位负值情况
     */
    public void mergeLedgerExchangeAndProfit(BillLedgerTimeSliceDTO ledgerTimeSliceDTO, AssetsCheckConfig assetsCheckConfig, Boolean calculateExchangeProfitTransfer) {
        Date nowDate = new Date();
        Date checkTime = ledgerTimeSliceDTO.getCheckTime();
        Map<Integer, PriceVo> rates = commonService.getCoinIdRatesMapCache(checkTime.getTime());
        // 合并业务线盈亏
        Map<String, List<BillContractProfitCoinDetail>> contractProfitCoinDetailMap = ledgerTimeSliceDTO.getContractProfitCoinDetailList().stream().collect(Collectors.groupingBy(BillContractProfitCoinDetail::getCoinProfitType));
        Map<String, AssetsContractProfitCoinDetail> lastContractProfitCoinDetailMap = lastLedgerSaveTimeSliceDTO.getAssetsContractProfitCoinDetailList()
                .stream().collect(Collectors.toMap(AssetsContractProfitCoinDetail::getCoinProfitType, item -> item));
        List<AssetsContractProfitCoinDetail> assetsContractProfitCoinDetailList = new ArrayList<>();
        List<AssetsContractProfitCoinDetail> insertAssetsContractProfitCoinDetailList = new ArrayList<>();
        for (Map.Entry<String, List<BillContractProfitCoinDetail>> entry : contractProfitCoinDetailMap.entrySet()) {
            BigDecimal totalRealizedCount = BigDecimal.ZERO;
            BigDecimal totalChangeRealizedCount = BigDecimal.ZERO;
            BigDecimal totalUnrealizedCount = BigDecimal.ZERO;
            BigDecimal totalInitCount = BigDecimal.ZERO;
            BigDecimal totalProfitCountIncr = BigDecimal.ZERO;
            for (BillContractProfitCoinDetail contractProfitCoinDetail : entry.getValue()) {
                totalRealizedCount = totalRealizedCount.add(contractProfitCoinDetail.getRealizedCount());
                totalChangeRealizedCount = totalChangeRealizedCount.add(contractProfitCoinDetail.getChangeRealizedCount());
                totalUnrealizedCount = totalUnrealizedCount.add(contractProfitCoinDetail.getUnrealizedCount());
                totalInitCount = totalInitCount.add(contractProfitCoinDetail.getInitCount());
                totalProfitCountIncr = totalProfitCountIncr.add(contractProfitCoinDetail.getProfitCountIncr());
            }
            BigDecimal profitCount = BigDecimal.ZERO.subtract(totalRealizedCount.add(totalUnrealizedCount).add(totalInitCount));
            BillContractProfitCoinDetail billContractProfitCoinDetail = entry.getValue().get(0);
            BigDecimal rate = commonService.checkRateBySwapTokenIdReturnUSDT(billContractProfitCoinDetail.getCoinId(), rates);
            long batchNo = noGenerator.nextNo();
            AssetsContractProfitCoinDetail contractProfitCoinDetail = new AssetsContractProfitCoinDetail();
            contractProfitCoinDetail.setId(noGenerator.nextNo());
            contractProfitCoinDetail.setBatchNo(batchNo);
            contractProfitCoinDetail.setAccountType(AccountTypeEnum.INTERNAL.getCode());
            contractProfitCoinDetail.setAccountParam(AccountTypeEnum.INTERNAL.getAccountParam());
            contractProfitCoinDetail.setCoinId(billContractProfitCoinDetail.getCoinId());
            contractProfitCoinDetail.setCheckOkTime(checkTime);
            contractProfitCoinDetail.setRealizedCount(totalRealizedCount);
            contractProfitCoinDetail.setChangeRealizedCount(totalChangeRealizedCount);
            contractProfitCoinDetail.setUnrealizedCount(totalUnrealizedCount);
            contractProfitCoinDetail.setInitCount(totalInitCount);
            contractProfitCoinDetail.setProfitCount(profitCount);
            contractProfitCoinDetail.setRate(rate);
            contractProfitCoinDetail.setProfitAmount(profitCount.multiply(rate));
            contractProfitCoinDetail.setStatus(ProfitTransferStatusEnum.UN_TRANSFER.getCode());
            contractProfitCoinDetail.setProfitType(billContractProfitCoinDetail.getProfitType());
            contractProfitCoinDetail.setCreateTime(nowDate);
            contractProfitCoinDetail.setUpdateTime(nowDate);
            AssetsContractProfitCoinDetail lastContractProfitCoinDetail = lastContractProfitCoinDetailMap.get(contractProfitCoinDetail.getCoinProfitType());
            if (lastContractProfitCoinDetail != null) {
                BigDecimal profitCountIncr = contractProfitCoinDetail.getProfitCount().subtract(lastContractProfitCoinDetail.getProfitCount());
                contractProfitCoinDetail.setProfitCountIncr(profitCountIncr);
                assetsContractProfitCoinDetailList.add(contractProfitCoinDetail);
            } else {
                contractProfitCoinDetail.setProfitCountIncr(contractProfitCoinDetail.getProfitCount());
                if (assetsCheckConfig.getLedgerMergeLastCoinProfitNotExistErrorOpen()) {
                    insertAssetsContractProfitCoinDetailList.add(contractProfitCoinDetail);
                } else {
                    assetsContractProfitCoinDetailList.add(contractProfitCoinDetail);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(insertAssetsContractProfitCoinDetailList)) {
            log.info("BillLedgerCheckModule.timeSliceMerge need insert init AssetsContractProfitCoinDetail data:{}", JSON.toJSONString(insertAssetsContractProfitCoinDetailList));
            throw new RuntimeException("总账合并盈亏换汇动账，上期数据不存在，请手动导入！");
        }
        ledgerTimeSliceDTO.setAssetsContractProfitCoinDetailList(assetsContractProfitCoinDetailList);

        // 合并业务线换汇数据
        Map<String, List<BillContractProfitSymbolDetail>> contractProfitSymbolDetailMap = ledgerTimeSliceDTO.getContractProfitSymbolDetailList().stream().collect(Collectors.groupingBy(BillContractProfitSymbolDetail::groupBySymbolCoinProfitType));
        Map<String, AssetsContractProfitSymbolDetail> lastContractProfitSymbolDetailMap = lastLedgerSaveTimeSliceDTO.getAssetsContractProfitSymbolDetailList()
                .stream().collect(Collectors.toMap(AssetsContractProfitSymbolDetail::groupBySymbolCoinProfitType, item -> item));
        List<AssetsContractProfitSymbolDetail> assetsContractProfitSymbolDetailList = new ArrayList<>();
        for (Map.Entry<String, List<BillContractProfitSymbolDetail>> entry : contractProfitSymbolDetailMap.entrySet()) {
            BigDecimal totalChangeRealizedCount = BigDecimal.ZERO;
            BigDecimal totalRealizedCount = BigDecimal.ZERO;
            BigDecimal totalUnrealizedCount = BigDecimal.ZERO;
            BigDecimal totalInitCount = BigDecimal.ZERO;
            BigDecimal totalProfitCountIncr = BigDecimal.ZERO;
            for (BillContractProfitSymbolDetail contractProfitSymbolDetail : entry.getValue()) {
                totalRealizedCount = totalRealizedCount.add(contractProfitSymbolDetail.getRealizedCount());
                totalChangeRealizedCount = totalChangeRealizedCount.add(contractProfitSymbolDetail.getChangeRealizedCount());
                totalUnrealizedCount = totalUnrealizedCount.add(contractProfitSymbolDetail.getUnrealizedCount());
                totalInitCount = totalInitCount.add(contractProfitSymbolDetail.getInitCount());
                totalProfitCountIncr = totalProfitCountIncr.add(contractProfitSymbolDetail.getProfitCountIncr());
            }
            BigDecimal profitCount = totalRealizedCount.add(totalUnrealizedCount).add(totalInitCount).negate();
            BillContractProfitSymbolDetail billContractProfitSymbolDetail = entry.getValue().get(0);
            long batchNo = noGenerator.nextNo();
            AssetsContractProfitSymbolDetail assetsContractProfitSymbolDetail = new AssetsContractProfitSymbolDetail();
            assetsContractProfitSymbolDetail.setId(noGenerator.nextNo());
            assetsContractProfitSymbolDetail.setBatchNo(batchNo);
            assetsContractProfitSymbolDetail.setAccountType(AccountTypeEnum.INTERNAL.getCode());
            assetsContractProfitSymbolDetail.setAccountParam(AccountTypeEnum.INTERNAL.getAccountParam());
            assetsContractProfitSymbolDetail.setCheckOkTime(checkTime);
            assetsContractProfitSymbolDetail.setProfitType(billContractProfitSymbolDetail.getProfitType());
            assetsContractProfitSymbolDetail.setSymbolId(billContractProfitSymbolDetail.getSymbolId());
            assetsContractProfitSymbolDetail.setCoinId(billContractProfitSymbolDetail.getCoinId());
            assetsContractProfitSymbolDetail.setChangeRealizedCount(totalChangeRealizedCount);
            assetsContractProfitSymbolDetail.setRealizedCount(totalRealizedCount);
            assetsContractProfitSymbolDetail.setUnrealizedCount(totalUnrealizedCount);
            assetsContractProfitSymbolDetail.setInitCount(totalInitCount);
            assetsContractProfitSymbolDetail.setProfitCount(profitCount);
            assetsContractProfitSymbolDetail.setCreateTime(nowDate);
            assetsContractProfitSymbolDetail.setUpdateTime(nowDate);
            AssetsContractProfitSymbolDetail lastContractProfitSymbolDetail = lastContractProfitSymbolDetailMap.get(assetsContractProfitSymbolDetail.groupBySymbolCoinProfitType());
            if (lastContractProfitSymbolDetail != null) {
                BigDecimal profitCountIncr = assetsContractProfitSymbolDetail.getProfitCount().subtract(lastContractProfitSymbolDetail.getProfitCount());
                assetsContractProfitSymbolDetail.setProfitCountIncr(profitCountIncr);
            } else {
                // 首期默认业务线增量，不能使用profitcount
                assetsContractProfitSymbolDetail.setProfitCountIncr(totalProfitCountIncr);
            }
            assetsContractProfitSymbolDetailList.add(assetsContractProfitSymbolDetail);
        }
        ledgerTimeSliceDTO.setAssetsContractProfitSymbolDetailList(assetsContractProfitSymbolDetailList);

        // 盈亏换汇生成动账明细
        if (calculateExchangeProfitTransfer) {
            List<BillContractProfitTransfer> contractProfitTransferList = BillContractProfitTransfer.generateProfitFromProfitCoinDetail(assetsCheckConfig, noGenerator, ledgerTimeSliceDTO.getAssetsContractProfitCoinDetailList(), reconSystemAccountService);
            ledgerTimeSliceDTO.setBillContractProfitTransferList(contractProfitTransferList);
        } else {
            log.info("BillLedgerCheckModule.mergeLedgerExchangeAndProfit not calculate exchange profit transfer checkTime:{} contractProfitTransferList:{}", checkTime, JSON.toJSONString(ledgerTimeSliceDTO.getBillContractProfitTransferList()));
            // 清空动账数据 把盈亏换汇 赋值历史数据 把上一期赋值到当期 落库不保存
            ledgerTimeSliceDTO.setCalculateExchangeProfitTransfer(false);
            ledgerTimeSliceDTO.setBillContractProfitTransferList(new ArrayList<>());
            ledgerTimeSliceDTO.setAssetsContractProfitCoinDetailHisList(ledgerTimeSliceDTO.getAssetsContractProfitCoinDetailList());
            ledgerTimeSliceDTO.setAssetsContractProfitSymbolDetailHisList(ledgerTimeSliceDTO.getAssetsContractProfitSymbolDetailList());
            ledgerTimeSliceDTO.setAssetsContractProfitCoinDetailList(lastLedgerSaveTimeSliceDTO.getAssetsContractProfitCoinDetailList());
            ledgerTimeSliceDTO.setAssetsContractProfitSymbolDetailList(lastLedgerSaveTimeSliceDTO.getAssetsContractProfitSymbolDetailList());

        }
    }

    /**
     * 判断是否跳过盈亏对账
     *
     * @param symbolId
     * @param assetsCheckConfig
     * @return
     */
    private boolean isNotRecalculatePositionProfitSymbol(String symbolId, Date checkTime, AssetsCheckConfig assetsCheckConfig) {
        Set<String> notRecalculatePositionProfitSymbolSet = assetsCheckConfig.getNotRecalculatePositionProfitSymbolSet();
        if (CollectionUtils.isNotEmpty(notRecalculatePositionProfitSymbolSet)) {
            for (String symbolIdAndTime : notRecalculatePositionProfitSymbolSet) {
                // 0=symbolId,1=startTime,2=endTime
                String[] split = symbolIdAndTime.split(":");
                if (split.length != 3) {
                    continue;
                }
                if (symbolId.equals(split[0]) && checkTime.getTime() >= Long.parseLong(split[1]) && checkTime.getTime() <= Long.parseLong(split[2])) {
                    return true;
                }
            }
        }
        return false;
    }
}