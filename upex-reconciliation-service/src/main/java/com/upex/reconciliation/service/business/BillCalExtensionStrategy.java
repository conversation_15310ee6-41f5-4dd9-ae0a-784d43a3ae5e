package com.upex.reconciliation.service.business;


import com.upex.reconciliation.facade.dto.results.ReconAccountAssetsInfoResult;
import com.upex.reconciliation.service.dao.entity.BillCoinTypeUserProperty;
import com.upex.reconciliation.service.dao.entity.BillConfig;
import com.upex.reconciliation.service.dao.entity.CalTotalAssetsModel;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.dao.entity.BillCoinUserAssets;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-10-31 14:15
 * @desc 对账计算扩展服务
 **/
public interface BillCalExtensionStrategy {

    /**
     * 汇总上期期末资产+账单流水
     *
     * @param billList
     * @param billCoinTypeUserPropertyList
     * @param billConfig
     * @param apolloBillConfig
     * @return
     */
    List<BillCoinTypeUserProperty> collectLastAssetsByExtension(List<ReconAccountAssetsInfoResult> billList, List<BillCoinTypeUserProperty> billCoinTypeUser<PERSON>ropertyList, BillConfig billConfig, ApolloReconciliationBizConfig apolloBillConfig);

    /**
     * 计算总资产
     *
     * @param newBillCoinUserPropertyList
     * @param billList
     * @param apolloBillConfig
     * @param billConfig
     * @return
     */
    CalTotalAssetsModel calTotalAssetsByExtension(List<BillCoinTypeUserProperty> newBillCoinUserPropertyList,
                                                  List<ReconAccountAssetsInfoResult> billList, ApolloReconciliationBizConfig apolloBillConfig, BillConfig billConfig);


    /**
     * 使用扩展信息组装用户资产
     *
     * @param billConfig
     * @param endAssets
     * @return
     */
    List<BillCoinUserAssets> buildBillCoinUserAssetsByExtension(BillConfig billConfig, List<BillCoinTypeUserProperty> endAssets);
}
