package com.upex.reconciliation.service.business;

import com.upex.reconciliation.service.dao.entity.FinancialVirtualAccount;

import java.util.List;

public interface FinancialVirtualAccountBizService {

    int saveHandler(List<FinancialVirtualAccount> financialVirtualAccounts);

    void modifyHandler(List<FinancialVirtualAccount> financialVirtualAccounts);

    int fixHandler(Long minId, Integer pageSize);
}
