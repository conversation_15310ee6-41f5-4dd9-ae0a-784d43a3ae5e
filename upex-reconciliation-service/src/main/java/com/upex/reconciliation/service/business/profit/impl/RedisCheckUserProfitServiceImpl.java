package com.upex.reconciliation.service.business.profit.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.upex.reconciliation.facade.dto.results.ReconTotalAssetsDetailVo;
import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.dto.AccountProfitDTO;
import com.upex.reconciliation.service.utils.BillCoinCalculationUtils;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.RedisUtil;
import com.upex.ticker.facade.dto.PriceVo;
import com.upex.utils.task.TaskManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR>
 * @Date 2025/4/26
 */
@Slf4j
@Service
public class RedisCheckUserProfitServiceImpl extends AbstractCheckUserProfitService {

    @Resource(name = "reconRedisTemplate")
    private RedisTemplate<String, String> reconRedisTemplate;

    @Resource(name = "userRedisProfitTaskManager")
    protected TaskManager userRedisProfitTaskManager;

    @Override
    public AccountProfitDTO getUserProfitAmount(ReconCheckResultsParams params, GlobalBillConfig billConfig, Integer timePeriod, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates) {
        return getUserProfitAmount(params.getUserId(), billConfig, params.getRequestDate(), timePeriod, allCoinsMap, rates);
    }

    private AccountProfitDTO getUserProfitAmount(Long userId, GlobalBillConfig billConfig, Long requestDate, Integer timePeriod, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates) {
        Date date = new Date(requestDate);
        Long addFiveMinDate = requestDate;
        if (!Objects.equals(date, DateUtil.getLastFiveMin(date))) {
            addFiveMinDate = DateUtil.addMinute(date, 5).getTime();
        }
        Long beginDate = DateUtil.addMinute(date, BillConstants.NEG_ONE * timePeriod).getTime();
        List<String> subSystemList = billConfig.getCheckProfitSubSystemList();
        BigDecimal beginBalance = reconCheckBillResultService.getUserBeginAssets(userId, beginDate, allCoinsMap, rates, billConfig);
        Long endDate = addFiveMinDate;
        Map<Integer, PriceVo> ratesMap = commonService.getCoinIdRatesMapCache(requestDate);
        Map<Byte, AccountProfitDTO> totalAccountProfitMap = new ConcurrentHashMap<>();
        userRedisProfitTaskManager.forEachSubmitBatchAndWait(subSystemList, (String subSystem) -> {
            String[] subSystemArr = subSystem.split(BillConstants.SEPARATOR);
            Map<Integer, BigDecimal> userProfitMap = getRedisUserProfitAmount(userId, subSystemArr[0], beginDate, endDate);
            // 计算业务线盈利
            if(MapUtil.isNotEmpty(userProfitMap)){
                AccountProfitDTO accountProfitDTO = new AccountProfitDTO();
                accountProfitDTO.setProfitAmount(BillCoinCalculationUtils.calculateCoinUsdt(userProfitMap, ratesMap));
                accountProfitDTO.getProfitCoinMap().putAll(userProfitMap);
                totalAccountProfitMap.put(Byte.valueOf(subSystemArr[0]), accountProfitDTO);
            }
        });

        // 汇总所有业务线盈亏
        AccountProfitDTO totalAccountProfit = new AccountProfitDTO();
        totalAccountProfit.setBeginBalance(beginBalance);
        totalAccountProfit.setProfitAmount(totalAccountProfitMap.values().stream().map(AccountProfitDTO::getProfitAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        Map<Integer, BigDecimal> totalCoinProfitMap = new HashMap<>();
        totalAccountProfitMap.values().stream().forEach(accountProfit -> {
            accountProfit.getProfitCoinMap().forEach((coinId, profitAmount) -> {
                totalCoinProfitMap.compute(coinId, (k, oldValue) -> oldValue == null ? profitAmount : oldValue.add(profitAmount));
            });
        });
        totalAccountProfit.setProfitCoinMap(totalCoinProfitMap);
        totalAccountProfit.setAccountProfitMap(totalAccountProfitMap);
        return totalAccountProfit;
    }

    private Map<Integer, BigDecimal> getRedisUserProfitAmount(Long userId, String subSystem, Long beginDate, Long endDate) {
        Map<Integer, BigDecimal> profitCoinMap = new HashMap<>();
        String userCoinProfit = reconRedisTemplate.opsForValue().get(RedisUtil.getBusinessUserProfitIncrement(Byte.valueOf(subSystem), userId));
        log.info("RedisCheckUserProfitServiceImpl getRedisUserProfitAmount userId:{} subSystem:{} coinProfit:{}", userId, subSystem, userCoinProfit);
        if (StringUtils.isNotBlank(userCoinProfit)) {
            TreeMap<Long, Map<Integer, BigDecimal>> userCoinProfitMap = JSON.parseObject(userCoinProfit, new TypeReference<TreeMap<Long, Map<Integer, BigDecimal>>>() {});
            if (CollectionUtil.isNotEmpty(userCoinProfitMap)) {
                Map<Long, Map<Integer, BigDecimal>> timeSliceCoinProfitMap = userCoinProfitMap.subMap(beginDate, true, endDate, true);
                timeSliceCoinProfitMap.forEach((key, value) -> BillCoinCalculationUtils.mergeCoinAssets(value, profitCoinMap));
            }
        }
        return profitCoinMap;
    }

    @Override
    public Map<Long, AccountProfitDTO> getUserChildProfitAmount(ReconCheckResultsParams params, List<Long> childUserIdList, GlobalBillConfig billConfig, Integer timePeriod, Map<Integer, String> allCoinsMap, Map<Integer, PriceVo> rates) {
        Map<Long, AccountProfitDTO> accountProfitMap = new ConcurrentHashMap<>();
        assetUserProfitTaskManager.forEachSubmitBatchAndWait(childUserIdList, (Long userId) -> {
            AccountProfitDTO userAccountProfit = getUserProfitAmount(userId, billConfig, params.getRequestDate(), timePeriod, allCoinsMap, rates);
            log.info("RedisCheckUserProfitServiceImpl checkProfitAccount accountProfitMap:{} requestTime:{}, parentUserId:{} userId:{}, timePeriod:{}",
                    JSONObject.toJSONString(accountProfitMap), params.getRequestDate(), params.getUserId(), userId, timePeriod);
            accountProfitMap.put(userId, userAccountProfit);
        });
        return accountProfitMap;
    }
}
