package com.upex.reconciliation.service.common.constants.enums;

public enum CoinIdEnum {

    /**
     * 币种usdt
     */
    USDT(1,"usdt"),
    /**
     * 币种USDT
     */
    USDT_UPCASE(2,"USDT"),

    ;

    private Integer code;
    private String desc;

    CoinIdEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CoinIdEnum toEnum(Integer code) {
        for (CoinIdEnum item : CoinIdEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }

}
