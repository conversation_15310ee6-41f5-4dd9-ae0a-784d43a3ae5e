package com.upex.reconciliation.service.business;

import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.reconciliation.service.common.constants.enums.SymbolCoinPropEnum;
import com.upex.reconciliation.service.common.constants.enums.SymbolPropEnum;
import com.upex.reconciliation.service.dao.entity.*;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.*;
import com.upex.ticker.facade.dto.PriceVo;
import reactor.util.function.Tuple2;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

public interface BillCheckService {

    Byte getAccountType();

    boolean checkUserFlow(ApolloReconciliationBizConfig apolloBizConfig, Byte accountType, String accountUniqueId, Long userId, Integer coinId, List<CommonBillChangeData> billChangeDataList, BillCoinUserProperty billCoinUserProperty, List<CommonBillChangeData> errorCommonBillChangeDataList);

    boolean timeSliceBillCheck(BillTimeSliceDTO endSliceDTO, Date checkOkTime, ApolloReconciliationBizConfig apolloBizConfig, CommonService commonService);

    boolean checkBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty);

    boolean checkCurrentBillPropertyMatch(CommonBillChangeData commonBillChangeData, BillCoinUserProperty billCoinUserProperty);

    /**
     * 对比业务线资产和内存对账资产是否一致
     *
     * @param bizCoinUserProperty
     * @param memCoinUserProperty
     * @return
     */
    boolean checkCoinUserProperty(BillCoinUserProperty bizCoinUserProperty, BillCoinUserProperty memCoinUserProperty);

    /**
     * 获取用户资产值
     *
     * @param coinUserProperty
     * @return
     */
    BigDecimal getPropSumByUserProperty(BillCoinUserProperty coinUserProperty);


    <T extends AbstractProperty> boolean checkAssets(T property,
                                                     AccountAssetsInfoResult totalAccountAssetsInfoResult,
                                                     ApolloReconciliationBizConfig apolloBillConfig,
                                                     Map<Integer, PriceVo> ratesToUSDTCoinIdMap,
                                                     Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap);

    /**
     * 提币检测验证sprop是否正确
     *
     * @param property
     * @param totalAccountAssetsInfoResult
     * @param apolloBillConfig
     * @param ratesToUSDTCoinIdMap
     * @param billWalletSupplementConfigUserCoinMap
     * @param <T>
     * @return
     */
    <T extends AbstractSProperty> boolean checkSpropAssets(T property,
                                                           AccountAssetsInfoResult totalAccountAssetsInfoResult,
                                                           ApolloReconciliationBizConfig apolloBillConfig,
                                                           Map<Integer, PriceVo> ratesToUSDTCoinIdMap,
                                                           Map<String, BillWalletSupplementConfig> billWalletSupplementConfigUserCoinMap);

    /**
     * 忽略个人资产对账 消息进入errormap
     *
     * @param eachBill
     * @param billCoinUserProperty
     * @return
     */
    boolean ignoreBillPropertyMatch(CommonBillChangeData eachBill, BillCoinUserProperty billCoinUserProperty);

    /**
     * 获取coin维度资产
     *
     * @param billCoinProperty
     * @return
     */
    BigDecimal getPropSumByProperty(AbstractProperty billCoinProperty);

    /**
     * 获取业务线入账 应收/实收 手续费资产字段金额
     *
     * @param abstractProperty
     * @return
     */
    BigDecimal getPropByTransferFee(AbstractProperty abstractProperty);

    /**
     * 获取change资产值
     *
     * @param abstractProperty
     * @return
     */
    BigDecimal getChangePropSumByProperty(AbstractProperty abstractProperty);

    /**
     * 获取change资产值 list
     *
     * @param abstractProperty
     * @return
     */
    BigDecimal getChangePropSumByProperty(List<? extends AbstractProperty> abstractProperty);

    /**
     * 获取用户资产值
     *
     * @param currentBill
     * @return
     */
    BigDecimal getPropSumByBillChangeData(CommonBillChangeData currentBill);

    /**
     * 获取用户资产值
     *
     * @param currentBill
     * @return
     */
    BigDecimal getChangePropSumByBillChangeData(CommonBillChangeData currentBill);

    /**
     * 获取用户change资产值
     *
     * @param abstractProperty
     * @return
     */
    BigDecimal getChangePropSumByAbstractProperty(AbstractProperty abstractProperty);

    /**
     * 修复流水数据
     *
     * @param billCoinUserProperty
     * @param billChangeDataList
     * @return
     */
    boolean repairBillFlowByCoinUserProperty(BillCoinUserProperty billCoinUserProperty, List<CommonBillChangeData> billChangeDataList);


    /**
     * 获取业务线类型
     *
     * @return
     */
    List<String> getBusinessType();

    <T extends AbstractProperty> boolean checkPosition(T property, AccountAssetsInfoResult totalAccountAssetsInfoResult);


    <T extends AbstractProperty> boolean checkInitAssets(T oldProperty, BillCoinUserProperty newBillCoinUserProperty);

    <T extends AbstractProperty> boolean checkInitCoinAssets(T oldProperty, BillCoinProperty newBillCoinUserProperty);

    <T extends AbstractProperty> boolean checkInitCoinChangeAssets(T oldProperty, BillCoinProperty newBillCoinUserProperty);

    <T extends AbstractProperty> boolean checkInitCoinTypeAssets(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty);

    <T extends AbstractProperty> boolean checkInitCoinTypeChangeAssets(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty);


    <T extends AbstractProperty> boolean checkCoinAssetsByAccountType(T oldProperty, BillCoinProperty newBillCoinUserProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO);

    <T extends AbstractProperty> boolean checkCoinTypeInitAssetsByAccountType(List<T> oldPropertyList, BillCoinTypeProperty newBillCoinTypeProperty, Map<Integer, BigDecimal> coinComparisonToleranceMap, DataCalResultDTO dataCalResultDTO);

    <T extends AbstractProperty> boolean checkCoinTypeAssetsByAccountType(T oldProperty, BillCoinTypeProperty newBillCoinTypeProperty);

    <T extends AbstractProperty> BigDecimal sumForInAll(T property);

    /**
     * 获取流水换汇增量数据
     *
     * @param commonBillChangeData
     * @return
     */
    BigDecimal getExchangeDataChangeAssets(CommonBillChangeData commonBillChangeData);

    /**
     * 业务线coinType转换总账coinType 只转换changeproperty
     *
     * @param billCoinTypeProperty
     * @return
     */
    AssetsBillCoinTypeProperty convertToAssetsBillCoinTypeProperty(Byte accountType, BillCoinTypeProperty billCoinTypeProperty);

    /**
     * 获取仓位已实现资产
     *
     * @param abstractProperty
     * @return
     */
    BigDecimal getQuoteTokenAssetsByProperty(AbstractProperty abstractProperty);

    /**
     * 获取仓位已实现change资产
     *
     * @param abstractProperty
     * @return
     */
    BigDecimal getQuoteTokenChangeAssets(AbstractProperty abstractProperty);

    /**
     * 设置symbol 未实现 和 初始值
     *
     * @param billSymbolProperty
     * @param initValue
     * @param unRealized
     */
    void setInitAndUnRealizedProp(AbstractProperty billSymbolProperty, BigDecimal initValue, BigDecimal unRealized);

    /**
     * 设置symbol 多 空仓
     *
     * @param billSymbolProperty
     * @param sCountTotal
     * @param lCountTotal
     */
    void setSCountLCountProp(AbstractProperty billSymbolProperty, BigDecimal sCountTotal, BigDecimal lCountTotal);

    /**
     * 保证金已实现
     *
     * @param abstractProperty
     * @return
     */
    BigDecimal getMarginRealizedProp(AbstractProperty abstractProperty);

    /**
     * 保证金已实现变动
     *
     * @param abstractProperty
     * @return
     */
    BigDecimal getMarginRealizedChangeProp(AbstractProperty abstractProperty);

    /**
     * 处理动账手续费
     *
     * @param commonBillChangeData
     * @param billTimeSliceDTO
     */
    void recalculateTransferFee(CommonBillChangeData commonBillChangeData, BillTimeSliceDTO billTimeSliceDTO);

    /**
     * 消息计算时间片币对维度数据
     *
     * @param billTimeSliceDTO
     * @param commonBillChangeData
     */
    void calculateMsgTimeSliceSymbol(Byte accountType, BillTimeSliceDTO billTimeSliceDTO, CommonBillChangeData commonBillChangeData);

    /**
     * 消息计算时间片币对币种维度数据
     *
     * @param billTimeSliceDTO
     * @param commonBillChangeData
     */
    void calculateMsgTimeSliceSymbolCoin(Byte accountType, BillTimeSliceDTO billTimeSliceDTO, CommonBillChangeData commonBillChangeData);

    /**
     * BillSymbolProperty 设置值
     *
     * @param billSymbolProperty
     * @param symbolPropEnum
     */
    void setBillSymbolProperty(BillSymbolProperty billSymbolProperty, SymbolPropEnum symbolPropEnum, BigDecimal value);

    /**
     * BillSymbolCoinProperty 设置值
     *
     * @param billSymbolCoinProperty
     * @param symbolCoinPropEnum
     */
    void setBillSymbolCoinProperty(BillSymbolCoinProperty billSymbolCoinProperty, SymbolCoinPropEnum symbolCoinPropEnum, BigDecimal value);

    /**
     * BillSymbolProperty 设置值
     *
     * @param billSymbolProperty
     * @param supplier
     */
    void setBillSymbolProperty(BillSymbolProperty billSymbolProperty, Supplier<Tuple2<SymbolPropEnum, BigDecimal>> supplier);

    /**
     * BillSymbolCoinProperty 设置值
     *
     * @param billSymbolCoinProperty
     * @param supplier
     */
    void setBillSymbolCoinProperty(BillSymbolCoinProperty billSymbolCoinProperty, Supplier<Tuple2<SymbolCoinPropEnum, BigDecimal>> supplier);

    /**
     * BillSymbolProperty 获取
     *
     * @param billSymbolProperty
     * @param symbolPropEnum
     */
    BigDecimal getBillSymbolProperty(BillSymbolProperty billSymbolProperty, SymbolPropEnum symbolPropEnum);

    /**
     * BillSymbolCoinProperty 获取
     *
     * @param billSymbolCoinProperty
     * @param symbolCoinPropEnum
     */
    BigDecimal getBillSymbolCoinProperty(BillSymbolCoinProperty billSymbolCoinProperty, SymbolCoinPropEnum symbolCoinPropEnum);

    /**
     * 清理并设置资产属性 只重新负值资产字段 清除无效字段
     *
     * @param abstractProperty
     * @param accountType
     * @return
     */
    void cleanAndSetAssetsProperty(AbstractProperty abstractProperty, Byte accountType);
}
