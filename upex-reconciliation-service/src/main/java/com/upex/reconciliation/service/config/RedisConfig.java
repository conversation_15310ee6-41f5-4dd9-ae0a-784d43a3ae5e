package com.upex.reconciliation.service.config;

import com.upex.commons.support.util.AwsManagerUtils;
import com.upex.reconciliation.service.model.config.ApolloRedisConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.utils.model.RedisAuth;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

@Component
@Configuration
public class RedisConfig {
    @Primary
    @Bean
    public LettuceConnectionFactory lettuceConnectionFactory() {
        ApolloRedisConfig redisConfig = ReconciliationApolloConfigUtils.getBillRedisConfig();
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(redisConfig.getMaxIdle());
        genericObjectPoolConfig.setMinIdle(redisConfig.getMinIdle());
        genericObjectPoolConfig.setMaxTotal(redisConfig.getMaxActive());
        genericObjectPoolConfig.setMaxWaitMillis(redisConfig.getMaxWait());
        genericObjectPoolConfig.setTimeBetweenEvictionRunsMillis(100);
        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
        redisStandaloneConfiguration.setDatabase(redisConfig.getDatabase());
        redisStandaloneConfiguration.setHostName(redisConfig.getHost());
        redisStandaloneConfiguration.setPort(redisConfig.getPort());
        if (StringUtils.isNotBlank(redisConfig.getPassword())) {
            redisStandaloneConfiguration.setPassword(RedisPassword.of(redisConfig.getPassword()));
        }
        if (StringUtils.isNotBlank(redisConfig.getSecretName())) {
            RedisAuth redisAuth = AwsManagerUtils.getAwsSecretManagerRedisAuth(redisConfig.getEndpoint(), redisConfig.getRegion(), redisConfig.getSecretName());
            redisStandaloneConfiguration.setPassword(redisAuth.getPassword());
        }

        //ClusterTopologyRefreshOptions配置用于开启自适应刷新和定时刷新。
        //如自适应刷新不开启，Redis集群变更时将会导致连接异常！
        ClusterTopologyRefreshOptions topologyRefreshOptions = ClusterTopologyRefreshOptions.builder()
                //开启自适应刷新
                //.enableAdaptiveRefreshTrigger(ClusterTopologyRefreshOptions.RefreshTrigger.MOVED_REDIRECT, ClusterTopologyRefreshOptions.RefreshTrigger.PERSISTENT_RECONNECTS)
                //开启所有自适应刷新，MOVED，ASK，PERSISTENT都会触发
                .enableAllAdaptiveRefreshTriggers().build();

        LettuceClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
                .poolConfig(genericObjectPoolConfig)
                .clientOptions(ClusterClientOptions.builder().topologyRefreshOptions(topologyRefreshOptions).build())
                .build();
        return new LettuceConnectionFactory(redisStandaloneConfiguration, clientConfig);
    }


    @Primary
    @Bean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(LettuceConnectionFactory lettuceConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(lettuceConnectionFactory);
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringRedisSerializer);
        template.setHashKeySerializer(stringRedisSerializer);
        template.setValueSerializer(stringRedisSerializer);
        template.setHashValueSerializer(stringRedisSerializer);
        template.afterPropertiesSet();
        return template;
    }

    @Bean
    public RedisSerializer<String> redisKeySerializer() {
        return new StringRedisSerializer();
    }

    @Bean
    public RedisSerializer<Object> redisValueSerializer() {
        return new Jackson2JsonRedisSerializer<>(Object.class);
    }

    @Bean
    public LettuceConnectionFactory reconLettuceConnectionFactory() {
        ApolloRedisConfig redisConfig = ReconciliationApolloConfigUtils.getReconRedisConfig();
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(redisConfig.getMaxIdle());
        genericObjectPoolConfig.setMinIdle(redisConfig.getMinIdle());
        genericObjectPoolConfig.setMaxTotal(redisConfig.getMaxActive());
        genericObjectPoolConfig.setMaxWaitMillis(redisConfig.getMaxWait());
        genericObjectPoolConfig.setTimeBetweenEvictionRunsMillis(100);
        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
        redisStandaloneConfiguration.setDatabase(redisConfig.getDatabase());
        redisStandaloneConfiguration.setHostName(redisConfig.getHost());
        redisStandaloneConfiguration.setPort(redisConfig.getPort());
        if (StringUtils.isNotBlank(redisConfig.getPassword())) {
            redisStandaloneConfiguration.setPassword(RedisPassword.of(redisConfig.getPassword()));
        }
        if (StringUtils.isNotBlank(redisConfig.getSecretName())) {
            RedisAuth redisAuth = AwsManagerUtils.getAwsSecretManagerRedisAuth(redisConfig.getEndpoint(), redisConfig.getRegion(), redisConfig.getSecretName());
            redisStandaloneConfiguration.setPassword(redisAuth.getPassword());
        }

        //ClusterTopologyRefreshOptions配置用于开启自适应刷新和定时刷新。
        //如自适应刷新不开启，Redis集群变更时将会导致连接异常！
        ClusterTopologyRefreshOptions topologyRefreshOptions = ClusterTopologyRefreshOptions.builder()
                //开启自适应刷新
                //.enableAdaptiveRefreshTrigger(ClusterTopologyRefreshOptions.RefreshTrigger.MOVED_REDIRECT, ClusterTopologyRefreshOptions.RefreshTrigger.PERSISTENT_RECONNECTS)
                //开启所有自适应刷新，MOVED，ASK，PERSISTENT都会触发
                .enableAllAdaptiveRefreshTriggers().build();

        LettuceClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
                .poolConfig(genericObjectPoolConfig)
                .clientOptions(ClusterClientOptions.builder().topologyRefreshOptions(topologyRefreshOptions).build())
                .build();
        return new LettuceConnectionFactory(redisStandaloneConfiguration, clientConfig);
    }


    @Primary
    @Bean(name = "reconRedisTemplate")
    public RedisTemplate<String, Object> reconRedisTemplate() {
        LettuceConnectionFactory lettuceConnectionFactory = reconLettuceConnectionFactory();
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(lettuceConnectionFactory);
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringRedisSerializer);
        template.setHashKeySerializer(stringRedisSerializer);
        template.setValueSerializer(stringRedisSerializer);
        template.setHashValueSerializer(stringRedisSerializer);
        template.afterPropertiesSet();
        return template;
    }
}
