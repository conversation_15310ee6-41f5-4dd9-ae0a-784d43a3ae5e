package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class IsolatedMarginAccountInfoRes implements IBinanceApiBaseRes{
    /**
     * {
     *    "assets":[
     *       {
     *         "baseAsset":
     *           {
     *           "asset": "BTC",
     *           "borrowEnabled": true,
     *           "borrowed": "0.********",
     *           "free": "0.********",
     *           "interest": "0.********",
     *           "locked": "0.********",
     *           "netAsset": "0.********",
     *           "netAssetOfBtc": "0.********",
     *           "repayEnabled": true,
     *           "totalAsset": "0.********"
     *         },
     *         "quoteAsset":
     *         {
     *           "asset": "USDT",
     *           "borrowEnabled": true,
     *           "borrowed": "0.********",
     *           "free": "0.********",
     *           "interest": "0.********",
     *           "locked": "0.********",
     *           "netAsset": "0.********",
     *           "netAssetOfBtc": "0.********",
     *           "repayEnabled": true,
     *           "totalAsset": "0.********"
     *         },
     *         "symbol": "BTCUSDT"
     *         "isolatedCreated": true,
     *         "enabled": true, // 账户是否启用，true-启用，false-停用
     *         "marginLevel": "0.********",
     *         "marginLevelStatus": "EXCESSIVE", // "EXCESSIVE", "NORMAL", "MARGIN_CALL", "PRE_LIQUIDATION", "FORCE_LIQUIDATION"
     *         "marginRatio": "0.********",
     *         "indexPrice": "10000.********",
     *         "liquidatePrice": "1000.********",
     *         "liquidateRate": "1.********",
     *         "tradeEnabled": true
     *       }
     *     ],
     *     "totalAssetOfBtc": "0.********",
     *     "totalLiabilityOfBtc": "0.********",
     *     "totalNetAssetOfBtc": "0.********"
     * }
     */
    private List<IsolatedMargintAccountInfoInnerRes> assets;
    private BigDecimal totalAssetOfBtc;
    private BigDecimal totalLiabilityOfBtc;
    private BigDecimal totalNetAssetOfBtc;

}
