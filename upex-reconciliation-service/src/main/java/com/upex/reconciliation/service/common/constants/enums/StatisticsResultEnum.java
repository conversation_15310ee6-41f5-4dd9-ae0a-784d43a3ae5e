package com.upex.reconciliation.service.common.constants.enums;

import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;

/**
 * 统计任务结果
 * @Date 2022/10/26 15:50
 * <AUTHOR>
 */
public enum StatisticsResultEnum {

    /**
     * 初始状态
     */
    INIT(0, "初始状态"),
    /**
     * 资产错误：资产统计不平
     */
    ASSET_ERROR(1, "资产错误"),
    /**
     * 资产正确：资产统计正常
     */
    ASSET_CORRECT(2,"资产正确");


    private Integer result;
    private String desc;

    StatisticsResultEnum(Integer code, String desc) {
        result = code;
        this.desc = desc;
    }

    public Integer getResult() {
        return result;
    }

    public String getDesc() {
        return desc;
    }

    public static StatisticsResultEnum toEnum(Integer code) {
        for (StatisticsResultEnum item : StatisticsResultEnum.values()) {
            if (item.result.equals(code)) {
                return item;
            }
        }
        StringBuilder str = new StringBuilder();
        str.append("the argument of ").append(code).append(" have no correspondence StatisticsStateEnum enum!");
        throw new ApiException(BillExceptionEnum.PARAMS_VALIDATE_ERROR);
    }

    public static StatisticsResultEnum toEnum(Boolean result) {
        return result ? ASSET_CORRECT : ASSET_ERROR;
    }
}
