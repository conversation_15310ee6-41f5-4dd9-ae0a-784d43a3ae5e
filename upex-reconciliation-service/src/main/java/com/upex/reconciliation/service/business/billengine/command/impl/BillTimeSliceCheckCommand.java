package com.upex.reconciliation.service.business.billengine.command.impl;


import com.upex.mixcontract.common.framework.AttributeMap;
import com.upex.mixcontract.common.framework.command.ICommandEnum;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.billengine.command.AbstractBillCommand;
import com.upex.reconciliation.service.common.constants.ReconciliationCommandEnum;
import com.upex.reconciliation.service.model.dto.BillUserCheckResult;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

@Slf4j
public class BillTimeSliceCheckCommand extends AbstractBillCommand<CommonBillChangeData, BillUserCheckResult> implements ApplicationContextAware {

    public BillTimeSliceCheckCommand(BillLogicGroup logicGroup) {
        super(logicGroup);
    }

    @Override
    public void init(AttributeMap initContext) {

    }


    // 个人唯独的初始化逻辑，需要查询业务系统，获取到用户的资产信息
    @Override
    public BillUserCheckResult execute(CommonBillChangeData request) {
//        BaseRequest baseRequest = new BaseRequest();
//        baseRequest.setBeginTime(request.getCreateTime().getTime());
//        baseRequest.setEndTime(request.getCreateTime().getTime());
//        ReconciliationSpringContext reconciliationSpringContext = logicGroup.getEngine().getContext();
//
//        List<Long> accountIds = new ArrayList<>();
//        accountIds.add(request.getAccountId());
//        List<AccountAssetsInfoResult> list = reconciliationSpringContext.getBillAccountAssetsService().queryUserAssets(accountIds,baseRequest);
//        request.setAccountAssets(list);
//        return BillUserCheckResult.builder().build();
        return null;
    }

    @Override
    public ICommandEnum getType() {
        return ReconciliationCommandEnum.BILL_CHANGE;
    }

    @Override
    public Class<CommonBillChangeData> getParamClass() {
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    }

}
