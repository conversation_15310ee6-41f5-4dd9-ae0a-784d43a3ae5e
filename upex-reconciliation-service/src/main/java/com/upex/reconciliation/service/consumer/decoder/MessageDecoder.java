package com.upex.reconciliation.service.consumer.decoder;

import com.alibaba.otter.canal.protocol.FlatMessage;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;

import java.util.List;
import java.util.Map;

public interface MessageDecoder {
    List<CommonBillChangeData> messageDecode(AlarmNotifyService alarmNotifyService, List<Map<String, String>> dataList, FlatMessage flatMessage, Integer partition, Long offset, Byte accountType);
}
