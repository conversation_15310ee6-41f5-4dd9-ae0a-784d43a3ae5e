package com.upex.reconciliation.service.utils;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 资金对账工具类
 * <AUTHOR>
 * @date 2023/6/7 17:11
 */
public class BillCapitalAmountUtils {

    /**
     * 资金对账差异
     *
     * @param diffAmount        差异金额
     * @param diffUExpression   差异折u的阈值表达式
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2023/4/23 17:50
     */
    public static Boolean billAssetDiff(BigDecimal diffAmount, String diffUExpression) {
        Map<String, BigDecimal> evaluationMap = new HashMap<>(1);
        if (StringUtils.isBlank(diffUExpression)) {
            return false;
        }
        // 获取币种对应的阈值表达式，为空则走通用阈值表达式
        // 币种的阈值表达式设置币种对应的折u金额
        evaluationMap.put("billDiffValue", diffAmount);
        return SpelExpressionUtils.getValue(evaluationMap, diffUExpression, Boolean.class);
    }

}
