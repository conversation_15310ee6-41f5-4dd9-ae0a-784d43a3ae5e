package com.upex.reconciliation.service.service;


import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.common.constants.enums.AssetsCheckTypeEnum;
import com.upex.reconciliation.service.dao.entity.AssetsBillCoinProperty;
import com.upex.reconciliation.service.dao.mapper.AssetsBillCoinPropertyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AssetsBillCoinPropertyService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource
    private AssetsBillCoinPropertyMapper assetsBillCoinPropertyMapper;

    public List<AssetsBillCoinProperty> selectAssetsByEndTime(Date endTime,
                                                              String billCheckType,
                                                              String billCheckParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinPropertyMapper.selectAssetsByEndTime(endTime, billCheckType, billCheckParam));
    }

    public Boolean deleteByCheckTime(String billCheckType, String billCheckParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinPropertyMapper.deleteByCheckTime(billCheckType, billCheckParam, checkTime, batchSize));
    }

    public int batchUpdate(List<AssetsBillCoinProperty> list,
                           String billCheckType,
                           String billCheckParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinPropertyMapper.batchUpdate(list, billCheckType, billCheckParam));
    }

    public int batchInsert(List<AssetsBillCoinProperty> list,
                           String billCheckType,
                           String billCheckParam) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinPropertyMapper.batchInsert(list, billCheckType, billCheckParam));
    }

    public Long getIdByCheckTime(String accountType, String accountParam, Date checkTime, String operation) {
        String operationNew = operation;
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinPropertyMapper.getIdByCheckTime(accountType, accountParam, checkTime, operationNew));
    }

    public boolean deleteByMaxId(String accountType, String accountParam, Long maxId, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinPropertyMapper.deleteByMaxId(accountType, accountParam, maxId, batchSize));
    }

    public Map<Integer, BigDecimal> getCoinSnapshotAssets(String assetsType, Date snapshotTime, List<Integer> coinIdsList) {
        Map<Integer, BigDecimal> assetsMap = new HashMap<>();
        AssetsCheckTypeEnum assetsCheckTypeEnum = AssetsCheckTypeEnum.toEnum(assetsType);
        List<AssetsBillCoinProperty> assetsBillCoinPropertyList = dbHelper.doDbOpInReconMaster(() -> assetsBillCoinPropertyMapper.getCoinSnapshotAssets(assetsCheckTypeEnum.getCode(), assetsCheckTypeEnum.getAssetsParam(), snapshotTime, coinIdsList));
        assetsBillCoinPropertyList.forEach(assetsBillCoinProperty -> {
            BigDecimal coinAssets = assetsMap.getOrDefault(assetsBillCoinProperty.getCoinId(), BigDecimal.ZERO);
            assetsMap.put(assetsBillCoinProperty.getCoinId(), coinAssets.add(assetsBillCoinProperty.getPropSum()));
        });
        return assetsMap;
    }

    public Boolean deleteByLtCheckTime(String billCheckType, String billCheckParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> assetsBillCoinPropertyMapper.deleteByLtCheckTime(billCheckType, billCheckParam, checkTime, batchSize));
    }
}
