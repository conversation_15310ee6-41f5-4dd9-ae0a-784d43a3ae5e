//package com.upex.reconciliation.service.business.convert.remote;
//
//import com.upex.spot.dto.params.bill.QuerySpotBillVO;
//import com.upex.spot.dto.result.bill.NewSpotBillInfoResult;
//import com.upex.spot.facade.query.SpotBillsQueryClientCallBackFactory;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//
//import java.util.List;
//
//
//@FeignClient(
//        value = "${upex.feignClient.spot.query}",
//        contextId = "spotBillsQueryClient",
//        path = "/inner/v1/spot",
//        fallbackFactory = SpotBillsQueryClientCallBackFactory.class
//)
//public interface SpotBillFeignService  {
//
//    @PostMapping({"/getBillByUserIdAndBizOrderId"})
//    List<NewSpotBillInfoResult> getBillByUserIdAndBizOrderId(@RequestBody QuerySpotBillVO querySpotBillVO);
//
//    @PostMapping({"/getBillListByUserIdAndBizTypeAndOrderId"})
//    List<NewSpotBillInfoResult> getBillListByUserIdAndBizTypeAndOrderId(@RequestBody List<QuerySpotBillVO> paramList);
//
//
//}
