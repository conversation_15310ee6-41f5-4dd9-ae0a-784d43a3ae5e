package com.upex.reconciliation.service.service.impl;

import com.upex.config.account.SystemAccountQueryByTypeListReq;
import com.upex.config.account.SystemAccountQueryReq;
import com.upex.config.account.enums.SystemAccountTypeEnum;
import com.upex.config.facade.account.SystemAccountService;
import com.upex.mixcontract.common.utils.MixContractConfigUtils;
import com.upex.mixcontract.common.utils.config.BusinessSymbol;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.common.constants.enums.ProfitTypeEnum;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.AssetsCheckConfig;
import com.upex.reconciliation.service.service.ReconSystemAccountService;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/9/25 15:02
 */
@Service
@Slf4j
public class ReconSystemAccountServiceImpl implements ReconSystemAccountService {
    @Resource
    private SystemAccountService systemAccountService;
    @Resource
    private AlarmNotifyService alarmNotifyService;

    @Override
    public Long querySystemFeeUserId(AccountTypeEnum accountTypeEnum, ProfitTypeEnum profitTypeEnum) {
        SystemAccountTypeEnum systemAccountTypeEnum = null;
        String thirdType = null;
        ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        Long apolloUserId = apolloBizConfig.getSystemFeeUserId();
        if (accountTypeEnum.isSpot()) {
            // "systemFeeUserId": *********,
            systemAccountTypeEnum = SystemAccountTypeEnum.SPOT_RECON_FEE;
        } else if (accountTypeEnum.isLever()) {
            // "systemFeeUserId": **********,
            systemAccountTypeEnum = SystemAccountTypeEnum.MARGIN_FEE;
        } else if (accountTypeEnum.isContract()) {
            // "systemFeeUserId": *********,
            systemAccountTypeEnum = SystemAccountTypeEnum.CONTRACT_FEE;
        } else if (accountTypeEnum.isUta()) {
            systemAccountTypeEnum = SystemAccountTypeEnum.UTA_FEE;
            thirdType = profitTypeEnum == ProfitTypeEnum.UTA_CONTRACT_DEAL_FEE ? "contract" :
                    profitTypeEnum == ProfitTypeEnum.UTA_SPOT_DEAL_FEE ? "spot" : null;
            apolloUserId = profitTypeEnum == ProfitTypeEnum.UTA_CONTRACT_DEAL_FEE ? apolloBizConfig.getSystemUtaContractFeeUserId() :
                    profitTypeEnum == ProfitTypeEnum.UTA_SPOT_DEAL_FEE ? apolloBizConfig.getSystemFeeUserId() : null;
        }
        return checkAndGetAccountId(systemAccountTypeEnum, thirdType, apolloUserId, true);
    }

    @Override
    public Long queryExchangeUserId(AccountTypeEnum accountTypeEnum) {
        // "exchangeUserId": **********,
        ApolloReconciliationBizConfig apolloReconciliationBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        Long apolloUserId = apolloReconciliationBizConfig.getExchangeUserId();
        return checkAndGetAccountId(SystemAccountTypeEnum.RECONCILIATION_BILL_EXCHANGE, null, apolloUserId, true);
    }

    @Override
    public Long queryInterestFeeUserId(AccountTypeEnum accountTypeEnum) {
        SystemAccountTypeEnum systemAccountTypeEnum = null;
        switch (accountTypeEnum) {
            case S_USDT_MIX_CONTRACT_BL:
            case USDT_MIX_CONTRACT_BL:
            case P_USDT_MIX_CONTRACT_BL:
                // "contractInterestFeeTransferToUId": **********,
                systemAccountTypeEnum = SystemAccountTypeEnum.CONTRACT_INTEREST;
                break;
            default:
                break;
        }
        ApolloReconciliationBizConfig apolloReconciliationBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        Long apolloUserId = apolloReconciliationBizConfig.getContractInterestFeeTransferToUId();
        return checkAndGetAccountId(systemAccountTypeEnum, null, apolloUserId, true);
    }

    @Override
    public Long queryHedgeAndWearIncomeUserId(AccountTypeEnum accountTypeEnum, ProfitTypeEnum profitTypeEnum, AssetsCheckConfig assetsCheckConfig) {
        SystemAccountTypeEnum systemAccountTypeEnum = null;
        Long apolloUserId = null;
        if (profitTypeEnum == ProfitTypeEnum.COIN_PROFIT) {
            systemAccountTypeEnum = SystemAccountTypeEnum.RECONCILIATION_HEDGE_EXCHANGE;
            apolloUserId = assetsCheckConfig.getContractExchangeTransferToUId();
        } else if (profitTypeEnum == ProfitTypeEnum.SYMBOL_PROFIT) {
            systemAccountTypeEnum = SystemAccountTypeEnum.RECONCILIATION_WEAR_INCOME;
            apolloUserId = assetsCheckConfig.getContractProfitTransferToUId();
        }
        return checkAndGetAccountId(systemAccountTypeEnum, null, apolloUserId, true);
    }

    @Override
    public Long queryContractAdlReceivedUserId(AccountTypeEnum accountTypeEnum, String symbolId) {
        String thirdType = symbolId;
        Long riskCapitalAccount = null;
        BusinessSymbol businessSymbol = MixContractConfigUtils.getBusinessSymbol(symbolId);
        if (businessSymbol != null) {
            riskCapitalAccount = businessSymbol.getRiskCapitalAccount();
        }
        if (riskCapitalAccount == null) {
            thirdType = "common";
            riskCapitalAccount = MixContractConfigUtils.getAppSystemAccountConfig().getRiskCapitalAccount();
        }
        return checkAndGetAccountId(SystemAccountTypeEnum.CONTRACT_RISK_CAPITAL, thirdType, riskCapitalAccount, false);
    }


    @Override
    public Boolean isContractAdlReceivedUserId(AccountTypeEnum accountTypeEnum, String symbolId, Long bizUserId) {
        if (!accountTypeEnum.isContract() || accountTypeEnum.isSContract() || StringUtils.isEmpty(symbolId) || !AccountTypeEnum.isContractBySymbol(symbolId) || bizUserId == null) {
            return false;
        }
        ApolloReconciliationBizConfig apolloReconciliationBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        if (apolloReconciliationBizConfig.getNotRecalculatePositionProfitSymbolSet().contains(symbolId)) {
            return false;
        }
        Long apolloUserId = queryContractAdlReceivedUserId(accountTypeEnum, symbolId);
        if (apolloUserId == null) {
            return false;
        }
        return bizUserId.equals(apolloUserId);
    }

    @Override
    public Set<Long> getSystemAccountIds(String systemAccountType) {
        Optional<SystemAccountTypeEnum> optional = Arrays.stream(SystemAccountTypeEnum.values())
                .filter(systemAccountTypeEnum -> Objects.equals(systemAccountType, systemAccountTypeEnum.name())).findFirst();
        if (optional.isEmpty()) {
            return Set.of();
        }
        SystemAccountQueryByTypeListReq req = SystemAccountQueryByTypeListReq.builder().systemAccountTypeEnum(optional.get()).forceUseConfigFlag(true).build();
        return systemAccountService.getAndCheckSysAccountByTypeList(req);
    }

    /**
     * 对比apollo和第三方账户，如果不一致，则报警并返回apollo的账户
     *
     * @param apolloUserId
     * @param systemAccountTypeEnum
     * @return
     */
    private Long checkAndGetAccountId(SystemAccountTypeEnum systemAccountTypeEnum, String thirdType, Long apolloUserId, boolean isThrowException) {
        if (Objects.isNull(systemAccountTypeEnum)) {
            return null;
        }
        Long thirdUserId = getAccountIdFromConfig(systemAccountTypeEnum, apolloUserId, thirdType);
        if (thirdUserId != null && apolloUserId != null && apolloUserId.equals(thirdUserId)) {
            return apolloUserId;
        } else if (thirdUserId != null && apolloUserId == null) {
            return thirdUserId;
        } else if (apolloUserId != null) {
            return apolloUserId;
        } else if (!isThrowException) {
            return null;
        } else {
            throw new RuntimeException("can not find system account for " + systemAccountTypeEnum.name());
        }
    }

    /**
     * 调用三方接口
     *
     * @param systemAccountTypeEnum
     * @param userId
     * @param thirdType
     * @return
     */
    private Long getAccountIdFromConfig(SystemAccountTypeEnum systemAccountTypeEnum, Long userId, String thirdType) {
        if (Objects.isNull(systemAccountTypeEnum)) {
            return null;
        }
//        SystemAccountQueryByTypeListReq systemAccountQueryByTypeListReq = new SystemAccountQueryByTypeListReq();
//        systemAccountQueryByTypeListReq.setSystemAccountTypeEnum(systemAccountTypeEnum);
//        systemAccountQueryByTypeListReq.setUserIdList(userIdList);
//        Set<Long> userIdSet = systemAccountService.getAndCheckSysAccountByTypeList(systemAccountQueryByTypeListReq);
//        return CollectionUtils.isEmpty(userIdSet) ? null : userIdSet.iterator().next();
        return systemAccountService.getAndCheckSysAccount(SystemAccountQueryReq.builder()
                .systemAccountTypeEnum(systemAccountTypeEnum)
                .thirdType(thirdType)
                .userId(userId)
                .build());

    }
}
