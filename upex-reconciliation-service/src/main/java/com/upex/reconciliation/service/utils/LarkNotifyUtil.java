package com.upex.reconciliation.service.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.upex.commons.support.util.HttpProxyUtil;
import com.upex.reconciliation.facade.enums.BillExceptionEnum;
import com.upex.reconciliation.service.dao.entity.AlarmNotifyTemplate;
import com.upex.reconciliation.service.model.alarm.AlarmNotifyModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * lark通知
 *
 * <AUTHOR>
 */
@Slf4j
public class LarkNotifyUtil {
    /***lark通知最大内容长度***/
    private static final int LARK_MAX_CONTENT_LENGTH = 3072;

    public static void sendLarkMessage(AlarmNotifyModel alarmNotifyModel) {
        if (!checkAlarmNotifyParam(alarmNotifyModel)) {
            return;
        }
        String content = alarmNotifyModel.getContent();
        int sendLarkRetryCount = alarmNotifyModel.getSendLarkRetryCount().intValue();
        while (sendLarkRetryCount >= 0) {
            try {
                sendLarkMessage(alarmNotifyModel.getSendLarkApi(), content, alarmNotifyModel.getSendLarkSecret());
                break;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                try {
                    Thread.sleep(alarmNotifyModel.getSendLarkRetryInterval());
                } catch (InterruptedException ex) {
                    log.error(ex.getMessage(), ex);
                }
                if (sendLarkRetryCount <= 0) {
                    log.error("lark send message error", e);
                }
            } finally {
                sendLarkRetryCount -= 1;
            }
        }
    }

    private static void sendLarkMessage(String sendLarkApi, String text, String larkSecret) throws Exception {
        if (StringUtils.isBlank(text)) {
            log.info("send Lark Message text is null,do not send alarm notify!!!");
            return;
        }
        text = text.length() > LARK_MAX_CONTENT_LENGTH ? text.substring(0, LARK_MAX_CONTENT_LENGTH) : text;
        long time = ZonedDateTime.now().toInstant().toEpochMilli();
        String hmac = larkSign(larkSecret, time);
        Map<String, String> param = new HashMap<>(4);

        param.putIfAbsent("timestamp", time + "");
        param.putIfAbsent("sign", hmac);
        param.putIfAbsent("msg_type", "text");

        JSONObject content = new JSONObject();
        content.putIfAbsent("text", text);
        param.putIfAbsent("content", content.toString());

        Map<String, String> header = new HashMap<>(1);
        header.put("application", "json");
        String result = HttpProxyUtil.httpPostInternalProxy(sendLarkApi, header, param);
        log.info("sendLarkMessage【result】:{}", result);
        if (StringUtils.isNotBlank(result)) {
            JSONObject jsonObject = JSON.parseObject(result);
            Integer code = jsonObject.getInteger("code");
            if (code != null && BillExceptionEnum.SEND_LARK_SUCCESS_CODE.getInteger().intValue() != code) {
                //AlarmUtils.error("send lark message error:{}", result);
            }
        }
    }

    private static String larkSign(String secret, long timestamp) throws NoSuchAlgorithmException, InvalidKeyException {
        //把timestamp+"\n"+密钥当做签名字符串
        String stringToSign = timestamp + "\n" + secret;

        //使用HmacSHA256算法计算签名
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(new byte[]{});
        return new String(Base64.encodeBase64(signData));
    }

    /**
     * 校验参数
     *
     * @param alarmNotifyModel
     * @return
     */
    private static boolean checkAlarmNotifyParam(AlarmNotifyModel alarmNotifyModel) {
        if (StringUtils.isBlank(alarmNotifyModel.getSendLarkApi())) {
            log.error("param sendLarkApi is empty");
            return false;
        }
        return true;
    }

    /**
     * 获取内容
     *
     * @param alarmNotifyModel
     * @return
     */
    public static String getContent(AlarmNotifyModel alarmNotifyModel, AlarmNotifyTemplate alarmNotifyTemplate) {
        if (alarmNotifyTemplate == null || StringUtils.isBlank(alarmNotifyTemplate.getTemplateContent())) {
            //AlarmUtils.error("send lark message template code: {} ,template content is null", alarmNotifyModel.getTemplateCode());
            return "";
        }
        List atUserIdList = ListUtils.emptyIfNull(alarmNotifyModel.getAtUserList()).stream().map(LarkNotifyUtil::getAtUserId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, Object> root = null;//alarmNotifyModel.getRoot();
        root.put("atUserIdList", atUserIdList);
        String content = FreeMarkerUtils.processToString(alarmNotifyTemplate.getTemplateCode(), alarmNotifyTemplate.getTemplateContent(), root);
        log.info("send lark message content:{}", content);
        return StringUtils.defaultString(content, "");
    }

    /**
     * 获取被通人用户id
     * yuan.xiongwei|ou_15fd11a6536ccce39a07942b52042f6f => ou_15fd11a6536ccce39a07942b52042f6f
     *
     * @param atUser
     * @return
     */
    public static String getAtUserId(String atUser) {
        if (StringUtils.isBlank(atUser) || !atUser.contains("|")) {
            return null;
        }
        return atUser.split("\\|")[1];
    }
}
