package com.upex.reconciliation.service.utils;

import com.upex.reconciliation.service.common.constants.BillConstants;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 数字工具类
 *
 * <AUTHOR>
 * @date 2022/11/23 21:31
 */
public class NumberUtil {
    /**
     * 获取数字为2的几次幂
     *
     * @param number 数字
     * @return {@link int }
     * <AUTHOR>
     * @date 2022/11/23 21:30
     */
    public static int isTimesTwo(int number) {
        if (number < 0) {
            throw new IllegalArgumentException("The Number Cannot Be Less Than Zero!!!");
        }
        if (!(number > 0 && (number & (number - 1)) == 0)) {
            return Integer.toBinaryString(number).length();
        }
        return Integer.toBinaryString(number).length() - 1;
    }

    /**
     * BigDecimal类型为空处理
     *
     * @param number
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2022/12/12 18:31
     */
    public static BigDecimal isNullDefaultZero(BigDecimal number) {
        return isNullDefaultValue(number, BigDecimal.ZERO);
    }


    /**
     * 除法，兼容分母为0时，返回除法结果1
     * @param numerator
     * @param denominator
     * @param scale
     * @param roundingMode
     * @return
     */
    public static String divideAndStripZeroDefaultOneStr(BigDecimal numerator, BigDecimal denominator, Integer scale, RoundingMode roundingMode) {
        return denominator.compareTo(BigDecimal.ZERO) == 0 ? BillConstants.ONE_STRING :
                numerator.divide(denominator,
                        scale, roundingMode).abs().stripTrailingZeros().toPlainString();
    }

    /**
     * 除法，兼容分母为0时，返回除法结果1
     * @param numerator
     * @param denominator
     * @param scale
     * @param roundingMode
     * @return
     */
    public static BigDecimal divideAndStripZeroDefaultOne(BigDecimal numerator, BigDecimal denominator, Integer scale, RoundingMode roundingMode) {
        return denominator.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ONE :
                numerator.divide(denominator,
                        scale, roundingMode).abs();
    }

    /**
     * Integer类型为空处理
     * @param number
     * @return {@link java.lang.Integer }
     * <AUTHOR>
     * @date 2022/12/12 18:31
     */
    public static Integer isNullDefaultZero(Integer number){
        return isNullDefaultValue(number, 0);
    }


    /**
     * Integer类型为空，设置默认值
     * @param number
     * @param defaultValue
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2023/3/3 19:18
     */
    public static Integer isNullDefaultValue(Integer number,Integer defaultValue){
        return number == null ? defaultValue : number;
    }

    /**
     * BigDecimal类型为空，设置默认值
     *
     * @param number
     * @param defaultValue
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2023/3/3 19:18
     */
    public static BigDecimal isNullDefaultValue(BigDecimal number, BigDecimal defaultValue) {
        return number == null ? defaultValue : number;
    }

    public static String toPlainString(BigDecimal number) {
        return number == null ? null : number.toPlainString();
    }


    /**
     * 是否为空或者为数字0
     *
     * @param value 数值
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2023/3/25 01:21
     */
    public static <T extends Number & Comparable> boolean isEmptyOrZero(T value) {
        if (value == null) {
            return true;
        }
        Number zero = org.springframework.util.NumberUtils.parseNumber("0", value.getClass());
        return value.compareTo(zero) == 0;
    }

    /**
     * 是否不为空并且不为数字0
     *
     * @param value 数值
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2023/3/25 01:21
     */
    public static <T extends Number & Comparable> boolean isNotEmptyOrZero(T value) {
        return !isEmptyOrZero(value);
    }

    /**
     * 多个数字累加
     *
     * @param a
     * @param b
     * @return
     */
    public static BigDecimal add(BigDecimal a, BigDecimal... b) {
        a = isNullDefaultZero(a);
        for (int i = 0; i < b.length; i++) {
            a = isNullDefaultZero(b[i]).add(a);
        }
        return a;
    }

    /**
     * 多个数字相减
     *
     * @param a
     * @param b
     * @return
     */
    public static BigDecimal subtract(BigDecimal a, BigDecimal... b) {
        a = isNullDefaultZero(a);
        for (int i = 0; i < b.length; i++) {
            a = a.subtract(isNullDefaultZero(b[i]));
        }
        return a;
    }

    /**
     * 多个数字相乘
     *
     * @param a
     * @param b
     * @return
     */
    public static BigDecimal multiply(BigDecimal a, BigDecimal... b) {
        a = isNullDefaultZero(a);
        for (int i = 0; i < b.length; i++) {
            a = a.multiply(isNullDefaultZero(b[i]));
        }
        return a;
    }

    /**
     * 判断两个BigDecimal类型的值是否相等
     *
     * @param a
     * @param b
     * @return
     */
    public static boolean equal(BigDecimal a, BigDecimal b) {
        if (Objects.isNull(a) && Objects.isNull(b)) {
            return true;
        }
        if (Objects.isNull(a) || Objects.isNull(b)) {
            return false;
        }
        return a.compareTo(b) == 0;
    }
}
