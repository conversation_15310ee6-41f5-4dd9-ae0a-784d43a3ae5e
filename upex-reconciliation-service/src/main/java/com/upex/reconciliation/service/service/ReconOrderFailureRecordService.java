package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.ReconOrderFailureRecord;
import com.upex.reconciliation.service.dao.mapper.ReconOrderFailureRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 对账失败记录服务
 */
@Slf4j
@Service
public class ReconOrderFailureRecordService {

    @Resource
    private BillDbHelper dbHelper;

    @Resource
    private ReconOrderFailureRecordMapper reconOrderFailureRecordMapper;

    /**
     * 记录对账失败
     *
     * @param reconOrderFailureRecord 对账失败记录实体
     * @return 是否成功
     */
    public boolean recordFailure(ReconOrderFailureRecord reconOrderFailureRecord) {

        return dbHelper.doDbOpInReconMaster(() -> reconOrderFailureRecordMapper.insert(reconOrderFailureRecord)) > 0;

    }

    /**
     * 批量记录对账失败
     *
     * @param records 失败记录列表
     * @return 成功记录数量
     */
    public int batchRecordFailure(List<ReconOrderFailureRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }

        try {
            // 设置默认值
            Date now = new Date();
            for (ReconOrderFailureRecord record : records) {
                if (record.getStatus() == null) {
                    record.setStatus((byte) 0); // 未处理
                }
                if (record.getCreateTime() == null) {
                    record.setCreateTime(now);
                }
                if (record.getUpdateTime() == null) {
                    record.setUpdateTime(now);
                }
            }

            int result = dbHelper.doDbOpInReconMaster(() -> reconOrderFailureRecordMapper.batchInsert(records));
            log.info("Batch recorded {} reconciliation failures out of {}", result, records.size());
            return result;
        } catch (Exception e) {
            log.error("Error batch recording reconciliation failures", e);
            return 0;
        }
    }

    /**
     * 更新失败记录状态
     *
     * @param id 记录ID
     * @param status 状态
     * @param processUser 处理人
     * @param processRemark 处理备注
     * @return 是否成功
     */
    public boolean updateStatus(Long id, Byte status, String processUser, String processRemark) {
        try {
            int result = dbHelper.doDbOpInReconMaster(() -> 
                    reconOrderFailureRecordMapper.updateStatus(id, status, processUser, processRemark));
            if (result > 0) {
                log.info("Successfully updated failure record status: id={}, status={}, user={}", 
                        id, status, processUser);
                return true;
            } else {
                log.warn("Failed to update failure record status: id={}, status={}, user={}", 
                        id, status, processUser);
                return false;
            }
        } catch (Exception e) {
            log.error("Error updating failure record status: id={}", id, e);
            return false;
        }
    }

    /**
     * 更新失败记录的重试次数
     * 注意: 需要在ReconOrderFailureRecord实体和Mapper中添加retryCount字段
     *
     * @param id 记录ID
     * @param retryCount 重试次数
     * @return 是否成功
     */
    public boolean updateRetryCount(Long id, int retryCount) {
        try {
            int result = dbHelper.doDbOpInReconMaster(() ->
                    reconOrderFailureRecordMapper.updateRetryCount(id, retryCount));
            if (result > 0) {
                log.info("Successfully updated failure record retry count: id={}, retryCount={}",
                        id, retryCount);
                return true;
            } else {
                log.warn("Failed to update failure record retry count: id={}, retryCount={}",
                        id, retryCount);
                return false;
            }
        } catch (Exception e) {
            log.error("Error updating failure record retry count: id={}", id, e);
            return false;
        }
    }

    /**
     * 根据订单ID查询失败记录
     *
     * @param orderId 订单ID
     * @return 失败记录
     */
    public ReconOrderFailureRecord getByOrderId(Long orderId) {
        try {
            return dbHelper.doDbOpInReconMaster(() -> reconOrderFailureRecordMapper.selectByOrderId(orderId));
        } catch (Exception e) {
            log.error("Error getting failure records by orderId: {}", orderId, e);
            return null;
        }
    }

    /**
     * 查询未处理的失败记录
     *
     * @param orderType 业务类型
     * @param failureType 失败类型
     * @return 失败记录列表
     */
    public List<ReconOrderFailureRecord> getUnprocessedRecords(Byte orderType, Byte failureType) {
        try {
            return dbHelper.doDbOpInReconMaster(() ->
                    reconOrderFailureRecordMapper.selectUnprocessed(orderType, failureType));
        } catch (Exception e) {
            log.error("Error getting unprocessed failure records", e);
            return null;
        }
    }
}
