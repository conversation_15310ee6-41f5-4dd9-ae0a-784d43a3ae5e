package com.upex.reconciliation.service.service.client.cex.utils;

import com.upex.commons.support.hmac.HmacEncryptUtil;
import com.upex.reconciliation.service.service.client.cex.CexConstants;
import com.upex.reconciliation.service.utils.EnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
@Slf4j
public class HmacUtil {

    public static String encrypt(String data) {
        try {
            if (StringUtils.isEmpty(data)) {
                return null;
            }
            //测试环境
            String keyTag="commonEncryptKey";
            if (EnvUtil.isLocalEnv()) {
                return encrypt(data, "commonEncryptKey", "abcdefghijklmnop");
            } else {
                if(EnvUtil.isOnline()||EnvUtil.isPre()){
                    //线上环境
                    keyTag="reconciliation-group-data";
                }
                return HmacEncryptUtil.encryptFieldContent(data, keyTag);
            }
        }catch (Exception e){
//            log.error("HmacUtilencrypt error:{}",e);
            return data;
        }
    }

    public static String decrypt(String data) {
        try {
            if (StringUtils.isEmpty(data)) {
                return data;
            }
            if (!data.startsWith(CexConstants.HMAC_pre)) {
                return data;
            }
            //测试环境
            String keyTag="commonEncryptKey";
            if (EnvUtil.isLocalEnv()) {
                return decrypt(data, "commonEncryptKey", "abcdefghijklmnop");
            } else {
                if(EnvUtil.isOnline()||EnvUtil.isPre()){
                    //线上环境
                    keyTag="reconciliation-group-data";
                }
                return HmacEncryptUtil.decryptFieldElseOriginalContent(data, keyTag);
            }
        }catch (Exception e){
//            log.error("HmacUtildecryptFieldElseOriginalContent error",e);
            return data;
        }
    }


    private static final String CIPHER_TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private static final String AES = "AES";

    /**
     * AES 加密
     *
     * @param data      明文
     * @param secretKey 密钥（16/24/32 字节）
     * @param iv        初始化向量（与 BlockSize 一致，通常为 16 字节）
     * @return Base64 编码的密文
     */
    public static String encrypt(String data, String secretKey, String iv) {
        try {
            Cipher cipher = Cipher.getInstance(CIPHER_TRANSFORMATION);
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), AES);
            IvParameterSpec ivSpec = new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8));

            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("AES 加密失败", e);
        }
    }

    /**
     * AES 解密
     *
     * @param cipherText 密文（Base64 编码）
     * @param secretKey  密钥
     * @param iv         初始化向量
     * @return 明文
     */
    public static String decrypt(String cipherText, String secretKey, String iv) {
        try {
            Cipher cipher = Cipher.getInstance(CIPHER_TRANSFORMATION);
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), AES);
            IvParameterSpec ivSpec = new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8));

            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] decoded = Base64.getDecoder().decode(cipherText);
            byte[] decrypted = cipher.doFinal(decoded);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("AES 解密失败", e);
        }
    }

    public static void main(String[] args) {
        System.out.println(encrypt("1277", "commonEncryptKey", "abcdefghijklmnop"));
//        System.out.println(decrypt("123456"));
        System.out.println("hmac_CwgCEiBDREM5MEY3Qjc1MzQwQUNGOUVFNkI5REI1RjI1QkFEQxoQY29tbW9uRW5jcnlwdEtleQwSLNcp9DbC+Kox7tiBXJUIFPDgbEOxcesUVbyjHi1cl4+2kYBKgdeFB1epd0Zq".length());
        System.out.println("hmac_CwgCEiBDREM5MEY3Qjc1MzQwQUNGOUVFNkI5REI1RjI1QkFEQxoQY29tbW9uRW5jcnlwdEtleQwSlgGzH0ocVlhbebT6pM5VYuKVhisiQJPtxaZOvUU2edEFVLRxMxnTfmoZsumxM/xp1nAhTvs87X5cAOPRp8b7rY4HGk9etr7RdbwqIcbfi18RbuezCfRytYu1TVdqhFzf/xyuTPWyt39IKyjCDDuUiffpfGshCFSGGNG8aaTInHg+u1KOT9vsde48vvJXUET+mWNdC/sWrSQ=".length());
    }
}

