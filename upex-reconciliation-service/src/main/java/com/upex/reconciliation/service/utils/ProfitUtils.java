package com.upex.reconciliation.service.utils;

import com.alibaba.fastjson.JSON;
import com.upex.bill.dto.results.AccountAssetsInfoResult ;
import com.upex.mixcontract.process.facade.dto.MixAccountAssetsExtension;
import com.upex.reconciliation.service.dao.entity.BillConfig;
import com.upex.reconciliation.service.dao.entity.BillCoinUserAssets;
import com.upex.reconciliation.service.model.dto.UserLCountSCountDetail;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ProfitUtils {

    /**
     * 未实现盈亏计算
     *
     * @param billConfig
     * @param list
     * @param userLCountSCountDetailList
     * @return
     */
    public static List<AccountAssetsInfoResult> dealToAccountAssetInfo(BillConfig billConfig, List<BillCoinUserAssets> list, List<UserLCountSCountDetail> userLCountSCountDetailList) {
        List<AccountAssetsInfoResult> infoResults = new ArrayList<>();

        list.forEach(pro -> {
            List<MixAccountAssetsExtension> params = JSON.parseArray(pro.getParams(), MixAccountAssetsExtension.class);
            //未实现数据不为0的数据
            List<MixAccountAssetsExtension> collect = params.stream().filter(param -> param.getUnR().compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            collect.forEach(info -> {
                AccountAssetsInfoResult build = AccountAssetsInfoResult.builder()
                        .symbolId(info.getSId())
                        .prop4(info.getUnR())
                        .userId(pro.getUserId())
                        .coinId(pro.getCoinId())
                        .build();
                infoResults.add(build);

                UserLCountSCountDetail userLCountSCountDetail = UserLCountSCountDetail.builder()
                        .userId(pro.getUserId())
                        .symbolId(info.getSId())
                        .sCount(info.getSCount())
                        .lCount(info.getLCount())
                        .build();
                userLCountSCountDetailList.add(userLCountSCountDetail);
            });
        });
        return infoResults;
    }

}
