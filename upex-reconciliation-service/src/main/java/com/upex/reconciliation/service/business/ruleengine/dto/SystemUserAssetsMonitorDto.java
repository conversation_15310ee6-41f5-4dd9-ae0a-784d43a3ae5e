package com.upex.reconciliation.service.business.ruleengine.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SystemUserAssetsMonitorDto {
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 开始时间
     */
    private String  startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 账户类型
     */
    private String accountTypeName;
    /**
     * 期初资产
     */
    private BigDecimal beginAmount;
    /**
     * 支出金额
     */
    private BigDecimal payAmount;
}
