package com.upex.reconciliation.service.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.google.common.base.Functions;
import com.upex.apollo.ApolloUtils;
import com.upex.common.function.FunctionP1;
import com.upex.reconciliation.facade.enums.ApolloKeyEnum;
import com.upex.reconciliation.service.business.convert.enums.ReconOrderTypeEnum;
import com.upex.reconciliation.service.business.ruleengine.config.RuleEngineProcessConfig;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.constants.KafkaTopicEnum;
import com.upex.reconciliation.service.model.alarm.AlarmNotifyConfig;
import com.upex.reconciliation.service.model.alarm.AlarmTemplateApolloConfig;
import com.upex.reconciliation.service.model.config.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.checkerframework.checker.units.qual.A;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class ReconciliationApolloConfigUtils {

    private static String NAMESPACE = "application";

    private static final Map<String, Object> cacheMap = new ConcurrentHashMap<>();
    private static final Map<String, String> cacheOutMap = new ConcurrentHashMap<>();


    public static ApolloReconciliationBizConfig getBillConfigByAccountType(Byte accountType) {
        Map<Byte, ApolloReconciliationBizConfig> accountTypeBizConfigMap = getAllBillConfigByAccountType().stream().collect(Collectors.toMap(ApolloReconciliationBizConfig::getAccountType, item -> item));
        return accountTypeBizConfigMap.get(accountType);
    }

    public static List<ApolloReconciliationBizConfig> getAllBillConfigByAccountType() {
        return ApolloUtils.parseList("application", ApolloKeyEnum.RECONCILIATION_BIZ_CONFIG.getKey(), ApolloReconciliationBizConfig.class);
    }

    public static List<ReconOrderConfig> getALlReconOrderConfig() {
        return ApolloUtils.parseList("application", ApolloKeyEnum.RECON_ORDER_CONFIG.getKey(), ReconOrderConfig.class);
    }

    public static ReconOrderConfig getReconOrderConfig(ReconOrderTypeEnum reconOrderType) {
        Map<Byte, ReconOrderConfig> collect = getALlReconOrderConfig().stream().collect(Collectors.toMap(ReconOrderConfig::getOrderType, Function.identity()));
        return collect.get(reconOrderType.getCode());

    }

    /**
     * 报警配置
     *
     * @return
     */
    public static AlarmNotifyConfig getAlarmNotifyConfig() {
        return ApolloUtils.getObjConfig(ApolloKeyEnum.ALARM_NOTIFY_CONFIG.getKey(), AlarmNotifyConfig.class);
    }

    /**
     * 报警配置
     *
     * @return
     */
    public static AlarmTemplateApolloConfig getAlarmTemplateApolloConfig() {
        return ApolloUtils.getObjConfig(ApolloKeyEnum.ALARM_NOTIFY_TEMPLATE_CONFIG.getKey(), AlarmTemplateApolloConfig.class);
    }

    public static AssetsCheckConfig getAssetsCheckConfig(String assetsCheckType, String assetsCheckParam) {
        List<AssetsCheckConfig> allAssetsCheckList = getAllAssetsCheckList();
        Map<String, AssetsCheckConfig> collect = allAssetsCheckList.stream().collect(Collectors.toMap(AssetsCheckConfig::getCheckUniqKey, Functions.identity()));
        return collect.get(BillConstants.buildAssetsCheckUniqKey(assetsCheckType, assetsCheckParam));
    }

    public static List<AssetsCheckConfig> getAllAssetsCheckList() {
        List<AssetsCheckConfig> resultList = new ArrayList<>();
        GlobalBillConfig globalBillConfig = getGlobalBillConfig();
        if (globalBillConfig != null && CollectionUtils.isNotEmpty(globalBillConfig.getAssetsCheckList())) {
            return globalBillConfig.getAssetsCheckList().stream().sorted(Comparator.comparing(AssetsCheckConfig::getPriorityLevel)).collect(Collectors.toList());
        }
        return resultList;
    }


    public static GlobalBillConfig getGlobalBillConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.GLOBAL_BILL_CONFIG, GlobalBillConfig.class);
    }

    public static <T> T getApolloObjectConfig(ApolloKeyEnum apolloKeyEnum, Class<T> clazz) {
        return cacheGetOrInvoke(NAMESPACE, apolloKeyEnum.getKey(), "{}", (configStr) -> {
            return JSONObject.parseObject(configStr, clazz);
        });
    }

    public static String getApolloStringConfig(ApolloKeyEnum apolloKeyEnum) {
        return cacheGetOrInvokeOut(NAMESPACE, apolloKeyEnum.getKey(), "{}");
    }

    /**
     * 获取指定key的apollo配置集合
     *
     * @param apolloKeyEnum apollo配置key枚举
     * @param clazz         需要序列化的类
     * @return {@link T }
     * <AUTHOR>
     * @date 2022/12/8 17:07
     */
    public static <T> List<T> getApolloArrayConfig(ApolloKeyEnum apolloKeyEnum, Class<T> clazz) {
        return cacheGetOrInvoke(NAMESPACE, apolloKeyEnum.getKey(), "[]", (configStr) -> {
            return JSON.parseArray(configStr, clazz);
        });
    }


    private static <T> T cacheGetOrInvoke(String namespace, String key, String defaultValue, FunctionP1<String, T> functionP1) {
        String cacheKey = "billConfig:" + namespace + ":" + key;
        return (T) cacheMap.computeIfAbsent(cacheKey, (tmp) -> {
            Config config = ConfigService.getConfig(namespace);
            String value = config.getProperty(key, defaultValue);
            Set<String> interestedKeys = new HashSet<>();
            interestedKeys.add(key);
            config.addChangeListener((changeEvent) -> {
                ConfigChange configChange = changeEvent.getChange(key);
                if (configChange == null) {
                    log.error("configChange event come but configChange is null key={}", key);
                    return;
                }
                //如果配置项被删除的话会出现newValue is null
                if (configChange.getNewValue() == null) {
                    cacheMap.remove(cacheKey);
                    return;
                }
                T data = functionP1.run(configChange.getNewValue());
                cacheMap.put(cacheKey, data);
            }, interestedKeys);
            return functionP1.run(value);
        });
    }

    private static String cacheGetOrInvokeOut(String namespace, String key, String defaultValue) {
        String cacheKey = "billConfig:" + namespace + ":" + key;
        return cacheOutMap.computeIfAbsent(cacheKey, (tmp) -> {
            Config config = ConfigService.getConfig(namespace);
            String value = config.getProperty(key, defaultValue);
            Set<String> interestedKeys = new HashSet<>();
            interestedKeys.add(key);
            config.addChangeListener((changeEvent) -> {
                ConfigChange configChange = changeEvent.getChange(key);
                if (configChange == null) {
                    log.error("configChange event come but configChange is null key={}", key);
                    return;
                }
                //如果配置项被删除的话会出现newValue is null
                if (configChange.getNewValue() == null) {
                    cacheOutMap.remove(cacheKey);
                    return;
                }
                cacheOutMap.put(cacheKey, configChange.getNewValue());
            }, interestedKeys);
            return value;
        });
    }


    public static MonitorSceneConfig getMonitorSceneConfig() {
        MonitorSceneConfig monitorSceneConfig = getApolloObjectConfig(ApolloKeyEnum.MONITOR_SCENE_CONFIG, MonitorSceneConfig.class);
        return monitorSceneConfig;
    }


    public static MonitorSceneTaskConfig getMonitorSceneTaskConfig(Long sceneId) {
        Map<Long, MonitorSceneTaskConfig> monitorSceneTaskConfigMap = getMonitorSceneConfig().getTaskConfigs().stream().collect(Collectors.toMap(MonitorSceneTaskConfig::getSceneId, item -> item));
        return monitorSceneTaskConfigMap.get(sceneId);
    }

    /**
     * 盈亏动账配置
     *
     * @return
     */
    public static ApolloProfitTransferConfig getApolloProfitTransferConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.PROFIT_TRANSFER_CONFIG, ApolloProfitTransferConfig.class);
    }


    /**
     * 财务收入对账配置
     *
     * @return
     */
    public static IncomeCheckConfig getIncomeCheckConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.INCOME_CHECK_CONFIG, IncomeCheckConfig.class);
    }


    /**
     * fee对账配置
     *
     * @return
     */
    public static FeeCheckConfig getFeeCheckConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.FEE_CHECK_CONFIG, FeeCheckConfig.class);
    }


    /**
     * commission对账配置
     *
     * @return
     */
    public static CommissionCheckConfig getCommissionCheckConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.COMMISSION_CHECK_CONFIG, CommissionCheckConfig.class);
    }

    /**
     * Redis配置
     *
     * @return
     */
    public static ApolloRedisConfig getBillRedisConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.BILL_REDIS_CONFIG, ApolloRedisConfig.class);
    }

    /**
     * Redis配置
     *
     * @return
     */
    public static ApolloRedisConfig getReconRedisConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.RECON_REDIS_CONFIG, ApolloRedisConfig.class);
    }

    public static ApolloKafkaConsumerConfig getKafkaConsumerConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.KAFKA_CONSUMER_CONFIG, ApolloKafkaConsumerConfig.class);
    }

    public static KafkaConsumerConfig getKafkaConsumerConfig(String key) {
        ApolloKafkaConsumerConfig apolloKafkaConsumerConfig = getApolloObjectConfig(ApolloKeyEnum.KAFKA_CONSUMER_CONFIG, ApolloKafkaConsumerConfig.class);
        return apolloKafkaConsumerConfig.getConsumerConfig().get(key);
    }

    public static KafkaConsumerConfig getKafkaConsumerConfig(KafkaTopicEnum kafkaTopicEnum) {
        return getKafkaConsumerConfig().getConsumerConfig().get(kafkaTopicEnum.getConsumerName());
    }

    public static AssetStatisticsConfig getAssetStatisticsConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.ASSET_STATISTICS_CONFIG, AssetStatisticsConfig.class);
    }

    /**
     * 获取网关config
     **/
    public static ApolloGatewayConfig getApolloGatewayConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.UPEX_GATEWAY_CONFIG, ApolloGatewayConfig.class);
    }


    /**
     * getSysUserIdsBillConfig
     *
     * @return
     */
    public static SysUserIdsBillConfig getSysUserIdsBillConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.BILL_SYSUSERIDS_CONFIG, SysUserIdsBillConfig.class);
    }

    public static List<RuleEngineProcessConfig> getRuleEngineProcessConfigList() {
        return ApolloUtils.parseList("application", ApolloKeyEnum.RULE_ENGINE_PROCESS_CONFIG.getKey(), RuleEngineProcessConfig.class);
    }

    public static RuleEngineProcessConfig getRuleEngineProcessConfigByCode(String processCode) {
        List<RuleEngineProcessConfig> ruleEngineProcessConfig = getRuleEngineProcessConfigList();
        return ruleEngineProcessConfig.stream().filter(item -> item.getProcessCode().equals(processCode)).findFirst().orElse(null);
    }

    public static ApolloBillDeleteConfig getApolloBillDeleteConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.BILL_DELETE_CONFIG, ApolloBillDeleteConfig.class);
    }

    public static DelayRetryConfig getDelayRetryConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.DELAY_RETRY_CONFIG, DelayRetryConfig.class);
    }

    public static ProfitBakCheckConfig getProfitBakCheckConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.PROFIT_BAK_CHECK_CONFIG, ProfitBakCheckConfig.class);
    }

    public static ReconTableRouteConfig getReconTableRouteConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.RECON_TABLE_ROUTE_CONFIG, ReconTableRouteConfig.class);
    }


    public static ApolloBillOrderDetailConfig getApolloBillOrderDetailConfig(String businessKey) {
        return getApolloBillOrderDetailConfig().stream().filter(item -> item.getOrderType().equals(businessKey)).findFirst().orElse(null);
    }

    public static List<ApolloBillOrderDetailConfig> getApolloBillOrderDetailConfig() {
        return getApolloBillOrderConfig().getBillOrderCheckList();
    }

    /**
     * 获取订单对账配置
     *
     * @return
     */
    public static ApolloBillOrderConfig getApolloBillOrderConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.BILL_ORDER_CONFIG, ApolloBillOrderConfig.class);
    }

    public static ApolloThirdCexAssetConfig getThirdCexAssetConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.THIRD_CEX_ASSET_CONFIG, ApolloThirdCexAssetConfig.class);
    }

    public static ApolloProxyConfig getApolloProxyConfig() {
        return getApolloObjectConfig(ApolloKeyEnum.UPEX_PROXY_CONFIG, ApolloProxyConfig.class);
    }
}
