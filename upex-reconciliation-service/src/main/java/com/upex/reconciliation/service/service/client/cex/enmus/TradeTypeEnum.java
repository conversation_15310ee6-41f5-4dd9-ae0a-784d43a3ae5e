package com.upex.reconciliation.service.service.client.cex.enmus;

import lombok.Getter;

@Getter
public enum TradeTypeEnum {

    CLASSIC_ACCOUNT(1, "经典账户"), // 经典账户
    UNIFIED_ACCOUNT(2,  "统一账户"); // 统一账户

    private Integer type;
    private String name;
    TradeTypeEnum(Integer type, String name){
        this.type= type;
        this.name= name;
    }

    public  static TradeTypeEnum fromType(Integer type){
        for(TradeTypeEnum tradeTypeEnum : TradeTypeEnum.values()){
            if(tradeTypeEnum.type.equals(type)){
                return tradeTypeEnum;
            }
        }
        return null;
    }
}
