package com.upex.reconciliation.service.business.billengine.command.impl;


import com.upex.mixcontract.common.framework.AttributeMap;
import com.upex.mixcontract.common.framework.command.ICommandEnum;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.billengine.command.AbstractBillCommand;
import com.upex.reconciliation.service.common.constants.ReconciliationCommandEnum;
import com.upex.reconciliation.service.model.dto.BillUserCheckResult;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

@Slf4j
public class BillUserAssetCheckCommand extends AbstractBillCommand<CommonBillChangeData, BillUserCheckResult> implements ApplicationContextAware {


    public BillUserAssetCheckCommand(BillLogicGroup logicGroup) {
        super(logicGroup);
    }

    @Override
    public void init(AttributeMap initContext) {
    }

    @Override
    public BillUserCheckResult execute(CommonBillChangeData request) {
        return null;
    }

    @Override
    public ICommandEnum getType() {
        return ReconciliationCommandEnum.UNKNOWN;
    }

    @Override
    public Class<CommonBillChangeData> getParamClass() {
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    }

}
