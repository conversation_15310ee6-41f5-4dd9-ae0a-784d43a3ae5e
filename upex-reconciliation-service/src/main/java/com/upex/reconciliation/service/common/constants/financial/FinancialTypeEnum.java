package com.upex.reconciliation.service.common.constants.financial;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;
@Getter
public enum FinancialTypeEnum implements FinancialSecondBizLineManager {

    /**
     * 理财二级业务线
     */
    FINANCIAL_POOL_X(POOL_X, "PoolX", 14), // 提前执行
    FINANCIAL_LAUNCH_POOL(LAUNCH_POOL, "LaunchPool理财模块", 3), // 提前执行
    FINANCIAL_SAVINGS(SAVINGS, "活期理财模块", 1),
    FINANCIAL_BGB_EARN(BGB_EARN, "BgbEarn理财模块", 2),
    FINANCIAL_SHARK_FIN(SHARK_FIN, "鲨鱼鳍理财模块", 4),
    FINANCIAL_DUAL_INVEST(DUAL_INVEST, "双币理财模块", 5),
    FINANCIAL_SAVINGS_FIXED(SAVINGS_FIXED, "定期理财(单币)模块", 6),
    FINANCIAL_POS_STAKING(POS_STAKING, "pos质押模块", 7),
    FINANCIAL_TREND(TREND, "趋势智盈模块", 8),
    FINANCIAL_RANGE_SNIPER(RANGE_SNIPER, "区间猎手模块", 9),
    FINANCIAL_BGB_STAKING(BGB_STAKING, "bgb质押模块", 10),
    FINANCIAL_AVALANCHE(AVALANCHE, "雪球理财", 11),
    FINANCIAL_FUND_MARKET(FUND_MARKET, "基金超市模块", 12),
    FINANCIAL_DIRECT_INVEST(DIRECT_INVEST, "现货定投", 13),
    FINANCIAL_ON_CHAIN_ELITE("OnChainElite", "链上精选", 15),
    FINANCIAL_CUSTOM_STRUCTURED("customStructured", "囤币卖币", 16)
    ;


    /**
     * 理财业务类型存储容器
     */
    private static final Map<String, FinancialTypeEnum> container = new HashMap<>();

    /**
     * 理财业务类型存储容器按groupType存储
     */
    private static final Map<Integer, FinancialTypeEnum> GROUP_TYPE_MAP = new HashMap<>();

    /**
     * type-groupType映射
     */
    private static final Map<String, Integer> TYPE_GROUP_MAP = new HashMap<>();

    static {
        Stream.of(FinancialTypeEnum.values()).forEach(e -> {
            container.put(e.type, e);
            GROUP_TYPE_MAP.put(e.getGroupType(), e);
            TYPE_GROUP_MAP.put(e.getType(), e.getGroupType());
        });

    }

    /**
     * 理财二级业务线分类
     */
    private final String type;

    /**
     * 理财二级业务线分类描述
     */
    private final String desc;


    private final Integer groupType;


    FinancialTypeEnum(String typeCode, String typeDesc, Integer groupType) {
        this.type = typeCode;
        this.desc = typeDesc;
        this.groupType = groupType;
    }

    /**
     * 查询 - 获取理财业务类型
     *
     * @param typeCode 业务类型
     * @return 理财业务类型枚举
     */
    public static FinancialTypeEnum getEnum(String typeCode) {
        if (StringUtils.isBlank(typeCode)) {
            return null;
        }
        return container.get(typeCode);
    }

    public static Integer getGroupType(String typeCode) {
        if (StringUtils.isBlank(typeCode)) {
            return null;
        }
        return TYPE_GROUP_MAP.get(typeCode);
    }

    /**
     * 根据groupType获取理财业务类型
     *
     * @param groupType
     * @return
     */
    public static FinancialTypeEnum getByGroupType(Integer groupType) {
        return GROUP_TYPE_MAP.get(groupType);
    }
}
