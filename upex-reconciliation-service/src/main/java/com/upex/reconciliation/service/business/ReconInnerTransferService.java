package com.upex.reconciliation.service.business;

import com.upex.reconciliation.service.dao.entity.CapitalOrder;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;

import java.util.List;

public interface ReconInnerTransferService {
    /**
     * 上报订单数据
     * @param capitalOrders
     *
     * <AUTHOR>
     * @date 2024/11/28 11:34
     */
    void reportedOrderData(List<CapitalOrder> capitalOrders);

    void billHandle(CommonBillChangeData commonBillChangeData);

    void reconCheck();

    void innerTransferRemoveOldRedisData();

    void removeFinanceOrderData(Byte accountType, ApolloReconciliationBizConfig apolloBizConfig, long currentTimeMillis);
}
