package com.upex.reconciliation.service.service.client.cex.enmus;

public enum UniversalTransferRecordTypeEnum {

    /**
     * MAIN_UMFUTURE 现货钱包转向U本位合约钱包
     * MAIN_CMFUTURE 现货钱包转向币本位合约钱包
     * MAIN_MARGIN 现货钱包转向杠杆全仓钱包
     * UMFUTURE_MAIN U本位合约钱包转向现货钱包
     * UMFUTURE_MARGIN U本位合约钱包转向杠杆全仓钱包
     * CMFUTURE_MAIN 币本位合约钱包转向现货钱包
     * MARGIN_MAIN 杠杆全仓钱包转向现货钱包
     * MARGIN_UMFUTURE 杠杆全仓钱包转向U本位合约钱包
     * MARGIN_CMFUTURE 杠杆全仓钱包转向币本位合约钱包
     * CMFUTURE_MARGIN 币本位合约钱包转向杠杆全仓钱包
     * ISOLATEDMARGIN_MARGIN 杠杆逐仓钱包转向杠杆全仓钱包
     * MARGIN_ISOLATEDMARGIN 杠杆全仓钱包转向杠杆逐仓钱包
     * ISOLATEDMARGIN_ISOLATEDMARGIN 杠杆逐仓钱包转向杠杆逐仓钱包
     * MAIN_FUNDING 现货钱包转向资金钱包
     * FUNDING_MAIN 资金钱包转向现货钱包
     * FUNDING_UMFUTURE 资金钱包转向U本位合约钱包
     * UMFUTURE_FUNDING U本位合约钱包转向资金钱包
     * MARGIN_FUNDING 杠杆全仓钱包转向资金钱包
     * FUNDING_MARGIN 资金钱包转向杠杆全仓钱包
     * FUNDING_CMFUTURE 资金钱包转向币本位合约钱包
     * CMFUTURE_FUNDING 币本位合约钱包转向资金钱包
     * MAIN_OPTION 现货钱包转向期权钱包
     * OPTION_MAIN 期权钱包转向现货钱包
     * UMFUTURE_OPTION U本位合约钱包转向期权钱包
     * OPTION_UMFUTURE 期权钱包转向U本位合约钱包
     * MARGIN_OPTION 杠杆全仓钱包转向期权钱包
     * OPTION_MARGIN 期权全仓钱包转向杠杆钱包
     * FUNDING_OPTION 资金钱包转向期权钱包
     * OPTION_FUNDING 期权钱包转向资金钱包
     * MAIN_PORTFOLIO_MARGIN 现货钱包转向统一账户钱包
     * PORTFOLIO_MARGIN_MAIN 统一账户钱包转向现货钱包
     * MAIN_ISOLATED_MARGIN 现货钱包转向逐仓账户钱包
     * ISOLATED_MARGIN_MAIN 逐仓钱包转向现货账户钱包
     */


    MAIN_UMFUTURE("MAIN_UMFUTURE", "SPOT", "UMFUTURE"),
    MAIN_CMFUTURE("MAIN_CMFUTURE", "SPOT", "CMFUTURE"),
    MAIN_MARGIN("MAIN_MARGIN", "SPOT", "MARGIN"),
    UMFUTURE_MAIN("UMFUTURE_MAIN", "UMFUTURE", "SPOT"),
    UMFUTURE_MARGIN("UMFUTURE_MARGIN", "UMFUTURE", "MARGIN"),
    CMFUTURE_MAIN("CMFUTURE_MAIN", "CMFUTURE", "SPOT"),
    MARGIN_MAIN("MARGIN_MAIN", "MARGIN", "SPOT"),
    MARGIN_UMFUTURE("MARGIN_UMFUTURE", "MARGIN", "UMFUTURE"),
    MARGIN_CMFUTURE("MARGIN_CMFUTURE", "MARGIN", "CMFUTURE"),
    CMFUTURE_MARGIN("CMFUTURE_MARGIN", "CMFUTURE", "MARGIN"),
   // ISOLATEDMARGIN_MARGIN("ISOLATEDMARGIN_MARGIN", "ISOLATED_MARGIN", "MARGIN"),
   // MARGIN_ISOLATEDMARGIN("MARGIN_ISOLATEDMARGIN", "MARGIN", "ISOLATED_MARGIN"),
   // ISOLATEDMARGIN_ISOLATEDMARGIN("ISOLATEDMARGIN_ISOLATEDMARGIN", "ISOLATED_MARGIN", "ISOLATED_MARGIN"),
    MAIN_FUNDING("MAIN_FUNDING", "SPOT", "FUNDING"),
    FUNDING_MAIN("FUNDING_MAIN", "FUNDING", "SPOT"),
    FUNDING_UMFUTURE("FUNDING_UMFUTURE", "FUNDING", "UMFUTURE"),
    UMFUTURE_FUNDING("UMFUTURE_FUNDING", "UMFUTURE", "FUNDING"),
    MARGIN_FUNDING("MARGIN_FUNDING", "MARGIN", "FUNDING"),
    FUNDING_MARGIN("FUNDING_MARGIN", "FUNDING", "MARGIN"),
    FUNDING_CMFUTURE("FUNDING_CMFUTURE", "FUNDING", "CMFUTURE"),
    CMFUTURE_FUNDING("CMFUTURE_FUNDING", "CMFUTURE", "FUNDING"),
    MAIN_OPTION("MAIN_OPTION", "SPOT", "OPTION"),
    OPTION_MAIN("OPTION_MAIN", "OPTION", "SPOT"),
    UMFUTURE_OPTION("UMFUTURE_OPTION", "UMFUTURE", "OPTION"),
    OPTION_UMFUTURE("OPTION_UMFUTURE", "OPTION", "UMFUTURE"),
    MARGIN_OPTION("MARGIN_OPTION", "MARGIN", "OPTION"),
    OPTION_MARGIN("OPTION_MARGIN", "OPTION", "MARGIN"),
    FUNDING_OPTION("FUNDING_OPTION", "FUNDING", "OPTION"),
    OPTION_FUNDING("OPTION_FUNDING", "OPTION", "FUNDING"),
    MAIN_PORTFOLIO_MARGIN("MAIN_PORTFOLIO_MARGIN", "SPOT", "PORTFOLIO_MARGIN"),
    PORTFOLIO_MARGIN_MAIN("PORTFOLIO_MARGIN_MAIN", "PORTFOLIO_MARGIN", "SPOT"),;
//    MAIN_ISOLATED_MARGIN("MAIN_ISOLATED_MARGIN", "SPOT", "ISOLATED_MARGIN"),
//    ISOLATED_MARGIN_MAIN("ISOLATED_MARGIN_MAIN", "ISOLATED_MARGIN", "SPOT");

    private final String name;
    private final String fromAccountType;
    private final String toAccountType;

    UniversalTransferRecordTypeEnum(String name, String fromAccountType, String toAccountType) {
        this.name = name;
        this.fromAccountType = fromAccountType;
        this.toAccountType = toAccountType;
    }

    public String getName() {
        return name;
    }

    public String getFromAccountType() {
        return fromAccountType;
    }

    public String getToAccountType() {
        return toAccountType;
    }

}
