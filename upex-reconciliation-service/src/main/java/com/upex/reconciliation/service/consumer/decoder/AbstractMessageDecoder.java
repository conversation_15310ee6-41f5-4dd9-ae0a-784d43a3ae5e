package com.upex.reconciliation.service.consumer.decoder;

import com.alibaba.otter.canal.protocol.FlatMessage;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.model.dto.FixKafkaMessageData;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 消息解析抽象类
 */
@Slf4j
public abstract class AbstractMessageDecoder implements MessageDecoder {
    /**
     * 修复流水
     * "repairKafkaMessageList":[
     * {"bizId":"********","changeProp1":0,"prop1":0,"changeProp2":0,"prop2":0,"changeProp3":0,"prop3":0,"changeProp4":0,"prop4":0,"changeProp5":0,"prop5":0}
     * ]
     *
     * @param dataList
     * @param flatMessage
     * @param partition
     * @param offset
     * @param accountType
     * @return
     */
    public List<CommonBillChangeData> messageDecode(AlarmNotifyService alarmNotifyService, List<Map<String, String>> dataList, FlatMessage flatMessage, Integer partition, Long offset, Byte accountType) {
        List<CommonBillChangeData> commonBillChangeDataList = null;
        try {
            commonBillChangeDataList = doMessageDecode(dataList, flatMessage, partition, offset, accountType);
        } catch (Exception e) {
            log.error("AbstractMessageDecoder.messageDecode error accountType:{} partition:{} offset:{} dataList:{},error:{}", accountType, partition, offset, dataList, e.getMessage());
            throw e;
        }
        if (CollectionUtils.isNotEmpty(commonBillChangeDataList)) {
            ApolloReconciliationBizConfig apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountType);
            // 数据修复逻辑
            List<FixKafkaMessageData> repairKafkaMessageList = apolloBizConfig.getRepairKafkaMessageList();
            if (CollectionUtils.isNotEmpty(repairKafkaMessageList)) {
                Map<Long, FixKafkaMessageData> fixKafkaMessageDataMap = repairKafkaMessageList.stream().collect(Collectors.toMap(FixKafkaMessageData::getBizId, item -> item));
                for (CommonBillChangeData billChangeData : commonBillChangeDataList) {
                    FixKafkaMessageData fixKafkaMessageData = fixKafkaMessageDataMap.get(billChangeData.getBizId());
                    if (fixKafkaMessageData != null) {
                        billChangeData.setProp1(fixKafkaMessageData.getProp1() != null ? fixKafkaMessageData.getProp1() : billChangeData.getProp1());
                        billChangeData.setProp2(fixKafkaMessageData.getProp2() != null ? fixKafkaMessageData.getProp2() : billChangeData.getProp2());
                        billChangeData.setProp3(fixKafkaMessageData.getProp3() != null ? fixKafkaMessageData.getProp3() : billChangeData.getProp3());
                        billChangeData.setProp4(fixKafkaMessageData.getProp4() != null ? fixKafkaMessageData.getProp4() : billChangeData.getProp4());
                        billChangeData.setProp5(fixKafkaMessageData.getProp5() != null ? fixKafkaMessageData.getProp5() : billChangeData.getProp5());
                        billChangeData.setChangeProp1(fixKafkaMessageData.getChangeProp1() != null ? fixKafkaMessageData.getChangeProp1() : billChangeData.getChangeProp1());
                        billChangeData.setChangeProp2(fixKafkaMessageData.getChangeProp2() != null ? fixKafkaMessageData.getChangeProp2() : billChangeData.getChangeProp2());
                        billChangeData.setChangeProp3(fixKafkaMessageData.getChangeProp3() != null ? fixKafkaMessageData.getChangeProp3() : billChangeData.getChangeProp3());
                        billChangeData.setChangeProp4(fixKafkaMessageData.getChangeProp4() != null ? fixKafkaMessageData.getChangeProp4() : billChangeData.getChangeProp4());
                        billChangeData.setChangeProp5(fixKafkaMessageData.getChangeProp5() != null ? fixKafkaMessageData.getChangeProp5() : billChangeData.getChangeProp5());
                        billChangeData.setBizTime(fixKafkaMessageData.getBizTime() != null ? fixKafkaMessageData.getBizTime() : billChangeData.getBizTime());
                        billChangeData.setBizTimeFromId(fixKafkaMessageData.getBizTime() != null ? fixKafkaMessageData.getBizTime() : billChangeData.getBizTimeFromId());
                        billChangeData.setCreateTime(fixKafkaMessageData.getCreateTime() != null ? fixKafkaMessageData.getCreateTime() : billChangeData.getCreateTime());
                        billChangeData.setBizType(StringUtils.isNotEmpty(fixKafkaMessageData.getBizType()) ? fixKafkaMessageData.getBizType() : billChangeData.getBizType());
                    }
                }
            }
        }
        return commonBillChangeDataList;
    }

    public abstract List<CommonBillChangeData> doMessageDecode(List<Map<String, String>> dataList, FlatMessage flatMessage, Integer partition, Long offset, Byte accountType);
}
