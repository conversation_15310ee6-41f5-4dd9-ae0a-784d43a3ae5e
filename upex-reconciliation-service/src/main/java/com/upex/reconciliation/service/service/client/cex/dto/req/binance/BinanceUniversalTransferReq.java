package com.upex.reconciliation.service.service.client.cex.dto.req.binance;

import com.binance.connector.client.common.Pair;
import lombok.Data;

/**
 * 母用户万向划转
 */
@Data
public class BinanceUniversalTransferReq extends BinanceApiBaseReq{

    private Long startTime;

    private Long endTime;

    private String type;

    public void setStartTime(Long startTime) {
        queryParams.add(new Pair("startTime", String.valueOf(startTime)));
    }

    public void setEndTime(Long endTime) {
       queryParams.add(new Pair("endTime", String.valueOf(endTime)));
    }

    public void setType(String type) {
        queryParams.add(new Pair("type", type));
    }
}
