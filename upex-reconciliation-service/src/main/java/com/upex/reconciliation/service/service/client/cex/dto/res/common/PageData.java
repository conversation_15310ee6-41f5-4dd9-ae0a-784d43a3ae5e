package com.upex.reconciliation.service.service.client.cex.dto.res.common;


import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.parameters.P;

import java.util.Collections;
import java.util.List;

/**
 * 分页返回数据封装
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PageData<T> {

    /**
     * 当前页数据
     */
    private List<T> items;



    /**
     * 总记录数
     */
    private long total;

    public PageData(List<T> items, int total) {
        this.items = items;
        this.total= total;
    }

    public static PageData empty(){
        return new PageData(Collections.EMPTY_LIST,0);
    }
}
