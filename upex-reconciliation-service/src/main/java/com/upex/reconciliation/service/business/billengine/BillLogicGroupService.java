package com.upex.reconciliation.service.business.billengine;

import com.upex.commons.support.exception.ApiException;
import com.upex.mixcontract.common.literal.enums.ExceptionEnum;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 根据 config 获取到执行引擎的 BillLogicGroup
 */
@Service
@Slf4j
public class BillLogicGroupService {


    @Autowired
    private BillEngineManager application;

    public BillLogicGroup checkAndGetBillLogicGroup(ApolloReconciliationBizConfig apolloBizConfig) {
        BillLogicGroup matchLogicGroup = application.getBillLogicGroup(apolloBizConfig.getAccountType());
        if (matchLogicGroup == null) {
            throw new ApiException(ExceptionEnum.UNKNOWN_ERROR);
        }
        AbstractBillEngine engine = matchLogicGroup.getEngine();
        if (engine == null) {
            throw new ApiException(ExceptionEnum.UNKNOWN_ERROR);
        }
        return matchLogicGroup;
    }
}
