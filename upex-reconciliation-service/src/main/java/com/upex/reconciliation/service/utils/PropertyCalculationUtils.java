package com.upex.reconciliation.service.utils;

import com.upex.reconciliation.service.model.dto.AbstractProperty;

import java.math.BigDecimal;

public class PropertyCalculationUtils {


    public static <T extends AbstractProperty> void addChange(T newProperty, T oldProperty){
        if (oldProperty.getChangeProp1().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setChangeProp1(newProperty.getChangeProp1());
        } else {
            newProperty.setChangeProp1(newProperty.getChangeProp1().add(oldProperty.getChangeProp2()));
        }
        if (oldProperty.getChangeProp2().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setChangeProp2(newProperty.getChangeProp2());
        } else {
            newProperty.setChangeProp2(newProperty.getChangeProp2().add(oldProperty.getChangeProp2()));
        }
        if (oldProperty.getChangeProp3().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setChangeProp3(newProperty.getChangeProp3());
        } else {
            newProperty.setChangeProp3(newProperty.getChangeProp3().add(oldProperty.getChangeProp3()));
        }
        if (oldProperty.getChangeProp4().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setChangeProp4(newProperty.getChangeProp4());
        } else {
            newProperty.setChangeProp4(newProperty.getChangeProp4().add(oldProperty.getChangeProp4()));
        }
        if (oldProperty.getChangeProp5().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setChangeProp5(newProperty.getChangeProp5());
        } else {
            newProperty.setChangeProp5(newProperty.getChangeProp5().add(oldProperty.getChangeProp5()));
        }
    }

    /**
     * 加合计算
     *
     * @param newProperty
     * @param oldProperty
     * @param <T>
     * @return
     */
    public static <T extends AbstractProperty> void addData(T newProperty, T oldProperty, boolean flag) {

        if (oldProperty.getProp1().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setProp1(newProperty.getProp1());
        } else {
            newProperty.setProp1(newProperty.getProp1().add(oldProperty.getProp1()));
        }
        if (oldProperty.getProp2().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setProp2(newProperty.getProp2());
        } else {
            newProperty.setProp2(newProperty.getProp2().add(oldProperty.getProp2()));
        }
        if (oldProperty.getProp3().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setProp3(newProperty.getProp3());
        } else {
            newProperty.setProp3(newProperty.getProp3().add(oldProperty.getProp3()));
        }
        if (oldProperty.getProp4().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setProp4(newProperty.getProp4());
        } else {
            newProperty.setProp4(newProperty.getProp4().add(oldProperty.getProp4()));
        }
        if (oldProperty.getProp5().compareTo(BigDecimal.ZERO) == 0) {
            newProperty.setProp5(newProperty.getProp5());
        } else {
            newProperty.setProp5(newProperty.getProp5().add(oldProperty.getProp5()));
        }
        if (flag) {
            if (oldProperty.getChangeProp1().compareTo(BigDecimal.ZERO) == 0) {
                newProperty.setChangeProp1(newProperty.getProp1());
            } else {
                newProperty.setChangeProp1(newProperty.getProp1().add(oldProperty.getChangeProp1()));
            }
            if (oldProperty.getChangeProp2().compareTo(BigDecimal.ZERO) == 0) {
                newProperty.setChangeProp2(newProperty.getProp2());
            } else {
                newProperty.setChangeProp2(newProperty.getProp2().add(oldProperty.getChangeProp2()));
            }
            if (oldProperty.getChangeProp3().compareTo(BigDecimal.ZERO) == 0) {
                newProperty.setChangeProp3(newProperty.getProp3());
            } else {
                newProperty.setChangeProp3(newProperty.getProp3().add(oldProperty.getChangeProp3()));
            }
            if (oldProperty.getChangeProp4().compareTo(BigDecimal.ZERO) == 0) {
                newProperty.setChangeProp4(newProperty.getProp4());
            } else {
                newProperty.setChangeProp4(newProperty.getProp4().add(oldProperty.getChangeProp4()));
            }
            if (oldProperty.getChangeProp5().compareTo(BigDecimal.ZERO) == 0) {
                newProperty.setChangeProp5(newProperty.getProp5());
            } else {
                newProperty.setChangeProp5(newProperty.getProp5().add(oldProperty.getChangeProp5()));
            }
        } else {
            newProperty.setChangeProp1(oldProperty.getProp1());
            newProperty.setChangeProp2(oldProperty.getProp2());
            newProperty.setChangeProp3(oldProperty.getProp3());
            newProperty.setChangeProp4(oldProperty.getProp4());
            newProperty.setChangeProp5(oldProperty.getProp5());
        }
        newProperty.setUpdateFlag(true);

    }


    public static <T extends AbstractProperty> boolean checkAssetProperty(T newProperty, T oldProperty) {
        if (newProperty == null && oldProperty != null || newProperty != null && oldProperty == null) {
            return false;
        }
        if (oldProperty == null && newProperty == null) {
            return true;
        }
        if (oldProperty.getProp1().compareTo(newProperty.getProp1()) != 0) {
            return false;
        }
        if (oldProperty.getProp2().compareTo(newProperty.getProp2()) != 0) {
            return false;
        }
        if (oldProperty.getProp3().compareTo(newProperty.getProp3()) != 0) {
            return false;
        }
        if (oldProperty.getProp4().compareTo(newProperty.getProp4()) != 0) {
            return false;
        }
        return true;
    }





}
