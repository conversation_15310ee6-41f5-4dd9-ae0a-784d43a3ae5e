package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexWithdrawHistory;
import com.upex.reconciliation.service.dao.mapper.cex.ThirdCexWithdrawHistoryMapper;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.UserWithdrawHistoryListReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ThirdCexWithdrawHistoryService {

    @Resource
    BillDbHelper billDbHelper;

    @Resource
    ThirdCexWithdrawHistoryMapper thirdCexWithdrawHistoryMapper;

    public Integer insert(ThirdCexWithdrawHistory history) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexWithdrawHistoryMapper.insert(history));
    }

    public Integer batchInsert(List<ThirdCexWithdrawHistory> records) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexWithdrawHistoryMapper.batchInsert(records));
    }

    public List<ThirdCexWithdrawHistory> selectPageByUserIds(List<String> cexUserIds, UserWithdrawHistoryListReq withdrawReq) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexWithdrawHistoryMapper.selectPageByUserIds(cexUserIds, withdrawReq));
    }

    public int countPageByUserIds(List<String> cexUserIds, UserWithdrawHistoryListReq withdrawReq) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexWithdrawHistoryMapper.countPageByUserIds(cexUserIds, withdrawReq));
    }

    public int deleteByWithdrawIds(List<String> withdrawIds) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexWithdrawHistoryMapper.deleteByWithdrawIds(withdrawIds));
    }


    public List<ThirdCexWithdrawHistory> selectUnCheckWithdraw(Integer cexType, Integer isLegal) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexWithdrawHistoryMapper.selectUnCheckWithdraw(cexType, isLegal));
    }

    public int checkWithdraw(Long id, Integer isLegal,String bgUserId,Integer isBgSys) {
        return billDbHelper.doDbOpInReconMaster(() -> thirdCexWithdrawHistoryMapper.checkWithdraw(id, isLegal,  bgUserId, isBgSys));
    }


}
