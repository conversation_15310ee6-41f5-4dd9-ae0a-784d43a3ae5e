package com.upex.reconciliation.service.business.createtablebyroute;

import com.upex.reconciliation.service.model.config.ReconTableRouteConfig;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class ReconCreateTableFactory {
    public static final String DEFAULT_ACCOUNT_TYPE = "default";
    @Autowired
    private List<ITableCreator> tableCreators;
    private Map<String, ITableCreator> createTableMap = new HashMap<>();

    @PostConstruct
    private void init() {
        tableCreators.forEach(tableCreator -> {
            createTableMap.put(tableCreator.getTableType(), tableCreator);
        });
    }


    public void createTable() {
        ReconTableRouteConfig reconTableRouteConfig = ReconciliationApolloConfigUtils.getReconTableRouteConfig();
        if (Objects.isNull(reconTableRouteConfig)) {
            log.warn("reconTableRouteConfig is empty");
            return;
        }
        for (Map.Entry<String, Map<String, ReconTableRouteConfig.TableRouteRule>> entry : reconTableRouteConfig.getRouteConfig().entrySet()) {
            String tableType = entry.getKey();
            Map<String, ReconTableRouteConfig.TableRouteRule> tableRouteRuleMap = entry.getValue();
            for (Map.Entry<String, ReconTableRouteConfig.TableRouteRule> tableRouteRuleEntry : tableRouteRuleMap.entrySet()) {
                ReconTableRouteConfig.TableRouteRule tableRouteRule = tableRouteRuleEntry.getValue();
                ITableCreator tableCreator = createTableMap.get(tableType);
                // 默认配置 创建所有业务类型 否则创建指定业务类型
                if (DEFAULT_ACCOUNT_TYPE.equals(tableRouteRuleEntry.getKey())) {
                    //兼容billcontractprofittransfer-accountType为空的情况
                    if(CollectionUtils.isEmpty(tableRouteRule.getAccountTypes())){
                        tableCreator.createTable(null);
                    }else {
                        for (String accountType : tableRouteRule.getAccountTypes()) {
                            tableCreator.createTable(accountType);
                        }
                    }
                } else {
                    tableCreator.createTable(tableRouteRuleEntry.getKey());
                }
            }
        }
    }
}
