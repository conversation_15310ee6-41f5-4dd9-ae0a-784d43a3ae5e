package com.upex.reconciliation.service.business.profitbacktest;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.model.config.ProfitBakCheckConfig;
import com.upex.reconciliation.service.model.dto.ProfitBakCheckReq;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 指定用户回测
 */
@Slf4j
@Service
public class SpecificUserProfitBakCheck extends AbstractProfitBakCheck {


    @Override
    public ProfitBakCheckReq getProfitCheckReq() {
        ProfitBakCheckConfig profitBakCheckConfig = ReconciliationApolloConfigUtils.getProfitBakCheckConfig();
        if (profitBakCheckConfig == null) {
            return null;
        }
        ProfitBakCheckReq profitBakCheckReq = ProfitBakCheckReq.builder()
                .userIds(profitBakCheckConfig.getUserIds())
                .reqStartTime(new Date(profitBakCheckConfig.getReqStartTime()))
                .build();
        log.info("指定用户回测:{}", JSONObject.toJSONString(profitBakCheckReq));
        return profitBakCheckReq;
    }


    @Override
    public Boolean isSupport() {
        //配置apollo就是指定回测
        Boolean isSupport = true;
        ProfitBakCheckConfig profitBakCheckConfig = ReconciliationApolloConfigUtils.getProfitBakCheckConfig();
        if (profitBakCheckConfig != null && CollectionUtils.isNotEmpty(profitBakCheckConfig.getUserIds()) && profitBakCheckConfig.getReqStartTime() != null &&
                (System.currentTimeMillis() - profitBakCheckConfig.getEffectiveTime() < BillConstants.FIVE_MINE_MIL_SEC)) {
            isSupport = true;
            return isSupport;
        }
        //配置xxl-job就是指定回测
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.isBlank(jobParam)) {
            isSupport = false;
        }
        try {
            JSONObject data = JSONObject.parseObject(jobParam);
            JSONArray userIds = data.getJSONArray("userIds");
            Long effectiveTime = data.getLong("effectiveTime");
            Long startTime = data.getLong("startTime");
            if (CollectionUtils.isNotEmpty(userIds) && (System.currentTimeMillis() - effectiveTime) < BillConstants.FIVE_MINE_MIL_SEC &&
                    startTime != null) {
                isSupport = true;
                return isSupport;
            }
        } catch (Exception e) {
            log.info("xxl-job-profit-bakcheck jobParam is error");
            isSupport = false;
        }
        return isSupport;
    }
}
