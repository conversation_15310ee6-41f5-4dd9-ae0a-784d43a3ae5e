package com.upex.reconciliation.service.service;

import com.upex.reconciliation.service.common.BillDbHelper;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.dao.mapper.BillCoinUserPropertyAssetSnapshotMapper;
import com.upex.reconciliation.service.model.dto.UserCoinIdDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;


@Service
public class BillCoinUserPropertyAssetSnapshotService {
    @Resource
    private BillDbHelper dbHelper;
    @Resource(name = "billCoinUserPropertyAssetSnapshotMapper")
    private BillCoinUserPropertyAssetSnapshotMapper billCoinUserPropertyAssetSnapshotMapper;


    public Boolean deleteByCheckTime(Byte accountType, String accountParam, Date checkTime, Long pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.deleteByCheckTime(accountType, accountParam, checkTime, pageSize));
    }


    public Boolean updateById(Byte accountType, String accountParam, BillCoinUserProperty billCoinUserProperty) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.updateById(accountType, accountParam, billCoinUserProperty));
    }

    public Boolean updateSelectiveById(Byte accountType, String accountParam, BillCoinUserProperty billCoinUserProperty) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.updateSelectiveById(accountType, accountParam, billCoinUserProperty));
    }

    public BillCoinUserProperty selectTime(Long userId,
                                           Integer accountType,
                                           String accountParam,
                                           Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectTime(userId, accountType, accountParam, checkTime));
    }

    public BillCoinUserProperty selectRecordLimitByUserCoin(Long userId,
                                           Integer coinId,
                                           Integer accountType,
                                           String accountParam,
                                           Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectRecordLimitByUserCoin(userId, coinId, accountType, accountParam, checkTime));
    }


    public BillCoinUserProperty selectById(Byte accountType, String accountParam, Long id) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectById(accountType, accountParam, id));
    }

    public int batchInsert(List<BillCoinUserProperty> records, Byte accountType, String accountParam) {
        if (CollectionUtils.isNotEmpty(records)) {
            return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.batchInsert(records, accountType, accountParam));
        }
        return 0;
    }

    public Boolean batchDelete(Long beginId, Long pageSize, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.batchDelete(beginId, pageSize, accountType, accountParam));
    }

    public int batchInsertOrUpdate(List<BillCoinUserProperty> records, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.batchInsertOrUpdate(records, accountType, accountParam));
    }

    public List<BillCoinUserProperty> selectRangeCheckTimeRecordPage(Integer accountType,
                                                                     String accountParam,
                                                                     Date startTime,
                                                                     Date endTime,
                                                                     Long minId,
                                                                     Long pageSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectRangeCheckTimeRecordPage(accountType, accountParam, startTime, endTime, minId, pageSize));
    }

    public List<BillCoinUserProperty> selectMaxCheckTime(Byte accountType,
                                                         String accountParam,
                                                         Long userId) {
        List<BillCoinUserProperty> list = dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectMaxCheckTime(accountType, accountParam, userId));
        return CollectionUtils.isNotEmpty(list) ? list : Collections.emptyList();
    }


    public List<BillCoinUserProperty> selectCheckTimeRecord(Integer accountType,
                                                            String accountParam,
                                                            Date checkTime,
                                                            Long beginId,
                                                            Long pageSize) {
        List<BillCoinUserProperty> result = dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectCheckTimeRecord(accountType, accountParam, checkTime, beginId, pageSize));
        if (CollectionUtils.isNotEmpty(result)) {
            return result;
        }
        return Collections.emptyList();
    }

    public BillCoinUserProperty selectCoinUserBeforeCheckTimeRecord(Integer accountType,
                                                                    String accountParam,
                                                                    Integer coinId,
                                                                    Long userId,
                                                                    Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectCoinUserBeforeCheckTimeRecord(accountType, accountParam, coinId, userId, checkTime));
    }


    public List<BillCoinUserProperty> selectAllCoinUserByCheckTimeAndCoinIds(Integer accountType,
                                                                             String accountParam,
                                                                             Long userId,
                                                                             Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectAllCoinUserByCheckTimeAndCoinIds(accountType, accountParam, userId, checkTime));
    }


    public BillCoinUserProperty selectCoinUserLatest(Integer accountType,
                                                     String accountParam,
                                                     Integer coinId,
                                                     Long userId) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectCoinUserLatest(accountType, accountParam, coinId, userId));
    }

    public BillCoinUserProperty selectCoinUserAfterCheckTimeRecord(Integer accountType,
                                                                   String accountParam,
                                                                   Integer coinId,
                                                                   Long userId,
                                                                   Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectCoinUserAfterCheckTimeRecord(accountType, accountParam, coinId, userId, checkTime));
    }

    public List<BillCoinUserProperty> selectUserLatestRecord(Integer accountType,
                                                             String accountParam,
                                                             Long userId,
                                                             Integer coinId) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectUserLatestRecord(accountType, accountParam, userId, coinId));
    }

    public List<BillCoinUserProperty> selectByIds(Date checkTime, Integer coinId, List<Long> uids, Byte accountType, String accountParam) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectByIds(checkTime, coinId, uids, accountType, accountParam));
    }

    /**
     * 获取时间片id
     *
     * @param checkTime
     * @return
     */
    public Long getIdByCheckTime(Byte accountType, String accountParam, Date checkTime, String operation) {
        String operationNew = operation;
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.getIdByCheckTime(accountType, accountParam, checkTime, operationNew));
    }

    /**
     * 删除数据
     *
     * @param accountType
     * @param accountParam
     * @param maxId
     * @param batchSize
     * @return
     */
    public Boolean deleteByMaxId(byte accountType, String accountParam, Long maxId, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.deleteByMaxId(accountType, accountParam, maxId, batchSize));
    }

    /**
     * 查询用户资产
     *
     * @param accountType
     * @param accountParam
     * @param userId
     * @param coinIds
     * @param checkTime
     * @return
     */
    public List<BillCoinUserProperty> selectCoinUserSnapshotAsset(Byte accountType,
                                                                  String accountParam,
                                                                  Long userId,
                                                                  List<Integer> coinIds,
                                                                  Date checkTime) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectCoinUserSnapshotAsset(accountType, accountParam, userId, coinIds, checkTime));
    }

    public List<UserCoinIdDTO> selectByUserAndCoins(Byte accountType,
                                                    String accountParam,
                                                    List<BillCoinUserProperty> records) {
        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.selectByUserAndCoins(accountType, accountParam, records));
    }

    public Boolean deleteByLtCheckTime(Byte accountType, String accountParam, Date checkTime, Long batchSize) {
        return dbHelper.doDbOpInReconMaster(() -> billCoinUserPropertyAssetSnapshotMapper.deleteByLtCheckTime(accountType, accountParam, checkTime, batchSize));
    }
}
