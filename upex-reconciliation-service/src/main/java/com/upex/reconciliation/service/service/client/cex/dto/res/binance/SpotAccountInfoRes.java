package com.upex.reconciliation.service.service.client.cex.dto.res.binance;

import lombok.Data;

import java.util.List;

@Data
public class SpotAccountInfoRes implements IBinanceApiBaseRes {
    /**
     *  "makerCommission": 10,
     *     "takerCommission": 10,
     *     "buyerCommission": 0,
     *     "sellerCommission": 0,
     *     "commissionRates": {
     *         "maker": "0.********",
     *         "taker": "0.********",
     *         "buyer": "0.********",
     *         "seller": "0.********"
     *     },
     *     "canTrade": true,
     *     "canWithdraw": true,
     *     "canDeposit": true,
     *     "brokered": false,
     *     "requireSelfTradePrevention": false,
     *     "preventSor": false,
     *     "updateTime": *************,
     *     "accountType": "SPOT",
     *     "balances": [
     *         {
     *             "asset": "BTC",
     *             "free": "0.********",
     *             "locked": "0.********"
     *         }
     *         ]
     */
    private Integer makerCommission;
    private Integer takerCommission;
    private Integer buyerCommission;
    private Integer sellerCommission;
    private CommissionRatesRes commissionRates;
    private Boolean canTrade;
    private Boolean canWithdraw;
    private Boolean canDeposit;
    private Boolean brokered;
    private Boolean requireSelfTradePrevention;
    private Boolean preventSor;
    private List<CoinAssetInnerRes> balances;

    private Long updateTime;

    private String accountType;

    @Data
    private class CommissionRatesRes {
        private String maker;
        private String taker;
        private String buyer;
        private String seller;
    }

    //todo 其他账户信息需要再定义
}
