package com.upex.reconciliation.service.common.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资金对账状态枚举
 * <AUTHOR>
 * @date 2023/4/19 17:51
 */
@AllArgsConstructor
@Getter
public enum BillCapitalStatusEnum {
    /**
     * 默认为0-初始化
     */
    INIT(0, "初始化"),
    /**
     * 1-对账完成：终态
     */
    DONE(1, "对账完成"),
    /**
     * 2-对账异常：终态
     */
    EXCEPTION(2, "对账异常"),
    /**
     * 3-对账异常未完成，执行剩余重试次数：中间态
     * 如果本次执行结束，对账异常，判定是否大于重试次数，否，中间态
     */
    EXCEPTION_UNDONE(3, "资金对账待确认")

    ;

    /**
     * 状态码
     */
    private Integer statusCode;
    /**
     * 状态描述
     */
    private String statusDesc;


}
