package com.upex.reconciliation.service.common.constants.enums;

/**
 * 是否有持仓
 * <AUTHOR>
 */
public enum ActiveFlagEnum {

    /**
     * 无
     */
    INVALID(0, "无效"),
    /**
     * 有
     */
    Active(1, "有效");


    private Integer code;
    private String desc;

    ActiveFlagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ActiveFlagEnum toEnum(Integer code) {
        for (ActiveFlagEnum item : ActiveFlagEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }
}
