package com.upex.reconciliation.service.service.client.cex.enmus;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum ThirdAssetType {

    PARENT_FUNDING(10,  "母账户资金"),
    PARENT_SPOT(11,  "母账户现货"),
    PARENT_MARGIN(12,  "母账户全仓"),
    PARENT_ISOLATED_MARGIN(13,  "母账户逐仓"),
    PARENT_UCONTRACT(14,"母账户U合约"),
    PARENT_COIN_CONTRACT(15, "母账户币币合约"),
    PARENT_FLEX_EARN(16,  "母账户活期理财"),
    PARENT_LOCKED_EARN(17,  "母账户定期理财"),

    SUB_SPOT(30, "子账户现货"),
    SUB_MARGIN(31, "子账户全仓"),
    SUB_ISOLATED_MARGIN(32,  "子账户逐仓"),
    SUB_UCONTRACT(33,  "子账户U合约"),
    SUB_COIN_CONTRACT(34,  "子账户币币合约");

    private Integer type;
    private String name;

    ThirdAssetType(Integer type,  String name) {
        this.type = type;
        this.name = name;
    }

    public static Boolean isParent(ThirdAssetType assetType){
        return assetType.equals(ThirdAssetType.PARENT_SPOT)||assetType.equals(ThirdAssetType.PARENT_MARGIN)||assetType.equals(ThirdAssetType.PARENT_ISOLATED_MARGIN)||assetType.equals(ThirdAssetType.PARENT_UCONTRACT)||assetType.equals(ThirdAssetType.PARENT_COIN_CONTRACT)||assetType.equals(ThirdAssetType.PARENT_FLEX_EARN)||assetType.equals(ThirdAssetType.PARENT_LOCKED_EARN);
    }

    public static ThirdAssetType fromType(Integer type){
        for(ThirdAssetType assetType:ThirdAssetType.values()){
            if(assetType.type.equals(type)){
                return assetType;
            }
        }
        return null;
    }

    public static List<Integer> subUserAssetTypes() {
        return Arrays.asList(ThirdAssetType.SUB_SPOT, ThirdAssetType.SUB_MARGIN, ThirdAssetType.SUB_UCONTRACT, ThirdAssetType.SUB_COIN_CONTRACT).stream().map(ThirdAssetType::getType).collect(Collectors.toList());
    }

    public static List<ThirdAssetType> subUserAssetTypeEnums() {
        return Arrays.asList(ThirdAssetType.SUB_SPOT, ThirdAssetType.SUB_MARGIN, ThirdAssetType.SUB_UCONTRACT, ThirdAssetType.SUB_COIN_CONTRACT);
    }

    public static List<Integer> parentUserAssetTypes() {
        return Arrays.asList(ThirdAssetType.PARENT_FUNDING, ThirdAssetType.PARENT_SPOT, ThirdAssetType.PARENT_MARGIN, ThirdAssetType.PARENT_ISOLATED_MARGIN, ThirdAssetType.PARENT_UCONTRACT, ThirdAssetType.PARENT_COIN_CONTRACT, ThirdAssetType.PARENT_FLEX_EARN, ThirdAssetType.PARENT_LOCKED_EARN).stream().map(ThirdAssetType::getType).collect(Collectors.toList());
    }

    public static List<ThirdAssetType> parentUserAssetTypeEnums() {
        return Arrays.asList(ThirdAssetType.PARENT_FUNDING, ThirdAssetType.PARENT_SPOT, ThirdAssetType.PARENT_MARGIN, ThirdAssetType.PARENT_ISOLATED_MARGIN, ThirdAssetType.PARENT_UCONTRACT, ThirdAssetType.PARENT_COIN_CONTRACT, ThirdAssetType.PARENT_FLEX_EARN, ThirdAssetType.PARENT_LOCKED_EARN);
    }




}
