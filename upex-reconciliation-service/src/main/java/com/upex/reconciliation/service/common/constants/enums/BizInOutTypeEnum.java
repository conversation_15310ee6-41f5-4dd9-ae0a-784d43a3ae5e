package com.upex.reconciliation.service.common.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 业务类型入出
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum BizInOutTypeEnum {
    NORMAL(0, "非入出"),
    IN(1, "入"),
    OUT(2, "出");


    private Integer code;
    private String desc;

    public static BizInOutTypeEnum toEnum(Integer code) {
        for (BizInOutTypeEnum item : BizInOutTypeEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }
}
