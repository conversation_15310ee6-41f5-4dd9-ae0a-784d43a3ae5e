package com.upex.reconciliation.service.business.ruleengine.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程执行type
 */
@Getter
@AllArgsConstructor
public enum ExecuteTypeEnum {
    BILL_FLOW("bill_flow", "账单流水"),
    SCHEDULE("schedule", "定时任务"),
    ;
    private String code;
    private String name;

    public static ExecuteTypeEnum toEnum(String code) {
        for (ExecuteTypeEnum executeTypeEnum : ExecuteTypeEnum.values()) {
            if (executeTypeEnum.getCode().equals(code)) {
                return executeTypeEnum;
            }
        }
        return null;
    }
}
