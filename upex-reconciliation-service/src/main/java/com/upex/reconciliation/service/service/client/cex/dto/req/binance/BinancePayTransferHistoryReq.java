package com.upex.reconciliation.service.service.client.cex.dto.req.binance;

import com.binance.connector.client.common.Pair;
import lombok.Data;

@Data
public class BinancePayTransferHistoryReq extends BinanceApiBaseReq {

    private Long startTime;

    private Long endTime;

    private Integer limit;

    public void setStartTime(Long startTime) {
        queryParams.add(new Pair("startTime", String.valueOf(startTime)));
    }

    public void setEndTime(Long endTime) {
        queryParams.add(new Pair("endTime", String.valueOf(endTime)));
    }

    public void setLimit(Integer limit) {
       queryParams.add(new Pair("limit", String.valueOf(limit)));
    }
}
