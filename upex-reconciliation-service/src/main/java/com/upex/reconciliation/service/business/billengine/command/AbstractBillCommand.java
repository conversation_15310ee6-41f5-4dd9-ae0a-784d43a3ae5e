package com.upex.reconciliation.service.business.billengine.command;


import com.upex.mixcontract.common.framework.command.AbstractCommand;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.model.config.BillCommonConfig;

public abstract class AbstractBillCommand<P, R> extends AbstractCommand<P, R, BillLogicGroup> {
    public AbstractBillCommand(BillLogicGroup logicGroup) {
        super(logicGroup);
    }

    public BillCommonConfig getBillConfig() {
        return logicGroup.getBillConfig();
    }
}
