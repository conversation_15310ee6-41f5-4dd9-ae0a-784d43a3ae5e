package com.upex.reconciliation.service.business.module.impl;

import com.alibaba.fastjson.JSON;
import com.upex.mixcontract.common.framework.AttributeMap;
import com.upex.reconciliation.service.business.AccountAssetsServiceFactory;
import com.upex.reconciliation.service.business.AlarmNotifyService;
import com.upex.reconciliation.service.business.BillCheckService;
import com.upex.reconciliation.service.business.CommonService;
import com.upex.reconciliation.service.business.billengine.BillLogicGroup;
import com.upex.reconciliation.service.business.module.AbstractBillModule;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.common.context.ReconciliationSpringContext;
import com.upex.reconciliation.service.dao.entity.BillCoinUserProperty;
import com.upex.reconciliation.service.model.BillCmdResult;
import com.upex.reconciliation.service.model.BillCmdWrapper;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.dto.UserAssetEventCommandData;
import com.upex.reconciliation.service.utils.DateUtil;
import com.upex.reconciliation.service.utils.MetricsUtil;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import com.upex.ticker.facade.dto.PriceVo;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

import static com.upex.reconciliation.service.common.constants.AlarmTemplateEnum.*;
import static com.upex.reconciliation.service.utils.MetricsUtil.HISTOGRAM_USER_TAKE_COMMAND;

/**
 * 帐务流水处理
 */
@Slf4j
public class BillUserAssetCheckModule extends AbstractBillModule {
    /***命令处理队列***/
    private BlockingQueue<BillCmdWrapper> cmdQueue = new ArrayBlockingQueue<>(10000);
    private AlarmNotifyService alarmNotifyService;
    private BillUserCheckModule billUserCheckModule;
    private CommonService commonService;
    private AccountAssetsServiceFactory accountAssetsServiceFactory;
    private ApolloReconciliationBizConfig apolloBizConfig;

    public BillUserAssetCheckModule(BillLogicGroup logicGroup) {
        super(logicGroup);
    }

    @Override
    public void init(AttributeMap initContext) {
        billUserCheckModule = getModule(BillUserCheckModule.class);
        accountTypeEnum = initContext.get(BillLogicGroup.ACCOUNT_TYPE_ENUM_KEY);
        ReconciliationSpringContext reconciliationSpringContext = initContext.get(BillLogicGroup.ENGINE_SPRING_CONTEXT_KEY);
        accountTypeEnum = initContext.get(BillLogicGroup.ACCOUNT_TYPE_ENUM_KEY);
        apolloBizConfig = ReconciliationApolloConfigUtils.getBillConfigByAccountType(accountTypeEnum.getCode());
        alarmNotifyService = reconciliationSpringContext.getAlarmNotifyService();
        commonService = reconciliationSpringContext.getCommonService();
        accountAssetsServiceFactory = reconciliationSpringContext.getAccountAssetsServiceFactory();
    }

    @Override
    public void offerCommand(BillCmdWrapper cmdWrapper) {
        boolean result = this.cmdQueue.offer(cmdWrapper);
        if (!result) {
            log.error("BillUserAssetCheckModule.offerCommand offer faile data {}", JSON.toJSONString(cmdWrapper));
            alarmNotifyService.alarm(accountTypeEnum.getCode(), USER_ASSET_CHECK_OFFER_COMMAND_ERROR, accountTypeEnum.getCode());
        }
    }

    @Override
    public BillCmdResult takeCommand() {
        BillCmdWrapper cmdWrapper = null;
        try {
            cmdWrapper = this.cmdQueue.poll(1, TimeUnit.SECONDS);
            if (cmdWrapper == null) {
                return null;
            }
            BillCmdWrapper finalCmdWrapper = cmdWrapper;
            return MetricsUtil.histogram(HISTOGRAM_USER_TAKE_COMMAND + accountTypeEnum.getCode(), () -> execCommand(finalCmdWrapper));
        } catch (Exception e) {
            log.error("BillUserAssetCheckModule check failed with error accountType {} data {}", accountTypeEnum.getCode(), cmdWrapper == null ? "" : JSON.toJSONString(cmdWrapper), e);
            alarmNotifyService.alarm(accountTypeEnum.getCode(), FLOW_CHECK_MODULE_TAKE_COMMAND_ERROR, accountTypeEnum.getCode());
        }
        return null;
    }

    private BillCmdResult execCommand(BillCmdWrapper cmdWrapper) {
        UserAssetEventCommandData commandData = (UserAssetEventCommandData) cmdWrapper.getCommandData();
        if (apolloBizConfig.isUserAssetCheckNegativeOpen()) {
            userCheckNegative(commandData);
        }
        return null;
    }

    /**
     * 用户资产负值检测
     *
     * @param commandData
     */
    private void userCheckNegative(UserAssetEventCommandData commandData) {
        Map<Integer, BillCoinUserProperty> userCoinPropertyMap = billUserCheckModule.getUserCoinPropertyMap(commandData.getUserId());
        if (userCoinPropertyMap != null && userCoinPropertyMap.size() > 0) {
            long lastMinute = commandData.getBizTime().getTime() / BillConstants.ONE_MINE_MIL_SEC * BillConstants.ONE_MINE_MIL_SEC;
            Map<Integer, PriceVo> priceVos = commonService.getCoinIdRatesMapCache(lastMinute);
            BillCheckService billCheckService = accountAssetsServiceFactory.getBillCheckService(accountTypeEnum.getCode());
            BigDecimal totalUsdtValue = BigDecimal.ZERO;
            // 计算折U值
            for (Map.Entry<Integer, BillCoinUserProperty> entry : userCoinPropertyMap.entrySet()) {
                Integer coinId = entry.getKey();
                BillCoinUserProperty billCoinUserProperty = entry.getValue();
                PriceVo priceVo = priceVos.get(coinId);
                if (priceVo == null) {
                    throw new RuntimeException("userCheckNegative priceVos is null coinId:" + coinId);
                }
                BigDecimal propSum = billCheckService.getPropSumByUserProperty(billCoinUserProperty);
                BigDecimal usdtValue = propSum.multiply(priceVo.getPrice());
                totalUsdtValue = totalUsdtValue.add(usdtValue);
            }
            if (totalUsdtValue.compareTo(BigDecimal.ZERO) < 0) {
                if (!apolloBizConfig.getNegativeUserWhiteList().contains(commandData.getUserId())) {
                    // 告警打印日志
                    String checkOkTimeStr = DateUtil.date2str(commandData.getBizTime());
                    StringBuilder logStr = new StringBuilder();
                    for (Map.Entry<Integer, BillCoinUserProperty> entry : userCoinPropertyMap.entrySet()) {
                        PriceVo priceVo = priceVos.get(entry.getKey());
                        BigDecimal propSum = billCheckService.getPropSumByUserProperty(entry.getValue());
                        logStr.append("coinId:").append(entry.getKey()).append(",propSum:").append(propSum.toPlainString()).append(",price:").append(priceVo.getPrice()).append(",");
                    }
                    logStr.append("totalUsdtValue:").append(totalUsdtValue);
                    alarmNotifyService.alarm(accountTypeEnum.getCode(), LEVER_CHECK_NEGATIVE_ERROR, accountTypeEnum.getCode(), commandData.getUserId(), checkOkTimeStr, logStr);
                }
            }
        }
    }
}
