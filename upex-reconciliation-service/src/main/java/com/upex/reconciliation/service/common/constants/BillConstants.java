package com.upex.reconciliation.service.common.constants;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.upex.bill.dto.enums.AccountTypeEnum;
import com.upex.bill.dto.enums.AssetsCheckTypeEnum;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020-11-18 16:14
 * @desc
 **/
public class BillConstants {


    public static final Long MONITOR_SCENE_FEE_CHECK = 4L;

    /**
     * 分隔符
     */
    public static final String SEPARATOR = "-";

    public static final String DEFAULT_BILL_BIZ_TYPE = "other";

    /**
     * 1024
     */
    public static final int ONE_THOUSAND_TWENTY_FOUR = 1024;

    public static final String CHECK_CONTINUE_ACCOUNT_TYPE_KEY = "check_continue_account_type_key:";

    public static final Long ONE_MINE_MIL_SEC = 60L * 1000L;

    public static final Double ERROR_LIMIT_RATE = 1.0;

    public static final Long ONE_HOUR_MIL = 60L * 60L;
    public static final Long ONE_HOUR_MIL_SEC = 60L * 60L * 1000L;

    public static final Long HALF_HOUR_MIL_SEC = 30L * 60L * 1000L;

    public static final Long FIVE_MINE_MIL_SEC = 5 * 60L * 1000L;

    public static final Long ONE_DAY_MIL_SEC = 24 * 60 * 60L * 1000L;

    public static final Long TWO_DAY_MIL_SEC = 48 * 60 * 60L * 1000L;
    public static final Long SEVEN_DAY_MIL_SEC = 7 * ONE_DAY_MIL_SEC;

    public static final Long ONE_THOUSAND = 1000L;

    // 消息量最大的业务线的消息产生速率，目前统计下来是10业务线，500tps
    public static final Long MAX_MQ_PRODUCER_SPEED = 500L;

    // 幂等表cache失效时间，单位：minute
    public static final Long IDEMPOTENT_CACHE_EXPIRE_MINUTES = 30L;

    // 十进制取模底数
    public static final Long MODULAR_DECIMAL_MODE = 10L;

    public static final Integer HIGH_MQ_CONSUME_SPEED = 800;
    public static final Integer LOW_MQ_CONSUME_SPEED = 300;


    /**
     * 下划线
     */
    public static final String UNDERSCORE_SEPARATOR = "_";

    /**
     * 默认参数
     */
    public static final String DEFAULT_ASSETS_CHECK_TYPE_PARAM = "default";

    /**
     * 默认参数
     */
    public static final String USER_ASSETS_CHECK_TYPE_PARAM = "user";

    /**
     * 内部对账检测key
     */
    public static final String INTERNAL_ASSETS_CHECK_KEY = AssetsCheckTypeEnum.INTERNAL_TOTAL_ASSETS.getCode() +
            SEPARATOR + DEFAULT_ASSETS_CHECK_TYPE_PARAM;
    /**
     * 默认保留小数位
     */
    public static final int SERVER_DEFAULT_SCALE = 16;



    /**
     * 默认保留小数位
     */
    public static final int SERVER_DEFAULT_SCALE_SIX = 6;

    /**
     * 每次增量查询用户pageSize量
     */
    public static final int INCREASE_USERS_PAGE_SIZE_50 = 50;
    /**
     * USDT币种名
     */
    public static final String COIN_NAME_USDT = "USDT";
    public static final Long UTC_BEGIN_TIME = 0L;

    /**
     * 组装资产检测唯一key
     *
     * @param accountCheckType
     * @param accountCheckParam
     * @return
     */
    public static String buildAssetsCheckUniqKey(String accountCheckType, String accountCheckParam) {
        StringBuffer sb = new StringBuffer();
        sb.append(accountCheckType).append(SEPARATOR).append(accountCheckParam);
        return sb.toString();
    }

    /**
     * （*）号
     */
    public static final String ASTERISK = "*";


    /**
     * 合约计算账户权益的固定价格
     */
    private static final BigDecimal SWAP_EQUITY_CAL_FIX_PRICE = new BigDecimal("10000");

    /**
     * 每页最大条数
     */
    public static final Integer MAX_PAGESIZE = 500;

    /**
     * 计算权益价格map
     */
    public static final Map<AccountTypeEnum, BigDecimal> EQUITY_CAL_FIX_PRICE_MAP = new HashMap<>();

    static {
        EQUITY_CAL_FIX_PRICE_MAP.computeIfAbsent(AccountTypeEnum.SWAP_MAIN, v -> SWAP_EQUITY_CAL_FIX_PRICE);
    }

    /**
     * 数组分段长度
     */
    public static final Integer LIST_PARTITION_SIZE = 2000;

    /**
     * 多线程分组长度
     */
    public static final Integer THREAD_PARTITION_SIZE = 200;


    /**
     * 逗号
     */
    public static final String COMMA = ",";

    /**
     * 冒号
     */
    public static final String COLON = ":";

    /**
     * 竖划线
     */
    public static final String VERTICAL_LINE = "|";

    /**
     * 井号
     */
    public static final String POUND_SIGN = "#";
    /**
     * 空串
     */
    public static final String EMPTY = "";

    /**
     * 圆括号-左
     */
    public static final String LEFT_PARENTHESES = "(";
    /**
     * 圆括号-右
     */
    public static final String RIGHT_PARENTHESES = ")";

    /**
     * 方括号-左
     */
    public static final String LEFT_BRACKETS = "[";
    /**
     * 方括号-右
     */
    public static final String RIGHT_BRACKETS = "]";
    /**
     * 方括号
     */
    public static final String BRACKETS = "[]";
    /***空json对象***/
    public static final String EMPTY_JSON = "{}";
    /**
     * 大括号-左
     */
    public static final String LEFT_BRACES = "{";
    /**
     * 大括号-右
     */
    public static final String RIGHT_BRACES = "}";


    /**
     * 资产证明精度
     */
    public static final Integer ASSETS_PROOF_SCALE = 8;

    /**
     * 资产查询页数
     */
    public static final Integer ASSETS_QUERY_PAGE_SIZE = 10000;

    /**
     * 资产插入页数
     */
    public static final Integer ASSETS_INSERT_PAGE_SIZE = 2000;

    /**
     * 流水用户执行时间间隔
     */
    public static final Long FLOW_CHANGE_USER_TIME_INTERVAL = 5 * 60 * 1000L;

    public static final Long TIME_SLICE_INTERVAL = 60 * 1000L;


    /**
     * Number of milliseconds in a standard second.
     *
     * @since 2.1
     */
    public static final long MILLIS_PER_SECOND = 1000;
    /**
     * Number of milliseconds in a standard minute.
     *
     * @since 2.1
     */
    public static final long MILLIS_PER_MINUTE = 60 * MILLIS_PER_SECOND;

    /**
     * 标准十分钟的毫秒值
     */
    public static final Long TEN_MILLIS_PER_MINUTE = 10 * MILLIS_PER_MINUTE;
    /**
     * 用户同步结束时间
     */
    public static final Long BILL_SNAPSHOT_USER_TIME_INTERVAL = 60 * 60 * 1000L;
    /**
     * Long类型数字0
     */
    public static final Long LONG_ZERO = 0L;


    public static final String ZERO_STR = "0";

    /**
     * 负一
     */
    public static final int NEG_ONE = -1;
    public static final int ZERO = 0;
    public static final int ONE = 1;

    public static final String ONE_STRING = "1";

    public static final int TWO = 2;
    public static final int THREE = 3;
    public static final int FOUR = 4;
    public static final int FIVE = 5;
    public static final int SEVEN = 7;
    public static final int EIGHT = 8;
    public static final int TEN = 10;
    public static final int TWELVE = 12;
    public static final int SIXTEEN = 16;

    public static final int THIRTY = 30;
    public static final int SEVENTY_FOUR = 74;
    /**
     * 一百
     */
    public static final int ONE_HUNDRED = 100;

    /**
     * 用户加密平台对注册服务名（专用）
     */
    public static final String UPEX_BILL_JOB_NAME = "upex-bill-job";

    public static final String DEFAULT = "default";

    public static final String WITH_DRAW_CHECK_ALARM_TITLE = "对账禁止提币报警";

    /**
     * 老的审计id集合
     */
    public static final List<String> oldAuditIds = Lists.newArrayList(
            "Au20230506",
            "Au20230403",
            "Au20230306",
            "Au20230131",
            "Au20221230",
            "Au20221202"
    );

    /**
     * 老的资金对账快照时间
     * 2023-08-01 00:00:00，之前的都在老表查询，之后的走分表逻辑
     */
    public static final Date oldBillCapitalTime = new Date(1690819200000L);

    /**
     * 老的动账分表快照时间
     * 2023-09-01 00:00:00，之前的都在老表查询，之后的走分表逻辑
     */
    public static final Date oldTBillContractProfitTransferTime = new Date(1693497600000L);

    public static final String MC_CONTRACT_SUCCESS_CODE = "00000";
    public static final Integer INSERT_NUMBER = 1000;
    public static final Integer ALARM_QUEUE_SIZE = 9000;

    /**
     * 对比标识
     */
    public static final String DIFF_BROKER_BILL_FLAG = "BROKER_BILL";
    public static final String DIFF_BILL_BROKER_FLAG = "BILL_BROKER";

    public static final Set<String> CHECK_PERMISSION_ERROR_CODE_SET = Sets.newHashSet("22100", "22103", "23140");
}
