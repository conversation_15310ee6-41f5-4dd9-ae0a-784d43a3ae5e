package com.upex.reconciliation.service.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.RateLimiter;
import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import com.upex.reconciliation.service.common.constants.BillConstants;
import com.upex.reconciliation.service.model.config.ApolloReconciliationBizConfig;
import com.upex.reconciliation.service.model.config.ReconKafkaOpsConfig;
import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import com.upex.reconciliation.service.model.dto.SyncBillUserDTO;
import org.junit.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class RateLimitCalculationUtilsTest {

    /**
     * 流速控制单元测试case
     *
     * 使用生产的apollo配置替换掉apollo的json
     */
    @Test
    public void test111() {
        ApolloReconciliationBizConfig apolloBizConfig = JSONObject.parseObject(" {\n" +
                "        \"accountType\": 10,\n" +
                "        \"isOpen\": true,\n" +
                "        \"mqConsumerRateLimit\": 60,\n" +
                "        \"mqConsumerRateCalFactor\": 30,\n" +
                "        \"kafkaBatchCount\": 50,\n" +
                "        \"kafkaConsumerConstructOpen\": true,\n" +
                "        \"pullKafkaMessageBackTime\": 600000,\n" +
                "        \"kafkaConsumeSkipBusinessLogic\": false,\n" +
                "        \"billCheckBeforeBizTimeThreshold\": 1800000,\n" +
                "        \"logLevelCode\": 2,\n" +
                "        \"startTimeSliceMapLimitSize\": 350,\n" +
                "        \"initPartitionSize\": 50,\n" +
                "        \"mqProducerSpeed\": 350,\n" +
                "        \"kafkaPartitionNum\": 6,\n" +
                "        \"resetKafkaOffsetOpen\": true,\n" +
                "        \"mergeTimeSliceSize\": 5,\n" +
                "        \"errorBillMapLimitSize\": 15000,\n" +
                "        \"toLoadMemoryData\": true,\n" +
                "        \"timeSliceCheckOpen\": true,\n" +
                "        \"inOutCheckOpen\": true,\n" +
                "        \"topic\": \"recon_consumer_bill_type_10_prod\",\n" +
                "        \"groupId\": \"upex-reconciliation-prod\",\n" +
                "        \"accountParam\": \"default\",\n" +
                "        \"timeSliceSize\": 60,\n" +
                "        \"coinComparisonToleranceList\": [\n" +
                "            {\n" +
                "                \"coinId\": 1,\n" +
                "                \"tolerance\": 0.0000002\n" +
                "            },\n" +
                "            {\n" +
                "                \"coinId\": 3,\n" +
                "                \"tolerance\": 0.2\n" +
                "            }\n" +
                "        ],\n" +
                "        \"transferIn\": [\n" +
                "            \"other\",\n" +
                "            \"2_10_1\",\n" +
                "            \"2_15_*\",\n" +
                "            \"2_1_1_3\",\n" +
                "            \"2_1_2_3\",\n" +
                "            \"2_1_3_3\",\n" +
                "            \"2_25_2\",\n" +
                "            \"10_1_SUCCESS\",\n" +
                "            \"2_5_10\",\n" +
                "            \"4_3\",\n" +
                "            \"1\",\n" +
                "            \"5\",\n" +
                "            \"7\",\n" +
                "            \"11\",\n" +
                "            \"28\",\n" +
                "            \"56\",\n" +
                "            \"29\",\n" +
                "            \"32\",\n" +
                "            \"86\",\n" +
                "            \"87\",\n" +
                "            \"96\",\n" +
                "            \"97\",\n" +
                "            \"103\",\n" +
                "            \"105\",\n" +
                "            \"139\",\n" +
                "            \"244\",\n" +
                "            \"246\",\n" +
                "            \"248\",\n" +
                "            \"308\",\n" +
                "            \"326\"\n" +
                "        ],\n" +
                "        \"transferOut\": [\n" +
                "            \"2_14_*\",\n" +
                "            \"2_24_2\",\n" +
                "            \"2_2_1_7\",\n" +
                "            \"2_2_2_3\",\n" +
                "            \"2_2_3_3\",\n" +
                "            \"10_2_SUCCESS\",\n" +
                "            \"2\",\n" +
                "            \"6\",\n" +
                "            \"31\",\n" +
                "            \"30\",\n" +
                "            \"85\",\n" +
                "            \"88\",\n" +
                "            \"100\",\n" +
                "            \"102\",\n" +
                "            \"104\",\n" +
                "            \"243\",\n" +
                "            \"245\",\n" +
                "            \"247\",\n" +
                "            \"325\"\n" +
                "        ],\n" +
                "        \"checkFlowInOutBizTypeOpen\": true,\n" +
                "        \"excludeTransferIn\": [\n" +
                "            \"other\",\n" +
                "            \"7\",\n" +
                "            \"105\"\n" +
                "        ],\n" +
                "        \"excludeTransferOut\": [\n" +
                "            \"105\"\n" +
                "        ]\n" +
                "    }", ApolloReconciliationBizConfig.class);


        // 取生产apollo
        ReconKafkaOpsConfig apolloOpsConfig = JSONObject.parseObject(" {\n" +
                "        \"accountType\": 10,\n" +
                "        \"isOpen\": true,\n" +
                "        \"mqConsumerRateLimit\": 60,\n" +
                "        \"mqConsumerRateCalFactor\": 30,\n" +
                "        \"kafkaBatchCount\": 50,\n" +
                "        \"kafkaConsumerConstructOpen\": true,\n" +
                "        \"pullKafkaMessageBackTime\": 600000,\n" +
                "        \"kafkaConsumeSkipBusinessLogic\": false,\n" +
                "        \"billCheckBeforeBizTimeThreshold\": 1800000,\n" +
                "        \"logLevelCode\": 2,\n" +
                "        \"startTimeSliceMapLimitSize\": 350,\n" +
                "        \"initPartitionSize\": 50,\n" +
                "        \"mqProducerSpeed\": 350,\n" +
                "        \"kafkaPartitionNum\": 6,\n" +
                "        \"resetKafkaOffsetOpen\": true,\n" +
                "        \"mergeTimeSliceSize\": 5,\n" +
                "        \"errorBillMapLimitSize\": 15000,\n" +
                "        \"toLoadMemoryData\": true,\n" +
                "        \"timeSliceCheckOpen\": true,\n" +
                "        \"inOutCheckOpen\": true,\n" +
                "        \"topic\": \"recon_consumer_bill_type_10_prod\",\n" +
                "        \"groupId\": \"upex-reconciliation-prod\",\n" +
                "        \"accountParam\": \"default\",\n" +
                "        \"timeSliceSize\": 60,\n" +
                "        \"coinComparisonToleranceList\": [\n" +
                "            {\n" +
                "                \"coinId\": 1,\n" +
                "                \"tolerance\": 0.0000002\n" +
                "            },\n" +
                "            {\n" +
                "                \"coinId\": 3,\n" +
                "                \"tolerance\": 0.2\n" +
                "            }\n" +
                "        ],\n" +
                "        \"transferIn\": [\n" +
                "            \"other\",\n" +
                "            \"2_10_1\",\n" +
                "            \"2_15_*\",\n" +
                "            \"2_1_1_3\",\n" +
                "            \"2_1_2_3\",\n" +
                "            \"2_1_3_3\",\n" +
                "            \"2_25_2\",\n" +
                "            \"10_1_SUCCESS\",\n" +
                "            \"2_5_10\",\n" +
                "            \"4_3\",\n" +
                "            \"1\",\n" +
                "            \"5\",\n" +
                "            \"7\",\n" +
                "            \"11\",\n" +
                "            \"28\",\n" +
                "            \"56\",\n" +
                "            \"29\",\n" +
                "            \"32\",\n" +
                "            \"86\",\n" +
                "            \"87\",\n" +
                "            \"96\",\n" +
                "            \"97\",\n" +
                "            \"103\",\n" +
                "            \"105\",\n" +
                "            \"139\",\n" +
                "            \"244\",\n" +
                "            \"246\",\n" +
                "            \"248\",\n" +
                "            \"308\",\n" +
                "            \"326\"\n" +
                "        ],\n" +
                "        \"transferOut\": [\n" +
                "            \"2_14_*\",\n" +
                "            \"2_24_2\",\n" +
                "            \"2_2_1_7\",\n" +
                "            \"2_2_2_3\",\n" +
                "            \"2_2_3_3\",\n" +
                "            \"10_2_SUCCESS\",\n" +
                "            \"2\",\n" +
                "            \"6\",\n" +
                "            \"31\",\n" +
                "            \"30\",\n" +
                "            \"85\",\n" +
                "            \"88\",\n" +
                "            \"100\",\n" +
                "            \"102\",\n" +
                "            \"104\",\n" +
                "            \"243\",\n" +
                "            \"245\",\n" +
                "            \"247\",\n" +
                "            \"325\"\n" +
                "        ],\n" +
                "        \"checkFlowInOutBizTypeOpen\": true,\n" +
                "        \"excludeTransferIn\": [\n" +
                "            \"other\",\n" +
                "            \"7\",\n" +
                "            \"105\"\n" +
                "        ],\n" +
                "        \"excludeTransferOut\": [\n" +
                "            \"105\"\n" +
                "        ]\n" +
                "    }", ReconKafkaOpsConfig.class);

        // 非阻塞，正常对账场景，该对象为空对象
        CommonBillChangeData minErrorMapData = new CommonBillChangeData();
        Map<Integer, Long> latestPartitionBizTime = new HashMap<>();
        latestPartitionBizTime.put(0, DateUtil.getDefaultDateLong("2024-03-17 21:55:58"));
        latestPartitionBizTime.put(1, DateUtil.getDefaultDateLong("2024-03-17 22:07:45"));
        latestPartitionBizTime.put(2, DateUtil.getDefaultDateLong("2024-03-17 21:34:52"));
        latestPartitionBizTime.put(3, DateUtil.getDefaultDateLong("2024-03-17 22:03:59"));
        latestPartitionBizTime.put(4, DateUtil.getDefaultDateLong("2024-03-17 22:09:34"));
        latestPartitionBizTime.put(5, DateUtil.getDefaultDateLong("2024-03-17 21:43:41"));
        long minTime = RateLimitCalculationUtils.getMinPartitionBizTime(latestPartitionBizTime);

        Map<Integer, Long> partitionBizTimeGapMap = new HashMap<>();
        Map<Integer, RateLimiter> partitionMap = new HashMap<>();
        // 当前时间 - partition bizTime= 时间差
        latestPartitionBizTime.forEach((k, v) -> {
            long timeGapMinute = Math.max((v - minTime), BillConstants.ONE_MINE_MIL_SEC) / BillConstants.ONE_MINE_MIL_SEC;
            partitionBizTimeGapMap.put(k, timeGapMinute);
            RateLimiter rateLimiter = RateLimiter.create(apolloOpsConfig.getMqConsumerRateLimit());
            partitionMap.put(k, rateLimiter);
        });

        RateLimitCalculationUtils.checkPartitionSpeedAndRateLimit(apolloBizConfig,apolloOpsConfig, 1000L, minErrorMapData, 500, 100, new Date(), latestPartitionBizTime, partitionBizTimeGapMap, partitionMap);
        System.out.println("end ");
    }

    @Test
    public void testCurentRate() {
        com.alibaba.google.common.util.concurrent.RateLimiter rateLimiter = com.alibaba.google.common.util.concurrent.RateLimiter.create(600);
        for(int i=0;i<1000;i++){
            rateLimiter.acquire(601);
            System.out.println("当前qps："+rateLimiter.getRate()+" 当前index："+i);
        }
    }





}
