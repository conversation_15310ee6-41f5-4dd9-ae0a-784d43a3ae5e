package com.upex.reconciliation.service.utils;

import com.upex.reconciliation.service.model.dto.CommonBillChangeData;
import org.junit.Test;

import java.math.BigDecimal;

public class CheckBalanceTest {

    @Test
    public void testCheckBalance() {
        CommonBillChangeData commonBillChangeData1=new CommonBillChangeData();
        commonBillChangeData1.setChangeProp1(BigDecimal.valueOf(-0.0001000000000000));
        commonBillChangeData1.setChangeProp2(BigDecimal.valueOf(0.0001000000000000));
        commonBillChangeData1.setChangeProp3(BigDecimal.ZERO);
        commonBillChangeData1.setProp1(BigDecimal.valueOf(1100003962.6380311870000000).add(commonBillChangeData1.getChangeProp1()));
        commonBillChangeData1.setProp2(BigDecimal.ZERO.add(commonBillChangeData1.getChangeProp2()));
        commonBillChangeData1.setProp3(BigDecimal.ZERO);

        CommonBillChangeData commonBillChangeData2=new CommonBillChangeData();
        commonBillChangeData2.setChangeProp1(BigDecimal.ZERO);
        commonBillChangeData2.setChangeProp2(BigDecimal.valueOf(-0.0001000000000000));
        commonBillChangeData2.setChangeProp3(BigDecimal.ZERO);
        commonBillChangeData2.setProp1(BigDecimal.valueOf(1100003962.6379311870000000).add(commonBillChangeData2.getChangeProp1()));
        commonBillChangeData2.setProp2(BigDecimal.ZERO.add(commonBillChangeData2.getChangeProp2()));
        commonBillChangeData2.setProp3(BigDecimal.ZERO);

        CommonBillChangeData commonBillChangeData3=new CommonBillChangeData();
        commonBillChangeData3.setChangeProp1(BigDecimal.valueOf(6.0006000000000000));
        commonBillChangeData3.setChangeProp2(BigDecimal.ZERO);
        commonBillChangeData3.setChangeProp3(BigDecimal.ZERO);
        commonBillChangeData3.setProp1(BigDecimal.valueOf(111764422051.2237376190517740).add(commonBillChangeData3.getChangeProp3()));
        commonBillChangeData3.setProp2(BigDecimal.valueOf(3153.6421140000000000).add(commonBillChangeData3.getChangeProp2()));
        commonBillChangeData3.setProp3(BigDecimal.ZERO);

        BigDecimal num1=BigDecimal.valueOf(4257.4407051570000000);
        BigDecimal num2=BigDecimal.valueOf(9.9910989000000000);
        BigDecimal num3=BigDecimal.valueOf(0.0001000000000000);
        BigDecimal num4=BigDecimal.valueOf(10.0011000000000000);
        System.out.println(num1.add(num2).add(num4));

    }
}
