package com.upex.reconciliation.service.utils;


import com.upex.reconciliation.facade.enums.AccountTypeEnum;
import org.junit.Test;

import java.math.BigInteger;

public class BinanryTest {


    @Test
    public void test() {
        //10000 & 100
        //存在状态位 (this.status & bitmark) !=0 ,如10000 & 10000 = 10000 假如没有设置状态位 100 & 10000 =0
        System.out.println(1 << 4 & 1<< 2);//==0
        System.out.println(1 << 4 & 1<< 4);//>0
        BigInteger num=BigInteger.ONE.shiftLeft(126);
        System.out.println(num.and(num));
        System.out.println(num);
        System.out.println(AccountTypeEnum.SPOT.getCode());

    }
    @Test
    public void test2BigInteger(){
        int initnum=1;
        int spotstatus=1 << 10;//spot status
        int p_spotstatus= 1 << 11;//p_spot status;
        int brokerstatus= 1 << 20;//level status;
        System.out.println(initnum);
        System.out.println("******setspotstatus******");
        //设置spotstatus
        initnum=initnum | spotstatus;
        System.out.println(initnum);
        System.out.println("******setp_spotstatus******");
        //设置p_spotstatus
        initnum=initnum | p_spotstatus;
        System.out.println(initnum);
        System.out.println("******checkp_spotstatus******");
        System.out.println(initnum & p_spotstatus);
        System.out.println("******checkspotstatus******");
        System.out.println(initnum & spotstatus);
        System.out.println("******brokerstatus******");
        System.out.println(initnum & brokerstatus);

    }
    @Test
    public void setStatus(){
        BigInteger initStatus=BigInteger.ONE;
        System.out.println("curStatus:"+initStatus);
        initStatus=setStatusByAccountType(initStatus,AccountTypeEnum.SPOT.getCode());
        initStatus=setStatusByAccountType(initStatus,AccountTypeEnum.P_SPOT.getCode());
//        initStatus=setStatusByAccountType(initStatus,AccountTypeEnum.DEMO_USDC_MIX_CONTRACT_BL.getCode());
//        initStatus=setStatusByAccountType(initStatus,AccountTypeEnum.IN_ALL.getCode());
        System.out.println("ifSetLevelFullStatus:"+checkIfSetStatus(initStatus,AccountTypeEnum.P_SPOT.getCode()));
        System.out.println("ifSetSpotStatus:"+checkIfSetStatus(initStatus,AccountTypeEnum.SPOT.getCode()));
//        System.out.println("ifSetBrokerStatus:"+checkIfSetStatus(initStatus,AccountTypeEnum.BROKER.getCode()));
//        System.out.println("ifSetUsdMixStatus:"+checkIfSetStatus(initStatus,AccountTypeEnum.DEMO_USDC_MIX_CONTRACT_BL.getCode()));
//        System.out.println("ifSetInAllStatus:"+checkIfSetStatus(initStatus,AccountTypeEnum.IN_ALL.getCode()));
        initStatus=disableStatus(initStatus,AccountTypeEnum.P_SPOT);
//        initStatus=disableStatus(initStatus,AccountTypeEnum.SPOT);
        System.out.println("ifSetLevelFullStatus:"+checkIfSetStatus(initStatus,AccountTypeEnum.SPOT.getCode()));

        System.out.println("ifSetSpotStatus:"+checkIfSetStatus(initStatus,AccountTypeEnum.P_SPOT.getCode()));

    }

    public boolean checkIfSetStatus(BigInteger curStatus,Byte accountType){
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        assert accountTypeEnum != null;
        return curStatus.and(BigInteger.ONE.shiftLeft(accountTypeEnum.getCode())).compareTo(BigInteger.ZERO)>0;
    }

    public BigInteger setStatusByAccountType(BigInteger curStatus,Byte accountType) {
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.toEnum(accountType);
        assert accountTypeEnum != null;
        curStatus=(BigInteger.ONE.shiftLeft(accountTypeEnum.getCode())).or(curStatus);
        System.out.println("curStatus:"+curStatus);
        return curStatus;
    }

    public  BigInteger disableStatus(BigInteger curStatus, AccountTypeEnum accountTypeEnum) {
        //curStatus & ~type.getBitMask();
        BigInteger originStatus=BigInteger.ONE.shiftLeft(accountTypeEnum.getCode());
        int bitLength=originStatus.bitLength();
        BigInteger mask=BigInteger.ONE.shiftLeft(bitLength).subtract(BigInteger.ONE);
        System.out.println("mask:"+mask.toString(2)+" xormask:"+(originStatus.xor(mask).and(mask)).toString(2));
        curStatus = (originStatus).xor(mask).and(mask).add(curStatus);
       return curStatus;
    }

    @Test
    public void reserverStatus() {
        BigInteger num = BigInteger.ONE.shiftLeft(10);
        System.out.println(num.toString(2));
        int bits = 10;
        BigInteger mask = BigInteger.ONE.shiftLeft(bits).subtract(BigInteger.ONE);
        BigInteger inverted = num.xor(mask).and(mask);
        System.out.println(inverted.toString(2));
    }

    @Test
    public void setStatusByNum() {
        BigInteger initStatus = BigInteger.ONE;
        System.out.println("curStatus:" + initStatus);
        initStatus = setStatusByNum(initStatus, 126);
        System.out.println("长度:"+initStatus.bitLength());
        System.out.println("字符串:"+initStatus.toString()+" 字符串长度："+initStatus.toString().length());
        initStatus = setStatusByNum(initStatus, 256);
        System.out.println("字符串:"+initStatus.toString()+" 字符串长度："+initStatus.toString().length());
        initStatus = setStatusByNum(initStatus, 500);
        System.out.println("字符串:"+initStatus.toString()+" 字符串长度："+initStatus.toString().length());
        initStatus = setStatusByNum(initStatus, 900);
        System.out.println("字符串:"+initStatus.toString()+" 字符串长度："+initStatus.toString().length());
        System.out.println("ifSet999Status"+checkIfSetStatus(initStatus,1999));
        System.out.println("ifSet998Status"+checkIfSetStatus(initStatus,998));
        System.out.println("ifSet300Status"+checkIfSetStatus(initStatus,300));
        System.out.println("ifSet500Status"+checkIfSetStatus(initStatus,500));
    }

    public boolean checkIfSetStatus(BigInteger curStatus,Integer num){
        return curStatus.and(BigInteger.ONE.shiftLeft(num)).compareTo(BigInteger.ZERO)>0;
    }

    public BigInteger setStatusByNum(BigInteger curStatus, Integer num) {
        curStatus=(BigInteger.ONE.shiftLeft(num)).or(curStatus);
        System.out.println("curStatus:"+curStatus);
        return curStatus;
    }

    @Test
    public void test2() {

        BigInteger initStatus = BigInteger.ZERO;
        System.out.println(initStatus);
        System.out.println("ifSetUnknow"+checkIfSetStatus(initStatus, AccountTypeEnum.UNKNOWN.getCode()));
        System.out.println("ifSetP_SPOT"+checkIfSetStatus(initStatus, AccountTypeEnum.P_SPOT.getCode()));
//        initStatus=setStatusByAccountType(initStatus,AccountTypeEnum.UNKNOWN.getCode());
        initStatus=setStatusByAccountType(initStatus,AccountTypeEnum.P_SPOT.getCode());
        initStatus=setStatusByAccountType(initStatus,AccountTypeEnum.LEVER.getCode());
        initStatus=setStatusByAccountType(initStatus,AccountTypeEnum.LEVER_FULL.getCode());
        System.out.println("ifSetUnknow"+checkIfSetStatus(initStatus, AccountTypeEnum.UNKNOWN.getCode()));
        System.out.println("ifSetP_SPOT"+checkIfSetStatus(initStatus, AccountTypeEnum.P_SPOT.getCode()));
        System.out.println("ifSetLEVELFULL"+checkIfSetStatus(initStatus, AccountTypeEnum.LEVER_FULL.getCode()));
        System.out.println("ifSetSWAP"+checkIfSetStatus(initStatus, AccountTypeEnum.SWAP_MARGIN.getCode()));



    }
}
