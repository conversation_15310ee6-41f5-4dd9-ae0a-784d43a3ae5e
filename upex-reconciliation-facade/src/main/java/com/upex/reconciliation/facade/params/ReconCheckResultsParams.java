package com.upex.reconciliation.facade.params;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 查询对账结果参数类
 */
@NoArgsConstructor
@Data
public class ReconCheckResultsParams implements Serializable {

    private static final long serialVersionUID = 7641475217308486802L;
    /**
     * 请求用户id
     */
    private Long userId;
    /**
     * 请求时间
     */
    private Long requestDate;
    /**
     * 订单号
     */
    private Long orderId;
    /**
     * 金额（目前只有现货后台页面提币会传，其他业务需要用的话，请对接好再用）
     */
    private BigDecimal amount;
    /**
     * 币种（目前只有现货后台页面提币会传，其他业务需要用的话，请对接好再用）
     */
    private Integer coinId;
    /**
     * 流水码
     */
    private String recordCode;
    /**
     * 业务来源
     */
    private String businessSource;

    /**
     * 请求链路traceId，非业务属性
     */
    private String traceId;
}
