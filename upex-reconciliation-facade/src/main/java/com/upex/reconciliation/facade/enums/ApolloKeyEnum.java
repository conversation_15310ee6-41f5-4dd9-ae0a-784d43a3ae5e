package com.upex.reconciliation.facade.enums;

/**
 * apollo配置key枚举
 *
 * <AUTHOR>
 * @date 2022/12/8 16:44
 */
public enum ApolloKeyEnum {

    /**
     * 业务对账配置
     */
    RECONCILIATION_BIZ_CONFIG("reconciliation.biz.config", "业务对账配置"),

    RECONCILIATION_OPS_CONFIG("reconciliation.ops.config", "业务运维配置"),

    ALARM_NOTIFY_CONFIG("alarm.notify.config", "业务告警通知配置"),
    GLOBAL_BILL_CONFIG("global.bill.config", "系统总入出账配置"),
    /**
     * 盈亏动账配置
     */
    PROFIT_TRANSFER_CONFIG("profit.transfer.config", "盈亏动账配置"),

    /**
     * 财务收入对账配置
     */
    INCOME_CHECK_CONFIG("income.check.config", "财务收入对账配置"),

    /**
     * fee对账配置
     */
    FEE_CHECK_CONFIG("fee.check.config", "fee对账配置"),


    /**
     * commission 对账配置
     */
    COMMISSION_CHECK_CONFIG("commission.check.config", "commission 对账配置"),

    /**
     * 告警模板的入参配置
     */
    ALARM_NOTIFY_TEMPLATE_CONFIG("alarm.notify.template.apollo.config", "告警模板的入参配置"),
    /**
     * Redis集群配置
     */
    BILL_REDIS_CONFIG("bill.redis.config", "Redis配置"),
    RECON_REDIS_CONFIG("recon.redis.config", "对账Redis配置 目前主要存放实时盈利检测"),


    MONITOR_SCENE_CONFIG("monitor.scene.config", "多纬度资金监控配置"),
    KAFKA_CONSUMER_CONFIG("kafka.consumer.config", "kafka消费者配置"),


    /**
     * 资产统计定时任务
     */
    ASSET_STATISTICS_CONFIG("asset.statistics.config", "资产统计定时任务"),

    /**
     * 系统用户集合配置(未知)
     */
    BILL_SYSUSERIDS_CONFIG("bill.sysUserIds.config", "系统用户集合配置(未知)"),

    BILL_BASE_ASSET_SNAPSHOT_APOLLO_CONFIG("bill.base.asset.snapshot.apollo.config", "对账基础资产快照配置"),

    COMMON_SERVICE_APOLLO_CONFIG("common.service.apollo.config", "公共服务apollo配置"),

    UPEX_GATEWAY_CONFIG("upex.gateway.config", "网关配置"),

    CAPITAL_ORDER_APOLLO_CONFIG("capital.order.apollo.config", "充提订单配置"),

    RULE_ENGINE_PROCESS_CONFIG("bill.rule.engine.process.config", "规则引擎配置"),
    BILL_DELETE_CONFIG("bill.delete.config", "删除历史对账数据"),

    /**
     * 延迟重试队列配置
     */
    DELAY_RETRY_CONFIG("delay.retry.config", "延迟重试队列配置"),
    BILL_FLOW_EXPIRE_CONFIG("bill.flow.expire.config", "流水过期时间配置"),

    PROFIT_BAK_CHECK_CONFIG("profit.bakcheck.config", "盈亏对账回测配置"),
    RECON_TABLE_ROUTE_CONFIG("recon.table.route.config", "对账表路由配置"),
    RECON_ORDER_CONFIG("recon.order.config", "订单对账配置"),
    BILL_ORDER_CONFIG("bill.order.config", "订单对账配置"),
    THIRD_CEX_ASSET_CONFIG("third.cex.asset.config","外部交易所资产配置"),
    UPEX_PROXY_CONFIG("upex.proxy.config", "网络代理配置"),;

    ;
    private String key;
    private String desc;

    ApolloKeyEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
}
