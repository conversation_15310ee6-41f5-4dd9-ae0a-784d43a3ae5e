package com.upex.reconciliation.facade.params;

import lombok.Data;

import javax.validation.constraints.NotNull;


@Data
public class FacadeDepositeAddressReq {

    /**
     * 交易所类型（1: Binance, 2: OKX）
     */
    @NotNull(message = "ILLEGAL_CEXTYPE")
    private Integer cexType;

    @NotNull(message = "USERID_CANNOT_BENULL")
    private String cexUserId;
    @NotNull(message = "COINNAME_CANNOT_BENULL")
    private String coinName;
}
