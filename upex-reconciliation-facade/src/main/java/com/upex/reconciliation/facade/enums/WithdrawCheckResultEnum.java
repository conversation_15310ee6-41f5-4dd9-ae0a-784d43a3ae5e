package com.upex.reconciliation.facade.enums;

public enum WithdrawCheckResultEnum {



    RECONCILIATION_OVERALL_OFF(1001, "RECONCILIATION_OVERALL_OFF", "对账侧整体开关关闭，禁止提币",1),

    RECONCILIATION_USER_NOT_EXIST(1002, "RECONCILIATION_USER_NOT_EXIST", "对账侧，用户不存在，禁止提币",1),

    RECONCILIATION_CHECK_DELAY_FAILED(1003, "RECONCILIATION_CHECK_DELAY_FAILED", "对账侧，存在延迟入账，禁止提币",1),

    RECONCILIATION_CHECK_PROFIT_FAILED(1004, "RECONCILIATION_CHECK_PROFIT_FAILED", "对账侧，用户盈利检测异常，禁止提币",1),

    RECONCILIATION_CHECK_BUSINESS_TYPE_CONFIG_FAIL(1005, "RECONCILIATION_CHECK_BUSINESS_TYPE_CONFIG_FAIL", "对账侧，业务线对账配置为空，禁止提币",2),

    RECONCILIATION_CHECK_NEGATIVE_FAIL(1006, "RECONCILIATION_CHECK_NEGATIVE_FAIL", "对账侧，用户实时负资产校验不通过，禁止提币",1),

    RECONCILIATION_CHECK_CAPITAL_FAIL(1007, "RECONCILIATION_CHECK_CAPITAL_FAIL", "对账侧，资金对账延迟，禁止提币",1),

    RECONCILIATION_CHECK_BACK_CALCULATION_FAIL(1008, "RECONCILIATION_CHECK_BACK_CALCULATION_FAIL", "对账侧，反算校验不通过，禁止提币",1),


    RECONCILIATION_CHECK_BUSINESS_TYPE_TIME_FAIL(1009, "RECONCILIATION_CHECK_BUSINESS_TYPE_TIME_FAIL", "对账侧，业务线对账时间延迟过大，禁止提币",1),

    RECONCILIATION_CHECK_IN_ALL_TIME_FAIL(1010, "RECONCILIATION_CHECK_IN_ALL_TIME_FAIL", "对账侧，总账对账时间延迟过大，禁止提币",1),

    RECONCILIATION_CHECK_IN_ALL_CONFIG_FAIL(1011, "RECONCILIATION_CHECK_IN_ALL_CONFIG_FAIL", "对账侧，总账对账配置为空，禁止提币",1),

    RECONCILIATION_CHECK_CAPITAL_CONFIG_FAIL(1012, "RECONCILIATION_CHECK_CAPITAL_CONFIG_FAIL", "对账侧，资金对账配置为空，禁止提币",2),


    RECONCILIATION_CHECK_CAPITAL_RESULT_FAIL(1013, "RECONCILIATION_CHECK_CAPITAL_RESULT_FAIL", "对账侧，资金对账存在差额，禁止提币",1),

    RECONCILIATION_CHECK_LAST_CAPITAL_CONFIG_FAIL(1014, "RECONCILIATION_CHECK_LAST_CAPITAL_CONFIG_FAIL", "对账侧，最后一次完成资金对账的配置为空，禁止提币",1),


    RECONCILIATION_CHECK_CAPITAL_BILL_FORBID_FAIL(1015, "RECONCILIATION_CHECK_CAPITAL_BILL_FORBID_FAIL", "对账侧，资金对账差额 >= 资金对账阻断提币值，禁止提币",1),

    RECONCILIATION_USER_ILLEGAL_MODIFICATION_BILL_DATA(1016, "RECONCILIATION_USER_ILLEGAL_MODIFICATION_BILL_DATA", "对账侧， 账单数据更新或删除，禁止提币",2),

    RECONCILIATION_HOSTING_BALANCE_CONFIG_NULL(1017, "RECONCILIATION_HOSTING_BALANCE_CONFIG_NULL", "对账侧， 资金托管对账配置为空，禁止用户提币",1),


    RECONCILIATION_HOSTING_BALANCE_FAIL(1018, "RECONCILIATION_HOSTING_BALANCE_TIMEOUT", "对账侧， 资金托管资金差异超过阈值，禁止用户提币",1),



    RECONCILIATION_USER_IN_BLACKLIST(1019, "RECONCILIATION_USER_IN_BLACKLIST", "对账侧，黑名单用户禁止提币",2),
    RECONCILIATION_ORDER_ERROR(1020, "RECONCILIATION_ORDER_ERROR", "对账侧，订单查询异常，禁止提币",1),


    USER_CHECK_DEMO_FAILED(2001, "USER_CHECK_DEMO_FAILED", "用户侧，demo账户禁止提币",2),


    USER_CHECK_FAILED(2002, "USER_CHECK_FAILED", "用户侧，用户检测不通过，禁止提币",2),

    RECON_REJECT_DIRECT(2003, "RECON_REJECT_DIRECT", "对账侧,对账拒绝，禁止提币", 3),

    RECON_TRANSACTION_LIMIT_EXCEEDED(2004, "RECON_TRANSACTION_LIMIT_EXCEEDED", "对账侧, 单笔限额超限，禁止提币", 1),

    ;

    private final int code;
    private final String value;
    private final String name;

    /**
     * 1 可通过重试解决
     * 2 不可通过重试解决
     * 3 直接拒绝
     * @See RetryAbleEnum
     */
    private final Integer retryAble;

    WithdrawCheckResultEnum(int code, String value, String name, Integer retryAble) {
        this.code = code;
        this.value = value;
        this.name = name;
        this.retryAble = retryAble;
    }

    public static WithdrawCheckResultEnum convert(int code) {
        for (WithdrawCheckResultEnum type : WithdrawCheckResultEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

    public static WithdrawCheckResultEnum convert(String value) {
        for (WithdrawCheckResultEnum type : WithdrawCheckResultEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public Integer getRetryAble() {
        return retryAble;
    }
}
