package com.upex.reconciliation.facade.model;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
@Data
public class FacadeDepositeAddress extends ArrayList<FacadeDepositeAddress.FacadeDepositeInnerAddress> implements Serializable {
    @Data
    public static class FacadeDepositeInnerAddress{
        private String coinName;
        private String address;
        private String tag;
        private Integer isDefault;
    }
}
