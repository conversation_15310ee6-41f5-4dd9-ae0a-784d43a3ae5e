package com.upex.reconciliation.facade.facade;

import com.upex.reconciliation.facade.model.FacadeDepositeAddress;
import com.upex.reconciliation.facade.params.FacadeDepositeAddressReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(value = "${upex.feignClient.recon}",contextId = "reconThirdCexService",path = "/inner/v1/recon/cex/third")
public interface ReconThirdCexService {


    @PostMapping("/facade/user/deposite/address")
    FacadeDepositeAddress queryDepositeAddress(@Valid @RequestBody FacadeDepositeAddressReq facadeDepositeAddressReq) ;
}
