import com.alibaba.fastjson.JSON;
import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import com.upex.reconciliation.rest.controller.inner.CheckBillResultController;
import org.junit.Test;

import javax.annotation.Resource;

public class CheckBillResultControllerTest  extends BaseTest {
    @Resource
    CheckBillResultController checkBillResultController;

    @Test
    public void testSelectCheckForTheResults() {
//        ReconCheckResultsParams reconCheckResultsParams = JSON.parseObject("{\"businessSource\":\"BG_MANAGE_SYS_BATCH_INTERNAL_TRANSFER\",\"recordCode\":\"a3bd7c260b5740b68d06d24e5ceb0239\",\"requestDate\":\"1750923799194\",\"traceId\":\"1c6fef27fd6ffd8b45c7ef5f0e9c223e\",\"userId\":\"2679750949\"}", ReconCheckResultsParams.class);
        ReconCheckResultsParams reconCheckResultsParams = JSON.parseObject("{\"businessSource\":\"BG_MANAGE_SYS_BATCH_INTERNAL_TRANSFER\",\"recordCode\":\"a3bd7c260b5740b68d06d24e5ceb0239\",\"amount\":\"40\",\"coinId\":\"1\",\"requestDate\":\"1750923799194\",\"traceId\":\"1c6fef27fd6ffd8b45c7ef5f0e9c223e\",\"userId\":\"2679750949\"}", ReconCheckResultsParams.class);
        checkBillResultController.selectCheckForTheResults(reconCheckResultsParams);
    }
}
