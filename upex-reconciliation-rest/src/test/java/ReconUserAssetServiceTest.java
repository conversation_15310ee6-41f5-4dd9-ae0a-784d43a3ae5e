import com.upex.bill.dto.results.AccountAssetsInfoResult;
import com.upex.reconciliation.facade.params.ReconCheckResultsParams;
import com.upex.reconciliation.rest.ReconciliationRestApplication;
import com.upex.reconciliation.service.business.ReconCheckBillResultService;
import com.upex.reconciliation.service.business.ReconUserAssetsSnapShotService;
import com.upex.reconciliation.service.model.config.GlobalBillConfig;
import com.upex.reconciliation.service.model.dto.WithdrawCheckResultDTO;
import com.upex.reconciliation.service.utils.ReconciliationApolloConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

import static com.upex.reconciliation.facade.enums.BusinessSourceEnum.BG_MANAGE_SYS_BATCH_INTERNAL_TRANSFER;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ReconciliationRestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ReconUserAssetServiceTest {
    @Autowired
    private ReconUserAssetsSnapShotService reconUserAssetsSnapShotService;
    @Autowired
    private ReconCheckBillResultService reconCheckBillResultService;

    @Test
    public void queryUserSingleRealTimeAssets() {
        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        List<AccountAssetsInfoResult> result =  reconUserAssetsSnapShotService.queryUserSingleRealTimeAssets(4246814224L,"10-default",new Date().getTime(), globalBillConfig);
        System.out.println(result);

    }

    @Test
    public void checkWithdrawLimitTest() {
        ReconCheckResultsParams reconCheckResultsParams = new ReconCheckResultsParams();
        reconCheckResultsParams.setUserId(0L);
        reconCheckResultsParams.setRequestDate(0L);
        reconCheckResultsParams.setOrderId(0L);
        reconCheckResultsParams.setRecordCode("");
//        reconCheckResultsParams.setBusinessSource(SMALL_WITHDRAW_INTERNAL_TRANSFER.getCode());
        reconCheckResultsParams.setBusinessSource(BG_MANAGE_SYS_BATCH_INTERNAL_TRANSFER.getCode());

        GlobalBillConfig globalBillConfig = ReconciliationApolloConfigUtils.getGlobalBillConfig();
        WithdrawCheckResultDTO result =  reconCheckBillResultService.checkWithdrawLimit(reconCheckResultsParams,globalBillConfig);
        System.out.println(result);
    }
}
