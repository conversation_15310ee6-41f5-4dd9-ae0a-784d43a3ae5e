spring.application.name=upex-reconciliation-rest-local


##dynamic dataSource
upex.datasource.apollo.namespace=application
upex.datasource.apollo.datasourceConfigList=bill.datasource.config
#
# otc client
#
upex.feignClient.otc=upex-otc-test
upex.feignClient.spot=upex-spot-rest-test
upex.feignClient.spot.query=upex-spot-rest-test
upex.feignClient.swap=upex-swap-rest-test
upex.feignClient.user=upex-user-rest-test
upex.feignClient.ticker=upex-ticker-rest-test
##test3\u73AF\u5883\u6CA1\u6709\u94B1\u5305\uFF0C\u76EE\u524D\u53EA\u6709test1\u73AF\u5883\u63D0\u4F9B\u6D4B\u8BD5
upex.feignClient.mcprocess=mc-process-rest-test
upex.feignClient.mcprocess.query=mc-process-rest-test
upex.feignClient.financial=upex-financial-rest-test
upex.feignClient.margin.rest=upex-margin-rest-test
upex.feignClient.payment=upex-fiat-payment-center-rest-test
upex.feignClient.data.risk=upex-data-risk-rest-test
upex.feignClient.fiat.order.center=upex-fiat-order-center-rest-test
upex.feignClient.assets=upex-assets-rest-test
upex.feignClient.broker=upex-broker-rest-test
upex.feignClient.config=upex-config-rest
upex.feignClient.assets.core=upex-assets-rest-test
upex.feignClient.margin.rest.api = upex-margin-rest-test
upex.feignClient.uta.rest=upex-unified-account-rest-test
upex.feignClient.uta.job=upex-unified-account-job-test
upex.feignClient.act = upex-act-rest
upex.feignClient.dcStatistics=upex-data-statistics-rest
upex.feignClient.inner.risk.engine=upex-risk-inner-engine-rest
upex.feignClient.trace.spot=upex-trace-spot-rest
upex.feignClient.trigger=upex-trigger-rest-online
upex.feignClient.dcStatistics.query=upex-data-statistics-rest-query
upex.feignClient.recon=upex-reconciliation-rest
upex.feignClient.data.feature=upex-data-feature-rest-test
upex.feignClient.convertrest=upex-convert-rest-test
upex.feignClient.kline=upex-kline-rest-test
upex.feignClient.mcmatch=upex-mcmatch-rest-test


#
# xxl
#
xxl.job.accessToken=upex_!*0505aim
xxl.job.admin.addresses=http://xxl-job.test7.bitget.tools/xxl-job-admin/
#xxl.job.admin.addresses=http://************:30186/xxl-job-admin
#xxl.job.admin.addresses=http://test3-upex-xxl-job-admin.k8s.itssofast.com/xxl-job-admin/
xxl.job.executor.appname=upex-reconciliation-rest
xxl.job.executor.ip=
xxl.job.executor.port=8090
xxl.job.executor.logpath=/data/logs/upex-bill-job/jobhandler
xxl.job.executor.logretentiondays=3
#
#redis
#
upex.common.redis.host=**************
upex.common.redis.port=6379
upex.common.redis.pool.max-idle=2
upex.common.redis.pool.min-idle=2
upex.common.redis.pool.max-wait=3000
upex.common.redis.pool.max-active=10
upex.common.redis.timeout=3000
upex.common.redis.password=Adddeuuria
upex.config.spot.coinInject.enable=true
upex.config.spot.symbolInject.enable=true
# ================================================================
#logging.level.org.springframework.boot.autoconfigure.logging=debug
logging.level.org.springframework.boot.autoconfigure=info
workid.url=http://upex-gateway.test7.bitget.tools
#
#jwt token
#
# upex.security.jwt.excludePathPatterns=/webjars/**,/customError/**,/error**/**,/favicon.ico,/inner/**,/admin/**,/hs/**,/actuator/**
# upex.security.jwt.secret=TVeaPff6rRB26W8y
# upex.security.jwt.cryptoKey=6FCkHr3vtsNuY722
# upex.security.jwt.expiration=2592000
#
#message
#
spring.messages.basename=i18n/messages
spring.messages.always-use-message-format=true
spring.messages.cache-duration=3600
spring.messages.fallback-to-system-locale=false
#
# feign client
#
#
# spring application name
#
logging.level.com.upex.bill.data=debug
spring.redis.host=redis
spring.redis.port=6379
spring.redis.pool.max-idle=10
spring.redis.pool.min-idle=5
spring.redis.pool.max-wait=3000
spring.redis.timeout=3000
spring.redis.password=
spring.redis.lettuce.pool.max-active=32
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=8
upex.rocketmq.namesrvAddr=rocketmq.test7.bitget.tools
upex.rocketmq.pushBillChangeForReconciliationTopic50=bill_change_for_reconciliation_50
upex.rocketmq.pushBillChangeForReconciliationConsumerGroup=bill_change_for_reconciliation
#spring.datasource.driverClassName=com.mysql.jdbc.Driver
#spring.datasource.url=******************************************************
#spring.datasource.username=root
#spring.datasource.password=root
#Eureka
#
# eureka server url
#
eureka.server.url=http://upex-cloud-eureka.test7.bitget.tools/eureka/
eureka.client.registryFetchIntervalSeconds=5
eureka.client.serviceUrl.defaultZone=http://upex-cloud-eureka.test7.bitget.tools/eureka/
eureka.instance.lease-expiration-duration-in-seconds=15
#Eureka Client\u5237\u65B0\u672C\u5730\u7F13\u5B58\u65F6\u95F4\uFF0C\u9ED8\u8BA430\u79D2\uFF0C \u6539\u62105\u79D2
eureka.instance.lease-renewal-interval-in-seconds=5
eureka.instance.instance-id=${spring.cloud.client.ip-address}:${spring.application.name}:${server.port}
eureka.instance.prefer-ip-address=true

upex.recon.kafka.namesrvAddr=dev-test-v351-kafka01.bitget.tools:9092,dev-test-v351-kafka02.bitget.tools:9092,dev-test-v351-kafka03.bitget.tools:9092
upex.recon.kafka.pushBillChangeForReconciliationConsumerGroup=upex-reconciliation-leichuang-local
upex.recon.kafka.maxPollRecords=1000

management.endpoints.web.exposure.include=health,service-registry,prometheus
management.endpoints.enabled-by-default=true
management.health.redis.enabled=false
management.health.jms.enabled=false
management.health.rabbit.enabled=false
management.health.db.enabled=false
management.health.mongo.enabled=false
management.health.mail.enabled=false
management.health.diskSpace.enabled=false
management.health.solr.enabled=false
management.server.port=8081
management.server.servlet.context-path=/cc
management.endpoints.health.enabled=true
management.endpoints.health.path=/health
management.management.security.enabled=false

# ProtoBuf Redis
upex.user.cache.pbInject.enable=true
protobuf.cluster.redis.host=eks-redis.test7.bitget.tools
protobuf.cluster.redis.port=6379
protobuf.cluster.redis.timeout=2000
protobuf.cluster.redis.pool.min-idle=10
protobuf.cluster.redis.pool.max-idle=50
protobuf.cluster.redis.pool.max-wait=3000
protobuf.cluster.redis.pool.max-active=100
protobuf.cluster.redis.endpoint=secretsmanager.ap-northeast-1.amazonaws.com
protobuf.cluster.redis.region=ap-northeast-1
protobuf.cluster.redis.secret-name=redis/test/auth