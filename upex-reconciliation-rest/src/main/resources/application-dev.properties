spring.application.name=upex-reconciliation-rest-test
#
# datasource
#
#upex.bill.datasource.master.type=com.alibaba.druid.pool.DruidDataSource
#upex.bill.datasource.master.driverClassName=com.mysql.cj.jdbc.Driver
#upex.bill.datasource.master.url=**********************************************************************************************************
#upex.bill.datasource.master.username=test_public
#upex.bill.datasource.master.password=YT=SDQ#YxLWsSi)$O$F]V0tOW6U1+KzV
#upex.bill.datasource.master.max-active=10
#upex.bill.datasource.master.min-idle=0
##dynamic dataSource
upex.datasource.apollo.namespace=application
upex.datasource.apollo.datasourceConfigList=bill.datasource.config
#
# otc client
#
upex.feignClient.otc=upex-otc-test
upex.feignClient.spot=upex-spot-rest-test
upex.feignClient.spot.query=upex-spot-rest-test
upex.feignClient.swap=upex-swap-rest-test
upex.feignClient.user=upex-user-rest-test
upex.feignClient.ticker=upex-ticker-rest-test
##test3\u73AF\u5883\u6CA1\u6709\u94B1\u5305\uFF0C\u76EE\u524D\u53EA\u6709test1\u73AF\u5883\u63D0\u4F9B\u6D4B\u8BD5
upex.feignClient.mcprocess=mc-process-rest-test
upex.feignClient.mcprocess.query=mc-process-rest-test
upex.feignClient.financial=upex-financial-rest-test
upex.feignClient.margin.rest=upex-margin-rest-test
upex.feignClient.payment=upex-fiat-payment-center-rest-test
upex.feignClient.data.risk=upex-data-risk-rest-test
upex.feignClient.fiat.order.center=upex-fiat-order-center-rest-test
upex.feignClient.assets=upex-assets-rest-test
upex.feignClient.broker=upex-broker-rest-test
upex.feignClient.config=upex-config-rest
upex.feignClient.assets.core=upex-assets-rest-test
upex.feignClient.margin.rest.api = upex-margin-rest-test
upex.feignClient.uta.rest=upex-unified-account-rest-test
upex.feignClient.uta.job=upex-unified-account-job-test
upex.feignClient.act = upex-act-rest
upex.feignClient.dcStatistics=upex-data-statistics-rest
upex.feignClient.inner.risk.engine=upex-risk-inner-engine-rest
upex.feignClient.trace.spot=upex-trace-spot-rest
upex.feignClient.trigger=upex-trigger-rest-online
upex.feignClient.dcStatistics.query=upex-data-statistics-rest-query
upex.feignClient.recon=upex-reconciliation-rest
upex.feignClient.data.feature=upex-data-feature-rest-test

upex.feignClient.convertrest=upex-convert-rest-test
upex.feignClient.kline=upex-kline-rest-test
upex.feignClient.mcmatch=upex-mcmatch-rest-test
#
# xxl
#
xxl.job.accessToken=upex_!*0505aim
xxl.job.admin.addresses=http://upex-xxl-job-admin:8080/xxl-job-admin/
#xxl.job.admin.addresses=http://xxl-job.test6.bitget.tools/xxl-job-admin/
xxl.job.executor.appname=upex-reconciliation-rest
xxl.job.executor.ip=
xxl.job.executor.port=8090
xxl.job.executor.logpath=/data/logs/jobhandler
xxl.job.executor.logretentiondays=3
workid.url=http://upex-gateway:8080
upex.config.spot.coinInject.enable=true
upex.config.spot.symbolInject.enable=true
upex.rocketmq.namesrvAddr=rocketmq.test7.bitget.tools
upex.rocketmq.pushBillChangeForReconciliationTopic50=bill_change_for_reconciliation_50
upex.rocketmq.pushBillChangeForReconciliationConsumerGroup=bill_change_for_reconciliation
# eureka
eureka.server.url=http://eureka:8761/eureka/
eureka.client.registryFetchIntervalSeconds=5
eureka.client.serviceUrl.defaultZone=http://eureka:8761/eureka/
eureka.instance.lease-expiration-duration-in-seconds=15
#Eureka Client\u5237\u65B0\u672C\u5730\u7F13\u5B58\u65F6\u95F4\uFF0C\u9ED8\u8BA430\u79D2\uFF0C \u6539\u62105\u79D2
eureka.instance.lease-renewal-interval-in-seconds=5
eureka.instance.instance-id=${spring.cloud.client.ip-address}:${spring.application.name}:${server.port}
eureka.instance.prefer-ip-address=true
#upex.recon.kafka.namesrvAddr=dev-test-v351-kafka01.bitget.tools:9092,dev-test-v351-kafka02.bitget.tools:9092,dev-test-v351-kafka03.bitget.tools:9092
upex.recon.kafka.pushBillChangeForReconciliationConsumerGroup=upex-reconciliation-test7
upex.recon.kafka.maxPollRecords=1000
management.endpoints.web.exposure.include=health,service-registry,prometheus
management.endpoints.enabled-by-default=true
management.health.redis.enabled=false
management.health.jms.enabled=false
management.health.rabbit.enabled=false
management.health.db.enabled=false
management.health.mongo.enabled=false
management.health.mail.enabled=false
management.health.diskSpace.enabled=false
management.health.solr.enabled=false
management.server.port=8081
management.server.servlet.context-path=/cc
management.endpoints.health.enabled=true
management.endpoints.health.path=/health
management.management.security.enabled=false
upex.config.system.user.inject.enable=true

# ProtoBuf Redis
upex.user.cache.pbInject.enable=true
protobuf.cluster.redis.host=eks-redis.test6.bitget.tools
protobuf.cluster.redis.port=6379
protobuf.cluster.redis.timeout=2000
protobuf.cluster.redis.pool.min-idle=10
protobuf.cluster.redis.pool.max-idle=50
protobuf.cluster.redis.pool.max-wait=3000
protobuf.cluster.redis.pool.max-active=100
protobuf.cluster.redis.endpoint=secretsmanager.ap-northeast-1.amazonaws.com
protobuf.cluster.redis.region=ap-northeast-1
protobuf.cluster.redis.secret-name=redis/test/auth