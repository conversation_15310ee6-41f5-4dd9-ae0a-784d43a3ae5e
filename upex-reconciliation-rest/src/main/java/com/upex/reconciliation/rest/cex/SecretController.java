package com.upex.reconciliation.rest.cex;

import com.upex.reconciliation.service.business.cex.CexSecretGenService;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.GenSecretRequest;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.SecreDataReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import com.upex.reconciliation.service.service.client.cex.utils.HmacUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import static com.upex.reconciliation.service.service.client.cex.utils.BgUserUtils.getSysUserId;


@RestController
@RequestMapping("/inner/v1/recon/cex/third")
public class SecretController {

    @Resource
    CexSecretGenService cexSecretGenService;

    /**
     * 密钥生成
     * @param genSecretRequest
     * @param request
     * @return
     * @throws Exception
     */
    @PostMapping("/secret/gen")
    @ResponseBody
    public CommonRes<String> generateSecret(@RequestBody GenSecretRequest genSecretRequest, HttpServletRequest request) throws Exception {
        String publicKey=cexSecretGenService.generateSecret(genSecretRequest,getSysUserId(request));
        return CommonRes.getSucApiBaseRes(publicKey);
    }

    @PostMapping("/hmac/test")
    @ResponseBody
    public CommonRes<String> hmacTest(@RequestBody SecreDataReq secreDataReq) throws Exception {
        if(secreDataReq.getIfEncryp()){
            return CommonRes.getSucApiBaseRes(HmacUtil.encrypt(secreDataReq.getData()));
        }else{
            return CommonRes.getSucApiBaseRes(HmacUtil.decrypt(secreDataReq.getData()));
        }
    }
}
