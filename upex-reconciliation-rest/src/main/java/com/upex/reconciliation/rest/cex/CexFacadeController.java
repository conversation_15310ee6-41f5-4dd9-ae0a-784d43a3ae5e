package com.upex.reconciliation.rest.cex;

import com.upex.reconciliation.facade.model.FacadeDepositeAddress;
import com.upex.reconciliation.facade.params.FacadeDepositeAddressReq;
import com.upex.reconciliation.service.business.cex.CexApiService;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.DepositeAddressReq;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonDepositeAddressInnerRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonDepositeAddressRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import com.upex.reconciliation.service.service.client.cex.enmus.CexTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Controller
@RequestMapping(value = "/inner/v1/recon/cex/third")
@Slf4j
public class CexFacadeController {

    @Resource
    CexApiService cexApiService;

    @RequestMapping(value = "/facade/user/deposite/address", method = RequestMethod.POST)
    @ResponseBody
    public FacadeDepositeAddress queryDepositeAddress(@Valid @RequestBody FacadeDepositeAddressReq facadeDepositeAddressReq) {
        DepositeAddressReq depositeAddressReq = new DepositeAddressReq();
        depositeAddressReq.setCoinName(facadeDepositeAddressReq.getCoinName());
        depositeAddressReq.setCexType(facadeDepositeAddressReq.getCexType());
        depositeAddressReq.setCexUserId(facadeDepositeAddressReq.getCexUserId());
        CommonRes<CommonDepositeAddressRes> res = cexApiService.queryUserDepositeAddress(depositeAddressReq);
        FacadeDepositeAddress facadeDepositeAddress = new FacadeDepositeAddress();
        List<FacadeDepositeAddress.FacadeDepositeInnerAddress> facadeDepositeInnerAddressList = Lists.newArrayList();
        if (res.getSuccess() && CollectionUtils.isNotEmpty(res.getData())) {
            for (CommonDepositeAddressInnerRes innerRes : res.getData()) {
                FacadeDepositeAddress.FacadeDepositeInnerAddress facadeDepositeInnerAddress = new FacadeDepositeAddress.FacadeDepositeInnerAddress();
                facadeDepositeInnerAddress.setAddress(innerRes.getAddress());
                facadeDepositeInnerAddress.setCoinName(innerRes.getCoinName());
                facadeDepositeInnerAddress.setTag(innerRes.getTag());
                facadeDepositeInnerAddress.setIsDefault(innerRes.getIsDefault());
                facadeDepositeInnerAddressList.add(facadeDepositeInnerAddress);
            }
        }
        facadeDepositeAddress.addAll(facadeDepositeInnerAddressList);
        return facadeDepositeAddress;
    }
}
