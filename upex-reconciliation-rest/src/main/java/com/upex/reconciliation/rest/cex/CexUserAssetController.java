package com.upex.reconciliation.rest.cex;

import com.upex.reconciliation.service.business.cex.*;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexDepositeHistory;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexWithdrawHistory;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.*;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@RequestMapping(value = "/inner/v1/recon/cex/third")
@Slf4j
public class CexUserAssetController {

    @Resource
    CexApiAggregateBizService cexApiAggregateBizService;

    @Resource
    CexApiService cexApiService;

    @Resource
    CexSubUserAssetHistoryBizService cexSubUserAssetHistoryBizService;

    @Resource
    CexUserDepositeHistoryBizService cexUserDepositeHistoryBizService;

    @Resource
    CexUserWithdrawHistoryBizService cexUserWithdrawHistoryBizService;

    @Resource
    CexParentUserAssetHistoryBizService cexParentUserAssetHistoryBizService;

    /**
     * 查询母用户总资产
     *
     * @param commonReq
     * @return
     */
    @RequestMapping(value = "/userasset/total", method = RequestMethod.POST)
    @ResponseBody
    public CommonRes<CommonTotalAssetRes> queryUserTotalAsset(@Valid @RequestBody UserAssetListReq commonReq) {
        return cexParentUserAssetHistoryBizService.queryCoinAsset(commonReq);
    }

    /**
     * 查询子用户总资产
     *
     * @param commonReq
     * @return
     */
    @RequestMapping(value = "/userasset/subuser/total", method = RequestMethod.POST)
    @ResponseBody
    public CommonRes<CommonSubUserTotalAssetRes> querySubUserTotalAsset(@Valid @RequestBody UserAssetListReq commonReq) {
        return cexSubUserAssetHistoryBizService.querySubUserCoinAsset(commonReq);
    }

    /**
     * 查看用户账户资产币种明细详情(现货、合约)
     *
     * @param commonReq
     * @return
     */
    @RequestMapping(value = "/userasset/detail", method = RequestMethod.POST)
    @ResponseBody
    public CommonRes<CommonTotalAssetRes> queryUserAssetDetail(@Valid @RequestBody UserAssetDetailReq commonReq) {
        return cexApiAggregateBizService.queryCoinAssetDetail(commonReq);
    }

    /**
     * 查询支持的币种信息
     */
    @RequestMapping(value = "/coin/list", method = RequestMethod.POST)
    @ResponseBody
    public CommonRes<CommonCoinInfoListRes> queryCoinList(@Valid @RequestBody CommonReq commonReq) {
        return cexApiAggregateBizService.querySupportCoinList(commonReq);
    }

    /**
     * 查询用户充值地址
     */

    @RequestMapping(value = "/user/deposite/address", method = RequestMethod.POST)
    @ResponseBody
    public CommonRes<CommonDepositeAddressRes> queryDepositeAddress(@Valid @RequestBody DepositeAddressReq commonReq) {
        return cexApiService.queryUserDepositeAddress(commonReq);
    }

    /**
     * 查询用户充值记录
     */
    @RequestMapping(value = "/user/deposite/history", method = RequestMethod.POST)
    @ResponseBody
    public CommonRes<PageData<ThirdCexDepositeHistory>> queryUserDepositeHistory(@RequestBody UserDepositeHistoryListReq commonReq) {
        return CommonRes.getSucApiBaseRes(cexUserDepositeHistoryBizService.getUserDepositeHistory(commonReq));
    }

    /**
     * 查询用户提现记录
     */
    @RequestMapping(value = "/user/withdraw/history", method = RequestMethod.POST)
    @ResponseBody
    public CommonRes<PageData<ThirdCexWithdrawHistory>> queryUserWithdrawHistory(@RequestBody UserWithdrawHistoryListReq commonReq) {
        return CommonRes.getSucApiBaseRes(cexUserWithdrawHistoryBizService.getUserWithdrawHistory(commonReq));
    }
}
