package com.upex.reconciliation.rest.aop;

import com.upex.reconciliation.service.service.client.cex.dto.res.common.CommonRes;
import com.upex.reconciliation.service.service.client.cex.enmus.ReconCexExceptionEnum;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.stream.Collectors;

@RestControllerAdvice(basePackages = "com.upex.reconciliation.rest.cex")
public class ValidationHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommonRes handleValidationErrors(MethodArgumentNotValidException ex) {
        String errorMessage = ex.getBindingResult()
                .getAllErrors()
                .stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage).findFirst().orElseGet(null);
        ReconCexExceptionEnum errorCode=ReconCexExceptionEnum.getByName(errorMessage);
        return CommonRes.getFailApiBaseRes(errorCode);
    }

}
