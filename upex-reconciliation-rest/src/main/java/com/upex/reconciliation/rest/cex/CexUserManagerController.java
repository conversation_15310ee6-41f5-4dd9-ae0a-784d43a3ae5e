package com.upex.reconciliation.rest.cex;

import com.upex.base.system.dto.SysUserDto;
import com.upex.commons.support.exception.ApiException;
import com.upex.reconciliation.service.business.cex.CexApiService;
import com.upex.reconciliation.service.business.cex.CexUserBizService;
import com.upex.reconciliation.service.business.cex.CexUserConfigBizService;
import com.upex.reconciliation.service.dao.cex.entity.ThirdCexUser;
import com.upex.reconciliation.service.service.client.cex.dto.req.common.*;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.*;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.user.CexUserConfigListRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.user.CexUserInfoInnerRes;
import com.upex.reconciliation.service.service.client.cex.dto.res.common.user.CommonUserStatusRes;
import com.upex.reconciliation.service.service.client.cex.enmus.*;
import com.upex.reconciliation.service.service.client.cex.utils.BgUserUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.upex.reconciliation.service.service.client.cex.utils.BgUserUtils.getSysUserEmail;
import static com.upex.reconciliation.service.service.client.cex.utils.BgUserUtils.getSysUserId;

@Controller
@RequestMapping(value = "/inner/v1/recon/cex/third")
@Slf4j
public class CexUserManagerController {

    @Resource
    CexUserBizService thirdCexUserBizService;

    @Resource
    CexUserConfigBizService cexUserConfigBizService;

    @Resource
    CexApiService cexApiService;

    @Resource
    CexUserBizService cexUserBizService;

    /**
     * 添加母用户
     *
     * @param modCexUserRequest
     * @param request
     * @return
     */
    @RequestMapping(value = "/user/add", method = RequestMethod.POST)
    @ResponseBody
    public CommonRes addCexUser(@Valid @RequestBody ModCexUserRequest modCexUserRequest, HttpServletRequest request) {
        int count = thirdCexUserBizService.addParentUser(modCexUserRequest);
        return CommonRes.getSucApiBaseRes(count);
    }

    /**
     * 母用户同步子用户
     *
     * @param syncSubUserReq
     * @return
     */
    @RequestMapping(value = "/sync/subuser", method = RequestMethod.POST)
    @ResponseBody
    public CommonRes syncSubUser(@Valid @RequestBody SyncSubUserReq syncSubUserReq) {
        return thirdCexUserBizService.syncSubUser(syncSubUserReq.getCexType(), syncSubUserReq.getParentUserId());
    }

    /**
     * 更新母子用户
     *
     * @param modCexUserRequest
     * @param request
     * @return
     */
    @RequestMapping(value = "/user/mod", method = RequestMethod.POST)
    @ResponseBody
    public CommonRes modCexUser(@Valid @RequestBody ModCexUserRequest modCexUserRequest, HttpServletRequest request) {
        int count = thirdCexUserBizService.modUser(modCexUserRequest);
        return CommonRes.getSucApiBaseRes(count);
    }

    /**
     * 用户列表
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/user/list", method = RequestMethod.POST)
    @ResponseBody
    public CommonRes<List<ThirdCexUser>> getCexUsers(@Valid @RequestBody CexUserPageListRequest request) {
        List<ThirdCexUser> cexUsers = cexUserBizService.getCexUsers(request);
        return CommonRes.getSucApiBaseRes(cexUsers);
    }

    /**
     * 查询母用户下所有子用户
     * @param request
     * @return
     */
    @PostMapping("/subuser/list")
    @ResponseBody
    public CommonRes<List<ThirdCexUser>> querySubUserList(@Valid @RequestBody CexUserListRequest request) {
        List<ThirdCexUser> subUsers = cexUserBizService.querySubUserList(request);
        return CommonRes.getSucApiBaseRes(subUsers);
    }

    /**
     * 查看用户详情
     */
    @PostMapping("/user/detail")
    @ResponseBody
    public CommonRes<CexUserInfoInnerRes> getCexUserDetail(@Valid @RequestBody CommonReq request) {
        return cexUserBizService.getCexUserDetail(request);
    }

    /**
     * 添加母账户API Key（资金监管或其他apikey）
     *
     * @param addApiKeyReq
     * @param request
     * @return
     */
    @PostMapping("/user/apikey/add")
    @ResponseBody
    public CommonRes addApiKey(@Valid @RequestBody AddApiKeyReq addApiKeyReq, HttpServletRequest request) throws Exception {
        // 调用服务层保存逻辑
        cexUserConfigBizService.addConfig(addApiKeyReq, getSysUserId(request), getSysUserEmail(request));
        return CommonRes.getSucApiBaseRes(addApiKeyReq.getCexType());
    }

    /**
     * 设置APIkey为监控API-key
     */
    @PostMapping("/user/apikey/setmonitor")
    @ResponseBody
    public CommonRes setApiKeyMonitor(@Valid @RequestBody SetApiKeyMonitorReq setApiKeyMonitorReq) {
        return CommonRes.getSucApiBaseRes(cexUserConfigBizService.setApiKeyMonitor(setApiKeyMonitorReq));
    }

    /**
     * 查看api-key权限
     *
     * @param queryApiKeyPermissionReq
     * @param request
     * @return
     */
    @PostMapping("/user/apikey/permission")
    @ResponseBody
    public CommonRes<CommonApiKeyPermissionRes> queryApiKeyPermission(@Valid @RequestBody QueryApiKeyPermissionReq queryApiKeyPermissionReq, HttpServletRequest request) throws Exception {
        return cexUserConfigBizService.queryApiPermission(queryApiKeyPermissionReq,BgUserUtils.getSysUserId( request));
    }

    /**
     * 查看用户状态
     */
    @PostMapping("/user/account/status")
    @ResponseBody
    public CommonRes<CommonUserStatusRes> queryUserStatus(@Valid @RequestBody CommonReq queryUserStatusRequest, HttpServletRequest request) {
        return cexApiService.queryUserStatus(queryUserStatusRequest);
    }

    /**
     * 恢复用户状态
     */
    @PostMapping("/userstatus/recover")
    @ResponseBody
    public CommonRes<CommonUserStatusRes> recoverUserStatus(@Valid @RequestBody CommonReq commonReq) {
        return cexUserBizService.recoverUserStatus(commonReq);
    }

    /**
     * 查看母用户api-key列表
     */
    @PostMapping("/user/apikey/list")
    @ResponseBody
    public CommonRes<CexUserConfigListRes> queryApikeyList(@Valid @RequestBody CommonReq commonReq) {
        return cexUserConfigBizService.queryApikeyList(commonReq);
    }

    /**
     * 用户状态启用、停用
     *
     * @param request
     * @return
     */
    @PostMapping("/user/status/mod")
    @ResponseBody
    public CommonRes modUserStatus(@Valid @RequestBody UserManagerStatusModReq request) {
        return CommonRes.getSucApiBaseRes(cexUserBizService.modUserStatus(request));
    }

    /**
     * 查看所有母用户列表
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/parentuser/list", method = RequestMethod.POST)
    @ResponseBody
    public CommonRes<List<ThirdCexUserInnerRes>> queryParentUserList(@Valid  @RequestBody ParentUserListReq parentUserListReq) {
        return CommonRes.getSucApiBaseRes(cexUserBizService.queryParentUserList(parentUserListReq.getCexType()));
    }

    /**
     * 查看用户使用类型枚举
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/enum/usetype/list", method = RequestMethod.POST)
    @ResponseBody
    public CommonRes queryUseTypeEnumList(HttpServletRequest request) {
        SysUserDto sysUserDto = BgUserUtils.getSysUserDto(request);
        if (sysUserDto == null) {
            throw new ApiException(ReconCexExceptionEnum.ILLEGAL_PARAMS);
        }
        List<EnumValueRes> enumValueRes = CexUseTypeEnum.getList();
        return CommonRes.getSucApiBaseRes(enumValueRes);
    }

    /**
     * 查看枚举列表
     *
     * @param request
     * @return
     */

    @PostMapping("/enum/list")
    @ResponseBody
    public CommonRes queryEnumList(HttpServletRequest request) {
        SysUserDto sysUserDto = BgUserUtils.getSysUserDto(request);
        if (sysUserDto == null) {
            throw new ApiException(ReconCexExceptionEnum.ILLEGAL_PARAMS);
        }
        Map<String,List<EnumValueRes>> enumValueRes = new HashMap<>();
        enumValueRes.put("ApiKeyStatusEnum",  Arrays.stream(ApiKeyStatusEnum.values())
                .map(e -> new EnumValueRes(e.getType(), e.getName()))
                .collect(Collectors.toList()));
       
        enumValueRes.put("CexUseTypeEnum",Arrays.stream(CexUseTypeEnum.values())
                .map(e -> new EnumValueRes(e.getType(), e.getName()))
                .collect(Collectors.toList()));
        enumValueRes.put("CexTypeEnum",Arrays.stream(CexTypeEnum.values())
                .map(e -> new EnumValueRes(e.getType(), e.getName()))
                .collect(Collectors.toList()));
        enumValueRes.put("TradeTypeEnum",Arrays.stream(TradeTypeEnum.values())
                .map(e -> new EnumValueRes(e.getType(), e.getName()))
                .collect(Collectors.toList()));
        enumValueRes.put("UserManagerStatusEnum",Arrays.stream(UserManagerStatusEnum.values())
                .map(e -> new EnumValueRes(e.getType(), e.getName()))
                .collect(Collectors.toList()));
        enumValueRes.put("CexUserStatusEnum",Arrays.stream(CexUserStatusEnum.values())
                .map(e -> new EnumValueRes(e.getType(), e.getName()))
                .collect(Collectors.toList()));
        enumValueRes.put("ThirdAssetType",Arrays.stream(ThirdAssetType.values())
                .map(e -> new EnumValueRes(e.getType(), e.getName()))
                .collect(Collectors.toList()));
        enumValueRes.put("ReadOnlyEnum",Arrays.stream(ReadOnlyEnum.values())
                .map(e -> new EnumValueRes(e.getType(), e.getName()))
                .collect(Collectors.toList()));
        enumValueRes.put("CexUserTypeEnum",Arrays.stream(CexUserTypeEnum.values())
                .map(e -> new EnumValueRes(e.getType(), e.getName()))
                .collect(Collectors.toList()));
        return CommonRes.getSucApiBaseRes(enumValueRes);
    }

}
